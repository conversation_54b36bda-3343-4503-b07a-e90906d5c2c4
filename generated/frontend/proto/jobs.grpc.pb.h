// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/jobs.proto
#ifndef GRPC_frontend_2fproto_2fjobs_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fjobs_2eproto__INCLUDED

#include "frontend/proto/jobs.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace jobs {

class JobsService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.jobs.JobsService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::carbon::frontend::jobs::GetNextJobsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobsResponse>> AsyncGetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobsResponse>>(AsyncGetNextJobsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobsResponse>> PrepareAsyncGetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobsResponse>>(PrepareAsyncGetNextJobsRaw(context, request, cq));
    }
    virtual ::grpc::Status CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::carbon::frontend::jobs::CreateJobResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::CreateJobResponse>> AsyncCreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::CreateJobResponse>>(AsyncCreateJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::CreateJobResponse>> PrepareAsyncCreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::CreateJobResponse>>(PrepareAsyncCreateJobRaw(context, request, cq));
    }
    virtual ::grpc::Status UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncUpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncUpdateJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncUpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncUpdateJobRaw(context, request, cq));
    }
    virtual ::grpc::Status StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncStartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncStartJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncStartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncStartJobRaw(context, request, cq));
    }
    virtual ::grpc::Status StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncStopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncStopActiveJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncStopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncStopActiveJobRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>> AsyncGetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>>(AsyncGetNextActiveJobIdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>> PrepareAsyncGetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>>(PrepareAsyncGetNextActiveJobIdRaw(context, request, cq));
    }
    virtual ::grpc::Status GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::carbon::frontend::jobs::GetJobResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetJobResponse>> AsyncGetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetJobResponse>>(AsyncGetJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetJobResponse>> PrepareAsyncGetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetJobResponse>>(PrepareAsyncGetJobRaw(context, request, cq));
    }
    virtual ::grpc::Status GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::carbon::frontend::jobs::GetConfigDumpResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetConfigDumpResponse>> AsyncGetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetConfigDumpResponse>>(AsyncGetConfigDumpRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetConfigDumpResponse>> PrepareAsyncGetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetConfigDumpResponse>>(PrepareAsyncGetConfigDumpRaw(context, request, cq));
    }
    virtual ::grpc::Status GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>> AsyncGetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>>(AsyncGetActiveJobMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>> PrepareAsyncGetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>>(PrepareAsyncGetActiveJobMetricsRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDeleteJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteJobRaw(context, request, cq));
    }
    virtual ::grpc::Status MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncMarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncMarkJobCompletedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncMarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncMarkJobCompletedRaw(context, request, cq));
    }
    virtual ::grpc::Status MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncMarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncMarkJobIncompleteRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncMarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncMarkJobIncompleteRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::carbon::frontend::jobs::GetNextJobResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobResponse>> AsyncGetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobResponse>>(AsyncGetNextJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobResponse>> PrepareAsyncGetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobResponse>>(PrepareAsyncGetNextJobRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobsResponse>* AsyncGetNextJobsRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobsResponse>* PrepareAsyncGetNextJobsRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::CreateJobResponse>* AsyncCreateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::CreateJobResponse>* PrepareAsyncCreateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncUpdateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncUpdateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncStartJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncStartJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncStopActiveJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncStopActiveJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* AsyncGetNextActiveJobIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* PrepareAsyncGetNextActiveJobIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetJobResponse>* AsyncGetJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetJobResponse>* PrepareAsyncGetJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetConfigDumpResponse>* AsyncGetConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetConfigDumpResponse>* PrepareAsyncGetConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* AsyncGetActiveJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* PrepareAsyncGetActiveJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDeleteJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncMarkJobCompletedRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncMarkJobCompletedRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncMarkJobIncompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncMarkJobIncompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobResponse>* AsyncGetNextJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::jobs::GetNextJobResponse>* PrepareAsyncGetNextJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::carbon::frontend::jobs::GetNextJobsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobsResponse>> AsyncGetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobsResponse>>(AsyncGetNextJobsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobsResponse>> PrepareAsyncGetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobsResponse>>(PrepareAsyncGetNextJobsRaw(context, request, cq));
    }
    ::grpc::Status CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::carbon::frontend::jobs::CreateJobResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::CreateJobResponse>> AsyncCreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::CreateJobResponse>>(AsyncCreateJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::CreateJobResponse>> PrepareAsyncCreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::CreateJobResponse>>(PrepareAsyncCreateJobRaw(context, request, cq));
    }
    ::grpc::Status UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncUpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncUpdateJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncUpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncUpdateJobRaw(context, request, cq));
    }
    ::grpc::Status StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncStartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncStartJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncStartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncStartJobRaw(context, request, cq));
    }
    ::grpc::Status StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncStopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncStopActiveJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncStopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncStopActiveJobRaw(context, request, cq));
    }
    ::grpc::Status GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>> AsyncGetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>>(AsyncGetNextActiveJobIdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>> PrepareAsyncGetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>>(PrepareAsyncGetNextActiveJobIdRaw(context, request, cq));
    }
    ::grpc::Status GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::carbon::frontend::jobs::GetJobResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetJobResponse>> AsyncGetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetJobResponse>>(AsyncGetJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetJobResponse>> PrepareAsyncGetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetJobResponse>>(PrepareAsyncGetJobRaw(context, request, cq));
    }
    ::grpc::Status GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::carbon::frontend::jobs::GetConfigDumpResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetConfigDumpResponse>> AsyncGetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetConfigDumpResponse>>(AsyncGetConfigDumpRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetConfigDumpResponse>> PrepareAsyncGetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetConfigDumpResponse>>(PrepareAsyncGetConfigDumpRaw(context, request, cq));
    }
    ::grpc::Status GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>> AsyncGetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>>(AsyncGetActiveJobMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>> PrepareAsyncGetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>>(PrepareAsyncGetActiveJobMetricsRaw(context, request, cq));
    }
    ::grpc::Status DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDeleteJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteJobRaw(context, request, cq));
    }
    ::grpc::Status MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncMarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncMarkJobCompletedRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncMarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncMarkJobCompletedRaw(context, request, cq));
    }
    ::grpc::Status MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncMarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncMarkJobIncompleteRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncMarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncMarkJobIncompleteRaw(context, request, cq));
    }
    ::grpc::Status GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::carbon::frontend::jobs::GetNextJobResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobResponse>> AsyncGetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobResponse>>(AsyncGetNextJobRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobResponse>> PrepareAsyncGetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobResponse>>(PrepareAsyncGetNextJobRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response, std::function<void(::grpc::Status)>) override;
      void CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response, std::function<void(::grpc::Status)>) override;
      void GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response, std::function<void(::grpc::Status)>) override;
      void GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobsResponse>* AsyncGetNextJobsRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobsResponse>* PrepareAsyncGetNextJobsRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::CreateJobResponse>* AsyncCreateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::CreateJobResponse>* PrepareAsyncCreateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncUpdateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncUpdateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncStartJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncStartJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncStopActiveJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncStopActiveJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* AsyncGetNextActiveJobIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* PrepareAsyncGetNextActiveJobIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetJobResponse>* AsyncGetJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetJobResponse>* PrepareAsyncGetJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetConfigDumpResponse>* AsyncGetConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetConfigDumpResponse>* PrepareAsyncGetConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* AsyncGetActiveJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* PrepareAsyncGetActiveJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDeleteJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncMarkJobCompletedRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncMarkJobCompletedRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncMarkJobIncompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncMarkJobIncompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobResponse>* AsyncGetNextJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobResponse>* PrepareAsyncGetNextJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextJobs_;
    const ::grpc::internal::RpcMethod rpcmethod_CreateJob_;
    const ::grpc::internal::RpcMethod rpcmethod_UpdateJob_;
    const ::grpc::internal::RpcMethod rpcmethod_StartJob_;
    const ::grpc::internal::RpcMethod rpcmethod_StopActiveJob_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextActiveJobId_;
    const ::grpc::internal::RpcMethod rpcmethod_GetJob_;
    const ::grpc::internal::RpcMethod rpcmethod_GetConfigDump_;
    const ::grpc::internal::RpcMethod rpcmethod_GetActiveJobMetrics_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteJob_;
    const ::grpc::internal::RpcMethod rpcmethod_MarkJobCompleted_;
    const ::grpc::internal::RpcMethod rpcmethod_MarkJobIncomplete_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextJob_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextJobs(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response);
    virtual ::grpc::Status CreateJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response);
    virtual ::grpc::Status UpdateJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status StartJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status StopActiveJob(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextActiveJobId(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response);
    virtual ::grpc::Status GetJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response);
    virtual ::grpc::Status GetConfigDump(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response);
    virtual ::grpc::Status GetActiveJobMetrics(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response);
    virtual ::grpc::Status DeleteJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status MarkJobCompleted(::grpc::ServerContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status MarkJobIncomplete(::grpc::ServerContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextJobs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextJobs() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextJobs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJobs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobsRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextJobs(::grpc::ServerContext* context, ::carbon::frontend::jobs::GetNextJobsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::jobs::GetNextJobsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CreateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CreateJob() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_CreateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::CreateJobRequest* /*request*/, ::carbon::frontend::jobs::CreateJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCreateJob(::grpc::ServerContext* context, ::carbon::frontend::jobs::CreateJobRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::jobs::CreateJobResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UpdateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UpdateJob() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_UpdateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::UpdateJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateJob(::grpc::ServerContext* context, ::carbon::frontend::jobs::UpdateJobRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartJob() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_StartJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::StartJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartJob(::grpc::ServerContext* context, ::carbon::frontend::jobs::StartJobRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StopActiveJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StopActiveJob() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_StopActiveJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopActiveJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStopActiveJob(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextActiveJobId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextActiveJobId() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_GetNextActiveJobId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveJobId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* /*request*/, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveJobId(::grpc::ServerContext* context, ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetJob() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetJobRequest* /*request*/, ::carbon::frontend::jobs::GetJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetJob(::grpc::ServerContext* context, ::carbon::frontend::jobs::GetJobRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::jobs::GetJobResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetConfigDump() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_GetConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetConfigDumpRequest* /*request*/, ::carbon::frontend::jobs::GetConfigDumpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConfigDump(::grpc::ServerContext* context, ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::jobs::GetConfigDumpResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetActiveJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetActiveJobMetrics() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_GetActiveJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetActiveJobMetrics(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteJob() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_DeleteJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::DeleteJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteJob(::grpc::ServerContext* context, ::carbon::frontend::jobs::DeleteJobRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_MarkJobCompleted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_MarkJobCompleted() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_MarkJobCompleted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobCompleted(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobCompletedRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMarkJobCompleted(::grpc::ServerContext* context, ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_MarkJobIncomplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_MarkJobIncomplete() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_MarkJobIncomplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobIncomplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMarkJobIncomplete(::grpc::ServerContext* context, ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextJob() {
      ::grpc::Service::MarkMethodAsync(12);
    }
    ~WithAsyncMethod_GetNextJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextJob(::grpc::ServerContext* context, ::carbon::frontend::jobs::GetNextJobRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::jobs::GetNextJobResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextJobs<WithAsyncMethod_CreateJob<WithAsyncMethod_UpdateJob<WithAsyncMethod_StartJob<WithAsyncMethod_StopActiveJob<WithAsyncMethod_GetNextActiveJobId<WithAsyncMethod_GetJob<WithAsyncMethod_GetConfigDump<WithAsyncMethod_GetActiveJobMetrics<WithAsyncMethod_DeleteJob<WithAsyncMethod_MarkJobCompleted<WithAsyncMethod_MarkJobIncomplete<WithAsyncMethod_GetNextJob<Service > > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextJobs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextJobs() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetNextJobsRequest, ::carbon::frontend::jobs::GetNextJobsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response) { return this->GetNextJobs(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextJobs(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::GetNextJobsRequest, ::carbon::frontend::jobs::GetNextJobsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetNextJobsRequest, ::carbon::frontend::jobs::GetNextJobsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextJobs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJobs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobsRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextJobs(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobsRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_CreateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CreateJob() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::CreateJobRequest, ::carbon::frontend::jobs::CreateJobResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response) { return this->CreateJob(context, request, response); }));}
    void SetMessageAllocatorFor_CreateJob(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::CreateJobRequest, ::carbon::frontend::jobs::CreateJobResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::CreateJobRequest, ::carbon::frontend::jobs::CreateJobResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CreateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::CreateJobRequest* /*request*/, ::carbon::frontend::jobs::CreateJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CreateJob(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::CreateJobRequest* /*request*/, ::carbon::frontend::jobs::CreateJobResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UpdateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UpdateJob() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::UpdateJobRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response) { return this->UpdateJob(context, request, response); }));}
    void SetMessageAllocatorFor_UpdateJob(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::UpdateJobRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::UpdateJobRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UpdateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::UpdateJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateJob(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::UpdateJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartJob() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::StartJobRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response) { return this->StartJob(context, request, response); }));}
    void SetMessageAllocatorFor_StartJob(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::StartJobRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::StartJobRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::StartJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartJob(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::StartJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StopActiveJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StopActiveJob() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->StopActiveJob(context, request, response); }));}
    void SetMessageAllocatorFor_StopActiveJob(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StopActiveJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopActiveJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StopActiveJob(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextActiveJobId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextActiveJobId() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::carbon::frontend::jobs::GetNextActiveJobIdResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response) { return this->GetNextActiveJobId(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextActiveJobId(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::carbon::frontend::jobs::GetNextActiveJobIdResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextActiveJobId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveJobId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* /*request*/, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveJobId(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* /*request*/, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetJob() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetJobRequest, ::carbon::frontend::jobs::GetJobResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response) { return this->GetJob(context, request, response); }));}
    void SetMessageAllocatorFor_GetJob(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::GetJobRequest, ::carbon::frontend::jobs::GetJobResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetJobRequest, ::carbon::frontend::jobs::GetJobResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetJobRequest* /*request*/, ::carbon::frontend::jobs::GetJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetJob(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::GetJobRequest* /*request*/, ::carbon::frontend::jobs::GetJobResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetConfigDump() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetConfigDumpRequest, ::carbon::frontend::jobs::GetConfigDumpResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response) { return this->GetConfigDump(context, request, response); }));}
    void SetMessageAllocatorFor_GetConfigDump(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::GetConfigDumpRequest, ::carbon::frontend::jobs::GetConfigDumpResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetConfigDumpRequest, ::carbon::frontend::jobs::GetConfigDumpResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetConfigDumpRequest* /*request*/, ::carbon::frontend::jobs::GetConfigDumpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConfigDump(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::GetConfigDumpRequest* /*request*/, ::carbon::frontend::jobs::GetConfigDumpResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetActiveJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetActiveJobMetrics() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::GetActiveJobMetricsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response) { return this->GetActiveJobMetrics(context, request, response); }));}
    void SetMessageAllocatorFor_GetActiveJobMetrics(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::GetActiveJobMetricsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetActiveJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetActiveJobMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteJob() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::DeleteJobRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response) { return this->DeleteJob(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteJob(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::DeleteJobRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::DeleteJobRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::DeleteJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteJob(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::DeleteJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_MarkJobCompleted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_MarkJobCompleted() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::MarkJobCompletedRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response) { return this->MarkJobCompleted(context, request, response); }));}
    void SetMessageAllocatorFor_MarkJobCompleted(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::MarkJobCompletedRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::MarkJobCompletedRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_MarkJobCompleted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobCompleted(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobCompletedRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* MarkJobCompleted(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobCompletedRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_MarkJobIncomplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_MarkJobIncomplete() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response) { return this->MarkJobIncomplete(context, request, response); }));}
    void SetMessageAllocatorFor_MarkJobIncomplete(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_MarkJobIncomplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobIncomplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* MarkJobIncomplete(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextJob() {
      ::grpc::Service::MarkMethodCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetNextJobRequest, ::carbon::frontend::jobs::GetNextJobResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response) { return this->GetNextJob(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextJob(
        ::grpc::MessageAllocator< ::carbon::frontend::jobs::GetNextJobRequest, ::carbon::frontend::jobs::GetNextJobResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(12);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::jobs::GetNextJobRequest, ::carbon::frontend::jobs::GetNextJobResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextJob(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextJobs<WithCallbackMethod_CreateJob<WithCallbackMethod_UpdateJob<WithCallbackMethod_StartJob<WithCallbackMethod_StopActiveJob<WithCallbackMethod_GetNextActiveJobId<WithCallbackMethod_GetJob<WithCallbackMethod_GetConfigDump<WithCallbackMethod_GetActiveJobMetrics<WithCallbackMethod_DeleteJob<WithCallbackMethod_MarkJobCompleted<WithCallbackMethod_MarkJobIncomplete<WithCallbackMethod_GetNextJob<Service > > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextJobs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextJobs() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextJobs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJobs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobsRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CreateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CreateJob() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_CreateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::CreateJobRequest* /*request*/, ::carbon::frontend::jobs::CreateJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UpdateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UpdateJob() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_UpdateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::UpdateJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartJob() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_StartJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::StartJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StopActiveJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StopActiveJob() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_StopActiveJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopActiveJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextActiveJobId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextActiveJobId() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_GetNextActiveJobId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveJobId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* /*request*/, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetJob() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetJobRequest* /*request*/, ::carbon::frontend::jobs::GetJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetConfigDump() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_GetConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetConfigDumpRequest* /*request*/, ::carbon::frontend::jobs::GetConfigDumpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetActiveJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetActiveJobMetrics() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_GetActiveJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteJob() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_DeleteJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::DeleteJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_MarkJobCompleted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_MarkJobCompleted() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_MarkJobCompleted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobCompleted(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobCompletedRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_MarkJobIncomplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_MarkJobIncomplete() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_MarkJobIncomplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobIncomplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextJob() {
      ::grpc::Service::MarkMethodGeneric(12);
    }
    ~WithGenericMethod_GetNextJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextJobs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextJobs() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextJobs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJobs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobsRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextJobs(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CreateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CreateJob() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_CreateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::CreateJobRequest* /*request*/, ::carbon::frontend::jobs::CreateJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCreateJob(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UpdateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UpdateJob() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_UpdateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::UpdateJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateJob(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartJob() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_StartJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::StartJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartJob(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StopActiveJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StopActiveJob() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_StopActiveJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopActiveJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStopActiveJob(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextActiveJobId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextActiveJobId() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_GetNextActiveJobId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveJobId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* /*request*/, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveJobId(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetJob() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetJobRequest* /*request*/, ::carbon::frontend::jobs::GetJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetJob(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetConfigDump() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_GetConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetConfigDumpRequest* /*request*/, ::carbon::frontend::jobs::GetConfigDumpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetConfigDump(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetActiveJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetActiveJobMetrics() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_GetActiveJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetActiveJobMetrics(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteJob() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_DeleteJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::DeleteJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteJob(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_MarkJobCompleted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_MarkJobCompleted() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_MarkJobCompleted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobCompleted(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobCompletedRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMarkJobCompleted(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_MarkJobIncomplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_MarkJobIncomplete() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_MarkJobIncomplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobIncomplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMarkJobIncomplete(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextJob() {
      ::grpc::Service::MarkMethodRaw(12);
    }
    ~WithRawMethod_GetNextJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextJob(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextJobs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextJobs() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextJobs(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextJobs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJobs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobsRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextJobs(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CreateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CreateJob() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CreateJob(context, request, response); }));
    }
    ~WithRawCallbackMethod_CreateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CreateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::CreateJobRequest* /*request*/, ::carbon::frontend::jobs::CreateJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CreateJob(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UpdateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UpdateJob() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UpdateJob(context, request, response); }));
    }
    ~WithRawCallbackMethod_UpdateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::UpdateJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateJob(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartJob() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartJob(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::StartJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartJob(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StopActiveJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StopActiveJob() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StopActiveJob(context, request, response); }));
    }
    ~WithRawCallbackMethod_StopActiveJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopActiveJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StopActiveJob(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextActiveJobId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextActiveJobId() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextActiveJobId(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextActiveJobId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveJobId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* /*request*/, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveJobId(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetJob() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetJob(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetJobRequest* /*request*/, ::carbon::frontend::jobs::GetJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetJob(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetConfigDump() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetConfigDump(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetConfigDumpRequest* /*request*/, ::carbon::frontend::jobs::GetConfigDumpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetConfigDump(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetActiveJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetActiveJobMetrics() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetActiveJobMetrics(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetActiveJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetActiveJobMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteJob() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteJob(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::DeleteJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteJob(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_MarkJobCompleted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_MarkJobCompleted() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->MarkJobCompleted(context, request, response); }));
    }
    ~WithRawCallbackMethod_MarkJobCompleted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobCompleted(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobCompletedRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* MarkJobCompleted(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_MarkJobIncomplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_MarkJobIncomplete() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->MarkJobIncomplete(context, request, response); }));
    }
    ~WithRawCallbackMethod_MarkJobIncomplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkJobIncomplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* MarkJobIncomplete(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextJob() {
      ::grpc::Service::MarkMethodRawCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextJob(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextJob(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextJobs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextJobs() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::GetNextJobsRequest, ::carbon::frontend::jobs::GetNextJobsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::GetNextJobsRequest, ::carbon::frontend::jobs::GetNextJobsResponse>* streamer) {
                       return this->StreamedGetNextJobs(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextJobs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextJobs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobsRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextJobs(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::GetNextJobsRequest,::carbon::frontend::jobs::GetNextJobsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CreateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CreateJob() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::CreateJobRequest, ::carbon::frontend::jobs::CreateJobResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::CreateJobRequest, ::carbon::frontend::jobs::CreateJobResponse>* streamer) {
                       return this->StreamedCreateJob(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CreateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CreateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::CreateJobRequest* /*request*/, ::carbon::frontend::jobs::CreateJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCreateJob(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::CreateJobRequest,::carbon::frontend::jobs::CreateJobResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UpdateJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UpdateJob() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::UpdateJobRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::UpdateJobRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedUpdateJob(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UpdateJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UpdateJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::UpdateJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUpdateJob(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::UpdateJobRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartJob() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::StartJobRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::StartJobRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedStartJob(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::StartJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartJob(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::StartJobRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StopActiveJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StopActiveJob() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedStopActiveJob(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StopActiveJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StopActiveJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStopActiveJob(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextActiveJobId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextActiveJobId() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::carbon::frontend::jobs::GetNextActiveJobIdResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* streamer) {
                       return this->StreamedGetNextActiveJobId(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextActiveJobId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextActiveJobId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* /*request*/, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextActiveJobId(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::GetNextActiveJobIdRequest,::carbon::frontend::jobs::GetNextActiveJobIdResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetJob() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::GetJobRequest, ::carbon::frontend::jobs::GetJobResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::GetJobRequest, ::carbon::frontend::jobs::GetJobResponse>* streamer) {
                       return this->StreamedGetJob(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetJobRequest* /*request*/, ::carbon::frontend::jobs::GetJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetJob(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::GetJobRequest,::carbon::frontend::jobs::GetJobResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetConfigDump : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetConfigDump() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::GetConfigDumpRequest, ::carbon::frontend::jobs::GetConfigDumpResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::GetConfigDumpRequest, ::carbon::frontend::jobs::GetConfigDumpResponse>* streamer) {
                       return this->StreamedGetConfigDump(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetConfigDump() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetConfigDump(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetConfigDumpRequest* /*request*/, ::carbon::frontend::jobs::GetConfigDumpResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetConfigDump(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::GetConfigDumpRequest,::carbon::frontend::jobs::GetConfigDumpResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetActiveJobMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetActiveJobMetrics() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::GetActiveJobMetricsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* streamer) {
                       return this->StreamedGetActiveJobMetrics(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetActiveJobMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetActiveJobMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetActiveJobMetrics(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::jobs::GetActiveJobMetricsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteJob() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::DeleteJobRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::DeleteJobRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDeleteJob(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::DeleteJobRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteJob(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::DeleteJobRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_MarkJobCompleted : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_MarkJobCompleted() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::MarkJobCompletedRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::MarkJobCompletedRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedMarkJobCompleted(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_MarkJobCompleted() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status MarkJobCompleted(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobCompletedRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedMarkJobCompleted(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::MarkJobCompletedRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_MarkJobIncomplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_MarkJobIncomplete() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedMarkJobIncomplete(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_MarkJobIncomplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status MarkJobIncomplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedMarkJobIncomplete(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::MarkJobIncompleteRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextJob : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextJob() {
      ::grpc::Service::MarkMethodStreamed(12,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::jobs::GetNextJobRequest, ::carbon::frontend::jobs::GetNextJobResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::jobs::GetNextJobRequest, ::carbon::frontend::jobs::GetNextJobResponse>* streamer) {
                       return this->StreamedGetNextJob(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextJob() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextJob(::grpc::ServerContext* /*context*/, const ::carbon::frontend::jobs::GetNextJobRequest* /*request*/, ::carbon::frontend::jobs::GetNextJobResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextJob(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::jobs::GetNextJobRequest,::carbon::frontend::jobs::GetNextJobResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextJobs<WithStreamedUnaryMethod_CreateJob<WithStreamedUnaryMethod_UpdateJob<WithStreamedUnaryMethod_StartJob<WithStreamedUnaryMethod_StopActiveJob<WithStreamedUnaryMethod_GetNextActiveJobId<WithStreamedUnaryMethod_GetJob<WithStreamedUnaryMethod_GetConfigDump<WithStreamedUnaryMethod_GetActiveJobMetrics<WithStreamedUnaryMethod_DeleteJob<WithStreamedUnaryMethod_MarkJobCompleted<WithStreamedUnaryMethod_MarkJobIncomplete<WithStreamedUnaryMethod_GetNextJob<Service > > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextJobs<WithStreamedUnaryMethod_CreateJob<WithStreamedUnaryMethod_UpdateJob<WithStreamedUnaryMethod_StartJob<WithStreamedUnaryMethod_StopActiveJob<WithStreamedUnaryMethod_GetNextActiveJobId<WithStreamedUnaryMethod_GetJob<WithStreamedUnaryMethod_GetConfigDump<WithStreamedUnaryMethod_GetActiveJobMetrics<WithStreamedUnaryMethod_DeleteJob<WithStreamedUnaryMethod_MarkJobCompleted<WithStreamedUnaryMethod_MarkJobIncomplete<WithStreamedUnaryMethod_GetNextJob<Service > > > > > > > > > > > > > StreamedService;
};

}  // namespace jobs
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fjobs_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/focus.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ffocus_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ffocus_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/camera.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2ffocus_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2ffocus_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ffocus_2eproto;
namespace carbon {
namespace frontend {
namespace focus {
class FocusState;
struct FocusStateDefaultTypeInternal;
extern FocusStateDefaultTypeInternal _FocusState_default_instance_;
class FocusStateRequest;
struct FocusStateRequestDefaultTypeInternal;
extern FocusStateRequestDefaultTypeInternal _FocusStateRequest_default_instance_;
class LensSetRequest;
struct LensSetRequestDefaultTypeInternal;
extern LensSetRequestDefaultTypeInternal _LensSetRequest_default_instance_;
class PredictFocusState;
struct PredictFocusStateDefaultTypeInternal;
extern PredictFocusStateDefaultTypeInternal _PredictFocusState_default_instance_;
class TargetFocusState;
struct TargetFocusStateDefaultTypeInternal;
extern TargetFocusStateDefaultTypeInternal _TargetFocusState_default_instance_;
}  // namespace focus
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::focus::FocusState* Arena::CreateMaybeMessage<::carbon::frontend::focus::FocusState>(Arena*);
template<> ::carbon::frontend::focus::FocusStateRequest* Arena::CreateMaybeMessage<::carbon::frontend::focus::FocusStateRequest>(Arena*);
template<> ::carbon::frontend::focus::LensSetRequest* Arena::CreateMaybeMessage<::carbon::frontend::focus::LensSetRequest>(Arena*);
template<> ::carbon::frontend::focus::PredictFocusState* Arena::CreateMaybeMessage<::carbon::frontend::focus::PredictFocusState>(Arena*);
template<> ::carbon::frontend::focus::TargetFocusState* Arena::CreateMaybeMessage<::carbon::frontend::focus::TargetFocusState>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace focus {

// ===================================================================

class TargetFocusState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.focus.TargetFocusState) */ {
 public:
  inline TargetFocusState() : TargetFocusState(nullptr) {}
  ~TargetFocusState() override;
  explicit constexpr TargetFocusState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TargetFocusState(const TargetFocusState& from);
  TargetFocusState(TargetFocusState&& from) noexcept
    : TargetFocusState() {
    *this = ::std::move(from);
  }

  inline TargetFocusState& operator=(const TargetFocusState& from) {
    CopyFrom(from);
    return *this;
  }
  inline TargetFocusState& operator=(TargetFocusState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TargetFocusState& default_instance() {
    return *internal_default_instance();
  }
  static inline const TargetFocusState* internal_default_instance() {
    return reinterpret_cast<const TargetFocusState*>(
               &_TargetFocusState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TargetFocusState& a, TargetFocusState& b) {
    a.Swap(&b);
  }
  inline void Swap(TargetFocusState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TargetFocusState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TargetFocusState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TargetFocusState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TargetFocusState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TargetFocusState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TargetFocusState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.focus.TargetFocusState";
  }
  protected:
  explicit TargetFocusState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFocusProgressPctFieldNumber = 2,
    kLiquidLensValueFieldNumber = 1,
    kMaxLensValueFieldNumber = 3,
    kMinLensValueFieldNumber = 4,
    kFocusInProgressFieldNumber = 5,
  };
  // double focus_progress_pct = 2;
  void clear_focus_progress_pct();
  double focus_progress_pct() const;
  void set_focus_progress_pct(double value);
  private:
  double _internal_focus_progress_pct() const;
  void _internal_set_focus_progress_pct(double value);
  public:

  // uint32 liquid_lens_value = 1;
  void clear_liquid_lens_value();
  uint32_t liquid_lens_value() const;
  void set_liquid_lens_value(uint32_t value);
  private:
  uint32_t _internal_liquid_lens_value() const;
  void _internal_set_liquid_lens_value(uint32_t value);
  public:

  // uint32 max_lens_value = 3;
  void clear_max_lens_value();
  uint32_t max_lens_value() const;
  void set_max_lens_value(uint32_t value);
  private:
  uint32_t _internal_max_lens_value() const;
  void _internal_set_max_lens_value(uint32_t value);
  public:

  // uint32 min_lens_value = 4;
  void clear_min_lens_value();
  uint32_t min_lens_value() const;
  void set_min_lens_value(uint32_t value);
  private:
  uint32_t _internal_min_lens_value() const;
  void _internal_set_min_lens_value(uint32_t value);
  public:

  // bool focus_in_progress = 5;
  void clear_focus_in_progress();
  bool focus_in_progress() const;
  void set_focus_in_progress(bool value);
  private:
  bool _internal_focus_in_progress() const;
  void _internal_set_focus_in_progress(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.focus.TargetFocusState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double focus_progress_pct_;
  uint32_t liquid_lens_value_;
  uint32_t max_lens_value_;
  uint32_t min_lens_value_;
  bool focus_in_progress_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ffocus_2eproto;
};
// -------------------------------------------------------------------

class PredictFocusState final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.focus.PredictFocusState) */ {
 public:
  inline PredictFocusState() : PredictFocusState(nullptr) {}
  explicit constexpr PredictFocusState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PredictFocusState(const PredictFocusState& from);
  PredictFocusState(PredictFocusState&& from) noexcept
    : PredictFocusState() {
    *this = ::std::move(from);
  }

  inline PredictFocusState& operator=(const PredictFocusState& from) {
    CopyFrom(from);
    return *this;
  }
  inline PredictFocusState& operator=(PredictFocusState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PredictFocusState& default_instance() {
    return *internal_default_instance();
  }
  static inline const PredictFocusState* internal_default_instance() {
    return reinterpret_cast<const PredictFocusState*>(
               &_PredictFocusState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(PredictFocusState& a, PredictFocusState& b) {
    a.Swap(&b);
  }
  inline void Swap(PredictFocusState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PredictFocusState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PredictFocusState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PredictFocusState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const PredictFocusState& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const PredictFocusState& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.focus.PredictFocusState";
  }
  protected:
  explicit PredictFocusState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.focus.PredictFocusState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ffocus_2eproto;
};
// -------------------------------------------------------------------

class FocusState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.focus.FocusState) */ {
 public:
  inline FocusState() : FocusState(nullptr) {}
  ~FocusState() override;
  explicit constexpr FocusState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FocusState(const FocusState& from);
  FocusState(FocusState&& from) noexcept
    : FocusState() {
    *this = ::std::move(from);
  }

  inline FocusState& operator=(const FocusState& from) {
    CopyFrom(from);
    return *this;
  }
  inline FocusState& operator=(FocusState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FocusState& default_instance() {
    return *internal_default_instance();
  }
  enum TypeStateCase {
    kTarget = 2,
    kPredict = 3,
    TYPE_STATE_NOT_SET = 0,
  };

  static inline const FocusState* internal_default_instance() {
    return reinterpret_cast<const FocusState*>(
               &_FocusState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(FocusState& a, FocusState& b) {
    a.Swap(&b);
  }
  inline void Swap(FocusState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FocusState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FocusState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FocusState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FocusState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FocusState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FocusState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.focus.FocusState";
  }
  protected:
  explicit FocusState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kGlobalFocusProgressPctFieldNumber = 4,
    kGridViewEnabledFieldNumber = 5,
    kFocusInProgressFieldNumber = 6,
    kTargetFieldNumber = 2,
    kPredictFieldNumber = 3,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // double global_focus_progress_pct = 4;
  void clear_global_focus_progress_pct();
  double global_focus_progress_pct() const;
  void set_global_focus_progress_pct(double value);
  private:
  double _internal_global_focus_progress_pct() const;
  void _internal_set_global_focus_progress_pct(double value);
  public:

  // bool grid_view_enabled = 5;
  void clear_grid_view_enabled();
  bool grid_view_enabled() const;
  void set_grid_view_enabled(bool value);
  private:
  bool _internal_grid_view_enabled() const;
  void _internal_set_grid_view_enabled(bool value);
  public:

  // bool focus_in_progress = 6;
  void clear_focus_in_progress();
  bool focus_in_progress() const;
  void set_focus_in_progress(bool value);
  private:
  bool _internal_focus_in_progress() const;
  void _internal_set_focus_in_progress(bool value);
  public:

  // .carbon.frontend.focus.TargetFocusState target = 2;
  bool has_target() const;
  private:
  bool _internal_has_target() const;
  public:
  void clear_target();
  const ::carbon::frontend::focus::TargetFocusState& target() const;
  PROTOBUF_NODISCARD ::carbon::frontend::focus::TargetFocusState* release_target();
  ::carbon::frontend::focus::TargetFocusState* mutable_target();
  void set_allocated_target(::carbon::frontend::focus::TargetFocusState* target);
  private:
  const ::carbon::frontend::focus::TargetFocusState& _internal_target() const;
  ::carbon::frontend::focus::TargetFocusState* _internal_mutable_target();
  public:
  void unsafe_arena_set_allocated_target(
      ::carbon::frontend::focus::TargetFocusState* target);
  ::carbon::frontend::focus::TargetFocusState* unsafe_arena_release_target();

  // .carbon.frontend.focus.PredictFocusState predict = 3;
  bool has_predict() const;
  private:
  bool _internal_has_predict() const;
  public:
  void clear_predict();
  const ::carbon::frontend::focus::PredictFocusState& predict() const;
  PROTOBUF_NODISCARD ::carbon::frontend::focus::PredictFocusState* release_predict();
  ::carbon::frontend::focus::PredictFocusState* mutable_predict();
  void set_allocated_predict(::carbon::frontend::focus::PredictFocusState* predict);
  private:
  const ::carbon::frontend::focus::PredictFocusState& _internal_predict() const;
  ::carbon::frontend::focus::PredictFocusState* _internal_mutable_predict();
  public:
  void unsafe_arena_set_allocated_predict(
      ::carbon::frontend::focus::PredictFocusState* predict);
  ::carbon::frontend::focus::PredictFocusState* unsafe_arena_release_predict();

  void clear_type_state();
  TypeStateCase type_state_case() const;
  // @@protoc_insertion_point(class_scope:carbon.frontend.focus.FocusState)
 private:
  class _Internal;
  void set_has_target();
  void set_has_predict();

  inline bool has_type_state() const;
  inline void clear_has_type_state();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  double global_focus_progress_pct_;
  bool grid_view_enabled_;
  bool focus_in_progress_;
  union TypeStateUnion {
    constexpr TypeStateUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::frontend::focus::TargetFocusState* target_;
    ::carbon::frontend::focus::PredictFocusState* predict_;
  } type_state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_frontend_2fproto_2ffocus_2eproto;
};
// -------------------------------------------------------------------

class LensSetRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.focus.LensSetRequest) */ {
 public:
  inline LensSetRequest() : LensSetRequest(nullptr) {}
  ~LensSetRequest() override;
  explicit constexpr LensSetRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LensSetRequest(const LensSetRequest& from);
  LensSetRequest(LensSetRequest&& from) noexcept
    : LensSetRequest() {
    *this = ::std::move(from);
  }

  inline LensSetRequest& operator=(const LensSetRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline LensSetRequest& operator=(LensSetRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LensSetRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const LensSetRequest* internal_default_instance() {
    return reinterpret_cast<const LensSetRequest*>(
               &_LensSetRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LensSetRequest& a, LensSetRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(LensSetRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LensSetRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LensSetRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LensSetRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LensSetRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LensSetRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LensSetRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.focus.LensSetRequest";
  }
  protected:
  explicit LensSetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 1,
    kLensValueFieldNumber = 2,
  };
  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // uint32 lens_value = 2;
  void clear_lens_value();
  uint32_t lens_value() const;
  void set_lens_value(uint32_t value);
  private:
  uint32_t _internal_lens_value() const;
  void _internal_set_lens_value(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.focus.LensSetRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  uint32_t lens_value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ffocus_2eproto;
};
// -------------------------------------------------------------------

class FocusStateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.focus.FocusStateRequest) */ {
 public:
  inline FocusStateRequest() : FocusStateRequest(nullptr) {}
  ~FocusStateRequest() override;
  explicit constexpr FocusStateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FocusStateRequest(const FocusStateRequest& from);
  FocusStateRequest(FocusStateRequest&& from) noexcept
    : FocusStateRequest() {
    *this = ::std::move(from);
  }

  inline FocusStateRequest& operator=(const FocusStateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline FocusStateRequest& operator=(FocusStateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FocusStateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const FocusStateRequest* internal_default_instance() {
    return reinterpret_cast<const FocusStateRequest*>(
               &_FocusStateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(FocusStateRequest& a, FocusStateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(FocusStateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FocusStateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FocusStateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FocusStateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FocusStateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FocusStateRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FocusStateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.focus.FocusStateRequest";
  }
  protected:
  explicit FocusStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 1,
    kTsFieldNumber = 2,
  };
  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.focus.FocusStateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ffocus_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TargetFocusState

// uint32 liquid_lens_value = 1;
inline void TargetFocusState::clear_liquid_lens_value() {
  liquid_lens_value_ = 0u;
}
inline uint32_t TargetFocusState::_internal_liquid_lens_value() const {
  return liquid_lens_value_;
}
inline uint32_t TargetFocusState::liquid_lens_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.TargetFocusState.liquid_lens_value)
  return _internal_liquid_lens_value();
}
inline void TargetFocusState::_internal_set_liquid_lens_value(uint32_t value) {
  
  liquid_lens_value_ = value;
}
inline void TargetFocusState::set_liquid_lens_value(uint32_t value) {
  _internal_set_liquid_lens_value(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.TargetFocusState.liquid_lens_value)
}

// double focus_progress_pct = 2;
inline void TargetFocusState::clear_focus_progress_pct() {
  focus_progress_pct_ = 0;
}
inline double TargetFocusState::_internal_focus_progress_pct() const {
  return focus_progress_pct_;
}
inline double TargetFocusState::focus_progress_pct() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.TargetFocusState.focus_progress_pct)
  return _internal_focus_progress_pct();
}
inline void TargetFocusState::_internal_set_focus_progress_pct(double value) {
  
  focus_progress_pct_ = value;
}
inline void TargetFocusState::set_focus_progress_pct(double value) {
  _internal_set_focus_progress_pct(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.TargetFocusState.focus_progress_pct)
}

// uint32 max_lens_value = 3;
inline void TargetFocusState::clear_max_lens_value() {
  max_lens_value_ = 0u;
}
inline uint32_t TargetFocusState::_internal_max_lens_value() const {
  return max_lens_value_;
}
inline uint32_t TargetFocusState::max_lens_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.TargetFocusState.max_lens_value)
  return _internal_max_lens_value();
}
inline void TargetFocusState::_internal_set_max_lens_value(uint32_t value) {
  
  max_lens_value_ = value;
}
inline void TargetFocusState::set_max_lens_value(uint32_t value) {
  _internal_set_max_lens_value(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.TargetFocusState.max_lens_value)
}

// uint32 min_lens_value = 4;
inline void TargetFocusState::clear_min_lens_value() {
  min_lens_value_ = 0u;
}
inline uint32_t TargetFocusState::_internal_min_lens_value() const {
  return min_lens_value_;
}
inline uint32_t TargetFocusState::min_lens_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.TargetFocusState.min_lens_value)
  return _internal_min_lens_value();
}
inline void TargetFocusState::_internal_set_min_lens_value(uint32_t value) {
  
  min_lens_value_ = value;
}
inline void TargetFocusState::set_min_lens_value(uint32_t value) {
  _internal_set_min_lens_value(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.TargetFocusState.min_lens_value)
}

// bool focus_in_progress = 5;
inline void TargetFocusState::clear_focus_in_progress() {
  focus_in_progress_ = false;
}
inline bool TargetFocusState::_internal_focus_in_progress() const {
  return focus_in_progress_;
}
inline bool TargetFocusState::focus_in_progress() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.TargetFocusState.focus_in_progress)
  return _internal_focus_in_progress();
}
inline void TargetFocusState::_internal_set_focus_in_progress(bool value) {
  
  focus_in_progress_ = value;
}
inline void TargetFocusState::set_focus_in_progress(bool value) {
  _internal_set_focus_in_progress(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.TargetFocusState.focus_in_progress)
}

// -------------------------------------------------------------------

// PredictFocusState

// -------------------------------------------------------------------

// FocusState

// .carbon.frontend.util.Timestamp ts = 1;
inline bool FocusState::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool FocusState::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& FocusState::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& FocusState::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.FocusState.ts)
  return _internal_ts();
}
inline void FocusState::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.focus.FocusState.ts)
}
inline ::carbon::frontend::util::Timestamp* FocusState::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* FocusState::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.focus.FocusState.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* FocusState::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* FocusState::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.focus.FocusState.ts)
  return _msg;
}
inline void FocusState::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.focus.FocusState.ts)
}

// .carbon.frontend.focus.TargetFocusState target = 2;
inline bool FocusState::_internal_has_target() const {
  return type_state_case() == kTarget;
}
inline bool FocusState::has_target() const {
  return _internal_has_target();
}
inline void FocusState::set_has_target() {
  _oneof_case_[0] = kTarget;
}
inline void FocusState::clear_target() {
  if (_internal_has_target()) {
    if (GetArenaForAllocation() == nullptr) {
      delete type_state_.target_;
    }
    clear_has_type_state();
  }
}
inline ::carbon::frontend::focus::TargetFocusState* FocusState::release_target() {
  // @@protoc_insertion_point(field_release:carbon.frontend.focus.FocusState.target)
  if (_internal_has_target()) {
    clear_has_type_state();
      ::carbon::frontend::focus::TargetFocusState* temp = type_state_.target_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    type_state_.target_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::focus::TargetFocusState& FocusState::_internal_target() const {
  return _internal_has_target()
      ? *type_state_.target_
      : reinterpret_cast< ::carbon::frontend::focus::TargetFocusState&>(::carbon::frontend::focus::_TargetFocusState_default_instance_);
}
inline const ::carbon::frontend::focus::TargetFocusState& FocusState::target() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.FocusState.target)
  return _internal_target();
}
inline ::carbon::frontend::focus::TargetFocusState* FocusState::unsafe_arena_release_target() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.focus.FocusState.target)
  if (_internal_has_target()) {
    clear_has_type_state();
    ::carbon::frontend::focus::TargetFocusState* temp = type_state_.target_;
    type_state_.target_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void FocusState::unsafe_arena_set_allocated_target(::carbon::frontend::focus::TargetFocusState* target) {
  clear_type_state();
  if (target) {
    set_has_target();
    type_state_.target_ = target;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.focus.FocusState.target)
}
inline ::carbon::frontend::focus::TargetFocusState* FocusState::_internal_mutable_target() {
  if (!_internal_has_target()) {
    clear_type_state();
    set_has_target();
    type_state_.target_ = CreateMaybeMessage< ::carbon::frontend::focus::TargetFocusState >(GetArenaForAllocation());
  }
  return type_state_.target_;
}
inline ::carbon::frontend::focus::TargetFocusState* FocusState::mutable_target() {
  ::carbon::frontend::focus::TargetFocusState* _msg = _internal_mutable_target();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.focus.FocusState.target)
  return _msg;
}

// .carbon.frontend.focus.PredictFocusState predict = 3;
inline bool FocusState::_internal_has_predict() const {
  return type_state_case() == kPredict;
}
inline bool FocusState::has_predict() const {
  return _internal_has_predict();
}
inline void FocusState::set_has_predict() {
  _oneof_case_[0] = kPredict;
}
inline void FocusState::clear_predict() {
  if (_internal_has_predict()) {
    if (GetArenaForAllocation() == nullptr) {
      delete type_state_.predict_;
    }
    clear_has_type_state();
  }
}
inline ::carbon::frontend::focus::PredictFocusState* FocusState::release_predict() {
  // @@protoc_insertion_point(field_release:carbon.frontend.focus.FocusState.predict)
  if (_internal_has_predict()) {
    clear_has_type_state();
      ::carbon::frontend::focus::PredictFocusState* temp = type_state_.predict_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    type_state_.predict_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::focus::PredictFocusState& FocusState::_internal_predict() const {
  return _internal_has_predict()
      ? *type_state_.predict_
      : reinterpret_cast< ::carbon::frontend::focus::PredictFocusState&>(::carbon::frontend::focus::_PredictFocusState_default_instance_);
}
inline const ::carbon::frontend::focus::PredictFocusState& FocusState::predict() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.FocusState.predict)
  return _internal_predict();
}
inline ::carbon::frontend::focus::PredictFocusState* FocusState::unsafe_arena_release_predict() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.focus.FocusState.predict)
  if (_internal_has_predict()) {
    clear_has_type_state();
    ::carbon::frontend::focus::PredictFocusState* temp = type_state_.predict_;
    type_state_.predict_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void FocusState::unsafe_arena_set_allocated_predict(::carbon::frontend::focus::PredictFocusState* predict) {
  clear_type_state();
  if (predict) {
    set_has_predict();
    type_state_.predict_ = predict;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.focus.FocusState.predict)
}
inline ::carbon::frontend::focus::PredictFocusState* FocusState::_internal_mutable_predict() {
  if (!_internal_has_predict()) {
    clear_type_state();
    set_has_predict();
    type_state_.predict_ = CreateMaybeMessage< ::carbon::frontend::focus::PredictFocusState >(GetArenaForAllocation());
  }
  return type_state_.predict_;
}
inline ::carbon::frontend::focus::PredictFocusState* FocusState::mutable_predict() {
  ::carbon::frontend::focus::PredictFocusState* _msg = _internal_mutable_predict();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.focus.FocusState.predict)
  return _msg;
}

// double global_focus_progress_pct = 4;
inline void FocusState::clear_global_focus_progress_pct() {
  global_focus_progress_pct_ = 0;
}
inline double FocusState::_internal_global_focus_progress_pct() const {
  return global_focus_progress_pct_;
}
inline double FocusState::global_focus_progress_pct() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.FocusState.global_focus_progress_pct)
  return _internal_global_focus_progress_pct();
}
inline void FocusState::_internal_set_global_focus_progress_pct(double value) {
  
  global_focus_progress_pct_ = value;
}
inline void FocusState::set_global_focus_progress_pct(double value) {
  _internal_set_global_focus_progress_pct(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.FocusState.global_focus_progress_pct)
}

// bool grid_view_enabled = 5;
inline void FocusState::clear_grid_view_enabled() {
  grid_view_enabled_ = false;
}
inline bool FocusState::_internal_grid_view_enabled() const {
  return grid_view_enabled_;
}
inline bool FocusState::grid_view_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.FocusState.grid_view_enabled)
  return _internal_grid_view_enabled();
}
inline void FocusState::_internal_set_grid_view_enabled(bool value) {
  
  grid_view_enabled_ = value;
}
inline void FocusState::set_grid_view_enabled(bool value) {
  _internal_set_grid_view_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.FocusState.grid_view_enabled)
}

// bool focus_in_progress = 6;
inline void FocusState::clear_focus_in_progress() {
  focus_in_progress_ = false;
}
inline bool FocusState::_internal_focus_in_progress() const {
  return focus_in_progress_;
}
inline bool FocusState::focus_in_progress() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.FocusState.focus_in_progress)
  return _internal_focus_in_progress();
}
inline void FocusState::_internal_set_focus_in_progress(bool value) {
  
  focus_in_progress_ = value;
}
inline void FocusState::set_focus_in_progress(bool value) {
  _internal_set_focus_in_progress(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.FocusState.focus_in_progress)
}

inline bool FocusState::has_type_state() const {
  return type_state_case() != TYPE_STATE_NOT_SET;
}
inline void FocusState::clear_has_type_state() {
  _oneof_case_[0] = TYPE_STATE_NOT_SET;
}
inline FocusState::TypeStateCase FocusState::type_state_case() const {
  return FocusState::TypeStateCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// LensSetRequest

// string cam_id = 1;
inline void LensSetRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& LensSetRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.LensSetRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LensSetRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.LensSetRequest.cam_id)
}
inline std::string* LensSetRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.focus.LensSetRequest.cam_id)
  return _s;
}
inline const std::string& LensSetRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void LensSetRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LensSetRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LensSetRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.focus.LensSetRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LensSetRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.focus.LensSetRequest.cam_id)
}

// uint32 lens_value = 2;
inline void LensSetRequest::clear_lens_value() {
  lens_value_ = 0u;
}
inline uint32_t LensSetRequest::_internal_lens_value() const {
  return lens_value_;
}
inline uint32_t LensSetRequest::lens_value() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.LensSetRequest.lens_value)
  return _internal_lens_value();
}
inline void LensSetRequest::_internal_set_lens_value(uint32_t value) {
  
  lens_value_ = value;
}
inline void LensSetRequest::set_lens_value(uint32_t value) {
  _internal_set_lens_value(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.LensSetRequest.lens_value)
}

// -------------------------------------------------------------------

// FocusStateRequest

// string cam_id = 1;
inline void FocusStateRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& FocusStateRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.FocusStateRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void FocusStateRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.focus.FocusStateRequest.cam_id)
}
inline std::string* FocusStateRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.focus.FocusStateRequest.cam_id)
  return _s;
}
inline const std::string& FocusStateRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void FocusStateRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* FocusStateRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* FocusStateRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.focus.FocusStateRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void FocusStateRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.focus.FocusStateRequest.cam_id)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool FocusStateRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool FocusStateRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& FocusStateRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& FocusStateRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.focus.FocusStateRequest.ts)
  return _internal_ts();
}
inline void FocusStateRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.focus.FocusStateRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* FocusStateRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* FocusStateRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.focus.FocusStateRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* FocusStateRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* FocusStateRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.focus.FocusStateRequest.ts)
  return _msg;
}
inline void FocusStateRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.focus.FocusStateRequest.ts)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace focus
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ffocus_2eproto

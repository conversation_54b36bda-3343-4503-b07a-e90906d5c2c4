// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/calibration.proto

#include "frontend/proto/calibration.pb.h"
#include "frontend/proto/calibration.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace color_calibration {

static const char* CalibrationService_method_names[] = {
  "/carbon.frontend.color_calibration.CalibrationService/StartColorCalibration",
  "/carbon.frontend.color_calibration.CalibrationService/SaveColorCalibration",
};

std::unique_ptr< CalibrationService::Stub> CalibrationService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< CalibrationService::Stub> stub(new CalibrationService::Stub(channel, options));
  return stub;
}

CalibrationService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_StartColorCalibration_(CalibrationService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SaveColorCalibration_(CalibrationService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status CalibrationService::Stub::StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::color_calibration::ColorCalibrationValues, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartColorCalibration_, context, request, response);
}

void CalibrationService::Stub::async::StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::color_calibration::ColorCalibrationValues, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartColorCalibration_, context, request, response, std::move(f));
}

void CalibrationService::Stub::async::StartColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartColorCalibration_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::color_calibration::ColorCalibrationValues>* CalibrationService::Stub::PrepareAsyncStartColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::camera::CameraRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartColorCalibration_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::color_calibration::ColorCalibrationValues>* CalibrationService::Stub::AsyncStartColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartColorCalibrationRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CalibrationService::Stub::SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SaveColorCalibration_, context, request, response);
}

void CalibrationService::Stub::async::SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveColorCalibration_, context, request, response, std::move(f));
}

void CalibrationService::Stub::async::SaveColorCalibration(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveColorCalibration_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CalibrationService::Stub::PrepareAsyncSaveColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::color_calibration::ColorCalibrationValues, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SaveColorCalibration_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CalibrationService::Stub::AsyncSaveColorCalibrationRaw(::grpc::ClientContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSaveColorCalibrationRaw(context, request, cq);
  result->StartCall();
  return result;
}

CalibrationService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CalibrationService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CalibrationService::Service, ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::color_calibration::ColorCalibrationValues, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CalibrationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::camera::CameraRequest* req,
             ::carbon::frontend::color_calibration::ColorCalibrationValues* resp) {
               return service->StartColorCalibration(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CalibrationService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CalibrationService::Service, ::carbon::frontend::color_calibration::ColorCalibrationValues, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CalibrationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::color_calibration::ColorCalibrationValues* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SaveColorCalibration(ctx, req, resp);
             }, this)));
}

CalibrationService::Service::~Service() {
}

::grpc::Status CalibrationService::Service::StartColorCalibration(::grpc::ServerContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::color_calibration::ColorCalibrationValues* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CalibrationService::Service::SaveColorCalibration(::grpc::ServerContext* context, const ::carbon::frontend::color_calibration::ColorCalibrationValues* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace color_calibration


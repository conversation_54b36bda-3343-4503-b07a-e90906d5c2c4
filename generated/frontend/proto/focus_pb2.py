# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/focus.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import camera_pb2 as frontend_dot_proto_dot_camera__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/focus.proto',
  package='carbon.frontend.focus',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1a\x66rontend/proto/focus.proto\x12\x15\x63\x61rbon.frontend.focus\x1a\x1b\x66rontend/proto/camera.proto\x1a\x19\x66rontend/proto/util.proto\"\x94\x01\n\x10TargetFocusState\x12\x19\n\x11liquid_lens_value\x18\x01 \x01(\r\x12\x1a\n\x12\x66ocus_progress_pct\x18\x02 \x01(\x01\x12\x16\n\x0emax_lens_value\x18\x03 \x01(\r\x12\x16\n\x0emin_lens_value\x18\x04 \x01(\r\x12\x19\n\x11\x66ocus_in_progress\x18\x05 \x01(\x08\"\x13\n\x11PredictFocusState\"\x98\x02\n\nFocusState\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x39\n\x06target\x18\x02 \x01(\x0b\x32\'.carbon.frontend.focus.TargetFocusStateH\x00\x12;\n\x07predict\x18\x03 \x01(\x0b\x32(.carbon.frontend.focus.PredictFocusStateH\x00\x12!\n\x19global_focus_progress_pct\x18\x04 \x01(\x01\x12\x19\n\x11grid_view_enabled\x18\x05 \x01(\x08\x12\x19\n\x11\x66ocus_in_progress\x18\x06 \x01(\x08\x42\x0c\n\ntype_state\"4\n\x0eLensSetRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12\x12\n\nlens_value\x18\x02 \x01(\r\"P\n\x11\x46ocusStateRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp2\x8f\x04\n\x0c\x46ocusService\x12Q\n\x15TogglePredictGridView\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12`\n\x11GetNextFocusState\x12(.carbon.frontend.focus.FocusStateRequest\x1a!.carbon.frontend.focus.FocusState\x12\\\n\x16StartAutoFocusSpecific\x12%.carbon.frontend.camera.CameraRequest\x1a\x1b.carbon.frontend.util.Empty\x12M\n\x11StartAutoFocusAll\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12I\n\rStopAutoFocus\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12R\n\x0cSetLensValue\x12%.carbon.frontend.focus.LensSetRequest\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_camera__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_TARGETFOCUSSTATE = _descriptor.Descriptor(
  name='TargetFocusState',
  full_name='carbon.frontend.focus.TargetFocusState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='liquid_lens_value', full_name='carbon.frontend.focus.TargetFocusState.liquid_lens_value', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focus_progress_pct', full_name='carbon.frontend.focus.TargetFocusState.focus_progress_pct', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_lens_value', full_name='carbon.frontend.focus.TargetFocusState.max_lens_value', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_lens_value', full_name='carbon.frontend.focus.TargetFocusState.min_lens_value', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focus_in_progress', full_name='carbon.frontend.focus.TargetFocusState.focus_in_progress', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=110,
  serialized_end=258,
)


_PREDICTFOCUSSTATE = _descriptor.Descriptor(
  name='PredictFocusState',
  full_name='carbon.frontend.focus.PredictFocusState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=260,
  serialized_end=279,
)


_FOCUSSTATE = _descriptor.Descriptor(
  name='FocusState',
  full_name='carbon.frontend.focus.FocusState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.focus.FocusState.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target', full_name='carbon.frontend.focus.FocusState.target', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict', full_name='carbon.frontend.focus.FocusState.predict', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='global_focus_progress_pct', full_name='carbon.frontend.focus.FocusState.global_focus_progress_pct', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='grid_view_enabled', full_name='carbon.frontend.focus.FocusState.grid_view_enabled', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focus_in_progress', full_name='carbon.frontend.focus.FocusState.focus_in_progress', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='type_state', full_name='carbon.frontend.focus.FocusState.type_state',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=282,
  serialized_end=562,
)


_LENSSETREQUEST = _descriptor.Descriptor(
  name='LensSetRequest',
  full_name='carbon.frontend.focus.LensSetRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.focus.LensSetRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lens_value', full_name='carbon.frontend.focus.LensSetRequest.lens_value', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=564,
  serialized_end=616,
)


_FOCUSSTATEREQUEST = _descriptor.Descriptor(
  name='FocusStateRequest',
  full_name='carbon.frontend.focus.FocusStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.focus.FocusStateRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.focus.FocusStateRequest.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=618,
  serialized_end=698,
)

_FOCUSSTATE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_FOCUSSTATE.fields_by_name['target'].message_type = _TARGETFOCUSSTATE
_FOCUSSTATE.fields_by_name['predict'].message_type = _PREDICTFOCUSSTATE
_FOCUSSTATE.oneofs_by_name['type_state'].fields.append(
  _FOCUSSTATE.fields_by_name['target'])
_FOCUSSTATE.fields_by_name['target'].containing_oneof = _FOCUSSTATE.oneofs_by_name['type_state']
_FOCUSSTATE.oneofs_by_name['type_state'].fields.append(
  _FOCUSSTATE.fields_by_name['predict'])
_FOCUSSTATE.fields_by_name['predict'].containing_oneof = _FOCUSSTATE.oneofs_by_name['type_state']
_FOCUSSTATEREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['TargetFocusState'] = _TARGETFOCUSSTATE
DESCRIPTOR.message_types_by_name['PredictFocusState'] = _PREDICTFOCUSSTATE
DESCRIPTOR.message_types_by_name['FocusState'] = _FOCUSSTATE
DESCRIPTOR.message_types_by_name['LensSetRequest'] = _LENSSETREQUEST
DESCRIPTOR.message_types_by_name['FocusStateRequest'] = _FOCUSSTATEREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TargetFocusState = _reflection.GeneratedProtocolMessageType('TargetFocusState', (_message.Message,), {
  'DESCRIPTOR' : _TARGETFOCUSSTATE,
  '__module__' : 'frontend.proto.focus_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.focus.TargetFocusState)
  })
_sym_db.RegisterMessage(TargetFocusState)

PredictFocusState = _reflection.GeneratedProtocolMessageType('PredictFocusState', (_message.Message,), {
  'DESCRIPTOR' : _PREDICTFOCUSSTATE,
  '__module__' : 'frontend.proto.focus_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.focus.PredictFocusState)
  })
_sym_db.RegisterMessage(PredictFocusState)

FocusState = _reflection.GeneratedProtocolMessageType('FocusState', (_message.Message,), {
  'DESCRIPTOR' : _FOCUSSTATE,
  '__module__' : 'frontend.proto.focus_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.focus.FocusState)
  })
_sym_db.RegisterMessage(FocusState)

LensSetRequest = _reflection.GeneratedProtocolMessageType('LensSetRequest', (_message.Message,), {
  'DESCRIPTOR' : _LENSSETREQUEST,
  '__module__' : 'frontend.proto.focus_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.focus.LensSetRequest)
  })
_sym_db.RegisterMessage(LensSetRequest)

FocusStateRequest = _reflection.GeneratedProtocolMessageType('FocusStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _FOCUSSTATEREQUEST,
  '__module__' : 'frontend.proto.focus_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.focus.FocusStateRequest)
  })
_sym_db.RegisterMessage(FocusStateRequest)


DESCRIPTOR._options = None

_FOCUSSERVICE = _descriptor.ServiceDescriptor(
  name='FocusService',
  full_name='carbon.frontend.focus.FocusService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=701,
  serialized_end=1228,
  methods=[
  _descriptor.MethodDescriptor(
    name='TogglePredictGridView',
    full_name='carbon.frontend.focus.FocusService.TogglePredictGridView',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextFocusState',
    full_name='carbon.frontend.focus.FocusService.GetNextFocusState',
    index=1,
    containing_service=None,
    input_type=_FOCUSSTATEREQUEST,
    output_type=_FOCUSSTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartAutoFocusSpecific',
    full_name='carbon.frontend.focus.FocusService.StartAutoFocusSpecific',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_camera__pb2._CAMERAREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartAutoFocusAll',
    full_name='carbon.frontend.focus.FocusService.StartAutoFocusAll',
    index=3,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopAutoFocus',
    full_name='carbon.frontend.focus.FocusService.StopAutoFocus',
    index=4,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetLensValue',
    full_name='carbon.frontend.focus.FocusService.SetLensValue',
    index=5,
    containing_service=None,
    input_type=_LENSSETREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_FOCUSSERVICE)

DESCRIPTOR.services_by_name['FocusService'] = _FOCUSSERVICE

# @@protoc_insertion_point(module_scope)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/software.proto

#include "frontend/proto/software.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace software {
constexpr SoftwareVersion::SoftwareVersion(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : tag_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , available_(false)
  , ready_(false){}
struct SoftwareVersionDefaultTypeInternal {
  constexpr SoftwareVersionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SoftwareVersionDefaultTypeInternal() {}
  union {
    SoftwareVersion _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SoftwareVersionDefaultTypeInternal _SoftwareVersion_default_instance_;
constexpr HostSoftwareVersionState::HostSoftwareVersionState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : host_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , current_(nullptr)
  , target_(nullptr)
  , previous_(nullptr)
  , host_id_(0u)
  , active_(false)
  , updating_(false){}
struct HostSoftwareVersionStateDefaultTypeInternal {
  constexpr HostSoftwareVersionStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~HostSoftwareVersionStateDefaultTypeInternal() {}
  union {
    HostSoftwareVersionState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT HostSoftwareVersionStateDefaultTypeInternal _HostSoftwareVersionState_default_instance_;
constexpr SoftwareVersionState::SoftwareVersionState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : host_states_()
  , ts_(nullptr)
  , current_(nullptr)
  , target_(nullptr)
  , previous_(nullptr)
  , updating_(false)
  , show_software_update_to_user_(false)
  , version_mismatch_(false){}
struct SoftwareVersionStateDefaultTypeInternal {
  constexpr SoftwareVersionStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SoftwareVersionStateDefaultTypeInternal() {}
  union {
    SoftwareVersionState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SoftwareVersionStateDefaultTypeInternal _SoftwareVersionState_default_instance_;
constexpr SoftwareVersionStateRequest::SoftwareVersionStateRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , get_host_states_(false){}
struct SoftwareVersionStateRequestDefaultTypeInternal {
  constexpr SoftwareVersionStateRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SoftwareVersionStateRequestDefaultTypeInternal() {}
  union {
    SoftwareVersionStateRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SoftwareVersionStateRequestDefaultTypeInternal _SoftwareVersionStateRequest_default_instance_;
constexpr UpdateHostRequest::UpdateHostRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : host_id_(0u){}
struct UpdateHostRequestDefaultTypeInternal {
  constexpr UpdateHostRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpdateHostRequestDefaultTypeInternal() {}
  union {
    UpdateHostRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpdateHostRequestDefaultTypeInternal _UpdateHostRequest_default_instance_;
}  // namespace software
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fsoftware_2eproto[5];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fsoftware_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fsoftware_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fsoftware_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersion, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersion, tag_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersion, available_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersion, ready_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::HostSoftwareVersionState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::HostSoftwareVersionState, host_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::HostSoftwareVersionState, host_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::HostSoftwareVersionState, active_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::HostSoftwareVersionState, current_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::HostSoftwareVersionState, target_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::HostSoftwareVersionState, previous_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::HostSoftwareVersionState, updating_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, current_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, target_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, previous_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, updating_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, show_software_update_to_user_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, host_states_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionState, version_mismatch_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionStateRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::SoftwareVersionStateRequest, get_host_states_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::UpdateHostRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::software::UpdateHostRequest, host_id_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::software::SoftwareVersion)},
  { 9, -1, -1, sizeof(::carbon::frontend::software::HostSoftwareVersionState)},
  { 22, -1, -1, sizeof(::carbon::frontend::software::SoftwareVersionState)},
  { 36, -1, -1, sizeof(::carbon::frontend::software::SoftwareVersionStateRequest)},
  { 44, -1, -1, sizeof(::carbon::frontend::software::UpdateHostRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::software::_SoftwareVersion_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::software::_HostSoftwareVersionState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::software::_SoftwareVersionState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::software::_SoftwareVersionStateRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::software::_UpdateHostRequest_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fsoftware_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035frontend/proto/software.proto\022\030carbon."
  "frontend.software\032\031frontend/proto/util.p"
  "roto\"@\n\017SoftwareVersion\022\013\n\003tag\030\001 \001(\t\022\021\n\t"
  "available\030\002 \001(\010\022\r\n\005ready\030\003 \001(\010\"\224\002\n\030HostS"
  "oftwareVersionState\022\021\n\thost_name\030\001 \001(\t\022\017"
  "\n\007host_id\030\002 \001(\r\022\016\n\006active\030\003 \001(\010\022:\n\007curre"
  "nt\030\004 \001(\0132).carbon.frontend.software.Soft"
  "wareVersion\0229\n\006target\030\005 \001(\0132).carbon.fro"
  "ntend.software.SoftwareVersion\022;\n\010previo"
  "us\030\006 \001(\0132).carbon.frontend.software.Soft"
  "wareVersion\022\020\n\010updating\030\007 \001(\010\"\222\003\n\024Softwa"
  "reVersionState\022+\n\002ts\030\001 \001(\0132\037.carbon.fron"
  "tend.util.Timestamp\022:\n\007current\030\002 \001(\0132).c"
  "arbon.frontend.software.SoftwareVersion\022"
  "9\n\006target\030\003 \001(\0132).carbon.frontend.softwa"
  "re.SoftwareVersion\022;\n\010previous\030\004 \001(\0132).c"
  "arbon.frontend.software.SoftwareVersion\022"
  "\020\n\010updating\030\005 \001(\010\022$\n\034show_software_updat"
  "e_to_user\030\006 \001(\010\022G\n\013host_states\030\007 \003(\01322.c"
  "arbon.frontend.software.HostSoftwareVers"
  "ionState\022\030\n\020version_mismatch\030\010 \001(\010\"c\n\033So"
  "ftwareVersionStateRequest\022+\n\002ts\030\001 \001(\0132\037."
  "carbon.frontend.util.Timestamp\022\027\n\017get_ho"
  "st_states\030\002 \001(\010\"$\n\021UpdateHostRequest\022\017\n\007"
  "host_id\030\001 \001(\r2\310\003\n\017SoftwareService\022\204\001\n\033Ge"
  "tNextSoftwareVersionState\0225.carbon.front"
  "end.software.SoftwareVersionStateRequest"
  "\032..carbon.frontend.software.SoftwareVers"
  "ionState\022V\n\nUpdateHost\022+.carbon.frontend"
  ".software.UpdateHostRequest\032\033.carbon.fro"
  "ntend.util.Empty\022B\n\006Update\022\033.carbon.fron"
  "tend.util.Empty\032\033.carbon.frontend.util.E"
  "mpty\022B\n\006Revert\022\033.carbon.frontend.util.Em"
  "pty\032\033.carbon.frontend.util.Empty\022N\n\022FixV"
  "ersionMismatch\022\033.carbon.frontend.util.Em"
  "pty\032\033.carbon.frontend.util.EmptyB\020Z\016prot"
  "o/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fsoftware_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fsoftware_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fsoftware_2eproto = {
  false, false, 1458, descriptor_table_protodef_frontend_2fproto_2fsoftware_2eproto, "frontend/proto/software.proto", 
  &descriptor_table_frontend_2fproto_2fsoftware_2eproto_once, descriptor_table_frontend_2fproto_2fsoftware_2eproto_deps, 1, 5,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fsoftware_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fsoftware_2eproto, file_level_enum_descriptors_frontend_2fproto_2fsoftware_2eproto, file_level_service_descriptors_frontend_2fproto_2fsoftware_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fsoftware_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fsoftware_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fsoftware_2eproto(&descriptor_table_frontend_2fproto_2fsoftware_2eproto);
namespace carbon {
namespace frontend {
namespace software {

// ===================================================================

class SoftwareVersion::_Internal {
 public:
};

SoftwareVersion::SoftwareVersion(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.software.SoftwareVersion)
}
SoftwareVersion::SoftwareVersion(const SoftwareVersion& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  tag_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    tag_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_tag().empty()) {
    tag_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_tag(), 
      GetArenaForAllocation());
  }
  ::memcpy(&available_, &from.available_,
    static_cast<size_t>(reinterpret_cast<char*>(&ready_) -
    reinterpret_cast<char*>(&available_)) + sizeof(ready_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.software.SoftwareVersion)
}

inline void SoftwareVersion::SharedCtor() {
tag_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  tag_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&available_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ready_) -
    reinterpret_cast<char*>(&available_)) + sizeof(ready_));
}

SoftwareVersion::~SoftwareVersion() {
  // @@protoc_insertion_point(destructor:carbon.frontend.software.SoftwareVersion)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SoftwareVersion::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  tag_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SoftwareVersion::ArenaDtor(void* object) {
  SoftwareVersion* _this = reinterpret_cast< SoftwareVersion* >(object);
  (void)_this;
}
void SoftwareVersion::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SoftwareVersion::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SoftwareVersion::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.software.SoftwareVersion)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  tag_.ClearToEmpty();
  ::memset(&available_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ready_) -
      reinterpret_cast<char*>(&available_)) + sizeof(ready_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SoftwareVersion::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string tag = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_tag();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.software.SoftwareVersion.tag"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool available = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          available_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool ready = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          ready_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SoftwareVersion::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.software.SoftwareVersion)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string tag = 1;
  if (!this->_internal_tag().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_tag().data(), static_cast<int>(this->_internal_tag().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.software.SoftwareVersion.tag");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_tag(), target);
  }

  // bool available = 2;
  if (this->_internal_available() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_available(), target);
  }

  // bool ready = 3;
  if (this->_internal_ready() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_ready(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.software.SoftwareVersion)
  return target;
}

size_t SoftwareVersion::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.software.SoftwareVersion)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string tag = 1;
  if (!this->_internal_tag().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_tag());
  }

  // bool available = 2;
  if (this->_internal_available() != 0) {
    total_size += 1 + 1;
  }

  // bool ready = 3;
  if (this->_internal_ready() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SoftwareVersion::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SoftwareVersion::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SoftwareVersion::GetClassData() const { return &_class_data_; }

void SoftwareVersion::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SoftwareVersion *>(to)->MergeFrom(
      static_cast<const SoftwareVersion &>(from));
}


void SoftwareVersion::MergeFrom(const SoftwareVersion& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.software.SoftwareVersion)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_tag().empty()) {
    _internal_set_tag(from._internal_tag());
  }
  if (from._internal_available() != 0) {
    _internal_set_available(from._internal_available());
  }
  if (from._internal_ready() != 0) {
    _internal_set_ready(from._internal_ready());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SoftwareVersion::CopyFrom(const SoftwareVersion& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.software.SoftwareVersion)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SoftwareVersion::IsInitialized() const {
  return true;
}

void SoftwareVersion::InternalSwap(SoftwareVersion* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &tag_, lhs_arena,
      &other->tag_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SoftwareVersion, ready_)
      + sizeof(SoftwareVersion::ready_)
      - PROTOBUF_FIELD_OFFSET(SoftwareVersion, available_)>(
          reinterpret_cast<char*>(&available_),
          reinterpret_cast<char*>(&other->available_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SoftwareVersion::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fsoftware_2eproto_getter, &descriptor_table_frontend_2fproto_2fsoftware_2eproto_once,
      file_level_metadata_frontend_2fproto_2fsoftware_2eproto[0]);
}

// ===================================================================

class HostSoftwareVersionState::_Internal {
 public:
  static const ::carbon::frontend::software::SoftwareVersion& current(const HostSoftwareVersionState* msg);
  static const ::carbon::frontend::software::SoftwareVersion& target(const HostSoftwareVersionState* msg);
  static const ::carbon::frontend::software::SoftwareVersion& previous(const HostSoftwareVersionState* msg);
};

const ::carbon::frontend::software::SoftwareVersion&
HostSoftwareVersionState::_Internal::current(const HostSoftwareVersionState* msg) {
  return *msg->current_;
}
const ::carbon::frontend::software::SoftwareVersion&
HostSoftwareVersionState::_Internal::target(const HostSoftwareVersionState* msg) {
  return *msg->target_;
}
const ::carbon::frontend::software::SoftwareVersion&
HostSoftwareVersionState::_Internal::previous(const HostSoftwareVersionState* msg) {
  return *msg->previous_;
}
HostSoftwareVersionState::HostSoftwareVersionState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.software.HostSoftwareVersionState)
}
HostSoftwareVersionState::HostSoftwareVersionState(const HostSoftwareVersionState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  host_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_host_name().empty()) {
    host_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_host_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_current()) {
    current_ = new ::carbon::frontend::software::SoftwareVersion(*from.current_);
  } else {
    current_ = nullptr;
  }
  if (from._internal_has_target()) {
    target_ = new ::carbon::frontend::software::SoftwareVersion(*from.target_);
  } else {
    target_ = nullptr;
  }
  if (from._internal_has_previous()) {
    previous_ = new ::carbon::frontend::software::SoftwareVersion(*from.previous_);
  } else {
    previous_ = nullptr;
  }
  ::memcpy(&host_id_, &from.host_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&updating_) -
    reinterpret_cast<char*>(&host_id_)) + sizeof(updating_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.software.HostSoftwareVersionState)
}

inline void HostSoftwareVersionState::SharedCtor() {
host_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&current_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&updating_) -
    reinterpret_cast<char*>(&current_)) + sizeof(updating_));
}

HostSoftwareVersionState::~HostSoftwareVersionState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.software.HostSoftwareVersionState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void HostSoftwareVersionState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  host_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete current_;
  if (this != internal_default_instance()) delete target_;
  if (this != internal_default_instance()) delete previous_;
}

void HostSoftwareVersionState::ArenaDtor(void* object) {
  HostSoftwareVersionState* _this = reinterpret_cast< HostSoftwareVersionState* >(object);
  (void)_this;
}
void HostSoftwareVersionState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void HostSoftwareVersionState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void HostSoftwareVersionState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.software.HostSoftwareVersionState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  host_name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && current_ != nullptr) {
    delete current_;
  }
  current_ = nullptr;
  if (GetArenaForAllocation() == nullptr && target_ != nullptr) {
    delete target_;
  }
  target_ = nullptr;
  if (GetArenaForAllocation() == nullptr && previous_ != nullptr) {
    delete previous_;
  }
  previous_ = nullptr;
  ::memset(&host_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&updating_) -
      reinterpret_cast<char*>(&host_id_)) + sizeof(updating_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* HostSoftwareVersionState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string host_name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_host_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.software.HostSoftwareVersionState.host_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 host_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          host_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool active = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          active_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.software.SoftwareVersion current = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_current(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.software.SoftwareVersion target = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_target(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.software.SoftwareVersion previous = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_previous(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool updating = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          updating_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* HostSoftwareVersionState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.software.HostSoftwareVersionState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string host_name = 1;
  if (!this->_internal_host_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_host_name().data(), static_cast<int>(this->_internal_host_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.software.HostSoftwareVersionState.host_name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_host_name(), target);
  }

  // uint32 host_id = 2;
  if (this->_internal_host_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_host_id(), target);
  }

  // bool active = 3;
  if (this->_internal_active() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_active(), target);
  }

  // .carbon.frontend.software.SoftwareVersion current = 4;
  if (this->_internal_has_current()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::current(this), target, stream);
  }

  // .carbon.frontend.software.SoftwareVersion target = 5;
  if (this->_internal_has_target()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::target(this), target, stream);
  }

  // .carbon.frontend.software.SoftwareVersion previous = 6;
  if (this->_internal_has_previous()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::previous(this), target, stream);
  }

  // bool updating = 7;
  if (this->_internal_updating() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_updating(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.software.HostSoftwareVersionState)
  return target;
}

size_t HostSoftwareVersionState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.software.HostSoftwareVersionState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string host_name = 1;
  if (!this->_internal_host_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_host_name());
  }

  // .carbon.frontend.software.SoftwareVersion current = 4;
  if (this->_internal_has_current()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *current_);
  }

  // .carbon.frontend.software.SoftwareVersion target = 5;
  if (this->_internal_has_target()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *target_);
  }

  // .carbon.frontend.software.SoftwareVersion previous = 6;
  if (this->_internal_has_previous()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *previous_);
  }

  // uint32 host_id = 2;
  if (this->_internal_host_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_host_id());
  }

  // bool active = 3;
  if (this->_internal_active() != 0) {
    total_size += 1 + 1;
  }

  // bool updating = 7;
  if (this->_internal_updating() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData HostSoftwareVersionState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    HostSoftwareVersionState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*HostSoftwareVersionState::GetClassData() const { return &_class_data_; }

void HostSoftwareVersionState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<HostSoftwareVersionState *>(to)->MergeFrom(
      static_cast<const HostSoftwareVersionState &>(from));
}


void HostSoftwareVersionState::MergeFrom(const HostSoftwareVersionState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.software.HostSoftwareVersionState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_host_name().empty()) {
    _internal_set_host_name(from._internal_host_name());
  }
  if (from._internal_has_current()) {
    _internal_mutable_current()->::carbon::frontend::software::SoftwareVersion::MergeFrom(from._internal_current());
  }
  if (from._internal_has_target()) {
    _internal_mutable_target()->::carbon::frontend::software::SoftwareVersion::MergeFrom(from._internal_target());
  }
  if (from._internal_has_previous()) {
    _internal_mutable_previous()->::carbon::frontend::software::SoftwareVersion::MergeFrom(from._internal_previous());
  }
  if (from._internal_host_id() != 0) {
    _internal_set_host_id(from._internal_host_id());
  }
  if (from._internal_active() != 0) {
    _internal_set_active(from._internal_active());
  }
  if (from._internal_updating() != 0) {
    _internal_set_updating(from._internal_updating());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void HostSoftwareVersionState::CopyFrom(const HostSoftwareVersionState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.software.HostSoftwareVersionState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool HostSoftwareVersionState::IsInitialized() const {
  return true;
}

void HostSoftwareVersionState::InternalSwap(HostSoftwareVersionState* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &host_name_, lhs_arena,
      &other->host_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(HostSoftwareVersionState, updating_)
      + sizeof(HostSoftwareVersionState::updating_)
      - PROTOBUF_FIELD_OFFSET(HostSoftwareVersionState, current_)>(
          reinterpret_cast<char*>(&current_),
          reinterpret_cast<char*>(&other->current_));
}

::PROTOBUF_NAMESPACE_ID::Metadata HostSoftwareVersionState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fsoftware_2eproto_getter, &descriptor_table_frontend_2fproto_2fsoftware_2eproto_once,
      file_level_metadata_frontend_2fproto_2fsoftware_2eproto[1]);
}

// ===================================================================

class SoftwareVersionState::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const SoftwareVersionState* msg);
  static const ::carbon::frontend::software::SoftwareVersion& current(const SoftwareVersionState* msg);
  static const ::carbon::frontend::software::SoftwareVersion& target(const SoftwareVersionState* msg);
  static const ::carbon::frontend::software::SoftwareVersion& previous(const SoftwareVersionState* msg);
};

const ::carbon::frontend::util::Timestamp&
SoftwareVersionState::_Internal::ts(const SoftwareVersionState* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::software::SoftwareVersion&
SoftwareVersionState::_Internal::current(const SoftwareVersionState* msg) {
  return *msg->current_;
}
const ::carbon::frontend::software::SoftwareVersion&
SoftwareVersionState::_Internal::target(const SoftwareVersionState* msg) {
  return *msg->target_;
}
const ::carbon::frontend::software::SoftwareVersion&
SoftwareVersionState::_Internal::previous(const SoftwareVersionState* msg) {
  return *msg->previous_;
}
void SoftwareVersionState::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
SoftwareVersionState::SoftwareVersionState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  host_states_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.software.SoftwareVersionState)
}
SoftwareVersionState::SoftwareVersionState(const SoftwareVersionState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      host_states_(from.host_states_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_current()) {
    current_ = new ::carbon::frontend::software::SoftwareVersion(*from.current_);
  } else {
    current_ = nullptr;
  }
  if (from._internal_has_target()) {
    target_ = new ::carbon::frontend::software::SoftwareVersion(*from.target_);
  } else {
    target_ = nullptr;
  }
  if (from._internal_has_previous()) {
    previous_ = new ::carbon::frontend::software::SoftwareVersion(*from.previous_);
  } else {
    previous_ = nullptr;
  }
  ::memcpy(&updating_, &from.updating_,
    static_cast<size_t>(reinterpret_cast<char*>(&version_mismatch_) -
    reinterpret_cast<char*>(&updating_)) + sizeof(version_mismatch_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.software.SoftwareVersionState)
}

inline void SoftwareVersionState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&version_mismatch_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(version_mismatch_));
}

SoftwareVersionState::~SoftwareVersionState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.software.SoftwareVersionState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SoftwareVersionState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete current_;
  if (this != internal_default_instance()) delete target_;
  if (this != internal_default_instance()) delete previous_;
}

void SoftwareVersionState::ArenaDtor(void* object) {
  SoftwareVersionState* _this = reinterpret_cast< SoftwareVersionState* >(object);
  (void)_this;
}
void SoftwareVersionState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SoftwareVersionState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SoftwareVersionState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.software.SoftwareVersionState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  host_states_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && current_ != nullptr) {
    delete current_;
  }
  current_ = nullptr;
  if (GetArenaForAllocation() == nullptr && target_ != nullptr) {
    delete target_;
  }
  target_ = nullptr;
  if (GetArenaForAllocation() == nullptr && previous_ != nullptr) {
    delete previous_;
  }
  previous_ = nullptr;
  ::memset(&updating_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&version_mismatch_) -
      reinterpret_cast<char*>(&updating_)) + sizeof(version_mismatch_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SoftwareVersionState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.software.SoftwareVersion current = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_current(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.software.SoftwareVersion target = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_target(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.software.SoftwareVersion previous = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_previous(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool updating = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          updating_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool show_software_update_to_user = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          show_software_update_to_user_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.software.HostSoftwareVersionState host_states = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_host_states(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool version_mismatch = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          version_mismatch_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SoftwareVersionState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.software.SoftwareVersionState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.software.SoftwareVersion current = 2;
  if (this->_internal_has_current()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::current(this), target, stream);
  }

  // .carbon.frontend.software.SoftwareVersion target = 3;
  if (this->_internal_has_target()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::target(this), target, stream);
  }

  // .carbon.frontend.software.SoftwareVersion previous = 4;
  if (this->_internal_has_previous()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::previous(this), target, stream);
  }

  // bool updating = 5;
  if (this->_internal_updating() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_updating(), target);
  }

  // bool show_software_update_to_user = 6;
  if (this->_internal_show_software_update_to_user() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_show_software_update_to_user(), target);
  }

  // repeated .carbon.frontend.software.HostSoftwareVersionState host_states = 7;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_host_states_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(7, this->_internal_host_states(i), target, stream);
  }

  // bool version_mismatch = 8;
  if (this->_internal_version_mismatch() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_version_mismatch(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.software.SoftwareVersionState)
  return target;
}

size_t SoftwareVersionState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.software.SoftwareVersionState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.software.HostSoftwareVersionState host_states = 7;
  total_size += 1UL * this->_internal_host_states_size();
  for (const auto& msg : this->host_states_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.software.SoftwareVersion current = 2;
  if (this->_internal_has_current()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *current_);
  }

  // .carbon.frontend.software.SoftwareVersion target = 3;
  if (this->_internal_has_target()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *target_);
  }

  // .carbon.frontend.software.SoftwareVersion previous = 4;
  if (this->_internal_has_previous()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *previous_);
  }

  // bool updating = 5;
  if (this->_internal_updating() != 0) {
    total_size += 1 + 1;
  }

  // bool show_software_update_to_user = 6;
  if (this->_internal_show_software_update_to_user() != 0) {
    total_size += 1 + 1;
  }

  // bool version_mismatch = 8;
  if (this->_internal_version_mismatch() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SoftwareVersionState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SoftwareVersionState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SoftwareVersionState::GetClassData() const { return &_class_data_; }

void SoftwareVersionState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SoftwareVersionState *>(to)->MergeFrom(
      static_cast<const SoftwareVersionState &>(from));
}


void SoftwareVersionState::MergeFrom(const SoftwareVersionState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.software.SoftwareVersionState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  host_states_.MergeFrom(from.host_states_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_current()) {
    _internal_mutable_current()->::carbon::frontend::software::SoftwareVersion::MergeFrom(from._internal_current());
  }
  if (from._internal_has_target()) {
    _internal_mutable_target()->::carbon::frontend::software::SoftwareVersion::MergeFrom(from._internal_target());
  }
  if (from._internal_has_previous()) {
    _internal_mutable_previous()->::carbon::frontend::software::SoftwareVersion::MergeFrom(from._internal_previous());
  }
  if (from._internal_updating() != 0) {
    _internal_set_updating(from._internal_updating());
  }
  if (from._internal_show_software_update_to_user() != 0) {
    _internal_set_show_software_update_to_user(from._internal_show_software_update_to_user());
  }
  if (from._internal_version_mismatch() != 0) {
    _internal_set_version_mismatch(from._internal_version_mismatch());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SoftwareVersionState::CopyFrom(const SoftwareVersionState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.software.SoftwareVersionState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SoftwareVersionState::IsInitialized() const {
  return true;
}

void SoftwareVersionState::InternalSwap(SoftwareVersionState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  host_states_.InternalSwap(&other->host_states_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SoftwareVersionState, version_mismatch_)
      + sizeof(SoftwareVersionState::version_mismatch_)
      - PROTOBUF_FIELD_OFFSET(SoftwareVersionState, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SoftwareVersionState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fsoftware_2eproto_getter, &descriptor_table_frontend_2fproto_2fsoftware_2eproto_once,
      file_level_metadata_frontend_2fproto_2fsoftware_2eproto[2]);
}

// ===================================================================

class SoftwareVersionStateRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const SoftwareVersionStateRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
SoftwareVersionStateRequest::_Internal::ts(const SoftwareVersionStateRequest* msg) {
  return *msg->ts_;
}
void SoftwareVersionStateRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
SoftwareVersionStateRequest::SoftwareVersionStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.software.SoftwareVersionStateRequest)
}
SoftwareVersionStateRequest::SoftwareVersionStateRequest(const SoftwareVersionStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  get_host_states_ = from.get_host_states_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.software.SoftwareVersionStateRequest)
}

inline void SoftwareVersionStateRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&get_host_states_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(get_host_states_));
}

SoftwareVersionStateRequest::~SoftwareVersionStateRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.software.SoftwareVersionStateRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SoftwareVersionStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void SoftwareVersionStateRequest::ArenaDtor(void* object) {
  SoftwareVersionStateRequest* _this = reinterpret_cast< SoftwareVersionStateRequest* >(object);
  (void)_this;
}
void SoftwareVersionStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SoftwareVersionStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SoftwareVersionStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.software.SoftwareVersionStateRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  get_host_states_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SoftwareVersionStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool get_host_states = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          get_host_states_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SoftwareVersionStateRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.software.SoftwareVersionStateRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // bool get_host_states = 2;
  if (this->_internal_get_host_states() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_get_host_states(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.software.SoftwareVersionStateRequest)
  return target;
}

size_t SoftwareVersionStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.software.SoftwareVersionStateRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // bool get_host_states = 2;
  if (this->_internal_get_host_states() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SoftwareVersionStateRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SoftwareVersionStateRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SoftwareVersionStateRequest::GetClassData() const { return &_class_data_; }

void SoftwareVersionStateRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SoftwareVersionStateRequest *>(to)->MergeFrom(
      static_cast<const SoftwareVersionStateRequest &>(from));
}


void SoftwareVersionStateRequest::MergeFrom(const SoftwareVersionStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.software.SoftwareVersionStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_get_host_states() != 0) {
    _internal_set_get_host_states(from._internal_get_host_states());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SoftwareVersionStateRequest::CopyFrom(const SoftwareVersionStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.software.SoftwareVersionStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SoftwareVersionStateRequest::IsInitialized() const {
  return true;
}

void SoftwareVersionStateRequest::InternalSwap(SoftwareVersionStateRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SoftwareVersionStateRequest, get_host_states_)
      + sizeof(SoftwareVersionStateRequest::get_host_states_)
      - PROTOBUF_FIELD_OFFSET(SoftwareVersionStateRequest, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SoftwareVersionStateRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fsoftware_2eproto_getter, &descriptor_table_frontend_2fproto_2fsoftware_2eproto_once,
      file_level_metadata_frontend_2fproto_2fsoftware_2eproto[3]);
}

// ===================================================================

class UpdateHostRequest::_Internal {
 public:
};

UpdateHostRequest::UpdateHostRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.software.UpdateHostRequest)
}
UpdateHostRequest::UpdateHostRequest(const UpdateHostRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  host_id_ = from.host_id_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.software.UpdateHostRequest)
}

inline void UpdateHostRequest::SharedCtor() {
host_id_ = 0u;
}

UpdateHostRequest::~UpdateHostRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.software.UpdateHostRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UpdateHostRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void UpdateHostRequest::ArenaDtor(void* object) {
  UpdateHostRequest* _this = reinterpret_cast< UpdateHostRequest* >(object);
  (void)_this;
}
void UpdateHostRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UpdateHostRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UpdateHostRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.software.UpdateHostRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  host_id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UpdateHostRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 host_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          host_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UpdateHostRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.software.UpdateHostRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 host_id = 1;
  if (this->_internal_host_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_host_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.software.UpdateHostRequest)
  return target;
}

size_t UpdateHostRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.software.UpdateHostRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 host_id = 1;
  if (this->_internal_host_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_host_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpdateHostRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UpdateHostRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpdateHostRequest::GetClassData() const { return &_class_data_; }

void UpdateHostRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UpdateHostRequest *>(to)->MergeFrom(
      static_cast<const UpdateHostRequest &>(from));
}


void UpdateHostRequest::MergeFrom(const UpdateHostRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.software.UpdateHostRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_host_id() != 0) {
    _internal_set_host_id(from._internal_host_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UpdateHostRequest::CopyFrom(const UpdateHostRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.software.UpdateHostRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpdateHostRequest::IsInitialized() const {
  return true;
}

void UpdateHostRequest::InternalSwap(UpdateHostRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(host_id_, other->host_id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UpdateHostRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fsoftware_2eproto_getter, &descriptor_table_frontend_2fproto_2fsoftware_2eproto_once,
      file_level_metadata_frontend_2fproto_2fsoftware_2eproto[4]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace software
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::software::SoftwareVersion* Arena::CreateMaybeMessage< ::carbon::frontend::software::SoftwareVersion >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::software::SoftwareVersion >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::software::HostSoftwareVersionState* Arena::CreateMaybeMessage< ::carbon::frontend::software::HostSoftwareVersionState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::software::HostSoftwareVersionState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::software::SoftwareVersionState* Arena::CreateMaybeMessage< ::carbon::frontend::software::SoftwareVersionState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::software::SoftwareVersionState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::software::SoftwareVersionStateRequest* Arena::CreateMaybeMessage< ::carbon::frontend::software::SoftwareVersionStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::software::SoftwareVersionStateRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::software::UpdateHostRequest* Arena::CreateMaybeMessage< ::carbon::frontend::software::UpdateHostRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::software::UpdateHostRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

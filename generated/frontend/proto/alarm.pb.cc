// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/alarm.proto

#include "frontend/proto/alarm.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace alarm {
constexpr AlarmRow::AlarmRow(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : translation_parameters_()
  , alarm_code_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , subsystem_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , identifier_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0})
  , level_(0)

  , impact_(0)

  , stop_timestamp_ms_(int64_t{0})
  , acknowledged_(false)
  , autofix_available_(false)
  , autofix_attempted_(false)
  , autofix_duration_sec_(0u){}
struct AlarmRowDefaultTypeInternal {
  constexpr AlarmRowDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AlarmRowDefaultTypeInternal() {}
  union {
    AlarmRow _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AlarmRowDefaultTypeInternal _AlarmRow_default_instance_;
constexpr AlarmTable::AlarmTable(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : alarms_()
  , ts_(nullptr){}
struct AlarmTableDefaultTypeInternal {
  constexpr AlarmTableDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AlarmTableDefaultTypeInternal() {}
  union {
    AlarmTable _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AlarmTableDefaultTypeInternal _AlarmTable_default_instance_;
constexpr AlarmCount::AlarmCount(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , count_(0u){}
struct AlarmCountDefaultTypeInternal {
  constexpr AlarmCountDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AlarmCountDefaultTypeInternal() {}
  union {
    AlarmCount _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AlarmCountDefaultTypeInternal _AlarmCount_default_instance_;
constexpr AcknowledgeRequest::AcknowledgeRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : identifier_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct AcknowledgeRequestDefaultTypeInternal {
  constexpr AcknowledgeRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AcknowledgeRequestDefaultTypeInternal() {}
  union {
    AcknowledgeRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AcknowledgeRequestDefaultTypeInternal _AcknowledgeRequest_default_instance_;
constexpr GetNextAlarmLogRequest::GetNextAlarmLogRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , from_idx_(0)
  , to_idx_(0)
  , visible_only_(false){}
struct GetNextAlarmLogRequestDefaultTypeInternal {
  constexpr GetNextAlarmLogRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAlarmLogRequestDefaultTypeInternal() {}
  union {
    GetNextAlarmLogRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAlarmLogRequestDefaultTypeInternal _GetNextAlarmLogRequest_default_instance_;
constexpr GetNextAlarmLogResponse::GetNextAlarmLogResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : alarms_()
  , ts_(nullptr){}
struct GetNextAlarmLogResponseDefaultTypeInternal {
  constexpr GetNextAlarmLogResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAlarmLogResponseDefaultTypeInternal() {}
  union {
    GetNextAlarmLogResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAlarmLogResponseDefaultTypeInternal _GetNextAlarmLogResponse_default_instance_;
constexpr GetNextAlarmLogCountRequest::GetNextAlarmLogCountRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , visible_only_(false){}
struct GetNextAlarmLogCountRequestDefaultTypeInternal {
  constexpr GetNextAlarmLogCountRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAlarmLogCountRequestDefaultTypeInternal() {}
  union {
    GetNextAlarmLogCountRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAlarmLogCountRequestDefaultTypeInternal _GetNextAlarmLogCountRequest_default_instance_;
constexpr GetNextAlarmLogCountResponse::GetNextAlarmLogCountResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , num_alarms_(0){}
struct GetNextAlarmLogCountResponseDefaultTypeInternal {
  constexpr GetNextAlarmLogCountResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAlarmLogCountResponseDefaultTypeInternal() {}
  union {
    GetNextAlarmLogCountResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAlarmLogCountResponseDefaultTypeInternal _GetNextAlarmLogCountResponse_default_instance_;
constexpr AttemptAutofixAlarmRequest::AttemptAutofixAlarmRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : identifier_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct AttemptAutofixAlarmRequestDefaultTypeInternal {
  constexpr AttemptAutofixAlarmRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AttemptAutofixAlarmRequestDefaultTypeInternal() {}
  union {
    AttemptAutofixAlarmRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AttemptAutofixAlarmRequestDefaultTypeInternal _AttemptAutofixAlarmRequest_default_instance_;
constexpr GetNextAutofixAlarmStatusRequest::GetNextAutofixAlarmStatusRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct GetNextAutofixAlarmStatusRequestDefaultTypeInternal {
  constexpr GetNextAutofixAlarmStatusRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAutofixAlarmStatusRequestDefaultTypeInternal() {}
  union {
    GetNextAutofixAlarmStatusRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAutofixAlarmStatusRequestDefaultTypeInternal _GetNextAutofixAlarmStatusRequest_default_instance_;
constexpr GetNextAutofixAlarmStatusResponse::GetNextAutofixAlarmStatusResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : error_message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , completed_(false){}
struct GetNextAutofixAlarmStatusResponseDefaultTypeInternal {
  constexpr GetNextAutofixAlarmStatusResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextAutofixAlarmStatusResponseDefaultTypeInternal() {}
  union {
    GetNextAutofixAlarmStatusResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextAutofixAlarmStatusResponseDefaultTypeInternal _GetNextAutofixAlarmStatusResponse_default_instance_;
}  // namespace alarm
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2falarm_2eproto[11];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2falarm_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2falarm_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2falarm_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, alarm_code_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, subsystem_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, description_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, level_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, identifier_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, acknowledged_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, impact_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, stop_timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, autofix_available_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, autofix_attempted_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, autofix_duration_sec_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, description_key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmRow, translation_parameters_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmTable, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmTable, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmTable, alarms_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmCount, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmCount, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AlarmCount, count_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AcknowledgeRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AcknowledgeRequest, identifier_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogRequest, from_idx_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogRequest, to_idx_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogRequest, visible_only_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogResponse, alarms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogResponse, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogCountRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogCountRequest, visible_only_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogCountResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogCountResponse, num_alarms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAlarmLogCountResponse, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AttemptAutofixAlarmRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::AttemptAutofixAlarmRequest, identifier_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse, completed_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse, error_message_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::alarm::AlarmRow)},
  { 20, -1, -1, sizeof(::carbon::frontend::alarm::AlarmTable)},
  { 28, -1, -1, sizeof(::carbon::frontend::alarm::AlarmCount)},
  { 36, -1, -1, sizeof(::carbon::frontend::alarm::AcknowledgeRequest)},
  { 43, -1, -1, sizeof(::carbon::frontend::alarm::GetNextAlarmLogRequest)},
  { 53, -1, -1, sizeof(::carbon::frontend::alarm::GetNextAlarmLogResponse)},
  { 61, -1, -1, sizeof(::carbon::frontend::alarm::GetNextAlarmLogCountRequest)},
  { 69, -1, -1, sizeof(::carbon::frontend::alarm::GetNextAlarmLogCountResponse)},
  { 77, -1, -1, sizeof(::carbon::frontend::alarm::AttemptAutofixAlarmRequest)},
  { 84, -1, -1, sizeof(::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest)},
  { 91, -1, -1, sizeof(::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_AlarmRow_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_AlarmTable_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_AlarmCount_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_AcknowledgeRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_GetNextAlarmLogRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_GetNextAlarmLogResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_GetNextAlarmLogCountRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_GetNextAlarmLogCountResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_AttemptAutofixAlarmRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_GetNextAutofixAlarmStatusRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::alarm::_GetNextAutofixAlarmStatusResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2falarm_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032frontend/proto/alarm.proto\022\025carbon.fro"
  "ntend.alarm\032 frontend/proto/translation."
  "proto\032\031frontend/proto/util.proto\"\307\003\n\010Ala"
  "rmRow\022\024\n\014timestamp_ms\030\001 \001(\003\022\022\n\nalarm_cod"
  "e\030\002 \001(\t\022\021\n\tsubsystem\030\003 \001(\t\022\023\n\013descriptio"
  "n\030\004 \001(\t\0220\n\005level\030\005 \001(\0162!.carbon.frontend"
  ".alarm.AlarmLevel\022\022\n\nidentifier\030\006 \001(\t\022\024\n"
  "\014acknowledged\030\007 \001(\010\0222\n\006impact\030\010 \001(\0162\".ca"
  "rbon.frontend.alarm.AlarmImpact\022\031\n\021stop_"
  "timestamp_ms\030\t \001(\003\022\031\n\021autofix_available\030"
  "\n \001(\010\022\031\n\021autofix_attempted\030\013 \001(\010\022\034\n\024auto"
  "fix_duration_sec\030\014 \001(\r\022\027\n\017description_ke"
  "y\030\r \001(\t\022Q\n\026translation_parameters\030\016 \003(\0132"
  "1.carbon.frontend.translation.Translatio"
  "nParameter\"j\n\nAlarmTable\022+\n\002ts\030\001 \001(\0132\037.c"
  "arbon.frontend.util.Timestamp\022/\n\006alarms\030"
  "\002 \003(\0132\037.carbon.frontend.alarm.AlarmRow\"H"
  "\n\nAlarmCount\022+\n\002ts\030\001 \001(\0132\037.carbon.fronte"
  "nd.util.Timestamp\022\r\n\005count\030\002 \001(\r\"(\n\022Ackn"
  "owledgeRequest\022\022\n\nidentifier\030\001 \001(\t\"}\n\026Ge"
  "tNextAlarmLogRequest\022\020\n\010from_idx\030\001 \001(\005\022\016"
  "\n\006to_idx\030\002 \001(\005\022+\n\002ts\030\003 \001(\0132\037.carbon.fron"
  "tend.util.Timestamp\022\024\n\014visible_only\030\004 \001("
  "\010\"w\n\027GetNextAlarmLogResponse\022/\n\006alarms\030\001"
  " \003(\0132\037.carbon.frontend.alarm.AlarmRow\022+\n"
  "\002ts\030\002 \001(\0132\037.carbon.frontend.util.Timesta"
  "mp\"`\n\033GetNextAlarmLogCountRequest\022+\n\002ts\030"
  "\001 \001(\0132\037.carbon.frontend.util.Timestamp\022\024"
  "\n\014visible_only\030\002 \001(\010\"_\n\034GetNextAlarmLogC"
  "ountResponse\022\022\n\nnum_alarms\030\001 \001(\005\022+\n\002ts\030\002"
  " \001(\0132\037.carbon.frontend.util.Timestamp\"0\n"
  "\032AttemptAutofixAlarmRequest\022\022\n\nidentifie"
  "r\030\001 \001(\t\"O\n GetNextAutofixAlarmStatusRequ"
  "est\022+\n\002ts\030\001 \001(\0132\037.carbon.frontend.util.T"
  "imestamp\"z\n!GetNextAutofixAlarmStatusRes"
  "ponse\022+\n\002ts\030\001 \001(\0132\037.carbon.frontend.util"
  ".Timestamp\022\021\n\tcompleted\030\002 \001(\010\022\025\n\rerror_m"
  "essage\030\003 \001(\t*d\n\nAlarmLevel\022\016\n\nAL_UNKNOWN"
  "\020\000\022\017\n\013AL_CRITICAL\020\001\022\013\n\007AL_HIGH\020\002\022\r\n\tAL_M"
  "EDIUM\020\003\022\n\n\006AL_LOW\020\004\022\r\n\tAL_HIDDEN\020\005*\\\n\013Al"
  "armImpact\022\016\n\nAI_UNKNOWN\020\000\022\017\n\013AI_CRITICAL"
  "\020\001\022\016\n\nAI_OFFLINE\020\002\022\017\n\013AI_DEGRADED\020\003\022\013\n\007A"
  "I_NONE\020\0042\252\007\n\014AlarmService\022V\n\020GetNextAlar"
  "mList\022\037.carbon.frontend.util.Timestamp\032!"
  ".carbon.frontend.alarm.AlarmTable\022W\n\021Get"
  "NextAlarmCount\022\037.carbon.frontend.util.Ti"
  "mestamp\032!.carbon.frontend.alarm.AlarmCou"
  "nt\022Y\n\023GetNextNewAlarmList\022\037.carbon.front"
  "end.util.Timestamp\032!.carbon.frontend.ala"
  "rm.AlarmTable\022Z\n\020AcknowledgeAlarm\022).carb"
  "on.frontend.alarm.AcknowledgeRequest\032\033.c"
  "arbon.frontend.util.Empty\022G\n\013ResetAlarms"
  "\022\033.carbon.frontend.util.Empty\032\033.carbon.f"
  "rontend.util.Empty\022p\n\017GetNextAlarmLog\022-."
  "carbon.frontend.alarm.GetNextAlarmLogReq"
  "uest\032..carbon.frontend.alarm.GetNextAlar"
  "mLogResponse\022\177\n\024GetNextAlarmLogCount\0222.c"
  "arbon.frontend.alarm.GetNextAlarmLogCoun"
  "tRequest\0323.carbon.frontend.alarm.GetNext"
  "AlarmLogCountResponse\022e\n\023AttemptAutofixA"
  "larm\0221.carbon.frontend.alarm.AttemptAuto"
  "fixAlarmRequest\032\033.carbon.frontend.util.E"
  "mpty\022\216\001\n\031GetNextAutofixAlarmStatus\0227.car"
  "bon.frontend.alarm.GetNextAutofixAlarmSt"
  "atusRequest\0328.carbon.frontend.alarm.GetN"
  "extAutofixAlarmStatusResponseB\020Z\016proto/f"
  "rontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2falarm_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2ftranslation_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2falarm_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2falarm_2eproto = {
  false, false, 2655, descriptor_table_protodef_frontend_2fproto_2falarm_2eproto, "frontend/proto/alarm.proto", 
  &descriptor_table_frontend_2fproto_2falarm_2eproto_once, descriptor_table_frontend_2fproto_2falarm_2eproto_deps, 2, 11,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2falarm_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2falarm_2eproto, file_level_enum_descriptors_frontend_2fproto_2falarm_2eproto, file_level_service_descriptors_frontend_2fproto_2falarm_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2falarm_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2falarm_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2falarm_2eproto(&descriptor_table_frontend_2fproto_2falarm_2eproto);
namespace carbon {
namespace frontend {
namespace alarm {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmLevel_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2falarm_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2falarm_2eproto[0];
}
bool AlarmLevel_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmImpact_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2falarm_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2falarm_2eproto[1];
}
bool AlarmImpact_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class AlarmRow::_Internal {
 public:
};

void AlarmRow::clear_translation_parameters() {
  translation_parameters_.Clear();
}
AlarmRow::AlarmRow(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  translation_parameters_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.AlarmRow)
}
AlarmRow::AlarmRow(const AlarmRow& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      translation_parameters_(from.translation_parameters_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  alarm_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    alarm_code_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_alarm_code().empty()) {
    alarm_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_alarm_code(), 
      GetArenaForAllocation());
  }
  subsystem_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    subsystem_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_subsystem().empty()) {
    subsystem_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_subsystem(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  identifier_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_identifier().empty()) {
    identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_identifier(), 
      GetArenaForAllocation());
  }
  description_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description_key().empty()) {
    description_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description_key(), 
      GetArenaForAllocation());
  }
  ::memcpy(&timestamp_ms_, &from.timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&autofix_duration_sec_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(autofix_duration_sec_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.AlarmRow)
}

inline void AlarmRow::SharedCtor() {
alarm_code_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  alarm_code_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
subsystem_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  subsystem_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
identifier_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
description_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&autofix_duration_sec_) -
    reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(autofix_duration_sec_));
}

AlarmRow::~AlarmRow() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.AlarmRow)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AlarmRow::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  alarm_code_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  subsystem_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  identifier_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AlarmRow::ArenaDtor(void* object) {
  AlarmRow* _this = reinterpret_cast< AlarmRow* >(object);
  (void)_this;
}
void AlarmRow::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AlarmRow::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AlarmRow::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.AlarmRow)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  translation_parameters_.Clear();
  alarm_code_.ClearToEmpty();
  subsystem_.ClearToEmpty();
  description_.ClearToEmpty();
  identifier_.ClearToEmpty();
  description_key_.ClearToEmpty();
  ::memset(&timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&autofix_duration_sec_) -
      reinterpret_cast<char*>(&timestamp_ms_)) + sizeof(autofix_duration_sec_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AlarmRow::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string alarm_code = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_alarm_code();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.alarm.AlarmRow.alarm_code"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string subsystem = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_subsystem();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.alarm.AlarmRow.subsystem"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.alarm.AlarmRow.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.alarm.AlarmLevel level = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_level(static_cast<::carbon::frontend::alarm::AlarmLevel>(val));
        } else
          goto handle_unusual;
        continue;
      // string identifier = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_identifier();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.alarm.AlarmRow.identifier"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool acknowledged = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          acknowledged_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.alarm.AlarmImpact impact = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_impact(static_cast<::carbon::frontend::alarm::AlarmImpact>(val));
        } else
          goto handle_unusual;
        continue;
      // int64 stop_timestamp_ms = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          stop_timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool autofix_available = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          autofix_available_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool autofix_attempted = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          autofix_attempted_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 autofix_duration_sec = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          autofix_duration_sec_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description_key = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          auto str = _internal_mutable_description_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.alarm.AlarmRow.description_key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.translation.TranslationParameter translation_parameters = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_translation_parameters(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<114>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AlarmRow::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.AlarmRow)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // string alarm_code = 2;
  if (!this->_internal_alarm_code().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_alarm_code().data(), static_cast<int>(this->_internal_alarm_code().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.alarm.AlarmRow.alarm_code");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_alarm_code(), target);
  }

  // string subsystem = 3;
  if (!this->_internal_subsystem().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_subsystem().data(), static_cast<int>(this->_internal_subsystem().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.alarm.AlarmRow.subsystem");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_subsystem(), target);
  }

  // string description = 4;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.alarm.AlarmRow.description");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_description(), target);
  }

  // .carbon.frontend.alarm.AlarmLevel level = 5;
  if (this->_internal_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->_internal_level(), target);
  }

  // string identifier = 6;
  if (!this->_internal_identifier().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_identifier().data(), static_cast<int>(this->_internal_identifier().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.alarm.AlarmRow.identifier");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_identifier(), target);
  }

  // bool acknowledged = 7;
  if (this->_internal_acknowledged() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_acknowledged(), target);
  }

  // .carbon.frontend.alarm.AlarmImpact impact = 8;
  if (this->_internal_impact() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      8, this->_internal_impact(), target);
  }

  // int64 stop_timestamp_ms = 9;
  if (this->_internal_stop_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(9, this->_internal_stop_timestamp_ms(), target);
  }

  // bool autofix_available = 10;
  if (this->_internal_autofix_available() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(10, this->_internal_autofix_available(), target);
  }

  // bool autofix_attempted = 11;
  if (this->_internal_autofix_attempted() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(11, this->_internal_autofix_attempted(), target);
  }

  // uint32 autofix_duration_sec = 12;
  if (this->_internal_autofix_duration_sec() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(12, this->_internal_autofix_duration_sec(), target);
  }

  // string description_key = 13;
  if (!this->_internal_description_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description_key().data(), static_cast<int>(this->_internal_description_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.alarm.AlarmRow.description_key");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_description_key(), target);
  }

  // repeated .carbon.frontend.translation.TranslationParameter translation_parameters = 14;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_translation_parameters_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(14, this->_internal_translation_parameters(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.AlarmRow)
  return target;
}

size_t AlarmRow::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.AlarmRow)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.translation.TranslationParameter translation_parameters = 14;
  total_size += 1UL * this->_internal_translation_parameters_size();
  for (const auto& msg : this->translation_parameters_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string alarm_code = 2;
  if (!this->_internal_alarm_code().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_alarm_code());
  }

  // string subsystem = 3;
  if (!this->_internal_subsystem().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_subsystem());
  }

  // string description = 4;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // string identifier = 6;
  if (!this->_internal_identifier().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_identifier());
  }

  // string description_key = 13;
  if (!this->_internal_description_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description_key());
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  // .carbon.frontend.alarm.AlarmLevel level = 5;
  if (this->_internal_level() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_level());
  }

  // .carbon.frontend.alarm.AlarmImpact impact = 8;
  if (this->_internal_impact() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_impact());
  }

  // int64 stop_timestamp_ms = 9;
  if (this->_internal_stop_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_stop_timestamp_ms());
  }

  // bool acknowledged = 7;
  if (this->_internal_acknowledged() != 0) {
    total_size += 1 + 1;
  }

  // bool autofix_available = 10;
  if (this->_internal_autofix_available() != 0) {
    total_size += 1 + 1;
  }

  // bool autofix_attempted = 11;
  if (this->_internal_autofix_attempted() != 0) {
    total_size += 1 + 1;
  }

  // uint32 autofix_duration_sec = 12;
  if (this->_internal_autofix_duration_sec() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_autofix_duration_sec());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AlarmRow::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AlarmRow::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AlarmRow::GetClassData() const { return &_class_data_; }

void AlarmRow::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AlarmRow *>(to)->MergeFrom(
      static_cast<const AlarmRow &>(from));
}


void AlarmRow::MergeFrom(const AlarmRow& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.AlarmRow)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  translation_parameters_.MergeFrom(from.translation_parameters_);
  if (!from._internal_alarm_code().empty()) {
    _internal_set_alarm_code(from._internal_alarm_code());
  }
  if (!from._internal_subsystem().empty()) {
    _internal_set_subsystem(from._internal_subsystem());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (!from._internal_identifier().empty()) {
    _internal_set_identifier(from._internal_identifier());
  }
  if (!from._internal_description_key().empty()) {
    _internal_set_description_key(from._internal_description_key());
  }
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  if (from._internal_level() != 0) {
    _internal_set_level(from._internal_level());
  }
  if (from._internal_impact() != 0) {
    _internal_set_impact(from._internal_impact());
  }
  if (from._internal_stop_timestamp_ms() != 0) {
    _internal_set_stop_timestamp_ms(from._internal_stop_timestamp_ms());
  }
  if (from._internal_acknowledged() != 0) {
    _internal_set_acknowledged(from._internal_acknowledged());
  }
  if (from._internal_autofix_available() != 0) {
    _internal_set_autofix_available(from._internal_autofix_available());
  }
  if (from._internal_autofix_attempted() != 0) {
    _internal_set_autofix_attempted(from._internal_autofix_attempted());
  }
  if (from._internal_autofix_duration_sec() != 0) {
    _internal_set_autofix_duration_sec(from._internal_autofix_duration_sec());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AlarmRow::CopyFrom(const AlarmRow& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.AlarmRow)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AlarmRow::IsInitialized() const {
  return true;
}

void AlarmRow::InternalSwap(AlarmRow* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  translation_parameters_.InternalSwap(&other->translation_parameters_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &alarm_code_, lhs_arena,
      &other->alarm_code_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &subsystem_, lhs_arena,
      &other->subsystem_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &identifier_, lhs_arena,
      &other->identifier_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_key_, lhs_arena,
      &other->description_key_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AlarmRow, autofix_duration_sec_)
      + sizeof(AlarmRow::autofix_duration_sec_)
      - PROTOBUF_FIELD_OFFSET(AlarmRow, timestamp_ms_)>(
          reinterpret_cast<char*>(&timestamp_ms_),
          reinterpret_cast<char*>(&other->timestamp_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AlarmRow::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[0]);
}

// ===================================================================

class AlarmTable::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const AlarmTable* msg);
};

const ::carbon::frontend::util::Timestamp&
AlarmTable::_Internal::ts(const AlarmTable* msg) {
  return *msg->ts_;
}
void AlarmTable::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
AlarmTable::AlarmTable(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  alarms_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.AlarmTable)
}
AlarmTable::AlarmTable(const AlarmTable& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      alarms_(from.alarms_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.AlarmTable)
}

inline void AlarmTable::SharedCtor() {
ts_ = nullptr;
}

AlarmTable::~AlarmTable() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.AlarmTable)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AlarmTable::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void AlarmTable::ArenaDtor(void* object) {
  AlarmTable* _this = reinterpret_cast< AlarmTable* >(object);
  (void)_this;
}
void AlarmTable::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AlarmTable::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AlarmTable::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.AlarmTable)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  alarms_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AlarmTable::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_alarms(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AlarmTable::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.AlarmTable)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_alarms_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_alarms(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.AlarmTable)
  return target;
}

size_t AlarmTable::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.AlarmTable)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
  total_size += 1UL * this->_internal_alarms_size();
  for (const auto& msg : this->alarms_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AlarmTable::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AlarmTable::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AlarmTable::GetClassData() const { return &_class_data_; }

void AlarmTable::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AlarmTable *>(to)->MergeFrom(
      static_cast<const AlarmTable &>(from));
}


void AlarmTable::MergeFrom(const AlarmTable& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.AlarmTable)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  alarms_.MergeFrom(from.alarms_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AlarmTable::CopyFrom(const AlarmTable& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.AlarmTable)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AlarmTable::IsInitialized() const {
  return true;
}

void AlarmTable::InternalSwap(AlarmTable* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  alarms_.InternalSwap(&other->alarms_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AlarmTable::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[1]);
}

// ===================================================================

class AlarmCount::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const AlarmCount* msg);
};

const ::carbon::frontend::util::Timestamp&
AlarmCount::_Internal::ts(const AlarmCount* msg) {
  return *msg->ts_;
}
void AlarmCount::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
AlarmCount::AlarmCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.AlarmCount)
}
AlarmCount::AlarmCount(const AlarmCount& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  count_ = from.count_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.AlarmCount)
}

inline void AlarmCount::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&count_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(count_));
}

AlarmCount::~AlarmCount() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.AlarmCount)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AlarmCount::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void AlarmCount::ArenaDtor(void* object) {
  AlarmCount* _this = reinterpret_cast< AlarmCount* >(object);
  (void)_this;
}
void AlarmCount::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AlarmCount::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AlarmCount::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.AlarmCount)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  count_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AlarmCount::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 count = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AlarmCount::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.AlarmCount)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // uint32 count = 2;
  if (this->_internal_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_count(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.AlarmCount)
  return target;
}

size_t AlarmCount::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.AlarmCount)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // uint32 count = 2;
  if (this->_internal_count() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_count());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AlarmCount::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AlarmCount::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AlarmCount::GetClassData() const { return &_class_data_; }

void AlarmCount::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AlarmCount *>(to)->MergeFrom(
      static_cast<const AlarmCount &>(from));
}


void AlarmCount::MergeFrom(const AlarmCount& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.AlarmCount)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_count() != 0) {
    _internal_set_count(from._internal_count());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AlarmCount::CopyFrom(const AlarmCount& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.AlarmCount)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AlarmCount::IsInitialized() const {
  return true;
}

void AlarmCount::InternalSwap(AlarmCount* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AlarmCount, count_)
      + sizeof(AlarmCount::count_)
      - PROTOBUF_FIELD_OFFSET(AlarmCount, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AlarmCount::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[2]);
}

// ===================================================================

class AcknowledgeRequest::_Internal {
 public:
};

AcknowledgeRequest::AcknowledgeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.AcknowledgeRequest)
}
AcknowledgeRequest::AcknowledgeRequest(const AcknowledgeRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  identifier_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_identifier().empty()) {
    identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_identifier(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.AcknowledgeRequest)
}

inline void AcknowledgeRequest::SharedCtor() {
identifier_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

AcknowledgeRequest::~AcknowledgeRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.AcknowledgeRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AcknowledgeRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  identifier_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AcknowledgeRequest::ArenaDtor(void* object) {
  AcknowledgeRequest* _this = reinterpret_cast< AcknowledgeRequest* >(object);
  (void)_this;
}
void AcknowledgeRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AcknowledgeRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AcknowledgeRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.AcknowledgeRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  identifier_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AcknowledgeRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string identifier = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_identifier();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.alarm.AcknowledgeRequest.identifier"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AcknowledgeRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.AcknowledgeRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string identifier = 1;
  if (!this->_internal_identifier().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_identifier().data(), static_cast<int>(this->_internal_identifier().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.alarm.AcknowledgeRequest.identifier");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_identifier(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.AcknowledgeRequest)
  return target;
}

size_t AcknowledgeRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.AcknowledgeRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string identifier = 1;
  if (!this->_internal_identifier().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_identifier());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AcknowledgeRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AcknowledgeRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AcknowledgeRequest::GetClassData() const { return &_class_data_; }

void AcknowledgeRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AcknowledgeRequest *>(to)->MergeFrom(
      static_cast<const AcknowledgeRequest &>(from));
}


void AcknowledgeRequest::MergeFrom(const AcknowledgeRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.AcknowledgeRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_identifier().empty()) {
    _internal_set_identifier(from._internal_identifier());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AcknowledgeRequest::CopyFrom(const AcknowledgeRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.AcknowledgeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AcknowledgeRequest::IsInitialized() const {
  return true;
}

void AcknowledgeRequest::InternalSwap(AcknowledgeRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &identifier_, lhs_arena,
      &other->identifier_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata AcknowledgeRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[3]);
}

// ===================================================================

class GetNextAlarmLogRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextAlarmLogRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextAlarmLogRequest::_Internal::ts(const GetNextAlarmLogRequest* msg) {
  return *msg->ts_;
}
void GetNextAlarmLogRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextAlarmLogRequest::GetNextAlarmLogRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.GetNextAlarmLogRequest)
}
GetNextAlarmLogRequest::GetNextAlarmLogRequest(const GetNextAlarmLogRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&from_idx_, &from.from_idx_,
    static_cast<size_t>(reinterpret_cast<char*>(&visible_only_) -
    reinterpret_cast<char*>(&from_idx_)) + sizeof(visible_only_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.GetNextAlarmLogRequest)
}

inline void GetNextAlarmLogRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&visible_only_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(visible_only_));
}

GetNextAlarmLogRequest::~GetNextAlarmLogRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.GetNextAlarmLogRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextAlarmLogRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextAlarmLogRequest::ArenaDtor(void* object) {
  GetNextAlarmLogRequest* _this = reinterpret_cast< GetNextAlarmLogRequest* >(object);
  (void)_this;
}
void GetNextAlarmLogRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextAlarmLogRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextAlarmLogRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.GetNextAlarmLogRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&from_idx_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&visible_only_) -
      reinterpret_cast<char*>(&from_idx_)) + sizeof(visible_only_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextAlarmLogRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 from_idx = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          from_idx_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 to_idx = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          to_idx_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool visible_only = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          visible_only_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextAlarmLogRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.GetNextAlarmLogRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 from_idx = 1;
  if (this->_internal_from_idx() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_from_idx(), target);
  }

  // int32 to_idx = 2;
  if (this->_internal_to_idx() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_to_idx(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 3;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::ts(this), target, stream);
  }

  // bool visible_only = 4;
  if (this->_internal_visible_only() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_visible_only(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.GetNextAlarmLogRequest)
  return target;
}

size_t GetNextAlarmLogRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.GetNextAlarmLogRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 3;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // int32 from_idx = 1;
  if (this->_internal_from_idx() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_from_idx());
  }

  // int32 to_idx = 2;
  if (this->_internal_to_idx() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_to_idx());
  }

  // bool visible_only = 4;
  if (this->_internal_visible_only() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextAlarmLogRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextAlarmLogRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextAlarmLogRequest::GetClassData() const { return &_class_data_; }

void GetNextAlarmLogRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextAlarmLogRequest *>(to)->MergeFrom(
      static_cast<const GetNextAlarmLogRequest &>(from));
}


void GetNextAlarmLogRequest::MergeFrom(const GetNextAlarmLogRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.GetNextAlarmLogRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_from_idx() != 0) {
    _internal_set_from_idx(from._internal_from_idx());
  }
  if (from._internal_to_idx() != 0) {
    _internal_set_to_idx(from._internal_to_idx());
  }
  if (from._internal_visible_only() != 0) {
    _internal_set_visible_only(from._internal_visible_only());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextAlarmLogRequest::CopyFrom(const GetNextAlarmLogRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.GetNextAlarmLogRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextAlarmLogRequest::IsInitialized() const {
  return true;
}

void GetNextAlarmLogRequest::InternalSwap(GetNextAlarmLogRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextAlarmLogRequest, visible_only_)
      + sizeof(GetNextAlarmLogRequest::visible_only_)
      - PROTOBUF_FIELD_OFFSET(GetNextAlarmLogRequest, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextAlarmLogRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[4]);
}

// ===================================================================

class GetNextAlarmLogResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextAlarmLogResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextAlarmLogResponse::_Internal::ts(const GetNextAlarmLogResponse* msg) {
  return *msg->ts_;
}
void GetNextAlarmLogResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextAlarmLogResponse::GetNextAlarmLogResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  alarms_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.GetNextAlarmLogResponse)
}
GetNextAlarmLogResponse::GetNextAlarmLogResponse(const GetNextAlarmLogResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      alarms_(from.alarms_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.GetNextAlarmLogResponse)
}

inline void GetNextAlarmLogResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextAlarmLogResponse::~GetNextAlarmLogResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.GetNextAlarmLogResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextAlarmLogResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextAlarmLogResponse::ArenaDtor(void* object) {
  GetNextAlarmLogResponse* _this = reinterpret_cast< GetNextAlarmLogResponse* >(object);
  (void)_this;
}
void GetNextAlarmLogResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextAlarmLogResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextAlarmLogResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.GetNextAlarmLogResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  alarms_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextAlarmLogResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_alarms(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextAlarmLogResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.GetNextAlarmLogResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_alarms_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_alarms(i), target, stream);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.GetNextAlarmLogResponse)
  return target;
}

size_t GetNextAlarmLogResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.GetNextAlarmLogResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
  total_size += 1UL * this->_internal_alarms_size();
  for (const auto& msg : this->alarms_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextAlarmLogResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextAlarmLogResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextAlarmLogResponse::GetClassData() const { return &_class_data_; }

void GetNextAlarmLogResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextAlarmLogResponse *>(to)->MergeFrom(
      static_cast<const GetNextAlarmLogResponse &>(from));
}


void GetNextAlarmLogResponse::MergeFrom(const GetNextAlarmLogResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.GetNextAlarmLogResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  alarms_.MergeFrom(from.alarms_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextAlarmLogResponse::CopyFrom(const GetNextAlarmLogResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.GetNextAlarmLogResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextAlarmLogResponse::IsInitialized() const {
  return true;
}

void GetNextAlarmLogResponse::InternalSwap(GetNextAlarmLogResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  alarms_.InternalSwap(&other->alarms_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextAlarmLogResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[5]);
}

// ===================================================================

class GetNextAlarmLogCountRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextAlarmLogCountRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextAlarmLogCountRequest::_Internal::ts(const GetNextAlarmLogCountRequest* msg) {
  return *msg->ts_;
}
void GetNextAlarmLogCountRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextAlarmLogCountRequest::GetNextAlarmLogCountRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
}
GetNextAlarmLogCountRequest::GetNextAlarmLogCountRequest(const GetNextAlarmLogCountRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  visible_only_ = from.visible_only_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
}

inline void GetNextAlarmLogCountRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&visible_only_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(visible_only_));
}

GetNextAlarmLogCountRequest::~GetNextAlarmLogCountRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextAlarmLogCountRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextAlarmLogCountRequest::ArenaDtor(void* object) {
  GetNextAlarmLogCountRequest* _this = reinterpret_cast< GetNextAlarmLogCountRequest* >(object);
  (void)_this;
}
void GetNextAlarmLogCountRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextAlarmLogCountRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextAlarmLogCountRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  visible_only_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextAlarmLogCountRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool visible_only = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          visible_only_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextAlarmLogCountRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // bool visible_only = 2;
  if (this->_internal_visible_only() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_visible_only(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
  return target;
}

size_t GetNextAlarmLogCountRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // bool visible_only = 2;
  if (this->_internal_visible_only() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextAlarmLogCountRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextAlarmLogCountRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextAlarmLogCountRequest::GetClassData() const { return &_class_data_; }

void GetNextAlarmLogCountRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextAlarmLogCountRequest *>(to)->MergeFrom(
      static_cast<const GetNextAlarmLogCountRequest &>(from));
}


void GetNextAlarmLogCountRequest::MergeFrom(const GetNextAlarmLogCountRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_visible_only() != 0) {
    _internal_set_visible_only(from._internal_visible_only());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextAlarmLogCountRequest::CopyFrom(const GetNextAlarmLogCountRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextAlarmLogCountRequest::IsInitialized() const {
  return true;
}

void GetNextAlarmLogCountRequest::InternalSwap(GetNextAlarmLogCountRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextAlarmLogCountRequest, visible_only_)
      + sizeof(GetNextAlarmLogCountRequest::visible_only_)
      - PROTOBUF_FIELD_OFFSET(GetNextAlarmLogCountRequest, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextAlarmLogCountRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[6]);
}

// ===================================================================

class GetNextAlarmLogCountResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextAlarmLogCountResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextAlarmLogCountResponse::_Internal::ts(const GetNextAlarmLogCountResponse* msg) {
  return *msg->ts_;
}
void GetNextAlarmLogCountResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextAlarmLogCountResponse::GetNextAlarmLogCountResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
}
GetNextAlarmLogCountResponse::GetNextAlarmLogCountResponse(const GetNextAlarmLogCountResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  num_alarms_ = from.num_alarms_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
}

inline void GetNextAlarmLogCountResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&num_alarms_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(num_alarms_));
}

GetNextAlarmLogCountResponse::~GetNextAlarmLogCountResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextAlarmLogCountResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextAlarmLogCountResponse::ArenaDtor(void* object) {
  GetNextAlarmLogCountResponse* _this = reinterpret_cast< GetNextAlarmLogCountResponse* >(object);
  (void)_this;
}
void GetNextAlarmLogCountResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextAlarmLogCountResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextAlarmLogCountResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  num_alarms_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextAlarmLogCountResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 num_alarms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          num_alarms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextAlarmLogCountResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_alarms = 1;
  if (this->_internal_num_alarms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_num_alarms(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
  return target;
}

size_t GetNextAlarmLogCountResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // int32 num_alarms = 1;
  if (this->_internal_num_alarms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_num_alarms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextAlarmLogCountResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextAlarmLogCountResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextAlarmLogCountResponse::GetClassData() const { return &_class_data_; }

void GetNextAlarmLogCountResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextAlarmLogCountResponse *>(to)->MergeFrom(
      static_cast<const GetNextAlarmLogCountResponse &>(from));
}


void GetNextAlarmLogCountResponse::MergeFrom(const GetNextAlarmLogCountResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_num_alarms() != 0) {
    _internal_set_num_alarms(from._internal_num_alarms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextAlarmLogCountResponse::CopyFrom(const GetNextAlarmLogCountResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextAlarmLogCountResponse::IsInitialized() const {
  return true;
}

void GetNextAlarmLogCountResponse::InternalSwap(GetNextAlarmLogCountResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextAlarmLogCountResponse, num_alarms_)
      + sizeof(GetNextAlarmLogCountResponse::num_alarms_)
      - PROTOBUF_FIELD_OFFSET(GetNextAlarmLogCountResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextAlarmLogCountResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[7]);
}

// ===================================================================

class AttemptAutofixAlarmRequest::_Internal {
 public:
};

AttemptAutofixAlarmRequest::AttemptAutofixAlarmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
}
AttemptAutofixAlarmRequest::AttemptAutofixAlarmRequest(const AttemptAutofixAlarmRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  identifier_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_identifier().empty()) {
    identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_identifier(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
}

inline void AttemptAutofixAlarmRequest::SharedCtor() {
identifier_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

AttemptAutofixAlarmRequest::~AttemptAutofixAlarmRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AttemptAutofixAlarmRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  identifier_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AttemptAutofixAlarmRequest::ArenaDtor(void* object) {
  AttemptAutofixAlarmRequest* _this = reinterpret_cast< AttemptAutofixAlarmRequest* >(object);
  (void)_this;
}
void AttemptAutofixAlarmRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AttemptAutofixAlarmRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AttemptAutofixAlarmRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  identifier_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AttemptAutofixAlarmRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string identifier = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_identifier();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.alarm.AttemptAutofixAlarmRequest.identifier"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AttemptAutofixAlarmRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string identifier = 1;
  if (!this->_internal_identifier().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_identifier().data(), static_cast<int>(this->_internal_identifier().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.alarm.AttemptAutofixAlarmRequest.identifier");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_identifier(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
  return target;
}

size_t AttemptAutofixAlarmRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string identifier = 1;
  if (!this->_internal_identifier().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_identifier());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AttemptAutofixAlarmRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AttemptAutofixAlarmRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AttemptAutofixAlarmRequest::GetClassData() const { return &_class_data_; }

void AttemptAutofixAlarmRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AttemptAutofixAlarmRequest *>(to)->MergeFrom(
      static_cast<const AttemptAutofixAlarmRequest &>(from));
}


void AttemptAutofixAlarmRequest::MergeFrom(const AttemptAutofixAlarmRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_identifier().empty()) {
    _internal_set_identifier(from._internal_identifier());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AttemptAutofixAlarmRequest::CopyFrom(const AttemptAutofixAlarmRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AttemptAutofixAlarmRequest::IsInitialized() const {
  return true;
}

void AttemptAutofixAlarmRequest::InternalSwap(AttemptAutofixAlarmRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &identifier_, lhs_arena,
      &other->identifier_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata AttemptAutofixAlarmRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[8]);
}

// ===================================================================

class GetNextAutofixAlarmStatusRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextAutofixAlarmStatusRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextAutofixAlarmStatusRequest::_Internal::ts(const GetNextAutofixAlarmStatusRequest* msg) {
  return *msg->ts_;
}
void GetNextAutofixAlarmStatusRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextAutofixAlarmStatusRequest::GetNextAutofixAlarmStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
}
GetNextAutofixAlarmStatusRequest::GetNextAutofixAlarmStatusRequest(const GetNextAutofixAlarmStatusRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
}

inline void GetNextAutofixAlarmStatusRequest::SharedCtor() {
ts_ = nullptr;
}

GetNextAutofixAlarmStatusRequest::~GetNextAutofixAlarmStatusRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextAutofixAlarmStatusRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextAutofixAlarmStatusRequest::ArenaDtor(void* object) {
  GetNextAutofixAlarmStatusRequest* _this = reinterpret_cast< GetNextAutofixAlarmStatusRequest* >(object);
  (void)_this;
}
void GetNextAutofixAlarmStatusRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextAutofixAlarmStatusRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextAutofixAlarmStatusRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextAutofixAlarmStatusRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextAutofixAlarmStatusRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
  return target;
}

size_t GetNextAutofixAlarmStatusRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextAutofixAlarmStatusRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextAutofixAlarmStatusRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextAutofixAlarmStatusRequest::GetClassData() const { return &_class_data_; }

void GetNextAutofixAlarmStatusRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextAutofixAlarmStatusRequest *>(to)->MergeFrom(
      static_cast<const GetNextAutofixAlarmStatusRequest &>(from));
}


void GetNextAutofixAlarmStatusRequest::MergeFrom(const GetNextAutofixAlarmStatusRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextAutofixAlarmStatusRequest::CopyFrom(const GetNextAutofixAlarmStatusRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextAutofixAlarmStatusRequest::IsInitialized() const {
  return true;
}

void GetNextAutofixAlarmStatusRequest::InternalSwap(GetNextAutofixAlarmStatusRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextAutofixAlarmStatusRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[9]);
}

// ===================================================================

class GetNextAutofixAlarmStatusResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextAutofixAlarmStatusResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextAutofixAlarmStatusResponse::_Internal::ts(const GetNextAutofixAlarmStatusResponse* msg) {
  return *msg->ts_;
}
void GetNextAutofixAlarmStatusResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextAutofixAlarmStatusResponse::GetNextAutofixAlarmStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
}
GetNextAutofixAlarmStatusResponse::GetNextAutofixAlarmStatusResponse(const GetNextAutofixAlarmStatusResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  error_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_error_message().empty()) {
    error_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_error_message(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  completed_ = from.completed_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
}

inline void GetNextAutofixAlarmStatusResponse::SharedCtor() {
error_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&completed_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(completed_));
}

GetNextAutofixAlarmStatusResponse::~GetNextAutofixAlarmStatusResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextAutofixAlarmStatusResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  error_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextAutofixAlarmStatusResponse::ArenaDtor(void* object) {
  GetNextAutofixAlarmStatusResponse* _this = reinterpret_cast< GetNextAutofixAlarmStatusResponse* >(object);
  (void)_this;
}
void GetNextAutofixAlarmStatusResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextAutofixAlarmStatusResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextAutofixAlarmStatusResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  error_message_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  completed_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextAutofixAlarmStatusResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool completed = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          completed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string error_message = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_error_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.error_message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextAutofixAlarmStatusResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // bool completed = 2;
  if (this->_internal_completed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_completed(), target);
  }

  // string error_message = 3;
  if (!this->_internal_error_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_error_message().data(), static_cast<int>(this->_internal_error_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.error_message");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_error_message(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
  return target;
}

size_t GetNextAutofixAlarmStatusResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string error_message = 3;
  if (!this->_internal_error_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_error_message());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // bool completed = 2;
  if (this->_internal_completed() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextAutofixAlarmStatusResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextAutofixAlarmStatusResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextAutofixAlarmStatusResponse::GetClassData() const { return &_class_data_; }

void GetNextAutofixAlarmStatusResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextAutofixAlarmStatusResponse *>(to)->MergeFrom(
      static_cast<const GetNextAutofixAlarmStatusResponse &>(from));
}


void GetNextAutofixAlarmStatusResponse::MergeFrom(const GetNextAutofixAlarmStatusResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_error_message().empty()) {
    _internal_set_error_message(from._internal_error_message());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_completed() != 0) {
    _internal_set_completed(from._internal_completed());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextAutofixAlarmStatusResponse::CopyFrom(const GetNextAutofixAlarmStatusResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextAutofixAlarmStatusResponse::IsInitialized() const {
  return true;
}

void GetNextAutofixAlarmStatusResponse::InternalSwap(GetNextAutofixAlarmStatusResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &error_message_, lhs_arena,
      &other->error_message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextAutofixAlarmStatusResponse, completed_)
      + sizeof(GetNextAutofixAlarmStatusResponse::completed_)
      - PROTOBUF_FIELD_OFFSET(GetNextAutofixAlarmStatusResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextAutofixAlarmStatusResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2falarm_2eproto_getter, &descriptor_table_frontend_2fproto_2falarm_2eproto_once,
      file_level_metadata_frontend_2fproto_2falarm_2eproto[10]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace alarm
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::AlarmRow* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::AlarmRow >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::AlarmRow >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::AlarmTable* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::AlarmTable >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::AlarmTable >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::AlarmCount* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::AlarmCount >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::AlarmCount >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::AcknowledgeRequest* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::AcknowledgeRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::AcknowledgeRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::GetNextAlarmLogRequest* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::GetNextAlarmLogRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::GetNextAlarmLogRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::GetNextAlarmLogResponse* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::GetNextAlarmLogResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::GetNextAlarmLogResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::GetNextAlarmLogCountRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::GetNextAlarmLogCountRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::AttemptAutofixAlarmRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::AttemptAutofixAlarmRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* Arena::CreateMaybeMessage< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

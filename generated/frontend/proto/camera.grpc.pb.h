// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/camera.proto
#ifndef GRPC_frontend_2fproto_2fcamera_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fcamera_2eproto__INCLUDED

#include "frontend/proto/camera.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace camera {

class CameraService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.camera.CameraService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::carbon::frontend::camera::CameraList* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>> AsyncGetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>>(AsyncGetCameraListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>> PrepareAsyncGetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>>(PrepareAsyncGetCameraListRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::carbon::frontend::camera::CameraList* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>> AsyncGetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>>(AsyncGetNextCameraListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>> PrepareAsyncGetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>>(PrepareAsyncGetNextCameraListRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>* AsyncGetCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>* PrepareAsyncGetCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>* AsyncGetNextCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::camera::CameraList>* PrepareAsyncGetNextCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::carbon::frontend::camera::CameraList* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>> AsyncGetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>>(AsyncGetCameraListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>> PrepareAsyncGetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>>(PrepareAsyncGetCameraListRaw(context, request, cq));
    }
    ::grpc::Status GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::carbon::frontend::camera::CameraList* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>> AsyncGetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>>(AsyncGetNextCameraListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>> PrepareAsyncGetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>>(PrepareAsyncGetNextCameraListRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response, std::function<void(::grpc::Status)>) override;
      void GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response, std::function<void(::grpc::Status)>) override;
      void GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>* AsyncGetCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>* PrepareAsyncGetCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>* AsyncGetNextCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>* PrepareAsyncGetNextCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetCameraList_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextCameraList_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetCameraList(::grpc::ServerContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response);
    virtual ::grpc::Status GetNextCameraList(::grpc::ServerContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetCameraList() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCameraList(::grpc::ServerContext* context, ::carbon::frontend::camera::CameraListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::camera::CameraList>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextCameraList() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::NextCameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCameraList(::grpc::ServerContext* context, ::carbon::frontend::camera::NextCameraListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::camera::CameraList>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetCameraList<WithAsyncMethod_GetNextCameraList<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetCameraList() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::camera::CameraListRequest, ::carbon::frontend::camera::CameraList>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response) { return this->GetCameraList(context, request, response); }));}
    void SetMessageAllocatorFor_GetCameraList(
        ::grpc::MessageAllocator< ::carbon::frontend::camera::CameraListRequest, ::carbon::frontend::camera::CameraList>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::camera::CameraListRequest, ::carbon::frontend::camera::CameraList>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCameraList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::camera::CameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextCameraList() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::camera::NextCameraListRequest, ::carbon::frontend::camera::CameraList>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response) { return this->GetNextCameraList(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextCameraList(
        ::grpc::MessageAllocator< ::carbon::frontend::camera::NextCameraListRequest, ::carbon::frontend::camera::CameraList>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::camera::NextCameraListRequest, ::carbon::frontend::camera::CameraList>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::NextCameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCameraList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::camera::NextCameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetCameraList<WithCallbackMethod_GetNextCameraList<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetCameraList() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextCameraList() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::NextCameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetCameraList() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCameraList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextCameraList() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::NextCameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCameraList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetCameraList() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetCameraList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCameraList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextCameraList() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextCameraList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::NextCameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCameraList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetCameraList() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::camera::CameraListRequest, ::carbon::frontend::camera::CameraList>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::camera::CameraListRequest, ::carbon::frontend::camera::CameraList>* streamer) {
                       return this->StreamedGetCameraList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetCameraList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::camera::CameraListRequest,::carbon::frontend::camera::CameraList>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextCameraList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextCameraList() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::camera::NextCameraListRequest, ::carbon::frontend::camera::CameraList>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::camera::NextCameraListRequest, ::carbon::frontend::camera::CameraList>* streamer) {
                       return this->StreamedGetNextCameraList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextCameraList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextCameraList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::NextCameraListRequest* /*request*/, ::carbon::frontend::camera::CameraList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextCameraList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::camera::NextCameraListRequest,::carbon::frontend::camera::CameraList>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetCameraList<WithStreamedUnaryMethod_GetNextCameraList<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetCameraList<WithStreamedUnaryMethod_GetNextCameraList<Service > > StreamedService;
};

}  // namespace camera
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fcamera_2eproto__INCLUDED

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/alarm.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import translation_pb2 as frontend_dot_proto_dot_translation__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/alarm.proto',
  package='carbon.frontend.alarm',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1a\x66rontend/proto/alarm.proto\x12\x15\x63\x61rbon.frontend.alarm\x1a frontend/proto/translation.proto\x1a\x19\x66rontend/proto/util.proto\"\xc7\x03\n\x08\x41larmRow\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\x12\x12\n\nalarm_code\x18\x02 \x01(\t\x12\x11\n\tsubsystem\x18\x03 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x04 \x01(\t\x12\x30\n\x05level\x18\x05 \x01(\x0e\x32!.carbon.frontend.alarm.AlarmLevel\x12\x12\n\nidentifier\x18\x06 \x01(\t\x12\x14\n\x0c\x61\x63knowledged\x18\x07 \x01(\x08\x12\x32\n\x06impact\x18\x08 \x01(\x0e\x32\".carbon.frontend.alarm.AlarmImpact\x12\x19\n\x11stop_timestamp_ms\x18\t \x01(\x03\x12\x19\n\x11\x61utofix_available\x18\n \x01(\x08\x12\x19\n\x11\x61utofix_attempted\x18\x0b \x01(\x08\x12\x1c\n\x14\x61utofix_duration_sec\x18\x0c \x01(\r\x12\x17\n\x0f\x64\x65scription_key\x18\r \x01(\t\x12Q\n\x16translation_parameters\x18\x0e \x03(\x0b\x32\x31.carbon.frontend.translation.TranslationParameter\"j\n\nAlarmTable\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12/\n\x06\x61larms\x18\x02 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\"H\n\nAlarmCount\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\r\n\x05\x63ount\x18\x02 \x01(\r\"(\n\x12\x41\x63knowledgeRequest\x12\x12\n\nidentifier\x18\x01 \x01(\t\"}\n\x16GetNextAlarmLogRequest\x12\x10\n\x08\x66rom_idx\x18\x01 \x01(\x05\x12\x0e\n\x06to_idx\x18\x02 \x01(\x05\x12+\n\x02ts\x18\x03 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x14\n\x0cvisible_only\x18\x04 \x01(\x08\"w\n\x17GetNextAlarmLogResponse\x12/\n\x06\x61larms\x18\x01 \x03(\x0b\x32\x1f.carbon.frontend.alarm.AlarmRow\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"`\n\x1bGetNextAlarmLogCountRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x14\n\x0cvisible_only\x18\x02 \x01(\x08\"_\n\x1cGetNextAlarmLogCountResponse\x12\x12\n\nnum_alarms\x18\x01 \x01(\x05\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"0\n\x1a\x41ttemptAutofixAlarmRequest\x12\x12\n\nidentifier\x18\x01 \x01(\t\"O\n GetNextAutofixAlarmStatusRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"z\n!GetNextAutofixAlarmStatusResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x11\n\tcompleted\x18\x02 \x01(\x08\x12\x15\n\rerror_message\x18\x03 \x01(\t*d\n\nAlarmLevel\x12\x0e\n\nAL_UNKNOWN\x10\x00\x12\x0f\n\x0b\x41L_CRITICAL\x10\x01\x12\x0b\n\x07\x41L_HIGH\x10\x02\x12\r\n\tAL_MEDIUM\x10\x03\x12\n\n\x06\x41L_LOW\x10\x04\x12\r\n\tAL_HIDDEN\x10\x05*\\\n\x0b\x41larmImpact\x12\x0e\n\nAI_UNKNOWN\x10\x00\x12\x0f\n\x0b\x41I_CRITICAL\x10\x01\x12\x0e\n\nAI_OFFLINE\x10\x02\x12\x0f\n\x0b\x41I_DEGRADED\x10\x03\x12\x0b\n\x07\x41I_NONE\x10\x04\x32\xaa\x07\n\x0c\x41larmService\x12V\n\x10GetNextAlarmList\x12\x1f.carbon.frontend.util.Timestamp\x1a!.carbon.frontend.alarm.AlarmTable\x12W\n\x11GetNextAlarmCount\x12\x1f.carbon.frontend.util.Timestamp\x1a!.carbon.frontend.alarm.AlarmCount\x12Y\n\x13GetNextNewAlarmList\x12\x1f.carbon.frontend.util.Timestamp\x1a!.carbon.frontend.alarm.AlarmTable\x12Z\n\x10\x41\x63knowledgeAlarm\x12).carbon.frontend.alarm.AcknowledgeRequest\x1a\x1b.carbon.frontend.util.Empty\x12G\n\x0bResetAlarms\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12p\n\x0fGetNextAlarmLog\x12-.carbon.frontend.alarm.GetNextAlarmLogRequest\x1a..carbon.frontend.alarm.GetNextAlarmLogResponse\x12\x7f\n\x14GetNextAlarmLogCount\x12\x32.carbon.frontend.alarm.GetNextAlarmLogCountRequest\x1a\x33.carbon.frontend.alarm.GetNextAlarmLogCountResponse\x12\x65\n\x13\x41ttemptAutofixAlarm\x12\x31.carbon.frontend.alarm.AttemptAutofixAlarmRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x8e\x01\n\x19GetNextAutofixAlarmStatus\x12\x37.carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest\x1a\x38.carbon.frontend.alarm.GetNextAutofixAlarmStatusResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_translation__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_ALARMLEVEL = _descriptor.EnumDescriptor(
  name='AlarmLevel',
  full_name='carbon.frontend.alarm.AlarmLevel',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='AL_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AL_CRITICAL', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AL_HIGH', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AL_MEDIUM', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AL_LOW', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AL_HIDDEN', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1494,
  serialized_end=1594,
)
_sym_db.RegisterEnumDescriptor(_ALARMLEVEL)

AlarmLevel = enum_type_wrapper.EnumTypeWrapper(_ALARMLEVEL)
_ALARMIMPACT = _descriptor.EnumDescriptor(
  name='AlarmImpact',
  full_name='carbon.frontend.alarm.AlarmImpact',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='AI_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AI_CRITICAL', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AI_OFFLINE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AI_DEGRADED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='AI_NONE', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1596,
  serialized_end=1688,
)
_sym_db.RegisterEnumDescriptor(_ALARMIMPACT)

AlarmImpact = enum_type_wrapper.EnumTypeWrapper(_ALARMIMPACT)
AL_UNKNOWN = 0
AL_CRITICAL = 1
AL_HIGH = 2
AL_MEDIUM = 3
AL_LOW = 4
AL_HIDDEN = 5
AI_UNKNOWN = 0
AI_CRITICAL = 1
AI_OFFLINE = 2
AI_DEGRADED = 3
AI_NONE = 4



_ALARMROW = _descriptor.Descriptor(
  name='AlarmRow',
  full_name='carbon.frontend.alarm.AlarmRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.frontend.alarm.AlarmRow.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alarm_code', full_name='carbon.frontend.alarm.AlarmRow.alarm_code', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='subsystem', full_name='carbon.frontend.alarm.AlarmRow.subsystem', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description', full_name='carbon.frontend.alarm.AlarmRow.description', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='level', full_name='carbon.frontend.alarm.AlarmRow.level', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='identifier', full_name='carbon.frontend.alarm.AlarmRow.identifier', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='acknowledged', full_name='carbon.frontend.alarm.AlarmRow.acknowledged', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='impact', full_name='carbon.frontend.alarm.AlarmRow.impact', index=7,
      number=8, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stop_timestamp_ms', full_name='carbon.frontend.alarm.AlarmRow.stop_timestamp_ms', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='autofix_available', full_name='carbon.frontend.alarm.AlarmRow.autofix_available', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='autofix_attempted', full_name='carbon.frontend.alarm.AlarmRow.autofix_attempted', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='autofix_duration_sec', full_name='carbon.frontend.alarm.AlarmRow.autofix_duration_sec', index=11,
      number=12, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description_key', full_name='carbon.frontend.alarm.AlarmRow.description_key', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='translation_parameters', full_name='carbon.frontend.alarm.AlarmRow.translation_parameters', index=13,
      number=14, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=115,
  serialized_end=570,
)


_ALARMTABLE = _descriptor.Descriptor(
  name='AlarmTable',
  full_name='carbon.frontend.alarm.AlarmTable',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.alarm.AlarmTable.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='alarms', full_name='carbon.frontend.alarm.AlarmTable.alarms', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=572,
  serialized_end=678,
)


_ALARMCOUNT = _descriptor.Descriptor(
  name='AlarmCount',
  full_name='carbon.frontend.alarm.AlarmCount',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.alarm.AlarmCount.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='count', full_name='carbon.frontend.alarm.AlarmCount.count', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=680,
  serialized_end=752,
)


_ACKNOWLEDGEREQUEST = _descriptor.Descriptor(
  name='AcknowledgeRequest',
  full_name='carbon.frontend.alarm.AcknowledgeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='identifier', full_name='carbon.frontend.alarm.AcknowledgeRequest.identifier', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=754,
  serialized_end=794,
)


_GETNEXTALARMLOGREQUEST = _descriptor.Descriptor(
  name='GetNextAlarmLogRequest',
  full_name='carbon.frontend.alarm.GetNextAlarmLogRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='from_idx', full_name='carbon.frontend.alarm.GetNextAlarmLogRequest.from_idx', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='to_idx', full_name='carbon.frontend.alarm.GetNextAlarmLogRequest.to_idx', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.alarm.GetNextAlarmLogRequest.ts', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='visible_only', full_name='carbon.frontend.alarm.GetNextAlarmLogRequest.visible_only', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=796,
  serialized_end=921,
)


_GETNEXTALARMLOGRESPONSE = _descriptor.Descriptor(
  name='GetNextAlarmLogResponse',
  full_name='carbon.frontend.alarm.GetNextAlarmLogResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='alarms', full_name='carbon.frontend.alarm.GetNextAlarmLogResponse.alarms', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.alarm.GetNextAlarmLogResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=923,
  serialized_end=1042,
)


_GETNEXTALARMLOGCOUNTREQUEST = _descriptor.Descriptor(
  name='GetNextAlarmLogCountRequest',
  full_name='carbon.frontend.alarm.GetNextAlarmLogCountRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.alarm.GetNextAlarmLogCountRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='visible_only', full_name='carbon.frontend.alarm.GetNextAlarmLogCountRequest.visible_only', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1044,
  serialized_end=1140,
)


_GETNEXTALARMLOGCOUNTRESPONSE = _descriptor.Descriptor(
  name='GetNextAlarmLogCountResponse',
  full_name='carbon.frontend.alarm.GetNextAlarmLogCountResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='num_alarms', full_name='carbon.frontend.alarm.GetNextAlarmLogCountResponse.num_alarms', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.alarm.GetNextAlarmLogCountResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1142,
  serialized_end=1237,
)


_ATTEMPTAUTOFIXALARMREQUEST = _descriptor.Descriptor(
  name='AttemptAutofixAlarmRequest',
  full_name='carbon.frontend.alarm.AttemptAutofixAlarmRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='identifier', full_name='carbon.frontend.alarm.AttemptAutofixAlarmRequest.identifier', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1239,
  serialized_end=1287,
)


_GETNEXTAUTOFIXALARMSTATUSREQUEST = _descriptor.Descriptor(
  name='GetNextAutofixAlarmStatusRequest',
  full_name='carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1289,
  serialized_end=1368,
)


_GETNEXTAUTOFIXALARMSTATUSRESPONSE = _descriptor.Descriptor(
  name='GetNextAutofixAlarmStatusResponse',
  full_name='carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='completed', full_name='carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.completed', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_message', full_name='carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.error_message', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1370,
  serialized_end=1492,
)

_ALARMROW.fields_by_name['level'].enum_type = _ALARMLEVEL
_ALARMROW.fields_by_name['impact'].enum_type = _ALARMIMPACT
_ALARMROW.fields_by_name['translation_parameters'].message_type = frontend_dot_proto_dot_translation__pb2._TRANSLATIONPARAMETER
_ALARMTABLE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_ALARMTABLE.fields_by_name['alarms'].message_type = _ALARMROW
_ALARMCOUNT.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTALARMLOGREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTALARMLOGRESPONSE.fields_by_name['alarms'].message_type = _ALARMROW
_GETNEXTALARMLOGRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTALARMLOGCOUNTREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTALARMLOGCOUNTRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTAUTOFIXALARMSTATUSREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTAUTOFIXALARMSTATUSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['AlarmRow'] = _ALARMROW
DESCRIPTOR.message_types_by_name['AlarmTable'] = _ALARMTABLE
DESCRIPTOR.message_types_by_name['AlarmCount'] = _ALARMCOUNT
DESCRIPTOR.message_types_by_name['AcknowledgeRequest'] = _ACKNOWLEDGEREQUEST
DESCRIPTOR.message_types_by_name['GetNextAlarmLogRequest'] = _GETNEXTALARMLOGREQUEST
DESCRIPTOR.message_types_by_name['GetNextAlarmLogResponse'] = _GETNEXTALARMLOGRESPONSE
DESCRIPTOR.message_types_by_name['GetNextAlarmLogCountRequest'] = _GETNEXTALARMLOGCOUNTREQUEST
DESCRIPTOR.message_types_by_name['GetNextAlarmLogCountResponse'] = _GETNEXTALARMLOGCOUNTRESPONSE
DESCRIPTOR.message_types_by_name['AttemptAutofixAlarmRequest'] = _ATTEMPTAUTOFIXALARMREQUEST
DESCRIPTOR.message_types_by_name['GetNextAutofixAlarmStatusRequest'] = _GETNEXTAUTOFIXALARMSTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetNextAutofixAlarmStatusResponse'] = _GETNEXTAUTOFIXALARMSTATUSRESPONSE
DESCRIPTOR.enum_types_by_name['AlarmLevel'] = _ALARMLEVEL
DESCRIPTOR.enum_types_by_name['AlarmImpact'] = _ALARMIMPACT
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

AlarmRow = _reflection.GeneratedProtocolMessageType('AlarmRow', (_message.Message,), {
  'DESCRIPTOR' : _ALARMROW,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AlarmRow)
  })
_sym_db.RegisterMessage(AlarmRow)

AlarmTable = _reflection.GeneratedProtocolMessageType('AlarmTable', (_message.Message,), {
  'DESCRIPTOR' : _ALARMTABLE,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AlarmTable)
  })
_sym_db.RegisterMessage(AlarmTable)

AlarmCount = _reflection.GeneratedProtocolMessageType('AlarmCount', (_message.Message,), {
  'DESCRIPTOR' : _ALARMCOUNT,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AlarmCount)
  })
_sym_db.RegisterMessage(AlarmCount)

AcknowledgeRequest = _reflection.GeneratedProtocolMessageType('AcknowledgeRequest', (_message.Message,), {
  'DESCRIPTOR' : _ACKNOWLEDGEREQUEST,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AcknowledgeRequest)
  })
_sym_db.RegisterMessage(AcknowledgeRequest)

GetNextAlarmLogRequest = _reflection.GeneratedProtocolMessageType('GetNextAlarmLogRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTALARMLOGREQUEST,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAlarmLogRequest)
  })
_sym_db.RegisterMessage(GetNextAlarmLogRequest)

GetNextAlarmLogResponse = _reflection.GeneratedProtocolMessageType('GetNextAlarmLogResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTALARMLOGRESPONSE,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAlarmLogResponse)
  })
_sym_db.RegisterMessage(GetNextAlarmLogResponse)

GetNextAlarmLogCountRequest = _reflection.GeneratedProtocolMessageType('GetNextAlarmLogCountRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTALARMLOGCOUNTREQUEST,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
  })
_sym_db.RegisterMessage(GetNextAlarmLogCountRequest)

GetNextAlarmLogCountResponse = _reflection.GeneratedProtocolMessageType('GetNextAlarmLogCountResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTALARMLOGCOUNTRESPONSE,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
  })
_sym_db.RegisterMessage(GetNextAlarmLogCountResponse)

AttemptAutofixAlarmRequest = _reflection.GeneratedProtocolMessageType('AttemptAutofixAlarmRequest', (_message.Message,), {
  'DESCRIPTOR' : _ATTEMPTAUTOFIXALARMREQUEST,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
  })
_sym_db.RegisterMessage(AttemptAutofixAlarmRequest)

GetNextAutofixAlarmStatusRequest = _reflection.GeneratedProtocolMessageType('GetNextAutofixAlarmStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTAUTOFIXALARMSTATUSREQUEST,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
  })
_sym_db.RegisterMessage(GetNextAutofixAlarmStatusRequest)

GetNextAutofixAlarmStatusResponse = _reflection.GeneratedProtocolMessageType('GetNextAutofixAlarmStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTAUTOFIXALARMSTATUSRESPONSE,
  '__module__' : 'frontend.proto.alarm_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
  })
_sym_db.RegisterMessage(GetNextAutofixAlarmStatusResponse)


DESCRIPTOR._options = None

_ALARMSERVICE = _descriptor.ServiceDescriptor(
  name='AlarmService',
  full_name='carbon.frontend.alarm.AlarmService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1691,
  serialized_end=2629,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextAlarmList',
    full_name='carbon.frontend.alarm.AlarmService.GetNextAlarmList',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_ALARMTABLE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextAlarmCount',
    full_name='carbon.frontend.alarm.AlarmService.GetNextAlarmCount',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_ALARMCOUNT,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextNewAlarmList',
    full_name='carbon.frontend.alarm.AlarmService.GetNextNewAlarmList',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_ALARMTABLE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='AcknowledgeAlarm',
    full_name='carbon.frontend.alarm.AlarmService.AcknowledgeAlarm',
    index=3,
    containing_service=None,
    input_type=_ACKNOWLEDGEREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResetAlarms',
    full_name='carbon.frontend.alarm.AlarmService.ResetAlarms',
    index=4,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextAlarmLog',
    full_name='carbon.frontend.alarm.AlarmService.GetNextAlarmLog',
    index=5,
    containing_service=None,
    input_type=_GETNEXTALARMLOGREQUEST,
    output_type=_GETNEXTALARMLOGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextAlarmLogCount',
    full_name='carbon.frontend.alarm.AlarmService.GetNextAlarmLogCount',
    index=6,
    containing_service=None,
    input_type=_GETNEXTALARMLOGCOUNTREQUEST,
    output_type=_GETNEXTALARMLOGCOUNTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='AttemptAutofixAlarm',
    full_name='carbon.frontend.alarm.AlarmService.AttemptAutofixAlarm',
    index=7,
    containing_service=None,
    input_type=_ATTEMPTAUTOFIXALARMREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextAutofixAlarmStatus',
    full_name='carbon.frontend.alarm.AlarmService.GetNextAutofixAlarmStatus',
    index=8,
    containing_service=None,
    input_type=_GETNEXTAUTOFIXALARMSTATUSREQUEST,
    output_type=_GETNEXTAUTOFIXALARMSTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ALARMSERVICE)

DESCRIPTOR.services_by_name['AlarmService'] = _ALARMSERVICE

# @@protoc_insertion_point(module_scope)

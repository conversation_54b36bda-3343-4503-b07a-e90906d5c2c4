// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/camera.proto

#include "frontend/proto/camera.pb.h"
#include "frontend/proto/camera.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace camera {

static const char* CameraService_method_names[] = {
  "/carbon.frontend.camera.CameraService/GetCameraList",
  "/carbon.frontend.camera.CameraService/GetNextCameraList",
};

std::unique_ptr< CameraService::Stub> CameraService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< CameraService::Stub> stub(new CameraService::Stub(channel, options));
  return stub;
}

CameraService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetCameraList_(CameraService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextCameraList_(CameraService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status CameraService::Stub::GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::carbon::frontend::camera::CameraList* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::camera::CameraListRequest, ::carbon::frontend::camera::CameraList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCameraList_, context, request, response);
}

void CameraService::Stub::async::GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::camera::CameraListRequest, ::carbon::frontend::camera::CameraList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraList_, context, request, response, std::move(f));
}

void CameraService::Stub::async::GetCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCameraList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>* CameraService::Stub::PrepareAsyncGetCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::camera::CameraList, ::carbon::frontend::camera::CameraListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCameraList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>* CameraService::Stub::AsyncGetCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCameraListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CameraService::Stub::GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::carbon::frontend::camera::CameraList* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::camera::NextCameraListRequest, ::carbon::frontend::camera::CameraList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextCameraList_, context, request, response);
}

void CameraService::Stub::async::GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::camera::NextCameraListRequest, ::carbon::frontend::camera::CameraList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCameraList_, context, request, response, std::move(f));
}

void CameraService::Stub::async::GetNextCameraList(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCameraList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>* CameraService::Stub::PrepareAsyncGetNextCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::camera::CameraList, ::carbon::frontend::camera::NextCameraListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextCameraList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::camera::CameraList>* CameraService::Stub::AsyncGetNextCameraListRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::NextCameraListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextCameraListRaw(context, request, cq);
  result->StartCall();
  return result;
}

CameraService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CameraService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CameraService::Service, ::carbon::frontend::camera::CameraListRequest, ::carbon::frontend::camera::CameraList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CameraService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::camera::CameraListRequest* req,
             ::carbon::frontend::camera::CameraList* resp) {
               return service->GetCameraList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CameraService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CameraService::Service, ::carbon::frontend::camera::NextCameraListRequest, ::carbon::frontend::camera::CameraList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CameraService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::camera::NextCameraListRequest* req,
             ::carbon::frontend::camera::CameraList* resp) {
               return service->GetNextCameraList(ctx, req, resp);
             }, this)));
}

CameraService::Service::~Service() {
}

::grpc::Status CameraService::Service::GetCameraList(::grpc::ServerContext* context, const ::carbon::frontend::camera::CameraListRequest* request, ::carbon::frontend::camera::CameraList* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CameraService::Service::GetNextCameraList(::grpc::ServerContext* context, const ::carbon::frontend::camera::NextCameraListRequest* request, ::carbon::frontend::camera::CameraList* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace camera


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/jobs.proto

#include "frontend/proto/jobs.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace jobs {
constexpr JobDescription::JobDescription(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestampms_(int64_t{0}){}
struct JobDescriptionDefaultTypeInternal {
  constexpr JobDescriptionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~JobDescriptionDefaultTypeInternal() {}
  union {
    JobDescription _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT JobDescriptionDefaultTypeInternal _JobDescription_default_instance_;
constexpr ActiveProfile::ActiveProfile(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , profile_type_(0)
{}
struct ActiveProfileDefaultTypeInternal {
  constexpr ActiveProfileDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ActiveProfileDefaultTypeInternal() {}
  union {
    ActiveProfile _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ActiveProfileDefaultTypeInternal _ActiveProfile_default_instance_;
constexpr Job_ActiveProfilesEntry_DoNotUse::Job_ActiveProfilesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct Job_ActiveProfilesEntry_DoNotUseDefaultTypeInternal {
  constexpr Job_ActiveProfilesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~Job_ActiveProfilesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    Job_ActiveProfilesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT Job_ActiveProfilesEntry_DoNotUseDefaultTypeInternal _Job_ActiveProfilesEntry_DoNotUse_default_instance_;
constexpr Job::Job(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : active_profiles_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , bandingprofile_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , thinningprofile_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , almanac_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , discriminator_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bandingprofileuuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , thinningprofileuuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , almanacprofileuuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , discriminatorprofileuuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , jobdescription_(nullptr)
  , stoptimems_(int64_t{0})
  , lastupdatetimems_(int64_t{0})
  , expectedacreage_(0)
  , completed_(false)
  , lastusedtimems_(int64_t{0}){}
struct JobDefaultTypeInternal {
  constexpr JobDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~JobDefaultTypeInternal() {}
  union {
    Job _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT JobDefaultTypeInternal _Job_default_instance_;
constexpr CreateJobRequest::CreateJobRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , active_(false)
  , expectedacreage_(0){}
struct CreateJobRequestDefaultTypeInternal {
  constexpr CreateJobRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CreateJobRequestDefaultTypeInternal() {}
  union {
    CreateJobRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CreateJobRequestDefaultTypeInternal _CreateJobRequest_default_instance_;
constexpr CreateJobResponse::CreateJobResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct CreateJobResponseDefaultTypeInternal {
  constexpr CreateJobResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CreateJobResponseDefaultTypeInternal() {}
  union {
    CreateJobResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CreateJobResponseDefaultTypeInternal _CreateJobResponse_default_instance_;
constexpr UpdateJobRequest::UpdateJobRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobdescription_(nullptr)
  , expectedacreage_(0){}
struct UpdateJobRequestDefaultTypeInternal {
  constexpr UpdateJobRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpdateJobRequestDefaultTypeInternal() {}
  union {
    UpdateJobRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpdateJobRequestDefaultTypeInternal _UpdateJobRequest_default_instance_;
constexpr GetNextJobsRequest::GetNextJobsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_(nullptr){}
struct GetNextJobsRequestDefaultTypeInternal {
  constexpr GetNextJobsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextJobsRequestDefaultTypeInternal() {}
  union {
    GetNextJobsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextJobsRequestDefaultTypeInternal _GetNextJobsRequest_default_instance_;
constexpr JobWithMetrics::JobWithMetrics(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : job_(nullptr)
  , metrics_(nullptr){}
struct JobWithMetricsDefaultTypeInternal {
  constexpr JobWithMetricsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~JobWithMetricsDefaultTypeInternal() {}
  union {
    JobWithMetrics _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT JobWithMetricsDefaultTypeInternal _JobWithMetrics_default_instance_;
constexpr GetNextJobsResponse::GetNextJobsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobs_()
  , activejobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_(nullptr){}
struct GetNextJobsResponseDefaultTypeInternal {
  constexpr GetNextJobsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextJobsResponseDefaultTypeInternal() {}
  union {
    GetNextJobsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextJobsResponseDefaultTypeInternal _GetNextJobsResponse_default_instance_;
constexpr GetNextActiveJobIdRequest::GetNextActiveJobIdRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_(nullptr){}
struct GetNextActiveJobIdRequestDefaultTypeInternal {
  constexpr GetNextActiveJobIdRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveJobIdRequestDefaultTypeInternal() {}
  union {
    GetNextActiveJobIdRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveJobIdRequestDefaultTypeInternal _GetNextActiveJobIdRequest_default_instance_;
constexpr GetNextActiveJobIdResponse::GetNextActiveJobIdResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : activejobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_(nullptr){}
struct GetNextActiveJobIdResponseDefaultTypeInternal {
  constexpr GetNextActiveJobIdResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveJobIdResponseDefaultTypeInternal() {}
  union {
    GetNextActiveJobIdResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveJobIdResponseDefaultTypeInternal _GetNextActiveJobIdResponse_default_instance_;
constexpr GetJobRequest::GetJobRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetJobRequestDefaultTypeInternal {
  constexpr GetJobRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetJobRequestDefaultTypeInternal() {}
  union {
    GetJobRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetJobRequestDefaultTypeInternal _GetJobRequest_default_instance_;
constexpr GetJobResponse::GetJobResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : job_(nullptr){}
struct GetJobResponseDefaultTypeInternal {
  constexpr GetJobResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetJobResponseDefaultTypeInternal() {}
  union {
    GetJobResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetJobResponseDefaultTypeInternal _GetJobResponse_default_instance_;
constexpr StartJobRequest::StartJobRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct StartJobRequestDefaultTypeInternal {
  constexpr StartJobRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StartJobRequestDefaultTypeInternal() {}
  union {
    StartJobRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StartJobRequestDefaultTypeInternal _StartJobRequest_default_instance_;
constexpr GetConfigDumpRequest::GetConfigDumpRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetConfigDumpRequestDefaultTypeInternal {
  constexpr GetConfigDumpRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetConfigDumpRequestDefaultTypeInternal() {}
  union {
    GetConfigDumpRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetConfigDumpRequestDefaultTypeInternal _GetConfigDumpRequest_default_instance_;
constexpr GetConfigDumpResponse::GetConfigDumpResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : rootconfig_(nullptr){}
struct GetConfigDumpResponseDefaultTypeInternal {
  constexpr GetConfigDumpResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetConfigDumpResponseDefaultTypeInternal() {}
  union {
    GetConfigDumpResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetConfigDumpResponseDefaultTypeInternal _GetConfigDumpResponse_default_instance_;
constexpr GetActiveJobMetricsResponse::GetActiveJobMetricsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobmetrics_(nullptr){}
struct GetActiveJobMetricsResponseDefaultTypeInternal {
  constexpr GetActiveJobMetricsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetActiveJobMetricsResponseDefaultTypeInternal() {}
  union {
    GetActiveJobMetricsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetActiveJobMetricsResponseDefaultTypeInternal _GetActiveJobMetricsResponse_default_instance_;
constexpr DeleteJobRequest::DeleteJobRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DeleteJobRequestDefaultTypeInternal {
  constexpr DeleteJobRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteJobRequestDefaultTypeInternal() {}
  union {
    DeleteJobRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteJobRequestDefaultTypeInternal _DeleteJobRequest_default_instance_;
constexpr MarkJobCompletedRequest::MarkJobCompletedRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct MarkJobCompletedRequestDefaultTypeInternal {
  constexpr MarkJobCompletedRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MarkJobCompletedRequestDefaultTypeInternal() {}
  union {
    MarkJobCompletedRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MarkJobCompletedRequestDefaultTypeInternal _MarkJobCompletedRequest_default_instance_;
constexpr MarkJobIncompleteRequest::MarkJobIncompleteRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct MarkJobIncompleteRequestDefaultTypeInternal {
  constexpr MarkJobIncompleteRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MarkJobIncompleteRequestDefaultTypeInternal() {}
  union {
    MarkJobIncompleteRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MarkJobIncompleteRequestDefaultTypeInternal _MarkJobIncompleteRequest_default_instance_;
constexpr GetNextJobRequest::GetNextJobRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : jobid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextJobRequestDefaultTypeInternal {
  constexpr GetNextJobRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextJobRequestDefaultTypeInternal() {}
  union {
    GetNextJobRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextJobRequestDefaultTypeInternal _GetNextJobRequest_default_instance_;
constexpr GetNextJobResponse::GetNextJobResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , job_(nullptr){}
struct GetNextJobResponseDefaultTypeInternal {
  constexpr GetNextJobResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextJobResponseDefaultTypeInternal() {}
  union {
    GetNextJobResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextJobResponseDefaultTypeInternal _GetNextJobResponse_default_instance_;
}  // namespace jobs
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fjobs_2eproto[23];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fjobs_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fjobs_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fjobs_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::JobDescription, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::JobDescription, jobid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::JobDescription, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::JobDescription, timestampms_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::ActiveProfile, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::ActiveProfile, profile_type_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::ActiveProfile, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::ActiveProfile, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, jobdescription_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, bandingprofile_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, thinningprofile_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, stoptimems_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, lastupdatetimems_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, expectedacreage_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, completed_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, almanac_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, discriminator_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, bandingprofileuuid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, thinningprofileuuid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, almanacprofileuuid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, discriminatorprofileuuid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, active_profiles_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::Job, lastusedtimems_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::CreateJobRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::CreateJobRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::CreateJobRequest, active_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::CreateJobRequest, expectedacreage_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::CreateJobResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::CreateJobResponse, jobid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::UpdateJobRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::UpdateJobRequest, jobdescription_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::UpdateJobRequest, expectedacreage_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobsRequest, timestamp_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::JobWithMetrics, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::JobWithMetrics, job_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::JobWithMetrics, metrics_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobsResponse, jobs_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobsResponse, activejobid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobsResponse, timestamp_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextActiveJobIdRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextActiveJobIdRequest, timestamp_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextActiveJobIdResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextActiveJobIdResponse, activejobid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextActiveJobIdResponse, timestamp_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetJobRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetJobRequest, jobid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetJobResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetJobResponse, job_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::StartJobRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::StartJobRequest, jobid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetConfigDumpRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetConfigDumpRequest, jobid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetConfigDumpResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetConfigDumpResponse, rootconfig_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetActiveJobMetricsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetActiveJobMetricsResponse, jobmetrics_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::DeleteJobRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::DeleteJobRequest, jobid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::MarkJobCompletedRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::MarkJobCompletedRequest, jobid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::MarkJobIncompleteRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::MarkJobIncompleteRequest, jobid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobRequest, jobid_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::jobs::GetNextJobResponse, job_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::jobs::JobDescription)},
  { 9, -1, -1, sizeof(::carbon::frontend::jobs::ActiveProfile)},
  { 18, 26, -1, sizeof(::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse)},
  { 28, -1, -1, sizeof(::carbon::frontend::jobs::Job)},
  { 50, -1, -1, sizeof(::carbon::frontend::jobs::CreateJobRequest)},
  { 59, -1, -1, sizeof(::carbon::frontend::jobs::CreateJobResponse)},
  { 66, -1, -1, sizeof(::carbon::frontend::jobs::UpdateJobRequest)},
  { 74, -1, -1, sizeof(::carbon::frontend::jobs::GetNextJobsRequest)},
  { 81, -1, -1, sizeof(::carbon::frontend::jobs::JobWithMetrics)},
  { 89, -1, -1, sizeof(::carbon::frontend::jobs::GetNextJobsResponse)},
  { 98, -1, -1, sizeof(::carbon::frontend::jobs::GetNextActiveJobIdRequest)},
  { 105, -1, -1, sizeof(::carbon::frontend::jobs::GetNextActiveJobIdResponse)},
  { 113, -1, -1, sizeof(::carbon::frontend::jobs::GetJobRequest)},
  { 120, -1, -1, sizeof(::carbon::frontend::jobs::GetJobResponse)},
  { 127, -1, -1, sizeof(::carbon::frontend::jobs::StartJobRequest)},
  { 134, -1, -1, sizeof(::carbon::frontend::jobs::GetConfigDumpRequest)},
  { 141, -1, -1, sizeof(::carbon::frontend::jobs::GetConfigDumpResponse)},
  { 148, -1, -1, sizeof(::carbon::frontend::jobs::GetActiveJobMetricsResponse)},
  { 155, -1, -1, sizeof(::carbon::frontend::jobs::DeleteJobRequest)},
  { 162, -1, -1, sizeof(::carbon::frontend::jobs::MarkJobCompletedRequest)},
  { 169, -1, -1, sizeof(::carbon::frontend::jobs::MarkJobIncompleteRequest)},
  { 176, -1, -1, sizeof(::carbon::frontend::jobs::GetNextJobRequest)},
  { 184, -1, -1, sizeof(::carbon::frontend::jobs::GetNextJobResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_JobDescription_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_ActiveProfile_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_Job_ActiveProfilesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_Job_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_CreateJobRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_CreateJobResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_UpdateJobRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetNextJobsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_JobWithMetrics_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetNextJobsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetNextActiveJobIdRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetNextActiveJobIdResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetJobRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetJobResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_StartJobRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetConfigDumpRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetConfigDumpResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetActiveJobMetricsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_DeleteJobRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_MarkJobCompletedRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_MarkJobIncompleteRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetNextJobRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::jobs::_GetNextJobResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fjobs_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031frontend/proto/jobs.proto\022\024carbon.fron"
  "tend.jobs\032\031frontend/proto/util.proto\032(fr"
  "ontend/proto/weeding_diagnostics.proto\032."
  "metrics/proto/metrics_aggregator_service"
  ".proto\032!frontend/proto/profile_sync.prot"
  "o\"B\n\016JobDescription\022\r\n\005jobId\030\001 \001(\t\022\014\n\004na"
  "me\030\002 \001(\t\022\023\n\013timestampMs\030\003 \001(\003\"j\n\rActiveP"
  "rofile\022\?\n\014profile_type\030\001 \001(\0162).carbon.fr"
  "ontend.profile_sync.ProfileType\022\n\n\002id\030\002 "
  "\001(\t\022\014\n\004name\030\003 \001(\t\"\272\004\n\003Job\022<\n\016jobDescript"
  "ion\030\001 \001(\0132$.carbon.frontend.jobs.JobDesc"
  "ription\022\026\n\016bandingProfile\030\002 \001(\t\022\027\n\017thinn"
  "ingProfile\030\003 \001(\t\022\022\n\nstopTimeMs\030\004 \001(\003\022\030\n\020"
  "lastUpdateTimeMs\030\005 \001(\003\022\027\n\017expectedAcreag"
  "e\030\006 \001(\002\022\021\n\tcompleted\030\007 \001(\010\022\017\n\007almanac\030\010 "
  "\001(\t\022\025\n\rdiscriminator\030\t \001(\t\022\017\n\007crop_id\030\n "
  "\001(\t\022\032\n\022bandingProfileUUID\030\013 \001(\t\022\033\n\023thinn"
  "ingProfileUUID\030\014 \001(\t\022\032\n\022almanacProfileUU"
  "ID\030\r \001(\t\022 \n\030discriminatorProfileUUID\030\016 \001"
  "(\t\022F\n\017active_profiles\030\017 \003(\0132-.carbon.fro"
  "ntend.jobs.Job.ActiveProfilesEntry\022\026\n\016la"
  "stUsedTimeMs\030\020 \001(\003\032Z\n\023ActiveProfilesEntr"
  "y\022\013\n\003key\030\001 \001(\005\0222\n\005value\030\002 \001(\0132#.carbon.f"
  "rontend.jobs.ActiveProfile:\0028\001\"I\n\020Create"
  "JobRequest\022\014\n\004name\030\001 \001(\t\022\016\n\006active\030\002 \001(\010"
  "\022\027\n\017expectedAcreage\030\003 \001(\002\"\"\n\021CreateJobRe"
  "sponse\022\r\n\005jobId\030\001 \001(\t\"i\n\020UpdateJobReques"
  "t\022<\n\016jobDescription\030\001 \001(\0132$.carbon.front"
  "end.jobs.JobDescription\022\027\n\017expectedAcrea"
  "ge\030\002 \001(\002\"H\n\022GetNextJobsRequest\0222\n\ttimest"
  "amp\030\001 \001(\0132\037.carbon.frontend.util.Timesta"
  "mp\"f\n\016JobWithMetrics\022&\n\003job\030\001 \001(\0132\031.carb"
  "on.frontend.jobs.Job\022,\n\007metrics\030\002 \001(\0132\033."
  "metrics_aggregator.Metrics\"\222\001\n\023GetNextJo"
  "bsResponse\0222\n\004jobs\030\001 \003(\0132$.carbon.fronte"
  "nd.jobs.JobWithMetrics\022\023\n\013activeJobId\030\002 "
  "\001(\t\0222\n\ttimestamp\030\003 \001(\0132\037.carbon.frontend"
  ".util.Timestamp\"O\n\031GetNextActiveJobIdReq"
  "uest\0222\n\ttimestamp\030\001 \001(\0132\037.carbon.fronten"
  "d.util.Timestamp\"e\n\032GetNextActiveJobIdRe"
  "sponse\022\023\n\013activeJobId\030\001 \001(\t\0222\n\ttimestamp"
  "\030\002 \001(\0132\037.carbon.frontend.util.Timestamp\""
  "\036\n\rGetJobRequest\022\r\n\005jobId\030\001 \001(\t\"8\n\016GetJo"
  "bResponse\022&\n\003job\030\001 \001(\0132\031.carbon.frontend"
  ".jobs.Job\" \n\017StartJobRequest\022\r\n\005jobId\030\001 "
  "\001(\t\"%\n\024GetConfigDumpRequest\022\r\n\005jobId\030\001 \001"
  "(\t\"d\n\025GetConfigDumpResponse\022K\n\nrootConfi"
  "g\030\001 \001(\01327.carbon.frontend.weeding_diagno"
  "stics.ConfigNodeSnapshot\"N\n\033GetActiveJob"
  "MetricsResponse\022/\n\njobMetrics\030\001 \001(\0132\033.me"
  "trics_aggregator.Metrics\"!\n\020DeleteJobReq"
  "uest\022\r\n\005jobId\030\001 \001(\t\"(\n\027MarkJobCompletedR"
  "equest\022\r\n\005jobId\030\001 \001(\t\")\n\030MarkJobIncomple"
  "teRequest\022\r\n\005jobId\030\001 \001(\t\"O\n\021GetNextJobRe"
  "quest\022+\n\002ts\030\001 \001(\0132\037.carbon.frontend.util"
  ".Timestamp\022\r\n\005jobId\030\002 \001(\t\"t\n\022GetNextJobR"
  "esponse\022+\n\002ts\030\001 \001(\0132\037.carbon.frontend.ut"
  "il.Timestamp\0221\n\003job\030\002 \001(\0132$.carbon.front"
  "end.jobs.JobWithMetrics2\320\t\n\013JobsService\022"
  "b\n\013GetNextJobs\022(.carbon.frontend.jobs.Ge"
  "tNextJobsRequest\032).carbon.frontend.jobs."
  "GetNextJobsResponse\022\\\n\tCreateJob\022&.carbo"
  "n.frontend.jobs.CreateJobRequest\032\'.carbo"
  "n.frontend.jobs.CreateJobResponse\022P\n\tUpd"
  "ateJob\022&.carbon.frontend.jobs.UpdateJobR"
  "equest\032\033.carbon.frontend.util.Empty\022N\n\010S"
  "tartJob\022%.carbon.frontend.jobs.StartJobR"
  "equest\032\033.carbon.frontend.util.Empty\022I\n\rS"
  "topActiveJob\022\033.carbon.frontend.util.Empt"
  "y\032\033.carbon.frontend.util.Empty\022w\n\022GetNex"
  "tActiveJobId\022/.carbon.frontend.jobs.GetN"
  "extActiveJobIdRequest\0320.carbon.frontend."
  "jobs.GetNextActiveJobIdResponse\022S\n\006GetJo"
  "b\022#.carbon.frontend.jobs.GetJobRequest\032$"
  ".carbon.frontend.jobs.GetJobResponse\022h\n\r"
  "GetConfigDump\022*.carbon.frontend.jobs.Get"
  "ConfigDumpRequest\032+.carbon.frontend.jobs"
  ".GetConfigDumpResponse\022e\n\023GetActiveJobMe"
  "trics\022\033.carbon.frontend.util.Empty\0321.car"
  "bon.frontend.jobs.GetActiveJobMetricsRes"
  "ponse\022P\n\tDeleteJob\022&.carbon.frontend.job"
  "s.DeleteJobRequest\032\033.carbon.frontend.uti"
  "l.Empty\022^\n\020MarkJobCompleted\022-.carbon.fro"
  "ntend.jobs.MarkJobCompletedRequest\032\033.car"
  "bon.frontend.util.Empty\022`\n\021MarkJobIncomp"
  "lete\022..carbon.frontend.jobs.MarkJobIncom"
  "pleteRequest\032\033.carbon.frontend.util.Empt"
  "y\022_\n\nGetNextJob\022\'.carbon.frontend.jobs.G"
  "etNextJobRequest\032(.carbon.frontend.jobs."
  "GetNextJobResponseB\020Z\016proto/frontendb\006pr"
  "oto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fjobs_2eproto_deps[4] = {
  &::descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
  &::descriptor_table_frontend_2fproto_2fweeding_5fdiagnostics_2eproto,
  &::descriptor_table_metrics_2fproto_2fmetrics_5faggregator_5fservice_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fjobs_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fjobs_2eproto = {
  false, false, 3604, descriptor_table_protodef_frontend_2fproto_2fjobs_2eproto, "frontend/proto/jobs.proto", 
  &descriptor_table_frontend_2fproto_2fjobs_2eproto_once, descriptor_table_frontend_2fproto_2fjobs_2eproto_deps, 4, 23,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fjobs_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fjobs_2eproto, file_level_enum_descriptors_frontend_2fproto_2fjobs_2eproto, file_level_service_descriptors_frontend_2fproto_2fjobs_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fjobs_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fjobs_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fjobs_2eproto(&descriptor_table_frontend_2fproto_2fjobs_2eproto);
namespace carbon {
namespace frontend {
namespace jobs {

// ===================================================================

class JobDescription::_Internal {
 public:
};

JobDescription::JobDescription(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.JobDescription)
}
JobDescription::JobDescription(const JobDescription& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  timestampms_ = from.timestampms_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.JobDescription)
}

inline void JobDescription::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
timestampms_ = int64_t{0};
}

JobDescription::~JobDescription() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.JobDescription)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void JobDescription::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void JobDescription::ArenaDtor(void* object) {
  JobDescription* _this = reinterpret_cast< JobDescription* >(object);
  (void)_this;
}
void JobDescription::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void JobDescription::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void JobDescription::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.JobDescription)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  name_.ClearToEmpty();
  timestampms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* JobDescription::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.JobDescription.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.JobDescription.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 timestampMs = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          timestampms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* JobDescription::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.JobDescription)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.JobDescription.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.JobDescription.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // int64 timestampMs = 3;
  if (this->_internal_timestampms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_timestampms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.JobDescription)
  return target;
}

size_t JobDescription::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.JobDescription)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // int64 timestampMs = 3;
  if (this->_internal_timestampms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestampms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData JobDescription::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    JobDescription::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*JobDescription::GetClassData() const { return &_class_data_; }

void JobDescription::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<JobDescription *>(to)->MergeFrom(
      static_cast<const JobDescription &>(from));
}


void JobDescription::MergeFrom(const JobDescription& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.JobDescription)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_timestampms() != 0) {
    _internal_set_timestampms(from._internal_timestampms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void JobDescription::CopyFrom(const JobDescription& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.JobDescription)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JobDescription::IsInitialized() const {
  return true;
}

void JobDescription::InternalSwap(JobDescription* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(timestampms_, other->timestampms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata JobDescription::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[0]);
}

// ===================================================================

class ActiveProfile::_Internal {
 public:
};

ActiveProfile::ActiveProfile(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.ActiveProfile)
}
ActiveProfile::ActiveProfile(const ActiveProfile& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  profile_type_ = from.profile_type_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.ActiveProfile)
}

inline void ActiveProfile::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
profile_type_ = 0;
}

ActiveProfile::~ActiveProfile() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.ActiveProfile)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ActiveProfile::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ActiveProfile::ArenaDtor(void* object) {
  ActiveProfile* _this = reinterpret_cast< ActiveProfile* >(object);
  (void)_this;
}
void ActiveProfile::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ActiveProfile::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ActiveProfile::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.ActiveProfile)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  name_.ClearToEmpty();
  profile_type_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ActiveProfile::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_profile_type(static_cast<::carbon::frontend::profile_sync::ProfileType>(val));
        } else
          goto handle_unusual;
        continue;
      // string id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.ActiveProfile.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.ActiveProfile.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ActiveProfile::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.ActiveProfile)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  if (this->_internal_profile_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_profile_type(), target);
  }

  // string id = 2;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.ActiveProfile.id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_id(), target);
  }

  // string name = 3;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.ActiveProfile.name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.ActiveProfile)
  return target;
}

size_t ActiveProfile::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.ActiveProfile)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 2;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string name = 3;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  if (this->_internal_profile_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_profile_type());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ActiveProfile::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ActiveProfile::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ActiveProfile::GetClassData() const { return &_class_data_; }

void ActiveProfile::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ActiveProfile *>(to)->MergeFrom(
      static_cast<const ActiveProfile &>(from));
}


void ActiveProfile::MergeFrom(const ActiveProfile& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.ActiveProfile)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_profile_type() != 0) {
    _internal_set_profile_type(from._internal_profile_type());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ActiveProfile::CopyFrom(const ActiveProfile& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.ActiveProfile)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ActiveProfile::IsInitialized() const {
  return true;
}

void ActiveProfile::InternalSwap(ActiveProfile* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(profile_type_, other->profile_type_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ActiveProfile::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[1]);
}

// ===================================================================

Job_ActiveProfilesEntry_DoNotUse::Job_ActiveProfilesEntry_DoNotUse() {}
Job_ActiveProfilesEntry_DoNotUse::Job_ActiveProfilesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void Job_ActiveProfilesEntry_DoNotUse::MergeFrom(const Job_ActiveProfilesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata Job_ActiveProfilesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[2]);
}

// ===================================================================

class Job::_Internal {
 public:
  static const ::carbon::frontend::jobs::JobDescription& jobdescription(const Job* msg);
};

const ::carbon::frontend::jobs::JobDescription&
Job::_Internal::jobdescription(const Job* msg) {
  return *msg->jobdescription_;
}
Job::Job(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  active_profiles_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.Job)
}
Job::Job(const Job& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  active_profiles_.MergeFrom(from.active_profiles_);
  bandingprofile_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    bandingprofile_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_bandingprofile().empty()) {
    bandingprofile_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bandingprofile(), 
      GetArenaForAllocation());
  }
  thinningprofile_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    thinningprofile_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_thinningprofile().empty()) {
    thinningprofile_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_thinningprofile(), 
      GetArenaForAllocation());
  }
  almanac_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    almanac_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_almanac().empty()) {
    almanac_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_almanac(), 
      GetArenaForAllocation());
  }
  discriminator_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    discriminator_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_discriminator().empty()) {
    discriminator_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_discriminator(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  bandingprofileuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    bandingprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_bandingprofileuuid().empty()) {
    bandingprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bandingprofileuuid(), 
      GetArenaForAllocation());
  }
  thinningprofileuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    thinningprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_thinningprofileuuid().empty()) {
    thinningprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_thinningprofileuuid(), 
      GetArenaForAllocation());
  }
  almanacprofileuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    almanacprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_almanacprofileuuid().empty()) {
    almanacprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_almanacprofileuuid(), 
      GetArenaForAllocation());
  }
  discriminatorprofileuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    discriminatorprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_discriminatorprofileuuid().empty()) {
    discriminatorprofileuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_discriminatorprofileuuid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_jobdescription()) {
    jobdescription_ = new ::carbon::frontend::jobs::JobDescription(*from.jobdescription_);
  } else {
    jobdescription_ = nullptr;
  }
  ::memcpy(&stoptimems_, &from.stoptimems_,
    static_cast<size_t>(reinterpret_cast<char*>(&lastusedtimems_) -
    reinterpret_cast<char*>(&stoptimems_)) + sizeof(lastusedtimems_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.Job)
}

inline void Job::SharedCtor() {
bandingprofile_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  bandingprofile_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
thinningprofile_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  thinningprofile_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
almanac_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  almanac_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
discriminator_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  discriminator_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
bandingprofileuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  bandingprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
thinningprofileuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  thinningprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
almanacprofileuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  almanacprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
discriminatorprofileuuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  discriminatorprofileuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&jobdescription_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&lastusedtimems_) -
    reinterpret_cast<char*>(&jobdescription_)) + sizeof(lastusedtimems_));
}

Job::~Job() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.Job)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Job::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  bandingprofile_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  thinningprofile_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  almanac_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  discriminator_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bandingprofileuuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  thinningprofileuuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  almanacprofileuuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  discriminatorprofileuuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete jobdescription_;
}

void Job::ArenaDtor(void* object) {
  Job* _this = reinterpret_cast< Job* >(object);
  (void)_this;
  _this->active_profiles_. ~MapField();
}
inline void Job::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &Job::ArenaDtor);
  }
}
void Job::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Job::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.Job)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  active_profiles_.Clear();
  bandingprofile_.ClearToEmpty();
  thinningprofile_.ClearToEmpty();
  almanac_.ClearToEmpty();
  discriminator_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  bandingprofileuuid_.ClearToEmpty();
  thinningprofileuuid_.ClearToEmpty();
  almanacprofileuuid_.ClearToEmpty();
  discriminatorprofileuuid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && jobdescription_ != nullptr) {
    delete jobdescription_;
  }
  jobdescription_ = nullptr;
  ::memset(&stoptimems_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&lastusedtimems_) -
      reinterpret_cast<char*>(&stoptimems_)) + sizeof(lastusedtimems_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Job::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.jobs.JobDescription jobDescription = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_jobdescription(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string bandingProfile = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_bandingprofile();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.bandingProfile"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string thinningProfile = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_thinningprofile();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.thinningProfile"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 stopTimeMs = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          stoptimems_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 lastUpdateTimeMs = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          lastupdatetimems_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float expectedAcreage = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 53)) {
          expectedacreage_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // bool completed = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          completed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string almanac = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_almanac();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.almanac"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string discriminator = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_discriminator();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.discriminator"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string bandingProfileUUID = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          auto str = _internal_mutable_bandingprofileuuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.bandingProfileUUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string thinningProfileUUID = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          auto str = _internal_mutable_thinningprofileuuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.thinningProfileUUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string almanacProfileUUID = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          auto str = _internal_mutable_almanacprofileuuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.almanacProfileUUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string discriminatorProfileUUID = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          auto str = _internal_mutable_discriminatorprofileuuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.Job.discriminatorProfileUUID"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<int32, .carbon.frontend.jobs.ActiveProfile> active_profiles = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&active_profiles_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<122>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int64 lastUsedTimeMs = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          lastusedtimems_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Job::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.Job)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.jobs.JobDescription jobDescription = 1;
  if (this->_internal_has_jobdescription()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::jobdescription(this), target, stream);
  }

  // string bandingProfile = 2;
  if (!this->_internal_bandingprofile().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_bandingprofile().data(), static_cast<int>(this->_internal_bandingprofile().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.bandingProfile");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_bandingprofile(), target);
  }

  // string thinningProfile = 3;
  if (!this->_internal_thinningprofile().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_thinningprofile().data(), static_cast<int>(this->_internal_thinningprofile().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.thinningProfile");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_thinningprofile(), target);
  }

  // int64 stopTimeMs = 4;
  if (this->_internal_stoptimems() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(4, this->_internal_stoptimems(), target);
  }

  // int64 lastUpdateTimeMs = 5;
  if (this->_internal_lastupdatetimems() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(5, this->_internal_lastupdatetimems(), target);
  }

  // float expectedAcreage = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = this->_internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(6, this->_internal_expectedacreage(), target);
  }

  // bool completed = 7;
  if (this->_internal_completed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_completed(), target);
  }

  // string almanac = 8;
  if (!this->_internal_almanac().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_almanac().data(), static_cast<int>(this->_internal_almanac().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.almanac");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_almanac(), target);
  }

  // string discriminator = 9;
  if (!this->_internal_discriminator().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_discriminator().data(), static_cast<int>(this->_internal_discriminator().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.discriminator");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_discriminator(), target);
  }

  // string crop_id = 10;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.crop_id");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_crop_id(), target);
  }

  // string bandingProfileUUID = 11;
  if (!this->_internal_bandingprofileuuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_bandingprofileuuid().data(), static_cast<int>(this->_internal_bandingprofileuuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.bandingProfileUUID");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_bandingprofileuuid(), target);
  }

  // string thinningProfileUUID = 12;
  if (!this->_internal_thinningprofileuuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_thinningprofileuuid().data(), static_cast<int>(this->_internal_thinningprofileuuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.thinningProfileUUID");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_thinningprofileuuid(), target);
  }

  // string almanacProfileUUID = 13;
  if (!this->_internal_almanacprofileuuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_almanacprofileuuid().data(), static_cast<int>(this->_internal_almanacprofileuuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.almanacProfileUUID");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_almanacprofileuuid(), target);
  }

  // string discriminatorProfileUUID = 14;
  if (!this->_internal_discriminatorprofileuuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_discriminatorprofileuuid().data(), static_cast<int>(this->_internal_discriminatorprofileuuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.Job.discriminatorProfileUUID");
    target = stream->WriteStringMaybeAliased(
        14, this->_internal_discriminatorprofileuuid(), target);
  }

  // map<int32, .carbon.frontend.jobs.ActiveProfile> active_profiles = 15;
  if (!this->_internal_active_profiles().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_active_profiles().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_active_profiles().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >::const_iterator
          it = this->_internal_active_profiles().begin();
          it != this->_internal_active_profiles().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = Job_ActiveProfilesEntry_DoNotUse::Funcs::InternalSerialize(15, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >::const_iterator
          it = this->_internal_active_profiles().begin();
          it != this->_internal_active_profiles().end(); ++it) {
        target = Job_ActiveProfilesEntry_DoNotUse::Funcs::InternalSerialize(15, it->first, it->second, target, stream);
      }
    }
  }

  // int64 lastUsedTimeMs = 16;
  if (this->_internal_lastusedtimems() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(16, this->_internal_lastusedtimems(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.Job)
  return target;
}

size_t Job::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.Job)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<int32, .carbon.frontend.jobs.ActiveProfile> active_profiles = 15;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_active_profiles_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::jobs::ActiveProfile >::const_iterator
      it = this->_internal_active_profiles().begin();
      it != this->_internal_active_profiles().end(); ++it) {
    total_size += Job_ActiveProfilesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string bandingProfile = 2;
  if (!this->_internal_bandingprofile().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_bandingprofile());
  }

  // string thinningProfile = 3;
  if (!this->_internal_thinningprofile().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_thinningprofile());
  }

  // string almanac = 8;
  if (!this->_internal_almanac().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_almanac());
  }

  // string discriminator = 9;
  if (!this->_internal_discriminator().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_discriminator());
  }

  // string crop_id = 10;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // string bandingProfileUUID = 11;
  if (!this->_internal_bandingprofileuuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_bandingprofileuuid());
  }

  // string thinningProfileUUID = 12;
  if (!this->_internal_thinningprofileuuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_thinningprofileuuid());
  }

  // string almanacProfileUUID = 13;
  if (!this->_internal_almanacprofileuuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_almanacprofileuuid());
  }

  // string discriminatorProfileUUID = 14;
  if (!this->_internal_discriminatorprofileuuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_discriminatorprofileuuid());
  }

  // .carbon.frontend.jobs.JobDescription jobDescription = 1;
  if (this->_internal_has_jobdescription()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *jobdescription_);
  }

  // int64 stopTimeMs = 4;
  if (this->_internal_stoptimems() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_stoptimems());
  }

  // int64 lastUpdateTimeMs = 5;
  if (this->_internal_lastupdatetimems() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_lastupdatetimems());
  }

  // float expectedAcreage = 6;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = this->_internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    total_size += 1 + 4;
  }

  // bool completed = 7;
  if (this->_internal_completed() != 0) {
    total_size += 1 + 1;
  }

  // int64 lastUsedTimeMs = 16;
  if (this->_internal_lastusedtimems() != 0) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64Size(
        this->_internal_lastusedtimems());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Job::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Job::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Job::GetClassData() const { return &_class_data_; }

void Job::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Job *>(to)->MergeFrom(
      static_cast<const Job &>(from));
}


void Job::MergeFrom(const Job& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.Job)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  active_profiles_.MergeFrom(from.active_profiles_);
  if (!from._internal_bandingprofile().empty()) {
    _internal_set_bandingprofile(from._internal_bandingprofile());
  }
  if (!from._internal_thinningprofile().empty()) {
    _internal_set_thinningprofile(from._internal_thinningprofile());
  }
  if (!from._internal_almanac().empty()) {
    _internal_set_almanac(from._internal_almanac());
  }
  if (!from._internal_discriminator().empty()) {
    _internal_set_discriminator(from._internal_discriminator());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (!from._internal_bandingprofileuuid().empty()) {
    _internal_set_bandingprofileuuid(from._internal_bandingprofileuuid());
  }
  if (!from._internal_thinningprofileuuid().empty()) {
    _internal_set_thinningprofileuuid(from._internal_thinningprofileuuid());
  }
  if (!from._internal_almanacprofileuuid().empty()) {
    _internal_set_almanacprofileuuid(from._internal_almanacprofileuuid());
  }
  if (!from._internal_discriminatorprofileuuid().empty()) {
    _internal_set_discriminatorprofileuuid(from._internal_discriminatorprofileuuid());
  }
  if (from._internal_has_jobdescription()) {
    _internal_mutable_jobdescription()->::carbon::frontend::jobs::JobDescription::MergeFrom(from._internal_jobdescription());
  }
  if (from._internal_stoptimems() != 0) {
    _internal_set_stoptimems(from._internal_stoptimems());
  }
  if (from._internal_lastupdatetimems() != 0) {
    _internal_set_lastupdatetimems(from._internal_lastupdatetimems());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = from._internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    _internal_set_expectedacreage(from._internal_expectedacreage());
  }
  if (from._internal_completed() != 0) {
    _internal_set_completed(from._internal_completed());
  }
  if (from._internal_lastusedtimems() != 0) {
    _internal_set_lastusedtimems(from._internal_lastusedtimems());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Job::CopyFrom(const Job& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.Job)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Job::IsInitialized() const {
  return true;
}

void Job::InternalSwap(Job* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  active_profiles_.InternalSwap(&other->active_profiles_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bandingprofile_, lhs_arena,
      &other->bandingprofile_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &thinningprofile_, lhs_arena,
      &other->thinningprofile_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &almanac_, lhs_arena,
      &other->almanac_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &discriminator_, lhs_arena,
      &other->discriminator_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bandingprofileuuid_, lhs_arena,
      &other->bandingprofileuuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &thinningprofileuuid_, lhs_arena,
      &other->thinningprofileuuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &almanacprofileuuid_, lhs_arena,
      &other->almanacprofileuuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &discriminatorprofileuuid_, lhs_arena,
      &other->discriminatorprofileuuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Job, lastusedtimems_)
      + sizeof(Job::lastusedtimems_)
      - PROTOBUF_FIELD_OFFSET(Job, jobdescription_)>(
          reinterpret_cast<char*>(&jobdescription_),
          reinterpret_cast<char*>(&other->jobdescription_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Job::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[3]);
}

// ===================================================================

class CreateJobRequest::_Internal {
 public:
};

CreateJobRequest::CreateJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.CreateJobRequest)
}
CreateJobRequest::CreateJobRequest(const CreateJobRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  ::memcpy(&active_, &from.active_,
    static_cast<size_t>(reinterpret_cast<char*>(&expectedacreage_) -
    reinterpret_cast<char*>(&active_)) + sizeof(expectedacreage_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.CreateJobRequest)
}

inline void CreateJobRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&active_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&expectedacreage_) -
    reinterpret_cast<char*>(&active_)) + sizeof(expectedacreage_));
}

CreateJobRequest::~CreateJobRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.CreateJobRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CreateJobRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CreateJobRequest::ArenaDtor(void* object) {
  CreateJobRequest* _this = reinterpret_cast< CreateJobRequest* >(object);
  (void)_this;
}
void CreateJobRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CreateJobRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CreateJobRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.CreateJobRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  ::memset(&active_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&expectedacreage_) -
      reinterpret_cast<char*>(&active_)) + sizeof(expectedacreage_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CreateJobRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.CreateJobRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool active = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          active_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float expectedAcreage = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          expectedacreage_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CreateJobRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.CreateJobRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.CreateJobRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // bool active = 2;
  if (this->_internal_active() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_active(), target);
  }

  // float expectedAcreage = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = this->_internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_expectedacreage(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.CreateJobRequest)
  return target;
}

size_t CreateJobRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.CreateJobRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // bool active = 2;
  if (this->_internal_active() != 0) {
    total_size += 1 + 1;
  }

  // float expectedAcreage = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = this->_internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CreateJobRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CreateJobRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CreateJobRequest::GetClassData() const { return &_class_data_; }

void CreateJobRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CreateJobRequest *>(to)->MergeFrom(
      static_cast<const CreateJobRequest &>(from));
}


void CreateJobRequest::MergeFrom(const CreateJobRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.CreateJobRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_active() != 0) {
    _internal_set_active(from._internal_active());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = from._internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    _internal_set_expectedacreage(from._internal_expectedacreage());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CreateJobRequest::CopyFrom(const CreateJobRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.CreateJobRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateJobRequest::IsInitialized() const {
  return true;
}

void CreateJobRequest::InternalSwap(CreateJobRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CreateJobRequest, expectedacreage_)
      + sizeof(CreateJobRequest::expectedacreage_)
      - PROTOBUF_FIELD_OFFSET(CreateJobRequest, active_)>(
          reinterpret_cast<char*>(&active_),
          reinterpret_cast<char*>(&other->active_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CreateJobRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[4]);
}

// ===================================================================

class CreateJobResponse::_Internal {
 public:
};

CreateJobResponse::CreateJobResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.CreateJobResponse)
}
CreateJobResponse::CreateJobResponse(const CreateJobResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.CreateJobResponse)
}

inline void CreateJobResponse::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CreateJobResponse::~CreateJobResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.CreateJobResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CreateJobResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CreateJobResponse::ArenaDtor(void* object) {
  CreateJobResponse* _this = reinterpret_cast< CreateJobResponse* >(object);
  (void)_this;
}
void CreateJobResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CreateJobResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CreateJobResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.CreateJobResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CreateJobResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.CreateJobResponse.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CreateJobResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.CreateJobResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.CreateJobResponse.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.CreateJobResponse)
  return target;
}

size_t CreateJobResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.CreateJobResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CreateJobResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CreateJobResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CreateJobResponse::GetClassData() const { return &_class_data_; }

void CreateJobResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CreateJobResponse *>(to)->MergeFrom(
      static_cast<const CreateJobResponse &>(from));
}


void CreateJobResponse::MergeFrom(const CreateJobResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.CreateJobResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CreateJobResponse::CopyFrom(const CreateJobResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.CreateJobResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CreateJobResponse::IsInitialized() const {
  return true;
}

void CreateJobResponse::InternalSwap(CreateJobResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata CreateJobResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[5]);
}

// ===================================================================

class UpdateJobRequest::_Internal {
 public:
  static const ::carbon::frontend::jobs::JobDescription& jobdescription(const UpdateJobRequest* msg);
};

const ::carbon::frontend::jobs::JobDescription&
UpdateJobRequest::_Internal::jobdescription(const UpdateJobRequest* msg) {
  return *msg->jobdescription_;
}
UpdateJobRequest::UpdateJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.UpdateJobRequest)
}
UpdateJobRequest::UpdateJobRequest(const UpdateJobRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_jobdescription()) {
    jobdescription_ = new ::carbon::frontend::jobs::JobDescription(*from.jobdescription_);
  } else {
    jobdescription_ = nullptr;
  }
  expectedacreage_ = from.expectedacreage_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.UpdateJobRequest)
}

inline void UpdateJobRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&jobdescription_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&expectedacreage_) -
    reinterpret_cast<char*>(&jobdescription_)) + sizeof(expectedacreage_));
}

UpdateJobRequest::~UpdateJobRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.UpdateJobRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UpdateJobRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete jobdescription_;
}

void UpdateJobRequest::ArenaDtor(void* object) {
  UpdateJobRequest* _this = reinterpret_cast< UpdateJobRequest* >(object);
  (void)_this;
}
void UpdateJobRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UpdateJobRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UpdateJobRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.UpdateJobRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && jobdescription_ != nullptr) {
    delete jobdescription_;
  }
  jobdescription_ = nullptr;
  expectedacreage_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UpdateJobRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.jobs.JobDescription jobDescription = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_jobdescription(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float expectedAcreage = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          expectedacreage_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UpdateJobRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.UpdateJobRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.jobs.JobDescription jobDescription = 1;
  if (this->_internal_has_jobdescription()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::jobdescription(this), target, stream);
  }

  // float expectedAcreage = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = this->_internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_expectedacreage(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.UpdateJobRequest)
  return target;
}

size_t UpdateJobRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.UpdateJobRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.jobs.JobDescription jobDescription = 1;
  if (this->_internal_has_jobdescription()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *jobdescription_);
  }

  // float expectedAcreage = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = this->_internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpdateJobRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UpdateJobRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpdateJobRequest::GetClassData() const { return &_class_data_; }

void UpdateJobRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UpdateJobRequest *>(to)->MergeFrom(
      static_cast<const UpdateJobRequest &>(from));
}


void UpdateJobRequest::MergeFrom(const UpdateJobRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.UpdateJobRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_jobdescription()) {
    _internal_mutable_jobdescription()->::carbon::frontend::jobs::JobDescription::MergeFrom(from._internal_jobdescription());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_expectedacreage = from._internal_expectedacreage();
  uint32_t raw_expectedacreage;
  memcpy(&raw_expectedacreage, &tmp_expectedacreage, sizeof(tmp_expectedacreage));
  if (raw_expectedacreage != 0) {
    _internal_set_expectedacreage(from._internal_expectedacreage());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UpdateJobRequest::CopyFrom(const UpdateJobRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.UpdateJobRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpdateJobRequest::IsInitialized() const {
  return true;
}

void UpdateJobRequest::InternalSwap(UpdateJobRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(UpdateJobRequest, expectedacreage_)
      + sizeof(UpdateJobRequest::expectedacreage_)
      - PROTOBUF_FIELD_OFFSET(UpdateJobRequest, jobdescription_)>(
          reinterpret_cast<char*>(&jobdescription_),
          reinterpret_cast<char*>(&other->jobdescription_));
}

::PROTOBUF_NAMESPACE_ID::Metadata UpdateJobRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[6]);
}

// ===================================================================

class GetNextJobsRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& timestamp(const GetNextJobsRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextJobsRequest::_Internal::timestamp(const GetNextJobsRequest* msg) {
  return *msg->timestamp_;
}
void GetNextJobsRequest::clear_timestamp() {
  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
}
GetNextJobsRequest::GetNextJobsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetNextJobsRequest)
}
GetNextJobsRequest::GetNextJobsRequest(const GetNextJobsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_timestamp()) {
    timestamp_ = new ::carbon::frontend::util::Timestamp(*from.timestamp_);
  } else {
    timestamp_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetNextJobsRequest)
}

inline void GetNextJobsRequest::SharedCtor() {
timestamp_ = nullptr;
}

GetNextJobsRequest::~GetNextJobsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetNextJobsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextJobsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete timestamp_;
}

void GetNextJobsRequest::ArenaDtor(void* object) {
  GetNextJobsRequest* _this = reinterpret_cast< GetNextJobsRequest* >(object);
  (void)_this;
}
void GetNextJobsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextJobsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextJobsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetNextJobsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextJobsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp timestamp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_timestamp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextJobsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetNextJobsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp timestamp = 1;
  if (this->_internal_has_timestamp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::timestamp(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetNextJobsRequest)
  return target;
}

size_t GetNextJobsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetNextJobsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp timestamp = 1;
  if (this->_internal_has_timestamp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *timestamp_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextJobsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextJobsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextJobsRequest::GetClassData() const { return &_class_data_; }

void GetNextJobsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextJobsRequest *>(to)->MergeFrom(
      static_cast<const GetNextJobsRequest &>(from));
}


void GetNextJobsRequest::MergeFrom(const GetNextJobsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetNextJobsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_timestamp()) {
    _internal_mutable_timestamp()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_timestamp());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextJobsRequest::CopyFrom(const GetNextJobsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetNextJobsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextJobsRequest::IsInitialized() const {
  return true;
}

void GetNextJobsRequest::InternalSwap(GetNextJobsRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(timestamp_, other->timestamp_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextJobsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[7]);
}

// ===================================================================

class JobWithMetrics::_Internal {
 public:
  static const ::carbon::frontend::jobs::Job& job(const JobWithMetrics* msg);
  static const ::metrics_aggregator::Metrics& metrics(const JobWithMetrics* msg);
};

const ::carbon::frontend::jobs::Job&
JobWithMetrics::_Internal::job(const JobWithMetrics* msg) {
  return *msg->job_;
}
const ::metrics_aggregator::Metrics&
JobWithMetrics::_Internal::metrics(const JobWithMetrics* msg) {
  return *msg->metrics_;
}
void JobWithMetrics::clear_metrics() {
  if (GetArenaForAllocation() == nullptr && metrics_ != nullptr) {
    delete metrics_;
  }
  metrics_ = nullptr;
}
JobWithMetrics::JobWithMetrics(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.JobWithMetrics)
}
JobWithMetrics::JobWithMetrics(const JobWithMetrics& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_job()) {
    job_ = new ::carbon::frontend::jobs::Job(*from.job_);
  } else {
    job_ = nullptr;
  }
  if (from._internal_has_metrics()) {
    metrics_ = new ::metrics_aggregator::Metrics(*from.metrics_);
  } else {
    metrics_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.JobWithMetrics)
}

inline void JobWithMetrics::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&job_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&metrics_) -
    reinterpret_cast<char*>(&job_)) + sizeof(metrics_));
}

JobWithMetrics::~JobWithMetrics() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.JobWithMetrics)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void JobWithMetrics::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete job_;
  if (this != internal_default_instance()) delete metrics_;
}

void JobWithMetrics::ArenaDtor(void* object) {
  JobWithMetrics* _this = reinterpret_cast< JobWithMetrics* >(object);
  (void)_this;
}
void JobWithMetrics::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void JobWithMetrics::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void JobWithMetrics::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.JobWithMetrics)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && job_ != nullptr) {
    delete job_;
  }
  job_ = nullptr;
  if (GetArenaForAllocation() == nullptr && metrics_ != nullptr) {
    delete metrics_;
  }
  metrics_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* JobWithMetrics::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.jobs.Job job = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_job(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .metrics_aggregator.Metrics metrics = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_metrics(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* JobWithMetrics::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.JobWithMetrics)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.jobs.Job job = 1;
  if (this->_internal_has_job()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::job(this), target, stream);
  }

  // .metrics_aggregator.Metrics metrics = 2;
  if (this->_internal_has_metrics()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::metrics(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.JobWithMetrics)
  return target;
}

size_t JobWithMetrics::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.JobWithMetrics)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.jobs.Job job = 1;
  if (this->_internal_has_job()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *job_);
  }

  // .metrics_aggregator.Metrics metrics = 2;
  if (this->_internal_has_metrics()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *metrics_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData JobWithMetrics::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    JobWithMetrics::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*JobWithMetrics::GetClassData() const { return &_class_data_; }

void JobWithMetrics::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<JobWithMetrics *>(to)->MergeFrom(
      static_cast<const JobWithMetrics &>(from));
}


void JobWithMetrics::MergeFrom(const JobWithMetrics& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.JobWithMetrics)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_job()) {
    _internal_mutable_job()->::carbon::frontend::jobs::Job::MergeFrom(from._internal_job());
  }
  if (from._internal_has_metrics()) {
    _internal_mutable_metrics()->::metrics_aggregator::Metrics::MergeFrom(from._internal_metrics());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void JobWithMetrics::CopyFrom(const JobWithMetrics& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.JobWithMetrics)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool JobWithMetrics::IsInitialized() const {
  return true;
}

void JobWithMetrics::InternalSwap(JobWithMetrics* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(JobWithMetrics, metrics_)
      + sizeof(JobWithMetrics::metrics_)
      - PROTOBUF_FIELD_OFFSET(JobWithMetrics, job_)>(
          reinterpret_cast<char*>(&job_),
          reinterpret_cast<char*>(&other->job_));
}

::PROTOBUF_NAMESPACE_ID::Metadata JobWithMetrics::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[8]);
}

// ===================================================================

class GetNextJobsResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& timestamp(const GetNextJobsResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextJobsResponse::_Internal::timestamp(const GetNextJobsResponse* msg) {
  return *msg->timestamp_;
}
void GetNextJobsResponse::clear_timestamp() {
  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
}
GetNextJobsResponse::GetNextJobsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  jobs_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetNextJobsResponse)
}
GetNextJobsResponse::GetNextJobsResponse(const GetNextJobsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      jobs_(from.jobs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  activejobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    activejobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_activejobid().empty()) {
    activejobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_activejobid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_timestamp()) {
    timestamp_ = new ::carbon::frontend::util::Timestamp(*from.timestamp_);
  } else {
    timestamp_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetNextJobsResponse)
}

inline void GetNextJobsResponse::SharedCtor() {
activejobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  activejobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
timestamp_ = nullptr;
}

GetNextJobsResponse::~GetNextJobsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetNextJobsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextJobsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  activejobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete timestamp_;
}

void GetNextJobsResponse::ArenaDtor(void* object) {
  GetNextJobsResponse* _this = reinterpret_cast< GetNextJobsResponse* >(object);
  (void)_this;
}
void GetNextJobsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextJobsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextJobsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetNextJobsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobs_.Clear();
  activejobid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextJobsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.jobs.JobWithMetrics jobs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_jobs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string activeJobId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_activejobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.GetNextJobsResponse.activeJobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp timestamp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_timestamp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextJobsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetNextJobsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.jobs.JobWithMetrics jobs = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_jobs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_jobs(i), target, stream);
  }

  // string activeJobId = 2;
  if (!this->_internal_activejobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_activejobid().data(), static_cast<int>(this->_internal_activejobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.GetNextJobsResponse.activeJobId");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_activejobid(), target);
  }

  // .carbon.frontend.util.Timestamp timestamp = 3;
  if (this->_internal_has_timestamp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::timestamp(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetNextJobsResponse)
  return target;
}

size_t GetNextJobsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetNextJobsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.jobs.JobWithMetrics jobs = 1;
  total_size += 1UL * this->_internal_jobs_size();
  for (const auto& msg : this->jobs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string activeJobId = 2;
  if (!this->_internal_activejobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_activejobid());
  }

  // .carbon.frontend.util.Timestamp timestamp = 3;
  if (this->_internal_has_timestamp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *timestamp_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextJobsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextJobsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextJobsResponse::GetClassData() const { return &_class_data_; }

void GetNextJobsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextJobsResponse *>(to)->MergeFrom(
      static_cast<const GetNextJobsResponse &>(from));
}


void GetNextJobsResponse::MergeFrom(const GetNextJobsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetNextJobsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  jobs_.MergeFrom(from.jobs_);
  if (!from._internal_activejobid().empty()) {
    _internal_set_activejobid(from._internal_activejobid());
  }
  if (from._internal_has_timestamp()) {
    _internal_mutable_timestamp()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_timestamp());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextJobsResponse::CopyFrom(const GetNextJobsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetNextJobsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextJobsResponse::IsInitialized() const {
  return true;
}

void GetNextJobsResponse::InternalSwap(GetNextJobsResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  jobs_.InternalSwap(&other->jobs_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &activejobid_, lhs_arena,
      &other->activejobid_, rhs_arena
  );
  swap(timestamp_, other->timestamp_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextJobsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[9]);
}

// ===================================================================

class GetNextActiveJobIdRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& timestamp(const GetNextActiveJobIdRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextActiveJobIdRequest::_Internal::timestamp(const GetNextActiveJobIdRequest* msg) {
  return *msg->timestamp_;
}
void GetNextActiveJobIdRequest::clear_timestamp() {
  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
}
GetNextActiveJobIdRequest::GetNextActiveJobIdRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetNextActiveJobIdRequest)
}
GetNextActiveJobIdRequest::GetNextActiveJobIdRequest(const GetNextActiveJobIdRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_timestamp()) {
    timestamp_ = new ::carbon::frontend::util::Timestamp(*from.timestamp_);
  } else {
    timestamp_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetNextActiveJobIdRequest)
}

inline void GetNextActiveJobIdRequest::SharedCtor() {
timestamp_ = nullptr;
}

GetNextActiveJobIdRequest::~GetNextActiveJobIdRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetNextActiveJobIdRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveJobIdRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete timestamp_;
}

void GetNextActiveJobIdRequest::ArenaDtor(void* object) {
  GetNextActiveJobIdRequest* _this = reinterpret_cast< GetNextActiveJobIdRequest* >(object);
  (void)_this;
}
void GetNextActiveJobIdRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveJobIdRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveJobIdRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetNextActiveJobIdRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveJobIdRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp timestamp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_timestamp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveJobIdRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetNextActiveJobIdRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp timestamp = 1;
  if (this->_internal_has_timestamp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::timestamp(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetNextActiveJobIdRequest)
  return target;
}

size_t GetNextActiveJobIdRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetNextActiveJobIdRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp timestamp = 1;
  if (this->_internal_has_timestamp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *timestamp_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveJobIdRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveJobIdRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveJobIdRequest::GetClassData() const { return &_class_data_; }

void GetNextActiveJobIdRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveJobIdRequest *>(to)->MergeFrom(
      static_cast<const GetNextActiveJobIdRequest &>(from));
}


void GetNextActiveJobIdRequest::MergeFrom(const GetNextActiveJobIdRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetNextActiveJobIdRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_timestamp()) {
    _internal_mutable_timestamp()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_timestamp());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveJobIdRequest::CopyFrom(const GetNextActiveJobIdRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetNextActiveJobIdRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveJobIdRequest::IsInitialized() const {
  return true;
}

void GetNextActiveJobIdRequest::InternalSwap(GetNextActiveJobIdRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(timestamp_, other->timestamp_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveJobIdRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[10]);
}

// ===================================================================

class GetNextActiveJobIdResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& timestamp(const GetNextActiveJobIdResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextActiveJobIdResponse::_Internal::timestamp(const GetNextActiveJobIdResponse* msg) {
  return *msg->timestamp_;
}
void GetNextActiveJobIdResponse::clear_timestamp() {
  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
}
GetNextActiveJobIdResponse::GetNextActiveJobIdResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetNextActiveJobIdResponse)
}
GetNextActiveJobIdResponse::GetNextActiveJobIdResponse(const GetNextActiveJobIdResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  activejobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    activejobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_activejobid().empty()) {
    activejobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_activejobid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_timestamp()) {
    timestamp_ = new ::carbon::frontend::util::Timestamp(*from.timestamp_);
  } else {
    timestamp_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetNextActiveJobIdResponse)
}

inline void GetNextActiveJobIdResponse::SharedCtor() {
activejobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  activejobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
timestamp_ = nullptr;
}

GetNextActiveJobIdResponse::~GetNextActiveJobIdResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetNextActiveJobIdResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveJobIdResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  activejobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete timestamp_;
}

void GetNextActiveJobIdResponse::ArenaDtor(void* object) {
  GetNextActiveJobIdResponse* _this = reinterpret_cast< GetNextActiveJobIdResponse* >(object);
  (void)_this;
}
void GetNextActiveJobIdResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveJobIdResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveJobIdResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetNextActiveJobIdResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  activejobid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && timestamp_ != nullptr) {
    delete timestamp_;
  }
  timestamp_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveJobIdResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string activeJobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_activejobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.GetNextActiveJobIdResponse.activeJobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp timestamp = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_timestamp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveJobIdResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetNextActiveJobIdResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string activeJobId = 1;
  if (!this->_internal_activejobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_activejobid().data(), static_cast<int>(this->_internal_activejobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.GetNextActiveJobIdResponse.activeJobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_activejobid(), target);
  }

  // .carbon.frontend.util.Timestamp timestamp = 2;
  if (this->_internal_has_timestamp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::timestamp(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetNextActiveJobIdResponse)
  return target;
}

size_t GetNextActiveJobIdResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetNextActiveJobIdResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string activeJobId = 1;
  if (!this->_internal_activejobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_activejobid());
  }

  // .carbon.frontend.util.Timestamp timestamp = 2;
  if (this->_internal_has_timestamp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *timestamp_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveJobIdResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveJobIdResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveJobIdResponse::GetClassData() const { return &_class_data_; }

void GetNextActiveJobIdResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveJobIdResponse *>(to)->MergeFrom(
      static_cast<const GetNextActiveJobIdResponse &>(from));
}


void GetNextActiveJobIdResponse::MergeFrom(const GetNextActiveJobIdResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetNextActiveJobIdResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_activejobid().empty()) {
    _internal_set_activejobid(from._internal_activejobid());
  }
  if (from._internal_has_timestamp()) {
    _internal_mutable_timestamp()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_timestamp());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveJobIdResponse::CopyFrom(const GetNextActiveJobIdResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetNextActiveJobIdResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveJobIdResponse::IsInitialized() const {
  return true;
}

void GetNextActiveJobIdResponse::InternalSwap(GetNextActiveJobIdResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &activejobid_, lhs_arena,
      &other->activejobid_, rhs_arena
  );
  swap(timestamp_, other->timestamp_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveJobIdResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[11]);
}

// ===================================================================

class GetJobRequest::_Internal {
 public:
};

GetJobRequest::GetJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetJobRequest)
}
GetJobRequest::GetJobRequest(const GetJobRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetJobRequest)
}

inline void GetJobRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetJobRequest::~GetJobRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetJobRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetJobRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetJobRequest::ArenaDtor(void* object) {
  GetJobRequest* _this = reinterpret_cast< GetJobRequest* >(object);
  (void)_this;
}
void GetJobRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetJobRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetJobRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetJobRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetJobRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.GetJobRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetJobRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetJobRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.GetJobRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetJobRequest)
  return target;
}

size_t GetJobRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetJobRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetJobRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetJobRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetJobRequest::GetClassData() const { return &_class_data_; }

void GetJobRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetJobRequest *>(to)->MergeFrom(
      static_cast<const GetJobRequest &>(from));
}


void GetJobRequest::MergeFrom(const GetJobRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetJobRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetJobRequest::CopyFrom(const GetJobRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetJobRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetJobRequest::IsInitialized() const {
  return true;
}

void GetJobRequest::InternalSwap(GetJobRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetJobRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[12]);
}

// ===================================================================

class GetJobResponse::_Internal {
 public:
  static const ::carbon::frontend::jobs::Job& job(const GetJobResponse* msg);
};

const ::carbon::frontend::jobs::Job&
GetJobResponse::_Internal::job(const GetJobResponse* msg) {
  return *msg->job_;
}
GetJobResponse::GetJobResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetJobResponse)
}
GetJobResponse::GetJobResponse(const GetJobResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_job()) {
    job_ = new ::carbon::frontend::jobs::Job(*from.job_);
  } else {
    job_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetJobResponse)
}

inline void GetJobResponse::SharedCtor() {
job_ = nullptr;
}

GetJobResponse::~GetJobResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetJobResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetJobResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete job_;
}

void GetJobResponse::ArenaDtor(void* object) {
  GetJobResponse* _this = reinterpret_cast< GetJobResponse* >(object);
  (void)_this;
}
void GetJobResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetJobResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetJobResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetJobResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && job_ != nullptr) {
    delete job_;
  }
  job_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetJobResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.jobs.Job job = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_job(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetJobResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetJobResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.jobs.Job job = 1;
  if (this->_internal_has_job()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::job(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetJobResponse)
  return target;
}

size_t GetJobResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetJobResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.jobs.Job job = 1;
  if (this->_internal_has_job()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *job_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetJobResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetJobResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetJobResponse::GetClassData() const { return &_class_data_; }

void GetJobResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetJobResponse *>(to)->MergeFrom(
      static_cast<const GetJobResponse &>(from));
}


void GetJobResponse::MergeFrom(const GetJobResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetJobResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_job()) {
    _internal_mutable_job()->::carbon::frontend::jobs::Job::MergeFrom(from._internal_job());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetJobResponse::CopyFrom(const GetJobResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetJobResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetJobResponse::IsInitialized() const {
  return true;
}

void GetJobResponse::InternalSwap(GetJobResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(job_, other->job_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetJobResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[13]);
}

// ===================================================================

class StartJobRequest::_Internal {
 public:
};

StartJobRequest::StartJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.StartJobRequest)
}
StartJobRequest::StartJobRequest(const StartJobRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.StartJobRequest)
}

inline void StartJobRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

StartJobRequest::~StartJobRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.StartJobRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StartJobRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StartJobRequest::ArenaDtor(void* object) {
  StartJobRequest* _this = reinterpret_cast< StartJobRequest* >(object);
  (void)_this;
}
void StartJobRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StartJobRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StartJobRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.StartJobRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StartJobRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.StartJobRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StartJobRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.StartJobRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.StartJobRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.StartJobRequest)
  return target;
}

size_t StartJobRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.StartJobRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StartJobRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StartJobRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StartJobRequest::GetClassData() const { return &_class_data_; }

void StartJobRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StartJobRequest *>(to)->MergeFrom(
      static_cast<const StartJobRequest &>(from));
}


void StartJobRequest::MergeFrom(const StartJobRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.StartJobRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StartJobRequest::CopyFrom(const StartJobRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.StartJobRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartJobRequest::IsInitialized() const {
  return true;
}

void StartJobRequest::InternalSwap(StartJobRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata StartJobRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[14]);
}

// ===================================================================

class GetConfigDumpRequest::_Internal {
 public:
};

GetConfigDumpRequest::GetConfigDumpRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetConfigDumpRequest)
}
GetConfigDumpRequest::GetConfigDumpRequest(const GetConfigDumpRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetConfigDumpRequest)
}

inline void GetConfigDumpRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetConfigDumpRequest::~GetConfigDumpRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetConfigDumpRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetConfigDumpRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetConfigDumpRequest::ArenaDtor(void* object) {
  GetConfigDumpRequest* _this = reinterpret_cast< GetConfigDumpRequest* >(object);
  (void)_this;
}
void GetConfigDumpRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetConfigDumpRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetConfigDumpRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetConfigDumpRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetConfigDumpRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.GetConfigDumpRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetConfigDumpRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetConfigDumpRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.GetConfigDumpRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetConfigDumpRequest)
  return target;
}

size_t GetConfigDumpRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetConfigDumpRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetConfigDumpRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetConfigDumpRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetConfigDumpRequest::GetClassData() const { return &_class_data_; }

void GetConfigDumpRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetConfigDumpRequest *>(to)->MergeFrom(
      static_cast<const GetConfigDumpRequest &>(from));
}


void GetConfigDumpRequest::MergeFrom(const GetConfigDumpRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetConfigDumpRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetConfigDumpRequest::CopyFrom(const GetConfigDumpRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetConfigDumpRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetConfigDumpRequest::IsInitialized() const {
  return true;
}

void GetConfigDumpRequest::InternalSwap(GetConfigDumpRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetConfigDumpRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[15]);
}

// ===================================================================

class GetConfigDumpResponse::_Internal {
 public:
  static const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot& rootconfig(const GetConfigDumpResponse* msg);
};

const ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot&
GetConfigDumpResponse::_Internal::rootconfig(const GetConfigDumpResponse* msg) {
  return *msg->rootconfig_;
}
void GetConfigDumpResponse::clear_rootconfig() {
  if (GetArenaForAllocation() == nullptr && rootconfig_ != nullptr) {
    delete rootconfig_;
  }
  rootconfig_ = nullptr;
}
GetConfigDumpResponse::GetConfigDumpResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetConfigDumpResponse)
}
GetConfigDumpResponse::GetConfigDumpResponse(const GetConfigDumpResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_rootconfig()) {
    rootconfig_ = new ::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot(*from.rootconfig_);
  } else {
    rootconfig_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetConfigDumpResponse)
}

inline void GetConfigDumpResponse::SharedCtor() {
rootconfig_ = nullptr;
}

GetConfigDumpResponse::~GetConfigDumpResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetConfigDumpResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetConfigDumpResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete rootconfig_;
}

void GetConfigDumpResponse::ArenaDtor(void* object) {
  GetConfigDumpResponse* _this = reinterpret_cast< GetConfigDumpResponse* >(object);
  (void)_this;
}
void GetConfigDumpResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetConfigDumpResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetConfigDumpResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetConfigDumpResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && rootconfig_ != nullptr) {
    delete rootconfig_;
  }
  rootconfig_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetConfigDumpResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_rootconfig(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetConfigDumpResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetConfigDumpResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 1;
  if (this->_internal_has_rootconfig()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::rootconfig(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetConfigDumpResponse)
  return target;
}

size_t GetConfigDumpResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetConfigDumpResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot rootConfig = 1;
  if (this->_internal_has_rootconfig()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rootconfig_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetConfigDumpResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetConfigDumpResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetConfigDumpResponse::GetClassData() const { return &_class_data_; }

void GetConfigDumpResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetConfigDumpResponse *>(to)->MergeFrom(
      static_cast<const GetConfigDumpResponse &>(from));
}


void GetConfigDumpResponse::MergeFrom(const GetConfigDumpResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetConfigDumpResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_rootconfig()) {
    _internal_mutable_rootconfig()->::carbon::frontend::weeding_diagnostics::ConfigNodeSnapshot::MergeFrom(from._internal_rootconfig());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetConfigDumpResponse::CopyFrom(const GetConfigDumpResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetConfigDumpResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetConfigDumpResponse::IsInitialized() const {
  return true;
}

void GetConfigDumpResponse::InternalSwap(GetConfigDumpResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(rootconfig_, other->rootconfig_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetConfigDumpResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[16]);
}

// ===================================================================

class GetActiveJobMetricsResponse::_Internal {
 public:
  static const ::metrics_aggregator::Metrics& jobmetrics(const GetActiveJobMetricsResponse* msg);
};

const ::metrics_aggregator::Metrics&
GetActiveJobMetricsResponse::_Internal::jobmetrics(const GetActiveJobMetricsResponse* msg) {
  return *msg->jobmetrics_;
}
void GetActiveJobMetricsResponse::clear_jobmetrics() {
  if (GetArenaForAllocation() == nullptr && jobmetrics_ != nullptr) {
    delete jobmetrics_;
  }
  jobmetrics_ = nullptr;
}
GetActiveJobMetricsResponse::GetActiveJobMetricsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetActiveJobMetricsResponse)
}
GetActiveJobMetricsResponse::GetActiveJobMetricsResponse(const GetActiveJobMetricsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_jobmetrics()) {
    jobmetrics_ = new ::metrics_aggregator::Metrics(*from.jobmetrics_);
  } else {
    jobmetrics_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetActiveJobMetricsResponse)
}

inline void GetActiveJobMetricsResponse::SharedCtor() {
jobmetrics_ = nullptr;
}

GetActiveJobMetricsResponse::~GetActiveJobMetricsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetActiveJobMetricsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetActiveJobMetricsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete jobmetrics_;
}

void GetActiveJobMetricsResponse::ArenaDtor(void* object) {
  GetActiveJobMetricsResponse* _this = reinterpret_cast< GetActiveJobMetricsResponse* >(object);
  (void)_this;
}
void GetActiveJobMetricsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetActiveJobMetricsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetActiveJobMetricsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetActiveJobMetricsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && jobmetrics_ != nullptr) {
    delete jobmetrics_;
  }
  jobmetrics_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetActiveJobMetricsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .metrics_aggregator.Metrics jobMetrics = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_jobmetrics(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetActiveJobMetricsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetActiveJobMetricsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .metrics_aggregator.Metrics jobMetrics = 1;
  if (this->_internal_has_jobmetrics()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::jobmetrics(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetActiveJobMetricsResponse)
  return target;
}

size_t GetActiveJobMetricsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetActiveJobMetricsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .metrics_aggregator.Metrics jobMetrics = 1;
  if (this->_internal_has_jobmetrics()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *jobmetrics_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetActiveJobMetricsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetActiveJobMetricsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetActiveJobMetricsResponse::GetClassData() const { return &_class_data_; }

void GetActiveJobMetricsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetActiveJobMetricsResponse *>(to)->MergeFrom(
      static_cast<const GetActiveJobMetricsResponse &>(from));
}


void GetActiveJobMetricsResponse::MergeFrom(const GetActiveJobMetricsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetActiveJobMetricsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_jobmetrics()) {
    _internal_mutable_jobmetrics()->::metrics_aggregator::Metrics::MergeFrom(from._internal_jobmetrics());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetActiveJobMetricsResponse::CopyFrom(const GetActiveJobMetricsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetActiveJobMetricsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetActiveJobMetricsResponse::IsInitialized() const {
  return true;
}

void GetActiveJobMetricsResponse::InternalSwap(GetActiveJobMetricsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(jobmetrics_, other->jobmetrics_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetActiveJobMetricsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[17]);
}

// ===================================================================

class DeleteJobRequest::_Internal {
 public:
};

DeleteJobRequest::DeleteJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.DeleteJobRequest)
}
DeleteJobRequest::DeleteJobRequest(const DeleteJobRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.DeleteJobRequest)
}

inline void DeleteJobRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DeleteJobRequest::~DeleteJobRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.DeleteJobRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeleteJobRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeleteJobRequest::ArenaDtor(void* object) {
  DeleteJobRequest* _this = reinterpret_cast< DeleteJobRequest* >(object);
  (void)_this;
}
void DeleteJobRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeleteJobRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeleteJobRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.DeleteJobRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeleteJobRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.DeleteJobRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeleteJobRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.DeleteJobRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.DeleteJobRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.DeleteJobRequest)
  return target;
}

size_t DeleteJobRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.DeleteJobRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteJobRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeleteJobRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteJobRequest::GetClassData() const { return &_class_data_; }

void DeleteJobRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeleteJobRequest *>(to)->MergeFrom(
      static_cast<const DeleteJobRequest &>(from));
}


void DeleteJobRequest::MergeFrom(const DeleteJobRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.DeleteJobRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeleteJobRequest::CopyFrom(const DeleteJobRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.DeleteJobRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteJobRequest::IsInitialized() const {
  return true;
}

void DeleteJobRequest::InternalSwap(DeleteJobRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DeleteJobRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[18]);
}

// ===================================================================

class MarkJobCompletedRequest::_Internal {
 public:
};

MarkJobCompletedRequest::MarkJobCompletedRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.MarkJobCompletedRequest)
}
MarkJobCompletedRequest::MarkJobCompletedRequest(const MarkJobCompletedRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.MarkJobCompletedRequest)
}

inline void MarkJobCompletedRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

MarkJobCompletedRequest::~MarkJobCompletedRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.MarkJobCompletedRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MarkJobCompletedRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MarkJobCompletedRequest::ArenaDtor(void* object) {
  MarkJobCompletedRequest* _this = reinterpret_cast< MarkJobCompletedRequest* >(object);
  (void)_this;
}
void MarkJobCompletedRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MarkJobCompletedRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MarkJobCompletedRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.MarkJobCompletedRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MarkJobCompletedRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.MarkJobCompletedRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MarkJobCompletedRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.MarkJobCompletedRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.MarkJobCompletedRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.MarkJobCompletedRequest)
  return target;
}

size_t MarkJobCompletedRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.MarkJobCompletedRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MarkJobCompletedRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MarkJobCompletedRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MarkJobCompletedRequest::GetClassData() const { return &_class_data_; }

void MarkJobCompletedRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MarkJobCompletedRequest *>(to)->MergeFrom(
      static_cast<const MarkJobCompletedRequest &>(from));
}


void MarkJobCompletedRequest::MergeFrom(const MarkJobCompletedRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.MarkJobCompletedRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MarkJobCompletedRequest::CopyFrom(const MarkJobCompletedRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.MarkJobCompletedRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MarkJobCompletedRequest::IsInitialized() const {
  return true;
}

void MarkJobCompletedRequest::InternalSwap(MarkJobCompletedRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata MarkJobCompletedRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[19]);
}

// ===================================================================

class MarkJobIncompleteRequest::_Internal {
 public:
};

MarkJobIncompleteRequest::MarkJobIncompleteRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.MarkJobIncompleteRequest)
}
MarkJobIncompleteRequest::MarkJobIncompleteRequest(const MarkJobIncompleteRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.MarkJobIncompleteRequest)
}

inline void MarkJobIncompleteRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

MarkJobIncompleteRequest::~MarkJobIncompleteRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.MarkJobIncompleteRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MarkJobIncompleteRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MarkJobIncompleteRequest::ArenaDtor(void* object) {
  MarkJobIncompleteRequest* _this = reinterpret_cast< MarkJobIncompleteRequest* >(object);
  (void)_this;
}
void MarkJobIncompleteRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MarkJobIncompleteRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MarkJobIncompleteRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.MarkJobIncompleteRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MarkJobIncompleteRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string jobId = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.MarkJobIncompleteRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MarkJobIncompleteRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.MarkJobIncompleteRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.MarkJobIncompleteRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_jobid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.MarkJobIncompleteRequest)
  return target;
}

size_t MarkJobIncompleteRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.MarkJobIncompleteRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 1;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MarkJobIncompleteRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MarkJobIncompleteRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MarkJobIncompleteRequest::GetClassData() const { return &_class_data_; }

void MarkJobIncompleteRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MarkJobIncompleteRequest *>(to)->MergeFrom(
      static_cast<const MarkJobIncompleteRequest &>(from));
}


void MarkJobIncompleteRequest::MergeFrom(const MarkJobIncompleteRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.MarkJobIncompleteRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MarkJobIncompleteRequest::CopyFrom(const MarkJobIncompleteRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.MarkJobIncompleteRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MarkJobIncompleteRequest::IsInitialized() const {
  return true;
}

void MarkJobIncompleteRequest::InternalSwap(MarkJobIncompleteRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata MarkJobIncompleteRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[20]);
}

// ===================================================================

class GetNextJobRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextJobRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextJobRequest::_Internal::ts(const GetNextJobRequest* msg) {
  return *msg->ts_;
}
void GetNextJobRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextJobRequest::GetNextJobRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetNextJobRequest)
}
GetNextJobRequest::GetNextJobRequest(const GetNextJobRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_jobid().empty()) {
    jobid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_jobid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetNextJobRequest)
}

inline void GetNextJobRequest::SharedCtor() {
jobid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  jobid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextJobRequest::~GetNextJobRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetNextJobRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextJobRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  jobid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextJobRequest::ArenaDtor(void* object) {
  GetNextJobRequest* _this = reinterpret_cast< GetNextJobRequest* >(object);
  (void)_this;
}
void GetNextJobRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextJobRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextJobRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetNextJobRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  jobid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextJobRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string jobId = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_jobid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.jobs.GetNextJobRequest.jobId"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextJobRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetNextJobRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string jobId = 2;
  if (!this->_internal_jobid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_jobid().data(), static_cast<int>(this->_internal_jobid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.jobs.GetNextJobRequest.jobId");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_jobid(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetNextJobRequest)
  return target;
}

size_t GetNextJobRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetNextJobRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string jobId = 2;
  if (!this->_internal_jobid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_jobid());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextJobRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextJobRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextJobRequest::GetClassData() const { return &_class_data_; }

void GetNextJobRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextJobRequest *>(to)->MergeFrom(
      static_cast<const GetNextJobRequest &>(from));
}


void GetNextJobRequest::MergeFrom(const GetNextJobRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetNextJobRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_jobid().empty()) {
    _internal_set_jobid(from._internal_jobid());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextJobRequest::CopyFrom(const GetNextJobRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetNextJobRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextJobRequest::IsInitialized() const {
  return true;
}

void GetNextJobRequest::InternalSwap(GetNextJobRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &jobid_, lhs_arena,
      &other->jobid_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextJobRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[21]);
}

// ===================================================================

class GetNextJobResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextJobResponse* msg);
  static const ::carbon::frontend::jobs::JobWithMetrics& job(const GetNextJobResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextJobResponse::_Internal::ts(const GetNextJobResponse* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::jobs::JobWithMetrics&
GetNextJobResponse::_Internal::job(const GetNextJobResponse* msg) {
  return *msg->job_;
}
void GetNextJobResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextJobResponse::GetNextJobResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.jobs.GetNextJobResponse)
}
GetNextJobResponse::GetNextJobResponse(const GetNextJobResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_job()) {
    job_ = new ::carbon::frontend::jobs::JobWithMetrics(*from.job_);
  } else {
    job_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.jobs.GetNextJobResponse)
}

inline void GetNextJobResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&job_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(job_));
}

GetNextJobResponse::~GetNextJobResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.jobs.GetNextJobResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextJobResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete job_;
}

void GetNextJobResponse::ArenaDtor(void* object) {
  GetNextJobResponse* _this = reinterpret_cast< GetNextJobResponse* >(object);
  (void)_this;
}
void GetNextJobResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextJobResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextJobResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.jobs.GetNextJobResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && job_ != nullptr) {
    delete job_;
  }
  job_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextJobResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.jobs.JobWithMetrics job = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_job(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextJobResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.jobs.GetNextJobResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.jobs.JobWithMetrics job = 2;
  if (this->_internal_has_job()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::job(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.jobs.GetNextJobResponse)
  return target;
}

size_t GetNextJobResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.jobs.GetNextJobResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.jobs.JobWithMetrics job = 2;
  if (this->_internal_has_job()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *job_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextJobResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextJobResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextJobResponse::GetClassData() const { return &_class_data_; }

void GetNextJobResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextJobResponse *>(to)->MergeFrom(
      static_cast<const GetNextJobResponse &>(from));
}


void GetNextJobResponse::MergeFrom(const GetNextJobResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.jobs.GetNextJobResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_job()) {
    _internal_mutable_job()->::carbon::frontend::jobs::JobWithMetrics::MergeFrom(from._internal_job());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextJobResponse::CopyFrom(const GetNextJobResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.jobs.GetNextJobResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextJobResponse::IsInitialized() const {
  return true;
}

void GetNextJobResponse::InternalSwap(GetNextJobResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextJobResponse, job_)
      + sizeof(GetNextJobResponse::job_)
      - PROTOBUF_FIELD_OFFSET(GetNextJobResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextJobResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fjobs_2eproto_getter, &descriptor_table_frontend_2fproto_2fjobs_2eproto_once,
      file_level_metadata_frontend_2fproto_2fjobs_2eproto[22]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace jobs
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::JobDescription* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::JobDescription >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::JobDescription >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::ActiveProfile* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::ActiveProfile >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::ActiveProfile >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::Job_ActiveProfilesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::Job* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::Job >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::Job >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::CreateJobRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::CreateJobRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::CreateJobRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::CreateJobResponse* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::CreateJobResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::CreateJobResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::UpdateJobRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::UpdateJobRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::UpdateJobRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetNextJobsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetNextJobsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetNextJobsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::JobWithMetrics* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::JobWithMetrics >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::JobWithMetrics >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetNextJobsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetNextJobsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetNextJobsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetNextActiveJobIdRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetNextActiveJobIdRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetNextActiveJobIdRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetNextActiveJobIdResponse* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetNextActiveJobIdResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetNextActiveJobIdResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetJobRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetJobRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetJobRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetJobResponse* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetJobResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetJobResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::StartJobRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::StartJobRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::StartJobRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetConfigDumpRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetConfigDumpRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetConfigDumpRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetConfigDumpResponse* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetConfigDumpResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetConfigDumpResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetActiveJobMetricsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetActiveJobMetricsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetActiveJobMetricsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::DeleteJobRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::DeleteJobRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::DeleteJobRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::MarkJobCompletedRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::MarkJobCompletedRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::MarkJobCompletedRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::MarkJobIncompleteRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::MarkJobIncompleteRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::MarkJobIncompleteRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetNextJobRequest* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetNextJobRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetNextJobRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::jobs::GetNextJobResponse* Arena::CreateMaybeMessage< ::carbon::frontend::jobs::GetNextJobResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::jobs::GetNextJobResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class TractorIfState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    expected: builtin___bool = ...
    connected: builtin___bool = ...

    def __init__(self,
        *,
        expected : typing___Optional[builtin___bool] = None,
        connected : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"connected",b"connected",u"expected",b"expected"]) -> None: ...
type___TractorIfState = TractorIfState

class TractorSafetyState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    is_safe: builtin___bool = ...
    enforced: builtin___bool = ...

    def __init__(self,
        *,
        is_safe : typing___Optional[builtin___bool] = None,
        enforced : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enforced",b"enforced",u"is_safe",b"is_safe"]) -> None: ...
type___TractorSafetyState = TractorSafetyState

class GetNextTractorIfStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def state(self) -> type___TractorIfState: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        state : typing___Optional[type___TractorIfState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"state",b"state",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"state",b"state",u"ts",b"ts"]) -> None: ...
type___GetNextTractorIfStateResponse = GetNextTractorIfStateResponse

class GetNextTractorSafetyStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def state(self) -> type___TractorSafetyState: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        state : typing___Optional[type___TractorSafetyState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"state",b"state",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"state",b"state",u"ts",b"ts"]) -> None: ...
type___GetNextTractorSafetyStateResponse = GetNextTractorSafetyStateResponse

class SetEnforcementPolicyRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enforced: builtin___bool = ...

    def __init__(self,
        *,
        enforced : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enforced",b"enforced"]) -> None: ...
type___SetEnforcementPolicyRequest = SetEnforcementPolicyRequest

class SetEnforcementPolicyResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetEnforcementPolicyResponse = SetEnforcementPolicyResponse

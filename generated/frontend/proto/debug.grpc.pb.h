// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/debug.proto
#ifndef GRPC_frontend_2fproto_2fdebug_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fdebug_2eproto__INCLUDED

#include "frontend/proto/debug.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace debug {

class DebugService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.debug.DebugService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::debug::RobotMessage* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::debug::RobotMessage>> AsyncGetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::debug::RobotMessage>>(AsyncGetRobotRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::debug::RobotMessage>> PrepareAsyncGetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::debug::RobotMessage>>(PrepareAsyncGetRobotRaw(context, request, cq));
    }
    virtual ::grpc::Status SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetLogLevelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetLogLevelRaw(context, request, cq));
    }
    virtual ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncStartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncStartSavingCropLineDetectionReplayRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncStartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncStartSavingCropLineDetectionReplayRaw(context, request, cq));
    }
    virtual ::grpc::Status StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::weed_tracking::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> AsyncStartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(AsyncStartRecordingAimbotInputsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>> PrepareAsyncStartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>>(PrepareAsyncStartRecordingAimbotInputsRaw(context, request, cq));
    }
    virtual ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncAddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncAddMockSpatialMetricsBlockRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncAddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncAddMockSpatialMetricsBlockRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDeleteProfileSyncDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteProfileSyncDataRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::debug::RobotMessage>* AsyncGetRobotRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::debug::RobotMessage>* PrepareAsyncGetRobotRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetLogLevelRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetLogLevelRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* AsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::weed_tracking::Empty>* PrepareAsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncAddMockSpatialMetricsBlockRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncAddMockSpatialMetricsBlockRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDeleteProfileSyncDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteProfileSyncDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::debug::RobotMessage* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::RobotMessage>> AsyncGetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::RobotMessage>>(AsyncGetRobotRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::RobotMessage>> PrepareAsyncGetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::RobotMessage>>(PrepareAsyncGetRobotRaw(context, request, cq));
    }
    ::grpc::Status SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetLogLevelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetLogLevelRaw(context, request, cq));
    }
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncStartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncStartSavingCropLineDetectionReplayRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncStartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncStartSavingCropLineDetectionReplayRaw(context, request, cq));
    }
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::weed_tracking::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> AsyncStartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(AsyncStartRecordingAimbotInputsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>> PrepareAsyncStartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>>(PrepareAsyncStartRecordingAimbotInputsRaw(context, request, cq));
    }
    ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncAddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncAddMockSpatialMetricsBlockRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncAddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncAddMockSpatialMetricsBlockRaw(context, request, cq));
    }
    ::grpc::Status DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDeleteProfileSyncDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteProfileSyncDataRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response, std::function<void(::grpc::Status)>) override;
      void GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::RobotMessage>* AsyncGetRobotRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::RobotMessage>* PrepareAsyncGetRobotRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetLogLevelRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetLogLevelRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* AsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* PrepareAsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncAddMockSpatialMetricsBlockRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncAddMockSpatialMetricsBlockRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDeleteProfileSyncDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteProfileSyncDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetRobot_;
    const ::grpc::internal::RpcMethod rpcmethod_SetLogLevel_;
    const ::grpc::internal::RpcMethod rpcmethod_StartSavingCropLineDetectionReplay_;
    const ::grpc::internal::RpcMethod rpcmethod_StartRecordingAimbotInputs_;
    const ::grpc::internal::RpcMethod rpcmethod_AddMockSpatialMetricsBlock_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteProfileSyncData_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetRobot(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response);
    virtual ::grpc::Status SetLogLevel(::grpc::ServerContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response);
    virtual ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status DeleteProfileSyncData(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetRobot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetRobot() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetRobot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::debug::RobotMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRobot(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::debug::RobotMessage>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetLogLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetLogLevel() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SetLogLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLogLevel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::debug::SetLogLevelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLogLevel(::grpc::ServerContext* context, ::carbon::frontend::debug::SetLogLevelRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartSavingCropLineDetectionReplay(::grpc::ServerContext* context, ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartRecordingAimbotInputs(::grpc::ServerContext* context, ::weed_tracking::RecordAimbotInputRequest* request, ::grpc::ServerAsyncResponseWriter< ::weed_tracking::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_AddMockSpatialMetricsBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_AddMockSpatialMetricsBlock() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_AddMockSpatialMetricsBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAddMockSpatialMetricsBlock(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteProfileSyncData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteProfileSyncData() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_DeleteProfileSyncData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfileSyncData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteProfileSyncData(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetRobot<WithAsyncMethod_SetLogLevel<WithAsyncMethod_StartSavingCropLineDetectionReplay<WithAsyncMethod_StartRecordingAimbotInputs<WithAsyncMethod_AddMockSpatialMetricsBlock<WithAsyncMethod_DeleteProfileSyncData<Service > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetRobot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetRobot() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::debug::RobotMessage>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response) { return this->GetRobot(context, request, response); }));}
    void SetMessageAllocatorFor_GetRobot(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::debug::RobotMessage>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::debug::RobotMessage>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetRobot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::debug::RobotMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRobot(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::debug::RobotMessage* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetLogLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetLogLevel() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::debug::SetLogLevelRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetLogLevel(context, request, response); }));}
    void SetMessageAllocatorFor_SetLogLevel(
        ::grpc::MessageAllocator< ::carbon::frontend::debug::SetLogLevelRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::debug::SetLogLevelRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetLogLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLogLevel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::debug::SetLogLevelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLogLevel(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::debug::SetLogLevelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response) { return this->StartSavingCropLineDetectionReplay(context, request, response); }));}
    void SetMessageAllocatorFor_StartSavingCropLineDetectionReplay(
        ::grpc::MessageAllocator< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartSavingCropLineDetectionReplay(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response) { return this->StartRecordingAimbotInputs(context, request, response); }));}
    void SetMessageAllocatorFor_StartRecordingAimbotInputs(
        ::grpc::MessageAllocator< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartRecordingAimbotInputs(
      ::grpc::CallbackServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_AddMockSpatialMetricsBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_AddMockSpatialMetricsBlock() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->AddMockSpatialMetricsBlock(context, request, response); }));}
    void SetMessageAllocatorFor_AddMockSpatialMetricsBlock(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_AddMockSpatialMetricsBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AddMockSpatialMetricsBlock(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteProfileSyncData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteProfileSyncData() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->DeleteProfileSyncData(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteProfileSyncData(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteProfileSyncData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfileSyncData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteProfileSyncData(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetRobot<WithCallbackMethod_SetLogLevel<WithCallbackMethod_StartSavingCropLineDetectionReplay<WithCallbackMethod_StartRecordingAimbotInputs<WithCallbackMethod_AddMockSpatialMetricsBlock<WithCallbackMethod_DeleteProfileSyncData<Service > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetRobot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetRobot() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetRobot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::debug::RobotMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetLogLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetLogLevel() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SetLogLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLogLevel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::debug::SetLogLevelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_AddMockSpatialMetricsBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_AddMockSpatialMetricsBlock() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_AddMockSpatialMetricsBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteProfileSyncData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteProfileSyncData() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_DeleteProfileSyncData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfileSyncData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetRobot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetRobot() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetRobot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::debug::RobotMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRobot(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetLogLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetLogLevel() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SetLogLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLogLevel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::debug::SetLogLevelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLogLevel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartSavingCropLineDetectionReplay(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartRecordingAimbotInputs(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_AddMockSpatialMetricsBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_AddMockSpatialMetricsBlock() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_AddMockSpatialMetricsBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAddMockSpatialMetricsBlock(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteProfileSyncData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteProfileSyncData() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_DeleteProfileSyncData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfileSyncData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteProfileSyncData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetRobot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetRobot() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetRobot(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetRobot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::debug::RobotMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRobot(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetLogLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetLogLevel() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetLogLevel(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetLogLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLogLevel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::debug::SetLogLevelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLogLevel(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartSavingCropLineDetectionReplay(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartSavingCropLineDetectionReplay(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartRecordingAimbotInputs(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartRecordingAimbotInputs(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_AddMockSpatialMetricsBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_AddMockSpatialMetricsBlock() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->AddMockSpatialMetricsBlock(context, request, response); }));
    }
    ~WithRawCallbackMethod_AddMockSpatialMetricsBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AddMockSpatialMetricsBlock(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteProfileSyncData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteProfileSyncData() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteProfileSyncData(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteProfileSyncData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfileSyncData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteProfileSyncData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetRobot : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetRobot() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::debug::RobotMessage>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::debug::RobotMessage>* streamer) {
                       return this->StreamedGetRobot(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetRobot() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetRobot(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::debug::RobotMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetRobot(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::debug::RobotMessage>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetLogLevel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetLogLevel() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::debug::SetLogLevelRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::debug::SetLogLevelRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetLogLevel(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetLogLevel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetLogLevel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::debug::SetLogLevelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetLogLevel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::debug::SetLogLevelRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedStartSavingCropLineDetectionReplay(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartSavingCropLineDetectionReplay(::grpc::ServerContext* /*context*/, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartSavingCropLineDetectionReplay(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::StartSavingCropLineDetectionReplayRequest,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartRecordingAimbotInputs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartRecordingAimbotInputs() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty>* streamer) {
                       return this->StreamedStartRecordingAimbotInputs(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartRecordingAimbotInputs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartRecordingAimbotInputs(::grpc::ServerContext* /*context*/, const ::weed_tracking::RecordAimbotInputRequest* /*request*/, ::weed_tracking::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartRecordingAimbotInputs(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::weed_tracking::RecordAimbotInputRequest,::weed_tracking::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_AddMockSpatialMetricsBlock : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_AddMockSpatialMetricsBlock() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedAddMockSpatialMetricsBlock(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_AddMockSpatialMetricsBlock() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status AddMockSpatialMetricsBlock(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedAddMockSpatialMetricsBlock(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteProfileSyncData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteProfileSyncData() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDeleteProfileSyncData(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteProfileSyncData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteProfileSyncData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteProfileSyncData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetRobot<WithStreamedUnaryMethod_SetLogLevel<WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay<WithStreamedUnaryMethod_StartRecordingAimbotInputs<WithStreamedUnaryMethod_AddMockSpatialMetricsBlock<WithStreamedUnaryMethod_DeleteProfileSyncData<Service > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetRobot<WithStreamedUnaryMethod_SetLogLevel<WithStreamedUnaryMethod_StartSavingCropLineDetectionReplay<WithStreamedUnaryMethod_StartRecordingAimbotInputs<WithStreamedUnaryMethod_AddMockSpatialMetricsBlock<WithStreamedUnaryMethod_DeleteProfileSyncData<Service > > > > > > StreamedService;
};

}  // namespace debug
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fdebug_2eproto__INCLUDED

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/focus.proto

#include "frontend/proto/focus.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace focus {
constexpr TargetFocusState::TargetFocusState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : focus_progress_pct_(0)
  , liquid_lens_value_(0u)
  , max_lens_value_(0u)
  , min_lens_value_(0u)
  , focus_in_progress_(false){}
struct TargetFocusStateDefaultTypeInternal {
  constexpr TargetFocusStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TargetFocusStateDefaultTypeInternal() {}
  union {
    TargetFocusState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TargetFocusStateDefaultTypeInternal _TargetFocusState_default_instance_;
constexpr PredictFocusState::PredictFocusState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct PredictFocusStateDefaultTypeInternal {
  constexpr PredictFocusStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PredictFocusStateDefaultTypeInternal() {}
  union {
    PredictFocusState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PredictFocusStateDefaultTypeInternal _PredictFocusState_default_instance_;
constexpr FocusState::FocusState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , global_focus_progress_pct_(0)
  , grid_view_enabled_(false)
  , focus_in_progress_(false)
  , _oneof_case_{}{}
struct FocusStateDefaultTypeInternal {
  constexpr FocusStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FocusStateDefaultTypeInternal() {}
  union {
    FocusState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FocusStateDefaultTypeInternal _FocusState_default_instance_;
constexpr LensSetRequest::LensSetRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , lens_value_(0u){}
struct LensSetRequestDefaultTypeInternal {
  constexpr LensSetRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LensSetRequestDefaultTypeInternal() {}
  union {
    LensSetRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LensSetRequestDefaultTypeInternal _LensSetRequest_default_instance_;
constexpr FocusStateRequest::FocusStateRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct FocusStateRequestDefaultTypeInternal {
  constexpr FocusStateRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FocusStateRequestDefaultTypeInternal() {}
  union {
    FocusStateRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FocusStateRequestDefaultTypeInternal _FocusStateRequest_default_instance_;
}  // namespace focus
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2ffocus_2eproto[5];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2ffocus_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2ffocus_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2ffocus_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::TargetFocusState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::TargetFocusState, liquid_lens_value_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::TargetFocusState, focus_progress_pct_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::TargetFocusState, max_lens_value_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::TargetFocusState, min_lens_value_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::TargetFocusState, focus_in_progress_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::PredictFocusState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusState, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusState, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusState, ts_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusState, global_focus_progress_pct_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusState, grid_view_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusState, focus_in_progress_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusState, type_state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::LensSetRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::LensSetRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::LensSetRequest, lens_value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusStateRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::focus::FocusStateRequest, ts_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::focus::TargetFocusState)},
  { 11, -1, -1, sizeof(::carbon::frontend::focus::PredictFocusState)},
  { 17, -1, -1, sizeof(::carbon::frontend::focus::FocusState)},
  { 30, -1, -1, sizeof(::carbon::frontend::focus::LensSetRequest)},
  { 38, -1, -1, sizeof(::carbon::frontend::focus::FocusStateRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::focus::_TargetFocusState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::focus::_PredictFocusState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::focus::_FocusState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::focus::_LensSetRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::focus::_FocusStateRequest_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2ffocus_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032frontend/proto/focus.proto\022\025carbon.fro"
  "ntend.focus\032\033frontend/proto/camera.proto"
  "\032\031frontend/proto/util.proto\"\224\001\n\020TargetFo"
  "cusState\022\031\n\021liquid_lens_value\030\001 \001(\r\022\032\n\022f"
  "ocus_progress_pct\030\002 \001(\001\022\026\n\016max_lens_valu"
  "e\030\003 \001(\r\022\026\n\016min_lens_value\030\004 \001(\r\022\031\n\021focus"
  "_in_progress\030\005 \001(\010\"\023\n\021PredictFocusState\""
  "\230\002\n\nFocusState\022+\n\002ts\030\001 \001(\0132\037.carbon.fron"
  "tend.util.Timestamp\0229\n\006target\030\002 \001(\0132\'.ca"
  "rbon.frontend.focus.TargetFocusStateH\000\022;"
  "\n\007predict\030\003 \001(\0132(.carbon.frontend.focus."
  "PredictFocusStateH\000\022!\n\031global_focus_prog"
  "ress_pct\030\004 \001(\001\022\031\n\021grid_view_enabled\030\005 \001("
  "\010\022\031\n\021focus_in_progress\030\006 \001(\010B\014\n\ntype_sta"
  "te\"4\n\016LensSetRequest\022\016\n\006cam_id\030\001 \001(\t\022\022\n\n"
  "lens_value\030\002 \001(\r\"P\n\021FocusStateRequest\022\016\n"
  "\006cam_id\030\001 \001(\t\022+\n\002ts\030\002 \001(\0132\037.carbon.front"
  "end.util.Timestamp2\217\004\n\014FocusService\022Q\n\025T"
  "ogglePredictGridView\022\033.carbon.frontend.u"
  "til.Empty\032\033.carbon.frontend.util.Empty\022`"
  "\n\021GetNextFocusState\022(.carbon.frontend.fo"
  "cus.FocusStateRequest\032!.carbon.frontend."
  "focus.FocusState\022\\\n\026StartAutoFocusSpecif"
  "ic\022%.carbon.frontend.camera.CameraReques"
  "t\032\033.carbon.frontend.util.Empty\022M\n\021StartA"
  "utoFocusAll\022\033.carbon.frontend.util.Empty"
  "\032\033.carbon.frontend.util.Empty\022I\n\rStopAut"
  "oFocus\022\033.carbon.frontend.util.Empty\032\033.ca"
  "rbon.frontend.util.Empty\022R\n\014SetLensValue"
  "\022%.carbon.frontend.focus.LensSetRequest\032"
  "\033.carbon.frontend.util.EmptyB\020Z\016proto/fr"
  "ontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2ffocus_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2fcamera_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2ffocus_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ffocus_2eproto = {
  false, false, 1254, descriptor_table_protodef_frontend_2fproto_2ffocus_2eproto, "frontend/proto/focus.proto", 
  &descriptor_table_frontend_2fproto_2ffocus_2eproto_once, descriptor_table_frontend_2fproto_2ffocus_2eproto_deps, 2, 5,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2ffocus_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2ffocus_2eproto, file_level_enum_descriptors_frontend_2fproto_2ffocus_2eproto, file_level_service_descriptors_frontend_2fproto_2ffocus_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2ffocus_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2ffocus_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2ffocus_2eproto(&descriptor_table_frontend_2fproto_2ffocus_2eproto);
namespace carbon {
namespace frontend {
namespace focus {

// ===================================================================

class TargetFocusState::_Internal {
 public:
};

TargetFocusState::TargetFocusState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.focus.TargetFocusState)
}
TargetFocusState::TargetFocusState(const TargetFocusState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&focus_progress_pct_, &from.focus_progress_pct_,
    static_cast<size_t>(reinterpret_cast<char*>(&focus_in_progress_) -
    reinterpret_cast<char*>(&focus_progress_pct_)) + sizeof(focus_in_progress_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.focus.TargetFocusState)
}

inline void TargetFocusState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&focus_progress_pct_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&focus_in_progress_) -
    reinterpret_cast<char*>(&focus_progress_pct_)) + sizeof(focus_in_progress_));
}

TargetFocusState::~TargetFocusState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.focus.TargetFocusState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TargetFocusState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TargetFocusState::ArenaDtor(void* object) {
  TargetFocusState* _this = reinterpret_cast< TargetFocusState* >(object);
  (void)_this;
}
void TargetFocusState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TargetFocusState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TargetFocusState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.focus.TargetFocusState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&focus_progress_pct_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&focus_in_progress_) -
      reinterpret_cast<char*>(&focus_progress_pct_)) + sizeof(focus_in_progress_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TargetFocusState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 liquid_lens_value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          liquid_lens_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double focus_progress_pct = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          focus_progress_pct_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // uint32 max_lens_value = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          max_lens_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 min_lens_value = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          min_lens_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool focus_in_progress = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          focus_in_progress_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TargetFocusState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.focus.TargetFocusState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 liquid_lens_value = 1;
  if (this->_internal_liquid_lens_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_liquid_lens_value(), target);
  }

  // double focus_progress_pct = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_focus_progress_pct = this->_internal_focus_progress_pct();
  uint64_t raw_focus_progress_pct;
  memcpy(&raw_focus_progress_pct, &tmp_focus_progress_pct, sizeof(tmp_focus_progress_pct));
  if (raw_focus_progress_pct != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_focus_progress_pct(), target);
  }

  // uint32 max_lens_value = 3;
  if (this->_internal_max_lens_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_max_lens_value(), target);
  }

  // uint32 min_lens_value = 4;
  if (this->_internal_min_lens_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_min_lens_value(), target);
  }

  // bool focus_in_progress = 5;
  if (this->_internal_focus_in_progress() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_focus_in_progress(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.focus.TargetFocusState)
  return target;
}

size_t TargetFocusState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.focus.TargetFocusState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double focus_progress_pct = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_focus_progress_pct = this->_internal_focus_progress_pct();
  uint64_t raw_focus_progress_pct;
  memcpy(&raw_focus_progress_pct, &tmp_focus_progress_pct, sizeof(tmp_focus_progress_pct));
  if (raw_focus_progress_pct != 0) {
    total_size += 1 + 8;
  }

  // uint32 liquid_lens_value = 1;
  if (this->_internal_liquid_lens_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_liquid_lens_value());
  }

  // uint32 max_lens_value = 3;
  if (this->_internal_max_lens_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_max_lens_value());
  }

  // uint32 min_lens_value = 4;
  if (this->_internal_min_lens_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_min_lens_value());
  }

  // bool focus_in_progress = 5;
  if (this->_internal_focus_in_progress() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TargetFocusState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TargetFocusState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TargetFocusState::GetClassData() const { return &_class_data_; }

void TargetFocusState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TargetFocusState *>(to)->MergeFrom(
      static_cast<const TargetFocusState &>(from));
}


void TargetFocusState::MergeFrom(const TargetFocusState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.focus.TargetFocusState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_focus_progress_pct = from._internal_focus_progress_pct();
  uint64_t raw_focus_progress_pct;
  memcpy(&raw_focus_progress_pct, &tmp_focus_progress_pct, sizeof(tmp_focus_progress_pct));
  if (raw_focus_progress_pct != 0) {
    _internal_set_focus_progress_pct(from._internal_focus_progress_pct());
  }
  if (from._internal_liquid_lens_value() != 0) {
    _internal_set_liquid_lens_value(from._internal_liquid_lens_value());
  }
  if (from._internal_max_lens_value() != 0) {
    _internal_set_max_lens_value(from._internal_max_lens_value());
  }
  if (from._internal_min_lens_value() != 0) {
    _internal_set_min_lens_value(from._internal_min_lens_value());
  }
  if (from._internal_focus_in_progress() != 0) {
    _internal_set_focus_in_progress(from._internal_focus_in_progress());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TargetFocusState::CopyFrom(const TargetFocusState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.focus.TargetFocusState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TargetFocusState::IsInitialized() const {
  return true;
}

void TargetFocusState::InternalSwap(TargetFocusState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TargetFocusState, focus_in_progress_)
      + sizeof(TargetFocusState::focus_in_progress_)
      - PROTOBUF_FIELD_OFFSET(TargetFocusState, focus_progress_pct_)>(
          reinterpret_cast<char*>(&focus_progress_pct_),
          reinterpret_cast<char*>(&other->focus_progress_pct_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TargetFocusState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ffocus_2eproto_getter, &descriptor_table_frontend_2fproto_2ffocus_2eproto_once,
      file_level_metadata_frontend_2fproto_2ffocus_2eproto[0]);
}

// ===================================================================

class PredictFocusState::_Internal {
 public:
};

PredictFocusState::PredictFocusState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.focus.PredictFocusState)
}
PredictFocusState::PredictFocusState(const PredictFocusState& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.focus.PredictFocusState)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PredictFocusState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PredictFocusState::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata PredictFocusState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ffocus_2eproto_getter, &descriptor_table_frontend_2fproto_2ffocus_2eproto_once,
      file_level_metadata_frontend_2fproto_2ffocus_2eproto[1]);
}

// ===================================================================

class FocusState::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const FocusState* msg);
  static const ::carbon::frontend::focus::TargetFocusState& target(const FocusState* msg);
  static const ::carbon::frontend::focus::PredictFocusState& predict(const FocusState* msg);
};

const ::carbon::frontend::util::Timestamp&
FocusState::_Internal::ts(const FocusState* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::focus::TargetFocusState&
FocusState::_Internal::target(const FocusState* msg) {
  return *msg->type_state_.target_;
}
const ::carbon::frontend::focus::PredictFocusState&
FocusState::_Internal::predict(const FocusState* msg) {
  return *msg->type_state_.predict_;
}
void FocusState::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void FocusState::set_allocated_target(::carbon::frontend::focus::TargetFocusState* target) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type_state();
  if (target) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::focus::TargetFocusState>::GetOwningArena(target);
    if (message_arena != submessage_arena) {
      target = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target, submessage_arena);
    }
    set_has_target();
    type_state_.target_ = target;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.focus.FocusState.target)
}
void FocusState::set_allocated_predict(::carbon::frontend::focus::PredictFocusState* predict) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_type_state();
  if (predict) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::focus::PredictFocusState>::GetOwningArena(predict);
    if (message_arena != submessage_arena) {
      predict = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, predict, submessage_arena);
    }
    set_has_predict();
    type_state_.predict_ = predict;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.focus.FocusState.predict)
}
FocusState::FocusState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.focus.FocusState)
}
FocusState::FocusState(const FocusState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&global_focus_progress_pct_, &from.global_focus_progress_pct_,
    static_cast<size_t>(reinterpret_cast<char*>(&focus_in_progress_) -
    reinterpret_cast<char*>(&global_focus_progress_pct_)) + sizeof(focus_in_progress_));
  clear_has_type_state();
  switch (from.type_state_case()) {
    case kTarget: {
      _internal_mutable_target()->::carbon::frontend::focus::TargetFocusState::MergeFrom(from._internal_target());
      break;
    }
    case kPredict: {
      _internal_mutable_predict()->::carbon::frontend::focus::PredictFocusState::MergeFrom(from._internal_predict());
      break;
    }
    case TYPE_STATE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.focus.FocusState)
}

inline void FocusState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&focus_in_progress_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(focus_in_progress_));
clear_has_type_state();
}

FocusState::~FocusState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.focus.FocusState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FocusState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (has_type_state()) {
    clear_type_state();
  }
}

void FocusState::ArenaDtor(void* object) {
  FocusState* _this = reinterpret_cast< FocusState* >(object);
  (void)_this;
}
void FocusState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FocusState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FocusState::clear_type_state() {
// @@protoc_insertion_point(one_of_clear_start:carbon.frontend.focus.FocusState)
  switch (type_state_case()) {
    case kTarget: {
      if (GetArenaForAllocation() == nullptr) {
        delete type_state_.target_;
      }
      break;
    }
    case kPredict: {
      if (GetArenaForAllocation() == nullptr) {
        delete type_state_.predict_;
      }
      break;
    }
    case TYPE_STATE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = TYPE_STATE_NOT_SET;
}


void FocusState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.focus.FocusState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&global_focus_progress_pct_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&focus_in_progress_) -
      reinterpret_cast<char*>(&global_focus_progress_pct_)) + sizeof(focus_in_progress_));
  clear_type_state();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FocusState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.focus.TargetFocusState target = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_target(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.focus.PredictFocusState predict = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_predict(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double global_focus_progress_pct = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          global_focus_progress_pct_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // bool grid_view_enabled = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          grid_view_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool focus_in_progress = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          focus_in_progress_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FocusState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.focus.FocusState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.focus.TargetFocusState target = 2;
  if (_internal_has_target()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::target(this), target, stream);
  }

  // .carbon.frontend.focus.PredictFocusState predict = 3;
  if (_internal_has_predict()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::predict(this), target, stream);
  }

  // double global_focus_progress_pct = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_global_focus_progress_pct = this->_internal_global_focus_progress_pct();
  uint64_t raw_global_focus_progress_pct;
  memcpy(&raw_global_focus_progress_pct, &tmp_global_focus_progress_pct, sizeof(tmp_global_focus_progress_pct));
  if (raw_global_focus_progress_pct != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_global_focus_progress_pct(), target);
  }

  // bool grid_view_enabled = 5;
  if (this->_internal_grid_view_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_grid_view_enabled(), target);
  }

  // bool focus_in_progress = 6;
  if (this->_internal_focus_in_progress() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_focus_in_progress(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.focus.FocusState)
  return target;
}

size_t FocusState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.focus.FocusState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // double global_focus_progress_pct = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_global_focus_progress_pct = this->_internal_global_focus_progress_pct();
  uint64_t raw_global_focus_progress_pct;
  memcpy(&raw_global_focus_progress_pct, &tmp_global_focus_progress_pct, sizeof(tmp_global_focus_progress_pct));
  if (raw_global_focus_progress_pct != 0) {
    total_size += 1 + 8;
  }

  // bool grid_view_enabled = 5;
  if (this->_internal_grid_view_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool focus_in_progress = 6;
  if (this->_internal_focus_in_progress() != 0) {
    total_size += 1 + 1;
  }

  switch (type_state_case()) {
    // .carbon.frontend.focus.TargetFocusState target = 2;
    case kTarget: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_state_.target_);
      break;
    }
    // .carbon.frontend.focus.PredictFocusState predict = 3;
    case kPredict: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *type_state_.predict_);
      break;
    }
    case TYPE_STATE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FocusState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FocusState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FocusState::GetClassData() const { return &_class_data_; }

void FocusState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FocusState *>(to)->MergeFrom(
      static_cast<const FocusState &>(from));
}


void FocusState::MergeFrom(const FocusState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.focus.FocusState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_global_focus_progress_pct = from._internal_global_focus_progress_pct();
  uint64_t raw_global_focus_progress_pct;
  memcpy(&raw_global_focus_progress_pct, &tmp_global_focus_progress_pct, sizeof(tmp_global_focus_progress_pct));
  if (raw_global_focus_progress_pct != 0) {
    _internal_set_global_focus_progress_pct(from._internal_global_focus_progress_pct());
  }
  if (from._internal_grid_view_enabled() != 0) {
    _internal_set_grid_view_enabled(from._internal_grid_view_enabled());
  }
  if (from._internal_focus_in_progress() != 0) {
    _internal_set_focus_in_progress(from._internal_focus_in_progress());
  }
  switch (from.type_state_case()) {
    case kTarget: {
      _internal_mutable_target()->::carbon::frontend::focus::TargetFocusState::MergeFrom(from._internal_target());
      break;
    }
    case kPredict: {
      _internal_mutable_predict()->::carbon::frontend::focus::PredictFocusState::MergeFrom(from._internal_predict());
      break;
    }
    case TYPE_STATE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FocusState::CopyFrom(const FocusState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.focus.FocusState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FocusState::IsInitialized() const {
  return true;
}

void FocusState::InternalSwap(FocusState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FocusState, focus_in_progress_)
      + sizeof(FocusState::focus_in_progress_)
      - PROTOBUF_FIELD_OFFSET(FocusState, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
  swap(type_state_, other->type_state_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata FocusState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ffocus_2eproto_getter, &descriptor_table_frontend_2fproto_2ffocus_2eproto_once,
      file_level_metadata_frontend_2fproto_2ffocus_2eproto[2]);
}

// ===================================================================

class LensSetRequest::_Internal {
 public:
};

LensSetRequest::LensSetRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.focus.LensSetRequest)
}
LensSetRequest::LensSetRequest(const LensSetRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  lens_value_ = from.lens_value_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.focus.LensSetRequest)
}

inline void LensSetRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
lens_value_ = 0u;
}

LensSetRequest::~LensSetRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.focus.LensSetRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LensSetRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void LensSetRequest::ArenaDtor(void* object) {
  LensSetRequest* _this = reinterpret_cast< LensSetRequest* >(object);
  (void)_this;
}
void LensSetRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LensSetRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LensSetRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.focus.LensSetRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  lens_value_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LensSetRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.focus.LensSetRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 lens_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          lens_value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LensSetRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.focus.LensSetRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.focus.LensSetRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  // uint32 lens_value = 2;
  if (this->_internal_lens_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_lens_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.focus.LensSetRequest)
  return target;
}

size_t LensSetRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.focus.LensSetRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // uint32 lens_value = 2;
  if (this->_internal_lens_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_lens_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LensSetRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LensSetRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LensSetRequest::GetClassData() const { return &_class_data_; }

void LensSetRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LensSetRequest *>(to)->MergeFrom(
      static_cast<const LensSetRequest &>(from));
}


void LensSetRequest::MergeFrom(const LensSetRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.focus.LensSetRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  if (from._internal_lens_value() != 0) {
    _internal_set_lens_value(from._internal_lens_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LensSetRequest::CopyFrom(const LensSetRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.focus.LensSetRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LensSetRequest::IsInitialized() const {
  return true;
}

void LensSetRequest::InternalSwap(LensSetRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  swap(lens_value_, other->lens_value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LensSetRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ffocus_2eproto_getter, &descriptor_table_frontend_2fproto_2ffocus_2eproto_once,
      file_level_metadata_frontend_2fproto_2ffocus_2eproto[3]);
}

// ===================================================================

class FocusStateRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const FocusStateRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
FocusStateRequest::_Internal::ts(const FocusStateRequest* msg) {
  return *msg->ts_;
}
void FocusStateRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
FocusStateRequest::FocusStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.focus.FocusStateRequest)
}
FocusStateRequest::FocusStateRequest(const FocusStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.focus.FocusStateRequest)
}

inline void FocusStateRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

FocusStateRequest::~FocusStateRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.focus.FocusStateRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FocusStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void FocusStateRequest::ArenaDtor(void* object) {
  FocusStateRequest* _this = reinterpret_cast< FocusStateRequest* >(object);
  (void)_this;
}
void FocusStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FocusStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FocusStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.focus.FocusStateRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FocusStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.focus.FocusStateRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FocusStateRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.focus.FocusStateRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.focus.FocusStateRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.focus.FocusStateRequest)
  return target;
}

size_t FocusStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.focus.FocusStateRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FocusStateRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FocusStateRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FocusStateRequest::GetClassData() const { return &_class_data_; }

void FocusStateRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FocusStateRequest *>(to)->MergeFrom(
      static_cast<const FocusStateRequest &>(from));
}


void FocusStateRequest::MergeFrom(const FocusStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.focus.FocusStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FocusStateRequest::CopyFrom(const FocusStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.focus.FocusStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FocusStateRequest::IsInitialized() const {
  return true;
}

void FocusStateRequest::InternalSwap(FocusStateRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FocusStateRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ffocus_2eproto_getter, &descriptor_table_frontend_2fproto_2ffocus_2eproto_once,
      file_level_metadata_frontend_2fproto_2ffocus_2eproto[4]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace focus
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::focus::TargetFocusState* Arena::CreateMaybeMessage< ::carbon::frontend::focus::TargetFocusState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::focus::TargetFocusState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::focus::PredictFocusState* Arena::CreateMaybeMessage< ::carbon::frontend::focus::PredictFocusState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::focus::PredictFocusState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::focus::FocusState* Arena::CreateMaybeMessage< ::carbon::frontend::focus::FocusState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::focus::FocusState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::focus::LensSetRequest* Arena::CreateMaybeMessage< ::carbon::frontend::focus::LensSetRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::focus::LensSetRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::focus::FocusStateRequest* Arena::CreateMaybeMessage< ::carbon::frontend::focus::FocusStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::focus::FocusStateRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

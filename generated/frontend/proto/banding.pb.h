// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/banding.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fbanding_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fbanding_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
#include "core/controls/exterminator/controllers/aimbot/process/proto/aimbot.pb.h"
#include "weed_tracking/proto/weed_tracking.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fbanding_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fbanding_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[23]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fbanding_2eproto;
namespace carbon {
namespace frontend {
namespace banding {
class BandingDef;
struct BandingDefDefaultTypeInternal;
extern BandingDefDefaultTypeInternal _BandingDef_default_instance_;
class BandingRow;
struct BandingRowDefaultTypeInternal;
extern BandingRowDefaultTypeInternal _BandingRow_default_instance_;
class DeleteBandingDefRequest;
struct DeleteBandingDefRequestDefaultTypeInternal;
extern DeleteBandingDefRequestDefaultTypeInternal _DeleteBandingDefRequest_default_instance_;
class GetActiveBandingDefResponse;
struct GetActiveBandingDefResponseDefaultTypeInternal;
extern GetActiveBandingDefResponseDefaultTypeInternal _GetActiveBandingDefResponse_default_instance_;
class GetDimensionsRequest;
struct GetDimensionsRequestDefaultTypeInternal;
extern GetDimensionsRequestDefaultTypeInternal _GetDimensionsRequest_default_instance_;
class GetNextBandingStateResponse;
struct GetNextBandingStateResponseDefaultTypeInternal;
extern GetNextBandingStateResponseDefaultTypeInternal _GetNextBandingStateResponse_default_instance_;
class GetNextVisualizationData2Response;
struct GetNextVisualizationData2ResponseDefaultTypeInternal;
extern GetNextVisualizationData2ResponseDefaultTypeInternal _GetNextVisualizationData2Response_default_instance_;
class GetNextVisualizationDataForAllRowsRequest;
struct GetNextVisualizationDataForAllRowsRequestDefaultTypeInternal;
extern GetNextVisualizationDataForAllRowsRequestDefaultTypeInternal _GetNextVisualizationDataForAllRowsRequest_default_instance_;
class GetNextVisualizationDataForAllRowsResponse;
struct GetNextVisualizationDataForAllRowsResponseDefaultTypeInternal;
extern GetNextVisualizationDataForAllRowsResponseDefaultTypeInternal _GetNextVisualizationDataForAllRowsResponse_default_instance_;
class GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse;
struct GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUseDefaultTypeInternal;
extern GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUseDefaultTypeInternal _GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse_default_instance_;
class GetNextVisualizationDataRequest;
struct GetNextVisualizationDataRequestDefaultTypeInternal;
extern GetNextVisualizationDataRequestDefaultTypeInternal _GetNextVisualizationDataRequest_default_instance_;
class GetNextVisualizationDataResponse;
struct GetNextVisualizationDataResponseDefaultTypeInternal;
extern GetNextVisualizationDataResponseDefaultTypeInternal _GetNextVisualizationDataResponse_default_instance_;
class GetVisualizationMetadataResponse;
struct GetVisualizationMetadataResponseDefaultTypeInternal;
extern GetVisualizationMetadataResponseDefaultTypeInternal _GetVisualizationMetadataResponse_default_instance_;
class GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse;
struct GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUseDefaultTypeInternal;
extern GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUseDefaultTypeInternal _GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse_default_instance_;
class IsBandingEnabledResponse;
struct IsBandingEnabledResponseDefaultTypeInternal;
extern IsBandingEnabledResponseDefaultTypeInternal _IsBandingEnabledResponse_default_instance_;
class LoadBandingDefsResponse;
struct LoadBandingDefsResponseDefaultTypeInternal;
extern LoadBandingDefsResponseDefaultTypeInternal _LoadBandingDefsResponse_default_instance_;
class SaveBandingDefRequest;
struct SaveBandingDefRequestDefaultTypeInternal;
extern SaveBandingDefRequestDefaultTypeInternal _SaveBandingDefRequest_default_instance_;
class SetActiveBandingDefRequest;
struct SetActiveBandingDefRequestDefaultTypeInternal;
extern SetActiveBandingDefRequestDefaultTypeInternal _SetActiveBandingDefRequest_default_instance_;
class SetBandingEnabledRequest;
struct SetBandingEnabledRequestDefaultTypeInternal;
extern SetBandingEnabledRequestDefaultTypeInternal _SetBandingEnabledRequest_default_instance_;
class SetBandingEnabledResponse;
struct SetBandingEnabledResponseDefaultTypeInternal;
extern SetBandingEnabledResponseDefaultTypeInternal _SetBandingEnabledResponse_default_instance_;
class ThresholdFilter;
struct ThresholdFilterDefaultTypeInternal;
extern ThresholdFilterDefaultTypeInternal _ThresholdFilter_default_instance_;
class ThresholdFilters;
struct ThresholdFiltersDefaultTypeInternal;
extern ThresholdFiltersDefaultTypeInternal _ThresholdFilters_default_instance_;
class VisualizationData;
struct VisualizationDataDefaultTypeInternal;
extern VisualizationDataDefaultTypeInternal _VisualizationData_default_instance_;
}  // namespace banding
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::banding::BandingDef* Arena::CreateMaybeMessage<::carbon::frontend::banding::BandingDef>(Arena*);
template<> ::carbon::frontend::banding::BandingRow* Arena::CreateMaybeMessage<::carbon::frontend::banding::BandingRow>(Arena*);
template<> ::carbon::frontend::banding::DeleteBandingDefRequest* Arena::CreateMaybeMessage<::carbon::frontend::banding::DeleteBandingDefRequest>(Arena*);
template<> ::carbon::frontend::banding::GetActiveBandingDefResponse* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetActiveBandingDefResponse>(Arena*);
template<> ::carbon::frontend::banding::GetDimensionsRequest* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetDimensionsRequest>(Arena*);
template<> ::carbon::frontend::banding::GetNextBandingStateResponse* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetNextBandingStateResponse>(Arena*);
template<> ::carbon::frontend::banding::GetNextVisualizationData2Response* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetNextVisualizationData2Response>(Arena*);
template<> ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest>(Arena*);
template<> ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>(Arena*);
template<> ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::banding::GetNextVisualizationDataRequest* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetNextVisualizationDataRequest>(Arena*);
template<> ::carbon::frontend::banding::GetNextVisualizationDataResponse* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetNextVisualizationDataResponse>(Arena*);
template<> ::carbon::frontend::banding::GetVisualizationMetadataResponse* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetVisualizationMetadataResponse>(Arena*);
template<> ::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::banding::GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::banding::IsBandingEnabledResponse* Arena::CreateMaybeMessage<::carbon::frontend::banding::IsBandingEnabledResponse>(Arena*);
template<> ::carbon::frontend::banding::LoadBandingDefsResponse* Arena::CreateMaybeMessage<::carbon::frontend::banding::LoadBandingDefsResponse>(Arena*);
template<> ::carbon::frontend::banding::SaveBandingDefRequest* Arena::CreateMaybeMessage<::carbon::frontend::banding::SaveBandingDefRequest>(Arena*);
template<> ::carbon::frontend::banding::SetActiveBandingDefRequest* Arena::CreateMaybeMessage<::carbon::frontend::banding::SetActiveBandingDefRequest>(Arena*);
template<> ::carbon::frontend::banding::SetBandingEnabledRequest* Arena::CreateMaybeMessage<::carbon::frontend::banding::SetBandingEnabledRequest>(Arena*);
template<> ::carbon::frontend::banding::SetBandingEnabledResponse* Arena::CreateMaybeMessage<::carbon::frontend::banding::SetBandingEnabledResponse>(Arena*);
template<> ::carbon::frontend::banding::ThresholdFilter* Arena::CreateMaybeMessage<::carbon::frontend::banding::ThresholdFilter>(Arena*);
template<> ::carbon::frontend::banding::ThresholdFilters* Arena::CreateMaybeMessage<::carbon::frontend::banding::ThresholdFilters>(Arena*);
template<> ::carbon::frontend::banding::VisualizationData* Arena::CreateMaybeMessage<::carbon::frontend::banding::VisualizationData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace banding {

enum VisualizationTypeToInclude : int {
  DUPLICATE_WEED = 0,
  DUPLICATE_CROP = 1,
  KILLED_WEED = 2,
  KILLED_CROP = 3,
  KILLING_WEED = 4,
  IGNORED_WEED = 5,
  KILLING_CROP = 6,
  ERROR_WEED = 7,
  ERROR_CROP = 8,
  IGNORED_CROP = 9,
  WEED = 10,
  CROP = 11,
  CROP_RADIUS = 12,
  CROP_KEPT = 13,
  THINNING_BOX = 14,
  VisualizationTypeToInclude_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  VisualizationTypeToInclude_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool VisualizationTypeToInclude_IsValid(int value);
constexpr VisualizationTypeToInclude VisualizationTypeToInclude_MIN = DUPLICATE_WEED;
constexpr VisualizationTypeToInclude VisualizationTypeToInclude_MAX = THINNING_BOX;
constexpr int VisualizationTypeToInclude_ARRAYSIZE = VisualizationTypeToInclude_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* VisualizationTypeToInclude_descriptor();
template<typename T>
inline const std::string& VisualizationTypeToInclude_Name(T enum_t_value) {
  static_assert(::std::is_same<T, VisualizationTypeToInclude>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function VisualizationTypeToInclude_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    VisualizationTypeToInclude_descriptor(), enum_t_value);
}
inline bool VisualizationTypeToInclude_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, VisualizationTypeToInclude* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<VisualizationTypeToInclude>(
    VisualizationTypeToInclude_descriptor(), name, value);
}
enum ThresholdState : int {
  ANY = 0,
  PASS = 1,
  FAIL = 2,
  ThresholdState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ThresholdState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ThresholdState_IsValid(int value);
constexpr ThresholdState ThresholdState_MIN = ANY;
constexpr ThresholdState ThresholdState_MAX = FAIL;
constexpr int ThresholdState_ARRAYSIZE = ThresholdState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ThresholdState_descriptor();
template<typename T>
inline const std::string& ThresholdState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ThresholdState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ThresholdState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ThresholdState_descriptor(), enum_t_value);
}
inline bool ThresholdState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ThresholdState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ThresholdState>(
    ThresholdState_descriptor(), name, value);
}
// ===================================================================

class BandingRow final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.BandingRow) */ {
 public:
  inline BandingRow() : BandingRow(nullptr) {}
  ~BandingRow() override;
  explicit constexpr BandingRow(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BandingRow(const BandingRow& from);
  BandingRow(BandingRow&& from) noexcept
    : BandingRow() {
    *this = ::std::move(from);
  }

  inline BandingRow& operator=(const BandingRow& from) {
    CopyFrom(from);
    return *this;
  }
  inline BandingRow& operator=(BandingRow&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BandingRow& default_instance() {
    return *internal_default_instance();
  }
  static inline const BandingRow* internal_default_instance() {
    return reinterpret_cast<const BandingRow*>(
               &_BandingRow_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(BandingRow& a, BandingRow& b) {
    a.Swap(&b);
  }
  inline void Swap(BandingRow* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BandingRow* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BandingRow* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BandingRow>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BandingRow& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BandingRow& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BandingRow* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.BandingRow";
  }
  protected:
  explicit BandingRow(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBandsFieldNumber = 2,
    kRowIdFieldNumber = 1,
  };
  // repeated .weed_tracking.BandDefinition bands = 2;
  int bands_size() const;
  private:
  int _internal_bands_size() const;
  public:
  void clear_bands();
  ::weed_tracking::BandDefinition* mutable_bands(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition >*
      mutable_bands();
  private:
  const ::weed_tracking::BandDefinition& _internal_bands(int index) const;
  ::weed_tracking::BandDefinition* _internal_add_bands();
  public:
  const ::weed_tracking::BandDefinition& bands(int index) const;
  ::weed_tracking::BandDefinition* add_bands();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition >&
      bands() const;

  // int32 row_id = 1;
  void clear_row_id();
  int32_t row_id() const;
  void set_row_id(int32_t value);
  private:
  int32_t _internal_row_id() const;
  void _internal_set_row_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.BandingRow)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition > bands_;
  int32_t row_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class BandingDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.BandingDef) */ {
 public:
  inline BandingDef() : BandingDef(nullptr) {}
  ~BandingDef() override;
  explicit constexpr BandingDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BandingDef(const BandingDef& from);
  BandingDef(BandingDef&& from) noexcept
    : BandingDef() {
    *this = ::std::move(from);
  }

  inline BandingDef& operator=(const BandingDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline BandingDef& operator=(BandingDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BandingDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const BandingDef* internal_default_instance() {
    return reinterpret_cast<const BandingDef*>(
               &_BandingDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(BandingDef& a, BandingDef& b) {
    a.Swap(&b);
  }
  inline void Swap(BandingDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BandingDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BandingDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BandingDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BandingDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BandingDef& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BandingDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.BandingDef";
  }
  protected:
  explicit BandingDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowsFieldNumber = 2,
    kNameFieldNumber = 1,
    kUuidFieldNumber = 3,
  };
  // repeated .carbon.frontend.banding.BandingRow rows = 2;
  int rows_size() const;
  private:
  int _internal_rows_size() const;
  public:
  void clear_rows();
  ::carbon::frontend::banding::BandingRow* mutable_rows(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingRow >*
      mutable_rows();
  private:
  const ::carbon::frontend::banding::BandingRow& _internal_rows(int index) const;
  ::carbon::frontend::banding::BandingRow* _internal_add_rows();
  public:
  const ::carbon::frontend::banding::BandingRow& rows(int index) const;
  ::carbon::frontend::banding::BandingRow* add_rows();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingRow >&
      rows() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string uuid = 3;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.BandingDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingRow > rows_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class SaveBandingDefRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.SaveBandingDefRequest) */ {
 public:
  inline SaveBandingDefRequest() : SaveBandingDefRequest(nullptr) {}
  ~SaveBandingDefRequest() override;
  explicit constexpr SaveBandingDefRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SaveBandingDefRequest(const SaveBandingDefRequest& from);
  SaveBandingDefRequest(SaveBandingDefRequest&& from) noexcept
    : SaveBandingDefRequest() {
    *this = ::std::move(from);
  }

  inline SaveBandingDefRequest& operator=(const SaveBandingDefRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SaveBandingDefRequest& operator=(SaveBandingDefRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SaveBandingDefRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SaveBandingDefRequest* internal_default_instance() {
    return reinterpret_cast<const SaveBandingDefRequest*>(
               &_SaveBandingDefRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SaveBandingDefRequest& a, SaveBandingDefRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SaveBandingDefRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SaveBandingDefRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SaveBandingDefRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SaveBandingDefRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SaveBandingDefRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SaveBandingDefRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SaveBandingDefRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.SaveBandingDefRequest";
  }
  protected:
  explicit SaveBandingDefRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBandingDefFieldNumber = 1,
    kSetActiveFieldNumber = 2,
  };
  // .carbon.frontend.banding.BandingDef bandingDef = 1;
  bool has_bandingdef() const;
  private:
  bool _internal_has_bandingdef() const;
  public:
  void clear_bandingdef();
  const ::carbon::frontend::banding::BandingDef& bandingdef() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::BandingDef* release_bandingdef();
  ::carbon::frontend::banding::BandingDef* mutable_bandingdef();
  void set_allocated_bandingdef(::carbon::frontend::banding::BandingDef* bandingdef);
  private:
  const ::carbon::frontend::banding::BandingDef& _internal_bandingdef() const;
  ::carbon::frontend::banding::BandingDef* _internal_mutable_bandingdef();
  public:
  void unsafe_arena_set_allocated_bandingdef(
      ::carbon::frontend::banding::BandingDef* bandingdef);
  ::carbon::frontend::banding::BandingDef* unsafe_arena_release_bandingdef();

  // bool setActive = 2;
  void clear_setactive();
  bool setactive() const;
  void set_setactive(bool value);
  private:
  bool _internal_setactive() const;
  void _internal_set_setactive(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.SaveBandingDefRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::banding::BandingDef* bandingdef_;
  bool setactive_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class LoadBandingDefsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.LoadBandingDefsResponse) */ {
 public:
  inline LoadBandingDefsResponse() : LoadBandingDefsResponse(nullptr) {}
  ~LoadBandingDefsResponse() override;
  explicit constexpr LoadBandingDefsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LoadBandingDefsResponse(const LoadBandingDefsResponse& from);
  LoadBandingDefsResponse(LoadBandingDefsResponse&& from) noexcept
    : LoadBandingDefsResponse() {
    *this = ::std::move(from);
  }

  inline LoadBandingDefsResponse& operator=(const LoadBandingDefsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline LoadBandingDefsResponse& operator=(LoadBandingDefsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LoadBandingDefsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const LoadBandingDefsResponse* internal_default_instance() {
    return reinterpret_cast<const LoadBandingDefsResponse*>(
               &_LoadBandingDefsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(LoadBandingDefsResponse& a, LoadBandingDefsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(LoadBandingDefsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LoadBandingDefsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LoadBandingDefsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LoadBandingDefsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LoadBandingDefsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LoadBandingDefsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LoadBandingDefsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.LoadBandingDefsResponse";
  }
  protected:
  explicit LoadBandingDefsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBandingDefsFieldNumber = 1,
    kActiveDefFieldNumber = 2,
    kActiveDefUUIDFieldNumber = 3,
  };
  // repeated .carbon.frontend.banding.BandingDef bandingDefs = 1;
  int bandingdefs_size() const;
  private:
  int _internal_bandingdefs_size() const;
  public:
  void clear_bandingdefs();
  ::carbon::frontend::banding::BandingDef* mutable_bandingdefs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef >*
      mutable_bandingdefs();
  private:
  const ::carbon::frontend::banding::BandingDef& _internal_bandingdefs(int index) const;
  ::carbon::frontend::banding::BandingDef* _internal_add_bandingdefs();
  public:
  const ::carbon::frontend::banding::BandingDef& bandingdefs(int index) const;
  ::carbon::frontend::banding::BandingDef* add_bandingdefs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef >&
      bandingdefs() const;

  // string activeDef = 2 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_activedef();
  PROTOBUF_DEPRECATED const std::string& activedef() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_activedef(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_activedef();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_activedef();
  PROTOBUF_DEPRECATED void set_allocated_activedef(std::string* activedef);
  private:
  const std::string& _internal_activedef() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_activedef(const std::string& value);
  std::string* _internal_mutable_activedef();
  public:

  // string activeDefUUID = 3;
  void clear_activedefuuid();
  const std::string& activedefuuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_activedefuuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_activedefuuid();
  PROTOBUF_NODISCARD std::string* release_activedefuuid();
  void set_allocated_activedefuuid(std::string* activedefuuid);
  private:
  const std::string& _internal_activedefuuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_activedefuuid(const std::string& value);
  std::string* _internal_mutable_activedefuuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.LoadBandingDefsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef > bandingdefs_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr activedef_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr activedefuuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class SetActiveBandingDefRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.SetActiveBandingDefRequest) */ {
 public:
  inline SetActiveBandingDefRequest() : SetActiveBandingDefRequest(nullptr) {}
  ~SetActiveBandingDefRequest() override;
  explicit constexpr SetActiveBandingDefRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveBandingDefRequest(const SetActiveBandingDefRequest& from);
  SetActiveBandingDefRequest(SetActiveBandingDefRequest&& from) noexcept
    : SetActiveBandingDefRequest() {
    *this = ::std::move(from);
  }

  inline SetActiveBandingDefRequest& operator=(const SetActiveBandingDefRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveBandingDefRequest& operator=(SetActiveBandingDefRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveBandingDefRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveBandingDefRequest* internal_default_instance() {
    return reinterpret_cast<const SetActiveBandingDefRequest*>(
               &_SetActiveBandingDefRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SetActiveBandingDefRequest& a, SetActiveBandingDefRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveBandingDefRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveBandingDefRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveBandingDefRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveBandingDefRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetActiveBandingDefRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetActiveBandingDefRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetActiveBandingDefRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.SetActiveBandingDefRequest";
  }
  protected:
  explicit SetActiveBandingDefRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kUuidFieldNumber = 2,
  };
  // string name = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_name();
  PROTOBUF_DEPRECATED const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_name(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_name();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_name();
  PROTOBUF_DEPRECATED void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string uuid = 2;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.SetActiveBandingDefRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetActiveBandingDefResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetActiveBandingDefResponse) */ {
 public:
  inline GetActiveBandingDefResponse() : GetActiveBandingDefResponse(nullptr) {}
  ~GetActiveBandingDefResponse() override;
  explicit constexpr GetActiveBandingDefResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetActiveBandingDefResponse(const GetActiveBandingDefResponse& from);
  GetActiveBandingDefResponse(GetActiveBandingDefResponse&& from) noexcept
    : GetActiveBandingDefResponse() {
    *this = ::std::move(from);
  }

  inline GetActiveBandingDefResponse& operator=(const GetActiveBandingDefResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetActiveBandingDefResponse& operator=(GetActiveBandingDefResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetActiveBandingDefResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetActiveBandingDefResponse* internal_default_instance() {
    return reinterpret_cast<const GetActiveBandingDefResponse*>(
               &_GetActiveBandingDefResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetActiveBandingDefResponse& a, GetActiveBandingDefResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetActiveBandingDefResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetActiveBandingDefResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetActiveBandingDefResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetActiveBandingDefResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetActiveBandingDefResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetActiveBandingDefResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetActiveBandingDefResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetActiveBandingDefResponse";
  }
  protected:
  explicit GetActiveBandingDefResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kUuidFieldNumber = 2,
  };
  // string name = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_name();
  PROTOBUF_DEPRECATED const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_name(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_name();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_name();
  PROTOBUF_DEPRECATED void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string uuid = 2;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetActiveBandingDefResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class DeleteBandingDefRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.DeleteBandingDefRequest) */ {
 public:
  inline DeleteBandingDefRequest() : DeleteBandingDefRequest(nullptr) {}
  ~DeleteBandingDefRequest() override;
  explicit constexpr DeleteBandingDefRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteBandingDefRequest(const DeleteBandingDefRequest& from);
  DeleteBandingDefRequest(DeleteBandingDefRequest&& from) noexcept
    : DeleteBandingDefRequest() {
    *this = ::std::move(from);
  }

  inline DeleteBandingDefRequest& operator=(const DeleteBandingDefRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteBandingDefRequest& operator=(DeleteBandingDefRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteBandingDefRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteBandingDefRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteBandingDefRequest*>(
               &_DeleteBandingDefRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DeleteBandingDefRequest& a, DeleteBandingDefRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteBandingDefRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteBandingDefRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteBandingDefRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteBandingDefRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteBandingDefRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeleteBandingDefRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteBandingDefRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.DeleteBandingDefRequest";
  }
  protected:
  explicit DeleteBandingDefRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kUuidFieldNumber = 2,
  };
  // string name = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_name();
  PROTOBUF_DEPRECATED const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_name(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_name();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_name();
  PROTOBUF_DEPRECATED void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string uuid = 2;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.DeleteBandingDefRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class VisualizationData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.VisualizationData) */ {
 public:
  inline VisualizationData() : VisualizationData(nullptr) {}
  ~VisualizationData() override;
  explicit constexpr VisualizationData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VisualizationData(const VisualizationData& from);
  VisualizationData(VisualizationData&& from) noexcept
    : VisualizationData() {
    *this = ::std::move(from);
  }

  inline VisualizationData& operator=(const VisualizationData& from) {
    CopyFrom(from);
    return *this;
  }
  inline VisualizationData& operator=(VisualizationData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VisualizationData& default_instance() {
    return *internal_default_instance();
  }
  static inline const VisualizationData* internal_default_instance() {
    return reinterpret_cast<const VisualizationData*>(
               &_VisualizationData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(VisualizationData& a, VisualizationData& b) {
    a.Swap(&b);
  }
  inline void Swap(VisualizationData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VisualizationData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VisualizationData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VisualizationData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VisualizationData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VisualizationData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VisualizationData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.VisualizationData";
  }
  protected:
  explicit VisualizationData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXMmFieldNumber = 1,
    kYMmFieldNumber = 2,
    kZMmFieldNumber = 3,
    kIsWeedFieldNumber = 4,
  };
  // int32 x_mm = 1;
  void clear_x_mm();
  int32_t x_mm() const;
  void set_x_mm(int32_t value);
  private:
  int32_t _internal_x_mm() const;
  void _internal_set_x_mm(int32_t value);
  public:

  // int32 y_mm = 2;
  void clear_y_mm();
  int32_t y_mm() const;
  void set_y_mm(int32_t value);
  private:
  int32_t _internal_y_mm() const;
  void _internal_set_y_mm(int32_t value);
  public:

  // int32 z_mm = 3;
  void clear_z_mm();
  int32_t z_mm() const;
  void set_z_mm(int32_t value);
  private:
  int32_t _internal_z_mm() const;
  void _internal_set_z_mm(int32_t value);
  public:

  // bool is_weed = 4;
  void clear_is_weed();
  bool is_weed() const;
  void set_is_weed(bool value);
  private:
  bool _internal_is_weed() const;
  void _internal_set_is_weed(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.VisualizationData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t x_mm_;
  int32_t y_mm_;
  int32_t z_mm_;
  bool is_weed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetNextVisualizationDataRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetNextVisualizationDataRequest) */ {
 public:
  inline GetNextVisualizationDataRequest() : GetNextVisualizationDataRequest(nullptr) {}
  ~GetNextVisualizationDataRequest() override;
  explicit constexpr GetNextVisualizationDataRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextVisualizationDataRequest(const GetNextVisualizationDataRequest& from);
  GetNextVisualizationDataRequest(GetNextVisualizationDataRequest&& from) noexcept
    : GetNextVisualizationDataRequest() {
    *this = ::std::move(from);
  }

  inline GetNextVisualizationDataRequest& operator=(const GetNextVisualizationDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextVisualizationDataRequest& operator=(GetNextVisualizationDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextVisualizationDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextVisualizationDataRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextVisualizationDataRequest*>(
               &_GetNextVisualizationDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(GetNextVisualizationDataRequest& a, GetNextVisualizationDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextVisualizationDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextVisualizationDataRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextVisualizationDataRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextVisualizationDataRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextVisualizationDataRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextVisualizationDataRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextVisualizationDataRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetNextVisualizationDataRequest";
  }
  protected:
  explicit GetNextVisualizationDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypesToIncludeFieldNumber = 3,
    kTsFieldNumber = 1,
    kThresholdFiltersFieldNumber = 4,
    kRowIdFieldNumber = 2,
  };
  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
  int types_to_include_size() const;
  private:
  int _internal_types_to_include_size() const;
  public:
  void clear_types_to_include();
  private:
  ::carbon::frontend::banding::VisualizationTypeToInclude _internal_types_to_include(int index) const;
  void _internal_add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_types_to_include();
  public:
  ::carbon::frontend::banding::VisualizationTypeToInclude types_to_include(int index) const;
  void set_types_to_include(int index, ::carbon::frontend::banding::VisualizationTypeToInclude value);
  void add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& types_to_include() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_types_to_include();

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.banding.ThresholdFilters threshold_filters = 4;
  bool has_threshold_filters() const;
  private:
  bool _internal_has_threshold_filters() const;
  public:
  void clear_threshold_filters();
  const ::carbon::frontend::banding::ThresholdFilters& threshold_filters() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::ThresholdFilters* release_threshold_filters();
  ::carbon::frontend::banding::ThresholdFilters* mutable_threshold_filters();
  void set_allocated_threshold_filters(::carbon::frontend::banding::ThresholdFilters* threshold_filters);
  private:
  const ::carbon::frontend::banding::ThresholdFilters& _internal_threshold_filters() const;
  ::carbon::frontend::banding::ThresholdFilters* _internal_mutable_threshold_filters();
  public:
  void unsafe_arena_set_allocated_threshold_filters(
      ::carbon::frontend::banding::ThresholdFilters* threshold_filters);
  ::carbon::frontend::banding::ThresholdFilters* unsafe_arena_release_threshold_filters();

  // int32 row_id = 2;
  void clear_row_id();
  int32_t row_id() const;
  void set_row_id(int32_t value);
  private:
  int32_t _internal_row_id() const;
  void _internal_set_row_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> types_to_include_;
  mutable std::atomic<int> _types_to_include_cached_byte_size_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::banding::ThresholdFilters* threshold_filters_;
  int32_t row_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetNextVisualizationDataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetNextVisualizationDataResponse) */ {
 public:
  inline GetNextVisualizationDataResponse() : GetNextVisualizationDataResponse(nullptr) {}
  ~GetNextVisualizationDataResponse() override;
  explicit constexpr GetNextVisualizationDataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextVisualizationDataResponse(const GetNextVisualizationDataResponse& from);
  GetNextVisualizationDataResponse(GetNextVisualizationDataResponse&& from) noexcept
    : GetNextVisualizationDataResponse() {
    *this = ::std::move(from);
  }

  inline GetNextVisualizationDataResponse& operator=(const GetNextVisualizationDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextVisualizationDataResponse& operator=(GetNextVisualizationDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextVisualizationDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextVisualizationDataResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextVisualizationDataResponse*>(
               &_GetNextVisualizationDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GetNextVisualizationDataResponse& a, GetNextVisualizationDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextVisualizationDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextVisualizationDataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextVisualizationDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextVisualizationDataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextVisualizationDataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextVisualizationDataResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextVisualizationDataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetNextVisualizationDataResponse";
  }
  protected:
  explicit GetNextVisualizationDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 2,
    kBandsFieldNumber = 3,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.frontend.banding.VisualizationData data = 2;
  int data_size() const;
  private:
  int _internal_data_size() const;
  public:
  void clear_data();
  ::carbon::frontend::banding::VisualizationData* mutable_data(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::VisualizationData >*
      mutable_data();
  private:
  const ::carbon::frontend::banding::VisualizationData& _internal_data(int index) const;
  ::carbon::frontend::banding::VisualizationData* _internal_add_data();
  public:
  const ::carbon::frontend::banding::VisualizationData& data(int index) const;
  ::carbon::frontend::banding::VisualizationData* add_data();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::VisualizationData >&
      data() const;

  // repeated .weed_tracking.BandDefinition bands = 3;
  int bands_size() const;
  private:
  int _internal_bands_size() const;
  public:
  void clear_bands();
  ::weed_tracking::BandDefinition* mutable_bands(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition >*
      mutable_bands();
  private:
  const ::weed_tracking::BandDefinition& _internal_bands(int index) const;
  ::weed_tracking::BandDefinition* _internal_add_bands();
  public:
  const ::weed_tracking::BandDefinition& bands(int index) const;
  ::weed_tracking::BandDefinition* add_bands();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition >&
      bands() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::VisualizationData > data_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition > bands_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetNextVisualizationData2Response final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetNextVisualizationData2Response) */ {
 public:
  inline GetNextVisualizationData2Response() : GetNextVisualizationData2Response(nullptr) {}
  ~GetNextVisualizationData2Response() override;
  explicit constexpr GetNextVisualizationData2Response(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextVisualizationData2Response(const GetNextVisualizationData2Response& from);
  GetNextVisualizationData2Response(GetNextVisualizationData2Response&& from) noexcept
    : GetNextVisualizationData2Response() {
    *this = ::std::move(from);
  }

  inline GetNextVisualizationData2Response& operator=(const GetNextVisualizationData2Response& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextVisualizationData2Response& operator=(GetNextVisualizationData2Response&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextVisualizationData2Response& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextVisualizationData2Response* internal_default_instance() {
    return reinterpret_cast<const GetNextVisualizationData2Response*>(
               &_GetNextVisualizationData2Response_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GetNextVisualizationData2Response& a, GetNextVisualizationData2Response& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextVisualizationData2Response* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextVisualizationData2Response* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextVisualizationData2Response* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextVisualizationData2Response>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextVisualizationData2Response& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextVisualizationData2Response& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextVisualizationData2Response* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetNextVisualizationData2Response";
  }
  protected:
  explicit GetNextVisualizationData2Response(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDataFieldNumber = 1,
    kTsFieldNumber = 2,
  };
  // .weed_tracking.DiagnosticsSnapshot data = 1;
  bool has_data() const;
  private:
  bool _internal_has_data() const;
  public:
  void clear_data();
  const ::weed_tracking::DiagnosticsSnapshot& data() const;
  PROTOBUF_NODISCARD ::weed_tracking::DiagnosticsSnapshot* release_data();
  ::weed_tracking::DiagnosticsSnapshot* mutable_data();
  void set_allocated_data(::weed_tracking::DiagnosticsSnapshot* data);
  private:
  const ::weed_tracking::DiagnosticsSnapshot& _internal_data() const;
  ::weed_tracking::DiagnosticsSnapshot* _internal_mutable_data();
  public:
  void unsafe_arena_set_allocated_data(
      ::weed_tracking::DiagnosticsSnapshot* data);
  ::weed_tracking::DiagnosticsSnapshot* unsafe_arena_release_data();

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationData2Response)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::weed_tracking::DiagnosticsSnapshot* data_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetDimensionsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetDimensionsRequest) */ {
 public:
  inline GetDimensionsRequest() : GetDimensionsRequest(nullptr) {}
  ~GetDimensionsRequest() override;
  explicit constexpr GetDimensionsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetDimensionsRequest(const GetDimensionsRequest& from);
  GetDimensionsRequest(GetDimensionsRequest&& from) noexcept
    : GetDimensionsRequest() {
    *this = ::std::move(from);
  }

  inline GetDimensionsRequest& operator=(const GetDimensionsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetDimensionsRequest& operator=(GetDimensionsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetDimensionsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetDimensionsRequest* internal_default_instance() {
    return reinterpret_cast<const GetDimensionsRequest*>(
               &_GetDimensionsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(GetDimensionsRequest& a, GetDimensionsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetDimensionsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetDimensionsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetDimensionsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetDimensionsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetDimensionsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetDimensionsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetDimensionsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetDimensionsRequest";
  }
  protected:
  explicit GetDimensionsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowIdFieldNumber = 1,
  };
  // int32 row_id = 1;
  void clear_row_id();
  int32_t row_id() const;
  void set_row_id(int32_t value);
  private:
  int32_t _internal_row_id() const;
  void _internal_set_row_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetDimensionsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t row_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class SetBandingEnabledRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.SetBandingEnabledRequest) */ {
 public:
  inline SetBandingEnabledRequest() : SetBandingEnabledRequest(nullptr) {}
  ~SetBandingEnabledRequest() override;
  explicit constexpr SetBandingEnabledRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetBandingEnabledRequest(const SetBandingEnabledRequest& from);
  SetBandingEnabledRequest(SetBandingEnabledRequest&& from) noexcept
    : SetBandingEnabledRequest() {
    *this = ::std::move(from);
  }

  inline SetBandingEnabledRequest& operator=(const SetBandingEnabledRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetBandingEnabledRequest& operator=(SetBandingEnabledRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetBandingEnabledRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetBandingEnabledRequest* internal_default_instance() {
    return reinterpret_cast<const SetBandingEnabledRequest*>(
               &_SetBandingEnabledRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(SetBandingEnabledRequest& a, SetBandingEnabledRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetBandingEnabledRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetBandingEnabledRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetBandingEnabledRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetBandingEnabledRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetBandingEnabledRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetBandingEnabledRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetBandingEnabledRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.SetBandingEnabledRequest";
  }
  protected:
  explicit SetBandingEnabledRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.SetBandingEnabledRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class SetBandingEnabledResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.SetBandingEnabledResponse) */ {
 public:
  inline SetBandingEnabledResponse() : SetBandingEnabledResponse(nullptr) {}
  explicit constexpr SetBandingEnabledResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetBandingEnabledResponse(const SetBandingEnabledResponse& from);
  SetBandingEnabledResponse(SetBandingEnabledResponse&& from) noexcept
    : SetBandingEnabledResponse() {
    *this = ::std::move(from);
  }

  inline SetBandingEnabledResponse& operator=(const SetBandingEnabledResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetBandingEnabledResponse& operator=(SetBandingEnabledResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetBandingEnabledResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetBandingEnabledResponse* internal_default_instance() {
    return reinterpret_cast<const SetBandingEnabledResponse*>(
               &_SetBandingEnabledResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(SetBandingEnabledResponse& a, SetBandingEnabledResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetBandingEnabledResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetBandingEnabledResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetBandingEnabledResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetBandingEnabledResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetBandingEnabledResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetBandingEnabledResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.SetBandingEnabledResponse";
  }
  protected:
  explicit SetBandingEnabledResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.SetBandingEnabledResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class IsBandingEnabledResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.IsBandingEnabledResponse) */ {
 public:
  inline IsBandingEnabledResponse() : IsBandingEnabledResponse(nullptr) {}
  ~IsBandingEnabledResponse() override;
  explicit constexpr IsBandingEnabledResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IsBandingEnabledResponse(const IsBandingEnabledResponse& from);
  IsBandingEnabledResponse(IsBandingEnabledResponse&& from) noexcept
    : IsBandingEnabledResponse() {
    *this = ::std::move(from);
  }

  inline IsBandingEnabledResponse& operator=(const IsBandingEnabledResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline IsBandingEnabledResponse& operator=(IsBandingEnabledResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IsBandingEnabledResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const IsBandingEnabledResponse* internal_default_instance() {
    return reinterpret_cast<const IsBandingEnabledResponse*>(
               &_IsBandingEnabledResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(IsBandingEnabledResponse& a, IsBandingEnabledResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(IsBandingEnabledResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IsBandingEnabledResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IsBandingEnabledResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IsBandingEnabledResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IsBandingEnabledResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const IsBandingEnabledResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IsBandingEnabledResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.IsBandingEnabledResponse";
  }
  protected:
  explicit IsBandingEnabledResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.IsBandingEnabledResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse, 
    int32_t, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse, 
    int32_t, float,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> SuperType;
  GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse();
  explicit constexpr GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse& other);
  static const GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse*>(&_GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetVisualizationMetadataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetVisualizationMetadataResponse) */ {
 public:
  inline GetVisualizationMetadataResponse() : GetVisualizationMetadataResponse(nullptr) {}
  ~GetVisualizationMetadataResponse() override;
  explicit constexpr GetVisualizationMetadataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetVisualizationMetadataResponse(const GetVisualizationMetadataResponse& from);
  GetVisualizationMetadataResponse(GetVisualizationMetadataResponse&& from) noexcept
    : GetVisualizationMetadataResponse() {
    *this = ::std::move(from);
  }

  inline GetVisualizationMetadataResponse& operator=(const GetVisualizationMetadataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetVisualizationMetadataResponse& operator=(GetVisualizationMetadataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetVisualizationMetadataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetVisualizationMetadataResponse* internal_default_instance() {
    return reinterpret_cast<const GetVisualizationMetadataResponse*>(
               &_GetVisualizationMetadataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(GetVisualizationMetadataResponse& a, GetVisualizationMetadataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetVisualizationMetadataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetVisualizationMetadataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetVisualizationMetadataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetVisualizationMetadataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetVisualizationMetadataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetVisualizationMetadataResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetVisualizationMetadataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetVisualizationMetadataResponse";
  }
  protected:
  explicit GetVisualizationMetadataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kCropSafetyRadiusMmPerRowFieldNumber = 1,
  };
  // map<int32, float> crop_safety_radius_mm_per_row = 1;
  int crop_safety_radius_mm_per_row_size() const;
  private:
  int _internal_crop_safety_radius_mm_per_row_size() const;
  public:
  void clear_crop_safety_radius_mm_per_row();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >&
      _internal_crop_safety_radius_mm_per_row() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >*
      _internal_mutable_crop_safety_radius_mm_per_row();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >&
      crop_safety_radius_mm_per_row() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >*
      mutable_crop_safety_radius_mm_per_row();

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetVisualizationMetadataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetVisualizationMetadataResponse_CropSafetyRadiusMmPerRowEntry_DoNotUse,
      int32_t, float,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_FLOAT> crop_safety_radius_mm_per_row_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class ThresholdFilter final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.ThresholdFilter) */ {
 public:
  inline ThresholdFilter() : ThresholdFilter(nullptr) {}
  ~ThresholdFilter() override;
  explicit constexpr ThresholdFilter(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ThresholdFilter(const ThresholdFilter& from);
  ThresholdFilter(ThresholdFilter&& from) noexcept
    : ThresholdFilter() {
    *this = ::std::move(from);
  }

  inline ThresholdFilter& operator=(const ThresholdFilter& from) {
    CopyFrom(from);
    return *this;
  }
  inline ThresholdFilter& operator=(ThresholdFilter&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ThresholdFilter& default_instance() {
    return *internal_default_instance();
  }
  static inline const ThresholdFilter* internal_default_instance() {
    return reinterpret_cast<const ThresholdFilter*>(
               &_ThresholdFilter_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(ThresholdFilter& a, ThresholdFilter& b) {
    a.Swap(&b);
  }
  inline void Swap(ThresholdFilter* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ThresholdFilter* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ThresholdFilter* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ThresholdFilter>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ThresholdFilter& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ThresholdFilter& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ThresholdFilter* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.ThresholdFilter";
  }
  protected:
  explicit ThresholdFilter(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWeedingFieldNumber = 1,
    kThinningFieldNumber = 2,
    kBandingFieldNumber = 3,
  };
  // .carbon.frontend.banding.ThresholdState weeding = 1;
  void clear_weeding();
  ::carbon::frontend::banding::ThresholdState weeding() const;
  void set_weeding(::carbon::frontend::banding::ThresholdState value);
  private:
  ::carbon::frontend::banding::ThresholdState _internal_weeding() const;
  void _internal_set_weeding(::carbon::frontend::banding::ThresholdState value);
  public:

  // .carbon.frontend.banding.ThresholdState thinning = 2;
  void clear_thinning();
  ::carbon::frontend::banding::ThresholdState thinning() const;
  void set_thinning(::carbon::frontend::banding::ThresholdState value);
  private:
  ::carbon::frontend::banding::ThresholdState _internal_thinning() const;
  void _internal_set_thinning(::carbon::frontend::banding::ThresholdState value);
  public:

  // .carbon.frontend.banding.ThresholdState banding = 3;
  void clear_banding();
  ::carbon::frontend::banding::ThresholdState banding() const;
  void set_banding(::carbon::frontend::banding::ThresholdState value);
  private:
  ::carbon::frontend::banding::ThresholdState _internal_banding() const;
  void _internal_set_banding(::carbon::frontend::banding::ThresholdState value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.ThresholdFilter)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int weeding_;
  int thinning_;
  int banding_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class ThresholdFilters final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.ThresholdFilters) */ {
 public:
  inline ThresholdFilters() : ThresholdFilters(nullptr) {}
  ~ThresholdFilters() override;
  explicit constexpr ThresholdFilters(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ThresholdFilters(const ThresholdFilters& from);
  ThresholdFilters(ThresholdFilters&& from) noexcept
    : ThresholdFilters() {
    *this = ::std::move(from);
  }

  inline ThresholdFilters& operator=(const ThresholdFilters& from) {
    CopyFrom(from);
    return *this;
  }
  inline ThresholdFilters& operator=(ThresholdFilters&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ThresholdFilters& default_instance() {
    return *internal_default_instance();
  }
  static inline const ThresholdFilters* internal_default_instance() {
    return reinterpret_cast<const ThresholdFilters*>(
               &_ThresholdFilters_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(ThresholdFilters& a, ThresholdFilters& b) {
    a.Swap(&b);
  }
  inline void Swap(ThresholdFilters* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ThresholdFilters* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ThresholdFilters* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ThresholdFilters>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ThresholdFilters& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ThresholdFilters& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ThresholdFilters* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.ThresholdFilters";
  }
  protected:
  explicit ThresholdFilters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropFieldNumber = 1,
    kWeedFieldNumber = 2,
  };
  // .carbon.frontend.banding.ThresholdFilter crop = 1;
  bool has_crop() const;
  private:
  bool _internal_has_crop() const;
  public:
  void clear_crop();
  const ::carbon::frontend::banding::ThresholdFilter& crop() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::ThresholdFilter* release_crop();
  ::carbon::frontend::banding::ThresholdFilter* mutable_crop();
  void set_allocated_crop(::carbon::frontend::banding::ThresholdFilter* crop);
  private:
  const ::carbon::frontend::banding::ThresholdFilter& _internal_crop() const;
  ::carbon::frontend::banding::ThresholdFilter* _internal_mutable_crop();
  public:
  void unsafe_arena_set_allocated_crop(
      ::carbon::frontend::banding::ThresholdFilter* crop);
  ::carbon::frontend::banding::ThresholdFilter* unsafe_arena_release_crop();

  // .carbon.frontend.banding.ThresholdFilter weed = 2;
  bool has_weed() const;
  private:
  bool _internal_has_weed() const;
  public:
  void clear_weed();
  const ::carbon::frontend::banding::ThresholdFilter& weed() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::ThresholdFilter* release_weed();
  ::carbon::frontend::banding::ThresholdFilter* mutable_weed();
  void set_allocated_weed(::carbon::frontend::banding::ThresholdFilter* weed);
  private:
  const ::carbon::frontend::banding::ThresholdFilter& _internal_weed() const;
  ::carbon::frontend::banding::ThresholdFilter* _internal_mutable_weed();
  public:
  void unsafe_arena_set_allocated_weed(
      ::carbon::frontend::banding::ThresholdFilter* weed);
  ::carbon::frontend::banding::ThresholdFilter* unsafe_arena_release_weed();

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.ThresholdFilters)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::banding::ThresholdFilter* crop_;
  ::carbon::frontend::banding::ThresholdFilter* weed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetNextVisualizationDataForAllRowsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest) */ {
 public:
  inline GetNextVisualizationDataForAllRowsRequest() : GetNextVisualizationDataForAllRowsRequest(nullptr) {}
  ~GetNextVisualizationDataForAllRowsRequest() override;
  explicit constexpr GetNextVisualizationDataForAllRowsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextVisualizationDataForAllRowsRequest(const GetNextVisualizationDataForAllRowsRequest& from);
  GetNextVisualizationDataForAllRowsRequest(GetNextVisualizationDataForAllRowsRequest&& from) noexcept
    : GetNextVisualizationDataForAllRowsRequest() {
    *this = ::std::move(from);
  }

  inline GetNextVisualizationDataForAllRowsRequest& operator=(const GetNextVisualizationDataForAllRowsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextVisualizationDataForAllRowsRequest& operator=(GetNextVisualizationDataForAllRowsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextVisualizationDataForAllRowsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextVisualizationDataForAllRowsRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextVisualizationDataForAllRowsRequest*>(
               &_GetNextVisualizationDataForAllRowsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(GetNextVisualizationDataForAllRowsRequest& a, GetNextVisualizationDataForAllRowsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextVisualizationDataForAllRowsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextVisualizationDataForAllRowsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextVisualizationDataForAllRowsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextVisualizationDataForAllRowsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextVisualizationDataForAllRowsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextVisualizationDataForAllRowsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextVisualizationDataForAllRowsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest";
  }
  protected:
  explicit GetNextVisualizationDataForAllRowsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypesToIncludeFieldNumber = 2,
    kTsFieldNumber = 1,
    kThresholdFiltersFieldNumber = 3,
  };
  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 2;
  int types_to_include_size() const;
  private:
  int _internal_types_to_include_size() const;
  public:
  void clear_types_to_include();
  private:
  ::carbon::frontend::banding::VisualizationTypeToInclude _internal_types_to_include(int index) const;
  void _internal_add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_types_to_include();
  public:
  ::carbon::frontend::banding::VisualizationTypeToInclude types_to_include(int index) const;
  void set_types_to_include(int index, ::carbon::frontend::banding::VisualizationTypeToInclude value);
  void add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& types_to_include() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_types_to_include();

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.banding.ThresholdFilters threshold_filters = 3;
  bool has_threshold_filters() const;
  private:
  bool _internal_has_threshold_filters() const;
  public:
  void clear_threshold_filters();
  const ::carbon::frontend::banding::ThresholdFilters& threshold_filters() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::ThresholdFilters* release_threshold_filters();
  ::carbon::frontend::banding::ThresholdFilters* mutable_threshold_filters();
  void set_allocated_threshold_filters(::carbon::frontend::banding::ThresholdFilters* threshold_filters);
  private:
  const ::carbon::frontend::banding::ThresholdFilters& _internal_threshold_filters() const;
  ::carbon::frontend::banding::ThresholdFilters* _internal_mutable_threshold_filters();
  public:
  void unsafe_arena_set_allocated_threshold_filters(
      ::carbon::frontend::banding::ThresholdFilters* threshold_filters);
  ::carbon::frontend::banding::ThresholdFilters* unsafe_arena_release_threshold_filters();

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> types_to_include_;
  mutable std::atomic<int> _types_to_include_cached_byte_size_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::banding::ThresholdFilters* threshold_filters_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse, 
    int32_t, ::weed_tracking::DiagnosticsSnapshot,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse, 
    int32_t, ::weed_tracking::DiagnosticsSnapshot,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse();
  explicit constexpr GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse& other);
  static const GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse*>(&_GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetNextVisualizationDataForAllRowsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse) */ {
 public:
  inline GetNextVisualizationDataForAllRowsResponse() : GetNextVisualizationDataForAllRowsResponse(nullptr) {}
  ~GetNextVisualizationDataForAllRowsResponse() override;
  explicit constexpr GetNextVisualizationDataForAllRowsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextVisualizationDataForAllRowsResponse(const GetNextVisualizationDataForAllRowsResponse& from);
  GetNextVisualizationDataForAllRowsResponse(GetNextVisualizationDataForAllRowsResponse&& from) noexcept
    : GetNextVisualizationDataForAllRowsResponse() {
    *this = ::std::move(from);
  }

  inline GetNextVisualizationDataForAllRowsResponse& operator=(const GetNextVisualizationDataForAllRowsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextVisualizationDataForAllRowsResponse& operator=(GetNextVisualizationDataForAllRowsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextVisualizationDataForAllRowsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextVisualizationDataForAllRowsResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextVisualizationDataForAllRowsResponse*>(
               &_GetNextVisualizationDataForAllRowsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(GetNextVisualizationDataForAllRowsResponse& a, GetNextVisualizationDataForAllRowsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextVisualizationDataForAllRowsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextVisualizationDataForAllRowsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextVisualizationDataForAllRowsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextVisualizationDataForAllRowsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextVisualizationDataForAllRowsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextVisualizationDataForAllRowsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextVisualizationDataForAllRowsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse";
  }
  protected:
  explicit GetNextVisualizationDataForAllRowsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kDataPerRowFieldNumber = 1,
    kTypesToIncludeFieldNumber = 3,
    kTsFieldNumber = 2,
  };
  // map<int32, .weed_tracking.DiagnosticsSnapshot> data_per_row = 1;
  int data_per_row_size() const;
  private:
  int _internal_data_per_row_size() const;
  public:
  void clear_data_per_row();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >&
      _internal_data_per_row() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >*
      _internal_mutable_data_per_row();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >&
      data_per_row() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >*
      mutable_data_per_row();

  // repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
  int types_to_include_size() const;
  private:
  int _internal_types_to_include_size() const;
  public:
  void clear_types_to_include();
  private:
  ::carbon::frontend::banding::VisualizationTypeToInclude _internal_types_to_include(int index) const;
  void _internal_add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_types_to_include();
  public:
  ::carbon::frontend::banding::VisualizationTypeToInclude types_to_include(int index) const;
  void set_types_to_include(int index, ::carbon::frontend::banding::VisualizationTypeToInclude value);
  void add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& types_to_include() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_types_to_include();

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetNextVisualizationDataForAllRowsResponse_DataPerRowEntry_DoNotUse,
      int32_t, ::weed_tracking::DiagnosticsSnapshot,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> data_per_row_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> types_to_include_;
  mutable std::atomic<int> _types_to_include_cached_byte_size_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// -------------------------------------------------------------------

class GetNextBandingStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.banding.GetNextBandingStateResponse) */ {
 public:
  inline GetNextBandingStateResponse() : GetNextBandingStateResponse(nullptr) {}
  ~GetNextBandingStateResponse() override;
  explicit constexpr GetNextBandingStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextBandingStateResponse(const GetNextBandingStateResponse& from);
  GetNextBandingStateResponse(GetNextBandingStateResponse&& from) noexcept
    : GetNextBandingStateResponse() {
    *this = ::std::move(from);
  }

  inline GetNextBandingStateResponse& operator=(const GetNextBandingStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextBandingStateResponse& operator=(GetNextBandingStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextBandingStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextBandingStateResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextBandingStateResponse*>(
               &_GetNextBandingStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(GetNextBandingStateResponse& a, GetNextBandingStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextBandingStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextBandingStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextBandingStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextBandingStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextBandingStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextBandingStateResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextBandingStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.banding.GetNextBandingStateResponse";
  }
  protected:
  explicit GetNextBandingStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBandingDefsFieldNumber = 2,
    kActiveDefUUIDFieldNumber = 3,
    kTsFieldNumber = 1,
    kIsBandingEnabledFieldNumber = 4,
    kIsDynamicBandingEnabledFieldNumber = 5,
  };
  // repeated .carbon.frontend.banding.BandingDef bandingDefs = 2;
  int bandingdefs_size() const;
  private:
  int _internal_bandingdefs_size() const;
  public:
  void clear_bandingdefs();
  ::carbon::frontend::banding::BandingDef* mutable_bandingdefs(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef >*
      mutable_bandingdefs();
  private:
  const ::carbon::frontend::banding::BandingDef& _internal_bandingdefs(int index) const;
  ::carbon::frontend::banding::BandingDef* _internal_add_bandingdefs();
  public:
  const ::carbon::frontend::banding::BandingDef& bandingdefs(int index) const;
  ::carbon::frontend::banding::BandingDef* add_bandingdefs();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef >&
      bandingdefs() const;

  // string activeDefUUID = 3;
  void clear_activedefuuid();
  const std::string& activedefuuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_activedefuuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_activedefuuid();
  PROTOBUF_NODISCARD std::string* release_activedefuuid();
  void set_allocated_activedefuuid(std::string* activedefuuid);
  private:
  const std::string& _internal_activedefuuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_activedefuuid(const std::string& value);
  std::string* _internal_mutable_activedefuuid();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // bool is_banding_enabled = 4;
  void clear_is_banding_enabled();
  bool is_banding_enabled() const;
  void set_is_banding_enabled(bool value);
  private:
  bool _internal_is_banding_enabled() const;
  void _internal_set_is_banding_enabled(bool value);
  public:

  // bool is_dynamic_banding_enabled = 5;
  void clear_is_dynamic_banding_enabled();
  bool is_dynamic_banding_enabled() const;
  void set_is_dynamic_banding_enabled(bool value);
  private:
  bool _internal_is_dynamic_banding_enabled() const;
  void _internal_set_is_dynamic_banding_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextBandingStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef > bandingdefs_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr activedefuuid_;
  ::carbon::frontend::util::Timestamp* ts_;
  bool is_banding_enabled_;
  bool is_dynamic_banding_enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fbanding_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// BandingRow

// int32 row_id = 1;
inline void BandingRow::clear_row_id() {
  row_id_ = 0;
}
inline int32_t BandingRow::_internal_row_id() const {
  return row_id_;
}
inline int32_t BandingRow::row_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.BandingRow.row_id)
  return _internal_row_id();
}
inline void BandingRow::_internal_set_row_id(int32_t value) {
  
  row_id_ = value;
}
inline void BandingRow::set_row_id(int32_t value) {
  _internal_set_row_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.BandingRow.row_id)
}

// repeated .weed_tracking.BandDefinition bands = 2;
inline int BandingRow::_internal_bands_size() const {
  return bands_.size();
}
inline int BandingRow::bands_size() const {
  return _internal_bands_size();
}
inline ::weed_tracking::BandDefinition* BandingRow::mutable_bands(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.BandingRow.bands)
  return bands_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition >*
BandingRow::mutable_bands() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.BandingRow.bands)
  return &bands_;
}
inline const ::weed_tracking::BandDefinition& BandingRow::_internal_bands(int index) const {
  return bands_.Get(index);
}
inline const ::weed_tracking::BandDefinition& BandingRow::bands(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.BandingRow.bands)
  return _internal_bands(index);
}
inline ::weed_tracking::BandDefinition* BandingRow::_internal_add_bands() {
  return bands_.Add();
}
inline ::weed_tracking::BandDefinition* BandingRow::add_bands() {
  ::weed_tracking::BandDefinition* _add = _internal_add_bands();
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.BandingRow.bands)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition >&
BandingRow::bands() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.BandingRow.bands)
  return bands_;
}

// -------------------------------------------------------------------

// BandingDef

// string name = 1;
inline void BandingDef::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& BandingDef::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.BandingDef.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BandingDef::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.BandingDef.name)
}
inline std::string* BandingDef::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.BandingDef.name)
  return _s;
}
inline const std::string& BandingDef::_internal_name() const {
  return name_.Get();
}
inline void BandingDef::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* BandingDef::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* BandingDef::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.BandingDef.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void BandingDef::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.BandingDef.name)
}

// repeated .carbon.frontend.banding.BandingRow rows = 2;
inline int BandingDef::_internal_rows_size() const {
  return rows_.size();
}
inline int BandingDef::rows_size() const {
  return _internal_rows_size();
}
inline void BandingDef::clear_rows() {
  rows_.Clear();
}
inline ::carbon::frontend::banding::BandingRow* BandingDef::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.BandingDef.rows)
  return rows_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingRow >*
BandingDef::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.BandingDef.rows)
  return &rows_;
}
inline const ::carbon::frontend::banding::BandingRow& BandingDef::_internal_rows(int index) const {
  return rows_.Get(index);
}
inline const ::carbon::frontend::banding::BandingRow& BandingDef::rows(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.BandingDef.rows)
  return _internal_rows(index);
}
inline ::carbon::frontend::banding::BandingRow* BandingDef::_internal_add_rows() {
  return rows_.Add();
}
inline ::carbon::frontend::banding::BandingRow* BandingDef::add_rows() {
  ::carbon::frontend::banding::BandingRow* _add = _internal_add_rows();
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.BandingDef.rows)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingRow >&
BandingDef::rows() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.BandingDef.rows)
  return rows_;
}

// string uuid = 3;
inline void BandingDef::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& BandingDef::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.BandingDef.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void BandingDef::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.BandingDef.uuid)
}
inline std::string* BandingDef::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.BandingDef.uuid)
  return _s;
}
inline const std::string& BandingDef::_internal_uuid() const {
  return uuid_.Get();
}
inline void BandingDef::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* BandingDef::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* BandingDef::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.BandingDef.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void BandingDef::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.BandingDef.uuid)
}

// -------------------------------------------------------------------

// SaveBandingDefRequest

// .carbon.frontend.banding.BandingDef bandingDef = 1;
inline bool SaveBandingDefRequest::_internal_has_bandingdef() const {
  return this != internal_default_instance() && bandingdef_ != nullptr;
}
inline bool SaveBandingDefRequest::has_bandingdef() const {
  return _internal_has_bandingdef();
}
inline void SaveBandingDefRequest::clear_bandingdef() {
  if (GetArenaForAllocation() == nullptr && bandingdef_ != nullptr) {
    delete bandingdef_;
  }
  bandingdef_ = nullptr;
}
inline const ::carbon::frontend::banding::BandingDef& SaveBandingDefRequest::_internal_bandingdef() const {
  const ::carbon::frontend::banding::BandingDef* p = bandingdef_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::banding::BandingDef&>(
      ::carbon::frontend::banding::_BandingDef_default_instance_);
}
inline const ::carbon::frontend::banding::BandingDef& SaveBandingDefRequest::bandingdef() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.SaveBandingDefRequest.bandingDef)
  return _internal_bandingdef();
}
inline void SaveBandingDefRequest::unsafe_arena_set_allocated_bandingdef(
    ::carbon::frontend::banding::BandingDef* bandingdef) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bandingdef_);
  }
  bandingdef_ = bandingdef;
  if (bandingdef) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.SaveBandingDefRequest.bandingDef)
}
inline ::carbon::frontend::banding::BandingDef* SaveBandingDefRequest::release_bandingdef() {
  
  ::carbon::frontend::banding::BandingDef* temp = bandingdef_;
  bandingdef_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::banding::BandingDef* SaveBandingDefRequest::unsafe_arena_release_bandingdef() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.SaveBandingDefRequest.bandingDef)
  
  ::carbon::frontend::banding::BandingDef* temp = bandingdef_;
  bandingdef_ = nullptr;
  return temp;
}
inline ::carbon::frontend::banding::BandingDef* SaveBandingDefRequest::_internal_mutable_bandingdef() {
  
  if (bandingdef_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::banding::BandingDef>(GetArenaForAllocation());
    bandingdef_ = p;
  }
  return bandingdef_;
}
inline ::carbon::frontend::banding::BandingDef* SaveBandingDefRequest::mutable_bandingdef() {
  ::carbon::frontend::banding::BandingDef* _msg = _internal_mutable_bandingdef();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.SaveBandingDefRequest.bandingDef)
  return _msg;
}
inline void SaveBandingDefRequest::set_allocated_bandingdef(::carbon::frontend::banding::BandingDef* bandingdef) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bandingdef_;
  }
  if (bandingdef) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::banding::BandingDef>::GetOwningArena(bandingdef);
    if (message_arena != submessage_arena) {
      bandingdef = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bandingdef, submessage_arena);
    }
    
  } else {
    
  }
  bandingdef_ = bandingdef;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.SaveBandingDefRequest.bandingDef)
}

// bool setActive = 2;
inline void SaveBandingDefRequest::clear_setactive() {
  setactive_ = false;
}
inline bool SaveBandingDefRequest::_internal_setactive() const {
  return setactive_;
}
inline bool SaveBandingDefRequest::setactive() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.SaveBandingDefRequest.setActive)
  return _internal_setactive();
}
inline void SaveBandingDefRequest::_internal_set_setactive(bool value) {
  
  setactive_ = value;
}
inline void SaveBandingDefRequest::set_setactive(bool value) {
  _internal_set_setactive(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.SaveBandingDefRequest.setActive)
}

// -------------------------------------------------------------------

// LoadBandingDefsResponse

// repeated .carbon.frontend.banding.BandingDef bandingDefs = 1;
inline int LoadBandingDefsResponse::_internal_bandingdefs_size() const {
  return bandingdefs_.size();
}
inline int LoadBandingDefsResponse::bandingdefs_size() const {
  return _internal_bandingdefs_size();
}
inline void LoadBandingDefsResponse::clear_bandingdefs() {
  bandingdefs_.Clear();
}
inline ::carbon::frontend::banding::BandingDef* LoadBandingDefsResponse::mutable_bandingdefs(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.LoadBandingDefsResponse.bandingDefs)
  return bandingdefs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef >*
LoadBandingDefsResponse::mutable_bandingdefs() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.LoadBandingDefsResponse.bandingDefs)
  return &bandingdefs_;
}
inline const ::carbon::frontend::banding::BandingDef& LoadBandingDefsResponse::_internal_bandingdefs(int index) const {
  return bandingdefs_.Get(index);
}
inline const ::carbon::frontend::banding::BandingDef& LoadBandingDefsResponse::bandingdefs(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.LoadBandingDefsResponse.bandingDefs)
  return _internal_bandingdefs(index);
}
inline ::carbon::frontend::banding::BandingDef* LoadBandingDefsResponse::_internal_add_bandingdefs() {
  return bandingdefs_.Add();
}
inline ::carbon::frontend::banding::BandingDef* LoadBandingDefsResponse::add_bandingdefs() {
  ::carbon::frontend::banding::BandingDef* _add = _internal_add_bandingdefs();
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.LoadBandingDefsResponse.bandingDefs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef >&
LoadBandingDefsResponse::bandingdefs() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.LoadBandingDefsResponse.bandingDefs)
  return bandingdefs_;
}

// string activeDef = 2 [deprecated = true];
inline void LoadBandingDefsResponse::clear_activedef() {
  activedef_.ClearToEmpty();
}
inline const std::string& LoadBandingDefsResponse::activedef() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.LoadBandingDefsResponse.activeDef)
  return _internal_activedef();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LoadBandingDefsResponse::set_activedef(ArgT0&& arg0, ArgT... args) {
 
 activedef_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.LoadBandingDefsResponse.activeDef)
}
inline std::string* LoadBandingDefsResponse::mutable_activedef() {
  std::string* _s = _internal_mutable_activedef();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.LoadBandingDefsResponse.activeDef)
  return _s;
}
inline const std::string& LoadBandingDefsResponse::_internal_activedef() const {
  return activedef_.Get();
}
inline void LoadBandingDefsResponse::_internal_set_activedef(const std::string& value) {
  
  activedef_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LoadBandingDefsResponse::_internal_mutable_activedef() {
  
  return activedef_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LoadBandingDefsResponse::release_activedef() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.LoadBandingDefsResponse.activeDef)
  return activedef_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LoadBandingDefsResponse::set_allocated_activedef(std::string* activedef) {
  if (activedef != nullptr) {
    
  } else {
    
  }
  activedef_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), activedef,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (activedef_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    activedef_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.LoadBandingDefsResponse.activeDef)
}

// string activeDefUUID = 3;
inline void LoadBandingDefsResponse::clear_activedefuuid() {
  activedefuuid_.ClearToEmpty();
}
inline const std::string& LoadBandingDefsResponse::activedefuuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.LoadBandingDefsResponse.activeDefUUID)
  return _internal_activedefuuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LoadBandingDefsResponse::set_activedefuuid(ArgT0&& arg0, ArgT... args) {
 
 activedefuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.LoadBandingDefsResponse.activeDefUUID)
}
inline std::string* LoadBandingDefsResponse::mutable_activedefuuid() {
  std::string* _s = _internal_mutable_activedefuuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.LoadBandingDefsResponse.activeDefUUID)
  return _s;
}
inline const std::string& LoadBandingDefsResponse::_internal_activedefuuid() const {
  return activedefuuid_.Get();
}
inline void LoadBandingDefsResponse::_internal_set_activedefuuid(const std::string& value) {
  
  activedefuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LoadBandingDefsResponse::_internal_mutable_activedefuuid() {
  
  return activedefuuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LoadBandingDefsResponse::release_activedefuuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.LoadBandingDefsResponse.activeDefUUID)
  return activedefuuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LoadBandingDefsResponse::set_allocated_activedefuuid(std::string* activedefuuid) {
  if (activedefuuid != nullptr) {
    
  } else {
    
  }
  activedefuuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), activedefuuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (activedefuuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    activedefuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.LoadBandingDefsResponse.activeDefUUID)
}

// -------------------------------------------------------------------

// SetActiveBandingDefRequest

// string name = 1 [deprecated = true];
inline void SetActiveBandingDefRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& SetActiveBandingDefRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.SetActiveBandingDefRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveBandingDefRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.SetActiveBandingDefRequest.name)
}
inline std::string* SetActiveBandingDefRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.SetActiveBandingDefRequest.name)
  return _s;
}
inline const std::string& SetActiveBandingDefRequest::_internal_name() const {
  return name_.Get();
}
inline void SetActiveBandingDefRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveBandingDefRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveBandingDefRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.SetActiveBandingDefRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveBandingDefRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.SetActiveBandingDefRequest.name)
}

// string uuid = 2;
inline void SetActiveBandingDefRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& SetActiveBandingDefRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.SetActiveBandingDefRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveBandingDefRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.SetActiveBandingDefRequest.uuid)
}
inline std::string* SetActiveBandingDefRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.SetActiveBandingDefRequest.uuid)
  return _s;
}
inline const std::string& SetActiveBandingDefRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void SetActiveBandingDefRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveBandingDefRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveBandingDefRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.SetActiveBandingDefRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveBandingDefRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.SetActiveBandingDefRequest.uuid)
}

// -------------------------------------------------------------------

// GetActiveBandingDefResponse

// string name = 1 [deprecated = true];
inline void GetActiveBandingDefResponse::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& GetActiveBandingDefResponse::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetActiveBandingDefResponse.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetActiveBandingDefResponse::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetActiveBandingDefResponse.name)
}
inline std::string* GetActiveBandingDefResponse::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetActiveBandingDefResponse.name)
  return _s;
}
inline const std::string& GetActiveBandingDefResponse::_internal_name() const {
  return name_.Get();
}
inline void GetActiveBandingDefResponse::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetActiveBandingDefResponse::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetActiveBandingDefResponse::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetActiveBandingDefResponse.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetActiveBandingDefResponse::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetActiveBandingDefResponse.name)
}

// string uuid = 2;
inline void GetActiveBandingDefResponse::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& GetActiveBandingDefResponse::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetActiveBandingDefResponse.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetActiveBandingDefResponse::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetActiveBandingDefResponse.uuid)
}
inline std::string* GetActiveBandingDefResponse::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetActiveBandingDefResponse.uuid)
  return _s;
}
inline const std::string& GetActiveBandingDefResponse::_internal_uuid() const {
  return uuid_.Get();
}
inline void GetActiveBandingDefResponse::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetActiveBandingDefResponse::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetActiveBandingDefResponse::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetActiveBandingDefResponse.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetActiveBandingDefResponse::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetActiveBandingDefResponse.uuid)
}

// -------------------------------------------------------------------

// DeleteBandingDefRequest

// string name = 1 [deprecated = true];
inline void DeleteBandingDefRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& DeleteBandingDefRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.DeleteBandingDefRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteBandingDefRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.DeleteBandingDefRequest.name)
}
inline std::string* DeleteBandingDefRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.DeleteBandingDefRequest.name)
  return _s;
}
inline const std::string& DeleteBandingDefRequest::_internal_name() const {
  return name_.Get();
}
inline void DeleteBandingDefRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteBandingDefRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteBandingDefRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.DeleteBandingDefRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteBandingDefRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.DeleteBandingDefRequest.name)
}

// string uuid = 2;
inline void DeleteBandingDefRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& DeleteBandingDefRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.DeleteBandingDefRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteBandingDefRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.DeleteBandingDefRequest.uuid)
}
inline std::string* DeleteBandingDefRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.DeleteBandingDefRequest.uuid)
  return _s;
}
inline const std::string& DeleteBandingDefRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void DeleteBandingDefRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteBandingDefRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteBandingDefRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.DeleteBandingDefRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteBandingDefRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.DeleteBandingDefRequest.uuid)
}

// -------------------------------------------------------------------

// VisualizationData

// int32 x_mm = 1;
inline void VisualizationData::clear_x_mm() {
  x_mm_ = 0;
}
inline int32_t VisualizationData::_internal_x_mm() const {
  return x_mm_;
}
inline int32_t VisualizationData::x_mm() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.VisualizationData.x_mm)
  return _internal_x_mm();
}
inline void VisualizationData::_internal_set_x_mm(int32_t value) {
  
  x_mm_ = value;
}
inline void VisualizationData::set_x_mm(int32_t value) {
  _internal_set_x_mm(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.VisualizationData.x_mm)
}

// int32 y_mm = 2;
inline void VisualizationData::clear_y_mm() {
  y_mm_ = 0;
}
inline int32_t VisualizationData::_internal_y_mm() const {
  return y_mm_;
}
inline int32_t VisualizationData::y_mm() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.VisualizationData.y_mm)
  return _internal_y_mm();
}
inline void VisualizationData::_internal_set_y_mm(int32_t value) {
  
  y_mm_ = value;
}
inline void VisualizationData::set_y_mm(int32_t value) {
  _internal_set_y_mm(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.VisualizationData.y_mm)
}

// int32 z_mm = 3;
inline void VisualizationData::clear_z_mm() {
  z_mm_ = 0;
}
inline int32_t VisualizationData::_internal_z_mm() const {
  return z_mm_;
}
inline int32_t VisualizationData::z_mm() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.VisualizationData.z_mm)
  return _internal_z_mm();
}
inline void VisualizationData::_internal_set_z_mm(int32_t value) {
  
  z_mm_ = value;
}
inline void VisualizationData::set_z_mm(int32_t value) {
  _internal_set_z_mm(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.VisualizationData.z_mm)
}

// bool is_weed = 4;
inline void VisualizationData::clear_is_weed() {
  is_weed_ = false;
}
inline bool VisualizationData::_internal_is_weed() const {
  return is_weed_;
}
inline bool VisualizationData::is_weed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.VisualizationData.is_weed)
  return _internal_is_weed();
}
inline void VisualizationData::_internal_set_is_weed(bool value) {
  
  is_weed_ = value;
}
inline void VisualizationData::set_is_weed(bool value) {
  _internal_set_is_weed(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.VisualizationData.is_weed)
}

// -------------------------------------------------------------------

// GetNextVisualizationDataRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextVisualizationDataRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextVisualizationDataRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationDataRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationDataRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataRequest.ts)
  return _internal_ts();
}
inline void GetNextVisualizationDataRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextVisualizationDataRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextVisualizationDataRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationDataRequest.ts)
  return _msg;
}
inline void GetNextVisualizationDataRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextVisualizationDataRequest.ts)
}

// int32 row_id = 2;
inline void GetNextVisualizationDataRequest::clear_row_id() {
  row_id_ = 0;
}
inline int32_t GetNextVisualizationDataRequest::_internal_row_id() const {
  return row_id_;
}
inline int32_t GetNextVisualizationDataRequest::row_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataRequest.row_id)
  return _internal_row_id();
}
inline void GetNextVisualizationDataRequest::_internal_set_row_id(int32_t value) {
  
  row_id_ = value;
}
inline void GetNextVisualizationDataRequest::set_row_id(int32_t value) {
  _internal_set_row_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetNextVisualizationDataRequest.row_id)
}

// repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
inline int GetNextVisualizationDataRequest::_internal_types_to_include_size() const {
  return types_to_include_.size();
}
inline int GetNextVisualizationDataRequest::types_to_include_size() const {
  return _internal_types_to_include_size();
}
inline void GetNextVisualizationDataRequest::clear_types_to_include() {
  types_to_include_.Clear();
}
inline ::carbon::frontend::banding::VisualizationTypeToInclude GetNextVisualizationDataRequest::_internal_types_to_include(int index) const {
  return static_cast< ::carbon::frontend::banding::VisualizationTypeToInclude >(types_to_include_.Get(index));
}
inline ::carbon::frontend::banding::VisualizationTypeToInclude GetNextVisualizationDataRequest::types_to_include(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataRequest.types_to_include)
  return _internal_types_to_include(index);
}
inline void GetNextVisualizationDataRequest::set_types_to_include(int index, ::carbon::frontend::banding::VisualizationTypeToInclude value) {
  types_to_include_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetNextVisualizationDataRequest.types_to_include)
}
inline void GetNextVisualizationDataRequest::_internal_add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value) {
  types_to_include_.Add(value);
}
inline void GetNextVisualizationDataRequest::add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value) {
  _internal_add_types_to_include(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.GetNextVisualizationDataRequest.types_to_include)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
GetNextVisualizationDataRequest::types_to_include() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.GetNextVisualizationDataRequest.types_to_include)
  return types_to_include_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
GetNextVisualizationDataRequest::_internal_mutable_types_to_include() {
  return &types_to_include_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
GetNextVisualizationDataRequest::mutable_types_to_include() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.GetNextVisualizationDataRequest.types_to_include)
  return _internal_mutable_types_to_include();
}

// .carbon.frontend.banding.ThresholdFilters threshold_filters = 4;
inline bool GetNextVisualizationDataRequest::_internal_has_threshold_filters() const {
  return this != internal_default_instance() && threshold_filters_ != nullptr;
}
inline bool GetNextVisualizationDataRequest::has_threshold_filters() const {
  return _internal_has_threshold_filters();
}
inline void GetNextVisualizationDataRequest::clear_threshold_filters() {
  if (GetArenaForAllocation() == nullptr && threshold_filters_ != nullptr) {
    delete threshold_filters_;
  }
  threshold_filters_ = nullptr;
}
inline const ::carbon::frontend::banding::ThresholdFilters& GetNextVisualizationDataRequest::_internal_threshold_filters() const {
  const ::carbon::frontend::banding::ThresholdFilters* p = threshold_filters_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::banding::ThresholdFilters&>(
      ::carbon::frontend::banding::_ThresholdFilters_default_instance_);
}
inline const ::carbon::frontend::banding::ThresholdFilters& GetNextVisualizationDataRequest::threshold_filters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataRequest.threshold_filters)
  return _internal_threshold_filters();
}
inline void GetNextVisualizationDataRequest::unsafe_arena_set_allocated_threshold_filters(
    ::carbon::frontend::banding::ThresholdFilters* threshold_filters) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(threshold_filters_);
  }
  threshold_filters_ = threshold_filters;
  if (threshold_filters) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextVisualizationDataRequest.threshold_filters)
}
inline ::carbon::frontend::banding::ThresholdFilters* GetNextVisualizationDataRequest::release_threshold_filters() {
  
  ::carbon::frontend::banding::ThresholdFilters* temp = threshold_filters_;
  threshold_filters_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::banding::ThresholdFilters* GetNextVisualizationDataRequest::unsafe_arena_release_threshold_filters() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextVisualizationDataRequest.threshold_filters)
  
  ::carbon::frontend::banding::ThresholdFilters* temp = threshold_filters_;
  threshold_filters_ = nullptr;
  return temp;
}
inline ::carbon::frontend::banding::ThresholdFilters* GetNextVisualizationDataRequest::_internal_mutable_threshold_filters() {
  
  if (threshold_filters_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::banding::ThresholdFilters>(GetArenaForAllocation());
    threshold_filters_ = p;
  }
  return threshold_filters_;
}
inline ::carbon::frontend::banding::ThresholdFilters* GetNextVisualizationDataRequest::mutable_threshold_filters() {
  ::carbon::frontend::banding::ThresholdFilters* _msg = _internal_mutable_threshold_filters();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationDataRequest.threshold_filters)
  return _msg;
}
inline void GetNextVisualizationDataRequest::set_allocated_threshold_filters(::carbon::frontend::banding::ThresholdFilters* threshold_filters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete threshold_filters_;
  }
  if (threshold_filters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::banding::ThresholdFilters>::GetOwningArena(threshold_filters);
    if (message_arena != submessage_arena) {
      threshold_filters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, threshold_filters, submessage_arena);
    }
    
  } else {
    
  }
  threshold_filters_ = threshold_filters;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextVisualizationDataRequest.threshold_filters)
}

// -------------------------------------------------------------------

// GetNextVisualizationDataResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextVisualizationDataResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextVisualizationDataResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationDataResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationDataResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataResponse.ts)
  return _internal_ts();
}
inline void GetNextVisualizationDataResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextVisualizationDataResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextVisualizationDataResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationDataResponse.ts)
  return _msg;
}
inline void GetNextVisualizationDataResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextVisualizationDataResponse.ts)
}

// repeated .carbon.frontend.banding.VisualizationData data = 2;
inline int GetNextVisualizationDataResponse::_internal_data_size() const {
  return data_.size();
}
inline int GetNextVisualizationDataResponse::data_size() const {
  return _internal_data_size();
}
inline void GetNextVisualizationDataResponse::clear_data() {
  data_.Clear();
}
inline ::carbon::frontend::banding::VisualizationData* GetNextVisualizationDataResponse::mutable_data(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationDataResponse.data)
  return data_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::VisualizationData >*
GetNextVisualizationDataResponse::mutable_data() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.GetNextVisualizationDataResponse.data)
  return &data_;
}
inline const ::carbon::frontend::banding::VisualizationData& GetNextVisualizationDataResponse::_internal_data(int index) const {
  return data_.Get(index);
}
inline const ::carbon::frontend::banding::VisualizationData& GetNextVisualizationDataResponse::data(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataResponse.data)
  return _internal_data(index);
}
inline ::carbon::frontend::banding::VisualizationData* GetNextVisualizationDataResponse::_internal_add_data() {
  return data_.Add();
}
inline ::carbon::frontend::banding::VisualizationData* GetNextVisualizationDataResponse::add_data() {
  ::carbon::frontend::banding::VisualizationData* _add = _internal_add_data();
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.GetNextVisualizationDataResponse.data)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::VisualizationData >&
GetNextVisualizationDataResponse::data() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.GetNextVisualizationDataResponse.data)
  return data_;
}

// repeated .weed_tracking.BandDefinition bands = 3;
inline int GetNextVisualizationDataResponse::_internal_bands_size() const {
  return bands_.size();
}
inline int GetNextVisualizationDataResponse::bands_size() const {
  return _internal_bands_size();
}
inline ::weed_tracking::BandDefinition* GetNextVisualizationDataResponse::mutable_bands(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationDataResponse.bands)
  return bands_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition >*
GetNextVisualizationDataResponse::mutable_bands() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.GetNextVisualizationDataResponse.bands)
  return &bands_;
}
inline const ::weed_tracking::BandDefinition& GetNextVisualizationDataResponse::_internal_bands(int index) const {
  return bands_.Get(index);
}
inline const ::weed_tracking::BandDefinition& GetNextVisualizationDataResponse::bands(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataResponse.bands)
  return _internal_bands(index);
}
inline ::weed_tracking::BandDefinition* GetNextVisualizationDataResponse::_internal_add_bands() {
  return bands_.Add();
}
inline ::weed_tracking::BandDefinition* GetNextVisualizationDataResponse::add_bands() {
  ::weed_tracking::BandDefinition* _add = _internal_add_bands();
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.GetNextVisualizationDataResponse.bands)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::BandDefinition >&
GetNextVisualizationDataResponse::bands() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.GetNextVisualizationDataResponse.bands)
  return bands_;
}

// -------------------------------------------------------------------

// GetNextVisualizationData2Response

// .weed_tracking.DiagnosticsSnapshot data = 1;
inline bool GetNextVisualizationData2Response::_internal_has_data() const {
  return this != internal_default_instance() && data_ != nullptr;
}
inline bool GetNextVisualizationData2Response::has_data() const {
  return _internal_has_data();
}
inline const ::weed_tracking::DiagnosticsSnapshot& GetNextVisualizationData2Response::_internal_data() const {
  const ::weed_tracking::DiagnosticsSnapshot* p = data_;
  return p != nullptr ? *p : reinterpret_cast<const ::weed_tracking::DiagnosticsSnapshot&>(
      ::weed_tracking::_DiagnosticsSnapshot_default_instance_);
}
inline const ::weed_tracking::DiagnosticsSnapshot& GetNextVisualizationData2Response::data() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationData2Response.data)
  return _internal_data();
}
inline void GetNextVisualizationData2Response::unsafe_arena_set_allocated_data(
    ::weed_tracking::DiagnosticsSnapshot* data) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  data_ = data;
  if (data) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextVisualizationData2Response.data)
}
inline ::weed_tracking::DiagnosticsSnapshot* GetNextVisualizationData2Response::release_data() {
  
  ::weed_tracking::DiagnosticsSnapshot* temp = data_;
  data_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::weed_tracking::DiagnosticsSnapshot* GetNextVisualizationData2Response::unsafe_arena_release_data() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextVisualizationData2Response.data)
  
  ::weed_tracking::DiagnosticsSnapshot* temp = data_;
  data_ = nullptr;
  return temp;
}
inline ::weed_tracking::DiagnosticsSnapshot* GetNextVisualizationData2Response::_internal_mutable_data() {
  
  if (data_ == nullptr) {
    auto* p = CreateMaybeMessage<::weed_tracking::DiagnosticsSnapshot>(GetArenaForAllocation());
    data_ = p;
  }
  return data_;
}
inline ::weed_tracking::DiagnosticsSnapshot* GetNextVisualizationData2Response::mutable_data() {
  ::weed_tracking::DiagnosticsSnapshot* _msg = _internal_mutable_data();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationData2Response.data)
  return _msg;
}
inline void GetNextVisualizationData2Response::set_allocated_data(::weed_tracking::DiagnosticsSnapshot* data) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(data_);
  }
  if (data) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(data));
    if (message_arena != submessage_arena) {
      data = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, data, submessage_arena);
    }
    
  } else {
    
  }
  data_ = data;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextVisualizationData2Response.data)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextVisualizationData2Response::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextVisualizationData2Response::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationData2Response::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationData2Response::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationData2Response.ts)
  return _internal_ts();
}
inline void GetNextVisualizationData2Response::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextVisualizationData2Response.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationData2Response::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationData2Response::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextVisualizationData2Response.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationData2Response::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationData2Response::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationData2Response.ts)
  return _msg;
}
inline void GetNextVisualizationData2Response::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextVisualizationData2Response.ts)
}

// -------------------------------------------------------------------

// GetDimensionsRequest

// int32 row_id = 1;
inline void GetDimensionsRequest::clear_row_id() {
  row_id_ = 0;
}
inline int32_t GetDimensionsRequest::_internal_row_id() const {
  return row_id_;
}
inline int32_t GetDimensionsRequest::row_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetDimensionsRequest.row_id)
  return _internal_row_id();
}
inline void GetDimensionsRequest::_internal_set_row_id(int32_t value) {
  
  row_id_ = value;
}
inline void GetDimensionsRequest::set_row_id(int32_t value) {
  _internal_set_row_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetDimensionsRequest.row_id)
}

// -------------------------------------------------------------------

// SetBandingEnabledRequest

// bool enabled = 1;
inline void SetBandingEnabledRequest::clear_enabled() {
  enabled_ = false;
}
inline bool SetBandingEnabledRequest::_internal_enabled() const {
  return enabled_;
}
inline bool SetBandingEnabledRequest::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.SetBandingEnabledRequest.enabled)
  return _internal_enabled();
}
inline void SetBandingEnabledRequest::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void SetBandingEnabledRequest::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.SetBandingEnabledRequest.enabled)
}

// -------------------------------------------------------------------

// SetBandingEnabledResponse

// -------------------------------------------------------------------

// IsBandingEnabledResponse

// bool enabled = 1;
inline void IsBandingEnabledResponse::clear_enabled() {
  enabled_ = false;
}
inline bool IsBandingEnabledResponse::_internal_enabled() const {
  return enabled_;
}
inline bool IsBandingEnabledResponse::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.IsBandingEnabledResponse.enabled)
  return _internal_enabled();
}
inline void IsBandingEnabledResponse::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void IsBandingEnabledResponse::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.IsBandingEnabledResponse.enabled)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetVisualizationMetadataResponse

// map<int32, float> crop_safety_radius_mm_per_row = 1;
inline int GetVisualizationMetadataResponse::_internal_crop_safety_radius_mm_per_row_size() const {
  return crop_safety_radius_mm_per_row_.size();
}
inline int GetVisualizationMetadataResponse::crop_safety_radius_mm_per_row_size() const {
  return _internal_crop_safety_radius_mm_per_row_size();
}
inline void GetVisualizationMetadataResponse::clear_crop_safety_radius_mm_per_row() {
  crop_safety_radius_mm_per_row_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >&
GetVisualizationMetadataResponse::_internal_crop_safety_radius_mm_per_row() const {
  return crop_safety_radius_mm_per_row_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >&
GetVisualizationMetadataResponse::crop_safety_radius_mm_per_row() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.banding.GetVisualizationMetadataResponse.crop_safety_radius_mm_per_row)
  return _internal_crop_safety_radius_mm_per_row();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >*
GetVisualizationMetadataResponse::_internal_mutable_crop_safety_radius_mm_per_row() {
  return crop_safety_radius_mm_per_row_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, float >*
GetVisualizationMetadataResponse::mutable_crop_safety_radius_mm_per_row() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.banding.GetVisualizationMetadataResponse.crop_safety_radius_mm_per_row)
  return _internal_mutable_crop_safety_radius_mm_per_row();
}

// -------------------------------------------------------------------

// ThresholdFilter

// .carbon.frontend.banding.ThresholdState weeding = 1;
inline void ThresholdFilter::clear_weeding() {
  weeding_ = 0;
}
inline ::carbon::frontend::banding::ThresholdState ThresholdFilter::_internal_weeding() const {
  return static_cast< ::carbon::frontend::banding::ThresholdState >(weeding_);
}
inline ::carbon::frontend::banding::ThresholdState ThresholdFilter::weeding() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.ThresholdFilter.weeding)
  return _internal_weeding();
}
inline void ThresholdFilter::_internal_set_weeding(::carbon::frontend::banding::ThresholdState value) {
  
  weeding_ = value;
}
inline void ThresholdFilter::set_weeding(::carbon::frontend::banding::ThresholdState value) {
  _internal_set_weeding(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.ThresholdFilter.weeding)
}

// .carbon.frontend.banding.ThresholdState thinning = 2;
inline void ThresholdFilter::clear_thinning() {
  thinning_ = 0;
}
inline ::carbon::frontend::banding::ThresholdState ThresholdFilter::_internal_thinning() const {
  return static_cast< ::carbon::frontend::banding::ThresholdState >(thinning_);
}
inline ::carbon::frontend::banding::ThresholdState ThresholdFilter::thinning() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.ThresholdFilter.thinning)
  return _internal_thinning();
}
inline void ThresholdFilter::_internal_set_thinning(::carbon::frontend::banding::ThresholdState value) {
  
  thinning_ = value;
}
inline void ThresholdFilter::set_thinning(::carbon::frontend::banding::ThresholdState value) {
  _internal_set_thinning(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.ThresholdFilter.thinning)
}

// .carbon.frontend.banding.ThresholdState banding = 3;
inline void ThresholdFilter::clear_banding() {
  banding_ = 0;
}
inline ::carbon::frontend::banding::ThresholdState ThresholdFilter::_internal_banding() const {
  return static_cast< ::carbon::frontend::banding::ThresholdState >(banding_);
}
inline ::carbon::frontend::banding::ThresholdState ThresholdFilter::banding() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.ThresholdFilter.banding)
  return _internal_banding();
}
inline void ThresholdFilter::_internal_set_banding(::carbon::frontend::banding::ThresholdState value) {
  
  banding_ = value;
}
inline void ThresholdFilter::set_banding(::carbon::frontend::banding::ThresholdState value) {
  _internal_set_banding(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.ThresholdFilter.banding)
}

// -------------------------------------------------------------------

// ThresholdFilters

// .carbon.frontend.banding.ThresholdFilter crop = 1;
inline bool ThresholdFilters::_internal_has_crop() const {
  return this != internal_default_instance() && crop_ != nullptr;
}
inline bool ThresholdFilters::has_crop() const {
  return _internal_has_crop();
}
inline void ThresholdFilters::clear_crop() {
  if (GetArenaForAllocation() == nullptr && crop_ != nullptr) {
    delete crop_;
  }
  crop_ = nullptr;
}
inline const ::carbon::frontend::banding::ThresholdFilter& ThresholdFilters::_internal_crop() const {
  const ::carbon::frontend::banding::ThresholdFilter* p = crop_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::banding::ThresholdFilter&>(
      ::carbon::frontend::banding::_ThresholdFilter_default_instance_);
}
inline const ::carbon::frontend::banding::ThresholdFilter& ThresholdFilters::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.ThresholdFilters.crop)
  return _internal_crop();
}
inline void ThresholdFilters::unsafe_arena_set_allocated_crop(
    ::carbon::frontend::banding::ThresholdFilter* crop) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(crop_);
  }
  crop_ = crop;
  if (crop) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.ThresholdFilters.crop)
}
inline ::carbon::frontend::banding::ThresholdFilter* ThresholdFilters::release_crop() {
  
  ::carbon::frontend::banding::ThresholdFilter* temp = crop_;
  crop_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::banding::ThresholdFilter* ThresholdFilters::unsafe_arena_release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.ThresholdFilters.crop)
  
  ::carbon::frontend::banding::ThresholdFilter* temp = crop_;
  crop_ = nullptr;
  return temp;
}
inline ::carbon::frontend::banding::ThresholdFilter* ThresholdFilters::_internal_mutable_crop() {
  
  if (crop_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::banding::ThresholdFilter>(GetArenaForAllocation());
    crop_ = p;
  }
  return crop_;
}
inline ::carbon::frontend::banding::ThresholdFilter* ThresholdFilters::mutable_crop() {
  ::carbon::frontend::banding::ThresholdFilter* _msg = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.ThresholdFilters.crop)
  return _msg;
}
inline void ThresholdFilters::set_allocated_crop(::carbon::frontend::banding::ThresholdFilter* crop) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete crop_;
  }
  if (crop) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::banding::ThresholdFilter>::GetOwningArena(crop);
    if (message_arena != submessage_arena) {
      crop = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, crop, submessage_arena);
    }
    
  } else {
    
  }
  crop_ = crop;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.ThresholdFilters.crop)
}

// .carbon.frontend.banding.ThresholdFilter weed = 2;
inline bool ThresholdFilters::_internal_has_weed() const {
  return this != internal_default_instance() && weed_ != nullptr;
}
inline bool ThresholdFilters::has_weed() const {
  return _internal_has_weed();
}
inline void ThresholdFilters::clear_weed() {
  if (GetArenaForAllocation() == nullptr && weed_ != nullptr) {
    delete weed_;
  }
  weed_ = nullptr;
}
inline const ::carbon::frontend::banding::ThresholdFilter& ThresholdFilters::_internal_weed() const {
  const ::carbon::frontend::banding::ThresholdFilter* p = weed_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::banding::ThresholdFilter&>(
      ::carbon::frontend::banding::_ThresholdFilter_default_instance_);
}
inline const ::carbon::frontend::banding::ThresholdFilter& ThresholdFilters::weed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.ThresholdFilters.weed)
  return _internal_weed();
}
inline void ThresholdFilters::unsafe_arena_set_allocated_weed(
    ::carbon::frontend::banding::ThresholdFilter* weed) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weed_);
  }
  weed_ = weed;
  if (weed) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.ThresholdFilters.weed)
}
inline ::carbon::frontend::banding::ThresholdFilter* ThresholdFilters::release_weed() {
  
  ::carbon::frontend::banding::ThresholdFilter* temp = weed_;
  weed_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::banding::ThresholdFilter* ThresholdFilters::unsafe_arena_release_weed() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.ThresholdFilters.weed)
  
  ::carbon::frontend::banding::ThresholdFilter* temp = weed_;
  weed_ = nullptr;
  return temp;
}
inline ::carbon::frontend::banding::ThresholdFilter* ThresholdFilters::_internal_mutable_weed() {
  
  if (weed_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::banding::ThresholdFilter>(GetArenaForAllocation());
    weed_ = p;
  }
  return weed_;
}
inline ::carbon::frontend::banding::ThresholdFilter* ThresholdFilters::mutable_weed() {
  ::carbon::frontend::banding::ThresholdFilter* _msg = _internal_mutable_weed();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.ThresholdFilters.weed)
  return _msg;
}
inline void ThresholdFilters::set_allocated_weed(::carbon::frontend::banding::ThresholdFilter* weed) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete weed_;
  }
  if (weed) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::banding::ThresholdFilter>::GetOwningArena(weed);
    if (message_arena != submessage_arena) {
      weed = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, weed, submessage_arena);
    }
    
  } else {
    
  }
  weed_ = weed;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.ThresholdFilters.weed)
}

// -------------------------------------------------------------------

// GetNextVisualizationDataForAllRowsRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextVisualizationDataForAllRowsRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextVisualizationDataForAllRowsRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationDataForAllRowsRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationDataForAllRowsRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.ts)
  return _internal_ts();
}
inline void GetNextVisualizationDataForAllRowsRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataForAllRowsRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataForAllRowsRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataForAllRowsRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataForAllRowsRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.ts)
  return _msg;
}
inline void GetNextVisualizationDataForAllRowsRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.ts)
}

// repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 2;
inline int GetNextVisualizationDataForAllRowsRequest::_internal_types_to_include_size() const {
  return types_to_include_.size();
}
inline int GetNextVisualizationDataForAllRowsRequest::types_to_include_size() const {
  return _internal_types_to_include_size();
}
inline void GetNextVisualizationDataForAllRowsRequest::clear_types_to_include() {
  types_to_include_.Clear();
}
inline ::carbon::frontend::banding::VisualizationTypeToInclude GetNextVisualizationDataForAllRowsRequest::_internal_types_to_include(int index) const {
  return static_cast< ::carbon::frontend::banding::VisualizationTypeToInclude >(types_to_include_.Get(index));
}
inline ::carbon::frontend::banding::VisualizationTypeToInclude GetNextVisualizationDataForAllRowsRequest::types_to_include(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.types_to_include)
  return _internal_types_to_include(index);
}
inline void GetNextVisualizationDataForAllRowsRequest::set_types_to_include(int index, ::carbon::frontend::banding::VisualizationTypeToInclude value) {
  types_to_include_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.types_to_include)
}
inline void GetNextVisualizationDataForAllRowsRequest::_internal_add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value) {
  types_to_include_.Add(value);
}
inline void GetNextVisualizationDataForAllRowsRequest::add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value) {
  _internal_add_types_to_include(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.types_to_include)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
GetNextVisualizationDataForAllRowsRequest::types_to_include() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.types_to_include)
  return types_to_include_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
GetNextVisualizationDataForAllRowsRequest::_internal_mutable_types_to_include() {
  return &types_to_include_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
GetNextVisualizationDataForAllRowsRequest::mutable_types_to_include() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.types_to_include)
  return _internal_mutable_types_to_include();
}

// .carbon.frontend.banding.ThresholdFilters threshold_filters = 3;
inline bool GetNextVisualizationDataForAllRowsRequest::_internal_has_threshold_filters() const {
  return this != internal_default_instance() && threshold_filters_ != nullptr;
}
inline bool GetNextVisualizationDataForAllRowsRequest::has_threshold_filters() const {
  return _internal_has_threshold_filters();
}
inline void GetNextVisualizationDataForAllRowsRequest::clear_threshold_filters() {
  if (GetArenaForAllocation() == nullptr && threshold_filters_ != nullptr) {
    delete threshold_filters_;
  }
  threshold_filters_ = nullptr;
}
inline const ::carbon::frontend::banding::ThresholdFilters& GetNextVisualizationDataForAllRowsRequest::_internal_threshold_filters() const {
  const ::carbon::frontend::banding::ThresholdFilters* p = threshold_filters_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::banding::ThresholdFilters&>(
      ::carbon::frontend::banding::_ThresholdFilters_default_instance_);
}
inline const ::carbon::frontend::banding::ThresholdFilters& GetNextVisualizationDataForAllRowsRequest::threshold_filters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.threshold_filters)
  return _internal_threshold_filters();
}
inline void GetNextVisualizationDataForAllRowsRequest::unsafe_arena_set_allocated_threshold_filters(
    ::carbon::frontend::banding::ThresholdFilters* threshold_filters) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(threshold_filters_);
  }
  threshold_filters_ = threshold_filters;
  if (threshold_filters) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.threshold_filters)
}
inline ::carbon::frontend::banding::ThresholdFilters* GetNextVisualizationDataForAllRowsRequest::release_threshold_filters() {
  
  ::carbon::frontend::banding::ThresholdFilters* temp = threshold_filters_;
  threshold_filters_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::banding::ThresholdFilters* GetNextVisualizationDataForAllRowsRequest::unsafe_arena_release_threshold_filters() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.threshold_filters)
  
  ::carbon::frontend::banding::ThresholdFilters* temp = threshold_filters_;
  threshold_filters_ = nullptr;
  return temp;
}
inline ::carbon::frontend::banding::ThresholdFilters* GetNextVisualizationDataForAllRowsRequest::_internal_mutable_threshold_filters() {
  
  if (threshold_filters_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::banding::ThresholdFilters>(GetArenaForAllocation());
    threshold_filters_ = p;
  }
  return threshold_filters_;
}
inline ::carbon::frontend::banding::ThresholdFilters* GetNextVisualizationDataForAllRowsRequest::mutable_threshold_filters() {
  ::carbon::frontend::banding::ThresholdFilters* _msg = _internal_mutable_threshold_filters();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.threshold_filters)
  return _msg;
}
inline void GetNextVisualizationDataForAllRowsRequest::set_allocated_threshold_filters(::carbon::frontend::banding::ThresholdFilters* threshold_filters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete threshold_filters_;
  }
  if (threshold_filters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::banding::ThresholdFilters>::GetOwningArena(threshold_filters);
    if (message_arena != submessage_arena) {
      threshold_filters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, threshold_filters, submessage_arena);
    }
    
  } else {
    
  }
  threshold_filters_ = threshold_filters;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.threshold_filters)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetNextVisualizationDataForAllRowsResponse

// map<int32, .weed_tracking.DiagnosticsSnapshot> data_per_row = 1;
inline int GetNextVisualizationDataForAllRowsResponse::_internal_data_per_row_size() const {
  return data_per_row_.size();
}
inline int GetNextVisualizationDataForAllRowsResponse::data_per_row_size() const {
  return _internal_data_per_row_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >&
GetNextVisualizationDataForAllRowsResponse::_internal_data_per_row() const {
  return data_per_row_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >&
GetNextVisualizationDataForAllRowsResponse::data_per_row() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.data_per_row)
  return _internal_data_per_row();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >*
GetNextVisualizationDataForAllRowsResponse::_internal_mutable_data_per_row() {
  return data_per_row_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::DiagnosticsSnapshot >*
GetNextVisualizationDataForAllRowsResponse::mutable_data_per_row() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.data_per_row)
  return _internal_mutable_data_per_row();
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextVisualizationDataForAllRowsResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextVisualizationDataForAllRowsResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationDataForAllRowsResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextVisualizationDataForAllRowsResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.ts)
  return _internal_ts();
}
inline void GetNextVisualizationDataForAllRowsResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataForAllRowsResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataForAllRowsResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataForAllRowsResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextVisualizationDataForAllRowsResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.ts)
  return _msg;
}
inline void GetNextVisualizationDataForAllRowsResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.ts)
}

// repeated .carbon.frontend.banding.VisualizationTypeToInclude types_to_include = 3;
inline int GetNextVisualizationDataForAllRowsResponse::_internal_types_to_include_size() const {
  return types_to_include_.size();
}
inline int GetNextVisualizationDataForAllRowsResponse::types_to_include_size() const {
  return _internal_types_to_include_size();
}
inline void GetNextVisualizationDataForAllRowsResponse::clear_types_to_include() {
  types_to_include_.Clear();
}
inline ::carbon::frontend::banding::VisualizationTypeToInclude GetNextVisualizationDataForAllRowsResponse::_internal_types_to_include(int index) const {
  return static_cast< ::carbon::frontend::banding::VisualizationTypeToInclude >(types_to_include_.Get(index));
}
inline ::carbon::frontend::banding::VisualizationTypeToInclude GetNextVisualizationDataForAllRowsResponse::types_to_include(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.types_to_include)
  return _internal_types_to_include(index);
}
inline void GetNextVisualizationDataForAllRowsResponse::set_types_to_include(int index, ::carbon::frontend::banding::VisualizationTypeToInclude value) {
  types_to_include_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.types_to_include)
}
inline void GetNextVisualizationDataForAllRowsResponse::_internal_add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value) {
  types_to_include_.Add(value);
}
inline void GetNextVisualizationDataForAllRowsResponse::add_types_to_include(::carbon::frontend::banding::VisualizationTypeToInclude value) {
  _internal_add_types_to_include(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.types_to_include)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
GetNextVisualizationDataForAllRowsResponse::types_to_include() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.types_to_include)
  return types_to_include_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
GetNextVisualizationDataForAllRowsResponse::_internal_mutable_types_to_include() {
  return &types_to_include_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
GetNextVisualizationDataForAllRowsResponse::mutable_types_to_include() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.types_to_include)
  return _internal_mutable_types_to_include();
}

// -------------------------------------------------------------------

// GetNextBandingStateResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextBandingStateResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextBandingStateResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextBandingStateResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextBandingStateResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextBandingStateResponse.ts)
  return _internal_ts();
}
inline void GetNextBandingStateResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.banding.GetNextBandingStateResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextBandingStateResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextBandingStateResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextBandingStateResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextBandingStateResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextBandingStateResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextBandingStateResponse.ts)
  return _msg;
}
inline void GetNextBandingStateResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextBandingStateResponse.ts)
}

// repeated .carbon.frontend.banding.BandingDef bandingDefs = 2;
inline int GetNextBandingStateResponse::_internal_bandingdefs_size() const {
  return bandingdefs_.size();
}
inline int GetNextBandingStateResponse::bandingdefs_size() const {
  return _internal_bandingdefs_size();
}
inline void GetNextBandingStateResponse::clear_bandingdefs() {
  bandingdefs_.Clear();
}
inline ::carbon::frontend::banding::BandingDef* GetNextBandingStateResponse::mutable_bandingdefs(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextBandingStateResponse.bandingDefs)
  return bandingdefs_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef >*
GetNextBandingStateResponse::mutable_bandingdefs() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.banding.GetNextBandingStateResponse.bandingDefs)
  return &bandingdefs_;
}
inline const ::carbon::frontend::banding::BandingDef& GetNextBandingStateResponse::_internal_bandingdefs(int index) const {
  return bandingdefs_.Get(index);
}
inline const ::carbon::frontend::banding::BandingDef& GetNextBandingStateResponse::bandingdefs(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextBandingStateResponse.bandingDefs)
  return _internal_bandingdefs(index);
}
inline ::carbon::frontend::banding::BandingDef* GetNextBandingStateResponse::_internal_add_bandingdefs() {
  return bandingdefs_.Add();
}
inline ::carbon::frontend::banding::BandingDef* GetNextBandingStateResponse::add_bandingdefs() {
  ::carbon::frontend::banding::BandingDef* _add = _internal_add_bandingdefs();
  // @@protoc_insertion_point(field_add:carbon.frontend.banding.GetNextBandingStateResponse.bandingDefs)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::banding::BandingDef >&
GetNextBandingStateResponse::bandingdefs() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.banding.GetNextBandingStateResponse.bandingDefs)
  return bandingdefs_;
}

// string activeDefUUID = 3;
inline void GetNextBandingStateResponse::clear_activedefuuid() {
  activedefuuid_.ClearToEmpty();
}
inline const std::string& GetNextBandingStateResponse::activedefuuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextBandingStateResponse.activeDefUUID)
  return _internal_activedefuuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextBandingStateResponse::set_activedefuuid(ArgT0&& arg0, ArgT... args) {
 
 activedefuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetNextBandingStateResponse.activeDefUUID)
}
inline std::string* GetNextBandingStateResponse::mutable_activedefuuid() {
  std::string* _s = _internal_mutable_activedefuuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.banding.GetNextBandingStateResponse.activeDefUUID)
  return _s;
}
inline const std::string& GetNextBandingStateResponse::_internal_activedefuuid() const {
  return activedefuuid_.Get();
}
inline void GetNextBandingStateResponse::_internal_set_activedefuuid(const std::string& value) {
  
  activedefuuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextBandingStateResponse::_internal_mutable_activedefuuid() {
  
  return activedefuuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextBandingStateResponse::release_activedefuuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.banding.GetNextBandingStateResponse.activeDefUUID)
  return activedefuuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextBandingStateResponse::set_allocated_activedefuuid(std::string* activedefuuid) {
  if (activedefuuid != nullptr) {
    
  } else {
    
  }
  activedefuuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), activedefuuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (activedefuuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    activedefuuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.banding.GetNextBandingStateResponse.activeDefUUID)
}

// bool is_banding_enabled = 4;
inline void GetNextBandingStateResponse::clear_is_banding_enabled() {
  is_banding_enabled_ = false;
}
inline bool GetNextBandingStateResponse::_internal_is_banding_enabled() const {
  return is_banding_enabled_;
}
inline bool GetNextBandingStateResponse::is_banding_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextBandingStateResponse.is_banding_enabled)
  return _internal_is_banding_enabled();
}
inline void GetNextBandingStateResponse::_internal_set_is_banding_enabled(bool value) {
  
  is_banding_enabled_ = value;
}
inline void GetNextBandingStateResponse::set_is_banding_enabled(bool value) {
  _internal_set_is_banding_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetNextBandingStateResponse.is_banding_enabled)
}

// bool is_dynamic_banding_enabled = 5;
inline void GetNextBandingStateResponse::clear_is_dynamic_banding_enabled() {
  is_dynamic_banding_enabled_ = false;
}
inline bool GetNextBandingStateResponse::_internal_is_dynamic_banding_enabled() const {
  return is_dynamic_banding_enabled_;
}
inline bool GetNextBandingStateResponse::is_dynamic_banding_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.banding.GetNextBandingStateResponse.is_dynamic_banding_enabled)
  return _internal_is_dynamic_banding_enabled();
}
inline void GetNextBandingStateResponse::_internal_set_is_dynamic_banding_enabled(bool value) {
  
  is_dynamic_banding_enabled_ = value;
}
inline void GetNextBandingStateResponse::set_is_dynamic_banding_enabled(bool value) {
  _internal_set_is_dynamic_banding_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.banding.GetNextBandingStateResponse.is_dynamic_banding_enabled)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace banding
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::banding::VisualizationTypeToInclude> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::banding::VisualizationTypeToInclude>() {
  return ::carbon::frontend::banding::VisualizationTypeToInclude_descriptor();
}
template <> struct is_proto_enum< ::carbon::frontend::banding::ThresholdState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::banding::ThresholdState>() {
  return ::carbon::frontend::banding::ThresholdState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fbanding_2eproto

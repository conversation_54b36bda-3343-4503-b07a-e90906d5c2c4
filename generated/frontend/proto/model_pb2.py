# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/model.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/model.proto',
  package='carbon.frontend.model',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1a\x66rontend/proto/model.proto\x12\x15\x63\x61rbon.frontend.model\x1a\x19\x66rontend/proto/util.proto\"\xe9\x03\n\x05Model\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0c\n\x04\x63rop\x18\x02 \x01(\t\x12+\n\x02ts\x18\x03 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0e\n\x06\x63ustom\x18\x04 \x01(\x08\x12\x0e\n\x06pinned\x18\x05 \x01(\x08\x12\x0e\n\x06\x61\x63tive\x18\x06 \x01(\x08\x12\x0e\n\x06synced\x18\x07 \x01(\x08\x12\x16\n\x0esynced_to_rows\x18\x08 \x03(\x08\x12\x13\n\x0b\x64ownloading\x18\t \x01(\x08\x12\x0c\n\x04type\x18\n \x01(\t\x12<\n\x13last_used_timestamp\x18\x0b \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x1c\n\x14\x64ownloading_progress\x18\x0c \x01(\x02\x12/\n\'estimated_downloading_remaining_time_ms\x18\r \x01(\x04\x12=\n\x14\x64ownloaded_timestamp\x18\x0e \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x13\n\x0brecommended\x18\x0f \x01(\x08\x12\x17\n\x0fviable_crop_ids\x18\x10 \x03(\t\x12\x12\n\nmaintained\x18\x11 \x01(\x08\x12\x10\n\x08nickname\x18\x12 \x01(\t\"$\n\x11SelectCropRequest\x12\x0f\n\x07\x63rop_id\x18\x01 \x01(\t\"\"\n\x12ListCropParameters\x12\x0c\n\x04lang\x18\x01 \x01(\t\"\xb0\x01\n\x0b\x45nabledCrop\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0f\n\x07\x63reated\x18\x02 \x01(\x03\x12\x17\n\x0b\x63\x61rbon_name\x18\x03 \x01(\tB\x02\x18\x01\x12\x13\n\x0b\x63ommon_name\x18\x04 \x01(\t\x12\x13\n\x0b\x64\x65scription\x18\x05 \x01(\t\x12\r\n\x05notes\x18\x06 \x01(\t\x12\x17\n\x0fpinned_model_id\x18\x07 \x01(\t\x12\x19\n\x11recommended_model\x18\x08 \x01(\t\"K\n\x0f\x45nabledCropList\x12\x38\n\x0c\x65nabledCrops\x18\x01 \x03(\x0b\x32\".carbon.frontend.model.EnabledCrop\"]\n\x1dGetNextSelectedCropIDResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0f\n\x07\x63rop_id\x18\x02 \x01(\t\"q\n\x0fPinModelRequest\x12\n\n\x02id\x18\x01 \x01(\t\x12\x10\n\x04\x63rop\x18\x02 \x01(\tB\x02\x18\x01\x12\"\n\x1a\x61llow_pinned_crop_override\x18\x03 \x01(\x08\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\x12\x0b\n\x03p2p\x18\x05 \x01(\x08\"C\n\x11UnpinModelRequest\x12\x10\n\x04\x63rop\x18\x01 \x01(\tB\x02\x18\x01\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\x12\x0b\n\x03p2p\x18\x05 \x01(\x08\"j\n\x18GetNextModelStateRequest\x12\x10\n\x04\x63rop\x18\x01 \x01(\tB\x02\x18\x01\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0f\n\x07\x63rop_id\x18\x03 \x01(\t\"\xb7\x01\n\x19GetNextModelStateResponse\x12,\n\x06models\x18\x01 \x03(\x0b\x32\x1c.carbon.frontend.model.Model\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x1c\n\x14\x63urrent_p2p_model_id\x18\x03 \x01(\t\x12!\n\x19\x63urrent_deepweed_model_id\x18\x04 \x01(\t\"(\n\x14\x44ownloadModelRequest\x12\x10\n\x08model_id\x18\x01 \x01(\t\"\xfe\x01\n\x13ModelHistoryRequest\x12\x17\n\x0fstart_timestamp\x18\x01 \x01(\x03\x12\r\n\x05\x63ount\x18\x02 \x01(\x03\x12\x0f\n\x07reverse\x18\x03 \x01(\x08\x12\x37\n\x0cmatch_filter\x18\x04 \x01(\x0b\x32!.carbon.frontend.model.ModelEvent\x12+\n\x02ts\x18\x05 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12H\n\x12\x65vent_type_matcher\x18\x06 \x01(\x0b\x32,.carbon.frontend.model.ModelEventTypeMatcher\"\xa3\x01\n\nModelEvent\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x12\n\nmodel_type\x18\x03 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\x12\x10\n\x08job_name\x18\x05 \x01(\t\x12\x0c\n\x04time\x18\x06 \x01(\x03\x12\x16\n\x0emodel_nickname\x18\x07 \x01(\t\x12\x18\n\x10model_parameters\x18\x08 \x01(\t\"\xe4\x01\n\x15ModelEventTypeMatcher\x12\x13\n\x0brobot_start\x18\x01 \x01(\x08\x12\x0e\n\x06pinned\x18\x02 \x01(\x08\x12\x10\n\x08unpinned\x18\x03 \x01(\x08\x12\x13\n\x0brecommended\x18\x04 \x01(\x08\x12\x11\n\tactivated\x18\x05 \x01(\x08\x12\x17\n\x0fnickname_change\x18\x06 \x01(\x08\x12\x17\n\x0fnickname_delete\x18\x07 \x01(\x08\x12 \n\x18\x64\x65\x66\x61ult_parameter_change\x18\x08 \x01(\x08\x12\x18\n\x10parameter_change\x18\t \x01(\x08\"v\n\x14ModelHistoryResponse\x12\x31\n\x06\x65vents\x18\x01 \x03(\x0b\x32!.carbon.frontend.model.ModelEvent\x12+\n\x02ts\x18\x05 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"Z\n\x18GetModelNicknamesRequest\x12\x11\n\tmodel_ids\x18\x01 \x03(\t\x12+\n\x02ts\x18\x05 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\xde\x01\n\x19GetModelNicknamesResponse\x12]\n\x0fmodel_nicknames\x18\x01 \x03(\x0b\x32\x44.carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry\x12+\n\x02ts\x18\x05 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x1a\x35\n\x13ModelNicknamesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"C\n\x17SetModelNicknameRequest\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x16\n\x0emodel_nickname\x18\x02 \x01(\t\"2\n\rCropModelPair\x12\x0f\n\x07\x63rop_id\x18\x01 \x01(\t\x12\x10\n\x08model_id\x18\x02 \x01(\t\"d\n$RefreshDefaultModelParametersRequest\x12<\n\x0e\x63ropModelPairs\x18\x01 \x03(\x0b\x32$.carbon.frontend.model.CropModelPair\"1\n\x12SyncCropIDsRequest\x12\x1b\n\x13\x66orce_cache_refresh\x18\x01 \x01(\x08\"W\n\x1aGetNextEnabledCropsRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0c\n\x04lang\x18\x02 \x01(\t\"\x84\x01\n\x1bGetNextEnabledCropsResponse\x12\x38\n\x0c\x65nabledCrops\x18\x01 \x03(\x0b\x32\".carbon.frontend.model.EnabledCrop\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"W\n\x1aGetNextCaptureCropsRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0c\n\x04lang\x18\x02 \x01(\t\"\x84\x01\n\x1bGetNextCaptureCropsResponse\x12\x38\n\x0c\x65nabledCrops\x18\x01 \x03(\x0b\x32\".carbon.frontend.model.EnabledCrop\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp2\xaa\x10\n\x0cModelService\x12O\n\x08PinModel\x12&.carbon.frontend.model.PinModelRequest\x1a\x1b.carbon.frontend.util.Empty\x12S\n\nUnpinModel\x12(.carbon.frontend.model.UnpinModelRequest\x1a\x1b.carbon.frontend.util.Empty\x12v\n\x11GetNextModelState\x12/.carbon.frontend.model.GetNextModelStateRequest\x1a\x30.carbon.frontend.model.GetNextModelStateResponse\x12y\n\x14GetNextAllModelState\x12/.carbon.frontend.model.GetNextModelStateRequest\x1a\x30.carbon.frontend.model.GetNextModelStateResponse\x12G\n\x0bUpdateModel\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12\x65\n\x10ListEnabledCrops\x12).carbon.frontend.model.ListCropParameters\x1a&.carbon.frontend.model.EnabledCropList\x12|\n\x13GetNextEnabledCrops\x12\x31.carbon.frontend.model.GetNextEnabledCropsRequest\x1a\x32.carbon.frontend.model.GetNextEnabledCropsResponse\x12\x65\n\x10ListCaptureCrops\x12).carbon.frontend.model.ListCropParameters\x1a&.carbon.frontend.model.EnabledCropList\x12|\n\x13GetNextCaptureCrops\x12\x31.carbon.frontend.model.GetNextCaptureCropsRequest\x1a\x32.carbon.frontend.model.GetNextCaptureCropsResponse\x12n\n\x15GetNextSelectedCropID\x12\x1f.carbon.frontend.util.Timestamp\x1a\x34.carbon.frontend.model.GetNextSelectedCropIDResponse\x12S\n\nSelectCrop\x12(.carbon.frontend.model.SelectCropRequest\x1a\x1b.carbon.frontend.util.Empty\x12Y\n\rDownloadModel\x12+.carbon.frontend.model.DownloadModelRequest\x1a\x1b.carbon.frontend.util.Empty\x12n\n\x13GetNextModelHistory\x12*.carbon.frontend.model.ModelHistoryRequest\x1a+.carbon.frontend.model.ModelHistoryResponse\x12j\n\x0fGetModelHistory\x12*.carbon.frontend.model.ModelHistoryRequest\x1a+.carbon.frontend.model.ModelHistoryResponse\x12v\n\x11GetModelNicknames\x12/.carbon.frontend.model.GetModelNicknamesRequest\x1a\x30.carbon.frontend.model.GetModelNicknamesResponse\x12z\n\x15GetNextModelNicknames\x12/.carbon.frontend.model.GetModelNicknamesRequest\x1a\x30.carbon.frontend.model.GetModelNicknamesResponse\x12_\n\x10SetModelNickname\x12..carbon.frontend.model.SetModelNicknameRequest\x1a\x1b.carbon.frontend.util.Empty\x12y\n\x1dRefreshDefaultModelParameters\x12;.carbon.frontend.model.RefreshDefaultModelParametersRequest\x1a\x1b.carbon.frontend.util.Empty\x12U\n\x0bSyncCropIDs\x12).carbon.frontend.model.SyncCropIDsRequest\x1a\x1b.carbon.frontend.util.Empty\x12K\n\x0fTriggerDownload\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_MODEL = _descriptor.Descriptor(
  name='Model',
  full_name='carbon.frontend.model.Model',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.model.Model.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.model.Model.crop', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.Model.ts', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='custom', full_name='carbon.frontend.model.Model.custom', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pinned', full_name='carbon.frontend.model.Model.pinned', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active', full_name='carbon.frontend.model.Model.active', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='synced', full_name='carbon.frontend.model.Model.synced', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='synced_to_rows', full_name='carbon.frontend.model.Model.synced_to_rows', index=7,
      number=8, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='downloading', full_name='carbon.frontend.model.Model.downloading', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.frontend.model.Model.type', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_used_timestamp', full_name='carbon.frontend.model.Model.last_used_timestamp', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='downloading_progress', full_name='carbon.frontend.model.Model.downloading_progress', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='estimated_downloading_remaining_time_ms', full_name='carbon.frontend.model.Model.estimated_downloading_remaining_time_ms', index=12,
      number=13, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='downloaded_timestamp', full_name='carbon.frontend.model.Model.downloaded_timestamp', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recommended', full_name='carbon.frontend.model.Model.recommended', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='viable_crop_ids', full_name='carbon.frontend.model.Model.viable_crop_ids', index=15,
      number=16, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='maintained', full_name='carbon.frontend.model.Model.maintained', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nickname', full_name='carbon.frontend.model.Model.nickname', index=17,
      number=18, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=81,
  serialized_end=570,
)


_SELECTCROPREQUEST = _descriptor.Descriptor(
  name='SelectCropRequest',
  full_name='carbon.frontend.model.SelectCropRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.model.SelectCropRequest.crop_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=572,
  serialized_end=608,
)


_LISTCROPPARAMETERS = _descriptor.Descriptor(
  name='ListCropParameters',
  full_name='carbon.frontend.model.ListCropParameters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='lang', full_name='carbon.frontend.model.ListCropParameters.lang', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=610,
  serialized_end=644,
)


_ENABLEDCROP = _descriptor.Descriptor(
  name='EnabledCrop',
  full_name='carbon.frontend.model.EnabledCrop',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.model.EnabledCrop.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='created', full_name='carbon.frontend.model.EnabledCrop.created', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='carbon_name', full_name='carbon.frontend.model.EnabledCrop.carbon_name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='common_name', full_name='carbon.frontend.model.EnabledCrop.common_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='description', full_name='carbon.frontend.model.EnabledCrop.description', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='notes', full_name='carbon.frontend.model.EnabledCrop.notes', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pinned_model_id', full_name='carbon.frontend.model.EnabledCrop.pinned_model_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recommended_model', full_name='carbon.frontend.model.EnabledCrop.recommended_model', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=647,
  serialized_end=823,
)


_ENABLEDCROPLIST = _descriptor.Descriptor(
  name='EnabledCropList',
  full_name='carbon.frontend.model.EnabledCropList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabledCrops', full_name='carbon.frontend.model.EnabledCropList.enabledCrops', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=825,
  serialized_end=900,
)


_GETNEXTSELECTEDCROPIDRESPONSE = _descriptor.Descriptor(
  name='GetNextSelectedCropIDResponse',
  full_name='carbon.frontend.model.GetNextSelectedCropIDResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetNextSelectedCropIDResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.model.GetNextSelectedCropIDResponse.crop_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=902,
  serialized_end=995,
)


_PINMODELREQUEST = _descriptor.Descriptor(
  name='PinModelRequest',
  full_name='carbon.frontend.model.PinModelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.model.PinModelRequest.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.model.PinModelRequest.crop', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='allow_pinned_crop_override', full_name='carbon.frontend.model.PinModelRequest.allow_pinned_crop_override', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.model.PinModelRequest.crop_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p', full_name='carbon.frontend.model.PinModelRequest.p2p', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=997,
  serialized_end=1110,
)


_UNPINMODELREQUEST = _descriptor.Descriptor(
  name='UnpinModelRequest',
  full_name='carbon.frontend.model.UnpinModelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.model.UnpinModelRequest.crop', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.model.UnpinModelRequest.crop_id', index=1,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p', full_name='carbon.frontend.model.UnpinModelRequest.p2p', index=2,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1112,
  serialized_end=1179,
)


_GETNEXTMODELSTATEREQUEST = _descriptor.Descriptor(
  name='GetNextModelStateRequest',
  full_name='carbon.frontend.model.GetNextModelStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.model.GetNextModelStateRequest.crop', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetNextModelStateRequest.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.model.GetNextModelStateRequest.crop_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1181,
  serialized_end=1287,
)


_GETNEXTMODELSTATERESPONSE = _descriptor.Descriptor(
  name='GetNextModelStateResponse',
  full_name='carbon.frontend.model.GetNextModelStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='models', full_name='carbon.frontend.model.GetNextModelStateResponse.models', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetNextModelStateResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_p2p_model_id', full_name='carbon.frontend.model.GetNextModelStateResponse.current_p2p_model_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_deepweed_model_id', full_name='carbon.frontend.model.GetNextModelStateResponse.current_deepweed_model_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1290,
  serialized_end=1473,
)


_DOWNLOADMODELREQUEST = _descriptor.Descriptor(
  name='DownloadModelRequest',
  full_name='carbon.frontend.model.DownloadModelRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.frontend.model.DownloadModelRequest.model_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1475,
  serialized_end=1515,
)


_MODELHISTORYREQUEST = _descriptor.Descriptor(
  name='ModelHistoryRequest',
  full_name='carbon.frontend.model.ModelHistoryRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='start_timestamp', full_name='carbon.frontend.model.ModelHistoryRequest.start_timestamp', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='count', full_name='carbon.frontend.model.ModelHistoryRequest.count', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reverse', full_name='carbon.frontend.model.ModelHistoryRequest.reverse', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='match_filter', full_name='carbon.frontend.model.ModelHistoryRequest.match_filter', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.ModelHistoryRequest.ts', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='event_type_matcher', full_name='carbon.frontend.model.ModelHistoryRequest.event_type_matcher', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1518,
  serialized_end=1772,
)


_MODELEVENT = _descriptor.Descriptor(
  name='ModelEvent',
  full_name='carbon.frontend.model.ModelEvent',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.frontend.model.ModelEvent.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.frontend.model.ModelEvent.model_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_type', full_name='carbon.frontend.model.ModelEvent.model_type', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.model.ModelEvent.crop_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='job_name', full_name='carbon.frontend.model.ModelEvent.job_name', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time', full_name='carbon.frontend.model.ModelEvent.time', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_nickname', full_name='carbon.frontend.model.ModelEvent.model_nickname', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_parameters', full_name='carbon.frontend.model.ModelEvent.model_parameters', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1775,
  serialized_end=1938,
)


_MODELEVENTTYPEMATCHER = _descriptor.Descriptor(
  name='ModelEventTypeMatcher',
  full_name='carbon.frontend.model.ModelEventTypeMatcher',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot_start', full_name='carbon.frontend.model.ModelEventTypeMatcher.robot_start', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pinned', full_name='carbon.frontend.model.ModelEventTypeMatcher.pinned', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='unpinned', full_name='carbon.frontend.model.ModelEventTypeMatcher.unpinned', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recommended', full_name='carbon.frontend.model.ModelEventTypeMatcher.recommended', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='activated', full_name='carbon.frontend.model.ModelEventTypeMatcher.activated', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nickname_change', full_name='carbon.frontend.model.ModelEventTypeMatcher.nickname_change', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nickname_delete', full_name='carbon.frontend.model.ModelEventTypeMatcher.nickname_delete', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='default_parameter_change', full_name='carbon.frontend.model.ModelEventTypeMatcher.default_parameter_change', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='parameter_change', full_name='carbon.frontend.model.ModelEventTypeMatcher.parameter_change', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1941,
  serialized_end=2169,
)


_MODELHISTORYRESPONSE = _descriptor.Descriptor(
  name='ModelHistoryResponse',
  full_name='carbon.frontend.model.ModelHistoryResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='events', full_name='carbon.frontend.model.ModelHistoryResponse.events', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.ModelHistoryResponse.ts', index=1,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2171,
  serialized_end=2289,
)


_GETMODELNICKNAMESREQUEST = _descriptor.Descriptor(
  name='GetModelNicknamesRequest',
  full_name='carbon.frontend.model.GetModelNicknamesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_ids', full_name='carbon.frontend.model.GetModelNicknamesRequest.model_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetModelNicknamesRequest.ts', index=1,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2291,
  serialized_end=2381,
)


_GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY = _descriptor.Descriptor(
  name='ModelNicknamesEntry',
  full_name='carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry.value', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2553,
  serialized_end=2606,
)

_GETMODELNICKNAMESRESPONSE = _descriptor.Descriptor(
  name='GetModelNicknamesResponse',
  full_name='carbon.frontend.model.GetModelNicknamesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_nicknames', full_name='carbon.frontend.model.GetModelNicknamesResponse.model_nicknames', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetModelNicknamesResponse.ts', index=1,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2384,
  serialized_end=2606,
)


_SETMODELNICKNAMEREQUEST = _descriptor.Descriptor(
  name='SetModelNicknameRequest',
  full_name='carbon.frontend.model.SetModelNicknameRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.frontend.model.SetModelNicknameRequest.model_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_nickname', full_name='carbon.frontend.model.SetModelNicknameRequest.model_nickname', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2608,
  serialized_end=2675,
)


_CROPMODELPAIR = _descriptor.Descriptor(
  name='CropModelPair',
  full_name='carbon.frontend.model.CropModelPair',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.model.CropModelPair.crop_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.frontend.model.CropModelPair.model_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2677,
  serialized_end=2727,
)


_REFRESHDEFAULTMODELPARAMETERSREQUEST = _descriptor.Descriptor(
  name='RefreshDefaultModelParametersRequest',
  full_name='carbon.frontend.model.RefreshDefaultModelParametersRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cropModelPairs', full_name='carbon.frontend.model.RefreshDefaultModelParametersRequest.cropModelPairs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2729,
  serialized_end=2829,
)


_SYNCCROPIDSREQUEST = _descriptor.Descriptor(
  name='SyncCropIDsRequest',
  full_name='carbon.frontend.model.SyncCropIDsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='force_cache_refresh', full_name='carbon.frontend.model.SyncCropIDsRequest.force_cache_refresh', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2831,
  serialized_end=2880,
)


_GETNEXTENABLEDCROPSREQUEST = _descriptor.Descriptor(
  name='GetNextEnabledCropsRequest',
  full_name='carbon.frontend.model.GetNextEnabledCropsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetNextEnabledCropsRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lang', full_name='carbon.frontend.model.GetNextEnabledCropsRequest.lang', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2882,
  serialized_end=2969,
)


_GETNEXTENABLEDCROPSRESPONSE = _descriptor.Descriptor(
  name='GetNextEnabledCropsResponse',
  full_name='carbon.frontend.model.GetNextEnabledCropsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabledCrops', full_name='carbon.frontend.model.GetNextEnabledCropsResponse.enabledCrops', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetNextEnabledCropsResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2972,
  serialized_end=3104,
)


_GETNEXTCAPTURECROPSREQUEST = _descriptor.Descriptor(
  name='GetNextCaptureCropsRequest',
  full_name='carbon.frontend.model.GetNextCaptureCropsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetNextCaptureCropsRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lang', full_name='carbon.frontend.model.GetNextCaptureCropsRequest.lang', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3106,
  serialized_end=3193,
)


_GETNEXTCAPTURECROPSRESPONSE = _descriptor.Descriptor(
  name='GetNextCaptureCropsResponse',
  full_name='carbon.frontend.model.GetNextCaptureCropsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabledCrops', full_name='carbon.frontend.model.GetNextCaptureCropsResponse.enabledCrops', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.model.GetNextCaptureCropsResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3196,
  serialized_end=3328,
)

_MODEL.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_MODEL.fields_by_name['last_used_timestamp'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_MODEL.fields_by_name['downloaded_timestamp'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_ENABLEDCROPLIST.fields_by_name['enabledCrops'].message_type = _ENABLEDCROP
_GETNEXTSELECTEDCROPIDRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTMODELSTATEREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTMODELSTATERESPONSE.fields_by_name['models'].message_type = _MODEL
_GETNEXTMODELSTATERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_MODELHISTORYREQUEST.fields_by_name['match_filter'].message_type = _MODELEVENT
_MODELHISTORYREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_MODELHISTORYREQUEST.fields_by_name['event_type_matcher'].message_type = _MODELEVENTTYPEMATCHER
_MODELHISTORYRESPONSE.fields_by_name['events'].message_type = _MODELEVENT
_MODELHISTORYRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETMODELNICKNAMESREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY.containing_type = _GETMODELNICKNAMESRESPONSE
_GETMODELNICKNAMESRESPONSE.fields_by_name['model_nicknames'].message_type = _GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY
_GETMODELNICKNAMESRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_REFRESHDEFAULTMODELPARAMETERSREQUEST.fields_by_name['cropModelPairs'].message_type = _CROPMODELPAIR
_GETNEXTENABLEDCROPSREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTENABLEDCROPSRESPONSE.fields_by_name['enabledCrops'].message_type = _ENABLEDCROP
_GETNEXTENABLEDCROPSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCAPTURECROPSREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCAPTURECROPSRESPONSE.fields_by_name['enabledCrops'].message_type = _ENABLEDCROP
_GETNEXTCAPTURECROPSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['Model'] = _MODEL
DESCRIPTOR.message_types_by_name['SelectCropRequest'] = _SELECTCROPREQUEST
DESCRIPTOR.message_types_by_name['ListCropParameters'] = _LISTCROPPARAMETERS
DESCRIPTOR.message_types_by_name['EnabledCrop'] = _ENABLEDCROP
DESCRIPTOR.message_types_by_name['EnabledCropList'] = _ENABLEDCROPLIST
DESCRIPTOR.message_types_by_name['GetNextSelectedCropIDResponse'] = _GETNEXTSELECTEDCROPIDRESPONSE
DESCRIPTOR.message_types_by_name['PinModelRequest'] = _PINMODELREQUEST
DESCRIPTOR.message_types_by_name['UnpinModelRequest'] = _UNPINMODELREQUEST
DESCRIPTOR.message_types_by_name['GetNextModelStateRequest'] = _GETNEXTMODELSTATEREQUEST
DESCRIPTOR.message_types_by_name['GetNextModelStateResponse'] = _GETNEXTMODELSTATERESPONSE
DESCRIPTOR.message_types_by_name['DownloadModelRequest'] = _DOWNLOADMODELREQUEST
DESCRIPTOR.message_types_by_name['ModelHistoryRequest'] = _MODELHISTORYREQUEST
DESCRIPTOR.message_types_by_name['ModelEvent'] = _MODELEVENT
DESCRIPTOR.message_types_by_name['ModelEventTypeMatcher'] = _MODELEVENTTYPEMATCHER
DESCRIPTOR.message_types_by_name['ModelHistoryResponse'] = _MODELHISTORYRESPONSE
DESCRIPTOR.message_types_by_name['GetModelNicknamesRequest'] = _GETMODELNICKNAMESREQUEST
DESCRIPTOR.message_types_by_name['GetModelNicknamesResponse'] = _GETMODELNICKNAMESRESPONSE
DESCRIPTOR.message_types_by_name['SetModelNicknameRequest'] = _SETMODELNICKNAMEREQUEST
DESCRIPTOR.message_types_by_name['CropModelPair'] = _CROPMODELPAIR
DESCRIPTOR.message_types_by_name['RefreshDefaultModelParametersRequest'] = _REFRESHDEFAULTMODELPARAMETERSREQUEST
DESCRIPTOR.message_types_by_name['SyncCropIDsRequest'] = _SYNCCROPIDSREQUEST
DESCRIPTOR.message_types_by_name['GetNextEnabledCropsRequest'] = _GETNEXTENABLEDCROPSREQUEST
DESCRIPTOR.message_types_by_name['GetNextEnabledCropsResponse'] = _GETNEXTENABLEDCROPSRESPONSE
DESCRIPTOR.message_types_by_name['GetNextCaptureCropsRequest'] = _GETNEXTCAPTURECROPSREQUEST
DESCRIPTOR.message_types_by_name['GetNextCaptureCropsResponse'] = _GETNEXTCAPTURECROPSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Model = _reflection.GeneratedProtocolMessageType('Model', (_message.Message,), {
  'DESCRIPTOR' : _MODEL,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.Model)
  })
_sym_db.RegisterMessage(Model)

SelectCropRequest = _reflection.GeneratedProtocolMessageType('SelectCropRequest', (_message.Message,), {
  'DESCRIPTOR' : _SELECTCROPREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.SelectCropRequest)
  })
_sym_db.RegisterMessage(SelectCropRequest)

ListCropParameters = _reflection.GeneratedProtocolMessageType('ListCropParameters', (_message.Message,), {
  'DESCRIPTOR' : _LISTCROPPARAMETERS,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.ListCropParameters)
  })
_sym_db.RegisterMessage(ListCropParameters)

EnabledCrop = _reflection.GeneratedProtocolMessageType('EnabledCrop', (_message.Message,), {
  'DESCRIPTOR' : _ENABLEDCROP,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.EnabledCrop)
  })
_sym_db.RegisterMessage(EnabledCrop)

EnabledCropList = _reflection.GeneratedProtocolMessageType('EnabledCropList', (_message.Message,), {
  'DESCRIPTOR' : _ENABLEDCROPLIST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.EnabledCropList)
  })
_sym_db.RegisterMessage(EnabledCropList)

GetNextSelectedCropIDResponse = _reflection.GeneratedProtocolMessageType('GetNextSelectedCropIDResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTSELECTEDCROPIDRESPONSE,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextSelectedCropIDResponse)
  })
_sym_db.RegisterMessage(GetNextSelectedCropIDResponse)

PinModelRequest = _reflection.GeneratedProtocolMessageType('PinModelRequest', (_message.Message,), {
  'DESCRIPTOR' : _PINMODELREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.PinModelRequest)
  })
_sym_db.RegisterMessage(PinModelRequest)

UnpinModelRequest = _reflection.GeneratedProtocolMessageType('UnpinModelRequest', (_message.Message,), {
  'DESCRIPTOR' : _UNPINMODELREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.UnpinModelRequest)
  })
_sym_db.RegisterMessage(UnpinModelRequest)

GetNextModelStateRequest = _reflection.GeneratedProtocolMessageType('GetNextModelStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTMODELSTATEREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextModelStateRequest)
  })
_sym_db.RegisterMessage(GetNextModelStateRequest)

GetNextModelStateResponse = _reflection.GeneratedProtocolMessageType('GetNextModelStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTMODELSTATERESPONSE,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextModelStateResponse)
  })
_sym_db.RegisterMessage(GetNextModelStateResponse)

DownloadModelRequest = _reflection.GeneratedProtocolMessageType('DownloadModelRequest', (_message.Message,), {
  'DESCRIPTOR' : _DOWNLOADMODELREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.DownloadModelRequest)
  })
_sym_db.RegisterMessage(DownloadModelRequest)

ModelHistoryRequest = _reflection.GeneratedProtocolMessageType('ModelHistoryRequest', (_message.Message,), {
  'DESCRIPTOR' : _MODELHISTORYREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.ModelHistoryRequest)
  })
_sym_db.RegisterMessage(ModelHistoryRequest)

ModelEvent = _reflection.GeneratedProtocolMessageType('ModelEvent', (_message.Message,), {
  'DESCRIPTOR' : _MODELEVENT,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.ModelEvent)
  })
_sym_db.RegisterMessage(ModelEvent)

ModelEventTypeMatcher = _reflection.GeneratedProtocolMessageType('ModelEventTypeMatcher', (_message.Message,), {
  'DESCRIPTOR' : _MODELEVENTTYPEMATCHER,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.ModelEventTypeMatcher)
  })
_sym_db.RegisterMessage(ModelEventTypeMatcher)

ModelHistoryResponse = _reflection.GeneratedProtocolMessageType('ModelHistoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _MODELHISTORYRESPONSE,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.ModelHistoryResponse)
  })
_sym_db.RegisterMessage(ModelHistoryResponse)

GetModelNicknamesRequest = _reflection.GeneratedProtocolMessageType('GetModelNicknamesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETMODELNICKNAMESREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetModelNicknamesRequest)
  })
_sym_db.RegisterMessage(GetModelNicknamesRequest)

GetModelNicknamesResponse = _reflection.GeneratedProtocolMessageType('GetModelNicknamesResponse', (_message.Message,), {

  'ModelNicknamesEntry' : _reflection.GeneratedProtocolMessageType('ModelNicknamesEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY,
    '__module__' : 'frontend.proto.model_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry)
    })
  ,
  'DESCRIPTOR' : _GETMODELNICKNAMESRESPONSE,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetModelNicknamesResponse)
  })
_sym_db.RegisterMessage(GetModelNicknamesResponse)
_sym_db.RegisterMessage(GetModelNicknamesResponse.ModelNicknamesEntry)

SetModelNicknameRequest = _reflection.GeneratedProtocolMessageType('SetModelNicknameRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETMODELNICKNAMEREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.SetModelNicknameRequest)
  })
_sym_db.RegisterMessage(SetModelNicknameRequest)

CropModelPair = _reflection.GeneratedProtocolMessageType('CropModelPair', (_message.Message,), {
  'DESCRIPTOR' : _CROPMODELPAIR,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.CropModelPair)
  })
_sym_db.RegisterMessage(CropModelPair)

RefreshDefaultModelParametersRequest = _reflection.GeneratedProtocolMessageType('RefreshDefaultModelParametersRequest', (_message.Message,), {
  'DESCRIPTOR' : _REFRESHDEFAULTMODELPARAMETERSREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.RefreshDefaultModelParametersRequest)
  })
_sym_db.RegisterMessage(RefreshDefaultModelParametersRequest)

SyncCropIDsRequest = _reflection.GeneratedProtocolMessageType('SyncCropIDsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SYNCCROPIDSREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.SyncCropIDsRequest)
  })
_sym_db.RegisterMessage(SyncCropIDsRequest)

GetNextEnabledCropsRequest = _reflection.GeneratedProtocolMessageType('GetNextEnabledCropsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTENABLEDCROPSREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextEnabledCropsRequest)
  })
_sym_db.RegisterMessage(GetNextEnabledCropsRequest)

GetNextEnabledCropsResponse = _reflection.GeneratedProtocolMessageType('GetNextEnabledCropsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTENABLEDCROPSRESPONSE,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextEnabledCropsResponse)
  })
_sym_db.RegisterMessage(GetNextEnabledCropsResponse)

GetNextCaptureCropsRequest = _reflection.GeneratedProtocolMessageType('GetNextCaptureCropsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCAPTURECROPSREQUEST,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextCaptureCropsRequest)
  })
_sym_db.RegisterMessage(GetNextCaptureCropsRequest)

GetNextCaptureCropsResponse = _reflection.GeneratedProtocolMessageType('GetNextCaptureCropsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCAPTURECROPSRESPONSE,
  '__module__' : 'frontend.proto.model_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.model.GetNextCaptureCropsResponse)
  })
_sym_db.RegisterMessage(GetNextCaptureCropsResponse)


DESCRIPTOR._options = None
_ENABLEDCROP.fields_by_name['carbon_name']._options = None
_PINMODELREQUEST.fields_by_name['crop']._options = None
_UNPINMODELREQUEST.fields_by_name['crop']._options = None
_GETNEXTMODELSTATEREQUEST.fields_by_name['crop']._options = None
_GETMODELNICKNAMESRESPONSE_MODELNICKNAMESENTRY._options = None

_MODELSERVICE = _descriptor.ServiceDescriptor(
  name='ModelService',
  full_name='carbon.frontend.model.ModelService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=3331,
  serialized_end=5421,
  methods=[
  _descriptor.MethodDescriptor(
    name='PinModel',
    full_name='carbon.frontend.model.ModelService.PinModel',
    index=0,
    containing_service=None,
    input_type=_PINMODELREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UnpinModel',
    full_name='carbon.frontend.model.ModelService.UnpinModel',
    index=1,
    containing_service=None,
    input_type=_UNPINMODELREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextModelState',
    full_name='carbon.frontend.model.ModelService.GetNextModelState',
    index=2,
    containing_service=None,
    input_type=_GETNEXTMODELSTATEREQUEST,
    output_type=_GETNEXTMODELSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextAllModelState',
    full_name='carbon.frontend.model.ModelService.GetNextAllModelState',
    index=3,
    containing_service=None,
    input_type=_GETNEXTMODELSTATEREQUEST,
    output_type=_GETNEXTMODELSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UpdateModel',
    full_name='carbon.frontend.model.ModelService.UpdateModel',
    index=4,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ListEnabledCrops',
    full_name='carbon.frontend.model.ModelService.ListEnabledCrops',
    index=5,
    containing_service=None,
    input_type=_LISTCROPPARAMETERS,
    output_type=_ENABLEDCROPLIST,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextEnabledCrops',
    full_name='carbon.frontend.model.ModelService.GetNextEnabledCrops',
    index=6,
    containing_service=None,
    input_type=_GETNEXTENABLEDCROPSREQUEST,
    output_type=_GETNEXTENABLEDCROPSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ListCaptureCrops',
    full_name='carbon.frontend.model.ModelService.ListCaptureCrops',
    index=7,
    containing_service=None,
    input_type=_LISTCROPPARAMETERS,
    output_type=_ENABLEDCROPLIST,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextCaptureCrops',
    full_name='carbon.frontend.model.ModelService.GetNextCaptureCrops',
    index=8,
    containing_service=None,
    input_type=_GETNEXTCAPTURECROPSREQUEST,
    output_type=_GETNEXTCAPTURECROPSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextSelectedCropID',
    full_name='carbon.frontend.model.ModelService.GetNextSelectedCropID',
    index=9,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTSELECTEDCROPIDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SelectCrop',
    full_name='carbon.frontend.model.ModelService.SelectCrop',
    index=10,
    containing_service=None,
    input_type=_SELECTCROPREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DownloadModel',
    full_name='carbon.frontend.model.ModelService.DownloadModel',
    index=11,
    containing_service=None,
    input_type=_DOWNLOADMODELREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextModelHistory',
    full_name='carbon.frontend.model.ModelService.GetNextModelHistory',
    index=12,
    containing_service=None,
    input_type=_MODELHISTORYREQUEST,
    output_type=_MODELHISTORYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetModelHistory',
    full_name='carbon.frontend.model.ModelService.GetModelHistory',
    index=13,
    containing_service=None,
    input_type=_MODELHISTORYREQUEST,
    output_type=_MODELHISTORYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetModelNicknames',
    full_name='carbon.frontend.model.ModelService.GetModelNicknames',
    index=14,
    containing_service=None,
    input_type=_GETMODELNICKNAMESREQUEST,
    output_type=_GETMODELNICKNAMESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextModelNicknames',
    full_name='carbon.frontend.model.ModelService.GetNextModelNicknames',
    index=15,
    containing_service=None,
    input_type=_GETMODELNICKNAMESREQUEST,
    output_type=_GETMODELNICKNAMESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetModelNickname',
    full_name='carbon.frontend.model.ModelService.SetModelNickname',
    index=16,
    containing_service=None,
    input_type=_SETMODELNICKNAMEREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='RefreshDefaultModelParameters',
    full_name='carbon.frontend.model.ModelService.RefreshDefaultModelParameters',
    index=17,
    containing_service=None,
    input_type=_REFRESHDEFAULTMODELPARAMETERSREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SyncCropIDs',
    full_name='carbon.frontend.model.ModelService.SyncCropIDs',
    index=18,
    containing_service=None,
    input_type=_SYNCCROPIDSREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='TriggerDownload',
    full_name='carbon.frontend.model.ModelService.TriggerDownload',
    index=19,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_MODELSERVICE)

DESCRIPTOR.services_by_name['ModelService'] = _MODELSERVICE

# @@protoc_insertion_point(module_scope)

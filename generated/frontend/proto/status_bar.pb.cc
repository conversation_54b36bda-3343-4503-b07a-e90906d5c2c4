// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/status_bar.proto

#include "frontend/proto/status_bar.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace status_bar {
constexpr GlobalStatus::GlobalStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : hint_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , icon_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , icon_color_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GlobalStatusDefaultTypeInternal {
  constexpr GlobalStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GlobalStatusDefaultTypeInternal() {}
  union {
    GlobalStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GlobalStatusDefaultTypeInternal _GlobalStatus_default_instance_;
constexpr ServiceStatus::ServiceStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , status_level_(0)
{}
struct ServiceStatusDefaultTypeInternal {
  constexpr ServiceStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ServiceStatusDefaultTypeInternal() {}
  union {
    ServiceStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ServiceStatusDefaultTypeInternal _ServiceStatus_default_instance_;
constexpr ServerStatus::ServerStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : service_status_()
  , status_level_(0)
{}
struct ServerStatusDefaultTypeInternal {
  constexpr ServerStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ServerStatusDefaultTypeInternal() {}
  union {
    ServerStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ServerStatusDefaultTypeInternal _ServerStatus_default_instance_;
constexpr TranslatedStatusMessageDetails::TranslatedStatusMessageDetails(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct TranslatedStatusMessageDetailsDefaultTypeInternal {
  constexpr TranslatedStatusMessageDetailsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TranslatedStatusMessageDetailsDefaultTypeInternal() {}
  union {
    TranslatedStatusMessageDetails _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TranslatedStatusMessageDetailsDefaultTypeInternal _TranslatedStatusMessageDetails_default_instance_;
constexpr TranslatedStatusMessage::TranslatedStatusMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : prefix_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , details_(nullptr){}
struct TranslatedStatusMessageDefaultTypeInternal {
  constexpr TranslatedStatusMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TranslatedStatusMessageDefaultTypeInternal() {}
  union {
    TranslatedStatusMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TranslatedStatusMessageDefaultTypeInternal _TranslatedStatusMessage_default_instance_;
constexpr StatusBarMessage_RowStatusEntry_DoNotUse::StatusBarMessage_RowStatusEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct StatusBarMessage_RowStatusEntry_DoNotUseDefaultTypeInternal {
  constexpr StatusBarMessage_RowStatusEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StatusBarMessage_RowStatusEntry_DoNotUseDefaultTypeInternal() {}
  union {
    StatusBarMessage_RowStatusEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StatusBarMessage_RowStatusEntry_DoNotUseDefaultTypeInternal _StatusBarMessage_RowStatusEntry_DoNotUse_default_instance_;
constexpr StatusBarMessage::StatusBarMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_status_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , global_statuses_()
  , status_message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , command_status_(nullptr)
  , translated_status_message_(nullptr)
  , lasers_enabled_(false)
  , weeding_enabled_(false)
  , status_level_(0)
{}
struct StatusBarMessageDefaultTypeInternal {
  constexpr StatusBarMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StatusBarMessageDefaultTypeInternal() {}
  union {
    StatusBarMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StatusBarMessageDefaultTypeInternal _StatusBarMessage_default_instance_;
constexpr ReportIssueRequest::ReportIssueRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , phone_number_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ReportIssueRequestDefaultTypeInternal {
  constexpr ReportIssueRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ReportIssueRequestDefaultTypeInternal() {}
  union {
    ReportIssueRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ReportIssueRequestDefaultTypeInternal _ReportIssueRequest_default_instance_;
constexpr SupportPhoneMessage::SupportPhoneMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : support_phone_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SupportPhoneMessageDefaultTypeInternal {
  constexpr SupportPhoneMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SupportPhoneMessageDefaultTypeInternal() {}
  union {
    SupportPhoneMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SupportPhoneMessageDefaultTypeInternal _SupportPhoneMessage_default_instance_;
}  // namespace status_bar
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[9];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fstatus_5fbar_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fstatus_5fbar_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::GlobalStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::GlobalStatus, hint_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::GlobalStatus, icon_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::GlobalStatus, icon_color_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ServiceStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ServiceStatus, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ServiceStatus, status_level_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ServerStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ServerStatus, status_level_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ServerStatus, service_status_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::TranslatedStatusMessageDetails, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::TranslatedStatusMessageDetails, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::TranslatedStatusMessageDetails, details_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::TranslatedStatusMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::TranslatedStatusMessage, prefix_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::TranslatedStatusMessage, details_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, lasers_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, weeding_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, status_level_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, status_message_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, serial_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, row_status_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, command_status_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, global_statuses_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::StatusBarMessage, translated_status_message_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ReportIssueRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ReportIssueRequest, description_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::ReportIssueRequest, phone_number_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::SupportPhoneMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::status_bar::SupportPhoneMessage, support_phone_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::status_bar::GlobalStatus)},
  { 9, -1, -1, sizeof(::carbon::frontend::status_bar::ServiceStatus)},
  { 17, -1, -1, sizeof(::carbon::frontend::status_bar::ServerStatus)},
  { 25, -1, -1, sizeof(::carbon::frontend::status_bar::TranslatedStatusMessageDetails)},
  { 34, -1, -1, sizeof(::carbon::frontend::status_bar::TranslatedStatusMessage)},
  { 42, 50, -1, sizeof(::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse)},
  { 52, -1, -1, sizeof(::carbon::frontend::status_bar::StatusBarMessage)},
  { 68, -1, -1, sizeof(::carbon::frontend::status_bar::ReportIssueRequest)},
  { 76, -1, -1, sizeof(::carbon::frontend::status_bar::SupportPhoneMessage)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_GlobalStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_ServiceStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_ServerStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_TranslatedStatusMessageDetails_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_TranslatedStatusMessage_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_StatusBarMessage_RowStatusEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_StatusBarMessage_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_ReportIssueRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::status_bar::_SupportPhoneMessage_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fstatus_5fbar_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\037frontend/proto/status_bar.proto\022\032carbo"
  "n.frontend.status_bar\032 frontend/proto/tr"
  "anslation.proto\032\031frontend/proto/util.pro"
  "to\"C\n\014GlobalStatus\022\014\n\004hint\030\001 \001(\t\022\021\n\ticon"
  "_name\030\002 \001(\t\022\022\n\nicon_color\030\003 \001(\t\"\\\n\rServi"
  "ceStatus\022\014\n\004name\030\001 \001(\t\022=\n\014status_level\030\002"
  " \001(\0162\'.carbon.frontend.status_bar.Status"
  "Level\"\220\001\n\014ServerStatus\022=\n\014status_level\030\001"
  " \001(\0162\'.carbon.frontend.status_bar.Status"
  "Level\022A\n\016service_status\030\002 \003(\0132).carbon.f"
  "rontend.status_bar.ServiceStatus\"\206\001\n\036Tra"
  "nslatedStatusMessageDetails\022\034\n\022details_s"
  "tring_key\030\001 \001(\tH\000\022;\n\005timer\030\002 \001(\0132*.carbo"
  "n.frontend.translation.DurationValueH\000B\t"
  "\n\007details\"v\n\027TranslatedStatusMessage\022\016\n\006"
  "prefix\030\001 \001(\t\022K\n\007details\030\002 \001(\0132:.carbon.f"
  "rontend.status_bar.TranslatedStatusMessa"
  "geDetails\"\334\004\n\020StatusBarMessage\022+\n\002ts\030\001 \001"
  "(\0132\037.carbon.frontend.util.Timestamp\022\026\n\016l"
  "asers_enabled\030\003 \001(\010\022\027\n\017weeding_enabled\030\004"
  " \001(\010\0228\n\014status_level\030\005 \001(\0162\".carbon.fron"
  "tend.status_bar.Status\022\026\n\016status_message"
  "\030\006 \001(\t\022\016\n\006serial\030\007 \001(\t\022O\n\nrow_status\030\010 \003"
  "(\0132;.carbon.frontend.status_bar.StatusBa"
  "rMessage.RowStatusEntry\022@\n\016command_statu"
  "s\030\t \001(\0132(.carbon.frontend.status_bar.Ser"
  "verStatus\022A\n\017global_statuses\030\n \003(\0132(.car"
  "bon.frontend.status_bar.GlobalStatus\022V\n\031"
  "translated_status_message\030\013 \001(\01323.carbon"
  ".frontend.status_bar.TranslatedStatusMes"
  "sage\032Z\n\016RowStatusEntry\022\013\n\003key\030\001 \001(\005\0227\n\005v"
  "alue\030\002 \001(\0132(.carbon.frontend.status_bar."
  "ServerStatus:\0028\001\"\?\n\022ReportIssueRequest\022\023"
  "\n\013description\030\001 \001(\t\022\024\n\014phone_number\030\002 \001("
  "\t\",\n\023SupportPhoneMessage\022\025\n\rsupport_phon"
  "e\030\001 \001(\t*\352\003\n\006Status\022\020\n\014STATUS_ERROR\020\000\022\023\n\017"
  "STATUS_ESTOPPED\020\001\022\024\n\020STATUS_PRE_ARMED\020\002\022"
  "\027\n\023STATUS_POWERED_DOWN\020\003\022\026\n\022STATUS_POWER"
  "ING_UP\020\004\022\034\n\030STATUS_UPDATE_INSTALLING\020\005\022\030"
  "\n\024STATUS_MODEL_LOADING\020\006\022\033\n\027STATUS_MODEL"
  "_INSTALLING\020\007\022\022\n\016STATUS_WEEDING\020\010\022\022\n\016STA"
  "TUS_STANDBY\020\t\022\022\n\016STATUS_UNKNOWN\020\n\022\027\n\023STA"
  "TUS_DISCONNECTED\020\013\022\021\n\rSTATUS_LIFTED\020\014\022\022\n"
  "\016STATUS_LOADING\020\r\022$\n STATUS_ALARM_AUTOFI"
  "X_IN_PROGRESS\020\016\022\035\n\031STATUS_FAILED_TO_POWE"
  "R_UP\020\017\022\"\n\036STATUS_SERVER_CABINET_COOLDOWN"
  "\020\020\022\033\n\027STATUS_CHILLER_COOLDOWN\020\021\022\033\n\027STATU"
  "S_TRACTOR_NOT_SAFE\020\022*D\n\013StatusLevel\022\013\n\007I"
  "NVALID\020\000\022\t\n\005READY\020\001\022\013\n\007LOADING\020\002\022\020\n\010ESTO"
  "PPED\020\003\032\002\010\0012\257\002\n\020StatusBarService\022^\n\rGetNe"
  "xtStatus\022\037.carbon.frontend.util.Timestam"
  "p\032,.carbon.frontend.status_bar.StatusBar"
  "Message\022Z\n\013ReportIssue\022..carbon.frontend"
  ".status_bar.ReportIssueRequest\032\033.carbon."
  "frontend.util.Empty\022_\n\017GetSupportPhone\022\033"
  ".carbon.frontend.util.Empty\032/.carbon.fro"
  "ntend.status_bar.SupportPhoneMessageB\020Z\016"
  "proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2ftranslation_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto = {
  false, false, 2302, descriptor_table_protodef_frontend_2fproto_2fstatus_5fbar_2eproto, "frontend/proto/status_bar.proto", 
  &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once, descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_deps, 2, 9,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto, file_level_enum_descriptors_frontend_2fproto_2fstatus_5fbar_2eproto, file_level_service_descriptors_frontend_2fproto_2fstatus_5fbar_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fstatus_5fbar_2eproto(&descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto);
namespace carbon {
namespace frontend {
namespace status_bar {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Status_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fstatus_5fbar_2eproto[0];
}
bool Status_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
    case 9:
    case 10:
    case 11:
    case 12:
    case 13:
    case 14:
    case 15:
    case 16:
    case 17:
    case 18:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* StatusLevel_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fstatus_5fbar_2eproto[1];
}
bool StatusLevel_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class GlobalStatus::_Internal {
 public:
};

GlobalStatus::GlobalStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.status_bar.GlobalStatus)
}
GlobalStatus::GlobalStatus(const GlobalStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  hint_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    hint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_hint().empty()) {
    hint_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_hint(), 
      GetArenaForAllocation());
  }
  icon_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    icon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_icon_name().empty()) {
    icon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_icon_name(), 
      GetArenaForAllocation());
  }
  icon_color_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    icon_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_icon_color().empty()) {
    icon_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_icon_color(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.status_bar.GlobalStatus)
}

inline void GlobalStatus::SharedCtor() {
hint_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  hint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
icon_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  icon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
icon_color_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  icon_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GlobalStatus::~GlobalStatus() {
  // @@protoc_insertion_point(destructor:carbon.frontend.status_bar.GlobalStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GlobalStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  hint_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  icon_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  icon_color_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GlobalStatus::ArenaDtor(void* object) {
  GlobalStatus* _this = reinterpret_cast< GlobalStatus* >(object);
  (void)_this;
}
void GlobalStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GlobalStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GlobalStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.status_bar.GlobalStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  hint_.ClearToEmpty();
  icon_name_.ClearToEmpty();
  icon_color_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GlobalStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string hint = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_hint();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.GlobalStatus.hint"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string icon_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_icon_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.GlobalStatus.icon_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string icon_color = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_icon_color();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.GlobalStatus.icon_color"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GlobalStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.status_bar.GlobalStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string hint = 1;
  if (!this->_internal_hint().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_hint().data(), static_cast<int>(this->_internal_hint().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.GlobalStatus.hint");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_hint(), target);
  }

  // string icon_name = 2;
  if (!this->_internal_icon_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_icon_name().data(), static_cast<int>(this->_internal_icon_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.GlobalStatus.icon_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_icon_name(), target);
  }

  // string icon_color = 3;
  if (!this->_internal_icon_color().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_icon_color().data(), static_cast<int>(this->_internal_icon_color().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.GlobalStatus.icon_color");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_icon_color(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.status_bar.GlobalStatus)
  return target;
}

size_t GlobalStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.status_bar.GlobalStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string hint = 1;
  if (!this->_internal_hint().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_hint());
  }

  // string icon_name = 2;
  if (!this->_internal_icon_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icon_name());
  }

  // string icon_color = 3;
  if (!this->_internal_icon_color().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icon_color());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GlobalStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GlobalStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GlobalStatus::GetClassData() const { return &_class_data_; }

void GlobalStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GlobalStatus *>(to)->MergeFrom(
      static_cast<const GlobalStatus &>(from));
}


void GlobalStatus::MergeFrom(const GlobalStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.status_bar.GlobalStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_hint().empty()) {
    _internal_set_hint(from._internal_hint());
  }
  if (!from._internal_icon_name().empty()) {
    _internal_set_icon_name(from._internal_icon_name());
  }
  if (!from._internal_icon_color().empty()) {
    _internal_set_icon_color(from._internal_icon_color());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GlobalStatus::CopyFrom(const GlobalStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.status_bar.GlobalStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GlobalStatus::IsInitialized() const {
  return true;
}

void GlobalStatus::InternalSwap(GlobalStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &hint_, lhs_arena,
      &other->hint_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &icon_name_, lhs_arena,
      &other->icon_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &icon_color_, lhs_arena,
      &other->icon_color_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GlobalStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[0]);
}

// ===================================================================

class ServiceStatus::_Internal {
 public:
};

ServiceStatus::ServiceStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.status_bar.ServiceStatus)
}
ServiceStatus::ServiceStatus(const ServiceStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  status_level_ = from.status_level_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.status_bar.ServiceStatus)
}

inline void ServiceStatus::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
status_level_ = 0;
}

ServiceStatus::~ServiceStatus() {
  // @@protoc_insertion_point(destructor:carbon.frontend.status_bar.ServiceStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ServiceStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ServiceStatus::ArenaDtor(void* object) {
  ServiceStatus* _this = reinterpret_cast< ServiceStatus* >(object);
  (void)_this;
}
void ServiceStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ServiceStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ServiceStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.status_bar.ServiceStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  status_level_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ServiceStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.ServiceStatus.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.status_bar.StatusLevel status_level = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_status_level(static_cast<::carbon::frontend::status_bar::StatusLevel>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ServiceStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.status_bar.ServiceStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.ServiceStatus.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // .carbon.frontend.status_bar.StatusLevel status_level = 2;
  if (this->_internal_status_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_status_level(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.status_bar.ServiceStatus)
  return target;
}

size_t ServiceStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.status_bar.ServiceStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .carbon.frontend.status_bar.StatusLevel status_level = 2;
  if (this->_internal_status_level() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status_level());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ServiceStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ServiceStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ServiceStatus::GetClassData() const { return &_class_data_; }

void ServiceStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ServiceStatus *>(to)->MergeFrom(
      static_cast<const ServiceStatus &>(from));
}


void ServiceStatus::MergeFrom(const ServiceStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.status_bar.ServiceStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_status_level() != 0) {
    _internal_set_status_level(from._internal_status_level());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ServiceStatus::CopyFrom(const ServiceStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.status_bar.ServiceStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServiceStatus::IsInitialized() const {
  return true;
}

void ServiceStatus::InternalSwap(ServiceStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(status_level_, other->status_level_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ServiceStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[1]);
}

// ===================================================================

class ServerStatus::_Internal {
 public:
};

ServerStatus::ServerStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  service_status_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.status_bar.ServerStatus)
}
ServerStatus::ServerStatus(const ServerStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      service_status_(from.service_status_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  status_level_ = from.status_level_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.status_bar.ServerStatus)
}

inline void ServerStatus::SharedCtor() {
status_level_ = 0;
}

ServerStatus::~ServerStatus() {
  // @@protoc_insertion_point(destructor:carbon.frontend.status_bar.ServerStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ServerStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ServerStatus::ArenaDtor(void* object) {
  ServerStatus* _this = reinterpret_cast< ServerStatus* >(object);
  (void)_this;
}
void ServerStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ServerStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ServerStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.status_bar.ServerStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  service_status_.Clear();
  status_level_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ServerStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.status_bar.StatusLevel status_level = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_status_level(static_cast<::carbon::frontend::status_bar::StatusLevel>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.status_bar.ServiceStatus service_status = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_service_status(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ServerStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.status_bar.ServerStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.status_bar.StatusLevel status_level = 1;
  if (this->_internal_status_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_status_level(), target);
  }

  // repeated .carbon.frontend.status_bar.ServiceStatus service_status = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_service_status_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_service_status(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.status_bar.ServerStatus)
  return target;
}

size_t ServerStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.status_bar.ServerStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.status_bar.ServiceStatus service_status = 2;
  total_size += 1UL * this->_internal_service_status_size();
  for (const auto& msg : this->service_status_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.status_bar.StatusLevel status_level = 1;
  if (this->_internal_status_level() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status_level());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ServerStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ServerStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ServerStatus::GetClassData() const { return &_class_data_; }

void ServerStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ServerStatus *>(to)->MergeFrom(
      static_cast<const ServerStatus &>(from));
}


void ServerStatus::MergeFrom(const ServerStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.status_bar.ServerStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  service_status_.MergeFrom(from.service_status_);
  if (from._internal_status_level() != 0) {
    _internal_set_status_level(from._internal_status_level());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ServerStatus::CopyFrom(const ServerStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.status_bar.ServerStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ServerStatus::IsInitialized() const {
  return true;
}

void ServerStatus::InternalSwap(ServerStatus* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  service_status_.InternalSwap(&other->service_status_);
  swap(status_level_, other->status_level_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ServerStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[2]);
}

// ===================================================================

class TranslatedStatusMessageDetails::_Internal {
 public:
  static const ::carbon::frontend::translation::DurationValue& timer(const TranslatedStatusMessageDetails* msg);
};

const ::carbon::frontend::translation::DurationValue&
TranslatedStatusMessageDetails::_Internal::timer(const TranslatedStatusMessageDetails* msg) {
  return *msg->details_.timer_;
}
void TranslatedStatusMessageDetails::set_allocated_timer(::carbon::frontend::translation::DurationValue* timer) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_details();
  if (timer) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(timer));
    if (message_arena != submessage_arena) {
      timer = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, timer, submessage_arena);
    }
    set_has_timer();
    details_.timer_ = timer;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.TranslatedStatusMessageDetails.timer)
}
void TranslatedStatusMessageDetails::clear_timer() {
  if (_internal_has_timer()) {
    if (GetArenaForAllocation() == nullptr) {
      delete details_.timer_;
    }
    clear_has_details();
  }
}
TranslatedStatusMessageDetails::TranslatedStatusMessageDetails(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
}
TranslatedStatusMessageDetails::TranslatedStatusMessageDetails(const TranslatedStatusMessageDetails& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_details();
  switch (from.details_case()) {
    case kDetailsStringKey: {
      _internal_set_details_string_key(from._internal_details_string_key());
      break;
    }
    case kTimer: {
      _internal_mutable_timer()->::carbon::frontend::translation::DurationValue::MergeFrom(from._internal_timer());
      break;
    }
    case DETAILS_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
}

inline void TranslatedStatusMessageDetails::SharedCtor() {
clear_has_details();
}

TranslatedStatusMessageDetails::~TranslatedStatusMessageDetails() {
  // @@protoc_insertion_point(destructor:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TranslatedStatusMessageDetails::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_details()) {
    clear_details();
  }
}

void TranslatedStatusMessageDetails::ArenaDtor(void* object) {
  TranslatedStatusMessageDetails* _this = reinterpret_cast< TranslatedStatusMessageDetails* >(object);
  (void)_this;
}
void TranslatedStatusMessageDetails::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TranslatedStatusMessageDetails::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TranslatedStatusMessageDetails::clear_details() {
// @@protoc_insertion_point(one_of_clear_start:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  switch (details_case()) {
    case kDetailsStringKey: {
      details_.details_string_key_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
      break;
    }
    case kTimer: {
      if (GetArenaForAllocation() == nullptr) {
        delete details_.timer_;
      }
      break;
    }
    case DETAILS_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = DETAILS_NOT_SET;
}


void TranslatedStatusMessageDetails::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_details();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TranslatedStatusMessageDetails::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string details_string_key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_details_string_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.TranslatedStatusMessageDetails.details_string_key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.DurationValue timer = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_timer(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TranslatedStatusMessageDetails::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string details_string_key = 1;
  if (_internal_has_details_string_key()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_details_string_key().data(), static_cast<int>(this->_internal_details_string_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.TranslatedStatusMessageDetails.details_string_key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_details_string_key(), target);
  }

  // .carbon.frontend.translation.DurationValue timer = 2;
  if (_internal_has_timer()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::timer(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  return target;
}

size_t TranslatedStatusMessageDetails::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (details_case()) {
    // string details_string_key = 1;
    case kDetailsStringKey: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_details_string_key());
      break;
    }
    // .carbon.frontend.translation.DurationValue timer = 2;
    case kTimer: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *details_.timer_);
      break;
    }
    case DETAILS_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TranslatedStatusMessageDetails::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TranslatedStatusMessageDetails::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TranslatedStatusMessageDetails::GetClassData() const { return &_class_data_; }

void TranslatedStatusMessageDetails::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TranslatedStatusMessageDetails *>(to)->MergeFrom(
      static_cast<const TranslatedStatusMessageDetails &>(from));
}


void TranslatedStatusMessageDetails::MergeFrom(const TranslatedStatusMessageDetails& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.details_case()) {
    case kDetailsStringKey: {
      _internal_set_details_string_key(from._internal_details_string_key());
      break;
    }
    case kTimer: {
      _internal_mutable_timer()->::carbon::frontend::translation::DurationValue::MergeFrom(from._internal_timer());
      break;
    }
    case DETAILS_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TranslatedStatusMessageDetails::CopyFrom(const TranslatedStatusMessageDetails& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TranslatedStatusMessageDetails::IsInitialized() const {
  return true;
}

void TranslatedStatusMessageDetails::InternalSwap(TranslatedStatusMessageDetails* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(details_, other->details_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata TranslatedStatusMessageDetails::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[3]);
}

// ===================================================================

class TranslatedStatusMessage::_Internal {
 public:
  static const ::carbon::frontend::status_bar::TranslatedStatusMessageDetails& details(const TranslatedStatusMessage* msg);
};

const ::carbon::frontend::status_bar::TranslatedStatusMessageDetails&
TranslatedStatusMessage::_Internal::details(const TranslatedStatusMessage* msg) {
  return *msg->details_;
}
TranslatedStatusMessage::TranslatedStatusMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.status_bar.TranslatedStatusMessage)
}
TranslatedStatusMessage::TranslatedStatusMessage(const TranslatedStatusMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  prefix_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    prefix_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_prefix().empty()) {
    prefix_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_prefix(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_details()) {
    details_ = new ::carbon::frontend::status_bar::TranslatedStatusMessageDetails(*from.details_);
  } else {
    details_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.status_bar.TranslatedStatusMessage)
}

inline void TranslatedStatusMessage::SharedCtor() {
prefix_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  prefix_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
details_ = nullptr;
}

TranslatedStatusMessage::~TranslatedStatusMessage() {
  // @@protoc_insertion_point(destructor:carbon.frontend.status_bar.TranslatedStatusMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TranslatedStatusMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  prefix_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete details_;
}

void TranslatedStatusMessage::ArenaDtor(void* object) {
  TranslatedStatusMessage* _this = reinterpret_cast< TranslatedStatusMessage* >(object);
  (void)_this;
}
void TranslatedStatusMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TranslatedStatusMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TranslatedStatusMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.status_bar.TranslatedStatusMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  prefix_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && details_ != nullptr) {
    delete details_;
  }
  details_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TranslatedStatusMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string prefix = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_prefix();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.TranslatedStatusMessage.prefix"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.status_bar.TranslatedStatusMessageDetails details = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_details(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TranslatedStatusMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.status_bar.TranslatedStatusMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string prefix = 1;
  if (!this->_internal_prefix().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_prefix().data(), static_cast<int>(this->_internal_prefix().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.TranslatedStatusMessage.prefix");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_prefix(), target);
  }

  // .carbon.frontend.status_bar.TranslatedStatusMessageDetails details = 2;
  if (this->_internal_has_details()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::details(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.status_bar.TranslatedStatusMessage)
  return target;
}

size_t TranslatedStatusMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.status_bar.TranslatedStatusMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string prefix = 1;
  if (!this->_internal_prefix().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_prefix());
  }

  // .carbon.frontend.status_bar.TranslatedStatusMessageDetails details = 2;
  if (this->_internal_has_details()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *details_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TranslatedStatusMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TranslatedStatusMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TranslatedStatusMessage::GetClassData() const { return &_class_data_; }

void TranslatedStatusMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TranslatedStatusMessage *>(to)->MergeFrom(
      static_cast<const TranslatedStatusMessage &>(from));
}


void TranslatedStatusMessage::MergeFrom(const TranslatedStatusMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.status_bar.TranslatedStatusMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_prefix().empty()) {
    _internal_set_prefix(from._internal_prefix());
  }
  if (from._internal_has_details()) {
    _internal_mutable_details()->::carbon::frontend::status_bar::TranslatedStatusMessageDetails::MergeFrom(from._internal_details());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TranslatedStatusMessage::CopyFrom(const TranslatedStatusMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.status_bar.TranslatedStatusMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TranslatedStatusMessage::IsInitialized() const {
  return true;
}

void TranslatedStatusMessage::InternalSwap(TranslatedStatusMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &prefix_, lhs_arena,
      &other->prefix_, rhs_arena
  );
  swap(details_, other->details_);
}

::PROTOBUF_NAMESPACE_ID::Metadata TranslatedStatusMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[4]);
}

// ===================================================================

StatusBarMessage_RowStatusEntry_DoNotUse::StatusBarMessage_RowStatusEntry_DoNotUse() {}
StatusBarMessage_RowStatusEntry_DoNotUse::StatusBarMessage_RowStatusEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void StatusBarMessage_RowStatusEntry_DoNotUse::MergeFrom(const StatusBarMessage_RowStatusEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata StatusBarMessage_RowStatusEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[5]);
}

// ===================================================================

class StatusBarMessage::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const StatusBarMessage* msg);
  static const ::carbon::frontend::status_bar::ServerStatus& command_status(const StatusBarMessage* msg);
  static const ::carbon::frontend::status_bar::TranslatedStatusMessage& translated_status_message(const StatusBarMessage* msg);
};

const ::carbon::frontend::util::Timestamp&
StatusBarMessage::_Internal::ts(const StatusBarMessage* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::status_bar::ServerStatus&
StatusBarMessage::_Internal::command_status(const StatusBarMessage* msg) {
  return *msg->command_status_;
}
const ::carbon::frontend::status_bar::TranslatedStatusMessage&
StatusBarMessage::_Internal::translated_status_message(const StatusBarMessage* msg) {
  return *msg->translated_status_message_;
}
void StatusBarMessage::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
StatusBarMessage::StatusBarMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  row_status_(arena),
  global_statuses_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.status_bar.StatusBarMessage)
}
StatusBarMessage::StatusBarMessage(const StatusBarMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      global_statuses_(from.global_statuses_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_status_.MergeFrom(from.row_status_);
  status_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    status_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_status_message().empty()) {
    status_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_status_message(), 
      GetArenaForAllocation());
  }
  serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serial().empty()) {
    serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serial(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_command_status()) {
    command_status_ = new ::carbon::frontend::status_bar::ServerStatus(*from.command_status_);
  } else {
    command_status_ = nullptr;
  }
  if (from._internal_has_translated_status_message()) {
    translated_status_message_ = new ::carbon::frontend::status_bar::TranslatedStatusMessage(*from.translated_status_message_);
  } else {
    translated_status_message_ = nullptr;
  }
  ::memcpy(&lasers_enabled_, &from.lasers_enabled_,
    static_cast<size_t>(reinterpret_cast<char*>(&status_level_) -
    reinterpret_cast<char*>(&lasers_enabled_)) + sizeof(status_level_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.status_bar.StatusBarMessage)
}

inline void StatusBarMessage::SharedCtor() {
status_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  status_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&status_level_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(status_level_));
}

StatusBarMessage::~StatusBarMessage() {
  // @@protoc_insertion_point(destructor:carbon.frontend.status_bar.StatusBarMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StatusBarMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  status_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete command_status_;
  if (this != internal_default_instance()) delete translated_status_message_;
}

void StatusBarMessage::ArenaDtor(void* object) {
  StatusBarMessage* _this = reinterpret_cast< StatusBarMessage* >(object);
  (void)_this;
  _this->row_status_. ~MapField();
}
inline void StatusBarMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &StatusBarMessage::ArenaDtor);
  }
}
void StatusBarMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StatusBarMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.status_bar.StatusBarMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  row_status_.Clear();
  global_statuses_.Clear();
  status_message_.ClearToEmpty();
  serial_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && command_status_ != nullptr) {
    delete command_status_;
  }
  command_status_ = nullptr;
  if (GetArenaForAllocation() == nullptr && translated_status_message_ != nullptr) {
    delete translated_status_message_;
  }
  translated_status_message_ = nullptr;
  ::memset(&lasers_enabled_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&status_level_) -
      reinterpret_cast<char*>(&lasers_enabled_)) + sizeof(status_level_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StatusBarMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool lasers_enabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          lasers_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool weeding_enabled = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          weeding_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.status_bar.Status status_level = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_status_level(static_cast<::carbon::frontend::status_bar::Status>(val));
        } else
          goto handle_unusual;
        continue;
      // string status_message = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_status_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.StatusBarMessage.status_message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string serial = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.StatusBarMessage.serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<int32, .carbon.frontend.status_bar.ServerStatus> row_status = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&row_status_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<66>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.status_bar.ServerStatus command_status = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_command_status(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.status_bar.GlobalStatus global_statuses = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_global_statuses(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<82>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_translated_status_message(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StatusBarMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.status_bar.StatusBarMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // bool lasers_enabled = 3;
  if (this->_internal_lasers_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_lasers_enabled(), target);
  }

  // bool weeding_enabled = 4;
  if (this->_internal_weeding_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_weeding_enabled(), target);
  }

  // .carbon.frontend.status_bar.Status status_level = 5;
  if (this->_internal_status_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->_internal_status_level(), target);
  }

  // string status_message = 6;
  if (!this->_internal_status_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_status_message().data(), static_cast<int>(this->_internal_status_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.StatusBarMessage.status_message");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_status_message(), target);
  }

  // string serial = 7;
  if (!this->_internal_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serial().data(), static_cast<int>(this->_internal_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.StatusBarMessage.serial");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_serial(), target);
  }

  // map<int32, .carbon.frontend.status_bar.ServerStatus> row_status = 8;
  if (!this->_internal_row_status().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_row_status().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_row_status().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >::const_iterator
          it = this->_internal_row_status().begin();
          it != this->_internal_row_status().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = StatusBarMessage_RowStatusEntry_DoNotUse::Funcs::InternalSerialize(8, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >::const_iterator
          it = this->_internal_row_status().begin();
          it != this->_internal_row_status().end(); ++it) {
        target = StatusBarMessage_RowStatusEntry_DoNotUse::Funcs::InternalSerialize(8, it->first, it->second, target, stream);
      }
    }
  }

  // .carbon.frontend.status_bar.ServerStatus command_status = 9;
  if (this->_internal_has_command_status()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::command_status(this), target, stream);
  }

  // repeated .carbon.frontend.status_bar.GlobalStatus global_statuses = 10;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_global_statuses_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(10, this->_internal_global_statuses(i), target, stream);
  }

  // .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 11;
  if (this->_internal_has_translated_status_message()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::translated_status_message(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.status_bar.StatusBarMessage)
  return target;
}

size_t StatusBarMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.status_bar.StatusBarMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<int32, .carbon.frontend.status_bar.ServerStatus> row_status = 8;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_row_status_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >::const_iterator
      it = this->_internal_row_status().begin();
      it != this->_internal_row_status().end(); ++it) {
    total_size += StatusBarMessage_RowStatusEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // repeated .carbon.frontend.status_bar.GlobalStatus global_statuses = 10;
  total_size += 1UL * this->_internal_global_statuses_size();
  for (const auto& msg : this->global_statuses_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string status_message = 6;
  if (!this->_internal_status_message().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_status_message());
  }

  // string serial = 7;
  if (!this->_internal_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serial());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.status_bar.ServerStatus command_status = 9;
  if (this->_internal_has_command_status()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *command_status_);
  }

  // .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 11;
  if (this->_internal_has_translated_status_message()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *translated_status_message_);
  }

  // bool lasers_enabled = 3;
  if (this->_internal_lasers_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool weeding_enabled = 4;
  if (this->_internal_weeding_enabled() != 0) {
    total_size += 1 + 1;
  }

  // .carbon.frontend.status_bar.Status status_level = 5;
  if (this->_internal_status_level() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_status_level());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StatusBarMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StatusBarMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StatusBarMessage::GetClassData() const { return &_class_data_; }

void StatusBarMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StatusBarMessage *>(to)->MergeFrom(
      static_cast<const StatusBarMessage &>(from));
}


void StatusBarMessage::MergeFrom(const StatusBarMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.status_bar.StatusBarMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  row_status_.MergeFrom(from.row_status_);
  global_statuses_.MergeFrom(from.global_statuses_);
  if (!from._internal_status_message().empty()) {
    _internal_set_status_message(from._internal_status_message());
  }
  if (!from._internal_serial().empty()) {
    _internal_set_serial(from._internal_serial());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_command_status()) {
    _internal_mutable_command_status()->::carbon::frontend::status_bar::ServerStatus::MergeFrom(from._internal_command_status());
  }
  if (from._internal_has_translated_status_message()) {
    _internal_mutable_translated_status_message()->::carbon::frontend::status_bar::TranslatedStatusMessage::MergeFrom(from._internal_translated_status_message());
  }
  if (from._internal_lasers_enabled() != 0) {
    _internal_set_lasers_enabled(from._internal_lasers_enabled());
  }
  if (from._internal_weeding_enabled() != 0) {
    _internal_set_weeding_enabled(from._internal_weeding_enabled());
  }
  if (from._internal_status_level() != 0) {
    _internal_set_status_level(from._internal_status_level());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StatusBarMessage::CopyFrom(const StatusBarMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.status_bar.StatusBarMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StatusBarMessage::IsInitialized() const {
  return true;
}

void StatusBarMessage::InternalSwap(StatusBarMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  row_status_.InternalSwap(&other->row_status_);
  global_statuses_.InternalSwap(&other->global_statuses_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &status_message_, lhs_arena,
      &other->status_message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serial_, lhs_arena,
      &other->serial_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(StatusBarMessage, status_level_)
      + sizeof(StatusBarMessage::status_level_)
      - PROTOBUF_FIELD_OFFSET(StatusBarMessage, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata StatusBarMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[6]);
}

// ===================================================================

class ReportIssueRequest::_Internal {
 public:
};

ReportIssueRequest::ReportIssueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.status_bar.ReportIssueRequest)
}
ReportIssueRequest::ReportIssueRequest(const ReportIssueRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  phone_number_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    phone_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_phone_number().empty()) {
    phone_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_phone_number(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.status_bar.ReportIssueRequest)
}

inline void ReportIssueRequest::SharedCtor() {
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
phone_number_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  phone_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ReportIssueRequest::~ReportIssueRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.status_bar.ReportIssueRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ReportIssueRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  phone_number_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ReportIssueRequest::ArenaDtor(void* object) {
  ReportIssueRequest* _this = reinterpret_cast< ReportIssueRequest* >(object);
  (void)_this;
}
void ReportIssueRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ReportIssueRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ReportIssueRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.status_bar.ReportIssueRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  description_.ClearToEmpty();
  phone_number_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ReportIssueRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string description = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.ReportIssueRequest.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string phone_number = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_phone_number();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.ReportIssueRequest.phone_number"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ReportIssueRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.status_bar.ReportIssueRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string description = 1;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.ReportIssueRequest.description");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_description(), target);
  }

  // string phone_number = 2;
  if (!this->_internal_phone_number().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_phone_number().data(), static_cast<int>(this->_internal_phone_number().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.ReportIssueRequest.phone_number");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_phone_number(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.status_bar.ReportIssueRequest)
  return target;
}

size_t ReportIssueRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.status_bar.ReportIssueRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string description = 1;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // string phone_number = 2;
  if (!this->_internal_phone_number().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_phone_number());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ReportIssueRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ReportIssueRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ReportIssueRequest::GetClassData() const { return &_class_data_; }

void ReportIssueRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ReportIssueRequest *>(to)->MergeFrom(
      static_cast<const ReportIssueRequest &>(from));
}


void ReportIssueRequest::MergeFrom(const ReportIssueRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.status_bar.ReportIssueRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (!from._internal_phone_number().empty()) {
    _internal_set_phone_number(from._internal_phone_number());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ReportIssueRequest::CopyFrom(const ReportIssueRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.status_bar.ReportIssueRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ReportIssueRequest::IsInitialized() const {
  return true;
}

void ReportIssueRequest::InternalSwap(ReportIssueRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &phone_number_, lhs_arena,
      &other->phone_number_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ReportIssueRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[7]);
}

// ===================================================================

class SupportPhoneMessage::_Internal {
 public:
};

SupportPhoneMessage::SupportPhoneMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.status_bar.SupportPhoneMessage)
}
SupportPhoneMessage::SupportPhoneMessage(const SupportPhoneMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  support_phone_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    support_phone_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_support_phone().empty()) {
    support_phone_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_support_phone(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.status_bar.SupportPhoneMessage)
}

inline void SupportPhoneMessage::SharedCtor() {
support_phone_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  support_phone_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SupportPhoneMessage::~SupportPhoneMessage() {
  // @@protoc_insertion_point(destructor:carbon.frontend.status_bar.SupportPhoneMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SupportPhoneMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  support_phone_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SupportPhoneMessage::ArenaDtor(void* object) {
  SupportPhoneMessage* _this = reinterpret_cast< SupportPhoneMessage* >(object);
  (void)_this;
}
void SupportPhoneMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SupportPhoneMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SupportPhoneMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.status_bar.SupportPhoneMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  support_phone_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SupportPhoneMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string support_phone = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_support_phone();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.status_bar.SupportPhoneMessage.support_phone"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SupportPhoneMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.status_bar.SupportPhoneMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string support_phone = 1;
  if (!this->_internal_support_phone().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_support_phone().data(), static_cast<int>(this->_internal_support_phone().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.status_bar.SupportPhoneMessage.support_phone");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_support_phone(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.status_bar.SupportPhoneMessage)
  return target;
}

size_t SupportPhoneMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.status_bar.SupportPhoneMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string support_phone = 1;
  if (!this->_internal_support_phone().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_support_phone());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SupportPhoneMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SupportPhoneMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SupportPhoneMessage::GetClassData() const { return &_class_data_; }

void SupportPhoneMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SupportPhoneMessage *>(to)->MergeFrom(
      static_cast<const SupportPhoneMessage &>(from));
}


void SupportPhoneMessage::MergeFrom(const SupportPhoneMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.status_bar.SupportPhoneMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_support_phone().empty()) {
    _internal_set_support_phone(from._internal_support_phone());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SupportPhoneMessage::CopyFrom(const SupportPhoneMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.status_bar.SupportPhoneMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SupportPhoneMessage::IsInitialized() const {
  return true;
}

void SupportPhoneMessage::InternalSwap(SupportPhoneMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &support_phone_, lhs_arena,
      &other->support_phone_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SupportPhoneMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_getter, &descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto_once,
      file_level_metadata_frontend_2fproto_2fstatus_5fbar_2eproto[8]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace status_bar
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::GlobalStatus* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::GlobalStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::GlobalStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::ServiceStatus* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::ServiceStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::ServiceStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::ServerStatus* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::ServerStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::ServerStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::TranslatedStatusMessageDetails >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::TranslatedStatusMessageDetails >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::TranslatedStatusMessage* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::TranslatedStatusMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::TranslatedStatusMessage >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::StatusBarMessage* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::StatusBarMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::StatusBarMessage >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::ReportIssueRequest* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::ReportIssueRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::ReportIssueRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::status_bar::SupportPhoneMessage* Arena::CreateMaybeMessage< ::carbon::frontend::status_bar::SupportPhoneMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::status_bar::SupportPhoneMessage >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

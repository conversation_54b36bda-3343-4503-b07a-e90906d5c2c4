// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/camera.proto

#include "frontend/proto/camera.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace camera {
constexpr CameraRequest::CameraRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct CameraRequestDefaultTypeInternal {
  constexpr CameraRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CameraRequestDefaultTypeInternal() {}
  union {
    CameraRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CameraRequestDefaultTypeInternal _CameraRequest_default_instance_;
constexpr Camera::Camera(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : camera_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , stream_host_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , rtc_info_(nullptr)
  , row_number_(0u)
  , type_(0)

  , stream_port_(0u)
  , width_(0u)
  , auto_focusable_(false)
  , transpose_(false)
  , connected_(false)
  , height_(0u){}
struct CameraDefaultTypeInternal {
  constexpr CameraDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CameraDefaultTypeInternal() {}
  union {
    Camera _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CameraDefaultTypeInternal _Camera_default_instance_;
constexpr RTCInfo::RTCInfo(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : host_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , stream_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct RTCInfoDefaultTypeInternal {
  constexpr RTCInfoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RTCInfoDefaultTypeInternal() {}
  union {
    RTCInfo _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RTCInfoDefaultTypeInternal _RTCInfo_default_instance_;
constexpr CameraList::CameraList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cameras_()
  , ts_(nullptr){}
struct CameraListDefaultTypeInternal {
  constexpr CameraListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CameraListDefaultTypeInternal() {}
  union {
    CameraList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CameraListDefaultTypeInternal _CameraList_default_instance_;
constexpr CameraListRequest::CameraListRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : type_(0)

  , include_disconnected_(false){}
struct CameraListRequestDefaultTypeInternal {
  constexpr CameraListRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CameraListRequestDefaultTypeInternal() {}
  union {
    CameraListRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CameraListRequestDefaultTypeInternal _CameraListRequest_default_instance_;
constexpr NextCameraListRequest::NextCameraListRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , type_(0)

  , include_disconnected_(false){}
struct NextCameraListRequestDefaultTypeInternal {
  constexpr NextCameraListRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~NextCameraListRequestDefaultTypeInternal() {}
  union {
    NextCameraListRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT NextCameraListRequestDefaultTypeInternal _NextCameraListRequest_default_instance_;
}  // namespace camera
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fcamera_2eproto[6];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fcamera_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fcamera_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fcamera_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::CameraRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::CameraRequest, cam_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, row_number_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, camera_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, auto_focusable_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, stream_host_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, stream_port_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, width_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, height_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, transpose_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, connected_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::Camera, rtc_info_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::RTCInfo, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::RTCInfo, host_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::RTCInfo, stream_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::CameraList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::CameraList, cameras_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::CameraList, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::CameraListRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::CameraListRequest, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::CameraListRequest, include_disconnected_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::NextCameraListRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::NextCameraListRequest, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::NextCameraListRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::camera::NextCameraListRequest, include_disconnected_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::camera::CameraRequest)},
  { 7, -1, -1, sizeof(::carbon::frontend::camera::Camera)},
  { 24, -1, -1, sizeof(::carbon::frontend::camera::RTCInfo)},
  { 32, -1, -1, sizeof(::carbon::frontend::camera::CameraList)},
  { 40, -1, -1, sizeof(::carbon::frontend::camera::CameraListRequest)},
  { 48, -1, -1, sizeof(::carbon::frontend::camera::NextCameraListRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::camera::_CameraRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::camera::_Camera_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::camera::_RTCInfo_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::camera::_CameraList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::camera::_CameraListRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::camera::_NextCameraListRequest_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fcamera_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033frontend/proto/camera.proto\022\026carbon.fr"
  "ontend.camera\032\031frontend/proto/util.proto"
  "\"\037\n\rCameraRequest\022\016\n\006cam_id\030\001 \001(\t\"\233\002\n\006Ca"
  "mera\022\022\n\nrow_number\030\001 \001(\r\022\021\n\tcamera_id\030\002 "
  "\001(\t\0220\n\004type\030\003 \001(\0162\".carbon.frontend.came"
  "ra.CameraType\022\026\n\016auto_focusable\030\004 \001(\010\022\023\n"
  "\013stream_host\030\005 \001(\t\022\023\n\013stream_port\030\006 \001(\r\022"
  "\r\n\005width\030\007 \001(\r\022\016\n\006height\030\010 \001(\r\022\021\n\ttransp"
  "ose\030\t \001(\010\022\021\n\tconnected\030\n \001(\010\0221\n\010rtc_info"
  "\030\013 \001(\0132\037.carbon.frontend.camera.RTCInfo\""
  "-\n\007RTCInfo\022\017\n\007host_id\030\001 \001(\t\022\021\n\tstream_id"
  "\030\002 \001(\t\"j\n\nCameraList\022/\n\007cameras\030\001 \003(\0132\036."
  "carbon.frontend.camera.Camera\022+\n\002ts\030\002 \001("
  "\0132\037.carbon.frontend.util.Timestamp\"c\n\021Ca"
  "meraListRequest\0220\n\004type\030\001 \001(\0162\".carbon.f"
  "rontend.camera.CameraType\022\034\n\024include_dis"
  "connected\030\002 \001(\010\"\224\001\n\025NextCameraListReques"
  "t\0220\n\004type\030\001 \001(\0162\".carbon.frontend.camera"
  ".CameraType\022+\n\002ts\030\002 \001(\0132\037.carbon.fronten"
  "d.util.Timestamp\022\034\n\024include_disconnected"
  "\030\003 \001(\010*;\n\nCameraType\022\007\n\003ANY\020\000\022\013\n\007PREDICT"
  "\020\001\022\n\n\006TARGET\020\002\022\013\n\007KILLCAM\020\0032\327\001\n\rCameraSe"
  "rvice\022^\n\rGetCameraList\022).carbon.frontend"
  ".camera.CameraListRequest\032\".carbon.front"
  "end.camera.CameraList\022f\n\021GetNextCameraLi"
  "st\022-.carbon.frontend.camera.NextCameraLi"
  "stRequest\032\".carbon.frontend.camera.Camer"
  "aListB\020Z\016proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fcamera_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fcamera_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcamera_2eproto = {
  false, false, 1111, descriptor_table_protodef_frontend_2fproto_2fcamera_2eproto, "frontend/proto/camera.proto", 
  &descriptor_table_frontend_2fproto_2fcamera_2eproto_once, descriptor_table_frontend_2fproto_2fcamera_2eproto_deps, 1, 6,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fcamera_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fcamera_2eproto, file_level_enum_descriptors_frontend_2fproto_2fcamera_2eproto, file_level_service_descriptors_frontend_2fproto_2fcamera_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fcamera_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fcamera_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fcamera_2eproto(&descriptor_table_frontend_2fproto_2fcamera_2eproto);
namespace carbon {
namespace frontend {
namespace camera {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CameraType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fcamera_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fcamera_2eproto[0];
}
bool CameraType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class CameraRequest::_Internal {
 public:
};

CameraRequest::CameraRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.camera.CameraRequest)
}
CameraRequest::CameraRequest(const CameraRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.camera.CameraRequest)
}

inline void CameraRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CameraRequest::~CameraRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.camera.CameraRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CameraRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CameraRequest::ArenaDtor(void* object) {
  CameraRequest* _this = reinterpret_cast< CameraRequest* >(object);
  (void)_this;
}
void CameraRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CameraRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CameraRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.camera.CameraRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CameraRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.camera.CameraRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CameraRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.camera.CameraRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.camera.CameraRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.camera.CameraRequest)
  return target;
}

size_t CameraRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.camera.CameraRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CameraRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CameraRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CameraRequest::GetClassData() const { return &_class_data_; }

void CameraRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CameraRequest *>(to)->MergeFrom(
      static_cast<const CameraRequest &>(from));
}


void CameraRequest::MergeFrom(const CameraRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.camera.CameraRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CameraRequest::CopyFrom(const CameraRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.camera.CameraRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CameraRequest::IsInitialized() const {
  return true;
}

void CameraRequest::InternalSwap(CameraRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata CameraRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcamera_2eproto_getter, &descriptor_table_frontend_2fproto_2fcamera_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcamera_2eproto[0]);
}

// ===================================================================

class Camera::_Internal {
 public:
  static const ::carbon::frontend::camera::RTCInfo& rtc_info(const Camera* msg);
};

const ::carbon::frontend::camera::RTCInfo&
Camera::_Internal::rtc_info(const Camera* msg) {
  return *msg->rtc_info_;
}
Camera::Camera(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.camera.Camera)
}
Camera::Camera(const Camera& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  camera_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_camera_id().empty()) {
    camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_camera_id(), 
      GetArenaForAllocation());
  }
  stream_host_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    stream_host_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_stream_host().empty()) {
    stream_host_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_stream_host(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_rtc_info()) {
    rtc_info_ = new ::carbon::frontend::camera::RTCInfo(*from.rtc_info_);
  } else {
    rtc_info_ = nullptr;
  }
  ::memcpy(&row_number_, &from.row_number_,
    static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&row_number_)) + sizeof(height_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.camera.Camera)
}

inline void Camera::SharedCtor() {
camera_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
stream_host_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  stream_host_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&rtc_info_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&rtc_info_)) + sizeof(height_));
}

Camera::~Camera() {
  // @@protoc_insertion_point(destructor:carbon.frontend.camera.Camera)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Camera::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  camera_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  stream_host_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete rtc_info_;
}

void Camera::ArenaDtor(void* object) {
  Camera* _this = reinterpret_cast< Camera* >(object);
  (void)_this;
}
void Camera::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Camera::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Camera::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.camera.Camera)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  camera_id_.ClearToEmpty();
  stream_host_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && rtc_info_ != nullptr) {
    delete rtc_info_;
  }
  rtc_info_ = nullptr;
  ::memset(&row_number_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&height_) -
      reinterpret_cast<char*>(&row_number_)) + sizeof(height_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Camera::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row_number = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_number_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string camera_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_camera_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.camera.Camera.camera_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.camera.CameraType type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::carbon::frontend::camera::CameraType>(val));
        } else
          goto handle_unusual;
        continue;
      // bool auto_focusable = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          auto_focusable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string stream_host = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_stream_host();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.camera.Camera.stream_host"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 stream_port = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          stream_port_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 width = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 height = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool transpose = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          transpose_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool connected = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          connected_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.camera.RTCInfo rtc_info = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_rtc_info(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Camera::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.camera.Camera)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row_number(), target);
  }

  // string camera_id = 2;
  if (!this->_internal_camera_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_camera_id().data(), static_cast<int>(this->_internal_camera_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.camera.Camera.camera_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_camera_id(), target);
  }

  // .carbon.frontend.camera.CameraType type = 3;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_type(), target);
  }

  // bool auto_focusable = 4;
  if (this->_internal_auto_focusable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_auto_focusable(), target);
  }

  // string stream_host = 5;
  if (!this->_internal_stream_host().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_stream_host().data(), static_cast<int>(this->_internal_stream_host().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.camera.Camera.stream_host");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_stream_host(), target);
  }

  // uint32 stream_port = 6;
  if (this->_internal_stream_port() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_stream_port(), target);
  }

  // uint32 width = 7;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(7, this->_internal_width(), target);
  }

  // uint32 height = 8;
  if (this->_internal_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_height(), target);
  }

  // bool transpose = 9;
  if (this->_internal_transpose() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(9, this->_internal_transpose(), target);
  }

  // bool connected = 10;
  if (this->_internal_connected() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(10, this->_internal_connected(), target);
  }

  // .carbon.frontend.camera.RTCInfo rtc_info = 11;
  if (this->_internal_has_rtc_info()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::rtc_info(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.camera.Camera)
  return target;
}

size_t Camera::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.camera.Camera)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string camera_id = 2;
  if (!this->_internal_camera_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_camera_id());
  }

  // string stream_host = 5;
  if (!this->_internal_stream_host().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_stream_host());
  }

  // .carbon.frontend.camera.RTCInfo rtc_info = 11;
  if (this->_internal_has_rtc_info()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rtc_info_);
  }

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_row_number());
  }

  // .carbon.frontend.camera.CameraType type = 3;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  // uint32 stream_port = 6;
  if (this->_internal_stream_port() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_stream_port());
  }

  // uint32 width = 7;
  if (this->_internal_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_width());
  }

  // bool auto_focusable = 4;
  if (this->_internal_auto_focusable() != 0) {
    total_size += 1 + 1;
  }

  // bool transpose = 9;
  if (this->_internal_transpose() != 0) {
    total_size += 1 + 1;
  }

  // bool connected = 10;
  if (this->_internal_connected() != 0) {
    total_size += 1 + 1;
  }

  // uint32 height = 8;
  if (this->_internal_height() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_height());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Camera::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Camera::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Camera::GetClassData() const { return &_class_data_; }

void Camera::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Camera *>(to)->MergeFrom(
      static_cast<const Camera &>(from));
}


void Camera::MergeFrom(const Camera& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.camera.Camera)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_camera_id().empty()) {
    _internal_set_camera_id(from._internal_camera_id());
  }
  if (!from._internal_stream_host().empty()) {
    _internal_set_stream_host(from._internal_stream_host());
  }
  if (from._internal_has_rtc_info()) {
    _internal_mutable_rtc_info()->::carbon::frontend::camera::RTCInfo::MergeFrom(from._internal_rtc_info());
  }
  if (from._internal_row_number() != 0) {
    _internal_set_row_number(from._internal_row_number());
  }
  if (from._internal_type() != 0) {
    _internal_set_type(from._internal_type());
  }
  if (from._internal_stream_port() != 0) {
    _internal_set_stream_port(from._internal_stream_port());
  }
  if (from._internal_width() != 0) {
    _internal_set_width(from._internal_width());
  }
  if (from._internal_auto_focusable() != 0) {
    _internal_set_auto_focusable(from._internal_auto_focusable());
  }
  if (from._internal_transpose() != 0) {
    _internal_set_transpose(from._internal_transpose());
  }
  if (from._internal_connected() != 0) {
    _internal_set_connected(from._internal_connected());
  }
  if (from._internal_height() != 0) {
    _internal_set_height(from._internal_height());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Camera::CopyFrom(const Camera& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.camera.Camera)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Camera::IsInitialized() const {
  return true;
}

void Camera::InternalSwap(Camera* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &camera_id_, lhs_arena,
      &other->camera_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &stream_host_, lhs_arena,
      &other->stream_host_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Camera, height_)
      + sizeof(Camera::height_)
      - PROTOBUF_FIELD_OFFSET(Camera, rtc_info_)>(
          reinterpret_cast<char*>(&rtc_info_),
          reinterpret_cast<char*>(&other->rtc_info_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Camera::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcamera_2eproto_getter, &descriptor_table_frontend_2fproto_2fcamera_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcamera_2eproto[1]);
}

// ===================================================================

class RTCInfo::_Internal {
 public:
};

RTCInfo::RTCInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.camera.RTCInfo)
}
RTCInfo::RTCInfo(const RTCInfo& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  host_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    host_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_host_id().empty()) {
    host_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_host_id(), 
      GetArenaForAllocation());
  }
  stream_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    stream_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_stream_id().empty()) {
    stream_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_stream_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.camera.RTCInfo)
}

inline void RTCInfo::SharedCtor() {
host_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  host_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
stream_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  stream_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RTCInfo::~RTCInfo() {
  // @@protoc_insertion_point(destructor:carbon.frontend.camera.RTCInfo)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RTCInfo::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  host_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  stream_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RTCInfo::ArenaDtor(void* object) {
  RTCInfo* _this = reinterpret_cast< RTCInfo* >(object);
  (void)_this;
}
void RTCInfo::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RTCInfo::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RTCInfo::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.camera.RTCInfo)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  host_id_.ClearToEmpty();
  stream_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RTCInfo::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string host_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_host_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.camera.RTCInfo.host_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string stream_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_stream_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.camera.RTCInfo.stream_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RTCInfo::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.camera.RTCInfo)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string host_id = 1;
  if (!this->_internal_host_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_host_id().data(), static_cast<int>(this->_internal_host_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.camera.RTCInfo.host_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_host_id(), target);
  }

  // string stream_id = 2;
  if (!this->_internal_stream_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_stream_id().data(), static_cast<int>(this->_internal_stream_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.camera.RTCInfo.stream_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_stream_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.camera.RTCInfo)
  return target;
}

size_t RTCInfo::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.camera.RTCInfo)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string host_id = 1;
  if (!this->_internal_host_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_host_id());
  }

  // string stream_id = 2;
  if (!this->_internal_stream_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_stream_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RTCInfo::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RTCInfo::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RTCInfo::GetClassData() const { return &_class_data_; }

void RTCInfo::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RTCInfo *>(to)->MergeFrom(
      static_cast<const RTCInfo &>(from));
}


void RTCInfo::MergeFrom(const RTCInfo& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.camera.RTCInfo)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_host_id().empty()) {
    _internal_set_host_id(from._internal_host_id());
  }
  if (!from._internal_stream_id().empty()) {
    _internal_set_stream_id(from._internal_stream_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RTCInfo::CopyFrom(const RTCInfo& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.camera.RTCInfo)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RTCInfo::IsInitialized() const {
  return true;
}

void RTCInfo::InternalSwap(RTCInfo* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &host_id_, lhs_arena,
      &other->host_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &stream_id_, lhs_arena,
      &other->stream_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata RTCInfo::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcamera_2eproto_getter, &descriptor_table_frontend_2fproto_2fcamera_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcamera_2eproto[2]);
}

// ===================================================================

class CameraList::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const CameraList* msg);
};

const ::carbon::frontend::util::Timestamp&
CameraList::_Internal::ts(const CameraList* msg) {
  return *msg->ts_;
}
void CameraList::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
CameraList::CameraList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  cameras_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.camera.CameraList)
}
CameraList::CameraList(const CameraList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      cameras_(from.cameras_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.camera.CameraList)
}

inline void CameraList::SharedCtor() {
ts_ = nullptr;
}

CameraList::~CameraList() {
  // @@protoc_insertion_point(destructor:carbon.frontend.camera.CameraList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CameraList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void CameraList::ArenaDtor(void* object) {
  CameraList* _this = reinterpret_cast< CameraList* >(object);
  (void)_this;
}
void CameraList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CameraList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CameraList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.camera.CameraList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cameras_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CameraList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.camera.Camera cameras = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_cameras(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CameraList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.camera.CameraList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.camera.Camera cameras = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_cameras_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_cameras(i), target, stream);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.camera.CameraList)
  return target;
}

size_t CameraList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.camera.CameraList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.camera.Camera cameras = 1;
  total_size += 1UL * this->_internal_cameras_size();
  for (const auto& msg : this->cameras_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CameraList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CameraList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CameraList::GetClassData() const { return &_class_data_; }

void CameraList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CameraList *>(to)->MergeFrom(
      static_cast<const CameraList &>(from));
}


void CameraList::MergeFrom(const CameraList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.camera.CameraList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cameras_.MergeFrom(from.cameras_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CameraList::CopyFrom(const CameraList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.camera.CameraList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CameraList::IsInitialized() const {
  return true;
}

void CameraList::InternalSwap(CameraList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  cameras_.InternalSwap(&other->cameras_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CameraList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcamera_2eproto_getter, &descriptor_table_frontend_2fproto_2fcamera_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcamera_2eproto[3]);
}

// ===================================================================

class CameraListRequest::_Internal {
 public:
};

CameraListRequest::CameraListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.camera.CameraListRequest)
}
CameraListRequest::CameraListRequest(const CameraListRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&type_, &from.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&include_disconnected_) -
    reinterpret_cast<char*>(&type_)) + sizeof(include_disconnected_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.camera.CameraListRequest)
}

inline void CameraListRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&type_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&include_disconnected_) -
    reinterpret_cast<char*>(&type_)) + sizeof(include_disconnected_));
}

CameraListRequest::~CameraListRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.camera.CameraListRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CameraListRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CameraListRequest::ArenaDtor(void* object) {
  CameraListRequest* _this = reinterpret_cast< CameraListRequest* >(object);
  (void)_this;
}
void CameraListRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CameraListRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CameraListRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.camera.CameraListRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&include_disconnected_) -
      reinterpret_cast<char*>(&type_)) + sizeof(include_disconnected_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CameraListRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.camera.CameraType type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::carbon::frontend::camera::CameraType>(val));
        } else
          goto handle_unusual;
        continue;
      // bool include_disconnected = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          include_disconnected_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CameraListRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.camera.CameraListRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.camera.CameraType type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // bool include_disconnected = 2;
  if (this->_internal_include_disconnected() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_include_disconnected(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.camera.CameraListRequest)
  return target;
}

size_t CameraListRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.camera.CameraListRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.camera.CameraType type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  // bool include_disconnected = 2;
  if (this->_internal_include_disconnected() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CameraListRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CameraListRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CameraListRequest::GetClassData() const { return &_class_data_; }

void CameraListRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CameraListRequest *>(to)->MergeFrom(
      static_cast<const CameraListRequest &>(from));
}


void CameraListRequest::MergeFrom(const CameraListRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.camera.CameraListRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_type() != 0) {
    _internal_set_type(from._internal_type());
  }
  if (from._internal_include_disconnected() != 0) {
    _internal_set_include_disconnected(from._internal_include_disconnected());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CameraListRequest::CopyFrom(const CameraListRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.camera.CameraListRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CameraListRequest::IsInitialized() const {
  return true;
}

void CameraListRequest::InternalSwap(CameraListRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CameraListRequest, include_disconnected_)
      + sizeof(CameraListRequest::include_disconnected_)
      - PROTOBUF_FIELD_OFFSET(CameraListRequest, type_)>(
          reinterpret_cast<char*>(&type_),
          reinterpret_cast<char*>(&other->type_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CameraListRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcamera_2eproto_getter, &descriptor_table_frontend_2fproto_2fcamera_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcamera_2eproto[4]);
}

// ===================================================================

class NextCameraListRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const NextCameraListRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
NextCameraListRequest::_Internal::ts(const NextCameraListRequest* msg) {
  return *msg->ts_;
}
void NextCameraListRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
NextCameraListRequest::NextCameraListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.camera.NextCameraListRequest)
}
NextCameraListRequest::NextCameraListRequest(const NextCameraListRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&type_, &from.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&include_disconnected_) -
    reinterpret_cast<char*>(&type_)) + sizeof(include_disconnected_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.camera.NextCameraListRequest)
}

inline void NextCameraListRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&include_disconnected_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(include_disconnected_));
}

NextCameraListRequest::~NextCameraListRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.camera.NextCameraListRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void NextCameraListRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void NextCameraListRequest::ArenaDtor(void* object) {
  NextCameraListRequest* _this = reinterpret_cast< NextCameraListRequest* >(object);
  (void)_this;
}
void NextCameraListRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void NextCameraListRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void NextCameraListRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.camera.NextCameraListRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&include_disconnected_) -
      reinterpret_cast<char*>(&type_)) + sizeof(include_disconnected_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* NextCameraListRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.camera.CameraType type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::carbon::frontend::camera::CameraType>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool include_disconnected = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          include_disconnected_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* NextCameraListRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.camera.NextCameraListRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.camera.CameraType type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  // bool include_disconnected = 3;
  if (this->_internal_include_disconnected() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_include_disconnected(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.camera.NextCameraListRequest)
  return target;
}

size_t NextCameraListRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.camera.NextCameraListRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.camera.CameraType type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  // bool include_disconnected = 3;
  if (this->_internal_include_disconnected() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData NextCameraListRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    NextCameraListRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*NextCameraListRequest::GetClassData() const { return &_class_data_; }

void NextCameraListRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<NextCameraListRequest *>(to)->MergeFrom(
      static_cast<const NextCameraListRequest &>(from));
}


void NextCameraListRequest::MergeFrom(const NextCameraListRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.camera.NextCameraListRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_type() != 0) {
    _internal_set_type(from._internal_type());
  }
  if (from._internal_include_disconnected() != 0) {
    _internal_set_include_disconnected(from._internal_include_disconnected());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void NextCameraListRequest::CopyFrom(const NextCameraListRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.camera.NextCameraListRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool NextCameraListRequest::IsInitialized() const {
  return true;
}

void NextCameraListRequest::InternalSwap(NextCameraListRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(NextCameraListRequest, include_disconnected_)
      + sizeof(NextCameraListRequest::include_disconnected_)
      - PROTOBUF_FIELD_OFFSET(NextCameraListRequest, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata NextCameraListRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcamera_2eproto_getter, &descriptor_table_frontend_2fproto_2fcamera_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcamera_2eproto[5]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace camera
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::camera::CameraRequest* Arena::CreateMaybeMessage< ::carbon::frontend::camera::CameraRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::camera::CameraRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::camera::Camera* Arena::CreateMaybeMessage< ::carbon::frontend::camera::Camera >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::camera::Camera >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::camera::RTCInfo* Arena::CreateMaybeMessage< ::carbon::frontend::camera::RTCInfo >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::camera::RTCInfo >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::camera::CameraList* Arena::CreateMaybeMessage< ::carbon::frontend::camera::CameraList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::camera::CameraList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::camera::CameraListRequest* Arena::CreateMaybeMessage< ::carbon::frontend::camera::CameraListRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::camera::CameraListRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::camera::NextCameraListRequest* Arena::CreateMaybeMessage< ::carbon::frontend::camera::NextCameraListRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::camera::NextCameraListRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import dashboard_pb2 as frontend_dot_proto_dot_dashboard__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class DashboardServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ToggleRow = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/ToggleRow',
                request_serializer=frontend_dot_proto_dot_dashboard__pb2.RowId.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.ToggleLasers = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/ToggleLasers',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextDashboardState = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/GetNextDashboardState',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_dashboard__pb2.DashboardStateMessage.FromString,
                )
        self.GetCropModelOptions = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/GetCropModelOptions',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_dashboard__pb2.CropModelOptions.FromString,
                )
        self.SetCropModel = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/SetCropModel',
                request_serializer=frontend_dot_proto_dot_dashboard__pb2.CropModel.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextWeedingVelocity = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/GetNextWeedingVelocity',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_dashboard__pb2.WeedingVelocity.FromString,
                )
        self.SetTargetingState = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/SetTargetingState',
                request_serializer=frontend_dot_proto_dot_dashboard__pb2.TargetingState.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.SetRowSpacing = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/SetRowSpacing',
                request_serializer=frontend_dot_proto_dot_dashboard__pb2.RowSpacing.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.SetCruiseEnabled = channel.unary_unary(
                '/carbon.frontend.dashboard.DashboardService/SetCruiseEnabled',
                request_serializer=frontend_dot_proto_dot_dashboard__pb2.CruiseEnable.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )


class DashboardServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def ToggleRow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ToggleLasers(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextDashboardState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCropModelOptions(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCropModel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextWeedingVelocity(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetTargetingState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetRowSpacing(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCruiseEnabled(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DashboardServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ToggleRow': grpc.unary_unary_rpc_method_handler(
                    servicer.ToggleRow,
                    request_deserializer=frontend_dot_proto_dot_dashboard__pb2.RowId.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'ToggleLasers': grpc.unary_unary_rpc_method_handler(
                    servicer.ToggleLasers,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextDashboardState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextDashboardState,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_dashboard__pb2.DashboardStateMessage.SerializeToString,
            ),
            'GetCropModelOptions': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCropModelOptions,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_dashboard__pb2.CropModelOptions.SerializeToString,
            ),
            'SetCropModel': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCropModel,
                    request_deserializer=frontend_dot_proto_dot_dashboard__pb2.CropModel.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextWeedingVelocity': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextWeedingVelocity,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_dashboard__pb2.WeedingVelocity.SerializeToString,
            ),
            'SetTargetingState': grpc.unary_unary_rpc_method_handler(
                    servicer.SetTargetingState,
                    request_deserializer=frontend_dot_proto_dot_dashboard__pb2.TargetingState.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetRowSpacing': grpc.unary_unary_rpc_method_handler(
                    servicer.SetRowSpacing,
                    request_deserializer=frontend_dot_proto_dot_dashboard__pb2.RowSpacing.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetCruiseEnabled': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCruiseEnabled,
                    request_deserializer=frontend_dot_proto_dot_dashboard__pb2.CruiseEnable.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.dashboard.DashboardService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DashboardService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def ToggleRow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/ToggleRow',
            frontend_dot_proto_dot_dashboard__pb2.RowId.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ToggleLasers(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/ToggleLasers',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextDashboardState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/GetNextDashboardState',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_dashboard__pb2.DashboardStateMessage.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCropModelOptions(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/GetCropModelOptions',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_dashboard__pb2.CropModelOptions.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetCropModel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/SetCropModel',
            frontend_dot_proto_dot_dashboard__pb2.CropModel.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextWeedingVelocity(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/GetNextWeedingVelocity',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_dashboard__pb2.WeedingVelocity.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetTargetingState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/SetTargetingState',
            frontend_dot_proto_dot_dashboard__pb2.TargetingState.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetRowSpacing(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/SetRowSpacing',
            frontend_dot_proto_dot_dashboard__pb2.RowSpacing.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetCruiseEnabled(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.dashboard.DashboardService/SetCruiseEnabled',
            frontend_dot_proto_dot_dashboard__pb2.CruiseEnable.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

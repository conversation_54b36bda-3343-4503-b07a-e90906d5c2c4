# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import reporting_pb2 as frontend_dot_proto_dot_reporting__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class ReportingServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextLocationHistory = channel.unary_unary(
                '/carbon.frontend.features.ReportingService/GetNextLocationHistory',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_reporting__pb2.LocationHistory.FromString,
                )


class ReportingServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextLocationHistory(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ReportingServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextLocationHistory': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextLocationHistory,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_reporting__pb2.LocationHistory.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.features.ReportingService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ReportingService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextLocationHistory(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.features.ReportingService/GetNextLocationHistory',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_reporting__pb2.LocationHistory.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

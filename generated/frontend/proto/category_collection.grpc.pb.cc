// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/category_collection.proto

#include "frontend/proto/category_collection.pb.h"
#include "frontend/proto/category_collection.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace category_collection {

static const char* CategoryCollectionService_method_names[] = {
  "/carbon.frontend.category_collection.CategoryCollectionService/GetNextCategoryCollectionsData",
  "/carbon.frontend.category_collection.CategoryCollectionService/GetNextActiveCategoryCollectionId",
  "/carbon.frontend.category_collection.CategoryCollectionService/SetActiveCategoryCollectionId",
  "/carbon.frontend.category_collection.CategoryCollectionService/ReloadCategoryCollection",
};

std::unique_ptr< CategoryCollectionService::Stub> CategoryCollectionService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< CategoryCollectionService::Stub> stub(new CategoryCollectionService::Stub(channel, options));
  return stub;
}

CategoryCollectionService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextCategoryCollectionsData_(CategoryCollectionService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextActiveCategoryCollectionId_(CategoryCollectionService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetActiveCategoryCollectionId_(CategoryCollectionService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ReloadCategoryCollection_(CategoryCollectionService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status CategoryCollectionService::Stub::GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextCategoryCollectionsData_, context, request, response);
}

void CategoryCollectionService::Stub::async::GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCategoryCollectionsData_, context, request, response, std::move(f));
}

void CategoryCollectionService::Stub::async::GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCategoryCollectionsData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* CategoryCollectionService::Stub::PrepareAsyncGetNextCategoryCollectionsDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextCategoryCollectionsData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* CategoryCollectionService::Stub::AsyncGetNextCategoryCollectionsDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextCategoryCollectionsDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CategoryCollectionService::Stub::GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextActiveCategoryCollectionId_, context, request, response);
}

void CategoryCollectionService::Stub::async::GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveCategoryCollectionId_, context, request, response, std::move(f));
}

void CategoryCollectionService::Stub::async::GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveCategoryCollectionId_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* CategoryCollectionService::Stub::PrepareAsyncGetNextActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextActiveCategoryCollectionId_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* CategoryCollectionService::Stub::AsyncGetNextActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextActiveCategoryCollectionIdRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CategoryCollectionService::Stub::SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetActiveCategoryCollectionId_, context, request, response);
}

void CategoryCollectionService::Stub::async::SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveCategoryCollectionId_, context, request, response, std::move(f));
}

void CategoryCollectionService::Stub::async::SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveCategoryCollectionId_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CategoryCollectionService::Stub::PrepareAsyncSetActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetActiveCategoryCollectionId_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CategoryCollectionService::Stub::AsyncSetActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetActiveCategoryCollectionIdRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CategoryCollectionService::Stub::ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReloadCategoryCollection_, context, request, response);
}

void CategoryCollectionService::Stub::async::ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadCategoryCollection_, context, request, response, std::move(f));
}

void CategoryCollectionService::Stub::async::ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReloadCategoryCollection_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CategoryCollectionService::Stub::PrepareAsyncReloadCategoryCollectionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReloadCategoryCollection_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CategoryCollectionService::Stub::AsyncReloadCategoryCollectionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReloadCategoryCollectionRaw(context, request, cq);
  result->StartCall();
  return result;
}

CategoryCollectionService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CategoryCollectionService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CategoryCollectionService::Service, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CategoryCollectionService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* req,
             ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* resp) {
               return service->GetNextCategoryCollectionsData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CategoryCollectionService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CategoryCollectionService::Service, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CategoryCollectionService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* req,
             ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* resp) {
               return service->GetNextActiveCategoryCollectionId(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CategoryCollectionService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CategoryCollectionService::Service, ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CategoryCollectionService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetActiveCategoryCollectionId(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CategoryCollectionService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CategoryCollectionService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CategoryCollectionService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ReloadCategoryCollection(ctx, req, resp);
             }, this)));
}

CategoryCollectionService::Service::~Service() {
}

::grpc::Status CategoryCollectionService::Service::GetNextCategoryCollectionsData(::grpc::ServerContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CategoryCollectionService::Service::GetNextActiveCategoryCollectionId(::grpc::ServerContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CategoryCollectionService::Service::SetActiveCategoryCollectionId(::grpc::ServerContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CategoryCollectionService::Service::ReloadCategoryCollection(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace category_collection


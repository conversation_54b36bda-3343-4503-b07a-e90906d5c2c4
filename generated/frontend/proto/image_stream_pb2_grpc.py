# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import image_stream_pb2 as frontend_dot_proto_dot_image__stream__pb2


class ImageStreamServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextCameraImage = channel.unary_unary(
                '/carbon.frontend.image_stream.ImageStreamService/GetNextCameraImage',
                request_serializer=frontend_dot_proto_dot_image__stream__pb2.CameraImageRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_image__stream__pb2.Image.FromString,
                )
        self.GetPredictImageByTimestamp = channel.unary_unary(
                '/carbon.frontend.image_stream.ImageStreamService/GetPredictImageByTimestamp',
                request_serializer=frontend_dot_proto_dot_image__stream__pb2.GetPredictImageByTimestampRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_image__stream__pb2.GetPredictImageByTimestampResponse.FromString,
                )
        self.GetMultiPredictPerspectives = channel.unary_unary(
                '/carbon.frontend.image_stream.ImageStreamService/GetMultiPredictPerspectives',
                request_serializer=frontend_dot_proto_dot_image__stream__pb2.GetMultiPredictPerspectivesRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_image__stream__pb2.GetMultiPredictPerspectivesResponse.FromString,
                )


class ImageStreamServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextCameraImage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPredictImageByTimestamp(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetMultiPredictPerspectives(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ImageStreamServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextCameraImage': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextCameraImage,
                    request_deserializer=frontend_dot_proto_dot_image__stream__pb2.CameraImageRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_image__stream__pb2.Image.SerializeToString,
            ),
            'GetPredictImageByTimestamp': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPredictImageByTimestamp,
                    request_deserializer=frontend_dot_proto_dot_image__stream__pb2.GetPredictImageByTimestampRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_image__stream__pb2.GetPredictImageByTimestampResponse.SerializeToString,
            ),
            'GetMultiPredictPerspectives': grpc.unary_unary_rpc_method_handler(
                    servicer.GetMultiPredictPerspectives,
                    request_deserializer=frontend_dot_proto_dot_image__stream__pb2.GetMultiPredictPerspectivesRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_image__stream__pb2.GetMultiPredictPerspectivesResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.image_stream.ImageStreamService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ImageStreamService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextCameraImage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.image_stream.ImageStreamService/GetNextCameraImage',
            frontend_dot_proto_dot_image__stream__pb2.CameraImageRequest.SerializeToString,
            frontend_dot_proto_dot_image__stream__pb2.Image.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPredictImageByTimestamp(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.image_stream.ImageStreamService/GetPredictImageByTimestamp',
            frontend_dot_proto_dot_image__stream__pb2.GetPredictImageByTimestampRequest.SerializeToString,
            frontend_dot_proto_dot_image__stream__pb2.GetPredictImageByTimestampResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetMultiPredictPerspectives(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.image_stream.ImageStreamService/GetMultiPredictPerspectives',
            frontend_dot_proto_dot_image__stream__pb2.GetMultiPredictPerspectivesRequest.SerializeToString,
            frontend_dot_proto_dot_image__stream__pb2.GetMultiPredictPerspectivesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/alarm.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2falarm_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2falarm_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/translation.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2falarm_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2falarm_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[11]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2falarm_2eproto;
namespace carbon {
namespace frontend {
namespace alarm {
class AcknowledgeRequest;
struct AcknowledgeRequestDefaultTypeInternal;
extern AcknowledgeRequestDefaultTypeInternal _AcknowledgeRequest_default_instance_;
class AlarmCount;
struct AlarmCountDefaultTypeInternal;
extern AlarmCountDefaultTypeInternal _AlarmCount_default_instance_;
class AlarmRow;
struct AlarmRowDefaultTypeInternal;
extern AlarmRowDefaultTypeInternal _AlarmRow_default_instance_;
class AlarmTable;
struct AlarmTableDefaultTypeInternal;
extern AlarmTableDefaultTypeInternal _AlarmTable_default_instance_;
class AttemptAutofixAlarmRequest;
struct AttemptAutofixAlarmRequestDefaultTypeInternal;
extern AttemptAutofixAlarmRequestDefaultTypeInternal _AttemptAutofixAlarmRequest_default_instance_;
class GetNextAlarmLogCountRequest;
struct GetNextAlarmLogCountRequestDefaultTypeInternal;
extern GetNextAlarmLogCountRequestDefaultTypeInternal _GetNextAlarmLogCountRequest_default_instance_;
class GetNextAlarmLogCountResponse;
struct GetNextAlarmLogCountResponseDefaultTypeInternal;
extern GetNextAlarmLogCountResponseDefaultTypeInternal _GetNextAlarmLogCountResponse_default_instance_;
class GetNextAlarmLogRequest;
struct GetNextAlarmLogRequestDefaultTypeInternal;
extern GetNextAlarmLogRequestDefaultTypeInternal _GetNextAlarmLogRequest_default_instance_;
class GetNextAlarmLogResponse;
struct GetNextAlarmLogResponseDefaultTypeInternal;
extern GetNextAlarmLogResponseDefaultTypeInternal _GetNextAlarmLogResponse_default_instance_;
class GetNextAutofixAlarmStatusRequest;
struct GetNextAutofixAlarmStatusRequestDefaultTypeInternal;
extern GetNextAutofixAlarmStatusRequestDefaultTypeInternal _GetNextAutofixAlarmStatusRequest_default_instance_;
class GetNextAutofixAlarmStatusResponse;
struct GetNextAutofixAlarmStatusResponseDefaultTypeInternal;
extern GetNextAutofixAlarmStatusResponseDefaultTypeInternal _GetNextAutofixAlarmStatusResponse_default_instance_;
}  // namespace alarm
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::alarm::AcknowledgeRequest* Arena::CreateMaybeMessage<::carbon::frontend::alarm::AcknowledgeRequest>(Arena*);
template<> ::carbon::frontend::alarm::AlarmCount* Arena::CreateMaybeMessage<::carbon::frontend::alarm::AlarmCount>(Arena*);
template<> ::carbon::frontend::alarm::AlarmRow* Arena::CreateMaybeMessage<::carbon::frontend::alarm::AlarmRow>(Arena*);
template<> ::carbon::frontend::alarm::AlarmTable* Arena::CreateMaybeMessage<::carbon::frontend::alarm::AlarmTable>(Arena*);
template<> ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* Arena::CreateMaybeMessage<::carbon::frontend::alarm::AttemptAutofixAlarmRequest>(Arena*);
template<> ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* Arena::CreateMaybeMessage<::carbon::frontend::alarm::GetNextAlarmLogCountRequest>(Arena*);
template<> ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* Arena::CreateMaybeMessage<::carbon::frontend::alarm::GetNextAlarmLogCountResponse>(Arena*);
template<> ::carbon::frontend::alarm::GetNextAlarmLogRequest* Arena::CreateMaybeMessage<::carbon::frontend::alarm::GetNextAlarmLogRequest>(Arena*);
template<> ::carbon::frontend::alarm::GetNextAlarmLogResponse* Arena::CreateMaybeMessage<::carbon::frontend::alarm::GetNextAlarmLogResponse>(Arena*);
template<> ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* Arena::CreateMaybeMessage<::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest>(Arena*);
template<> ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* Arena::CreateMaybeMessage<::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace alarm {

enum AlarmLevel : int {
  AL_UNKNOWN = 0,
  AL_CRITICAL = 1,
  AL_HIGH = 2,
  AL_MEDIUM = 3,
  AL_LOW = 4,
  AL_HIDDEN = 5,
  AlarmLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  AlarmLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool AlarmLevel_IsValid(int value);
constexpr AlarmLevel AlarmLevel_MIN = AL_UNKNOWN;
constexpr AlarmLevel AlarmLevel_MAX = AL_HIDDEN;
constexpr int AlarmLevel_ARRAYSIZE = AlarmLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmLevel_descriptor();
template<typename T>
inline const std::string& AlarmLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AlarmLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AlarmLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AlarmLevel_descriptor(), enum_t_value);
}
inline bool AlarmLevel_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AlarmLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AlarmLevel>(
    AlarmLevel_descriptor(), name, value);
}
enum AlarmImpact : int {
  AI_UNKNOWN = 0,
  AI_CRITICAL = 1,
  AI_OFFLINE = 2,
  AI_DEGRADED = 3,
  AI_NONE = 4,
  AlarmImpact_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  AlarmImpact_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool AlarmImpact_IsValid(int value);
constexpr AlarmImpact AlarmImpact_MIN = AI_UNKNOWN;
constexpr AlarmImpact AlarmImpact_MAX = AI_NONE;
constexpr int AlarmImpact_ARRAYSIZE = AlarmImpact_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* AlarmImpact_descriptor();
template<typename T>
inline const std::string& AlarmImpact_Name(T enum_t_value) {
  static_assert(::std::is_same<T, AlarmImpact>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function AlarmImpact_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    AlarmImpact_descriptor(), enum_t_value);
}
inline bool AlarmImpact_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, AlarmImpact* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<AlarmImpact>(
    AlarmImpact_descriptor(), name, value);
}
// ===================================================================

class AlarmRow final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.AlarmRow) */ {
 public:
  inline AlarmRow() : AlarmRow(nullptr) {}
  ~AlarmRow() override;
  explicit constexpr AlarmRow(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlarmRow(const AlarmRow& from);
  AlarmRow(AlarmRow&& from) noexcept
    : AlarmRow() {
    *this = ::std::move(from);
  }

  inline AlarmRow& operator=(const AlarmRow& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlarmRow& operator=(AlarmRow&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AlarmRow& default_instance() {
    return *internal_default_instance();
  }
  static inline const AlarmRow* internal_default_instance() {
    return reinterpret_cast<const AlarmRow*>(
               &_AlarmRow_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(AlarmRow& a, AlarmRow& b) {
    a.Swap(&b);
  }
  inline void Swap(AlarmRow* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlarmRow* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlarmRow* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlarmRow>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AlarmRow& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AlarmRow& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlarmRow* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.AlarmRow";
  }
  protected:
  explicit AlarmRow(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTranslationParametersFieldNumber = 14,
    kAlarmCodeFieldNumber = 2,
    kSubsystemFieldNumber = 3,
    kDescriptionFieldNumber = 4,
    kIdentifierFieldNumber = 6,
    kDescriptionKeyFieldNumber = 13,
    kTimestampMsFieldNumber = 1,
    kLevelFieldNumber = 5,
    kImpactFieldNumber = 8,
    kStopTimestampMsFieldNumber = 9,
    kAcknowledgedFieldNumber = 7,
    kAutofixAvailableFieldNumber = 10,
    kAutofixAttemptedFieldNumber = 11,
    kAutofixDurationSecFieldNumber = 12,
  };
  // repeated .carbon.frontend.translation.TranslationParameter translation_parameters = 14;
  int translation_parameters_size() const;
  private:
  int _internal_translation_parameters_size() const;
  public:
  void clear_translation_parameters();
  ::carbon::frontend::translation::TranslationParameter* mutable_translation_parameters(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::translation::TranslationParameter >*
      mutable_translation_parameters();
  private:
  const ::carbon::frontend::translation::TranslationParameter& _internal_translation_parameters(int index) const;
  ::carbon::frontend::translation::TranslationParameter* _internal_add_translation_parameters();
  public:
  const ::carbon::frontend::translation::TranslationParameter& translation_parameters(int index) const;
  ::carbon::frontend::translation::TranslationParameter* add_translation_parameters();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::translation::TranslationParameter >&
      translation_parameters() const;

  // string alarm_code = 2;
  void clear_alarm_code();
  const std::string& alarm_code() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_alarm_code(ArgT0&& arg0, ArgT... args);
  std::string* mutable_alarm_code();
  PROTOBUF_NODISCARD std::string* release_alarm_code();
  void set_allocated_alarm_code(std::string* alarm_code);
  private:
  const std::string& _internal_alarm_code() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_alarm_code(const std::string& value);
  std::string* _internal_mutable_alarm_code();
  public:

  // string subsystem = 3;
  void clear_subsystem();
  const std::string& subsystem() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_subsystem(ArgT0&& arg0, ArgT... args);
  std::string* mutable_subsystem();
  PROTOBUF_NODISCARD std::string* release_subsystem();
  void set_allocated_subsystem(std::string* subsystem);
  private:
  const std::string& _internal_subsystem() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_subsystem(const std::string& value);
  std::string* _internal_mutable_subsystem();
  public:

  // string description = 4;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // string identifier = 6;
  void clear_identifier();
  const std::string& identifier() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_identifier(ArgT0&& arg0, ArgT... args);
  std::string* mutable_identifier();
  PROTOBUF_NODISCARD std::string* release_identifier();
  void set_allocated_identifier(std::string* identifier);
  private:
  const std::string& _internal_identifier() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_identifier(const std::string& value);
  std::string* _internal_mutable_identifier();
  public:

  // string description_key = 13;
  void clear_description_key();
  const std::string& description_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description_key();
  PROTOBUF_NODISCARD std::string* release_description_key();
  void set_allocated_description_key(std::string* description_key);
  private:
  const std::string& _internal_description_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description_key(const std::string& value);
  std::string* _internal_mutable_description_key();
  public:

  // int64 timestamp_ms = 1;
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // .carbon.frontend.alarm.AlarmLevel level = 5;
  void clear_level();
  ::carbon::frontend::alarm::AlarmLevel level() const;
  void set_level(::carbon::frontend::alarm::AlarmLevel value);
  private:
  ::carbon::frontend::alarm::AlarmLevel _internal_level() const;
  void _internal_set_level(::carbon::frontend::alarm::AlarmLevel value);
  public:

  // .carbon.frontend.alarm.AlarmImpact impact = 8;
  void clear_impact();
  ::carbon::frontend::alarm::AlarmImpact impact() const;
  void set_impact(::carbon::frontend::alarm::AlarmImpact value);
  private:
  ::carbon::frontend::alarm::AlarmImpact _internal_impact() const;
  void _internal_set_impact(::carbon::frontend::alarm::AlarmImpact value);
  public:

  // int64 stop_timestamp_ms = 9;
  void clear_stop_timestamp_ms();
  int64_t stop_timestamp_ms() const;
  void set_stop_timestamp_ms(int64_t value);
  private:
  int64_t _internal_stop_timestamp_ms() const;
  void _internal_set_stop_timestamp_ms(int64_t value);
  public:

  // bool acknowledged = 7;
  void clear_acknowledged();
  bool acknowledged() const;
  void set_acknowledged(bool value);
  private:
  bool _internal_acknowledged() const;
  void _internal_set_acknowledged(bool value);
  public:

  // bool autofix_available = 10;
  void clear_autofix_available();
  bool autofix_available() const;
  void set_autofix_available(bool value);
  private:
  bool _internal_autofix_available() const;
  void _internal_set_autofix_available(bool value);
  public:

  // bool autofix_attempted = 11;
  void clear_autofix_attempted();
  bool autofix_attempted() const;
  void set_autofix_attempted(bool value);
  private:
  bool _internal_autofix_attempted() const;
  void _internal_set_autofix_attempted(bool value);
  public:

  // uint32 autofix_duration_sec = 12;
  void clear_autofix_duration_sec();
  uint32_t autofix_duration_sec() const;
  void set_autofix_duration_sec(uint32_t value);
  private:
  uint32_t _internal_autofix_duration_sec() const;
  void _internal_set_autofix_duration_sec(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AlarmRow)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::translation::TranslationParameter > translation_parameters_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr alarm_code_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr subsystem_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr identifier_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_key_;
  int64_t timestamp_ms_;
  int level_;
  int impact_;
  int64_t stop_timestamp_ms_;
  bool acknowledged_;
  bool autofix_available_;
  bool autofix_attempted_;
  uint32_t autofix_duration_sec_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class AlarmTable final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.AlarmTable) */ {
 public:
  inline AlarmTable() : AlarmTable(nullptr) {}
  ~AlarmTable() override;
  explicit constexpr AlarmTable(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlarmTable(const AlarmTable& from);
  AlarmTable(AlarmTable&& from) noexcept
    : AlarmTable() {
    *this = ::std::move(from);
  }

  inline AlarmTable& operator=(const AlarmTable& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlarmTable& operator=(AlarmTable&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AlarmTable& default_instance() {
    return *internal_default_instance();
  }
  static inline const AlarmTable* internal_default_instance() {
    return reinterpret_cast<const AlarmTable*>(
               &_AlarmTable_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(AlarmTable& a, AlarmTable& b) {
    a.Swap(&b);
  }
  inline void Swap(AlarmTable* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlarmTable* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlarmTable* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlarmTable>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AlarmTable& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AlarmTable& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlarmTable* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.AlarmTable";
  }
  protected:
  explicit AlarmTable(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlarmsFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
  int alarms_size() const;
  private:
  int _internal_alarms_size() const;
  public:
  void clear_alarms();
  ::carbon::frontend::alarm::AlarmRow* mutable_alarms(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
      mutable_alarms();
  private:
  const ::carbon::frontend::alarm::AlarmRow& _internal_alarms(int index) const;
  ::carbon::frontend::alarm::AlarmRow* _internal_add_alarms();
  public:
  const ::carbon::frontend::alarm::AlarmRow& alarms(int index) const;
  ::carbon::frontend::alarm::AlarmRow* add_alarms();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
      alarms() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AlarmTable)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow > alarms_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class AlarmCount final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.AlarmCount) */ {
 public:
  inline AlarmCount() : AlarmCount(nullptr) {}
  ~AlarmCount() override;
  explicit constexpr AlarmCount(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AlarmCount(const AlarmCount& from);
  AlarmCount(AlarmCount&& from) noexcept
    : AlarmCount() {
    *this = ::std::move(from);
  }

  inline AlarmCount& operator=(const AlarmCount& from) {
    CopyFrom(from);
    return *this;
  }
  inline AlarmCount& operator=(AlarmCount&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AlarmCount& default_instance() {
    return *internal_default_instance();
  }
  static inline const AlarmCount* internal_default_instance() {
    return reinterpret_cast<const AlarmCount*>(
               &_AlarmCount_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(AlarmCount& a, AlarmCount& b) {
    a.Swap(&b);
  }
  inline void Swap(AlarmCount* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AlarmCount* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AlarmCount* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AlarmCount>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AlarmCount& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AlarmCount& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AlarmCount* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.AlarmCount";
  }
  protected:
  explicit AlarmCount(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kCountFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // uint32 count = 2;
  void clear_count();
  uint32_t count() const;
  void set_count(uint32_t value);
  private:
  uint32_t _internal_count() const;
  void _internal_set_count(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AlarmCount)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  uint32_t count_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class AcknowledgeRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.AcknowledgeRequest) */ {
 public:
  inline AcknowledgeRequest() : AcknowledgeRequest(nullptr) {}
  ~AcknowledgeRequest() override;
  explicit constexpr AcknowledgeRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AcknowledgeRequest(const AcknowledgeRequest& from);
  AcknowledgeRequest(AcknowledgeRequest&& from) noexcept
    : AcknowledgeRequest() {
    *this = ::std::move(from);
  }

  inline AcknowledgeRequest& operator=(const AcknowledgeRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline AcknowledgeRequest& operator=(AcknowledgeRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AcknowledgeRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const AcknowledgeRequest* internal_default_instance() {
    return reinterpret_cast<const AcknowledgeRequest*>(
               &_AcknowledgeRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(AcknowledgeRequest& a, AcknowledgeRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(AcknowledgeRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AcknowledgeRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AcknowledgeRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AcknowledgeRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AcknowledgeRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AcknowledgeRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AcknowledgeRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.AcknowledgeRequest";
  }
  protected:
  explicit AcknowledgeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdentifierFieldNumber = 1,
  };
  // string identifier = 1;
  void clear_identifier();
  const std::string& identifier() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_identifier(ArgT0&& arg0, ArgT... args);
  std::string* mutable_identifier();
  PROTOBUF_NODISCARD std::string* release_identifier();
  void set_allocated_identifier(std::string* identifier);
  private:
  const std::string& _internal_identifier() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_identifier(const std::string& value);
  std::string* _internal_mutable_identifier();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AcknowledgeRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr identifier_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class GetNextAlarmLogRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.GetNextAlarmLogRequest) */ {
 public:
  inline GetNextAlarmLogRequest() : GetNextAlarmLogRequest(nullptr) {}
  ~GetNextAlarmLogRequest() override;
  explicit constexpr GetNextAlarmLogRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextAlarmLogRequest(const GetNextAlarmLogRequest& from);
  GetNextAlarmLogRequest(GetNextAlarmLogRequest&& from) noexcept
    : GetNextAlarmLogRequest() {
    *this = ::std::move(from);
  }

  inline GetNextAlarmLogRequest& operator=(const GetNextAlarmLogRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextAlarmLogRequest& operator=(GetNextAlarmLogRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextAlarmLogRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextAlarmLogRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextAlarmLogRequest*>(
               &_GetNextAlarmLogRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetNextAlarmLogRequest& a, GetNextAlarmLogRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextAlarmLogRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextAlarmLogRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextAlarmLogRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextAlarmLogRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextAlarmLogRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextAlarmLogRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextAlarmLogRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.GetNextAlarmLogRequest";
  }
  protected:
  explicit GetNextAlarmLogRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 3,
    kFromIdxFieldNumber = 1,
    kToIdxFieldNumber = 2,
    kVisibleOnlyFieldNumber = 4,
  };
  // .carbon.frontend.util.Timestamp ts = 3;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // int32 from_idx = 1;
  void clear_from_idx();
  int32_t from_idx() const;
  void set_from_idx(int32_t value);
  private:
  int32_t _internal_from_idx() const;
  void _internal_set_from_idx(int32_t value);
  public:

  // int32 to_idx = 2;
  void clear_to_idx();
  int32_t to_idx() const;
  void set_to_idx(int32_t value);
  private:
  int32_t _internal_to_idx() const;
  void _internal_set_to_idx(int32_t value);
  public:

  // bool visible_only = 4;
  void clear_visible_only();
  bool visible_only() const;
  void set_visible_only(bool value);
  private:
  bool _internal_visible_only() const;
  void _internal_set_visible_only(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAlarmLogRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  int32_t from_idx_;
  int32_t to_idx_;
  bool visible_only_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class GetNextAlarmLogResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.GetNextAlarmLogResponse) */ {
 public:
  inline GetNextAlarmLogResponse() : GetNextAlarmLogResponse(nullptr) {}
  ~GetNextAlarmLogResponse() override;
  explicit constexpr GetNextAlarmLogResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextAlarmLogResponse(const GetNextAlarmLogResponse& from);
  GetNextAlarmLogResponse(GetNextAlarmLogResponse&& from) noexcept
    : GetNextAlarmLogResponse() {
    *this = ::std::move(from);
  }

  inline GetNextAlarmLogResponse& operator=(const GetNextAlarmLogResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextAlarmLogResponse& operator=(GetNextAlarmLogResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextAlarmLogResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextAlarmLogResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextAlarmLogResponse*>(
               &_GetNextAlarmLogResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetNextAlarmLogResponse& a, GetNextAlarmLogResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextAlarmLogResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextAlarmLogResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextAlarmLogResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextAlarmLogResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextAlarmLogResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextAlarmLogResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextAlarmLogResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.GetNextAlarmLogResponse";
  }
  protected:
  explicit GetNextAlarmLogResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlarmsFieldNumber = 1,
    kTsFieldNumber = 2,
  };
  // repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
  int alarms_size() const;
  private:
  int _internal_alarms_size() const;
  public:
  void clear_alarms();
  ::carbon::frontend::alarm::AlarmRow* mutable_alarms(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
      mutable_alarms();
  private:
  const ::carbon::frontend::alarm::AlarmRow& _internal_alarms(int index) const;
  ::carbon::frontend::alarm::AlarmRow* _internal_add_alarms();
  public:
  const ::carbon::frontend::alarm::AlarmRow& alarms(int index) const;
  ::carbon::frontend::alarm::AlarmRow* add_alarms();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
      alarms() const;

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAlarmLogResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow > alarms_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class GetNextAlarmLogCountRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.GetNextAlarmLogCountRequest) */ {
 public:
  inline GetNextAlarmLogCountRequest() : GetNextAlarmLogCountRequest(nullptr) {}
  ~GetNextAlarmLogCountRequest() override;
  explicit constexpr GetNextAlarmLogCountRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextAlarmLogCountRequest(const GetNextAlarmLogCountRequest& from);
  GetNextAlarmLogCountRequest(GetNextAlarmLogCountRequest&& from) noexcept
    : GetNextAlarmLogCountRequest() {
    *this = ::std::move(from);
  }

  inline GetNextAlarmLogCountRequest& operator=(const GetNextAlarmLogCountRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextAlarmLogCountRequest& operator=(GetNextAlarmLogCountRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextAlarmLogCountRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextAlarmLogCountRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextAlarmLogCountRequest*>(
               &_GetNextAlarmLogCountRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(GetNextAlarmLogCountRequest& a, GetNextAlarmLogCountRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextAlarmLogCountRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextAlarmLogCountRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextAlarmLogCountRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextAlarmLogCountRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextAlarmLogCountRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextAlarmLogCountRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextAlarmLogCountRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.GetNextAlarmLogCountRequest";
  }
  protected:
  explicit GetNextAlarmLogCountRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kVisibleOnlyFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // bool visible_only = 2;
  void clear_visible_only();
  bool visible_only() const;
  void set_visible_only(bool value);
  private:
  bool _internal_visible_only() const;
  void _internal_set_visible_only(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAlarmLogCountRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  bool visible_only_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class GetNextAlarmLogCountResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.GetNextAlarmLogCountResponse) */ {
 public:
  inline GetNextAlarmLogCountResponse() : GetNextAlarmLogCountResponse(nullptr) {}
  ~GetNextAlarmLogCountResponse() override;
  explicit constexpr GetNextAlarmLogCountResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextAlarmLogCountResponse(const GetNextAlarmLogCountResponse& from);
  GetNextAlarmLogCountResponse(GetNextAlarmLogCountResponse&& from) noexcept
    : GetNextAlarmLogCountResponse() {
    *this = ::std::move(from);
  }

  inline GetNextAlarmLogCountResponse& operator=(const GetNextAlarmLogCountResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextAlarmLogCountResponse& operator=(GetNextAlarmLogCountResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextAlarmLogCountResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextAlarmLogCountResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextAlarmLogCountResponse*>(
               &_GetNextAlarmLogCountResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(GetNextAlarmLogCountResponse& a, GetNextAlarmLogCountResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextAlarmLogCountResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextAlarmLogCountResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextAlarmLogCountResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextAlarmLogCountResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextAlarmLogCountResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextAlarmLogCountResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextAlarmLogCountResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.GetNextAlarmLogCountResponse";
  }
  protected:
  explicit GetNextAlarmLogCountResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 2,
    kNumAlarmsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // int32 num_alarms = 1;
  void clear_num_alarms();
  int32_t num_alarms() const;
  void set_num_alarms(int32_t value);
  private:
  int32_t _internal_num_alarms() const;
  void _internal_set_num_alarms(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAlarmLogCountResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  int32_t num_alarms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class AttemptAutofixAlarmRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.AttemptAutofixAlarmRequest) */ {
 public:
  inline AttemptAutofixAlarmRequest() : AttemptAutofixAlarmRequest(nullptr) {}
  ~AttemptAutofixAlarmRequest() override;
  explicit constexpr AttemptAutofixAlarmRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AttemptAutofixAlarmRequest(const AttemptAutofixAlarmRequest& from);
  AttemptAutofixAlarmRequest(AttemptAutofixAlarmRequest&& from) noexcept
    : AttemptAutofixAlarmRequest() {
    *this = ::std::move(from);
  }

  inline AttemptAutofixAlarmRequest& operator=(const AttemptAutofixAlarmRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline AttemptAutofixAlarmRequest& operator=(AttemptAutofixAlarmRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AttemptAutofixAlarmRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const AttemptAutofixAlarmRequest* internal_default_instance() {
    return reinterpret_cast<const AttemptAutofixAlarmRequest*>(
               &_AttemptAutofixAlarmRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(AttemptAutofixAlarmRequest& a, AttemptAutofixAlarmRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(AttemptAutofixAlarmRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AttemptAutofixAlarmRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AttemptAutofixAlarmRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AttemptAutofixAlarmRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AttemptAutofixAlarmRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AttemptAutofixAlarmRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AttemptAutofixAlarmRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.AttemptAutofixAlarmRequest";
  }
  protected:
  explicit AttemptAutofixAlarmRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdentifierFieldNumber = 1,
  };
  // string identifier = 1;
  void clear_identifier();
  const std::string& identifier() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_identifier(ArgT0&& arg0, ArgT... args);
  std::string* mutable_identifier();
  PROTOBUF_NODISCARD std::string* release_identifier();
  void set_allocated_identifier(std::string* identifier);
  private:
  const std::string& _internal_identifier() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_identifier(const std::string& value);
  std::string* _internal_mutable_identifier();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.AttemptAutofixAlarmRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr identifier_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class GetNextAutofixAlarmStatusRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest) */ {
 public:
  inline GetNextAutofixAlarmStatusRequest() : GetNextAutofixAlarmStatusRequest(nullptr) {}
  ~GetNextAutofixAlarmStatusRequest() override;
  explicit constexpr GetNextAutofixAlarmStatusRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextAutofixAlarmStatusRequest(const GetNextAutofixAlarmStatusRequest& from);
  GetNextAutofixAlarmStatusRequest(GetNextAutofixAlarmStatusRequest&& from) noexcept
    : GetNextAutofixAlarmStatusRequest() {
    *this = ::std::move(from);
  }

  inline GetNextAutofixAlarmStatusRequest& operator=(const GetNextAutofixAlarmStatusRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextAutofixAlarmStatusRequest& operator=(GetNextAutofixAlarmStatusRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextAutofixAlarmStatusRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextAutofixAlarmStatusRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextAutofixAlarmStatusRequest*>(
               &_GetNextAutofixAlarmStatusRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GetNextAutofixAlarmStatusRequest& a, GetNextAutofixAlarmStatusRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextAutofixAlarmStatusRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextAutofixAlarmStatusRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextAutofixAlarmStatusRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextAutofixAlarmStatusRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextAutofixAlarmStatusRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextAutofixAlarmStatusRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextAutofixAlarmStatusRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest";
  }
  protected:
  explicit GetNextAutofixAlarmStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// -------------------------------------------------------------------

class GetNextAutofixAlarmStatusResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse) */ {
 public:
  inline GetNextAutofixAlarmStatusResponse() : GetNextAutofixAlarmStatusResponse(nullptr) {}
  ~GetNextAutofixAlarmStatusResponse() override;
  explicit constexpr GetNextAutofixAlarmStatusResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextAutofixAlarmStatusResponse(const GetNextAutofixAlarmStatusResponse& from);
  GetNextAutofixAlarmStatusResponse(GetNextAutofixAlarmStatusResponse&& from) noexcept
    : GetNextAutofixAlarmStatusResponse() {
    *this = ::std::move(from);
  }

  inline GetNextAutofixAlarmStatusResponse& operator=(const GetNextAutofixAlarmStatusResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextAutofixAlarmStatusResponse& operator=(GetNextAutofixAlarmStatusResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextAutofixAlarmStatusResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextAutofixAlarmStatusResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextAutofixAlarmStatusResponse*>(
               &_GetNextAutofixAlarmStatusResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(GetNextAutofixAlarmStatusResponse& a, GetNextAutofixAlarmStatusResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextAutofixAlarmStatusResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextAutofixAlarmStatusResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextAutofixAlarmStatusResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextAutofixAlarmStatusResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextAutofixAlarmStatusResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextAutofixAlarmStatusResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextAutofixAlarmStatusResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse";
  }
  protected:
  explicit GetNextAutofixAlarmStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kErrorMessageFieldNumber = 3,
    kTsFieldNumber = 1,
    kCompletedFieldNumber = 2,
  };
  // string error_message = 3;
  void clear_error_message();
  const std::string& error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error_message();
  PROTOBUF_NODISCARD std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);
  private:
  const std::string& _internal_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error_message(const std::string& value);
  std::string* _internal_mutable_error_message();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // bool completed = 2;
  void clear_completed();
  bool completed() const;
  void set_completed(bool value);
  private:
  bool _internal_completed() const;
  void _internal_set_completed(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
  ::carbon::frontend::util::Timestamp* ts_;
  bool completed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2falarm_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// AlarmRow

// int64 timestamp_ms = 1;
inline void AlarmRow::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
}
inline int64_t AlarmRow::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t AlarmRow::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void AlarmRow::_internal_set_timestamp_ms(int64_t value) {
  
  timestamp_ms_ = value;
}
inline void AlarmRow::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.timestamp_ms)
}

// string alarm_code = 2;
inline void AlarmRow::clear_alarm_code() {
  alarm_code_.ClearToEmpty();
}
inline const std::string& AlarmRow::alarm_code() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.alarm_code)
  return _internal_alarm_code();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlarmRow::set_alarm_code(ArgT0&& arg0, ArgT... args) {
 
 alarm_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.alarm_code)
}
inline std::string* AlarmRow::mutable_alarm_code() {
  std::string* _s = _internal_mutable_alarm_code();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmRow.alarm_code)
  return _s;
}
inline const std::string& AlarmRow::_internal_alarm_code() const {
  return alarm_code_.Get();
}
inline void AlarmRow::_internal_set_alarm_code(const std::string& value) {
  
  alarm_code_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlarmRow::_internal_mutable_alarm_code() {
  
  return alarm_code_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlarmRow::release_alarm_code() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AlarmRow.alarm_code)
  return alarm_code_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlarmRow::set_allocated_alarm_code(std::string* alarm_code) {
  if (alarm_code != nullptr) {
    
  } else {
    
  }
  alarm_code_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), alarm_code,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (alarm_code_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    alarm_code_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AlarmRow.alarm_code)
}

// string subsystem = 3;
inline void AlarmRow::clear_subsystem() {
  subsystem_.ClearToEmpty();
}
inline const std::string& AlarmRow::subsystem() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.subsystem)
  return _internal_subsystem();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlarmRow::set_subsystem(ArgT0&& arg0, ArgT... args) {
 
 subsystem_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.subsystem)
}
inline std::string* AlarmRow::mutable_subsystem() {
  std::string* _s = _internal_mutable_subsystem();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmRow.subsystem)
  return _s;
}
inline const std::string& AlarmRow::_internal_subsystem() const {
  return subsystem_.Get();
}
inline void AlarmRow::_internal_set_subsystem(const std::string& value) {
  
  subsystem_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlarmRow::_internal_mutable_subsystem() {
  
  return subsystem_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlarmRow::release_subsystem() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AlarmRow.subsystem)
  return subsystem_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlarmRow::set_allocated_subsystem(std::string* subsystem) {
  if (subsystem != nullptr) {
    
  } else {
    
  }
  subsystem_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), subsystem,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (subsystem_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    subsystem_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AlarmRow.subsystem)
}

// string description = 4;
inline void AlarmRow::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& AlarmRow::description() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlarmRow::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.description)
}
inline std::string* AlarmRow::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmRow.description)
  return _s;
}
inline const std::string& AlarmRow::_internal_description() const {
  return description_.Get();
}
inline void AlarmRow::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlarmRow::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlarmRow::release_description() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AlarmRow.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlarmRow::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (description_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AlarmRow.description)
}

// .carbon.frontend.alarm.AlarmLevel level = 5;
inline void AlarmRow::clear_level() {
  level_ = 0;
}
inline ::carbon::frontend::alarm::AlarmLevel AlarmRow::_internal_level() const {
  return static_cast< ::carbon::frontend::alarm::AlarmLevel >(level_);
}
inline ::carbon::frontend::alarm::AlarmLevel AlarmRow::level() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.level)
  return _internal_level();
}
inline void AlarmRow::_internal_set_level(::carbon::frontend::alarm::AlarmLevel value) {
  
  level_ = value;
}
inline void AlarmRow::set_level(::carbon::frontend::alarm::AlarmLevel value) {
  _internal_set_level(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.level)
}

// string identifier = 6;
inline void AlarmRow::clear_identifier() {
  identifier_.ClearToEmpty();
}
inline const std::string& AlarmRow::identifier() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.identifier)
  return _internal_identifier();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlarmRow::set_identifier(ArgT0&& arg0, ArgT... args) {
 
 identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.identifier)
}
inline std::string* AlarmRow::mutable_identifier() {
  std::string* _s = _internal_mutable_identifier();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmRow.identifier)
  return _s;
}
inline const std::string& AlarmRow::_internal_identifier() const {
  return identifier_.Get();
}
inline void AlarmRow::_internal_set_identifier(const std::string& value) {
  
  identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlarmRow::_internal_mutable_identifier() {
  
  return identifier_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlarmRow::release_identifier() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AlarmRow.identifier)
  return identifier_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlarmRow::set_allocated_identifier(std::string* identifier) {
  if (identifier != nullptr) {
    
  } else {
    
  }
  identifier_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), identifier,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (identifier_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AlarmRow.identifier)
}

// bool acknowledged = 7;
inline void AlarmRow::clear_acknowledged() {
  acknowledged_ = false;
}
inline bool AlarmRow::_internal_acknowledged() const {
  return acknowledged_;
}
inline bool AlarmRow::acknowledged() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.acknowledged)
  return _internal_acknowledged();
}
inline void AlarmRow::_internal_set_acknowledged(bool value) {
  
  acknowledged_ = value;
}
inline void AlarmRow::set_acknowledged(bool value) {
  _internal_set_acknowledged(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.acknowledged)
}

// .carbon.frontend.alarm.AlarmImpact impact = 8;
inline void AlarmRow::clear_impact() {
  impact_ = 0;
}
inline ::carbon::frontend::alarm::AlarmImpact AlarmRow::_internal_impact() const {
  return static_cast< ::carbon::frontend::alarm::AlarmImpact >(impact_);
}
inline ::carbon::frontend::alarm::AlarmImpact AlarmRow::impact() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.impact)
  return _internal_impact();
}
inline void AlarmRow::_internal_set_impact(::carbon::frontend::alarm::AlarmImpact value) {
  
  impact_ = value;
}
inline void AlarmRow::set_impact(::carbon::frontend::alarm::AlarmImpact value) {
  _internal_set_impact(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.impact)
}

// int64 stop_timestamp_ms = 9;
inline void AlarmRow::clear_stop_timestamp_ms() {
  stop_timestamp_ms_ = int64_t{0};
}
inline int64_t AlarmRow::_internal_stop_timestamp_ms() const {
  return stop_timestamp_ms_;
}
inline int64_t AlarmRow::stop_timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.stop_timestamp_ms)
  return _internal_stop_timestamp_ms();
}
inline void AlarmRow::_internal_set_stop_timestamp_ms(int64_t value) {
  
  stop_timestamp_ms_ = value;
}
inline void AlarmRow::set_stop_timestamp_ms(int64_t value) {
  _internal_set_stop_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.stop_timestamp_ms)
}

// bool autofix_available = 10;
inline void AlarmRow::clear_autofix_available() {
  autofix_available_ = false;
}
inline bool AlarmRow::_internal_autofix_available() const {
  return autofix_available_;
}
inline bool AlarmRow::autofix_available() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.autofix_available)
  return _internal_autofix_available();
}
inline void AlarmRow::_internal_set_autofix_available(bool value) {
  
  autofix_available_ = value;
}
inline void AlarmRow::set_autofix_available(bool value) {
  _internal_set_autofix_available(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.autofix_available)
}

// bool autofix_attempted = 11;
inline void AlarmRow::clear_autofix_attempted() {
  autofix_attempted_ = false;
}
inline bool AlarmRow::_internal_autofix_attempted() const {
  return autofix_attempted_;
}
inline bool AlarmRow::autofix_attempted() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.autofix_attempted)
  return _internal_autofix_attempted();
}
inline void AlarmRow::_internal_set_autofix_attempted(bool value) {
  
  autofix_attempted_ = value;
}
inline void AlarmRow::set_autofix_attempted(bool value) {
  _internal_set_autofix_attempted(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.autofix_attempted)
}

// uint32 autofix_duration_sec = 12;
inline void AlarmRow::clear_autofix_duration_sec() {
  autofix_duration_sec_ = 0u;
}
inline uint32_t AlarmRow::_internal_autofix_duration_sec() const {
  return autofix_duration_sec_;
}
inline uint32_t AlarmRow::autofix_duration_sec() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.autofix_duration_sec)
  return _internal_autofix_duration_sec();
}
inline void AlarmRow::_internal_set_autofix_duration_sec(uint32_t value) {
  
  autofix_duration_sec_ = value;
}
inline void AlarmRow::set_autofix_duration_sec(uint32_t value) {
  _internal_set_autofix_duration_sec(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.autofix_duration_sec)
}

// string description_key = 13;
inline void AlarmRow::clear_description_key() {
  description_key_.ClearToEmpty();
}
inline const std::string& AlarmRow::description_key() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.description_key)
  return _internal_description_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AlarmRow::set_description_key(ArgT0&& arg0, ArgT... args) {
 
 description_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmRow.description_key)
}
inline std::string* AlarmRow::mutable_description_key() {
  std::string* _s = _internal_mutable_description_key();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmRow.description_key)
  return _s;
}
inline const std::string& AlarmRow::_internal_description_key() const {
  return description_key_.Get();
}
inline void AlarmRow::_internal_set_description_key(const std::string& value) {
  
  description_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AlarmRow::_internal_mutable_description_key() {
  
  return description_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AlarmRow::release_description_key() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AlarmRow.description_key)
  return description_key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AlarmRow::set_allocated_description_key(std::string* description_key) {
  if (description_key != nullptr) {
    
  } else {
    
  }
  description_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description_key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (description_key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    description_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AlarmRow.description_key)
}

// repeated .carbon.frontend.translation.TranslationParameter translation_parameters = 14;
inline int AlarmRow::_internal_translation_parameters_size() const {
  return translation_parameters_.size();
}
inline int AlarmRow::translation_parameters_size() const {
  return _internal_translation_parameters_size();
}
inline ::carbon::frontend::translation::TranslationParameter* AlarmRow::mutable_translation_parameters(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmRow.translation_parameters)
  return translation_parameters_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::translation::TranslationParameter >*
AlarmRow::mutable_translation_parameters() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.alarm.AlarmRow.translation_parameters)
  return &translation_parameters_;
}
inline const ::carbon::frontend::translation::TranslationParameter& AlarmRow::_internal_translation_parameters(int index) const {
  return translation_parameters_.Get(index);
}
inline const ::carbon::frontend::translation::TranslationParameter& AlarmRow::translation_parameters(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmRow.translation_parameters)
  return _internal_translation_parameters(index);
}
inline ::carbon::frontend::translation::TranslationParameter* AlarmRow::_internal_add_translation_parameters() {
  return translation_parameters_.Add();
}
inline ::carbon::frontend::translation::TranslationParameter* AlarmRow::add_translation_parameters() {
  ::carbon::frontend::translation::TranslationParameter* _add = _internal_add_translation_parameters();
  // @@protoc_insertion_point(field_add:carbon.frontend.alarm.AlarmRow.translation_parameters)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::translation::TranslationParameter >&
AlarmRow::translation_parameters() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.alarm.AlarmRow.translation_parameters)
  return translation_parameters_;
}

// -------------------------------------------------------------------

// AlarmTable

// .carbon.frontend.util.Timestamp ts = 1;
inline bool AlarmTable::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool AlarmTable::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& AlarmTable::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& AlarmTable::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmTable.ts)
  return _internal_ts();
}
inline void AlarmTable::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.alarm.AlarmTable.ts)
}
inline ::carbon::frontend::util::Timestamp* AlarmTable::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* AlarmTable::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AlarmTable.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* AlarmTable::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* AlarmTable::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmTable.ts)
  return _msg;
}
inline void AlarmTable::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AlarmTable.ts)
}

// repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
inline int AlarmTable::_internal_alarms_size() const {
  return alarms_.size();
}
inline int AlarmTable::alarms_size() const {
  return _internal_alarms_size();
}
inline void AlarmTable::clear_alarms() {
  alarms_.Clear();
}
inline ::carbon::frontend::alarm::AlarmRow* AlarmTable::mutable_alarms(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmTable.alarms)
  return alarms_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
AlarmTable::mutable_alarms() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.alarm.AlarmTable.alarms)
  return &alarms_;
}
inline const ::carbon::frontend::alarm::AlarmRow& AlarmTable::_internal_alarms(int index) const {
  return alarms_.Get(index);
}
inline const ::carbon::frontend::alarm::AlarmRow& AlarmTable::alarms(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmTable.alarms)
  return _internal_alarms(index);
}
inline ::carbon::frontend::alarm::AlarmRow* AlarmTable::_internal_add_alarms() {
  return alarms_.Add();
}
inline ::carbon::frontend::alarm::AlarmRow* AlarmTable::add_alarms() {
  ::carbon::frontend::alarm::AlarmRow* _add = _internal_add_alarms();
  // @@protoc_insertion_point(field_add:carbon.frontend.alarm.AlarmTable.alarms)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
AlarmTable::alarms() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.alarm.AlarmTable.alarms)
  return alarms_;
}

// -------------------------------------------------------------------

// AlarmCount

// .carbon.frontend.util.Timestamp ts = 1;
inline bool AlarmCount::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool AlarmCount::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& AlarmCount::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& AlarmCount::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmCount.ts)
  return _internal_ts();
}
inline void AlarmCount::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.alarm.AlarmCount.ts)
}
inline ::carbon::frontend::util::Timestamp* AlarmCount::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* AlarmCount::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AlarmCount.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* AlarmCount::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* AlarmCount::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AlarmCount.ts)
  return _msg;
}
inline void AlarmCount::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AlarmCount.ts)
}

// uint32 count = 2;
inline void AlarmCount::clear_count() {
  count_ = 0u;
}
inline uint32_t AlarmCount::_internal_count() const {
  return count_;
}
inline uint32_t AlarmCount::count() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AlarmCount.count)
  return _internal_count();
}
inline void AlarmCount::_internal_set_count(uint32_t value) {
  
  count_ = value;
}
inline void AlarmCount::set_count(uint32_t value) {
  _internal_set_count(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AlarmCount.count)
}

// -------------------------------------------------------------------

// AcknowledgeRequest

// string identifier = 1;
inline void AcknowledgeRequest::clear_identifier() {
  identifier_.ClearToEmpty();
}
inline const std::string& AcknowledgeRequest::identifier() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AcknowledgeRequest.identifier)
  return _internal_identifier();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AcknowledgeRequest::set_identifier(ArgT0&& arg0, ArgT... args) {
 
 identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AcknowledgeRequest.identifier)
}
inline std::string* AcknowledgeRequest::mutable_identifier() {
  std::string* _s = _internal_mutable_identifier();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AcknowledgeRequest.identifier)
  return _s;
}
inline const std::string& AcknowledgeRequest::_internal_identifier() const {
  return identifier_.Get();
}
inline void AcknowledgeRequest::_internal_set_identifier(const std::string& value) {
  
  identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AcknowledgeRequest::_internal_mutable_identifier() {
  
  return identifier_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AcknowledgeRequest::release_identifier() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AcknowledgeRequest.identifier)
  return identifier_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AcknowledgeRequest::set_allocated_identifier(std::string* identifier) {
  if (identifier != nullptr) {
    
  } else {
    
  }
  identifier_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), identifier,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (identifier_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AcknowledgeRequest.identifier)
}

// -------------------------------------------------------------------

// GetNextAlarmLogRequest

// int32 from_idx = 1;
inline void GetNextAlarmLogRequest::clear_from_idx() {
  from_idx_ = 0;
}
inline int32_t GetNextAlarmLogRequest::_internal_from_idx() const {
  return from_idx_;
}
inline int32_t GetNextAlarmLogRequest::from_idx() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogRequest.from_idx)
  return _internal_from_idx();
}
inline void GetNextAlarmLogRequest::_internal_set_from_idx(int32_t value) {
  
  from_idx_ = value;
}
inline void GetNextAlarmLogRequest::set_from_idx(int32_t value) {
  _internal_set_from_idx(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.GetNextAlarmLogRequest.from_idx)
}

// int32 to_idx = 2;
inline void GetNextAlarmLogRequest::clear_to_idx() {
  to_idx_ = 0;
}
inline int32_t GetNextAlarmLogRequest::_internal_to_idx() const {
  return to_idx_;
}
inline int32_t GetNextAlarmLogRequest::to_idx() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogRequest.to_idx)
  return _internal_to_idx();
}
inline void GetNextAlarmLogRequest::_internal_set_to_idx(int32_t value) {
  
  to_idx_ = value;
}
inline void GetNextAlarmLogRequest::set_to_idx(int32_t value) {
  _internal_set_to_idx(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.GetNextAlarmLogRequest.to_idx)
}

// .carbon.frontend.util.Timestamp ts = 3;
inline bool GetNextAlarmLogRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextAlarmLogRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlarmLogRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlarmLogRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogRequest.ts)
  return _internal_ts();
}
inline void GetNextAlarmLogRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.alarm.GetNextAlarmLogRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.GetNextAlarmLogRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.GetNextAlarmLogRequest.ts)
  return _msg;
}
inline void GetNextAlarmLogRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.GetNextAlarmLogRequest.ts)
}

// bool visible_only = 4;
inline void GetNextAlarmLogRequest::clear_visible_only() {
  visible_only_ = false;
}
inline bool GetNextAlarmLogRequest::_internal_visible_only() const {
  return visible_only_;
}
inline bool GetNextAlarmLogRequest::visible_only() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogRequest.visible_only)
  return _internal_visible_only();
}
inline void GetNextAlarmLogRequest::_internal_set_visible_only(bool value) {
  
  visible_only_ = value;
}
inline void GetNextAlarmLogRequest::set_visible_only(bool value) {
  _internal_set_visible_only(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.GetNextAlarmLogRequest.visible_only)
}

// -------------------------------------------------------------------

// GetNextAlarmLogResponse

// repeated .carbon.frontend.alarm.AlarmRow alarms = 1;
inline int GetNextAlarmLogResponse::_internal_alarms_size() const {
  return alarms_.size();
}
inline int GetNextAlarmLogResponse::alarms_size() const {
  return _internal_alarms_size();
}
inline void GetNextAlarmLogResponse::clear_alarms() {
  alarms_.Clear();
}
inline ::carbon::frontend::alarm::AlarmRow* GetNextAlarmLogResponse::mutable_alarms(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.GetNextAlarmLogResponse.alarms)
  return alarms_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
GetNextAlarmLogResponse::mutable_alarms() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.alarm.GetNextAlarmLogResponse.alarms)
  return &alarms_;
}
inline const ::carbon::frontend::alarm::AlarmRow& GetNextAlarmLogResponse::_internal_alarms(int index) const {
  return alarms_.Get(index);
}
inline const ::carbon::frontend::alarm::AlarmRow& GetNextAlarmLogResponse::alarms(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogResponse.alarms)
  return _internal_alarms(index);
}
inline ::carbon::frontend::alarm::AlarmRow* GetNextAlarmLogResponse::_internal_add_alarms() {
  return alarms_.Add();
}
inline ::carbon::frontend::alarm::AlarmRow* GetNextAlarmLogResponse::add_alarms() {
  ::carbon::frontend::alarm::AlarmRow* _add = _internal_add_alarms();
  // @@protoc_insertion_point(field_add:carbon.frontend.alarm.GetNextAlarmLogResponse.alarms)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
GetNextAlarmLogResponse::alarms() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.alarm.GetNextAlarmLogResponse.alarms)
  return alarms_;
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextAlarmLogResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextAlarmLogResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlarmLogResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlarmLogResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogResponse.ts)
  return _internal_ts();
}
inline void GetNextAlarmLogResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.alarm.GetNextAlarmLogResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.GetNextAlarmLogResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.GetNextAlarmLogResponse.ts)
  return _msg;
}
inline void GetNextAlarmLogResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.GetNextAlarmLogResponse.ts)
}

// -------------------------------------------------------------------

// GetNextAlarmLogCountRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextAlarmLogCountRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextAlarmLogCountRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlarmLogCountRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlarmLogCountRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogCountRequest.ts)
  return _internal_ts();
}
inline void GetNextAlarmLogCountRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.alarm.GetNextAlarmLogCountRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogCountRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogCountRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.GetNextAlarmLogCountRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogCountRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogCountRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.GetNextAlarmLogCountRequest.ts)
  return _msg;
}
inline void GetNextAlarmLogCountRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.GetNextAlarmLogCountRequest.ts)
}

// bool visible_only = 2;
inline void GetNextAlarmLogCountRequest::clear_visible_only() {
  visible_only_ = false;
}
inline bool GetNextAlarmLogCountRequest::_internal_visible_only() const {
  return visible_only_;
}
inline bool GetNextAlarmLogCountRequest::visible_only() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogCountRequest.visible_only)
  return _internal_visible_only();
}
inline void GetNextAlarmLogCountRequest::_internal_set_visible_only(bool value) {
  
  visible_only_ = value;
}
inline void GetNextAlarmLogCountRequest::set_visible_only(bool value) {
  _internal_set_visible_only(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.GetNextAlarmLogCountRequest.visible_only)
}

// -------------------------------------------------------------------

// GetNextAlarmLogCountResponse

// int32 num_alarms = 1;
inline void GetNextAlarmLogCountResponse::clear_num_alarms() {
  num_alarms_ = 0;
}
inline int32_t GetNextAlarmLogCountResponse::_internal_num_alarms() const {
  return num_alarms_;
}
inline int32_t GetNextAlarmLogCountResponse::num_alarms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogCountResponse.num_alarms)
  return _internal_num_alarms();
}
inline void GetNextAlarmLogCountResponse::_internal_set_num_alarms(int32_t value) {
  
  num_alarms_ = value;
}
inline void GetNextAlarmLogCountResponse::set_num_alarms(int32_t value) {
  _internal_set_num_alarms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.GetNextAlarmLogCountResponse.num_alarms)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextAlarmLogCountResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextAlarmLogCountResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlarmLogCountResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextAlarmLogCountResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAlarmLogCountResponse.ts)
  return _internal_ts();
}
inline void GetNextAlarmLogCountResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.alarm.GetNextAlarmLogCountResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogCountResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogCountResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.GetNextAlarmLogCountResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogCountResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextAlarmLogCountResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.GetNextAlarmLogCountResponse.ts)
  return _msg;
}
inline void GetNextAlarmLogCountResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.GetNextAlarmLogCountResponse.ts)
}

// -------------------------------------------------------------------

// AttemptAutofixAlarmRequest

// string identifier = 1;
inline void AttemptAutofixAlarmRequest::clear_identifier() {
  identifier_.ClearToEmpty();
}
inline const std::string& AttemptAutofixAlarmRequest::identifier() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.AttemptAutofixAlarmRequest.identifier)
  return _internal_identifier();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AttemptAutofixAlarmRequest::set_identifier(ArgT0&& arg0, ArgT... args) {
 
 identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.AttemptAutofixAlarmRequest.identifier)
}
inline std::string* AttemptAutofixAlarmRequest::mutable_identifier() {
  std::string* _s = _internal_mutable_identifier();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.AttemptAutofixAlarmRequest.identifier)
  return _s;
}
inline const std::string& AttemptAutofixAlarmRequest::_internal_identifier() const {
  return identifier_.Get();
}
inline void AttemptAutofixAlarmRequest::_internal_set_identifier(const std::string& value) {
  
  identifier_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AttemptAutofixAlarmRequest::_internal_mutable_identifier() {
  
  return identifier_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AttemptAutofixAlarmRequest::release_identifier() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.AttemptAutofixAlarmRequest.identifier)
  return identifier_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AttemptAutofixAlarmRequest::set_allocated_identifier(std::string* identifier) {
  if (identifier != nullptr) {
    
  } else {
    
  }
  identifier_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), identifier,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (identifier_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    identifier_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.AttemptAutofixAlarmRequest.identifier)
}

// -------------------------------------------------------------------

// GetNextAutofixAlarmStatusRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextAutofixAlarmStatusRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextAutofixAlarmStatusRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextAutofixAlarmStatusRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextAutofixAlarmStatusRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest.ts)
  return _internal_ts();
}
inline void GetNextAutofixAlarmStatusRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextAutofixAlarmStatusRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAutofixAlarmStatusRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAutofixAlarmStatusRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextAutofixAlarmStatusRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest.ts)
  return _msg;
}
inline void GetNextAutofixAlarmStatusRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.GetNextAutofixAlarmStatusRequest.ts)
}

// -------------------------------------------------------------------

// GetNextAutofixAlarmStatusResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextAutofixAlarmStatusResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextAutofixAlarmStatusResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextAutofixAlarmStatusResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextAutofixAlarmStatusResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.ts)
  return _internal_ts();
}
inline void GetNextAutofixAlarmStatusResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextAutofixAlarmStatusResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAutofixAlarmStatusResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextAutofixAlarmStatusResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextAutofixAlarmStatusResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.ts)
  return _msg;
}
inline void GetNextAutofixAlarmStatusResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.ts)
}

// bool completed = 2;
inline void GetNextAutofixAlarmStatusResponse::clear_completed() {
  completed_ = false;
}
inline bool GetNextAutofixAlarmStatusResponse::_internal_completed() const {
  return completed_;
}
inline bool GetNextAutofixAlarmStatusResponse::completed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.completed)
  return _internal_completed();
}
inline void GetNextAutofixAlarmStatusResponse::_internal_set_completed(bool value) {
  
  completed_ = value;
}
inline void GetNextAutofixAlarmStatusResponse::set_completed(bool value) {
  _internal_set_completed(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.completed)
}

// string error_message = 3;
inline void GetNextAutofixAlarmStatusResponse::clear_error_message() {
  error_message_.ClearToEmpty();
}
inline const std::string& GetNextAutofixAlarmStatusResponse::error_message() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.error_message)
  return _internal_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextAutofixAlarmStatusResponse::set_error_message(ArgT0&& arg0, ArgT... args) {
 
 error_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.error_message)
}
inline std::string* GetNextAutofixAlarmStatusResponse::mutable_error_message() {
  std::string* _s = _internal_mutable_error_message();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.error_message)
  return _s;
}
inline const std::string& GetNextAutofixAlarmStatusResponse::_internal_error_message() const {
  return error_message_.Get();
}
inline void GetNextAutofixAlarmStatusResponse::_internal_set_error_message(const std::string& value) {
  
  error_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextAutofixAlarmStatusResponse::_internal_mutable_error_message() {
  
  return error_message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextAutofixAlarmStatusResponse::release_error_message() {
  // @@protoc_insertion_point(field_release:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.error_message)
  return error_message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextAutofixAlarmStatusResponse::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  error_message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), error_message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (error_message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.alarm.GetNextAutofixAlarmStatusResponse.error_message)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace alarm
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::alarm::AlarmLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::alarm::AlarmLevel>() {
  return ::carbon::frontend::alarm::AlarmLevel_descriptor();
}
template <> struct is_proto_enum< ::carbon::frontend::alarm::AlarmImpact> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::alarm::AlarmImpact>() {
  return ::carbon::frontend::alarm::AlarmImpact_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2falarm_2eproto

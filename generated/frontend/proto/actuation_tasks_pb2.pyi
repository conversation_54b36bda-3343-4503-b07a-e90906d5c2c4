"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.core.controls.exterminator.controllers.aimbot.process.proto.aimbot_pb2 import (
    ActuationTaskRequest as core___controls___exterminator___controllers___aimbot___process___proto___aimbot_pb2___ActuationTaskRequest,
)

from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GlobalAimbotActuationTaskRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...

    @property
    def task(self) -> core___controls___exterminator___controllers___aimbot___process___proto___aimbot_pb2___ActuationTaskRequest: ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        task : typing___Optional[core___controls___exterminator___controllers___aimbot___process___proto___aimbot_pb2___ActuationTaskRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"task",b"task"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_id",b"row_id",u"task",b"task"]) -> None: ...
type___GlobalAimbotActuationTaskRequest = GlobalAimbotActuationTaskRequest

class GlobalActuationTaskState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    running: builtin___bool = ...
    elapsed_time_ms: builtin___int = ...
    expected_time_ms: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        running : typing___Optional[builtin___bool] = None,
        elapsed_time_ms : typing___Optional[builtin___int] = None,
        expected_time_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"elapsed_time_ms",b"elapsed_time_ms",u"expected_time_ms",b"expected_time_ms",u"running",b"running",u"ts",b"ts"]) -> None: ...
type___GlobalActuationTaskState = GlobalActuationTaskState

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class ModuleIdentity(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    serial: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        serial : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"serial",b"serial"]) -> None: ...
type___ModuleIdentity = ModuleIdentity

class GetNextModulesListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___GetNextModulesListRequest = GetNextModulesListRequest

class GetNextModulesListResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def assigned_modules(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModuleIdentity]: ...

    @property
    def unassigned_modules(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModuleIdentity]: ...

    @property
    def unset_serial_modules(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModuleIdentity]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        assigned_modules : typing___Optional[typing___Iterable[type___ModuleIdentity]] = None,
        unassigned_modules : typing___Optional[typing___Iterable[type___ModuleIdentity]] = None,
        unset_serial_modules : typing___Optional[typing___Iterable[type___ModuleIdentity]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"assigned_modules",b"assigned_modules",u"ts",b"ts",u"unassigned_modules",b"unassigned_modules",u"unset_serial_modules",b"unset_serial_modules"]) -> None: ...
type___GetNextModulesListResponse = GetNextModulesListResponse

class GetNextActiveModulesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___GetNextActiveModulesRequest = GetNextActiveModulesRequest

class GetNextActiveModulesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def active_modules(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModuleIdentity]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        active_modules : typing___Optional[typing___Iterable[type___ModuleIdentity]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active_modules",b"active_modules",u"ts",b"ts"]) -> None: ...
type___GetNextActiveModulesResponse = GetNextActiveModulesResponse

class IdentifyModuleRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def module_identity(self) -> type___ModuleIdentity: ...

    def __init__(self,
        *,
        module_identity : typing___Optional[type___ModuleIdentity] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"module_identity",b"module_identity"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"module_identity",b"module_identity"]) -> None: ...
type___IdentifyModuleRequest = IdentifyModuleRequest

class AssignModuleRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def module_identity(self) -> type___ModuleIdentity: ...

    def __init__(self,
        *,
        module_identity : typing___Optional[type___ModuleIdentity] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"module_identity",b"module_identity"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"module_identity",b"module_identity"]) -> None: ...
type___AssignModuleRequest = AssignModuleRequest

class ClearModuleAssignmentRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def module_identity(self) -> type___ModuleIdentity: ...

    def __init__(self,
        *,
        module_identity : typing___Optional[type___ModuleIdentity] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"module_identity",b"module_identity"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"module_identity",b"module_identity"]) -> None: ...
type___ClearModuleAssignmentRequest = ClearModuleAssignmentRequest

class SetModuleSerialRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    new_serial: typing___Text = ...

    @property
    def module_identity(self) -> type___ModuleIdentity: ...

    def __init__(self,
        *,
        module_identity : typing___Optional[type___ModuleIdentity] = None,
        new_serial : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"module_identity",b"module_identity"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"module_identity",b"module_identity",u"new_serial",b"new_serial"]) -> None: ...
type___SetModuleSerialRequest = SetModuleSerialRequest

class ModuleDefinition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    module_spacing_mm: builtin___float = ...
    disabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        module_spacing_mm : typing___Optional[builtin___float] = None,
        disabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"disabled",b"disabled",u"module_id",b"module_id",u"module_spacing_mm",b"module_spacing_mm"]) -> None: ...
type___ModuleDefinition = ModuleDefinition

class RowDefinition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...
    row_spacing_mm: builtin___float = ...

    @property
    def modules(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModuleDefinition]: ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        modules : typing___Optional[typing___Iterable[type___ModuleDefinition]] = None,
        row_spacing_mm : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"modules",b"modules",u"row_id",b"row_id",u"row_spacing_mm",b"row_spacing_mm"]) -> None: ...
type___RowDefinition = RowDefinition

class BarDefinition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    bar_length_mm: builtin___int = ...
    folding: builtin___bool = ...

    def __init__(self,
        *,
        bar_length_mm : typing___Optional[builtin___int] = None,
        folding : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bar_length_mm",b"bar_length_mm",u"folding",b"folding"]) -> None: ...
type___BarDefinition = BarDefinition

class RobotDefinition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def rows(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___RowDefinition]: ...

    @property
    def bar_definition(self) -> type___BarDefinition: ...

    def __init__(self,
        *,
        rows : typing___Optional[typing___Iterable[type___RowDefinition]] = None,
        bar_definition : typing___Optional[type___BarDefinition] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bar_definition",b"bar_definition"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bar_definition",b"bar_definition",u"rows",b"rows"]) -> None: ...
type___RobotDefinition = RobotDefinition

class Preset(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uuid: typing___Text = ...
    display_name: typing___Text = ...

    @property
    def definition(self) -> type___RobotDefinition: ...

    def __init__(self,
        *,
        uuid : typing___Optional[typing___Text] = None,
        display_name : typing___Optional[typing___Text] = None,
        definition : typing___Optional[type___RobotDefinition] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"definition",b"definition"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"definition",b"definition",u"display_name",b"display_name",u"uuid",b"uuid"]) -> None: ...
type___Preset = Preset

class GetPresetsListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    language: typing___Text = ...

    def __init__(self,
        *,
        language : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"language",b"language"]) -> None: ...
type___GetPresetsListRequest = GetPresetsListRequest

class GetPresetsListResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def presets(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Preset]: ...

    def __init__(self,
        *,
        presets : typing___Optional[typing___Iterable[type___Preset]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"presets",b"presets"]) -> None: ...
type___GetPresetsListResponse = GetPresetsListResponse

class GetCurrentRobotDefinitionResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def current_definition(self) -> type___RobotDefinition: ...

    def __init__(self,
        *,
        current_definition : typing___Optional[type___RobotDefinition] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_current_definition",b"_current_definition",u"current_definition",b"current_definition"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_current_definition",b"_current_definition",u"current_definition",b"current_definition"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_current_definition",b"_current_definition"]) -> typing_extensions___Literal["current_definition"]: ...
type___GetCurrentRobotDefinitionResponse = GetCurrentRobotDefinitionResponse

class SetCurrentRobotDefinitionRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def current_definition(self) -> type___RobotDefinition: ...

    def __init__(self,
        *,
        current_definition : typing___Optional[type___RobotDefinition] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current_definition",b"current_definition"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_definition",b"current_definition"]) -> None: ...
type___SetCurrentRobotDefinitionRequest = SetCurrentRobotDefinitionRequest

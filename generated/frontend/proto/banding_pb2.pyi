"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)

from generated.weed_tracking.proto.weed_tracking_pb2 import (
    BandDefinition as weed_tracking___proto___weed_tracking_pb2___BandDefinition,
    DiagnosticsSnapshot as weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

VisualizationTypeToIncludeValue = typing___NewType('VisualizationTypeToIncludeValue', builtin___int)
type___VisualizationTypeToIncludeValue = VisualizationTypeToIncludeValue
VisualizationTypeToInclude: _VisualizationTypeToInclude
class _VisualizationTypeToInclude(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[VisualizationTypeToIncludeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    DUPLICATE_WEED = typing___cast(VisualizationTypeToIncludeValue, 0)
    DUPLICATE_CROP = typing___cast(VisualizationTypeToIncludeValue, 1)
    KILLED_WEED = typing___cast(VisualizationTypeToIncludeValue, 2)
    KILLED_CROP = typing___cast(VisualizationTypeToIncludeValue, 3)
    KILLING_WEED = typing___cast(VisualizationTypeToIncludeValue, 4)
    IGNORED_WEED = typing___cast(VisualizationTypeToIncludeValue, 5)
    KILLING_CROP = typing___cast(VisualizationTypeToIncludeValue, 6)
    ERROR_WEED = typing___cast(VisualizationTypeToIncludeValue, 7)
    ERROR_CROP = typing___cast(VisualizationTypeToIncludeValue, 8)
    IGNORED_CROP = typing___cast(VisualizationTypeToIncludeValue, 9)
    WEED = typing___cast(VisualizationTypeToIncludeValue, 10)
    CROP = typing___cast(VisualizationTypeToIncludeValue, 11)
    CROP_RADIUS = typing___cast(VisualizationTypeToIncludeValue, 12)
    CROP_KEPT = typing___cast(VisualizationTypeToIncludeValue, 13)
    THINNING_BOX = typing___cast(VisualizationTypeToIncludeValue, 14)
DUPLICATE_WEED = typing___cast(VisualizationTypeToIncludeValue, 0)
DUPLICATE_CROP = typing___cast(VisualizationTypeToIncludeValue, 1)
KILLED_WEED = typing___cast(VisualizationTypeToIncludeValue, 2)
KILLED_CROP = typing___cast(VisualizationTypeToIncludeValue, 3)
KILLING_WEED = typing___cast(VisualizationTypeToIncludeValue, 4)
IGNORED_WEED = typing___cast(VisualizationTypeToIncludeValue, 5)
KILLING_CROP = typing___cast(VisualizationTypeToIncludeValue, 6)
ERROR_WEED = typing___cast(VisualizationTypeToIncludeValue, 7)
ERROR_CROP = typing___cast(VisualizationTypeToIncludeValue, 8)
IGNORED_CROP = typing___cast(VisualizationTypeToIncludeValue, 9)
WEED = typing___cast(VisualizationTypeToIncludeValue, 10)
CROP = typing___cast(VisualizationTypeToIncludeValue, 11)
CROP_RADIUS = typing___cast(VisualizationTypeToIncludeValue, 12)
CROP_KEPT = typing___cast(VisualizationTypeToIncludeValue, 13)
THINNING_BOX = typing___cast(VisualizationTypeToIncludeValue, 14)

ThresholdStateValue = typing___NewType('ThresholdStateValue', builtin___int)
type___ThresholdStateValue = ThresholdStateValue
ThresholdState: _ThresholdState
class _ThresholdState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ThresholdStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    ANY = typing___cast(ThresholdStateValue, 0)
    PASS = typing___cast(ThresholdStateValue, 1)
    FAIL = typing___cast(ThresholdStateValue, 2)
ANY = typing___cast(ThresholdStateValue, 0)
PASS = typing___cast(ThresholdStateValue, 1)
FAIL = typing___cast(ThresholdStateValue, 2)

class BandingRow(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...

    @property
    def bands(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[weed_tracking___proto___weed_tracking_pb2___BandDefinition]: ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        bands : typing___Optional[typing___Iterable[weed_tracking___proto___weed_tracking_pb2___BandDefinition]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bands",b"bands",u"row_id",b"row_id"]) -> None: ...
type___BandingRow = BandingRow

class BandingDef(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    uuid: typing___Text = ...

    @property
    def rows(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___BandingRow]: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        rows : typing___Optional[typing___Iterable[type___BandingRow]] = None,
        uuid : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"rows",b"rows",u"uuid",b"uuid"]) -> None: ...
type___BandingDef = BandingDef

class SaveBandingDefRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    setActive: builtin___bool = ...

    @property
    def bandingDef(self) -> type___BandingDef: ...

    def __init__(self,
        *,
        bandingDef : typing___Optional[type___BandingDef] = None,
        setActive : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bandingDef",b"bandingDef"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bandingDef",b"bandingDef",u"setActive",b"setActive"]) -> None: ...
type___SaveBandingDefRequest = SaveBandingDefRequest

class LoadBandingDefsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    activeDef: typing___Text = ...
    activeDefUUID: typing___Text = ...

    @property
    def bandingDefs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___BandingDef]: ...

    def __init__(self,
        *,
        bandingDefs : typing___Optional[typing___Iterable[type___BandingDef]] = None,
        activeDef : typing___Optional[typing___Text] = None,
        activeDefUUID : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"activeDef",b"activeDef",u"activeDefUUID",b"activeDefUUID",u"bandingDefs",b"bandingDefs"]) -> None: ...
type___LoadBandingDefsResponse = LoadBandingDefsResponse

class SetActiveBandingDefRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    uuid: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        uuid : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"uuid",b"uuid"]) -> None: ...
type___SetActiveBandingDefRequest = SetActiveBandingDefRequest

class GetActiveBandingDefResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    uuid: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        uuid : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"uuid",b"uuid"]) -> None: ...
type___GetActiveBandingDefResponse = GetActiveBandingDefResponse

class DeleteBandingDefRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    uuid: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        uuid : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"uuid",b"uuid"]) -> None: ...
type___DeleteBandingDefRequest = DeleteBandingDefRequest

class VisualizationData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x_mm: builtin___int = ...
    y_mm: builtin___int = ...
    z_mm: builtin___int = ...
    is_weed: builtin___bool = ...

    def __init__(self,
        *,
        x_mm : typing___Optional[builtin___int] = None,
        y_mm : typing___Optional[builtin___int] = None,
        z_mm : typing___Optional[builtin___int] = None,
        is_weed : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"is_weed",b"is_weed",u"x_mm",b"x_mm",u"y_mm",b"y_mm",u"z_mm",b"z_mm"]) -> None: ...
type___VisualizationData = VisualizationData

class GetNextVisualizationDataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...
    types_to_include: google___protobuf___internal___containers___RepeatedScalarFieldContainer[type___VisualizationTypeToIncludeValue] = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def threshold_filters(self) -> type___ThresholdFilters: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        row_id : typing___Optional[builtin___int] = None,
        types_to_include : typing___Optional[typing___Iterable[type___VisualizationTypeToIncludeValue]] = None,
        threshold_filters : typing___Optional[type___ThresholdFilters] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"threshold_filters",b"threshold_filters",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_id",b"row_id",u"threshold_filters",b"threshold_filters",u"ts",b"ts",u"types_to_include",b"types_to_include"]) -> None: ...
type___GetNextVisualizationDataRequest = GetNextVisualizationDataRequest

class GetNextVisualizationDataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def data(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___VisualizationData]: ...

    @property
    def bands(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[weed_tracking___proto___weed_tracking_pb2___BandDefinition]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        data : typing___Optional[typing___Iterable[type___VisualizationData]] = None,
        bands : typing___Optional[typing___Iterable[weed_tracking___proto___weed_tracking_pb2___BandDefinition]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bands",b"bands",u"data",b"data",u"ts",b"ts"]) -> None: ...
type___GetNextVisualizationDataResponse = GetNextVisualizationDataResponse

class GetNextVisualizationData2Response(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def data(self) -> weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        data : typing___Optional[weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"data",b"data",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data",b"data",u"ts",b"ts"]) -> None: ...
type___GetNextVisualizationData2Response = GetNextVisualizationData2Response

class GetDimensionsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_id",b"row_id"]) -> None: ...
type___GetDimensionsRequest = GetDimensionsRequest

class SetBandingEnabledRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___SetBandingEnabledRequest = SetBandingEnabledRequest

class SetBandingEnabledResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetBandingEnabledResponse = SetBandingEnabledResponse

class IsBandingEnabledResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___IsBandingEnabledResponse = IsBandingEnabledResponse

class GetVisualizationMetadataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class CropSafetyRadiusMmPerRowEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...
        value: builtin___float = ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[builtin___float] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___CropSafetyRadiusMmPerRowEntry = CropSafetyRadiusMmPerRowEntry


    @property
    def crop_safety_radius_mm_per_row(self) -> google___protobuf___internal___containers___ScalarMap[builtin___int, builtin___float]: ...

    def __init__(self,
        *,
        crop_safety_radius_mm_per_row : typing___Optional[typing___Mapping[builtin___int, builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_safety_radius_mm_per_row",b"crop_safety_radius_mm_per_row"]) -> None: ...
type___GetVisualizationMetadataResponse = GetVisualizationMetadataResponse

class ThresholdFilter(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weeding: type___ThresholdStateValue = ...
    thinning: type___ThresholdStateValue = ...
    banding: type___ThresholdStateValue = ...

    def __init__(self,
        *,
        weeding : typing___Optional[type___ThresholdStateValue] = None,
        thinning : typing___Optional[type___ThresholdStateValue] = None,
        banding : typing___Optional[type___ThresholdStateValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"banding",b"banding",u"thinning",b"thinning",u"weeding",b"weeding"]) -> None: ...
type___ThresholdFilter = ThresholdFilter

class ThresholdFilters(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def crop(self) -> type___ThresholdFilter: ...

    @property
    def weed(self) -> type___ThresholdFilter: ...

    def __init__(self,
        *,
        crop : typing___Optional[type___ThresholdFilter] = None,
        weed : typing___Optional[type___ThresholdFilter] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"crop",b"crop",u"weed",b"weed"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop",b"crop",u"weed",b"weed"]) -> None: ...
type___ThresholdFilters = ThresholdFilters

class GetNextVisualizationDataForAllRowsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    types_to_include: google___protobuf___internal___containers___RepeatedScalarFieldContainer[type___VisualizationTypeToIncludeValue] = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def threshold_filters(self) -> type___ThresholdFilters: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        types_to_include : typing___Optional[typing___Iterable[type___VisualizationTypeToIncludeValue]] = None,
        threshold_filters : typing___Optional[type___ThresholdFilters] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"threshold_filters",b"threshold_filters",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"threshold_filters",b"threshold_filters",u"ts",b"ts",u"types_to_include",b"types_to_include"]) -> None: ...
type___GetNextVisualizationDataForAllRowsRequest = GetNextVisualizationDataForAllRowsRequest

class GetNextVisualizationDataForAllRowsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class DataPerRowEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___DataPerRowEntry = DataPerRowEntry

    types_to_include: google___protobuf___internal___containers___RepeatedScalarFieldContainer[type___VisualizationTypeToIncludeValue] = ...

    @property
    def data_per_row(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        data_per_row : typing___Optional[typing___Mapping[builtin___int, weed_tracking___proto___weed_tracking_pb2___DiagnosticsSnapshot]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        types_to_include : typing___Optional[typing___Iterable[type___VisualizationTypeToIncludeValue]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data_per_row",b"data_per_row",u"ts",b"ts",u"types_to_include",b"types_to_include"]) -> None: ...
type___GetNextVisualizationDataForAllRowsResponse = GetNextVisualizationDataForAllRowsResponse

class GetNextBandingStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    activeDefUUID: typing___Text = ...
    is_banding_enabled: builtin___bool = ...
    is_dynamic_banding_enabled: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def bandingDefs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___BandingDef]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        bandingDefs : typing___Optional[typing___Iterable[type___BandingDef]] = None,
        activeDefUUID : typing___Optional[typing___Text] = None,
        is_banding_enabled : typing___Optional[builtin___bool] = None,
        is_dynamic_banding_enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"activeDefUUID",b"activeDefUUID",u"bandingDefs",b"bandingDefs",u"is_banding_enabled",b"is_banding_enabled",u"is_dynamic_banding_enabled",b"is_dynamic_banding_enabled",u"ts",b"ts"]) -> None: ...
type___GetNextBandingStateResponse = GetNextBandingStateResponse

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/util.proto

#include "frontend/proto/util.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace util {
constexpr Empty::Empty(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct EmptyDefaultTypeInternal {
  constexpr EmptyDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EmptyDefaultTypeInternal() {}
  union {
    Empty _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EmptyDefaultTypeInternal _Empty_default_instance_;
constexpr Timestamp::Timestamp(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_ms_(int64_t{0}){}
struct TimestampDefaultTypeInternal {
  constexpr TimestampDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TimestampDefaultTypeInternal() {}
  union {
    Timestamp _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TimestampDefaultTypeInternal _Timestamp_default_instance_;
constexpr FeatureFlags_FlagsEntry_DoNotUse::FeatureFlags_FlagsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct FeatureFlags_FlagsEntry_DoNotUseDefaultTypeInternal {
  constexpr FeatureFlags_FlagsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FeatureFlags_FlagsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    FeatureFlags_FlagsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FeatureFlags_FlagsEntry_DoNotUseDefaultTypeInternal _FeatureFlags_FlagsEntry_DoNotUse_default_instance_;
constexpr FeatureFlags::FeatureFlags(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : flags_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , ts_(nullptr){}
struct FeatureFlagsDefaultTypeInternal {
  constexpr FeatureFlagsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FeatureFlagsDefaultTypeInternal() {}
  union {
    FeatureFlags _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FeatureFlagsDefaultTypeInternal _FeatureFlags_default_instance_;
}  // namespace util
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2futil_2eproto[4];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2futil_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2futil_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2futil_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::Empty, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::Timestamp, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::Timestamp, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::FeatureFlags_FlagsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::FeatureFlags_FlagsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::FeatureFlags_FlagsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::FeatureFlags_FlagsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::FeatureFlags, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::FeatureFlags, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::util::FeatureFlags, flags_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::util::Empty)},
  { 6, -1, -1, sizeof(::carbon::frontend::util::Timestamp)},
  { 13, 21, -1, sizeof(::carbon::frontend::util::FeatureFlags_FlagsEntry_DoNotUse)},
  { 23, -1, -1, sizeof(::carbon::frontend::util::FeatureFlags)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::util::_Empty_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::util::_Timestamp_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::util::_FeatureFlags_FlagsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::util::_FeatureFlags_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2futil_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031frontend/proto/util.proto\022\024carbon.fron"
  "tend.util\"\007\n\005Empty\"!\n\tTimestamp\022\024\n\014times"
  "tamp_ms\030\001 \001(\003\"\247\001\n\014FeatureFlags\022+\n\002ts\030\001 \001"
  "(\0132\037.carbon.frontend.util.Timestamp\022<\n\005f"
  "lags\030\002 \003(\0132-.carbon.frontend.util.Featur"
  "eFlags.FlagsEntry\032,\n\nFlagsEntry\022\013\n\003key\030\001"
  " \001(\t\022\r\n\005value\030\002 \001(\010:\0028\001B\020Z\016proto/fronten"
  "db\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2futil_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2futil_2eproto = {
  false, false, 289, descriptor_table_protodef_frontend_2fproto_2futil_2eproto, "frontend/proto/util.proto", 
  &descriptor_table_frontend_2fproto_2futil_2eproto_once, nullptr, 0, 4,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2futil_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2futil_2eproto, file_level_enum_descriptors_frontend_2fproto_2futil_2eproto, file_level_service_descriptors_frontend_2fproto_2futil_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2futil_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2futil_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2futil_2eproto(&descriptor_table_frontend_2fproto_2futil_2eproto);
namespace carbon {
namespace frontend {
namespace util {

// ===================================================================

class Empty::_Internal {
 public:
};

Empty::Empty(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.util.Empty)
}
Empty::Empty(const Empty& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.util.Empty)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Empty::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Empty::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata Empty::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2futil_2eproto_getter, &descriptor_table_frontend_2fproto_2futil_2eproto_once,
      file_level_metadata_frontend_2fproto_2futil_2eproto[0]);
}

// ===================================================================

class Timestamp::_Internal {
 public:
};

Timestamp::Timestamp(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.util.Timestamp)
}
Timestamp::Timestamp(const Timestamp& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.util.Timestamp)
}

inline void Timestamp::SharedCtor() {
timestamp_ms_ = int64_t{0};
}

Timestamp::~Timestamp() {
  // @@protoc_insertion_point(destructor:carbon.frontend.util.Timestamp)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Timestamp::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void Timestamp::ArenaDtor(void* object) {
  Timestamp* _this = reinterpret_cast< Timestamp* >(object);
  (void)_this;
}
void Timestamp::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Timestamp::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Timestamp::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.util.Timestamp)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Timestamp::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Timestamp::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.util.Timestamp)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.util.Timestamp)
  return target;
}

size_t Timestamp::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.util.Timestamp)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Timestamp::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Timestamp::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Timestamp::GetClassData() const { return &_class_data_; }

void Timestamp::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Timestamp *>(to)->MergeFrom(
      static_cast<const Timestamp &>(from));
}


void Timestamp::MergeFrom(const Timestamp& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.util.Timestamp)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Timestamp::CopyFrom(const Timestamp& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.util.Timestamp)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Timestamp::IsInitialized() const {
  return true;
}

void Timestamp::InternalSwap(Timestamp* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(timestamp_ms_, other->timestamp_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Timestamp::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2futil_2eproto_getter, &descriptor_table_frontend_2fproto_2futil_2eproto_once,
      file_level_metadata_frontend_2fproto_2futil_2eproto[1]);
}

// ===================================================================

FeatureFlags_FlagsEntry_DoNotUse::FeatureFlags_FlagsEntry_DoNotUse() {}
FeatureFlags_FlagsEntry_DoNotUse::FeatureFlags_FlagsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void FeatureFlags_FlagsEntry_DoNotUse::MergeFrom(const FeatureFlags_FlagsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata FeatureFlags_FlagsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2futil_2eproto_getter, &descriptor_table_frontend_2fproto_2futil_2eproto_once,
      file_level_metadata_frontend_2fproto_2futil_2eproto[2]);
}

// ===================================================================

class FeatureFlags::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const FeatureFlags* msg);
};

const ::carbon::frontend::util::Timestamp&
FeatureFlags::_Internal::ts(const FeatureFlags* msg) {
  return *msg->ts_;
}
FeatureFlags::FeatureFlags(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  flags_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.util.FeatureFlags)
}
FeatureFlags::FeatureFlags(const FeatureFlags& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  flags_.MergeFrom(from.flags_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.util.FeatureFlags)
}

inline void FeatureFlags::SharedCtor() {
ts_ = nullptr;
}

FeatureFlags::~FeatureFlags() {
  // @@protoc_insertion_point(destructor:carbon.frontend.util.FeatureFlags)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FeatureFlags::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void FeatureFlags::ArenaDtor(void* object) {
  FeatureFlags* _this = reinterpret_cast< FeatureFlags* >(object);
  (void)_this;
  _this->flags_. ~MapField();
}
inline void FeatureFlags::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &FeatureFlags::ArenaDtor);
  }
}
void FeatureFlags::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FeatureFlags::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.util.FeatureFlags)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  flags_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FeatureFlags::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<string, bool> flags = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&flags_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FeatureFlags::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.util.FeatureFlags)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // map<string, bool> flags = 2;
  if (!this->_internal_flags().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.util.FeatureFlags.FlagsEntry.key");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_flags().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_flags().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::const_iterator
          it = this->_internal_flags().begin();
          it != this->_internal_flags().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = FeatureFlags_FlagsEntry_DoNotUse::Funcs::InternalSerialize(2, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::const_iterator
          it = this->_internal_flags().begin();
          it != this->_internal_flags().end(); ++it) {
        target = FeatureFlags_FlagsEntry_DoNotUse::Funcs::InternalSerialize(2, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.util.FeatureFlags)
  return target;
}

size_t FeatureFlags::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.util.FeatureFlags)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, bool> flags = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_flags_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, bool >::const_iterator
      it = this->_internal_flags().begin();
      it != this->_internal_flags().end(); ++it) {
    total_size += FeatureFlags_FlagsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FeatureFlags::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FeatureFlags::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FeatureFlags::GetClassData() const { return &_class_data_; }

void FeatureFlags::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FeatureFlags *>(to)->MergeFrom(
      static_cast<const FeatureFlags &>(from));
}


void FeatureFlags::MergeFrom(const FeatureFlags& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.util.FeatureFlags)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  flags_.MergeFrom(from.flags_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FeatureFlags::CopyFrom(const FeatureFlags& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.util.FeatureFlags)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FeatureFlags::IsInitialized() const {
  return true;
}

void FeatureFlags::InternalSwap(FeatureFlags* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  flags_.InternalSwap(&other->flags_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FeatureFlags::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2futil_2eproto_getter, &descriptor_table_frontend_2fproto_2futil_2eproto_once,
      file_level_metadata_frontend_2fproto_2futil_2eproto[3]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace util
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::util::Empty* Arena::CreateMaybeMessage< ::carbon::frontend::util::Empty >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::util::Empty >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::util::Timestamp* Arena::CreateMaybeMessage< ::carbon::frontend::util::Timestamp >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::util::Timestamp >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::util::FeatureFlags_FlagsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::util::FeatureFlags_FlagsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::util::FeatureFlags_FlagsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::util::FeatureFlags* Arena::CreateMaybeMessage< ::carbon::frontend::util::FeatureFlags >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::util::FeatureFlags >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/translation.proto

#include "frontend/proto/translation.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace translation {
constexpr IntegerValue::IntegerValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : value_(int64_t{0}){}
struct IntegerValueDefaultTypeInternal {
  constexpr IntegerValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~IntegerValueDefaultTypeInternal() {}
  union {
    IntegerValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT IntegerValueDefaultTypeInternal _IntegerValue_default_instance_;
constexpr DoubleValue::DoubleValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : value_(0){}
struct DoubleValueDefaultTypeInternal {
  constexpr DoubleValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DoubleValueDefaultTypeInternal() {}
  union {
    DoubleValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DoubleValueDefaultTypeInternal _DoubleValue_default_instance_;
constexpr StringValue::StringValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : value_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct StringValueDefaultTypeInternal {
  constexpr StringValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StringValueDefaultTypeInternal() {}
  union {
    StringValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StringValueDefaultTypeInternal _StringValue_default_instance_;
constexpr TemperatureValue::TemperatureValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct TemperatureValueDefaultTypeInternal {
  constexpr TemperatureValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TemperatureValueDefaultTypeInternal() {}
  union {
    TemperatureValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TemperatureValueDefaultTypeInternal _TemperatureValue_default_instance_;
constexpr PercentValue::PercentValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : percent_(0u){}
struct PercentValueDefaultTypeInternal {
  constexpr PercentValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PercentValueDefaultTypeInternal() {}
  union {
    PercentValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PercentValueDefaultTypeInternal _PercentValue_default_instance_;
constexpr VoltageValue::VoltageValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : volts_(0){}
struct VoltageValueDefaultTypeInternal {
  constexpr VoltageValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~VoltageValueDefaultTypeInternal() {}
  union {
    VoltageValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT VoltageValueDefaultTypeInternal _VoltageValue_default_instance_;
constexpr FrequencyValue::FrequencyValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : hertz_(0){}
struct FrequencyValueDefaultTypeInternal {
  constexpr FrequencyValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FrequencyValueDefaultTypeInternal() {}
  union {
    FrequencyValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FrequencyValueDefaultTypeInternal _FrequencyValue_default_instance_;
constexpr AreaValue::AreaValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct AreaValueDefaultTypeInternal {
  constexpr AreaValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AreaValueDefaultTypeInternal() {}
  union {
    AreaValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AreaValueDefaultTypeInternal _AreaValue_default_instance_;
constexpr DurationValue::DurationValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct DurationValueDefaultTypeInternal {
  constexpr DurationValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DurationValueDefaultTypeInternal() {}
  union {
    DurationValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DurationValueDefaultTypeInternal _DurationValue_default_instance_;
constexpr DistanceValue::DistanceValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct DistanceValueDefaultTypeInternal {
  constexpr DistanceValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DistanceValueDefaultTypeInternal() {}
  union {
    DistanceValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DistanceValueDefaultTypeInternal _DistanceValue_default_instance_;
constexpr SpeedValue::SpeedValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : _oneof_case_{}{}
struct SpeedValueDefaultTypeInternal {
  constexpr SpeedValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SpeedValueDefaultTypeInternal() {}
  union {
    SpeedValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SpeedValueDefaultTypeInternal _SpeedValue_default_instance_;
constexpr TranslationParameter::TranslationParameter(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , _oneof_case_{}{}
struct TranslationParameterDefaultTypeInternal {
  constexpr TranslationParameterDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TranslationParameterDefaultTypeInternal() {}
  union {
    TranslationParameter _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TranslationParameterDefaultTypeInternal _TranslationParameter_default_instance_;
}  // namespace translation
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2ftranslation_2eproto[12];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2ftranslation_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2ftranslation_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2ftranslation_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::IntegerValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::IntegerValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::DoubleValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::DoubleValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::StringValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::StringValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::TemperatureValue, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::TemperatureValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::TemperatureValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::PercentValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::PercentValue, percent_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::VoltageValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::VoltageValue, volts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::FrequencyValue, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::FrequencyValue, hertz_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::AreaValue, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::AreaValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::AreaValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::DurationValue, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::DurationValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::DurationValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::DistanceValue, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::DistanceValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::DistanceValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::SpeedValue, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::SpeedValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::SpeedValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::TranslationParameter, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::TranslationParameter, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::TranslationParameter, name_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::translation::TranslationParameter, value_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::translation::IntegerValue)},
  { 7, -1, -1, sizeof(::carbon::frontend::translation::DoubleValue)},
  { 14, -1, -1, sizeof(::carbon::frontend::translation::StringValue)},
  { 21, -1, -1, sizeof(::carbon::frontend::translation::TemperatureValue)},
  { 30, -1, -1, sizeof(::carbon::frontend::translation::PercentValue)},
  { 37, -1, -1, sizeof(::carbon::frontend::translation::VoltageValue)},
  { 44, -1, -1, sizeof(::carbon::frontend::translation::FrequencyValue)},
  { 51, -1, -1, sizeof(::carbon::frontend::translation::AreaValue)},
  { 62, -1, -1, sizeof(::carbon::frontend::translation::DurationValue)},
  { 73, -1, -1, sizeof(::carbon::frontend::translation::DistanceValue)},
  { 87, -1, -1, sizeof(::carbon::frontend::translation::SpeedValue)},
  { 96, -1, -1, sizeof(::carbon::frontend::translation::TranslationParameter)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_IntegerValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_DoubleValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_StringValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_TemperatureValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_PercentValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_VoltageValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_FrequencyValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_AreaValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_DurationValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_DistanceValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_SpeedValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::translation::_TranslationParameter_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2ftranslation_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n frontend/proto/translation.proto\022\033carb"
  "on.frontend.translation\"\035\n\014IntegerValue\022"
  "\r\n\005value\030\001 \001(\003\"\034\n\013DoubleValue\022\r\n\005value\030\001"
  " \001(\001\"\034\n\013StringValue\022\r\n\005value\030\001 \001(\t\"D\n\020Te"
  "mperatureValue\022\021\n\007celcius\030\001 \001(\001H\000\022\024\n\nfah"
  "renheit\030\002 \001(\001H\000B\007\n\005value\"\037\n\014PercentValue"
  "\022\017\n\007percent\030\001 \001(\r\"\035\n\014VoltageValue\022\r\n\005vol"
  "ts\030\001 \001(\001\"\037\n\016FrequencyValue\022\r\n\005hertz\030\001 \001("
  "\001\"i\n\tAreaValue\022\017\n\005acres\030\001 \001(\001H\000\022\022\n\010hecta"
  "res\030\002 \001(\001H\000\022\025\n\013square_feet\030\003 \001(\001H\000\022\027\n\rsq"
  "uare_meters\030\004 \001(\001H\000B\007\n\005value\"g\n\rDuration"
  "Value\022\026\n\014milliseconds\030\001 \001(\004H\000\022\021\n\007seconds"
  "\030\002 \001(\004H\000\022\021\n\007minutes\030\003 \001(\004H\000\022\017\n\005hours\030\004 \001"
  "(\004H\000B\007\n\005value\"\241\001\n\rDistanceValue\022\025\n\013milli"
  "meters\030\001 \001(\001H\000\022\020\n\006meters\030\002 \001(\001H\000\022\024\n\nkilo"
  "meters\030\003 \001(\001H\000\022\020\n\006inches\030\004 \001(\001H\000\022\016\n\004feet"
  "\030\005 \001(\001H\000\022\017\n\005miles\030\006 \001(\001H\000\022\025\n\013centimeters"
  "\030\007 \001(\001H\000B\007\n\005value\"N\n\nSpeedValue\022\035\n\023kilom"
  "eters_per_hour\030\001 \001(\001H\000\022\030\n\016miles_per_hour"
  "\030\002 \001(\001H\000B\007\n\005value\"\227\006\n\024TranslationParamet"
  "er\022\014\n\004name\030\001 \001(\t\022>\n\tint_value\030\002 \001(\0132).ca"
  "rbon.frontend.translation.IntegerValueH\000"
  "\022@\n\014double_value\030\003 \001(\0132(.carbon.frontend"
  ".translation.DoubleValueH\000\022@\n\014string_val"
  "ue\030\004 \001(\0132(.carbon.frontend.translation.S"
  "tringValueH\000\022J\n\021temperature_value\030\005 \001(\0132"
  "-.carbon.frontend.translation.Temperatur"
  "eValueH\000\022B\n\rpercent_value\030\006 \001(\0132).carbon"
  ".frontend.translation.PercentValueH\000\022B\n\r"
  "voltage_value\030\007 \001(\0132).carbon.frontend.tr"
  "anslation.VoltageValueH\000\022F\n\017frequency_va"
  "lue\030\010 \001(\0132+.carbon.frontend.translation."
  "FrequencyValueH\000\022<\n\narea_value\030\t \001(\0132&.c"
  "arbon.frontend.translation.AreaValueH\000\022D"
  "\n\016duration_value\030\n \001(\0132*.carbon.frontend"
  ".translation.DurationValueH\000\022D\n\016distance"
  "_value\030\013 \001(\0132*.carbon.frontend.translati"
  "on.DistanceValueH\000\022>\n\013speed_value\030\014 \001(\0132"
  "\'.carbon.frontend.translation.SpeedValue"
  "H\000B\007\n\005valueB\020Z\016proto/frontendb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2ftranslation_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ftranslation_2eproto = {
  false, false, 1597, descriptor_table_protodef_frontend_2fproto_2ftranslation_2eproto, "frontend/proto/translation.proto", 
  &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once, nullptr, 0, 12,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2ftranslation_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2ftranslation_2eproto, file_level_enum_descriptors_frontend_2fproto_2ftranslation_2eproto, file_level_service_descriptors_frontend_2fproto_2ftranslation_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2ftranslation_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2ftranslation_2eproto(&descriptor_table_frontend_2fproto_2ftranslation_2eproto);
namespace carbon {
namespace frontend {
namespace translation {

// ===================================================================

class IntegerValue::_Internal {
 public:
};

IntegerValue::IntegerValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.IntegerValue)
}
IntegerValue::IntegerValue(const IntegerValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.IntegerValue)
}

inline void IntegerValue::SharedCtor() {
value_ = int64_t{0};
}

IntegerValue::~IntegerValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.IntegerValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void IntegerValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void IntegerValue::ArenaDtor(void* object) {
  IntegerValue* _this = reinterpret_cast< IntegerValue* >(object);
  (void)_this;
}
void IntegerValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void IntegerValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IntegerValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.IntegerValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IntegerValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          value_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IntegerValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.IntegerValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 value = 1;
  if (this->_internal_value() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.IntegerValue)
  return target;
}

size_t IntegerValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.IntegerValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 value = 1;
  if (this->_internal_value() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IntegerValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    IntegerValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IntegerValue::GetClassData() const { return &_class_data_; }

void IntegerValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<IntegerValue *>(to)->MergeFrom(
      static_cast<const IntegerValue &>(from));
}


void IntegerValue::MergeFrom(const IntegerValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.IntegerValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_value() != 0) {
    _internal_set_value(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IntegerValue::CopyFrom(const IntegerValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.IntegerValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IntegerValue::IsInitialized() const {
  return true;
}

void IntegerValue::InternalSwap(IntegerValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata IntegerValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[0]);
}

// ===================================================================

class DoubleValue::_Internal {
 public:
};

DoubleValue::DoubleValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.DoubleValue)
}
DoubleValue::DoubleValue(const DoubleValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  value_ = from.value_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.DoubleValue)
}

inline void DoubleValue::SharedCtor() {
value_ = 0;
}

DoubleValue::~DoubleValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.DoubleValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DoubleValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DoubleValue::ArenaDtor(void* object) {
  DoubleValue* _this = reinterpret_cast< DoubleValue* >(object);
  (void)_this;
}
void DoubleValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DoubleValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DoubleValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.DoubleValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DoubleValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          value_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DoubleValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.DoubleValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double value = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_value = this->_internal_value();
  uint64_t raw_value;
  memcpy(&raw_value, &tmp_value, sizeof(tmp_value));
  if (raw_value != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.DoubleValue)
  return target;
}

size_t DoubleValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.DoubleValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double value = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_value = this->_internal_value();
  uint64_t raw_value;
  memcpy(&raw_value, &tmp_value, sizeof(tmp_value));
  if (raw_value != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DoubleValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DoubleValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DoubleValue::GetClassData() const { return &_class_data_; }

void DoubleValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DoubleValue *>(to)->MergeFrom(
      static_cast<const DoubleValue &>(from));
}


void DoubleValue::MergeFrom(const DoubleValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.DoubleValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_value = from._internal_value();
  uint64_t raw_value;
  memcpy(&raw_value, &tmp_value, sizeof(tmp_value));
  if (raw_value != 0) {
    _internal_set_value(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DoubleValue::CopyFrom(const DoubleValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.DoubleValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DoubleValue::IsInitialized() const {
  return true;
}

void DoubleValue::InternalSwap(DoubleValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DoubleValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[1]);
}

// ===================================================================

class StringValue::_Internal {
 public:
};

StringValue::StringValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.StringValue)
}
StringValue::StringValue(const StringValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_value().empty()) {
    value_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_value(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.StringValue)
}

inline void StringValue::SharedCtor() {
value_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  value_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

StringValue::~StringValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.StringValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StringValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  value_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StringValue::ArenaDtor(void* object) {
  StringValue* _this = reinterpret_cast< StringValue* >(object);
  (void)_this;
}
void StringValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StringValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StringValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.StringValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  value_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StringValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string value = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_value();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.translation.StringValue.value"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StringValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.StringValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string value = 1;
  if (!this->_internal_value().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_value().data(), static_cast<int>(this->_internal_value().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.translation.StringValue.value");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_value(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.StringValue)
  return target;
}

size_t StringValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.StringValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string value = 1;
  if (!this->_internal_value().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_value());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StringValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StringValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StringValue::GetClassData() const { return &_class_data_; }

void StringValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StringValue *>(to)->MergeFrom(
      static_cast<const StringValue &>(from));
}


void StringValue::MergeFrom(const StringValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.StringValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_value().empty()) {
    _internal_set_value(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StringValue::CopyFrom(const StringValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.StringValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StringValue::IsInitialized() const {
  return true;
}

void StringValue::InternalSwap(StringValue* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &value_, lhs_arena,
      &other->value_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata StringValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[2]);
}

// ===================================================================

class TemperatureValue::_Internal {
 public:
};

TemperatureValue::TemperatureValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.TemperatureValue)
}
TemperatureValue::TemperatureValue(const TemperatureValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_value();
  switch (from.value_case()) {
    case kCelcius: {
      _internal_set_celcius(from._internal_celcius());
      break;
    }
    case kFahrenheit: {
      _internal_set_fahrenheit(from._internal_fahrenheit());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.TemperatureValue)
}

inline void TemperatureValue::SharedCtor() {
clear_has_value();
}

TemperatureValue::~TemperatureValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.TemperatureValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TemperatureValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_value()) {
    clear_value();
  }
}

void TemperatureValue::ArenaDtor(void* object) {
  TemperatureValue* _this = reinterpret_cast< TemperatureValue* >(object);
  (void)_this;
}
void TemperatureValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TemperatureValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TemperatureValue::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:carbon.frontend.translation.TemperatureValue)
  switch (value_case()) {
    case kCelcius: {
      // No need to clear
      break;
    }
    case kFahrenheit: {
      // No need to clear
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void TemperatureValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.TemperatureValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TemperatureValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double celcius = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _internal_set_celcius(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double fahrenheit = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _internal_set_fahrenheit(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TemperatureValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.TemperatureValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double celcius = 1;
  if (_internal_has_celcius()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_celcius(), target);
  }

  // double fahrenheit = 2;
  if (_internal_has_fahrenheit()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_fahrenheit(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.TemperatureValue)
  return target;
}

size_t TemperatureValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.TemperatureValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (value_case()) {
    // double celcius = 1;
    case kCelcius: {
      total_size += 1 + 8;
      break;
    }
    // double fahrenheit = 2;
    case kFahrenheit: {
      total_size += 1 + 8;
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TemperatureValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TemperatureValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TemperatureValue::GetClassData() const { return &_class_data_; }

void TemperatureValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TemperatureValue *>(to)->MergeFrom(
      static_cast<const TemperatureValue &>(from));
}


void TemperatureValue::MergeFrom(const TemperatureValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.TemperatureValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.value_case()) {
    case kCelcius: {
      _internal_set_celcius(from._internal_celcius());
      break;
    }
    case kFahrenheit: {
      _internal_set_fahrenheit(from._internal_fahrenheit());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TemperatureValue::CopyFrom(const TemperatureValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.TemperatureValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TemperatureValue::IsInitialized() const {
  return true;
}

void TemperatureValue::InternalSwap(TemperatureValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata TemperatureValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[3]);
}

// ===================================================================

class PercentValue::_Internal {
 public:
};

PercentValue::PercentValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.PercentValue)
}
PercentValue::PercentValue(const PercentValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  percent_ = from.percent_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.PercentValue)
}

inline void PercentValue::SharedCtor() {
percent_ = 0u;
}

PercentValue::~PercentValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.PercentValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PercentValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PercentValue::ArenaDtor(void* object) {
  PercentValue* _this = reinterpret_cast< PercentValue* >(object);
  (void)_this;
}
void PercentValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PercentValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PercentValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.PercentValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  percent_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PercentValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 percent = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          percent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PercentValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.PercentValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 percent = 1;
  if (this->_internal_percent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_percent(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.PercentValue)
  return target;
}

size_t PercentValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.PercentValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 percent = 1;
  if (this->_internal_percent() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_percent());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PercentValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PercentValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PercentValue::GetClassData() const { return &_class_data_; }

void PercentValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PercentValue *>(to)->MergeFrom(
      static_cast<const PercentValue &>(from));
}


void PercentValue::MergeFrom(const PercentValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.PercentValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_percent() != 0) {
    _internal_set_percent(from._internal_percent());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PercentValue::CopyFrom(const PercentValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.PercentValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PercentValue::IsInitialized() const {
  return true;
}

void PercentValue::InternalSwap(PercentValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(percent_, other->percent_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PercentValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[4]);
}

// ===================================================================

class VoltageValue::_Internal {
 public:
};

VoltageValue::VoltageValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.VoltageValue)
}
VoltageValue::VoltageValue(const VoltageValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  volts_ = from.volts_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.VoltageValue)
}

inline void VoltageValue::SharedCtor() {
volts_ = 0;
}

VoltageValue::~VoltageValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.VoltageValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void VoltageValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void VoltageValue::ArenaDtor(void* object) {
  VoltageValue* _this = reinterpret_cast< VoltageValue* >(object);
  (void)_this;
}
void VoltageValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void VoltageValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void VoltageValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.VoltageValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  volts_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* VoltageValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double volts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          volts_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* VoltageValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.VoltageValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double volts = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_volts = this->_internal_volts();
  uint64_t raw_volts;
  memcpy(&raw_volts, &tmp_volts, sizeof(tmp_volts));
  if (raw_volts != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_volts(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.VoltageValue)
  return target;
}

size_t VoltageValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.VoltageValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double volts = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_volts = this->_internal_volts();
  uint64_t raw_volts;
  memcpy(&raw_volts, &tmp_volts, sizeof(tmp_volts));
  if (raw_volts != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData VoltageValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    VoltageValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*VoltageValue::GetClassData() const { return &_class_data_; }

void VoltageValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<VoltageValue *>(to)->MergeFrom(
      static_cast<const VoltageValue &>(from));
}


void VoltageValue::MergeFrom(const VoltageValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.VoltageValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_volts = from._internal_volts();
  uint64_t raw_volts;
  memcpy(&raw_volts, &tmp_volts, sizeof(tmp_volts));
  if (raw_volts != 0) {
    _internal_set_volts(from._internal_volts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void VoltageValue::CopyFrom(const VoltageValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.VoltageValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool VoltageValue::IsInitialized() const {
  return true;
}

void VoltageValue::InternalSwap(VoltageValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(volts_, other->volts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata VoltageValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[5]);
}

// ===================================================================

class FrequencyValue::_Internal {
 public:
};

FrequencyValue::FrequencyValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.FrequencyValue)
}
FrequencyValue::FrequencyValue(const FrequencyValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  hertz_ = from.hertz_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.FrequencyValue)
}

inline void FrequencyValue::SharedCtor() {
hertz_ = 0;
}

FrequencyValue::~FrequencyValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.FrequencyValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FrequencyValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void FrequencyValue::ArenaDtor(void* object) {
  FrequencyValue* _this = reinterpret_cast< FrequencyValue* >(object);
  (void)_this;
}
void FrequencyValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FrequencyValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FrequencyValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.FrequencyValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  hertz_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FrequencyValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double hertz = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          hertz_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FrequencyValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.FrequencyValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double hertz = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_hertz = this->_internal_hertz();
  uint64_t raw_hertz;
  memcpy(&raw_hertz, &tmp_hertz, sizeof(tmp_hertz));
  if (raw_hertz != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_hertz(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.FrequencyValue)
  return target;
}

size_t FrequencyValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.FrequencyValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double hertz = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_hertz = this->_internal_hertz();
  uint64_t raw_hertz;
  memcpy(&raw_hertz, &tmp_hertz, sizeof(tmp_hertz));
  if (raw_hertz != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FrequencyValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FrequencyValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FrequencyValue::GetClassData() const { return &_class_data_; }

void FrequencyValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FrequencyValue *>(to)->MergeFrom(
      static_cast<const FrequencyValue &>(from));
}


void FrequencyValue::MergeFrom(const FrequencyValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.FrequencyValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_hertz = from._internal_hertz();
  uint64_t raw_hertz;
  memcpy(&raw_hertz, &tmp_hertz, sizeof(tmp_hertz));
  if (raw_hertz != 0) {
    _internal_set_hertz(from._internal_hertz());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FrequencyValue::CopyFrom(const FrequencyValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.FrequencyValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FrequencyValue::IsInitialized() const {
  return true;
}

void FrequencyValue::InternalSwap(FrequencyValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(hertz_, other->hertz_);
}

::PROTOBUF_NAMESPACE_ID::Metadata FrequencyValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[6]);
}

// ===================================================================

class AreaValue::_Internal {
 public:
};

AreaValue::AreaValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.AreaValue)
}
AreaValue::AreaValue(const AreaValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_value();
  switch (from.value_case()) {
    case kAcres: {
      _internal_set_acres(from._internal_acres());
      break;
    }
    case kHectares: {
      _internal_set_hectares(from._internal_hectares());
      break;
    }
    case kSquareFeet: {
      _internal_set_square_feet(from._internal_square_feet());
      break;
    }
    case kSquareMeters: {
      _internal_set_square_meters(from._internal_square_meters());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.AreaValue)
}

inline void AreaValue::SharedCtor() {
clear_has_value();
}

AreaValue::~AreaValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.AreaValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AreaValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_value()) {
    clear_value();
  }
}

void AreaValue::ArenaDtor(void* object) {
  AreaValue* _this = reinterpret_cast< AreaValue* >(object);
  (void)_this;
}
void AreaValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AreaValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AreaValue::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:carbon.frontend.translation.AreaValue)
  switch (value_case()) {
    case kAcres: {
      // No need to clear
      break;
    }
    case kHectares: {
      // No need to clear
      break;
    }
    case kSquareFeet: {
      // No need to clear
      break;
    }
    case kSquareMeters: {
      // No need to clear
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void AreaValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.AreaValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AreaValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double acres = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _internal_set_acres(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double hectares = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _internal_set_hectares(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double square_feet = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          _internal_set_square_feet(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double square_meters = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _internal_set_square_meters(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AreaValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.AreaValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double acres = 1;
  if (_internal_has_acres()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_acres(), target);
  }

  // double hectares = 2;
  if (_internal_has_hectares()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_hectares(), target);
  }

  // double square_feet = 3;
  if (_internal_has_square_feet()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_square_feet(), target);
  }

  // double square_meters = 4;
  if (_internal_has_square_meters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_square_meters(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.AreaValue)
  return target;
}

size_t AreaValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.AreaValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (value_case()) {
    // double acres = 1;
    case kAcres: {
      total_size += 1 + 8;
      break;
    }
    // double hectares = 2;
    case kHectares: {
      total_size += 1 + 8;
      break;
    }
    // double square_feet = 3;
    case kSquareFeet: {
      total_size += 1 + 8;
      break;
    }
    // double square_meters = 4;
    case kSquareMeters: {
      total_size += 1 + 8;
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AreaValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AreaValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AreaValue::GetClassData() const { return &_class_data_; }

void AreaValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AreaValue *>(to)->MergeFrom(
      static_cast<const AreaValue &>(from));
}


void AreaValue::MergeFrom(const AreaValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.AreaValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.value_case()) {
    case kAcres: {
      _internal_set_acres(from._internal_acres());
      break;
    }
    case kHectares: {
      _internal_set_hectares(from._internal_hectares());
      break;
    }
    case kSquareFeet: {
      _internal_set_square_feet(from._internal_square_feet());
      break;
    }
    case kSquareMeters: {
      _internal_set_square_meters(from._internal_square_meters());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AreaValue::CopyFrom(const AreaValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.AreaValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AreaValue::IsInitialized() const {
  return true;
}

void AreaValue::InternalSwap(AreaValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata AreaValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[7]);
}

// ===================================================================

class DurationValue::_Internal {
 public:
};

DurationValue::DurationValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.DurationValue)
}
DurationValue::DurationValue(const DurationValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_value();
  switch (from.value_case()) {
    case kMilliseconds: {
      _internal_set_milliseconds(from._internal_milliseconds());
      break;
    }
    case kSeconds: {
      _internal_set_seconds(from._internal_seconds());
      break;
    }
    case kMinutes: {
      _internal_set_minutes(from._internal_minutes());
      break;
    }
    case kHours: {
      _internal_set_hours(from._internal_hours());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.DurationValue)
}

inline void DurationValue::SharedCtor() {
clear_has_value();
}

DurationValue::~DurationValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.DurationValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DurationValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_value()) {
    clear_value();
  }
}

void DurationValue::ArenaDtor(void* object) {
  DurationValue* _this = reinterpret_cast< DurationValue* >(object);
  (void)_this;
}
void DurationValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DurationValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DurationValue::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:carbon.frontend.translation.DurationValue)
  switch (value_case()) {
    case kMilliseconds: {
      // No need to clear
      break;
    }
    case kSeconds: {
      // No need to clear
      break;
    }
    case kMinutes: {
      // No need to clear
      break;
    }
    case kHours: {
      // No need to clear
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void DurationValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.DurationValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DurationValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 milliseconds = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _internal_set_milliseconds(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 seconds = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _internal_set_seconds(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 minutes = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _internal_set_minutes(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 hours = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _internal_set_hours(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DurationValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.DurationValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 milliseconds = 1;
  if (_internal_has_milliseconds()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_milliseconds(), target);
  }

  // uint64 seconds = 2;
  if (_internal_has_seconds()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_seconds(), target);
  }

  // uint64 minutes = 3;
  if (_internal_has_minutes()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_minutes(), target);
  }

  // uint64 hours = 4;
  if (_internal_has_hours()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_hours(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.DurationValue)
  return target;
}

size_t DurationValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.DurationValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (value_case()) {
    // uint64 milliseconds = 1;
    case kMilliseconds: {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_milliseconds());
      break;
    }
    // uint64 seconds = 2;
    case kSeconds: {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_seconds());
      break;
    }
    // uint64 minutes = 3;
    case kMinutes: {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_minutes());
      break;
    }
    // uint64 hours = 4;
    case kHours: {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_hours());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DurationValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DurationValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DurationValue::GetClassData() const { return &_class_data_; }

void DurationValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DurationValue *>(to)->MergeFrom(
      static_cast<const DurationValue &>(from));
}


void DurationValue::MergeFrom(const DurationValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.DurationValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.value_case()) {
    case kMilliseconds: {
      _internal_set_milliseconds(from._internal_milliseconds());
      break;
    }
    case kSeconds: {
      _internal_set_seconds(from._internal_seconds());
      break;
    }
    case kMinutes: {
      _internal_set_minutes(from._internal_minutes());
      break;
    }
    case kHours: {
      _internal_set_hours(from._internal_hours());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DurationValue::CopyFrom(const DurationValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.DurationValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DurationValue::IsInitialized() const {
  return true;
}

void DurationValue::InternalSwap(DurationValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata DurationValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[8]);
}

// ===================================================================

class DistanceValue::_Internal {
 public:
};

DistanceValue::DistanceValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.DistanceValue)
}
DistanceValue::DistanceValue(const DistanceValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_value();
  switch (from.value_case()) {
    case kMillimeters: {
      _internal_set_millimeters(from._internal_millimeters());
      break;
    }
    case kMeters: {
      _internal_set_meters(from._internal_meters());
      break;
    }
    case kKilometers: {
      _internal_set_kilometers(from._internal_kilometers());
      break;
    }
    case kInches: {
      _internal_set_inches(from._internal_inches());
      break;
    }
    case kFeet: {
      _internal_set_feet(from._internal_feet());
      break;
    }
    case kMiles: {
      _internal_set_miles(from._internal_miles());
      break;
    }
    case kCentimeters: {
      _internal_set_centimeters(from._internal_centimeters());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.DistanceValue)
}

inline void DistanceValue::SharedCtor() {
clear_has_value();
}

DistanceValue::~DistanceValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.DistanceValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DistanceValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_value()) {
    clear_value();
  }
}

void DistanceValue::ArenaDtor(void* object) {
  DistanceValue* _this = reinterpret_cast< DistanceValue* >(object);
  (void)_this;
}
void DistanceValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DistanceValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DistanceValue::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:carbon.frontend.translation.DistanceValue)
  switch (value_case()) {
    case kMillimeters: {
      // No need to clear
      break;
    }
    case kMeters: {
      // No need to clear
      break;
    }
    case kKilometers: {
      // No need to clear
      break;
    }
    case kInches: {
      // No need to clear
      break;
    }
    case kFeet: {
      // No need to clear
      break;
    }
    case kMiles: {
      // No need to clear
      break;
    }
    case kCentimeters: {
      // No need to clear
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void DistanceValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.DistanceValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DistanceValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double millimeters = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _internal_set_millimeters(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double meters = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _internal_set_meters(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double kilometers = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          _internal_set_kilometers(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double inches = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _internal_set_inches(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double feet = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 41)) {
          _internal_set_feet(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double miles = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 49)) {
          _internal_set_miles(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double centimeters = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 57)) {
          _internal_set_centimeters(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DistanceValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.DistanceValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double millimeters = 1;
  if (_internal_has_millimeters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_millimeters(), target);
  }

  // double meters = 2;
  if (_internal_has_meters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_meters(), target);
  }

  // double kilometers = 3;
  if (_internal_has_kilometers()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_kilometers(), target);
  }

  // double inches = 4;
  if (_internal_has_inches()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_inches(), target);
  }

  // double feet = 5;
  if (_internal_has_feet()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_feet(), target);
  }

  // double miles = 6;
  if (_internal_has_miles()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(6, this->_internal_miles(), target);
  }

  // double centimeters = 7;
  if (_internal_has_centimeters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(7, this->_internal_centimeters(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.DistanceValue)
  return target;
}

size_t DistanceValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.DistanceValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (value_case()) {
    // double millimeters = 1;
    case kMillimeters: {
      total_size += 1 + 8;
      break;
    }
    // double meters = 2;
    case kMeters: {
      total_size += 1 + 8;
      break;
    }
    // double kilometers = 3;
    case kKilometers: {
      total_size += 1 + 8;
      break;
    }
    // double inches = 4;
    case kInches: {
      total_size += 1 + 8;
      break;
    }
    // double feet = 5;
    case kFeet: {
      total_size += 1 + 8;
      break;
    }
    // double miles = 6;
    case kMiles: {
      total_size += 1 + 8;
      break;
    }
    // double centimeters = 7;
    case kCentimeters: {
      total_size += 1 + 8;
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DistanceValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DistanceValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DistanceValue::GetClassData() const { return &_class_data_; }

void DistanceValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DistanceValue *>(to)->MergeFrom(
      static_cast<const DistanceValue &>(from));
}


void DistanceValue::MergeFrom(const DistanceValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.DistanceValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.value_case()) {
    case kMillimeters: {
      _internal_set_millimeters(from._internal_millimeters());
      break;
    }
    case kMeters: {
      _internal_set_meters(from._internal_meters());
      break;
    }
    case kKilometers: {
      _internal_set_kilometers(from._internal_kilometers());
      break;
    }
    case kInches: {
      _internal_set_inches(from._internal_inches());
      break;
    }
    case kFeet: {
      _internal_set_feet(from._internal_feet());
      break;
    }
    case kMiles: {
      _internal_set_miles(from._internal_miles());
      break;
    }
    case kCentimeters: {
      _internal_set_centimeters(from._internal_centimeters());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DistanceValue::CopyFrom(const DistanceValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.DistanceValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DistanceValue::IsInitialized() const {
  return true;
}

void DistanceValue::InternalSwap(DistanceValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata DistanceValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[9]);
}

// ===================================================================

class SpeedValue::_Internal {
 public:
};

SpeedValue::SpeedValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.SpeedValue)
}
SpeedValue::SpeedValue(const SpeedValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  clear_has_value();
  switch (from.value_case()) {
    case kKilometersPerHour: {
      _internal_set_kilometers_per_hour(from._internal_kilometers_per_hour());
      break;
    }
    case kMilesPerHour: {
      _internal_set_miles_per_hour(from._internal_miles_per_hour());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.SpeedValue)
}

inline void SpeedValue::SharedCtor() {
clear_has_value();
}

SpeedValue::~SpeedValue() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.SpeedValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SpeedValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_value()) {
    clear_value();
  }
}

void SpeedValue::ArenaDtor(void* object) {
  SpeedValue* _this = reinterpret_cast< SpeedValue* >(object);
  (void)_this;
}
void SpeedValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SpeedValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SpeedValue::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:carbon.frontend.translation.SpeedValue)
  switch (value_case()) {
    case kKilometersPerHour: {
      // No need to clear
      break;
    }
    case kMilesPerHour: {
      // No need to clear
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void SpeedValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.SpeedValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  clear_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SpeedValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double kilometers_per_hour = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          _internal_set_kilometers_per_hour(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double miles_per_hour = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          _internal_set_miles_per_hour(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SpeedValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.SpeedValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double kilometers_per_hour = 1;
  if (_internal_has_kilometers_per_hour()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_kilometers_per_hour(), target);
  }

  // double miles_per_hour = 2;
  if (_internal_has_miles_per_hour()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_miles_per_hour(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.SpeedValue)
  return target;
}

size_t SpeedValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.SpeedValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  switch (value_case()) {
    // double kilometers_per_hour = 1;
    case kKilometersPerHour: {
      total_size += 1 + 8;
      break;
    }
    // double miles_per_hour = 2;
    case kMilesPerHour: {
      total_size += 1 + 8;
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SpeedValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SpeedValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SpeedValue::GetClassData() const { return &_class_data_; }

void SpeedValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SpeedValue *>(to)->MergeFrom(
      static_cast<const SpeedValue &>(from));
}


void SpeedValue::MergeFrom(const SpeedValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.SpeedValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  switch (from.value_case()) {
    case kKilometersPerHour: {
      _internal_set_kilometers_per_hour(from._internal_kilometers_per_hour());
      break;
    }
    case kMilesPerHour: {
      _internal_set_miles_per_hour(from._internal_miles_per_hour());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SpeedValue::CopyFrom(const SpeedValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.SpeedValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SpeedValue::IsInitialized() const {
  return true;
}

void SpeedValue::InternalSwap(SpeedValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata SpeedValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[10]);
}

// ===================================================================

class TranslationParameter::_Internal {
 public:
  static const ::carbon::frontend::translation::IntegerValue& int_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::DoubleValue& double_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::StringValue& string_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::TemperatureValue& temperature_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::PercentValue& percent_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::VoltageValue& voltage_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::FrequencyValue& frequency_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::AreaValue& area_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::DurationValue& duration_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::DistanceValue& distance_value(const TranslationParameter* msg);
  static const ::carbon::frontend::translation::SpeedValue& speed_value(const TranslationParameter* msg);
};

const ::carbon::frontend::translation::IntegerValue&
TranslationParameter::_Internal::int_value(const TranslationParameter* msg) {
  return *msg->value_.int_value_;
}
const ::carbon::frontend::translation::DoubleValue&
TranslationParameter::_Internal::double_value(const TranslationParameter* msg) {
  return *msg->value_.double_value_;
}
const ::carbon::frontend::translation::StringValue&
TranslationParameter::_Internal::string_value(const TranslationParameter* msg) {
  return *msg->value_.string_value_;
}
const ::carbon::frontend::translation::TemperatureValue&
TranslationParameter::_Internal::temperature_value(const TranslationParameter* msg) {
  return *msg->value_.temperature_value_;
}
const ::carbon::frontend::translation::PercentValue&
TranslationParameter::_Internal::percent_value(const TranslationParameter* msg) {
  return *msg->value_.percent_value_;
}
const ::carbon::frontend::translation::VoltageValue&
TranslationParameter::_Internal::voltage_value(const TranslationParameter* msg) {
  return *msg->value_.voltage_value_;
}
const ::carbon::frontend::translation::FrequencyValue&
TranslationParameter::_Internal::frequency_value(const TranslationParameter* msg) {
  return *msg->value_.frequency_value_;
}
const ::carbon::frontend::translation::AreaValue&
TranslationParameter::_Internal::area_value(const TranslationParameter* msg) {
  return *msg->value_.area_value_;
}
const ::carbon::frontend::translation::DurationValue&
TranslationParameter::_Internal::duration_value(const TranslationParameter* msg) {
  return *msg->value_.duration_value_;
}
const ::carbon::frontend::translation::DistanceValue&
TranslationParameter::_Internal::distance_value(const TranslationParameter* msg) {
  return *msg->value_.distance_value_;
}
const ::carbon::frontend::translation::SpeedValue&
TranslationParameter::_Internal::speed_value(const TranslationParameter* msg) {
  return *msg->value_.speed_value_;
}
void TranslationParameter::set_allocated_int_value(::carbon::frontend::translation::IntegerValue* int_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (int_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::IntegerValue>::GetOwningArena(int_value);
    if (message_arena != submessage_arena) {
      int_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, int_value, submessage_arena);
    }
    set_has_int_value();
    value_.int_value_ = int_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.int_value)
}
void TranslationParameter::set_allocated_double_value(::carbon::frontend::translation::DoubleValue* double_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (double_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::DoubleValue>::GetOwningArena(double_value);
    if (message_arena != submessage_arena) {
      double_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, double_value, submessage_arena);
    }
    set_has_double_value();
    value_.double_value_ = double_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.double_value)
}
void TranslationParameter::set_allocated_string_value(::carbon::frontend::translation::StringValue* string_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (string_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::StringValue>::GetOwningArena(string_value);
    if (message_arena != submessage_arena) {
      string_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, string_value, submessage_arena);
    }
    set_has_string_value();
    value_.string_value_ = string_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.string_value)
}
void TranslationParameter::set_allocated_temperature_value(::carbon::frontend::translation::TemperatureValue* temperature_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (temperature_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::TemperatureValue>::GetOwningArena(temperature_value);
    if (message_arena != submessage_arena) {
      temperature_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, temperature_value, submessage_arena);
    }
    set_has_temperature_value();
    value_.temperature_value_ = temperature_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.temperature_value)
}
void TranslationParameter::set_allocated_percent_value(::carbon::frontend::translation::PercentValue* percent_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (percent_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::PercentValue>::GetOwningArena(percent_value);
    if (message_arena != submessage_arena) {
      percent_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, percent_value, submessage_arena);
    }
    set_has_percent_value();
    value_.percent_value_ = percent_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.percent_value)
}
void TranslationParameter::set_allocated_voltage_value(::carbon::frontend::translation::VoltageValue* voltage_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (voltage_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::VoltageValue>::GetOwningArena(voltage_value);
    if (message_arena != submessage_arena) {
      voltage_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, voltage_value, submessage_arena);
    }
    set_has_voltage_value();
    value_.voltage_value_ = voltage_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.voltage_value)
}
void TranslationParameter::set_allocated_frequency_value(::carbon::frontend::translation::FrequencyValue* frequency_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (frequency_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::FrequencyValue>::GetOwningArena(frequency_value);
    if (message_arena != submessage_arena) {
      frequency_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, frequency_value, submessage_arena);
    }
    set_has_frequency_value();
    value_.frequency_value_ = frequency_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.frequency_value)
}
void TranslationParameter::set_allocated_area_value(::carbon::frontend::translation::AreaValue* area_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (area_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::AreaValue>::GetOwningArena(area_value);
    if (message_arena != submessage_arena) {
      area_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, area_value, submessage_arena);
    }
    set_has_area_value();
    value_.area_value_ = area_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.area_value)
}
void TranslationParameter::set_allocated_duration_value(::carbon::frontend::translation::DurationValue* duration_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (duration_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::DurationValue>::GetOwningArena(duration_value);
    if (message_arena != submessage_arena) {
      duration_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, duration_value, submessage_arena);
    }
    set_has_duration_value();
    value_.duration_value_ = duration_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.duration_value)
}
void TranslationParameter::set_allocated_distance_value(::carbon::frontend::translation::DistanceValue* distance_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (distance_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::DistanceValue>::GetOwningArena(distance_value);
    if (message_arena != submessage_arena) {
      distance_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, distance_value, submessage_arena);
    }
    set_has_distance_value();
    value_.distance_value_ = distance_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.distance_value)
}
void TranslationParameter::set_allocated_speed_value(::carbon::frontend::translation::SpeedValue* speed_value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_value();
  if (speed_value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::translation::SpeedValue>::GetOwningArena(speed_value);
    if (message_arena != submessage_arena) {
      speed_value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, speed_value, submessage_arena);
    }
    set_has_speed_value();
    value_.speed_value_ = speed_value;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.translation.TranslationParameter.speed_value)
}
TranslationParameter::TranslationParameter(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.translation.TranslationParameter)
}
TranslationParameter::TranslationParameter(const TranslationParameter& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  clear_has_value();
  switch (from.value_case()) {
    case kIntValue: {
      _internal_mutable_int_value()->::carbon::frontend::translation::IntegerValue::MergeFrom(from._internal_int_value());
      break;
    }
    case kDoubleValue: {
      _internal_mutable_double_value()->::carbon::frontend::translation::DoubleValue::MergeFrom(from._internal_double_value());
      break;
    }
    case kStringValue: {
      _internal_mutable_string_value()->::carbon::frontend::translation::StringValue::MergeFrom(from._internal_string_value());
      break;
    }
    case kTemperatureValue: {
      _internal_mutable_temperature_value()->::carbon::frontend::translation::TemperatureValue::MergeFrom(from._internal_temperature_value());
      break;
    }
    case kPercentValue: {
      _internal_mutable_percent_value()->::carbon::frontend::translation::PercentValue::MergeFrom(from._internal_percent_value());
      break;
    }
    case kVoltageValue: {
      _internal_mutable_voltage_value()->::carbon::frontend::translation::VoltageValue::MergeFrom(from._internal_voltage_value());
      break;
    }
    case kFrequencyValue: {
      _internal_mutable_frequency_value()->::carbon::frontend::translation::FrequencyValue::MergeFrom(from._internal_frequency_value());
      break;
    }
    case kAreaValue: {
      _internal_mutable_area_value()->::carbon::frontend::translation::AreaValue::MergeFrom(from._internal_area_value());
      break;
    }
    case kDurationValue: {
      _internal_mutable_duration_value()->::carbon::frontend::translation::DurationValue::MergeFrom(from._internal_duration_value());
      break;
    }
    case kDistanceValue: {
      _internal_mutable_distance_value()->::carbon::frontend::translation::DistanceValue::MergeFrom(from._internal_distance_value());
      break;
    }
    case kSpeedValue: {
      _internal_mutable_speed_value()->::carbon::frontend::translation::SpeedValue::MergeFrom(from._internal_speed_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.translation.TranslationParameter)
}

inline void TranslationParameter::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
clear_has_value();
}

TranslationParameter::~TranslationParameter() {
  // @@protoc_insertion_point(destructor:carbon.frontend.translation.TranslationParameter)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TranslationParameter::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_value()) {
    clear_value();
  }
}

void TranslationParameter::ArenaDtor(void* object) {
  TranslationParameter* _this = reinterpret_cast< TranslationParameter* >(object);
  (void)_this;
}
void TranslationParameter::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TranslationParameter::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TranslationParameter::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:carbon.frontend.translation.TranslationParameter)
  switch (value_case()) {
    case kIntValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.int_value_;
      }
      break;
    }
    case kDoubleValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.double_value_;
      }
      break;
    }
    case kStringValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.string_value_;
      }
      break;
    }
    case kTemperatureValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.temperature_value_;
      }
      break;
    }
    case kPercentValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.percent_value_;
      }
      break;
    }
    case kVoltageValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.voltage_value_;
      }
      break;
    }
    case kFrequencyValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.frequency_value_;
      }
      break;
    }
    case kAreaValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.area_value_;
      }
      break;
    }
    case kDurationValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.duration_value_;
      }
      break;
    }
    case kDistanceValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.distance_value_;
      }
      break;
    }
    case kSpeedValue: {
      if (GetArenaForAllocation() == nullptr) {
        delete value_.speed_value_;
      }
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void TranslationParameter::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.translation.TranslationParameter)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  clear_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TranslationParameter::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.translation.TranslationParameter.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.IntegerValue int_value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_int_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.DoubleValue double_value = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_double_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.StringValue string_value = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_string_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.TemperatureValue temperature_value = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_temperature_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.PercentValue percent_value = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_percent_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.VoltageValue voltage_value = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_voltage_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.FrequencyValue frequency_value = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_frequency_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.AreaValue area_value = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ctx->ParseMessage(_internal_mutable_area_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.DurationValue duration_value = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_duration_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.DistanceValue distance_value = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_distance_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.SpeedValue speed_value = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          ptr = ctx->ParseMessage(_internal_mutable_speed_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TranslationParameter::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.translation.TranslationParameter)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.translation.TranslationParameter.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // .carbon.frontend.translation.IntegerValue int_value = 2;
  if (_internal_has_int_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::int_value(this), target, stream);
  }

  // .carbon.frontend.translation.DoubleValue double_value = 3;
  if (_internal_has_double_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::double_value(this), target, stream);
  }

  // .carbon.frontend.translation.StringValue string_value = 4;
  if (_internal_has_string_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::string_value(this), target, stream);
  }

  // .carbon.frontend.translation.TemperatureValue temperature_value = 5;
  if (_internal_has_temperature_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::temperature_value(this), target, stream);
  }

  // .carbon.frontend.translation.PercentValue percent_value = 6;
  if (_internal_has_percent_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::percent_value(this), target, stream);
  }

  // .carbon.frontend.translation.VoltageValue voltage_value = 7;
  if (_internal_has_voltage_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::voltage_value(this), target, stream);
  }

  // .carbon.frontend.translation.FrequencyValue frequency_value = 8;
  if (_internal_has_frequency_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::frequency_value(this), target, stream);
  }

  // .carbon.frontend.translation.AreaValue area_value = 9;
  if (_internal_has_area_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        9, _Internal::area_value(this), target, stream);
  }

  // .carbon.frontend.translation.DurationValue duration_value = 10;
  if (_internal_has_duration_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::duration_value(this), target, stream);
  }

  // .carbon.frontend.translation.DistanceValue distance_value = 11;
  if (_internal_has_distance_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::distance_value(this), target, stream);
  }

  // .carbon.frontend.translation.SpeedValue speed_value = 12;
  if (_internal_has_speed_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        12, _Internal::speed_value(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.translation.TranslationParameter)
  return target;
}

size_t TranslationParameter::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.translation.TranslationParameter)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  switch (value_case()) {
    // .carbon.frontend.translation.IntegerValue int_value = 2;
    case kIntValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.int_value_);
      break;
    }
    // .carbon.frontend.translation.DoubleValue double_value = 3;
    case kDoubleValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.double_value_);
      break;
    }
    // .carbon.frontend.translation.StringValue string_value = 4;
    case kStringValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.string_value_);
      break;
    }
    // .carbon.frontend.translation.TemperatureValue temperature_value = 5;
    case kTemperatureValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.temperature_value_);
      break;
    }
    // .carbon.frontend.translation.PercentValue percent_value = 6;
    case kPercentValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.percent_value_);
      break;
    }
    // .carbon.frontend.translation.VoltageValue voltage_value = 7;
    case kVoltageValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.voltage_value_);
      break;
    }
    // .carbon.frontend.translation.FrequencyValue frequency_value = 8;
    case kFrequencyValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.frequency_value_);
      break;
    }
    // .carbon.frontend.translation.AreaValue area_value = 9;
    case kAreaValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.area_value_);
      break;
    }
    // .carbon.frontend.translation.DurationValue duration_value = 10;
    case kDurationValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.duration_value_);
      break;
    }
    // .carbon.frontend.translation.DistanceValue distance_value = 11;
    case kDistanceValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.distance_value_);
      break;
    }
    // .carbon.frontend.translation.SpeedValue speed_value = 12;
    case kSpeedValue: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *value_.speed_value_);
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TranslationParameter::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TranslationParameter::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TranslationParameter::GetClassData() const { return &_class_data_; }

void TranslationParameter::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TranslationParameter *>(to)->MergeFrom(
      static_cast<const TranslationParameter &>(from));
}


void TranslationParameter::MergeFrom(const TranslationParameter& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.translation.TranslationParameter)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  switch (from.value_case()) {
    case kIntValue: {
      _internal_mutable_int_value()->::carbon::frontend::translation::IntegerValue::MergeFrom(from._internal_int_value());
      break;
    }
    case kDoubleValue: {
      _internal_mutable_double_value()->::carbon::frontend::translation::DoubleValue::MergeFrom(from._internal_double_value());
      break;
    }
    case kStringValue: {
      _internal_mutable_string_value()->::carbon::frontend::translation::StringValue::MergeFrom(from._internal_string_value());
      break;
    }
    case kTemperatureValue: {
      _internal_mutable_temperature_value()->::carbon::frontend::translation::TemperatureValue::MergeFrom(from._internal_temperature_value());
      break;
    }
    case kPercentValue: {
      _internal_mutable_percent_value()->::carbon::frontend::translation::PercentValue::MergeFrom(from._internal_percent_value());
      break;
    }
    case kVoltageValue: {
      _internal_mutable_voltage_value()->::carbon::frontend::translation::VoltageValue::MergeFrom(from._internal_voltage_value());
      break;
    }
    case kFrequencyValue: {
      _internal_mutable_frequency_value()->::carbon::frontend::translation::FrequencyValue::MergeFrom(from._internal_frequency_value());
      break;
    }
    case kAreaValue: {
      _internal_mutable_area_value()->::carbon::frontend::translation::AreaValue::MergeFrom(from._internal_area_value());
      break;
    }
    case kDurationValue: {
      _internal_mutable_duration_value()->::carbon::frontend::translation::DurationValue::MergeFrom(from._internal_duration_value());
      break;
    }
    case kDistanceValue: {
      _internal_mutable_distance_value()->::carbon::frontend::translation::DistanceValue::MergeFrom(from._internal_distance_value());
      break;
    }
    case kSpeedValue: {
      _internal_mutable_speed_value()->::carbon::frontend::translation::SpeedValue::MergeFrom(from._internal_speed_value());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TranslationParameter::CopyFrom(const TranslationParameter& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.translation.TranslationParameter)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TranslationParameter::IsInitialized() const {
  return true;
}

void TranslationParameter::InternalSwap(TranslationParameter* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata TranslationParameter::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftranslation_2eproto_getter, &descriptor_table_frontend_2fproto_2ftranslation_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftranslation_2eproto[11]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace translation
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::IntegerValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::IntegerValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::IntegerValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::DoubleValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::DoubleValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::DoubleValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::StringValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::StringValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::StringValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::TemperatureValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::TemperatureValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::TemperatureValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::PercentValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::PercentValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::PercentValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::VoltageValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::VoltageValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::VoltageValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::FrequencyValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::FrequencyValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::FrequencyValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::AreaValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::AreaValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::AreaValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::DurationValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::DurationValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::DurationValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::DistanceValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::DistanceValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::DistanceValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::SpeedValue* Arena::CreateMaybeMessage< ::carbon::frontend::translation::SpeedValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::SpeedValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::translation::TranslationParameter* Arena::CreateMaybeMessage< ::carbon::frontend::translation::TranslationParameter >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::translation::TranslationParameter >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

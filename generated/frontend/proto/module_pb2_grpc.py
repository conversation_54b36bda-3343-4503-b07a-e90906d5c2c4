# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import module_pb2 as frontend_dot_proto_dot_module__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class ModuleAssignmentServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextModulesList = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/GetNextModulesList',
                request_serializer=frontend_dot_proto_dot_module__pb2.GetNextModulesListRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_module__pb2.GetNextModulesListResponse.FromString,
                )
        self.GetNextActiveModules = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/GetNextActiveModules',
                request_serializer=frontend_dot_proto_dot_module__pb2.GetNextActiveModulesRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_module__pb2.GetNextActiveModulesResponse.FromString,
                )
        self.IdentifyModule = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/IdentifyModule',
                request_serializer=frontend_dot_proto_dot_module__pb2.IdentifyModuleRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.AssignModule = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/AssignModule',
                request_serializer=frontend_dot_proto_dot_module__pb2.AssignModuleRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.ClearModuleAssignment = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/ClearModuleAssignment',
                request_serializer=frontend_dot_proto_dot_module__pb2.ClearModuleAssignmentRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.SetModuleSerial = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/SetModuleSerial',
                request_serializer=frontend_dot_proto_dot_module__pb2.SetModuleSerialRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetPresetsList = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/GetPresetsList',
                request_serializer=frontend_dot_proto_dot_module__pb2.GetPresetsListRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_module__pb2.GetPresetsListResponse.FromString,
                )
        self.GetCurrentRobotDefinition = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/GetCurrentRobotDefinition',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_module__pb2.GetCurrentRobotDefinitionResponse.FromString,
                )
        self.SetCurrentRobotDefinition = channel.unary_unary(
                '/carbon.frontend.module.ModuleAssignmentService/SetCurrentRobotDefinition',
                request_serializer=frontend_dot_proto_dot_module__pb2.SetCurrentRobotDefinitionRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )


class ModuleAssignmentServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextModulesList(self, request, context):
        """Module Identity 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextActiveModules(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def IdentifyModule(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AssignModule(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ClearModuleAssignment(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetModuleSerial(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPresetsList(self, request, context):
        """Robot Definition 
        """
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCurrentRobotDefinition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCurrentRobotDefinition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ModuleAssignmentServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextModulesList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextModulesList,
                    request_deserializer=frontend_dot_proto_dot_module__pb2.GetNextModulesListRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_module__pb2.GetNextModulesListResponse.SerializeToString,
            ),
            'GetNextActiveModules': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextActiveModules,
                    request_deserializer=frontend_dot_proto_dot_module__pb2.GetNextActiveModulesRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_module__pb2.GetNextActiveModulesResponse.SerializeToString,
            ),
            'IdentifyModule': grpc.unary_unary_rpc_method_handler(
                    servicer.IdentifyModule,
                    request_deserializer=frontend_dot_proto_dot_module__pb2.IdentifyModuleRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'AssignModule': grpc.unary_unary_rpc_method_handler(
                    servicer.AssignModule,
                    request_deserializer=frontend_dot_proto_dot_module__pb2.AssignModuleRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'ClearModuleAssignment': grpc.unary_unary_rpc_method_handler(
                    servicer.ClearModuleAssignment,
                    request_deserializer=frontend_dot_proto_dot_module__pb2.ClearModuleAssignmentRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'SetModuleSerial': grpc.unary_unary_rpc_method_handler(
                    servicer.SetModuleSerial,
                    request_deserializer=frontend_dot_proto_dot_module__pb2.SetModuleSerialRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetPresetsList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPresetsList,
                    request_deserializer=frontend_dot_proto_dot_module__pb2.GetPresetsListRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_module__pb2.GetPresetsListResponse.SerializeToString,
            ),
            'GetCurrentRobotDefinition': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCurrentRobotDefinition,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_module__pb2.GetCurrentRobotDefinitionResponse.SerializeToString,
            ),
            'SetCurrentRobotDefinition': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCurrentRobotDefinition,
                    request_deserializer=frontend_dot_proto_dot_module__pb2.SetCurrentRobotDefinitionRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.module.ModuleAssignmentService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ModuleAssignmentService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextModulesList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/GetNextModulesList',
            frontend_dot_proto_dot_module__pb2.GetNextModulesListRequest.SerializeToString,
            frontend_dot_proto_dot_module__pb2.GetNextModulesListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextActiveModules(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/GetNextActiveModules',
            frontend_dot_proto_dot_module__pb2.GetNextActiveModulesRequest.SerializeToString,
            frontend_dot_proto_dot_module__pb2.GetNextActiveModulesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def IdentifyModule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/IdentifyModule',
            frontend_dot_proto_dot_module__pb2.IdentifyModuleRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AssignModule(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/AssignModule',
            frontend_dot_proto_dot_module__pb2.AssignModuleRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ClearModuleAssignment(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/ClearModuleAssignment',
            frontend_dot_proto_dot_module__pb2.ClearModuleAssignmentRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetModuleSerial(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/SetModuleSerial',
            frontend_dot_proto_dot_module__pb2.SetModuleSerialRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPresetsList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/GetPresetsList',
            frontend_dot_proto_dot_module__pb2.GetPresetsListRequest.SerializeToString,
            frontend_dot_proto_dot_module__pb2.GetPresetsListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCurrentRobotDefinition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/GetCurrentRobotDefinition',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_module__pb2.GetCurrentRobotDefinitionResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetCurrentRobotDefinition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.module.ModuleAssignmentService/SetCurrentRobotDefinition',
            frontend_dot_proto_dot_module__pb2.SetCurrentRobotDefinitionRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

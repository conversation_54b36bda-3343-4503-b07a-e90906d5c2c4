# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import target_velocity_estimator_pb2 as frontend_dot_proto_dot_target__velocity__estimator__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class TargetVelocityEstimatorServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextAvailableProfiles = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextAvailableProfiles',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.GetNextAvailableTVEProfilesResponse.FromString,
                )
        self.GetNextActiveProfile = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextActiveProfile',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.GetNextActiveTVEProfileResponse.FromString,
                )
        self.LoadProfile = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/LoadProfile',
                request_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.LoadTVEProfileRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.LoadTVEProfileResponse.FromString,
                )
        self.SaveProfile = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SaveProfile',
                request_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.SaveTVEProfileRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.SaveTVEProfileResponse.FromString,
                )
        self.SetActive = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SetActive',
                request_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.SetActiveTVEProfileRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.SetActiveTVEProfileResponse.FromString,
                )
        self.DeleteProfile = channel.unary_unary(
                '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/DeleteProfile',
                request_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.DeleteTVEProfileRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.DeleteTVEProfileResponse.FromString,
                )


class TargetVelocityEstimatorServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextAvailableProfiles(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextActiveProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def LoadProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetActive(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TargetVelocityEstimatorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextAvailableProfiles': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAvailableProfiles,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.GetNextAvailableTVEProfilesResponse.SerializeToString,
            ),
            'GetNextActiveProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextActiveProfile,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.GetNextActiveTVEProfileResponse.SerializeToString,
            ),
            'LoadProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.LoadProfile,
                    request_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.LoadTVEProfileRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.LoadTVEProfileResponse.SerializeToString,
            ),
            'SaveProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveProfile,
                    request_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.SaveTVEProfileRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.SaveTVEProfileResponse.SerializeToString,
            ),
            'SetActive': grpc.unary_unary_rpc_method_handler(
                    servicer.SetActive,
                    request_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.SetActiveTVEProfileRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.SetActiveTVEProfileResponse.SerializeToString,
            ),
            'DeleteProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteProfile,
                    request_deserializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.DeleteTVEProfileRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_target__velocity__estimator__pb2.DeleteTVEProfileResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class TargetVelocityEstimatorService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextAvailableProfiles(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextAvailableProfiles',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_target__velocity__estimator__pb2.GetNextAvailableTVEProfilesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextActiveProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextActiveProfile',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_target__velocity__estimator__pb2.GetNextActiveTVEProfileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def LoadProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/LoadProfile',
            frontend_dot_proto_dot_target__velocity__estimator__pb2.LoadTVEProfileRequest.SerializeToString,
            frontend_dot_proto_dot_target__velocity__estimator__pb2.LoadTVEProfileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SaveProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SaveProfile',
            frontend_dot_proto_dot_target__velocity__estimator__pb2.SaveTVEProfileRequest.SerializeToString,
            frontend_dot_proto_dot_target__velocity__estimator__pb2.SaveTVEProfileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetActive(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SetActive',
            frontend_dot_proto_dot_target__velocity__estimator__pb2.SetActiveTVEProfileRequest.SerializeToString,
            frontend_dot_proto_dot_target__velocity__estimator__pb2.SetActiveTVEProfileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/DeleteProfile',
            frontend_dot_proto_dot_target__velocity__estimator__pb2.DeleteTVEProfileRequest.SerializeToString,
            frontend_dot_proto_dot_target__velocity__estimator__pb2.DeleteTVEProfileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

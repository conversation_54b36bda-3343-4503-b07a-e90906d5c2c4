"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Model(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    crop: typing___Text = ...
    custom: builtin___bool = ...
    pinned: builtin___bool = ...
    active: builtin___bool = ...
    synced: builtin___bool = ...
    synced_to_rows: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    downloading: builtin___bool = ...
    type: typing___Text = ...
    downloading_progress: builtin___float = ...
    estimated_downloading_remaining_time_ms: builtin___int = ...
    recommended: builtin___bool = ...
    viable_crop_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    maintained: builtin___bool = ...
    nickname: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def last_used_timestamp(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def downloaded_timestamp(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        crop : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        custom : typing___Optional[builtin___bool] = None,
        pinned : typing___Optional[builtin___bool] = None,
        active : typing___Optional[builtin___bool] = None,
        synced : typing___Optional[builtin___bool] = None,
        synced_to_rows : typing___Optional[typing___Iterable[builtin___bool]] = None,
        downloading : typing___Optional[builtin___bool] = None,
        type : typing___Optional[typing___Text] = None,
        last_used_timestamp : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        downloading_progress : typing___Optional[builtin___float] = None,
        estimated_downloading_remaining_time_ms : typing___Optional[builtin___int] = None,
        downloaded_timestamp : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        recommended : typing___Optional[builtin___bool] = None,
        viable_crop_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        maintained : typing___Optional[builtin___bool] = None,
        nickname : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"downloaded_timestamp",b"downloaded_timestamp",u"last_used_timestamp",b"last_used_timestamp",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active",b"active",u"crop",b"crop",u"custom",b"custom",u"downloaded_timestamp",b"downloaded_timestamp",u"downloading",b"downloading",u"downloading_progress",b"downloading_progress",u"estimated_downloading_remaining_time_ms",b"estimated_downloading_remaining_time_ms",u"id",b"id",u"last_used_timestamp",b"last_used_timestamp",u"maintained",b"maintained",u"nickname",b"nickname",u"pinned",b"pinned",u"recommended",b"recommended",u"synced",b"synced",u"synced_to_rows",b"synced_to_rows",u"ts",b"ts",u"type",b"type",u"viable_crop_ids",b"viable_crop_ids"]) -> None: ...
type___Model = Model

class SelectCropRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crop_id: typing___Text = ...

    def __init__(self,
        *,
        crop_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_id",b"crop_id"]) -> None: ...
type___SelectCropRequest = SelectCropRequest

class ListCropParameters(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lang: typing___Text = ...

    def __init__(self,
        *,
        lang : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lang",b"lang"]) -> None: ...
type___ListCropParameters = ListCropParameters

class EnabledCrop(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    created: builtin___int = ...
    carbon_name: typing___Text = ...
    common_name: typing___Text = ...
    description: typing___Text = ...
    notes: typing___Text = ...
    pinned_model_id: typing___Text = ...
    recommended_model: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        created : typing___Optional[builtin___int] = None,
        carbon_name : typing___Optional[typing___Text] = None,
        common_name : typing___Optional[typing___Text] = None,
        description : typing___Optional[typing___Text] = None,
        notes : typing___Optional[typing___Text] = None,
        pinned_model_id : typing___Optional[typing___Text] = None,
        recommended_model : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"carbon_name",b"carbon_name",u"common_name",b"common_name",u"created",b"created",u"description",b"description",u"id",b"id",u"notes",b"notes",u"pinned_model_id",b"pinned_model_id",u"recommended_model",b"recommended_model"]) -> None: ...
type___EnabledCrop = EnabledCrop

class EnabledCropList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def enabledCrops(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___EnabledCrop]: ...

    def __init__(self,
        *,
        enabledCrops : typing___Optional[typing___Iterable[type___EnabledCrop]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabledCrops",b"enabledCrops"]) -> None: ...
type___EnabledCropList = EnabledCropList

class GetNextSelectedCropIDResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crop_id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        crop_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_id",b"crop_id",u"ts",b"ts"]) -> None: ...
type___GetNextSelectedCropIDResponse = GetNextSelectedCropIDResponse

class PinModelRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    crop: typing___Text = ...
    allow_pinned_crop_override: builtin___bool = ...
    crop_id: typing___Text = ...
    p2p: builtin___bool = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        crop : typing___Optional[typing___Text] = None,
        allow_pinned_crop_override : typing___Optional[builtin___bool] = None,
        crop_id : typing___Optional[typing___Text] = None,
        p2p : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"allow_pinned_crop_override",b"allow_pinned_crop_override",u"crop",b"crop",u"crop_id",b"crop_id",u"id",b"id",u"p2p",b"p2p"]) -> None: ...
type___PinModelRequest = PinModelRequest

class UnpinModelRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crop: typing___Text = ...
    crop_id: typing___Text = ...
    p2p: builtin___bool = ...

    def __init__(self,
        *,
        crop : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        p2p : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop",b"crop",u"crop_id",b"crop_id",u"p2p",b"p2p"]) -> None: ...
type___UnpinModelRequest = UnpinModelRequest

class GetNextModelStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crop: typing___Text = ...
    crop_id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        crop : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        crop_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop",b"crop",u"crop_id",b"crop_id",u"ts",b"ts"]) -> None: ...
type___GetNextModelStateRequest = GetNextModelStateRequest

class GetNextModelStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current_p2p_model_id: typing___Text = ...
    current_deepweed_model_id: typing___Text = ...

    @property
    def models(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Model]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        models : typing___Optional[typing___Iterable[type___Model]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        current_p2p_model_id : typing___Optional[typing___Text] = None,
        current_deepweed_model_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_deepweed_model_id",b"current_deepweed_model_id",u"current_p2p_model_id",b"current_p2p_model_id",u"models",b"models",u"ts",b"ts"]) -> None: ...
type___GetNextModelStateResponse = GetNextModelStateResponse

class DownloadModelRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model_id: typing___Text = ...

    def __init__(self,
        *,
        model_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model_id",b"model_id"]) -> None: ...
type___DownloadModelRequest = DownloadModelRequest

class ModelHistoryRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    start_timestamp: builtin___int = ...
    count: builtin___int = ...
    reverse: builtin___bool = ...

    @property
    def match_filter(self) -> type___ModelEvent: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def event_type_matcher(self) -> type___ModelEventTypeMatcher: ...

    def __init__(self,
        *,
        start_timestamp : typing___Optional[builtin___int] = None,
        count : typing___Optional[builtin___int] = None,
        reverse : typing___Optional[builtin___bool] = None,
        match_filter : typing___Optional[type___ModelEvent] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        event_type_matcher : typing___Optional[type___ModelEventTypeMatcher] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"event_type_matcher",b"event_type_matcher",u"match_filter",b"match_filter",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"count",b"count",u"event_type_matcher",b"event_type_matcher",u"match_filter",b"match_filter",u"reverse",b"reverse",u"start_timestamp",b"start_timestamp",u"ts",b"ts"]) -> None: ...
type___ModelHistoryRequest = ModelHistoryRequest

class ModelEvent(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: typing___Text = ...
    model_id: typing___Text = ...
    model_type: typing___Text = ...
    crop_id: typing___Text = ...
    job_name: typing___Text = ...
    time: builtin___int = ...
    model_nickname: typing___Text = ...
    model_parameters: typing___Text = ...

    def __init__(self,
        *,
        type : typing___Optional[typing___Text] = None,
        model_id : typing___Optional[typing___Text] = None,
        model_type : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        job_name : typing___Optional[typing___Text] = None,
        time : typing___Optional[builtin___int] = None,
        model_nickname : typing___Optional[typing___Text] = None,
        model_parameters : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_id",b"crop_id",u"job_name",b"job_name",u"model_id",b"model_id",u"model_nickname",b"model_nickname",u"model_parameters",b"model_parameters",u"model_type",b"model_type",u"time",b"time",u"type",b"type"]) -> None: ...
type___ModelEvent = ModelEvent

class ModelEventTypeMatcher(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot_start: builtin___bool = ...
    pinned: builtin___bool = ...
    unpinned: builtin___bool = ...
    recommended: builtin___bool = ...
    activated: builtin___bool = ...
    nickname_change: builtin___bool = ...
    nickname_delete: builtin___bool = ...
    default_parameter_change: builtin___bool = ...
    parameter_change: builtin___bool = ...

    def __init__(self,
        *,
        robot_start : typing___Optional[builtin___bool] = None,
        pinned : typing___Optional[builtin___bool] = None,
        unpinned : typing___Optional[builtin___bool] = None,
        recommended : typing___Optional[builtin___bool] = None,
        activated : typing___Optional[builtin___bool] = None,
        nickname_change : typing___Optional[builtin___bool] = None,
        nickname_delete : typing___Optional[builtin___bool] = None,
        default_parameter_change : typing___Optional[builtin___bool] = None,
        parameter_change : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"activated",b"activated",u"default_parameter_change",b"default_parameter_change",u"nickname_change",b"nickname_change",u"nickname_delete",b"nickname_delete",u"parameter_change",b"parameter_change",u"pinned",b"pinned",u"recommended",b"recommended",u"robot_start",b"robot_start",u"unpinned",b"unpinned"]) -> None: ...
type___ModelEventTypeMatcher = ModelEventTypeMatcher

class ModelHistoryResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def events(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModelEvent]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        events : typing___Optional[typing___Iterable[type___ModelEvent]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"events",b"events",u"ts",b"ts"]) -> None: ...
type___ModelHistoryResponse = ModelHistoryResponse

class GetModelNicknamesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        model_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model_ids",b"model_ids",u"ts",b"ts"]) -> None: ...
type___GetModelNicknamesRequest = GetModelNicknamesRequest

class GetModelNicknamesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ModelNicknamesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: typing___Text = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[typing___Text] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ModelNicknamesEntry = ModelNicknamesEntry


    @property
    def model_nicknames(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, typing___Text]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        model_nicknames : typing___Optional[typing___Mapping[typing___Text, typing___Text]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model_nicknames",b"model_nicknames",u"ts",b"ts"]) -> None: ...
type___GetModelNicknamesResponse = GetModelNicknamesResponse

class SetModelNicknameRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model_id: typing___Text = ...
    model_nickname: typing___Text = ...

    def __init__(self,
        *,
        model_id : typing___Optional[typing___Text] = None,
        model_nickname : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"model_id",b"model_id",u"model_nickname",b"model_nickname"]) -> None: ...
type___SetModelNicknameRequest = SetModelNicknameRequest

class CropModelPair(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crop_id: typing___Text = ...
    model_id: typing___Text = ...

    def __init__(self,
        *,
        crop_id : typing___Optional[typing___Text] = None,
        model_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_id",b"crop_id",u"model_id",b"model_id"]) -> None: ...
type___CropModelPair = CropModelPair

class RefreshDefaultModelParametersRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def cropModelPairs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CropModelPair]: ...

    def __init__(self,
        *,
        cropModelPairs : typing___Optional[typing___Iterable[type___CropModelPair]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cropModelPairs",b"cropModelPairs"]) -> None: ...
type___RefreshDefaultModelParametersRequest = RefreshDefaultModelParametersRequest

class SyncCropIDsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    force_cache_refresh: builtin___bool = ...

    def __init__(self,
        *,
        force_cache_refresh : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"force_cache_refresh",b"force_cache_refresh"]) -> None: ...
type___SyncCropIDsRequest = SyncCropIDsRequest

class GetNextEnabledCropsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lang: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        lang : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lang",b"lang",u"ts",b"ts"]) -> None: ...
type___GetNextEnabledCropsRequest = GetNextEnabledCropsRequest

class GetNextEnabledCropsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def enabledCrops(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___EnabledCrop]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        enabledCrops : typing___Optional[typing___Iterable[type___EnabledCrop]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabledCrops",b"enabledCrops",u"ts",b"ts"]) -> None: ...
type___GetNextEnabledCropsResponse = GetNextEnabledCropsResponse

class GetNextCaptureCropsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    lang: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        lang : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"lang",b"lang",u"ts",b"ts"]) -> None: ...
type___GetNextCaptureCropsRequest = GetNextCaptureCropsRequest

class GetNextCaptureCropsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def enabledCrops(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___EnabledCrop]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        enabledCrops : typing___Optional[typing___Iterable[type___EnabledCrop]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabledCrops",b"enabledCrops",u"ts",b"ts"]) -> None: ...
type___GetNextCaptureCropsResponse = GetNextCaptureCropsResponse

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.almanac.almanac_pb2 import (
    AlmanacConfig as proto___almanac___almanac_pb2___AlmanacConfig,
    ModelinatorConfig as proto___almanac___almanac_pb2___ModelinatorConfig,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)

from generated.weed_tracking.proto.weed_tracking_pb2 import (
    PlantCaptchaItemMetadata as weed_tracking___proto___weed_tracking_pb2___PlantCaptchaItemMetadata,
    PlantCaptchaStatusResponse as weed_tracking___proto___weed_tracking_pb2___PlantCaptchaStatusResponse,
    PlantCaptchaStatusValue as weed_tracking___proto___weed_tracking_pb2___PlantCaptchaStatusValue,
    PlantCaptchaUserPredictionValue as weed_tracking___proto___weed_tracking_pb2___PlantCaptchaUserPredictionValue,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

PlantCaptchaUploadStateValue = typing___NewType('PlantCaptchaUploadStateValue', builtin___int)
type___PlantCaptchaUploadStateValue = PlantCaptchaUploadStateValue
PlantCaptchaUploadState: _PlantCaptchaUploadState
class _PlantCaptchaUploadState(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[PlantCaptchaUploadStateValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NONE = typing___cast(PlantCaptchaUploadStateValue, 0)
    IN_PROGRESS = typing___cast(PlantCaptchaUploadStateValue, 1)
    DONE = typing___cast(PlantCaptchaUploadStateValue, 2)
NONE = typing___cast(PlantCaptchaUploadStateValue, 0)
IN_PROGRESS = typing___cast(PlantCaptchaUploadStateValue, 1)
DONE = typing___cast(PlantCaptchaUploadStateValue, 2)

PlantLabelAlgorithmFailureReasonValue = typing___NewType('PlantLabelAlgorithmFailureReasonValue', builtin___int)
type___PlantLabelAlgorithmFailureReasonValue = PlantLabelAlgorithmFailureReasonValue
PlantLabelAlgorithmFailureReason: _PlantLabelAlgorithmFailureReason
class _PlantLabelAlgorithmFailureReason(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[PlantLabelAlgorithmFailureReasonValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NO_FAILURE = typing___cast(PlantLabelAlgorithmFailureReasonValue, 0)
    METRICS_NOT_MET = typing___cast(PlantLabelAlgorithmFailureReasonValue, 1)
    NOT_ENOUGH_ITEMS = typing___cast(PlantLabelAlgorithmFailureReasonValue, 2)
NO_FAILURE = typing___cast(PlantLabelAlgorithmFailureReasonValue, 0)
METRICS_NOT_MET = typing___cast(PlantLabelAlgorithmFailureReasonValue, 1)
NOT_ENOUGH_ITEMS = typing___cast(PlantLabelAlgorithmFailureReasonValue, 2)

class PlantCaptcha(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    model_id: typing___Text = ...
    crop_id: typing___Text = ...
    crop_name: typing___Text = ...
    start_time_ms: builtin___int = ...
    rows_used: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        model_id : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        crop_name : typing___Optional[typing___Text] = None,
        start_time_ms : typing___Optional[builtin___int] = None,
        rows_used : typing___Optional[typing___Iterable[builtin___int]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_id",b"crop_id",u"crop_name",b"crop_name",u"model_id",b"model_id",u"name",b"name",u"rows_used",b"rows_used",u"start_time_ms",b"start_time_ms"]) -> None: ...
type___PlantCaptcha = PlantCaptcha

class StartPlantCaptchaRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def plant_captcha(self) -> type___PlantCaptcha: ...

    def __init__(self,
        *,
        plant_captcha : typing___Optional[type___PlantCaptcha] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"plant_captcha",b"plant_captcha"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"plant_captcha",b"plant_captcha"]) -> None: ...
type___StartPlantCaptchaRequest = StartPlantCaptchaRequest

class StartPlantCaptchaResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___StartPlantCaptchaResponse = StartPlantCaptchaResponse

class GetNextPlantCaptchaStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___GetNextPlantCaptchaStatusRequest = GetNextPlantCaptchaStatusRequest

class GetNextPlantCaptchaStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    status: weed_tracking___proto___weed_tracking_pb2___PlantCaptchaStatusValue = ...
    total_images: builtin___int = ...
    images_taken: builtin___int = ...
    metadata_taken: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        status : typing___Optional[weed_tracking___proto___weed_tracking_pb2___PlantCaptchaStatusValue] = None,
        total_images : typing___Optional[builtin___int] = None,
        images_taken : typing___Optional[builtin___int] = None,
        metadata_taken : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"images_taken",b"images_taken",u"metadata_taken",b"metadata_taken",u"status",b"status",u"total_images",b"total_images",u"ts",b"ts"]) -> None: ...
type___GetNextPlantCaptchaStatusResponse = GetNextPlantCaptchaStatusResponse

class GetNextPlantCaptchasListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___GetNextPlantCaptchasListRequest = GetNextPlantCaptchasListRequest

class PlantCaptchaListItem(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    images_taken: builtin___int = ...
    images_processed: builtin___int = ...

    @property
    def plant_captcha(self) -> type___PlantCaptcha: ...

    def __init__(self,
        *,
        plant_captcha : typing___Optional[type___PlantCaptcha] = None,
        images_taken : typing___Optional[builtin___int] = None,
        images_processed : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"plant_captcha",b"plant_captcha"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"images_processed",b"images_processed",u"images_taken",b"images_taken",u"plant_captcha",b"plant_captcha"]) -> None: ...
type___PlantCaptchaListItem = PlantCaptchaListItem

class GetNextPlantCaptchasListResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def plant_captchas(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PlantCaptchaListItem]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        plant_captchas : typing___Optional[typing___Iterable[type___PlantCaptchaListItem]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"plant_captchas",b"plant_captchas",u"ts",b"ts"]) -> None: ...
type___GetNextPlantCaptchasListResponse = GetNextPlantCaptchasListResponse

class DeletePlantCaptchaRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___DeletePlantCaptchaRequest = DeletePlantCaptchaRequest

class GetPlantCaptchaRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___GetPlantCaptchaRequest = GetPlantCaptchaRequest

class PlantCaptchaItem(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    image_url: typing___Text = ...
    additional_image_urls: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    @property
    def metadata(self) -> weed_tracking___proto___weed_tracking_pb2___PlantCaptchaItemMetadata: ...

    @property
    def additional_metadatas(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[weed_tracking___proto___weed_tracking_pb2___PlantCaptchaItemMetadata]: ...

    def __init__(self,
        *,
        image_url : typing___Optional[typing___Text] = None,
        metadata : typing___Optional[weed_tracking___proto___weed_tracking_pb2___PlantCaptchaItemMetadata] = None,
        additional_image_urls : typing___Optional[typing___Iterable[typing___Text]] = None,
        additional_metadatas : typing___Optional[typing___Iterable[weed_tracking___proto___weed_tracking_pb2___PlantCaptchaItemMetadata]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"metadata",b"metadata"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"additional_image_urls",b"additional_image_urls",u"additional_metadatas",b"additional_metadatas",u"image_url",b"image_url",u"metadata",b"metadata"]) -> None: ...
type___PlantCaptchaItem = PlantCaptchaItem

class GetPlantCaptchaResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def plant_captcha(self) -> type___PlantCaptcha: ...

    @property
    def items(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PlantCaptchaItem]: ...

    def __init__(self,
        *,
        plant_captcha : typing___Optional[type___PlantCaptcha] = None,
        items : typing___Optional[typing___Iterable[type___PlantCaptchaItem]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"plant_captcha",b"plant_captcha"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"items",b"items",u"plant_captcha",b"plant_captcha"]) -> None: ...
type___GetPlantCaptchaResponse = GetPlantCaptchaResponse

class StartPlantCaptchaUploadRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___StartPlantCaptchaUploadRequest = StartPlantCaptchaUploadRequest

class GetNextPlantCaptchaUploadStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"ts",b"ts"]) -> None: ...
type___GetNextPlantCaptchaUploadStateRequest = GetNextPlantCaptchaUploadStateRequest

class GetNextPlantCaptchaUploadStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    upload_state: type___PlantCaptchaUploadStateValue = ...
    percent: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        upload_state : typing___Optional[type___PlantCaptchaUploadStateValue] = None,
        percent : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"percent",b"percent",u"ts",b"ts",u"upload_state",b"upload_state"]) -> None: ...
type___GetNextPlantCaptchaUploadStateResponse = GetNextPlantCaptchaUploadStateResponse

class PlantCaptchaItemResult(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    user_prediction: weed_tracking___proto___weed_tracking_pb2___PlantCaptchaUserPredictionValue = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        user_prediction : typing___Optional[weed_tracking___proto___weed_tracking_pb2___PlantCaptchaUserPredictionValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"user_prediction",b"user_prediction"]) -> None: ...
type___PlantCaptchaItemResult = PlantCaptchaItemResult

class SubmitPlantCaptchaResultsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    @property
    def results(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PlantCaptchaItemResult]: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        results : typing___Optional[typing___Iterable[type___PlantCaptchaItemResult]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"results",b"results"]) -> None: ...
type___SubmitPlantCaptchaResultsRequest = SubmitPlantCaptchaResultsRequest

class GetPlantCaptchaItemResultsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    id: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        id : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"name",b"name"]) -> None: ...
type___GetPlantCaptchaItemResultsRequest = GetPlantCaptchaItemResultsRequest

class GetPlantCaptchaItemResultsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def results(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PlantCaptchaItemResult]: ...

    def __init__(self,
        *,
        results : typing___Optional[typing___Iterable[type___PlantCaptchaItemResult]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"results",b"results"]) -> None: ...
type___GetPlantCaptchaItemResultsResponse = GetPlantCaptchaItemResultsResponse

class CalculatePlantCaptchaRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___CalculatePlantCaptchaRequest = CalculatePlantCaptchaRequest

class CalculatePlantCaptchaResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    succeeded: builtin___bool = ...
    failure_reason: type___PlantLabelAlgorithmFailureReasonValue = ...

    @property
    def modelinator_config(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    def __init__(self,
        *,
        modelinator_config : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        succeeded : typing___Optional[builtin___bool] = None,
        failure_reason : typing___Optional[type___PlantLabelAlgorithmFailureReasonValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"modelinator_config",b"modelinator_config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"failure_reason",b"failure_reason",u"modelinator_config",b"modelinator_config",u"succeeded",b"succeeded"]) -> None: ...
type___CalculatePlantCaptchaResponse = CalculatePlantCaptchaResponse

class PlantCaptchaResult(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    label: weed_tracking___proto___weed_tracking_pb2___PlantCaptchaUserPredictionValue = ...

    @property
    def metadata(self) -> weed_tracking___proto___weed_tracking_pb2___PlantCaptchaItemMetadata: ...

    def __init__(self,
        *,
        label : typing___Optional[weed_tracking___proto___weed_tracking_pb2___PlantCaptchaUserPredictionValue] = None,
        metadata : typing___Optional[weed_tracking___proto___weed_tracking_pb2___PlantCaptchaItemMetadata] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"metadata",b"metadata"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"label",b"label",u"metadata",b"metadata"]) -> None: ...
type___PlantCaptchaResult = PlantCaptchaResult

class PlantCaptchaResults(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    algorithm: typing___Text = ...
    goal_crops_targeted: builtin___float = ...
    goal_weeds_targeted: builtin___float = ...
    goal_unknown_targeted: builtin___float = ...
    max_recommended_mindoo: builtin___float = ...
    min_items_for_recommendation: builtin___int = ...
    use_weed_categories_for_weed_labels: builtin___bool = ...
    min_recommended_mindoo: builtin___float = ...
    min_recommended_weed_threshold: builtin___float = ...
    max_recommended_weed_threshold: builtin___float = ...
    min_recommended_crop_threshold: builtin___float = ...
    max_recommended_crop_threshold: builtin___float = ...
    min_doo_for_recommendation: builtin___float = ...
    use_other_as_tiebreaker: builtin___bool = ...
    limit_by_crops_missed: builtin___bool = ...
    number_of_crop_configurations: builtin___int = ...
    tiebreaker: typing___Text = ...
    pad_crop_configurations: builtin___bool = ...
    mindoo_tiebreaker: typing___Text = ...
    use_beneficials_as_crops: builtin___bool = ...
    use_volunteers_as_weeds: builtin___bool = ...
    tiebreaker_strategy_threshold_weed: typing___Text = ...
    tiebreaker_strategy_threshold_crop: typing___Text = ...
    tiebreaker_strategy_mindoo_weed: typing___Text = ...
    tiebreaker_strategy_mindoo_crop: typing___Text = ...

    @property
    def current_parameters(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    @property
    def captcha_results(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___PlantCaptchaResult]: ...

    @property
    def almanac(self) -> proto___almanac___almanac_pb2___AlmanacConfig: ...

    def __init__(self,
        *,
        current_parameters : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        captcha_results : typing___Optional[typing___Iterable[type___PlantCaptchaResult]] = None,
        algorithm : typing___Optional[typing___Text] = None,
        goal_crops_targeted : typing___Optional[builtin___float] = None,
        goal_weeds_targeted : typing___Optional[builtin___float] = None,
        goal_unknown_targeted : typing___Optional[builtin___float] = None,
        almanac : typing___Optional[proto___almanac___almanac_pb2___AlmanacConfig] = None,
        max_recommended_mindoo : typing___Optional[builtin___float] = None,
        min_items_for_recommendation : typing___Optional[builtin___int] = None,
        use_weed_categories_for_weed_labels : typing___Optional[builtin___bool] = None,
        min_recommended_mindoo : typing___Optional[builtin___float] = None,
        min_recommended_weed_threshold : typing___Optional[builtin___float] = None,
        max_recommended_weed_threshold : typing___Optional[builtin___float] = None,
        min_recommended_crop_threshold : typing___Optional[builtin___float] = None,
        max_recommended_crop_threshold : typing___Optional[builtin___float] = None,
        min_doo_for_recommendation : typing___Optional[builtin___float] = None,
        use_other_as_tiebreaker : typing___Optional[builtin___bool] = None,
        limit_by_crops_missed : typing___Optional[builtin___bool] = None,
        number_of_crop_configurations : typing___Optional[builtin___int] = None,
        tiebreaker : typing___Optional[typing___Text] = None,
        pad_crop_configurations : typing___Optional[builtin___bool] = None,
        mindoo_tiebreaker : typing___Optional[typing___Text] = None,
        use_beneficials_as_crops : typing___Optional[builtin___bool] = None,
        use_volunteers_as_weeds : typing___Optional[builtin___bool] = None,
        tiebreaker_strategy_threshold_weed : typing___Optional[typing___Text] = None,
        tiebreaker_strategy_threshold_crop : typing___Optional[typing___Text] = None,
        tiebreaker_strategy_mindoo_weed : typing___Optional[typing___Text] = None,
        tiebreaker_strategy_mindoo_crop : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"almanac",b"almanac",u"current_parameters",b"current_parameters"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"algorithm",b"algorithm",u"almanac",b"almanac",u"captcha_results",b"captcha_results",u"current_parameters",b"current_parameters",u"goal_crops_targeted",b"goal_crops_targeted",u"goal_unknown_targeted",b"goal_unknown_targeted",u"goal_weeds_targeted",b"goal_weeds_targeted",u"limit_by_crops_missed",b"limit_by_crops_missed",u"max_recommended_crop_threshold",b"max_recommended_crop_threshold",u"max_recommended_mindoo",b"max_recommended_mindoo",u"max_recommended_weed_threshold",b"max_recommended_weed_threshold",u"min_doo_for_recommendation",b"min_doo_for_recommendation",u"min_items_for_recommendation",b"min_items_for_recommendation",u"min_recommended_crop_threshold",b"min_recommended_crop_threshold",u"min_recommended_mindoo",b"min_recommended_mindoo",u"min_recommended_weed_threshold",b"min_recommended_weed_threshold",u"mindoo_tiebreaker",b"mindoo_tiebreaker",u"number_of_crop_configurations",b"number_of_crop_configurations",u"pad_crop_configurations",b"pad_crop_configurations",u"tiebreaker",b"tiebreaker",u"tiebreaker_strategy_mindoo_crop",b"tiebreaker_strategy_mindoo_crop",u"tiebreaker_strategy_mindoo_weed",b"tiebreaker_strategy_mindoo_weed",u"tiebreaker_strategy_threshold_crop",b"tiebreaker_strategy_threshold_crop",u"tiebreaker_strategy_threshold_weed",b"tiebreaker_strategy_threshold_weed",u"use_beneficials_as_crops",b"use_beneficials_as_crops",u"use_other_as_tiebreaker",b"use_other_as_tiebreaker",u"use_volunteers_as_weeds",b"use_volunteers_as_weeds",u"use_weed_categories_for_weed_labels",b"use_weed_categories_for_weed_labels"]) -> None: ...
type___PlantCaptchaResults = PlantCaptchaResults

class VeselkaPlantCaptchaResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    succeeded: builtin___bool = ...

    @property
    def new_model_parameters(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    def __init__(self,
        *,
        new_model_parameters : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        succeeded : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"new_model_parameters",b"new_model_parameters"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"new_model_parameters",b"new_model_parameters",u"succeeded",b"succeeded"]) -> None: ...
type___VeselkaPlantCaptchaResponse = VeselkaPlantCaptchaResponse

class GetOriginalModelinatorConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___GetOriginalModelinatorConfigRequest = GetOriginalModelinatorConfigRequest

class GetOriginalModelinatorConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def modelinator_config(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    def __init__(self,
        *,
        modelinator_config : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"modelinator_config",b"modelinator_config"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"modelinator_config",b"modelinator_config"]) -> None: ...
type___GetOriginalModelinatorConfigResponse = GetOriginalModelinatorConfigResponse

class GetCaptchaRowStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class RowStatusEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> weed_tracking___proto___weed_tracking_pb2___PlantCaptchaStatusResponse: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[weed_tracking___proto___weed_tracking_pb2___PlantCaptchaStatusResponse] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___RowStatusEntry = RowStatusEntry


    @property
    def row_status(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, weed_tracking___proto___weed_tracking_pb2___PlantCaptchaStatusResponse]: ...

    def __init__(self,
        *,
        row_status : typing___Optional[typing___Mapping[builtin___int, weed_tracking___proto___weed_tracking_pb2___PlantCaptchaStatusResponse]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_status",b"row_status"]) -> None: ...
type___GetCaptchaRowStatusResponse = GetCaptchaRowStatusResponse

class CancelPlantCaptchaOnRowRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_id: builtin___int = ...

    def __init__(self,
        *,
        row_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"row_id",b"row_id"]) -> None: ...
type___CancelPlantCaptchaOnRowRequest = CancelPlantCaptchaOnRowRequest

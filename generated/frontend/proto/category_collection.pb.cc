// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/category_collection.proto

#include "frontend/proto/category_collection.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace category_collection {
constexpr GetNextCategoryCollectionsDataRequest::GetNextCategoryCollectionsDataRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct GetNextCategoryCollectionsDataRequestDefaultTypeInternal {
  constexpr GetNextCategoryCollectionsDataRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextCategoryCollectionsDataRequestDefaultTypeInternal() {}
  union {
    GetNextCategoryCollectionsDataRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextCategoryCollectionsDataRequestDefaultTypeInternal _GetNextCategoryCollectionsDataRequest_default_instance_;
constexpr GetNextCategoryCollectionsDataResponse::GetNextCategoryCollectionsDataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : category_collections_()
  , ts_(nullptr){}
struct GetNextCategoryCollectionsDataResponseDefaultTypeInternal {
  constexpr GetNextCategoryCollectionsDataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextCategoryCollectionsDataResponseDefaultTypeInternal() {}
  union {
    GetNextCategoryCollectionsDataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextCategoryCollectionsDataResponseDefaultTypeInternal _GetNextCategoryCollectionsDataResponse_default_instance_;
constexpr GetNextActiveCategoryCollectionIdRequest::GetNextActiveCategoryCollectionIdRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct GetNextActiveCategoryCollectionIdRequestDefaultTypeInternal {
  constexpr GetNextActiveCategoryCollectionIdRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveCategoryCollectionIdRequestDefaultTypeInternal() {}
  union {
    GetNextActiveCategoryCollectionIdRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveCategoryCollectionIdRequestDefaultTypeInternal _GetNextActiveCategoryCollectionIdRequest_default_instance_;
constexpr GetNextActiveCategoryCollectionIdResponse::GetNextActiveCategoryCollectionIdResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , last_updated_timestamp_ms_(int64_t{0})
  , reload_required_(false){}
struct GetNextActiveCategoryCollectionIdResponseDefaultTypeInternal {
  constexpr GetNextActiveCategoryCollectionIdResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveCategoryCollectionIdResponseDefaultTypeInternal() {}
  union {
    GetNextActiveCategoryCollectionIdResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveCategoryCollectionIdResponseDefaultTypeInternal _GetNextActiveCategoryCollectionIdResponse_default_instance_;
constexpr SetActiveCategoryCollectionIdRequest::SetActiveCategoryCollectionIdRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct SetActiveCategoryCollectionIdRequestDefaultTypeInternal {
  constexpr SetActiveCategoryCollectionIdRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveCategoryCollectionIdRequestDefaultTypeInternal() {}
  union {
    SetActiveCategoryCollectionIdRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveCategoryCollectionIdRequestDefaultTypeInternal _SetActiveCategoryCollectionIdRequest_default_instance_;
}  // namespace category_collection
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fcategory_5fcollection_2eproto[5];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fcategory_5fcollection_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fcategory_5fcollection_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fcategory_5fcollection_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse, category_collections_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, uuid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, reload_required_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse, last_updated_timestamp_ms_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, uuid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ts_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest)},
  { 7, -1, -1, sizeof(::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse)},
  { 15, -1, -1, sizeof(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest)},
  { 22, -1, -1, sizeof(::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse)},
  { 32, -1, -1, sizeof(::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::category_collection::_GetNextCategoryCollectionsDataRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::category_collection::_GetNextCategoryCollectionsDataResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::category_collection::_GetNextActiveCategoryCollectionIdRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::category_collection::_GetNextActiveCategoryCollectionIdResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::category_collection::_SetActiveCategoryCollectionIdRequest_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fcategory_5fcollection_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n(frontend/proto/category_collection.pro"
  "to\022#carbon.frontend.category_collection\032"
  "\031frontend/proto/util.proto\032\035category/pro"
  "to/category.proto\"T\n%GetNextCategoryColl"
  "ectionsDataRequest\022+\n\002ts\030\001 \001(\0132\037.carbon."
  "frontend.util.Timestamp\"\230\001\n&GetNextCateg"
  "oryCollectionsDataResponse\022+\n\002ts\030\001 \001(\0132\037"
  ".carbon.frontend.util.Timestamp\022A\n\024categ"
  "ory_collections\030\003 \003(\0132#.carbon.category."
  "CategoryCollection\"W\n(GetNextActiveCateg"
  "oryCollectionIdRequest\022+\n\002ts\030\001 \001(\0132\037.car"
  "bon.frontend.util.Timestamp\"\242\001\n)GetNextA"
  "ctiveCategoryCollectionIdResponse\022\014\n\004uui"
  "d\030\001 \001(\t\022+\n\002ts\030\002 \001(\0132\037.carbon.frontend.ut"
  "il.Timestamp\022\027\n\017reload_required\030\003 \001(\010\022!\n"
  "\031last_updated_timestamp_ms\030\004 \001(\003\"a\n$SetA"
  "ctiveCategoryCollectionIdRequest\022\014\n\004uuid"
  "\030\001 \001(\t\022+\n\002ts\030\002 \001(\0132\037.carbon.frontend.uti"
  "l.Timestamp2\374\004\n\031CategoryCollectionServic"
  "e\022\271\001\n\036GetNextCategoryCollectionsData\022J.c"
  "arbon.frontend.category_collection.GetNe"
  "xtCategoryCollectionsDataRequest\032K.carbo"
  "n.frontend.category_collection.GetNextCa"
  "tegoryCollectionsDataResponse\022\302\001\n!GetNex"
  "tActiveCategoryCollectionId\022M.carbon.fro"
  "ntend.category_collection.GetNextActiveC"
  "ategoryCollectionIdRequest\032N.carbon.fron"
  "tend.category_collection.GetNextActiveCa"
  "tegoryCollectionIdResponse\022\207\001\n\035SetActive"
  "CategoryCollectionId\022I.carbon.frontend.c"
  "ategory_collection.SetActiveCategoryColl"
  "ectionIdRequest\032\033.carbon.frontend.util.E"
  "mpty\022T\n\030ReloadCategoryCollection\022\033.carbo"
  "n.frontend.util.Empty\032\033.carbon.frontend."
  "util.EmptyB\020Z\016proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_deps[2] = {
  &::descriptor_table_category_2fproto_2fcategory_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto = {
  false, false, 1396, descriptor_table_protodef_frontend_2fproto_2fcategory_5fcollection_2eproto, "frontend/proto/category_collection.proto", 
  &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_once, descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_deps, 2, 5,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fcategory_5fcollection_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fcategory_5fcollection_2eproto, file_level_enum_descriptors_frontend_2fproto_2fcategory_5fcollection_2eproto, file_level_service_descriptors_frontend_2fproto_2fcategory_5fcollection_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fcategory_5fcollection_2eproto(&descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto);
namespace carbon {
namespace frontend {
namespace category_collection {

// ===================================================================

class GetNextCategoryCollectionsDataRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextCategoryCollectionsDataRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextCategoryCollectionsDataRequest::_Internal::ts(const GetNextCategoryCollectionsDataRequest* msg) {
  return *msg->ts_;
}
void GetNextCategoryCollectionsDataRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextCategoryCollectionsDataRequest::GetNextCategoryCollectionsDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
}
GetNextCategoryCollectionsDataRequest::GetNextCategoryCollectionsDataRequest(const GetNextCategoryCollectionsDataRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
}

inline void GetNextCategoryCollectionsDataRequest::SharedCtor() {
ts_ = nullptr;
}

GetNextCategoryCollectionsDataRequest::~GetNextCategoryCollectionsDataRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextCategoryCollectionsDataRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextCategoryCollectionsDataRequest::ArenaDtor(void* object) {
  GetNextCategoryCollectionsDataRequest* _this = reinterpret_cast< GetNextCategoryCollectionsDataRequest* >(object);
  (void)_this;
}
void GetNextCategoryCollectionsDataRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextCategoryCollectionsDataRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextCategoryCollectionsDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextCategoryCollectionsDataRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextCategoryCollectionsDataRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
  return target;
}

size_t GetNextCategoryCollectionsDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextCategoryCollectionsDataRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextCategoryCollectionsDataRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextCategoryCollectionsDataRequest::GetClassData() const { return &_class_data_; }

void GetNextCategoryCollectionsDataRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextCategoryCollectionsDataRequest *>(to)->MergeFrom(
      static_cast<const GetNextCategoryCollectionsDataRequest &>(from));
}


void GetNextCategoryCollectionsDataRequest::MergeFrom(const GetNextCategoryCollectionsDataRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextCategoryCollectionsDataRequest::CopyFrom(const GetNextCategoryCollectionsDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextCategoryCollectionsDataRequest::IsInitialized() const {
  return true;
}

void GetNextCategoryCollectionsDataRequest::InternalSwap(GetNextCategoryCollectionsDataRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextCategoryCollectionsDataRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_getter, &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcategory_5fcollection_2eproto[0]);
}

// ===================================================================

class GetNextCategoryCollectionsDataResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextCategoryCollectionsDataResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextCategoryCollectionsDataResponse::_Internal::ts(const GetNextCategoryCollectionsDataResponse* msg) {
  return *msg->ts_;
}
void GetNextCategoryCollectionsDataResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void GetNextCategoryCollectionsDataResponse::clear_category_collections() {
  category_collections_.Clear();
}
GetNextCategoryCollectionsDataResponse::GetNextCategoryCollectionsDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  category_collections_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
}
GetNextCategoryCollectionsDataResponse::GetNextCategoryCollectionsDataResponse(const GetNextCategoryCollectionsDataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      category_collections_(from.category_collections_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
}

inline void GetNextCategoryCollectionsDataResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextCategoryCollectionsDataResponse::~GetNextCategoryCollectionsDataResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextCategoryCollectionsDataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextCategoryCollectionsDataResponse::ArenaDtor(void* object) {
  GetNextCategoryCollectionsDataResponse* _this = reinterpret_cast< GetNextCategoryCollectionsDataResponse* >(object);
  (void)_this;
}
void GetNextCategoryCollectionsDataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextCategoryCollectionsDataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextCategoryCollectionsDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  category_collections_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextCategoryCollectionsDataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.category.CategoryCollection category_collections = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_category_collections(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextCategoryCollectionsDataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.category.CategoryCollection category_collections = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_category_collections_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_category_collections(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
  return target;
}

size_t GetNextCategoryCollectionsDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.category.CategoryCollection category_collections = 3;
  total_size += 1UL * this->_internal_category_collections_size();
  for (const auto& msg : this->category_collections_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextCategoryCollectionsDataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextCategoryCollectionsDataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextCategoryCollectionsDataResponse::GetClassData() const { return &_class_data_; }

void GetNextCategoryCollectionsDataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextCategoryCollectionsDataResponse *>(to)->MergeFrom(
      static_cast<const GetNextCategoryCollectionsDataResponse &>(from));
}


void GetNextCategoryCollectionsDataResponse::MergeFrom(const GetNextCategoryCollectionsDataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  category_collections_.MergeFrom(from.category_collections_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextCategoryCollectionsDataResponse::CopyFrom(const GetNextCategoryCollectionsDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextCategoryCollectionsDataResponse::IsInitialized() const {
  return true;
}

void GetNextCategoryCollectionsDataResponse::InternalSwap(GetNextCategoryCollectionsDataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  category_collections_.InternalSwap(&other->category_collections_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextCategoryCollectionsDataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_getter, &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcategory_5fcollection_2eproto[1]);
}

// ===================================================================

class GetNextActiveCategoryCollectionIdRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextActiveCategoryCollectionIdRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextActiveCategoryCollectionIdRequest::_Internal::ts(const GetNextActiveCategoryCollectionIdRequest* msg) {
  return *msg->ts_;
}
void GetNextActiveCategoryCollectionIdRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextActiveCategoryCollectionIdRequest::GetNextActiveCategoryCollectionIdRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
}
GetNextActiveCategoryCollectionIdRequest::GetNextActiveCategoryCollectionIdRequest(const GetNextActiveCategoryCollectionIdRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
}

inline void GetNextActiveCategoryCollectionIdRequest::SharedCtor() {
ts_ = nullptr;
}

GetNextActiveCategoryCollectionIdRequest::~GetNextActiveCategoryCollectionIdRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveCategoryCollectionIdRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextActiveCategoryCollectionIdRequest::ArenaDtor(void* object) {
  GetNextActiveCategoryCollectionIdRequest* _this = reinterpret_cast< GetNextActiveCategoryCollectionIdRequest* >(object);
  (void)_this;
}
void GetNextActiveCategoryCollectionIdRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveCategoryCollectionIdRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveCategoryCollectionIdRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveCategoryCollectionIdRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveCategoryCollectionIdRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
  return target;
}

size_t GetNextActiveCategoryCollectionIdRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveCategoryCollectionIdRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveCategoryCollectionIdRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveCategoryCollectionIdRequest::GetClassData() const { return &_class_data_; }

void GetNextActiveCategoryCollectionIdRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveCategoryCollectionIdRequest *>(to)->MergeFrom(
      static_cast<const GetNextActiveCategoryCollectionIdRequest &>(from));
}


void GetNextActiveCategoryCollectionIdRequest::MergeFrom(const GetNextActiveCategoryCollectionIdRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveCategoryCollectionIdRequest::CopyFrom(const GetNextActiveCategoryCollectionIdRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveCategoryCollectionIdRequest::IsInitialized() const {
  return true;
}

void GetNextActiveCategoryCollectionIdRequest::InternalSwap(GetNextActiveCategoryCollectionIdRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveCategoryCollectionIdRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_getter, &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcategory_5fcollection_2eproto[2]);
}

// ===================================================================

class GetNextActiveCategoryCollectionIdResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextActiveCategoryCollectionIdResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextActiveCategoryCollectionIdResponse::_Internal::ts(const GetNextActiveCategoryCollectionIdResponse* msg) {
  return *msg->ts_;
}
void GetNextActiveCategoryCollectionIdResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextActiveCategoryCollectionIdResponse::GetNextActiveCategoryCollectionIdResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
}
GetNextActiveCategoryCollectionIdResponse::GetNextActiveCategoryCollectionIdResponse(const GetNextActiveCategoryCollectionIdResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&last_updated_timestamp_ms_, &from.last_updated_timestamp_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&reload_required_) -
    reinterpret_cast<char*>(&last_updated_timestamp_ms_)) + sizeof(reload_required_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
}

inline void GetNextActiveCategoryCollectionIdResponse::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&reload_required_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(reload_required_));
}

GetNextActiveCategoryCollectionIdResponse::~GetNextActiveCategoryCollectionIdResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveCategoryCollectionIdResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextActiveCategoryCollectionIdResponse::ArenaDtor(void* object) {
  GetNextActiveCategoryCollectionIdResponse* _this = reinterpret_cast< GetNextActiveCategoryCollectionIdResponse* >(object);
  (void)_this;
}
void GetNextActiveCategoryCollectionIdResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveCategoryCollectionIdResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveCategoryCollectionIdResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&last_updated_timestamp_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&reload_required_) -
      reinterpret_cast<char*>(&last_updated_timestamp_ms_)) + sizeof(reload_required_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveCategoryCollectionIdResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool reload_required = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          reload_required_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 last_updated_timestamp_ms = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          last_updated_timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveCategoryCollectionIdResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  // bool reload_required = 3;
  if (this->_internal_reload_required() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_reload_required(), target);
  }

  // int64 last_updated_timestamp_ms = 4;
  if (this->_internal_last_updated_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(4, this->_internal_last_updated_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
  return target;
}

size_t GetNextActiveCategoryCollectionIdResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // int64 last_updated_timestamp_ms = 4;
  if (this->_internal_last_updated_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_last_updated_timestamp_ms());
  }

  // bool reload_required = 3;
  if (this->_internal_reload_required() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveCategoryCollectionIdResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveCategoryCollectionIdResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveCategoryCollectionIdResponse::GetClassData() const { return &_class_data_; }

void GetNextActiveCategoryCollectionIdResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveCategoryCollectionIdResponse *>(to)->MergeFrom(
      static_cast<const GetNextActiveCategoryCollectionIdResponse &>(from));
}


void GetNextActiveCategoryCollectionIdResponse::MergeFrom(const GetNextActiveCategoryCollectionIdResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_last_updated_timestamp_ms() != 0) {
    _internal_set_last_updated_timestamp_ms(from._internal_last_updated_timestamp_ms());
  }
  if (from._internal_reload_required() != 0) {
    _internal_set_reload_required(from._internal_reload_required());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveCategoryCollectionIdResponse::CopyFrom(const GetNextActiveCategoryCollectionIdResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveCategoryCollectionIdResponse::IsInitialized() const {
  return true;
}

void GetNextActiveCategoryCollectionIdResponse::InternalSwap(GetNextActiveCategoryCollectionIdResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextActiveCategoryCollectionIdResponse, reload_required_)
      + sizeof(GetNextActiveCategoryCollectionIdResponse::reload_required_)
      - PROTOBUF_FIELD_OFFSET(GetNextActiveCategoryCollectionIdResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveCategoryCollectionIdResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_getter, &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcategory_5fcollection_2eproto[3]);
}

// ===================================================================

class SetActiveCategoryCollectionIdRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const SetActiveCategoryCollectionIdRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
SetActiveCategoryCollectionIdRequest::_Internal::ts(const SetActiveCategoryCollectionIdRequest* msg) {
  return *msg->ts_;
}
void SetActiveCategoryCollectionIdRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
SetActiveCategoryCollectionIdRequest::SetActiveCategoryCollectionIdRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
}
SetActiveCategoryCollectionIdRequest::SetActiveCategoryCollectionIdRequest(const SetActiveCategoryCollectionIdRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
}

inline void SetActiveCategoryCollectionIdRequest::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

SetActiveCategoryCollectionIdRequest::~SetActiveCategoryCollectionIdRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetActiveCategoryCollectionIdRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void SetActiveCategoryCollectionIdRequest::ArenaDtor(void* object) {
  SetActiveCategoryCollectionIdRequest* _this = reinterpret_cast< SetActiveCategoryCollectionIdRequest* >(object);
  (void)_this;
}
void SetActiveCategoryCollectionIdRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetActiveCategoryCollectionIdRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetActiveCategoryCollectionIdRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetActiveCategoryCollectionIdRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetActiveCategoryCollectionIdRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
  return target;
}

size_t SetActiveCategoryCollectionIdRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveCategoryCollectionIdRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetActiveCategoryCollectionIdRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveCategoryCollectionIdRequest::GetClassData() const { return &_class_data_; }

void SetActiveCategoryCollectionIdRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetActiveCategoryCollectionIdRequest *>(to)->MergeFrom(
      static_cast<const SetActiveCategoryCollectionIdRequest &>(from));
}


void SetActiveCategoryCollectionIdRequest::MergeFrom(const SetActiveCategoryCollectionIdRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetActiveCategoryCollectionIdRequest::CopyFrom(const SetActiveCategoryCollectionIdRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetActiveCategoryCollectionIdRequest::IsInitialized() const {
  return true;
}

void SetActiveCategoryCollectionIdRequest::InternalSwap(SetActiveCategoryCollectionIdRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetActiveCategoryCollectionIdRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_getter, &descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcategory_5fcollection_2eproto[4]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace category_collection
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* Arena::CreateMaybeMessage< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* Arena::CreateMaybeMessage< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* Arena::CreateMaybeMessage< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* Arena::CreateMaybeMessage< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* Arena::CreateMaybeMessage< ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

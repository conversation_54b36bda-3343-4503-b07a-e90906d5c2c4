// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/laser.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2flaser_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2flaser_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/camera.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2flaser_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2flaser_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2flaser_2eproto;
namespace carbon {
namespace frontend {
namespace laser {
class FixLaserMetricsRequest;
struct FixLaserMetricsRequestDefaultTypeInternal;
extern FixLaserMetricsRequestDefaultTypeInternal _FixLaserMetricsRequest_default_instance_;
class LaserDescriptor;
struct LaserDescriptorDefaultTypeInternal;
extern LaserDescriptorDefaultTypeInternal _LaserDescriptor_default_instance_;
class LaserState;
struct LaserStateDefaultTypeInternal;
extern LaserStateDefaultTypeInternal _LaserState_default_instance_;
class LaserStateList;
struct LaserStateListDefaultTypeInternal;
extern LaserStateListDefaultTypeInternal _LaserStateList_default_instance_;
class RowRequest;
struct RowRequestDefaultTypeInternal;
extern RowRequestDefaultTypeInternal _RowRequest_default_instance_;
class SetLaserPowerRequest;
struct SetLaserPowerRequestDefaultTypeInternal;
extern SetLaserPowerRequestDefaultTypeInternal _SetLaserPowerRequest_default_instance_;
}  // namespace laser
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::laser::FixLaserMetricsRequest* Arena::CreateMaybeMessage<::carbon::frontend::laser::FixLaserMetricsRequest>(Arena*);
template<> ::carbon::frontend::laser::LaserDescriptor* Arena::CreateMaybeMessage<::carbon::frontend::laser::LaserDescriptor>(Arena*);
template<> ::carbon::frontend::laser::LaserState* Arena::CreateMaybeMessage<::carbon::frontend::laser::LaserState>(Arena*);
template<> ::carbon::frontend::laser::LaserStateList* Arena::CreateMaybeMessage<::carbon::frontend::laser::LaserStateList>(Arena*);
template<> ::carbon::frontend::laser::RowRequest* Arena::CreateMaybeMessage<::carbon::frontend::laser::RowRequest>(Arena*);
template<> ::carbon::frontend::laser::SetLaserPowerRequest* Arena::CreateMaybeMessage<::carbon::frontend::laser::SetLaserPowerRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace laser {

// ===================================================================

class LaserDescriptor final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.laser.LaserDescriptor) */ {
 public:
  inline LaserDescriptor() : LaserDescriptor(nullptr) {}
  ~LaserDescriptor() override;
  explicit constexpr LaserDescriptor(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserDescriptor(const LaserDescriptor& from);
  LaserDescriptor(LaserDescriptor&& from) noexcept
    : LaserDescriptor() {
    *this = ::std::move(from);
  }

  inline LaserDescriptor& operator=(const LaserDescriptor& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserDescriptor& operator=(LaserDescriptor&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserDescriptor& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserDescriptor* internal_default_instance() {
    return reinterpret_cast<const LaserDescriptor*>(
               &_LaserDescriptor_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(LaserDescriptor& a, LaserDescriptor& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserDescriptor* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserDescriptor* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserDescriptor* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserDescriptor>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserDescriptor& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserDescriptor& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserDescriptor* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.laser.LaserDescriptor";
  }
  protected:
  explicit LaserDescriptor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCameraIdFieldNumber = 3,
    kSerialFieldNumber = 4,
    kRowNumberFieldNumber = 1,
    kLaserIdFieldNumber = 2,
  };
  // string camera_id = 3;
  void clear_camera_id();
  const std::string& camera_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_camera_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_camera_id();
  PROTOBUF_NODISCARD std::string* release_camera_id();
  void set_allocated_camera_id(std::string* camera_id);
  private:
  const std::string& _internal_camera_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_camera_id(const std::string& value);
  std::string* _internal_mutable_camera_id();
  public:

  // string serial = 4;
  void clear_serial();
  const std::string& serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial();
  PROTOBUF_NODISCARD std::string* release_serial();
  void set_allocated_serial(std::string* serial);
  private:
  const std::string& _internal_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial(const std::string& value);
  std::string* _internal_mutable_serial();
  public:

  // uint32 row_number = 1;
  void clear_row_number();
  uint32_t row_number() const;
  void set_row_number(uint32_t value);
  private:
  uint32_t _internal_row_number() const;
  void _internal_set_row_number(uint32_t value);
  public:

  // uint32 laser_id = 2;
  void clear_laser_id();
  uint32_t laser_id() const;
  void set_laser_id(uint32_t value);
  private:
  uint32_t _internal_laser_id() const;
  void _internal_set_laser_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.laser.LaserDescriptor)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr camera_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_;
  uint32_t row_number_;
  uint32_t laser_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2flaser_2eproto;
};
// -------------------------------------------------------------------

class LaserState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.laser.LaserState) */ {
 public:
  inline LaserState() : LaserState(nullptr) {}
  ~LaserState() override;
  explicit constexpr LaserState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserState(const LaserState& from);
  LaserState(LaserState&& from) noexcept
    : LaserState() {
    *this = ::std::move(from);
  }

  inline LaserState& operator=(const LaserState& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserState& operator=(LaserState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserState& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserState* internal_default_instance() {
    return reinterpret_cast<const LaserState*>(
               &_LaserState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(LaserState& a, LaserState& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.laser.LaserState";
  }
  protected:
  explicit LaserState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaserDescriptorFieldNumber = 1,
    kFiringFieldNumber = 2,
    kEnabledFieldNumber = 3,
    kErrorFieldNumber = 4,
    kDeltaTempFieldNumber = 7,
    kTotalFireCountFieldNumber = 5,
    kTotalFireTimeMsFieldNumber = 6,
    kCurrentFieldNumber = 9,
    kTargetTrajectoryIdFieldNumber = 10,
    kLifetimeSecFieldNumber = 11,
    kInstalledAtFieldNumber = 13,
    kPowerLevelFieldNumber = 12,
  };
  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  bool has_laser_descriptor() const;
  private:
  bool _internal_has_laser_descriptor() const;
  public:
  void clear_laser_descriptor();
  const ::carbon::frontend::laser::LaserDescriptor& laser_descriptor() const;
  PROTOBUF_NODISCARD ::carbon::frontend::laser::LaserDescriptor* release_laser_descriptor();
  ::carbon::frontend::laser::LaserDescriptor* mutable_laser_descriptor();
  void set_allocated_laser_descriptor(::carbon::frontend::laser::LaserDescriptor* laser_descriptor);
  private:
  const ::carbon::frontend::laser::LaserDescriptor& _internal_laser_descriptor() const;
  ::carbon::frontend::laser::LaserDescriptor* _internal_mutable_laser_descriptor();
  public:
  void unsafe_arena_set_allocated_laser_descriptor(
      ::carbon::frontend::laser::LaserDescriptor* laser_descriptor);
  ::carbon::frontend::laser::LaserDescriptor* unsafe_arena_release_laser_descriptor();

  // bool firing = 2;
  void clear_firing();
  bool firing() const;
  void set_firing(bool value);
  private:
  bool _internal_firing() const;
  void _internal_set_firing(bool value);
  public:

  // bool enabled = 3;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // bool error = 4;
  void clear_error();
  bool error() const;
  void set_error(bool value);
  private:
  bool _internal_error() const;
  void _internal_set_error(bool value);
  public:

  // float delta_temp = 7;
  void clear_delta_temp();
  float delta_temp() const;
  void set_delta_temp(float value);
  private:
  float _internal_delta_temp() const;
  void _internal_set_delta_temp(float value);
  public:

  // int64 total_fire_count = 5;
  void clear_total_fire_count();
  int64_t total_fire_count() const;
  void set_total_fire_count(int64_t value);
  private:
  int64_t _internal_total_fire_count() const;
  void _internal_set_total_fire_count(int64_t value);
  public:

  // int64 total_fire_time_ms = 6;
  void clear_total_fire_time_ms();
  int64_t total_fire_time_ms() const;
  void set_total_fire_time_ms(int64_t value);
  private:
  int64_t _internal_total_fire_time_ms() const;
  void _internal_set_total_fire_time_ms(int64_t value);
  public:

  // float current = 9;
  void clear_current();
  float current() const;
  void set_current(float value);
  private:
  float _internal_current() const;
  void _internal_set_current(float value);
  public:

  // uint32 target_trajectory_id = 10;
  void clear_target_trajectory_id();
  uint32_t target_trajectory_id() const;
  void set_target_trajectory_id(uint32_t value);
  private:
  uint32_t _internal_target_trajectory_id() const;
  void _internal_set_target_trajectory_id(uint32_t value);
  public:

  // uint64 lifetime_sec = 11;
  void clear_lifetime_sec();
  uint64_t lifetime_sec() const;
  void set_lifetime_sec(uint64_t value);
  private:
  uint64_t _internal_lifetime_sec() const;
  void _internal_set_lifetime_sec(uint64_t value);
  public:

  // int64 installed_at = 13;
  void clear_installed_at();
  int64_t installed_at() const;
  void set_installed_at(int64_t value);
  private:
  int64_t _internal_installed_at() const;
  void _internal_set_installed_at(int64_t value);
  public:

  // float power_level = 12;
  void clear_power_level();
  float power_level() const;
  void set_power_level(float value);
  private:
  float _internal_power_level() const;
  void _internal_set_power_level(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.laser.LaserState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::laser::LaserDescriptor* laser_descriptor_;
  bool firing_;
  bool enabled_;
  bool error_;
  float delta_temp_;
  int64_t total_fire_count_;
  int64_t total_fire_time_ms_;
  float current_;
  uint32_t target_trajectory_id_;
  uint64_t lifetime_sec_;
  int64_t installed_at_;
  float power_level_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2flaser_2eproto;
};
// -------------------------------------------------------------------

class LaserStateList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.laser.LaserStateList) */ {
 public:
  inline LaserStateList() : LaserStateList(nullptr) {}
  ~LaserStateList() override;
  explicit constexpr LaserStateList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  LaserStateList(const LaserStateList& from);
  LaserStateList(LaserStateList&& from) noexcept
    : LaserStateList() {
    *this = ::std::move(from);
  }

  inline LaserStateList& operator=(const LaserStateList& from) {
    CopyFrom(from);
    return *this;
  }
  inline LaserStateList& operator=(LaserStateList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const LaserStateList& default_instance() {
    return *internal_default_instance();
  }
  static inline const LaserStateList* internal_default_instance() {
    return reinterpret_cast<const LaserStateList*>(
               &_LaserStateList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(LaserStateList& a, LaserStateList& b) {
    a.Swap(&b);
  }
  inline void Swap(LaserStateList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(LaserStateList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  LaserStateList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<LaserStateList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const LaserStateList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const LaserStateList& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(LaserStateList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.laser.LaserStateList";
  }
  protected:
  explicit LaserStateList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLasersFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.frontend.laser.LaserState lasers = 2;
  int lasers_size() const;
  private:
  int _internal_lasers_size() const;
  public:
  void clear_lasers();
  ::carbon::frontend::laser::LaserState* mutable_lasers(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::laser::LaserState >*
      mutable_lasers();
  private:
  const ::carbon::frontend::laser::LaserState& _internal_lasers(int index) const;
  ::carbon::frontend::laser::LaserState* _internal_add_lasers();
  public:
  const ::carbon::frontend::laser::LaserState& lasers(int index) const;
  ::carbon::frontend::laser::LaserState* add_lasers();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::laser::LaserState >&
      lasers() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.laser.LaserStateList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::laser::LaserState > lasers_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2flaser_2eproto;
};
// -------------------------------------------------------------------

class RowRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.laser.RowRequest) */ {
 public:
  inline RowRequest() : RowRequest(nullptr) {}
  ~RowRequest() override;
  explicit constexpr RowRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RowRequest(const RowRequest& from);
  RowRequest(RowRequest&& from) noexcept
    : RowRequest() {
    *this = ::std::move(from);
  }

  inline RowRequest& operator=(const RowRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RowRequest& operator=(RowRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RowRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RowRequest* internal_default_instance() {
    return reinterpret_cast<const RowRequest*>(
               &_RowRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(RowRequest& a, RowRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RowRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RowRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RowRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RowRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RowRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RowRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RowRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.laser.RowRequest";
  }
  protected:
  explicit RowRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowNumberFieldNumber = 1,
  };
  // uint32 row_number = 1;
  void clear_row_number();
  uint32_t row_number() const;
  void set_row_number(uint32_t value);
  private:
  uint32_t _internal_row_number() const;
  void _internal_set_row_number(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.laser.RowRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t row_number_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2flaser_2eproto;
};
// -------------------------------------------------------------------

class SetLaserPowerRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.laser.SetLaserPowerRequest) */ {
 public:
  inline SetLaserPowerRequest() : SetLaserPowerRequest(nullptr) {}
  ~SetLaserPowerRequest() override;
  explicit constexpr SetLaserPowerRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetLaserPowerRequest(const SetLaserPowerRequest& from);
  SetLaserPowerRequest(SetLaserPowerRequest&& from) noexcept
    : SetLaserPowerRequest() {
    *this = ::std::move(from);
  }

  inline SetLaserPowerRequest& operator=(const SetLaserPowerRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetLaserPowerRequest& operator=(SetLaserPowerRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetLaserPowerRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetLaserPowerRequest* internal_default_instance() {
    return reinterpret_cast<const SetLaserPowerRequest*>(
               &_SetLaserPowerRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SetLaserPowerRequest& a, SetLaserPowerRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetLaserPowerRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetLaserPowerRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetLaserPowerRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetLaserPowerRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetLaserPowerRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetLaserPowerRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetLaserPowerRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.laser.SetLaserPowerRequest";
  }
  protected:
  explicit SetLaserPowerRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaserDescriptorFieldNumber = 1,
    kPowerLevelFieldNumber = 2,
  };
  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  bool has_laser_descriptor() const;
  private:
  bool _internal_has_laser_descriptor() const;
  public:
  void clear_laser_descriptor();
  const ::carbon::frontend::laser::LaserDescriptor& laser_descriptor() const;
  PROTOBUF_NODISCARD ::carbon::frontend::laser::LaserDescriptor* release_laser_descriptor();
  ::carbon::frontend::laser::LaserDescriptor* mutable_laser_descriptor();
  void set_allocated_laser_descriptor(::carbon::frontend::laser::LaserDescriptor* laser_descriptor);
  private:
  const ::carbon::frontend::laser::LaserDescriptor& _internal_laser_descriptor() const;
  ::carbon::frontend::laser::LaserDescriptor* _internal_mutable_laser_descriptor();
  public:
  void unsafe_arena_set_allocated_laser_descriptor(
      ::carbon::frontend::laser::LaserDescriptor* laser_descriptor);
  ::carbon::frontend::laser::LaserDescriptor* unsafe_arena_release_laser_descriptor();

  // float power_level = 2;
  void clear_power_level();
  float power_level() const;
  void set_power_level(float value);
  private:
  float _internal_power_level() const;
  void _internal_set_power_level(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.laser.SetLaserPowerRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::laser::LaserDescriptor* laser_descriptor_;
  float power_level_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2flaser_2eproto;
};
// -------------------------------------------------------------------

class FixLaserMetricsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.laser.FixLaserMetricsRequest) */ {
 public:
  inline FixLaserMetricsRequest() : FixLaserMetricsRequest(nullptr) {}
  ~FixLaserMetricsRequest() override;
  explicit constexpr FixLaserMetricsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FixLaserMetricsRequest(const FixLaserMetricsRequest& from);
  FixLaserMetricsRequest(FixLaserMetricsRequest&& from) noexcept
    : FixLaserMetricsRequest() {
    *this = ::std::move(from);
  }

  inline FixLaserMetricsRequest& operator=(const FixLaserMetricsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline FixLaserMetricsRequest& operator=(FixLaserMetricsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FixLaserMetricsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const FixLaserMetricsRequest* internal_default_instance() {
    return reinterpret_cast<const FixLaserMetricsRequest*>(
               &_FixLaserMetricsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(FixLaserMetricsRequest& a, FixLaserMetricsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(FixLaserMetricsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FixLaserMetricsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FixLaserMetricsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FixLaserMetricsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FixLaserMetricsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FixLaserMetricsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FixLaserMetricsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.laser.FixLaserMetricsRequest";
  }
  protected:
  explicit FixLaserMetricsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLaserDescriptorFieldNumber = 1,
    kTotalFireCountFieldNumber = 2,
    kTotalFireTimeMsFieldNumber = 3,
    kLifetimeSecFieldNumber = 4,
  };
  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  bool has_laser_descriptor() const;
  private:
  bool _internal_has_laser_descriptor() const;
  public:
  void clear_laser_descriptor();
  const ::carbon::frontend::laser::LaserDescriptor& laser_descriptor() const;
  PROTOBUF_NODISCARD ::carbon::frontend::laser::LaserDescriptor* release_laser_descriptor();
  ::carbon::frontend::laser::LaserDescriptor* mutable_laser_descriptor();
  void set_allocated_laser_descriptor(::carbon::frontend::laser::LaserDescriptor* laser_descriptor);
  private:
  const ::carbon::frontend::laser::LaserDescriptor& _internal_laser_descriptor() const;
  ::carbon::frontend::laser::LaserDescriptor* _internal_mutable_laser_descriptor();
  public:
  void unsafe_arena_set_allocated_laser_descriptor(
      ::carbon::frontend::laser::LaserDescriptor* laser_descriptor);
  ::carbon::frontend::laser::LaserDescriptor* unsafe_arena_release_laser_descriptor();

  // int64 total_fire_count = 2;
  void clear_total_fire_count();
  int64_t total_fire_count() const;
  void set_total_fire_count(int64_t value);
  private:
  int64_t _internal_total_fire_count() const;
  void _internal_set_total_fire_count(int64_t value);
  public:

  // int64 total_fire_time_ms = 3;
  void clear_total_fire_time_ms();
  int64_t total_fire_time_ms() const;
  void set_total_fire_time_ms(int64_t value);
  private:
  int64_t _internal_total_fire_time_ms() const;
  void _internal_set_total_fire_time_ms(int64_t value);
  public:

  // uint64 lifetime_sec = 4;
  void clear_lifetime_sec();
  uint64_t lifetime_sec() const;
  void set_lifetime_sec(uint64_t value);
  private:
  uint64_t _internal_lifetime_sec() const;
  void _internal_set_lifetime_sec(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.laser.FixLaserMetricsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::laser::LaserDescriptor* laser_descriptor_;
  int64_t total_fire_count_;
  int64_t total_fire_time_ms_;
  uint64_t lifetime_sec_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2flaser_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// LaserDescriptor

// uint32 row_number = 1;
inline void LaserDescriptor::clear_row_number() {
  row_number_ = 0u;
}
inline uint32_t LaserDescriptor::_internal_row_number() const {
  return row_number_;
}
inline uint32_t LaserDescriptor::row_number() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserDescriptor.row_number)
  return _internal_row_number();
}
inline void LaserDescriptor::_internal_set_row_number(uint32_t value) {
  
  row_number_ = value;
}
inline void LaserDescriptor::set_row_number(uint32_t value) {
  _internal_set_row_number(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserDescriptor.row_number)
}

// uint32 laser_id = 2;
inline void LaserDescriptor::clear_laser_id() {
  laser_id_ = 0u;
}
inline uint32_t LaserDescriptor::_internal_laser_id() const {
  return laser_id_;
}
inline uint32_t LaserDescriptor::laser_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserDescriptor.laser_id)
  return _internal_laser_id();
}
inline void LaserDescriptor::_internal_set_laser_id(uint32_t value) {
  
  laser_id_ = value;
}
inline void LaserDescriptor::set_laser_id(uint32_t value) {
  _internal_set_laser_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserDescriptor.laser_id)
}

// string camera_id = 3;
inline void LaserDescriptor::clear_camera_id() {
  camera_id_.ClearToEmpty();
}
inline const std::string& LaserDescriptor::camera_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserDescriptor.camera_id)
  return _internal_camera_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LaserDescriptor::set_camera_id(ArgT0&& arg0, ArgT... args) {
 
 camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserDescriptor.camera_id)
}
inline std::string* LaserDescriptor::mutable_camera_id() {
  std::string* _s = _internal_mutable_camera_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.laser.LaserDescriptor.camera_id)
  return _s;
}
inline const std::string& LaserDescriptor::_internal_camera_id() const {
  return camera_id_.Get();
}
inline void LaserDescriptor::_internal_set_camera_id(const std::string& value) {
  
  camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LaserDescriptor::_internal_mutable_camera_id() {
  
  return camera_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LaserDescriptor::release_camera_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.laser.LaserDescriptor.camera_id)
  return camera_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LaserDescriptor::set_allocated_camera_id(std::string* camera_id) {
  if (camera_id != nullptr) {
    
  } else {
    
  }
  camera_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), camera_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (camera_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.laser.LaserDescriptor.camera_id)
}

// string serial = 4;
inline void LaserDescriptor::clear_serial() {
  serial_.ClearToEmpty();
}
inline const std::string& LaserDescriptor::serial() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserDescriptor.serial)
  return _internal_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void LaserDescriptor::set_serial(ArgT0&& arg0, ArgT... args) {
 
 serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserDescriptor.serial)
}
inline std::string* LaserDescriptor::mutable_serial() {
  std::string* _s = _internal_mutable_serial();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.laser.LaserDescriptor.serial)
  return _s;
}
inline const std::string& LaserDescriptor::_internal_serial() const {
  return serial_.Get();
}
inline void LaserDescriptor::_internal_set_serial(const std::string& value) {
  
  serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* LaserDescriptor::_internal_mutable_serial() {
  
  return serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* LaserDescriptor::release_serial() {
  // @@protoc_insertion_point(field_release:carbon.frontend.laser.LaserDescriptor.serial)
  return serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void LaserDescriptor::set_allocated_serial(std::string* serial) {
  if (serial != nullptr) {
    
  } else {
    
  }
  serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.laser.LaserDescriptor.serial)
}

// -------------------------------------------------------------------

// LaserState

// .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
inline bool LaserState::_internal_has_laser_descriptor() const {
  return this != internal_default_instance() && laser_descriptor_ != nullptr;
}
inline bool LaserState::has_laser_descriptor() const {
  return _internal_has_laser_descriptor();
}
inline void LaserState::clear_laser_descriptor() {
  if (GetArenaForAllocation() == nullptr && laser_descriptor_ != nullptr) {
    delete laser_descriptor_;
  }
  laser_descriptor_ = nullptr;
}
inline const ::carbon::frontend::laser::LaserDescriptor& LaserState::_internal_laser_descriptor() const {
  const ::carbon::frontend::laser::LaserDescriptor* p = laser_descriptor_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::laser::LaserDescriptor&>(
      ::carbon::frontend::laser::_LaserDescriptor_default_instance_);
}
inline const ::carbon::frontend::laser::LaserDescriptor& LaserState::laser_descriptor() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.laser_descriptor)
  return _internal_laser_descriptor();
}
inline void LaserState::unsafe_arena_set_allocated_laser_descriptor(
    ::carbon::frontend::laser::LaserDescriptor* laser_descriptor) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_descriptor_);
  }
  laser_descriptor_ = laser_descriptor;
  if (laser_descriptor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.laser.LaserState.laser_descriptor)
}
inline ::carbon::frontend::laser::LaserDescriptor* LaserState::release_laser_descriptor() {
  
  ::carbon::frontend::laser::LaserDescriptor* temp = laser_descriptor_;
  laser_descriptor_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::laser::LaserDescriptor* LaserState::unsafe_arena_release_laser_descriptor() {
  // @@protoc_insertion_point(field_release:carbon.frontend.laser.LaserState.laser_descriptor)
  
  ::carbon::frontend::laser::LaserDescriptor* temp = laser_descriptor_;
  laser_descriptor_ = nullptr;
  return temp;
}
inline ::carbon::frontend::laser::LaserDescriptor* LaserState::_internal_mutable_laser_descriptor() {
  
  if (laser_descriptor_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::laser::LaserDescriptor>(GetArenaForAllocation());
    laser_descriptor_ = p;
  }
  return laser_descriptor_;
}
inline ::carbon::frontend::laser::LaserDescriptor* LaserState::mutable_laser_descriptor() {
  ::carbon::frontend::laser::LaserDescriptor* _msg = _internal_mutable_laser_descriptor();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.laser.LaserState.laser_descriptor)
  return _msg;
}
inline void LaserState::set_allocated_laser_descriptor(::carbon::frontend::laser::LaserDescriptor* laser_descriptor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete laser_descriptor_;
  }
  if (laser_descriptor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::laser::LaserDescriptor>::GetOwningArena(laser_descriptor);
    if (message_arena != submessage_arena) {
      laser_descriptor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, laser_descriptor, submessage_arena);
    }
    
  } else {
    
  }
  laser_descriptor_ = laser_descriptor;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.laser.LaserState.laser_descriptor)
}

// bool firing = 2;
inline void LaserState::clear_firing() {
  firing_ = false;
}
inline bool LaserState::_internal_firing() const {
  return firing_;
}
inline bool LaserState::firing() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.firing)
  return _internal_firing();
}
inline void LaserState::_internal_set_firing(bool value) {
  
  firing_ = value;
}
inline void LaserState::set_firing(bool value) {
  _internal_set_firing(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.firing)
}

// bool enabled = 3;
inline void LaserState::clear_enabled() {
  enabled_ = false;
}
inline bool LaserState::_internal_enabled() const {
  return enabled_;
}
inline bool LaserState::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.enabled)
  return _internal_enabled();
}
inline void LaserState::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void LaserState::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.enabled)
}

// bool error = 4;
inline void LaserState::clear_error() {
  error_ = false;
}
inline bool LaserState::_internal_error() const {
  return error_;
}
inline bool LaserState::error() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.error)
  return _internal_error();
}
inline void LaserState::_internal_set_error(bool value) {
  
  error_ = value;
}
inline void LaserState::set_error(bool value) {
  _internal_set_error(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.error)
}

// int64 total_fire_count = 5;
inline void LaserState::clear_total_fire_count() {
  total_fire_count_ = int64_t{0};
}
inline int64_t LaserState::_internal_total_fire_count() const {
  return total_fire_count_;
}
inline int64_t LaserState::total_fire_count() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.total_fire_count)
  return _internal_total_fire_count();
}
inline void LaserState::_internal_set_total_fire_count(int64_t value) {
  
  total_fire_count_ = value;
}
inline void LaserState::set_total_fire_count(int64_t value) {
  _internal_set_total_fire_count(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.total_fire_count)
}

// int64 total_fire_time_ms = 6;
inline void LaserState::clear_total_fire_time_ms() {
  total_fire_time_ms_ = int64_t{0};
}
inline int64_t LaserState::_internal_total_fire_time_ms() const {
  return total_fire_time_ms_;
}
inline int64_t LaserState::total_fire_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.total_fire_time_ms)
  return _internal_total_fire_time_ms();
}
inline void LaserState::_internal_set_total_fire_time_ms(int64_t value) {
  
  total_fire_time_ms_ = value;
}
inline void LaserState::set_total_fire_time_ms(int64_t value) {
  _internal_set_total_fire_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.total_fire_time_ms)
}

// float delta_temp = 7;
inline void LaserState::clear_delta_temp() {
  delta_temp_ = 0;
}
inline float LaserState::_internal_delta_temp() const {
  return delta_temp_;
}
inline float LaserState::delta_temp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.delta_temp)
  return _internal_delta_temp();
}
inline void LaserState::_internal_set_delta_temp(float value) {
  
  delta_temp_ = value;
}
inline void LaserState::set_delta_temp(float value) {
  _internal_set_delta_temp(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.delta_temp)
}

// float current = 9;
inline void LaserState::clear_current() {
  current_ = 0;
}
inline float LaserState::_internal_current() const {
  return current_;
}
inline float LaserState::current() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.current)
  return _internal_current();
}
inline void LaserState::_internal_set_current(float value) {
  
  current_ = value;
}
inline void LaserState::set_current(float value) {
  _internal_set_current(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.current)
}

// uint32 target_trajectory_id = 10;
inline void LaserState::clear_target_trajectory_id() {
  target_trajectory_id_ = 0u;
}
inline uint32_t LaserState::_internal_target_trajectory_id() const {
  return target_trajectory_id_;
}
inline uint32_t LaserState::target_trajectory_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.target_trajectory_id)
  return _internal_target_trajectory_id();
}
inline void LaserState::_internal_set_target_trajectory_id(uint32_t value) {
  
  target_trajectory_id_ = value;
}
inline void LaserState::set_target_trajectory_id(uint32_t value) {
  _internal_set_target_trajectory_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.target_trajectory_id)
}

// uint64 lifetime_sec = 11;
inline void LaserState::clear_lifetime_sec() {
  lifetime_sec_ = uint64_t{0u};
}
inline uint64_t LaserState::_internal_lifetime_sec() const {
  return lifetime_sec_;
}
inline uint64_t LaserState::lifetime_sec() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.lifetime_sec)
  return _internal_lifetime_sec();
}
inline void LaserState::_internal_set_lifetime_sec(uint64_t value) {
  
  lifetime_sec_ = value;
}
inline void LaserState::set_lifetime_sec(uint64_t value) {
  _internal_set_lifetime_sec(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.lifetime_sec)
}

// float power_level = 12;
inline void LaserState::clear_power_level() {
  power_level_ = 0;
}
inline float LaserState::_internal_power_level() const {
  return power_level_;
}
inline float LaserState::power_level() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.power_level)
  return _internal_power_level();
}
inline void LaserState::_internal_set_power_level(float value) {
  
  power_level_ = value;
}
inline void LaserState::set_power_level(float value) {
  _internal_set_power_level(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.power_level)
}

// int64 installed_at = 13;
inline void LaserState::clear_installed_at() {
  installed_at_ = int64_t{0};
}
inline int64_t LaserState::_internal_installed_at() const {
  return installed_at_;
}
inline int64_t LaserState::installed_at() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserState.installed_at)
  return _internal_installed_at();
}
inline void LaserState::_internal_set_installed_at(int64_t value) {
  
  installed_at_ = value;
}
inline void LaserState::set_installed_at(int64_t value) {
  _internal_set_installed_at(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.LaserState.installed_at)
}

// -------------------------------------------------------------------

// LaserStateList

// .carbon.frontend.util.Timestamp ts = 1;
inline bool LaserStateList::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool LaserStateList::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& LaserStateList::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& LaserStateList::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserStateList.ts)
  return _internal_ts();
}
inline void LaserStateList::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.laser.LaserStateList.ts)
}
inline ::carbon::frontend::util::Timestamp* LaserStateList::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* LaserStateList::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.laser.LaserStateList.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* LaserStateList::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* LaserStateList::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.laser.LaserStateList.ts)
  return _msg;
}
inline void LaserStateList::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.laser.LaserStateList.ts)
}

// repeated .carbon.frontend.laser.LaserState lasers = 2;
inline int LaserStateList::_internal_lasers_size() const {
  return lasers_.size();
}
inline int LaserStateList::lasers_size() const {
  return _internal_lasers_size();
}
inline void LaserStateList::clear_lasers() {
  lasers_.Clear();
}
inline ::carbon::frontend::laser::LaserState* LaserStateList::mutable_lasers(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.laser.LaserStateList.lasers)
  return lasers_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::laser::LaserState >*
LaserStateList::mutable_lasers() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.laser.LaserStateList.lasers)
  return &lasers_;
}
inline const ::carbon::frontend::laser::LaserState& LaserStateList::_internal_lasers(int index) const {
  return lasers_.Get(index);
}
inline const ::carbon::frontend::laser::LaserState& LaserStateList::lasers(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.LaserStateList.lasers)
  return _internal_lasers(index);
}
inline ::carbon::frontend::laser::LaserState* LaserStateList::_internal_add_lasers() {
  return lasers_.Add();
}
inline ::carbon::frontend::laser::LaserState* LaserStateList::add_lasers() {
  ::carbon::frontend::laser::LaserState* _add = _internal_add_lasers();
  // @@protoc_insertion_point(field_add:carbon.frontend.laser.LaserStateList.lasers)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::laser::LaserState >&
LaserStateList::lasers() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.laser.LaserStateList.lasers)
  return lasers_;
}

// -------------------------------------------------------------------

// RowRequest

// uint32 row_number = 1;
inline void RowRequest::clear_row_number() {
  row_number_ = 0u;
}
inline uint32_t RowRequest::_internal_row_number() const {
  return row_number_;
}
inline uint32_t RowRequest::row_number() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.RowRequest.row_number)
  return _internal_row_number();
}
inline void RowRequest::_internal_set_row_number(uint32_t value) {
  
  row_number_ = value;
}
inline void RowRequest::set_row_number(uint32_t value) {
  _internal_set_row_number(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.RowRequest.row_number)
}

// -------------------------------------------------------------------

// SetLaserPowerRequest

// .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
inline bool SetLaserPowerRequest::_internal_has_laser_descriptor() const {
  return this != internal_default_instance() && laser_descriptor_ != nullptr;
}
inline bool SetLaserPowerRequest::has_laser_descriptor() const {
  return _internal_has_laser_descriptor();
}
inline void SetLaserPowerRequest::clear_laser_descriptor() {
  if (GetArenaForAllocation() == nullptr && laser_descriptor_ != nullptr) {
    delete laser_descriptor_;
  }
  laser_descriptor_ = nullptr;
}
inline const ::carbon::frontend::laser::LaserDescriptor& SetLaserPowerRequest::_internal_laser_descriptor() const {
  const ::carbon::frontend::laser::LaserDescriptor* p = laser_descriptor_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::laser::LaserDescriptor&>(
      ::carbon::frontend::laser::_LaserDescriptor_default_instance_);
}
inline const ::carbon::frontend::laser::LaserDescriptor& SetLaserPowerRequest::laser_descriptor() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.SetLaserPowerRequest.laser_descriptor)
  return _internal_laser_descriptor();
}
inline void SetLaserPowerRequest::unsafe_arena_set_allocated_laser_descriptor(
    ::carbon::frontend::laser::LaserDescriptor* laser_descriptor) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_descriptor_);
  }
  laser_descriptor_ = laser_descriptor;
  if (laser_descriptor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.laser.SetLaserPowerRequest.laser_descriptor)
}
inline ::carbon::frontend::laser::LaserDescriptor* SetLaserPowerRequest::release_laser_descriptor() {
  
  ::carbon::frontend::laser::LaserDescriptor* temp = laser_descriptor_;
  laser_descriptor_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::laser::LaserDescriptor* SetLaserPowerRequest::unsafe_arena_release_laser_descriptor() {
  // @@protoc_insertion_point(field_release:carbon.frontend.laser.SetLaserPowerRequest.laser_descriptor)
  
  ::carbon::frontend::laser::LaserDescriptor* temp = laser_descriptor_;
  laser_descriptor_ = nullptr;
  return temp;
}
inline ::carbon::frontend::laser::LaserDescriptor* SetLaserPowerRequest::_internal_mutable_laser_descriptor() {
  
  if (laser_descriptor_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::laser::LaserDescriptor>(GetArenaForAllocation());
    laser_descriptor_ = p;
  }
  return laser_descriptor_;
}
inline ::carbon::frontend::laser::LaserDescriptor* SetLaserPowerRequest::mutable_laser_descriptor() {
  ::carbon::frontend::laser::LaserDescriptor* _msg = _internal_mutable_laser_descriptor();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.laser.SetLaserPowerRequest.laser_descriptor)
  return _msg;
}
inline void SetLaserPowerRequest::set_allocated_laser_descriptor(::carbon::frontend::laser::LaserDescriptor* laser_descriptor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete laser_descriptor_;
  }
  if (laser_descriptor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::laser::LaserDescriptor>::GetOwningArena(laser_descriptor);
    if (message_arena != submessage_arena) {
      laser_descriptor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, laser_descriptor, submessage_arena);
    }
    
  } else {
    
  }
  laser_descriptor_ = laser_descriptor;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.laser.SetLaserPowerRequest.laser_descriptor)
}

// float power_level = 2;
inline void SetLaserPowerRequest::clear_power_level() {
  power_level_ = 0;
}
inline float SetLaserPowerRequest::_internal_power_level() const {
  return power_level_;
}
inline float SetLaserPowerRequest::power_level() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.SetLaserPowerRequest.power_level)
  return _internal_power_level();
}
inline void SetLaserPowerRequest::_internal_set_power_level(float value) {
  
  power_level_ = value;
}
inline void SetLaserPowerRequest::set_power_level(float value) {
  _internal_set_power_level(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.SetLaserPowerRequest.power_level)
}

// -------------------------------------------------------------------

// FixLaserMetricsRequest

// .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
inline bool FixLaserMetricsRequest::_internal_has_laser_descriptor() const {
  return this != internal_default_instance() && laser_descriptor_ != nullptr;
}
inline bool FixLaserMetricsRequest::has_laser_descriptor() const {
  return _internal_has_laser_descriptor();
}
inline void FixLaserMetricsRequest::clear_laser_descriptor() {
  if (GetArenaForAllocation() == nullptr && laser_descriptor_ != nullptr) {
    delete laser_descriptor_;
  }
  laser_descriptor_ = nullptr;
}
inline const ::carbon::frontend::laser::LaserDescriptor& FixLaserMetricsRequest::_internal_laser_descriptor() const {
  const ::carbon::frontend::laser::LaserDescriptor* p = laser_descriptor_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::laser::LaserDescriptor&>(
      ::carbon::frontend::laser::_LaserDescriptor_default_instance_);
}
inline const ::carbon::frontend::laser::LaserDescriptor& FixLaserMetricsRequest::laser_descriptor() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.FixLaserMetricsRequest.laser_descriptor)
  return _internal_laser_descriptor();
}
inline void FixLaserMetricsRequest::unsafe_arena_set_allocated_laser_descriptor(
    ::carbon::frontend::laser::LaserDescriptor* laser_descriptor) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(laser_descriptor_);
  }
  laser_descriptor_ = laser_descriptor;
  if (laser_descriptor) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.laser.FixLaserMetricsRequest.laser_descriptor)
}
inline ::carbon::frontend::laser::LaserDescriptor* FixLaserMetricsRequest::release_laser_descriptor() {
  
  ::carbon::frontend::laser::LaserDescriptor* temp = laser_descriptor_;
  laser_descriptor_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::laser::LaserDescriptor* FixLaserMetricsRequest::unsafe_arena_release_laser_descriptor() {
  // @@protoc_insertion_point(field_release:carbon.frontend.laser.FixLaserMetricsRequest.laser_descriptor)
  
  ::carbon::frontend::laser::LaserDescriptor* temp = laser_descriptor_;
  laser_descriptor_ = nullptr;
  return temp;
}
inline ::carbon::frontend::laser::LaserDescriptor* FixLaserMetricsRequest::_internal_mutable_laser_descriptor() {
  
  if (laser_descriptor_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::laser::LaserDescriptor>(GetArenaForAllocation());
    laser_descriptor_ = p;
  }
  return laser_descriptor_;
}
inline ::carbon::frontend::laser::LaserDescriptor* FixLaserMetricsRequest::mutable_laser_descriptor() {
  ::carbon::frontend::laser::LaserDescriptor* _msg = _internal_mutable_laser_descriptor();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.laser.FixLaserMetricsRequest.laser_descriptor)
  return _msg;
}
inline void FixLaserMetricsRequest::set_allocated_laser_descriptor(::carbon::frontend::laser::LaserDescriptor* laser_descriptor) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete laser_descriptor_;
  }
  if (laser_descriptor) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::laser::LaserDescriptor>::GetOwningArena(laser_descriptor);
    if (message_arena != submessage_arena) {
      laser_descriptor = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, laser_descriptor, submessage_arena);
    }
    
  } else {
    
  }
  laser_descriptor_ = laser_descriptor;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.laser.FixLaserMetricsRequest.laser_descriptor)
}

// int64 total_fire_count = 2;
inline void FixLaserMetricsRequest::clear_total_fire_count() {
  total_fire_count_ = int64_t{0};
}
inline int64_t FixLaserMetricsRequest::_internal_total_fire_count() const {
  return total_fire_count_;
}
inline int64_t FixLaserMetricsRequest::total_fire_count() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.FixLaserMetricsRequest.total_fire_count)
  return _internal_total_fire_count();
}
inline void FixLaserMetricsRequest::_internal_set_total_fire_count(int64_t value) {
  
  total_fire_count_ = value;
}
inline void FixLaserMetricsRequest::set_total_fire_count(int64_t value) {
  _internal_set_total_fire_count(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.FixLaserMetricsRequest.total_fire_count)
}

// int64 total_fire_time_ms = 3;
inline void FixLaserMetricsRequest::clear_total_fire_time_ms() {
  total_fire_time_ms_ = int64_t{0};
}
inline int64_t FixLaserMetricsRequest::_internal_total_fire_time_ms() const {
  return total_fire_time_ms_;
}
inline int64_t FixLaserMetricsRequest::total_fire_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.FixLaserMetricsRequest.total_fire_time_ms)
  return _internal_total_fire_time_ms();
}
inline void FixLaserMetricsRequest::_internal_set_total_fire_time_ms(int64_t value) {
  
  total_fire_time_ms_ = value;
}
inline void FixLaserMetricsRequest::set_total_fire_time_ms(int64_t value) {
  _internal_set_total_fire_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.FixLaserMetricsRequest.total_fire_time_ms)
}

// uint64 lifetime_sec = 4;
inline void FixLaserMetricsRequest::clear_lifetime_sec() {
  lifetime_sec_ = uint64_t{0u};
}
inline uint64_t FixLaserMetricsRequest::_internal_lifetime_sec() const {
  return lifetime_sec_;
}
inline uint64_t FixLaserMetricsRequest::lifetime_sec() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.laser.FixLaserMetricsRequest.lifetime_sec)
  return _internal_lifetime_sec();
}
inline void FixLaserMetricsRequest::_internal_set_lifetime_sec(uint64_t value) {
  
  lifetime_sec_ = value;
}
inline void FixLaserMetricsRequest::set_lifetime_sec(uint64_t value) {
  _internal_set_lifetime_sec(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.laser.FixLaserMetricsRequest.lifetime_sec)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace laser
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2flaser_2eproto

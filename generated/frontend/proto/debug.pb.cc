// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/debug.proto

#include "frontend/proto/debug.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/io/zero_copy_stream_impl_lite.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace debug {
constexpr RobotMessage::RobotMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : alarms_()
  , serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct RobotMessageDefaultTypeInternal {
  constexpr RobotMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RobotMessageDefaultTypeInternal() {}
  union {
    RobotMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RobotMessageDefaultTypeInternal _RobotMessage_default_instance_;
constexpr SetLogLevelRequest::SetLogLevelRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : component_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , level_(0)

  , row_num_(0){}
struct SetLogLevelRequestDefaultTypeInternal {
  constexpr SetLogLevelRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetLogLevelRequestDefaultTypeInternal() {}
  union {
    SetLogLevelRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetLogLevelRequestDefaultTypeInternal _SetLogLevelRequest_default_instance_;
}  // namespace debug
}  // namespace frontend
}  // namespace carbon
namespace carbon {
namespace frontend {
namespace debug {

// ===================================================================

class RobotMessage::_Internal {
 public:
};

void RobotMessage::clear_alarms() {
  alarms_.Clear();
}
RobotMessage::RobotMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned),
  alarms_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.debug.RobotMessage)
}
RobotMessage::RobotMessage(const RobotMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(),
      alarms_(from.alarms_) {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serial().empty()) {
    serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serial(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.debug.RobotMessage)
}

inline void RobotMessage::SharedCtor() {
serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RobotMessage::~RobotMessage() {
  // @@protoc_insertion_point(destructor:carbon.frontend.debug.RobotMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void RobotMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RobotMessage::ArenaDtor(void* object) {
  RobotMessage* _this = reinterpret_cast< RobotMessage* >(object);
  (void)_this;
}
void RobotMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RobotMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RobotMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.debug.RobotMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  alarms_.Clear();
  serial_.ClearToEmpty();
  _internal_metadata_.Clear<std::string>();
}

const char* RobotMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string serial = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, nullptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_alarms(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RobotMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.debug.RobotMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string serial = 1;
  if (!this->_internal_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serial().data(), static_cast<int>(this->_internal_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.debug.RobotMessage.serial");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_serial(), target);
  }

  // repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_alarms_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_alarms(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.debug.RobotMessage)
  return target;
}

size_t RobotMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.debug.RobotMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
  total_size += 1UL * this->_internal_alarms_size();
  for (const auto& msg : this->alarms_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string serial = 1;
  if (!this->_internal_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serial());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void RobotMessage::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const RobotMessage*>(
      &from));
}

void RobotMessage::MergeFrom(const RobotMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.debug.RobotMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  alarms_.MergeFrom(from.alarms_);
  if (!from._internal_serial().empty()) {
    _internal_set_serial(from._internal_serial());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void RobotMessage::CopyFrom(const RobotMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.debug.RobotMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RobotMessage::IsInitialized() const {
  return true;
}

void RobotMessage::InternalSwap(RobotMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  alarms_.InternalSwap(&other->alarms_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serial_, lhs_arena,
      &other->serial_, rhs_arena
  );
}

std::string RobotMessage::GetTypeName() const {
  return "carbon.frontend.debug.RobotMessage";
}


// ===================================================================

class SetLogLevelRequest::_Internal {
 public:
};

SetLogLevelRequest::SetLogLevelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.debug.SetLogLevelRequest)
}
SetLogLevelRequest::SetLogLevelRequest(const SetLogLevelRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::MessageLite() {
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
  component_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    component_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_component().empty()) {
    component_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_component(), 
      GetArenaForAllocation());
  }
  ::memcpy(&level_, &from.level_,
    static_cast<size_t>(reinterpret_cast<char*>(&row_num_) -
    reinterpret_cast<char*>(&level_)) + sizeof(row_num_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.debug.SetLogLevelRequest)
}

inline void SetLogLevelRequest::SharedCtor() {
component_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  component_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&level_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&row_num_) -
    reinterpret_cast<char*>(&level_)) + sizeof(row_num_));
}

SetLogLevelRequest::~SetLogLevelRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.debug.SetLogLevelRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<std::string>();
}

inline void SetLogLevelRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  component_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetLogLevelRequest::ArenaDtor(void* object) {
  SetLogLevelRequest* _this = reinterpret_cast< SetLogLevelRequest* >(object);
  (void)_this;
}
void SetLogLevelRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetLogLevelRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetLogLevelRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.debug.SetLogLevelRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  component_.ClearToEmpty();
  ::memset(&level_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&row_num_) -
      reinterpret_cast<char*>(&level_)) + sizeof(row_num_));
  _internal_metadata_.Clear<std::string>();
}

const char* SetLogLevelRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.logging.LogLevel level = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_level(static_cast<::carbon::logging::LogLevel>(val));
        } else
          goto handle_unusual;
        continue;
      // string component = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_component();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, nullptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 row_num = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          row_num_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<std::string>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetLogLevelRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.debug.SetLogLevelRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.logging.LogLevel level = 1;
  if (this->_internal_level() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_level(), target);
  }

  // string component = 2;
  if (!this->_internal_component().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_component().data(), static_cast<int>(this->_internal_component().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.debug.SetLogLevelRequest.component");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_component(), target);
  }

  // int32 row_num = 3;
  if (this->_internal_row_num() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_row_num(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = stream->WriteRaw(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).data(),
        static_cast<int>(_internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size()), target);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.debug.SetLogLevelRequest)
  return target;
}

size_t SetLogLevelRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.debug.SetLogLevelRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string component = 2;
  if (!this->_internal_component().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_component());
  }

  // .carbon.logging.LogLevel level = 1;
  if (this->_internal_level() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_level());
  }

  // int32 row_num = 3;
  if (this->_internal_row_num() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_row_num());
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    total_size += _internal_metadata_.unknown_fields<std::string>(::PROTOBUF_NAMESPACE_ID::internal::GetEmptyString).size();
  }
  int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(total_size);
  SetCachedSize(cached_size);
  return total_size;
}

void SetLogLevelRequest::CheckTypeAndMergeFrom(
    const ::PROTOBUF_NAMESPACE_ID::MessageLite& from) {
  MergeFrom(*::PROTOBUF_NAMESPACE_ID::internal::DownCast<const SetLogLevelRequest*>(
      &from));
}

void SetLogLevelRequest::MergeFrom(const SetLogLevelRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.debug.SetLogLevelRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_component().empty()) {
    _internal_set_component(from._internal_component());
  }
  if (from._internal_level() != 0) {
    _internal_set_level(from._internal_level());
  }
  if (from._internal_row_num() != 0) {
    _internal_set_row_num(from._internal_row_num());
  }
  _internal_metadata_.MergeFrom<std::string>(from._internal_metadata_);
}

void SetLogLevelRequest::CopyFrom(const SetLogLevelRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.debug.SetLogLevelRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetLogLevelRequest::IsInitialized() const {
  return true;
}

void SetLogLevelRequest::InternalSwap(SetLogLevelRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &component_, lhs_arena,
      &other->component_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SetLogLevelRequest, row_num_)
      + sizeof(SetLogLevelRequest::row_num_)
      - PROTOBUF_FIELD_OFFSET(SetLogLevelRequest, level_)>(
          reinterpret_cast<char*>(&level_),
          reinterpret_cast<char*>(&other->level_));
}

std::string SetLogLevelRequest::GetTypeName() const {
  return "carbon.frontend.debug.SetLogLevelRequest";
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace debug
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::debug::RobotMessage* Arena::CreateMaybeMessage< ::carbon::frontend::debug::RobotMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::debug::RobotMessage >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::debug::SetLogLevelRequest* Arena::CreateMaybeMessage< ::carbon::frontend::debug::SetLogLevelRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::debug::SetLogLevelRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

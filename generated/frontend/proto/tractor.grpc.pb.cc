// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/tractor.proto

#include "frontend/proto/tractor.pb.h"
#include "frontend/proto/tractor.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace tractor {

static const char* TractorService_method_names[] = {
  "/carbon.frontend.tractor.TractorService/GetNextTractorIfState",
  "/carbon.frontend.tractor.TractorService/GetNextTractorSafetyState",
  "/carbon.frontend.tractor.TractorService/SetEnforcementPolicy",
};

std::unique_ptr< TractorService::Stub> TractorService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< TractorService::Stub> stub(new TractorService::Stub(channel, options));
  return stub;
}

TractorService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextTractorIfState_(TractorService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextTractorSafetyState_(TractorService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetEnforcementPolicy_(TractorService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status TractorService::Stub::GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorIfStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextTractorIfState_, context, request, response);
}

void TractorService::Stub::async::GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorIfStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextTractorIfState_, context, request, response, std::move(f));
}

void TractorService::Stub::async::GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextTractorIfState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* TractorService::Stub::PrepareAsyncGetNextTractorIfStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::tractor::GetNextTractorIfStateResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextTractorIfState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* TractorService::Stub::AsyncGetNextTractorIfStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextTractorIfStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TractorService::Stub::GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextTractorSafetyState_, context, request, response);
}

void TractorService::Stub::async::GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextTractorSafetyState_, context, request, response, std::move(f));
}

void TractorService::Stub::async::GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextTractorSafetyState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* TractorService::Stub::PrepareAsyncGetNextTractorSafetyStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextTractorSafetyState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* TractorService::Stub::AsyncGetNextTractorSafetyStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextTractorSafetyStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TractorService::Stub::SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::carbon::frontend::tractor::SetEnforcementPolicyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetEnforcementPolicy_, context, request, response);
}

void TractorService::Stub::async::SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::carbon::frontend::tractor::SetEnforcementPolicyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetEnforcementPolicy_, context, request, response, std::move(f));
}

void TractorService::Stub::async::SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetEnforcementPolicy_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* TractorService::Stub::PrepareAsyncSetEnforcementPolicyRaw(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::tractor::SetEnforcementPolicyResponse, ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetEnforcementPolicy_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* TractorService::Stub::AsyncSetEnforcementPolicyRaw(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetEnforcementPolicyRaw(context, request, cq);
  result->StartCall();
  return result;
}

TractorService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TractorService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TractorService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorIfStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TractorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::tractor::GetNextTractorIfStateResponse* resp) {
               return service->GetNextTractorIfState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TractorService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TractorService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TractorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* resp) {
               return service->GetNextTractorSafetyState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TractorService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TractorService::Service, ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::carbon::frontend::tractor::SetEnforcementPolicyResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TractorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* req,
             ::carbon::frontend::tractor::SetEnforcementPolicyResponse* resp) {
               return service->SetEnforcementPolicy(ctx, req, resp);
             }, this)));
}

TractorService::Service::~Service() {
}

::grpc::Status TractorService::Service::GetNextTractorIfState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TractorService::Service::GetNextTractorSafetyState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TractorService::Service::SetEnforcementPolicy(::grpc::ServerContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace tractor


"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

CameraTypeValue = typing___NewType('CameraTypeValue', builtin___int)
type___CameraTypeValue = CameraTypeValue
CameraType: _CameraType
class _CameraType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[CameraTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    ANY = typing___cast(CameraTypeValue, 0)
    PREDICT = typing___cast(CameraTypeValue, 1)
    TARGET = typing___cast(CameraTypeValue, 2)
    KILLCAM = typing___cast(CameraTypeValue, 3)
ANY = typing___cast(CameraTypeValue, 0)
PREDICT = typing___cast(CameraTypeValue, 1)
TARGET = typing___cast(CameraTypeValue, 2)
KILLCAM = typing___cast(CameraTypeValue, 3)

class CameraRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id"]) -> None: ...
type___CameraRequest = CameraRequest

class Camera(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    row_number: builtin___int = ...
    camera_id: typing___Text = ...
    type: type___CameraTypeValue = ...
    auto_focusable: builtin___bool = ...
    stream_host: typing___Text = ...
    stream_port: builtin___int = ...
    width: builtin___int = ...
    height: builtin___int = ...
    transpose: builtin___bool = ...
    connected: builtin___bool = ...

    @property
    def rtc_info(self) -> type___RTCInfo: ...

    def __init__(self,
        *,
        row_number : typing___Optional[builtin___int] = None,
        camera_id : typing___Optional[typing___Text] = None,
        type : typing___Optional[type___CameraTypeValue] = None,
        auto_focusable : typing___Optional[builtin___bool] = None,
        stream_host : typing___Optional[typing___Text] = None,
        stream_port : typing___Optional[builtin___int] = None,
        width : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        transpose : typing___Optional[builtin___bool] = None,
        connected : typing___Optional[builtin___bool] = None,
        rtc_info : typing___Optional[type___RTCInfo] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"rtc_info",b"rtc_info"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"auto_focusable",b"auto_focusable",u"camera_id",b"camera_id",u"connected",b"connected",u"height",b"height",u"row_number",b"row_number",u"rtc_info",b"rtc_info",u"stream_host",b"stream_host",u"stream_port",b"stream_port",u"transpose",b"transpose",u"type",b"type",u"width",b"width"]) -> None: ...
type___Camera = Camera

class RTCInfo(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    host_id: typing___Text = ...
    stream_id: typing___Text = ...

    def __init__(self,
        *,
        host_id : typing___Optional[typing___Text] = None,
        stream_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"host_id",b"host_id",u"stream_id",b"stream_id"]) -> None: ...
type___RTCInfo = RTCInfo

class CameraList(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def cameras(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Camera]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        cameras : typing___Optional[typing___Iterable[type___Camera]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cameras",b"cameras",u"ts",b"ts"]) -> None: ...
type___CameraList = CameraList

class CameraListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: type___CameraTypeValue = ...
    include_disconnected: builtin___bool = ...

    def __init__(self,
        *,
        type : typing___Optional[type___CameraTypeValue] = None,
        include_disconnected : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"include_disconnected",b"include_disconnected",u"type",b"type"]) -> None: ...
type___CameraListRequest = CameraListRequest

class NextCameraListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: type___CameraTypeValue = ...
    include_disconnected: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        type : typing___Optional[type___CameraTypeValue] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        include_disconnected : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"include_disconnected",b"include_disconnected",u"ts",b"ts",u"type",b"type"]) -> None: ...
type___NextCameraListRequest = NextCameraListRequest

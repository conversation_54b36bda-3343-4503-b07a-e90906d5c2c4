// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/image_stream.proto

#include "frontend/proto/image_stream.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace image_stream {
constexpr Annotations::Annotations(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : detections_(nullptr)
  , bands_(nullptr)
  , crosshair_x_(0)
  , crosshair_y_(0){}
struct AnnotationsDefaultTypeInternal {
  constexpr AnnotationsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AnnotationsDefaultTypeInternal() {}
  union {
    Annotations _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AnnotationsDefaultTypeInternal _Annotations_default_instance_;
constexpr Image::Image(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , annotations_(nullptr)
  , width_(0u)
  , height_(0u)
  , focus_(0){}
struct ImageDefaultTypeInternal {
  constexpr ImageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ImageDefaultTypeInternal() {}
  union {
    Image _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ImageDefaultTypeInternal _Image_default_instance_;
constexpr CameraImageRequest::CameraImageRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , annotated_(false)
  , include_annotations_metadata_(false)
  , dont_downsample_(false)
  , encode_as_png_(false)
  , encode_as_raw_(false){}
struct CameraImageRequestDefaultTypeInternal {
  constexpr CameraImageRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CameraImageRequestDefaultTypeInternal() {}
  union {
    CameraImageRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CameraImageRequestDefaultTypeInternal _CameraImageRequest_default_instance_;
constexpr GetPredictImageByTimestampRequest::GetPredictImageByTimestampRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , crop_around_x_(0)
  , crop_around_y_(0){}
struct GetPredictImageByTimestampRequestDefaultTypeInternal {
  constexpr GetPredictImageByTimestampRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPredictImageByTimestampRequestDefaultTypeInternal() {}
  union {
    GetPredictImageByTimestampRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPredictImageByTimestampRequestDefaultTypeInternal _GetPredictImageByTimestampRequest_default_instance_;
constexpr GetPredictImageByTimestampResponse::GetPredictImageByTimestampResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , center_x_(0)
  , center_y_(0){}
struct GetPredictImageByTimestampResponseDefaultTypeInternal {
  constexpr GetPredictImageByTimestampResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPredictImageByTimestampResponseDefaultTypeInternal() {}
  union {
    GetPredictImageByTimestampResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPredictImageByTimestampResponseDefaultTypeInternal _GetPredictImageByTimestampResponse_default_instance_;
constexpr PossiblePerspective::PossiblePerspective(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , crop_around_x_(0)
  , crop_around_y_(0){}
struct PossiblePerspectiveDefaultTypeInternal {
  constexpr PossiblePerspectiveDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PossiblePerspectiveDefaultTypeInternal() {}
  union {
    PossiblePerspective _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PossiblePerspectiveDefaultTypeInternal _PossiblePerspective_default_instance_;
constexpr GetMultiPredictPerspectivesRequest::GetMultiPredictPerspectivesRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : perspectives_()
  , cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , requested_perspectives_(0){}
struct GetMultiPredictPerspectivesRequestDefaultTypeInternal {
  constexpr GetMultiPredictPerspectivesRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetMultiPredictPerspectivesRequestDefaultTypeInternal() {}
  union {
    GetMultiPredictPerspectivesRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetMultiPredictPerspectivesRequestDefaultTypeInternal _GetMultiPredictPerspectivesRequest_default_instance_;
constexpr CentroidPerspective::CentroidPerspective(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : data_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , center_x_(0)
  , center_y_(0){}
struct CentroidPerspectiveDefaultTypeInternal {
  constexpr CentroidPerspectiveDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CentroidPerspectiveDefaultTypeInternal() {}
  union {
    CentroidPerspective _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CentroidPerspectiveDefaultTypeInternal _CentroidPerspective_default_instance_;
constexpr GetMultiPredictPerspectivesResponse::GetMultiPredictPerspectivesResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : perspectives_(){}
struct GetMultiPredictPerspectivesResponseDefaultTypeInternal {
  constexpr GetMultiPredictPerspectivesResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetMultiPredictPerspectivesResponseDefaultTypeInternal() {}
  union {
    GetMultiPredictPerspectivesResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetMultiPredictPerspectivesResponseDefaultTypeInternal _GetMultiPredictPerspectivesResponse_default_instance_;
}  // namespace image_stream
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[9];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fimage_5fstream_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fimage_5fstream_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fimage_5fstream_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Annotations, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Annotations, detections_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Annotations, bands_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Annotations, crosshair_x_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Annotations, crosshair_y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Image, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Image, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Image, width_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Image, height_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Image, focus_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Image, data_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::Image, annotations_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CameraImageRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CameraImageRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CameraImageRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CameraImageRequest, annotated_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CameraImageRequest, include_annotations_metadata_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CameraImageRequest, dont_downsample_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CameraImageRequest, encode_as_png_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CameraImageRequest, encode_as_raw_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, crop_around_x_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, crop_around_y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampResponse, data_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampResponse, center_x_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetPredictImageByTimestampResponse, center_y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::PossiblePerspective, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::PossiblePerspective, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::PossiblePerspective, crop_around_x_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::PossiblePerspective, crop_around_y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, perspectives_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, requested_perspectives_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CentroidPerspective, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CentroidPerspective, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CentroidPerspective, center_x_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CentroidPerspective, center_y_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::CentroidPerspective, data_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse, perspectives_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::image_stream::Annotations)},
  { 10, -1, -1, sizeof(::carbon::frontend::image_stream::Image)},
  { 22, -1, -1, sizeof(::carbon::frontend::image_stream::CameraImageRequest)},
  { 35, -1, -1, sizeof(::carbon::frontend::image_stream::GetPredictImageByTimestampRequest)},
  { 45, -1, -1, sizeof(::carbon::frontend::image_stream::GetPredictImageByTimestampResponse)},
  { 54, -1, -1, sizeof(::carbon::frontend::image_stream::PossiblePerspective)},
  { 63, -1, -1, sizeof(::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest)},
  { 72, -1, -1, sizeof(::carbon::frontend::image_stream::CentroidPerspective)},
  { 82, -1, -1, sizeof(::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_Annotations_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_Image_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_CameraImageRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_GetPredictImageByTimestampRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_GetPredictImageByTimestampResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_PossiblePerspective_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_GetMultiPredictPerspectivesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_CentroidPerspective_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::image_stream::_GetMultiPredictPerspectivesResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fimage_5fstream_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n!frontend/proto/image_stream.proto\022\034car"
  "bon.frontend.image_stream\032\031frontend/prot"
  "o/util.proto\032\'weed_tracking/proto/weed_t"
  "racking.proto\"\213\001\n\013Annotations\022-\n\ndetecti"
  "ons\030\001 \001(\0132\031.weed_tracking.Detections\022#\n\005"
  "bands\030\002 \001(\0132\024.weed_tracking.Bands\022\023\n\013cro"
  "sshair_x\030\003 \001(\005\022\023\n\013crosshair_y\030\004 \001(\005\"\260\001\n\005"
  "Image\022+\n\002ts\030\001 \001(\0132\037.carbon.frontend.util"
  ".Timestamp\022\r\n\005width\030\002 \001(\r\022\016\n\006height\030\003 \001("
  "\r\022\r\n\005focus\030\004 \001(\001\022\014\n\004data\030\005 \001(\014\022>\n\013annota"
  "tions\030\006 \001(\0132).carbon.frontend.image_stre"
  "am.Annotations\"\321\001\n\022CameraImageRequest\022\016\n"
  "\006cam_id\030\001 \001(\t\022+\n\002ts\030\002 \001(\0132\037.carbon.front"
  "end.util.Timestamp\022\021\n\tannotated\030\003 \001(\010\022$\n"
  "\034include_annotations_metadata\030\004 \001(\010\022\027\n\017d"
  "ont_downsample\030\005 \001(\010\022\025\n\rencode_as_png\030\006 "
  "\001(\010\022\025\n\rencode_as_raw\030\007 \001(\010\"\216\001\n!GetPredic"
  "tImageByTimestampRequest\022\016\n\006cam_id\030\001 \001(\t"
  "\022+\n\002ts\030\002 \001(\0132\037.carbon.frontend.util.Time"
  "stamp\022\025\n\rcrop_around_x\030\003 \001(\005\022\025\n\rcrop_aro"
  "und_y\030\004 \001(\005\"V\n\"GetPredictImageByTimestam"
  "pResponse\022\014\n\004data\030\001 \001(\014\022\020\n\010center_x\030\002 \001("
  "\005\022\020\n\010center_y\030\003 \001(\005\"p\n\023PossiblePerspecti"
  "ve\022+\n\002ts\030\001 \001(\0132\037.carbon.frontend.util.Ti"
  "mestamp\022\025\n\rcrop_around_x\030\002 \001(\005\022\025\n\rcrop_a"
  "round_y\030\003 \001(\005\"\235\001\n\"GetMultiPredictPerspec"
  "tivesRequest\022\016\n\006cam_id\030\001 \001(\t\022G\n\014perspect"
  "ives\030\002 \003(\01321.carbon.frontend.image_strea"
  "m.PossiblePerspective\022\036\n\026requested_persp"
  "ectives\030\003 \001(\005\"t\n\023CentroidPerspective\022+\n\002"
  "ts\030\001 \001(\0132\037.carbon.frontend.util.Timestam"
  "p\022\020\n\010center_x\030\002 \001(\005\022\020\n\010center_y\030\003 \001(\005\022\014\n"
  "\004data\030\004 \001(\014\"n\n#GetMultiPredictPerspectiv"
  "esResponse\022G\n\014perspectives\030\001 \003(\01321.carbo"
  "n.frontend.image_stream.CentroidPerspect"
  "ive2\310\003\n\022ImageStreamService\022k\n\022GetNextCam"
  "eraImage\0220.carbon.frontend.image_stream."
  "CameraImageRequest\032#.carbon.frontend.ima"
  "ge_stream.Image\022\237\001\n\032GetPredictImageByTim"
  "estamp\022\?.carbon.frontend.image_stream.Ge"
  "tPredictImageByTimestampRequest\032@.carbon"
  ".frontend.image_stream.GetPredictImageBy"
  "TimestampResponse\022\242\001\n\033GetMultiPredictPer"
  "spectives\<EMAIL>.image_stream"
  ".GetMultiPredictPerspectivesRequest\032A.ca"
  "rbon.frontend.image_stream.GetMultiPredi"
  "ctPerspectivesResponseB\020Z\016proto/frontend"
  "b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
  &::descriptor_table_weed_5ftracking_2fproto_2fweed_5ftracking_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto = {
  false, false, 1888, descriptor_table_protodef_frontend_2fproto_2fimage_5fstream_2eproto, "frontend/proto/image_stream.proto", 
  &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once, descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_deps, 2, 9,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fimage_5fstream_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto, file_level_enum_descriptors_frontend_2fproto_2fimage_5fstream_2eproto, file_level_service_descriptors_frontend_2fproto_2fimage_5fstream_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fimage_5fstream_2eproto(&descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto);
namespace carbon {
namespace frontend {
namespace image_stream {

// ===================================================================

class Annotations::_Internal {
 public:
  static const ::weed_tracking::Detections& detections(const Annotations* msg);
  static const ::weed_tracking::Bands& bands(const Annotations* msg);
};

const ::weed_tracking::Detections&
Annotations::_Internal::detections(const Annotations* msg) {
  return *msg->detections_;
}
const ::weed_tracking::Bands&
Annotations::_Internal::bands(const Annotations* msg) {
  return *msg->bands_;
}
void Annotations::clear_detections() {
  if (GetArenaForAllocation() == nullptr && detections_ != nullptr) {
    delete detections_;
  }
  detections_ = nullptr;
}
void Annotations::clear_bands() {
  if (GetArenaForAllocation() == nullptr && bands_ != nullptr) {
    delete bands_;
  }
  bands_ = nullptr;
}
Annotations::Annotations(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.Annotations)
}
Annotations::Annotations(const Annotations& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_detections()) {
    detections_ = new ::weed_tracking::Detections(*from.detections_);
  } else {
    detections_ = nullptr;
  }
  if (from._internal_has_bands()) {
    bands_ = new ::weed_tracking::Bands(*from.bands_);
  } else {
    bands_ = nullptr;
  }
  ::memcpy(&crosshair_x_, &from.crosshair_x_,
    static_cast<size_t>(reinterpret_cast<char*>(&crosshair_y_) -
    reinterpret_cast<char*>(&crosshair_x_)) + sizeof(crosshair_y_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.Annotations)
}

inline void Annotations::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&detections_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&crosshair_y_) -
    reinterpret_cast<char*>(&detections_)) + sizeof(crosshair_y_));
}

Annotations::~Annotations() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.Annotations)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Annotations::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete detections_;
  if (this != internal_default_instance()) delete bands_;
}

void Annotations::ArenaDtor(void* object) {
  Annotations* _this = reinterpret_cast< Annotations* >(object);
  (void)_this;
}
void Annotations::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Annotations::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Annotations::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.Annotations)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && detections_ != nullptr) {
    delete detections_;
  }
  detections_ = nullptr;
  if (GetArenaForAllocation() == nullptr && bands_ != nullptr) {
    delete bands_;
  }
  bands_ = nullptr;
  ::memset(&crosshair_x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&crosshair_y_) -
      reinterpret_cast<char*>(&crosshair_x_)) + sizeof(crosshair_y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Annotations::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .weed_tracking.Detections detections = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_detections(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .weed_tracking.Bands bands = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_bands(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 crosshair_x = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          crosshair_x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 crosshair_y = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          crosshair_y_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Annotations::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.Annotations)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .weed_tracking.Detections detections = 1;
  if (this->_internal_has_detections()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::detections(this), target, stream);
  }

  // .weed_tracking.Bands bands = 2;
  if (this->_internal_has_bands()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::bands(this), target, stream);
  }

  // int32 crosshair_x = 3;
  if (this->_internal_crosshair_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_crosshair_x(), target);
  }

  // int32 crosshair_y = 4;
  if (this->_internal_crosshair_y() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_crosshair_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.Annotations)
  return target;
}

size_t Annotations::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.Annotations)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .weed_tracking.Detections detections = 1;
  if (this->_internal_has_detections()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *detections_);
  }

  // .weed_tracking.Bands bands = 2;
  if (this->_internal_has_bands()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bands_);
  }

  // int32 crosshair_x = 3;
  if (this->_internal_crosshair_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_crosshair_x());
  }

  // int32 crosshair_y = 4;
  if (this->_internal_crosshair_y() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_crosshair_y());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Annotations::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Annotations::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Annotations::GetClassData() const { return &_class_data_; }

void Annotations::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Annotations *>(to)->MergeFrom(
      static_cast<const Annotations &>(from));
}


void Annotations::MergeFrom(const Annotations& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.Annotations)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_detections()) {
    _internal_mutable_detections()->::weed_tracking::Detections::MergeFrom(from._internal_detections());
  }
  if (from._internal_has_bands()) {
    _internal_mutable_bands()->::weed_tracking::Bands::MergeFrom(from._internal_bands());
  }
  if (from._internal_crosshair_x() != 0) {
    _internal_set_crosshair_x(from._internal_crosshair_x());
  }
  if (from._internal_crosshair_y() != 0) {
    _internal_set_crosshair_y(from._internal_crosshair_y());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Annotations::CopyFrom(const Annotations& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.Annotations)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Annotations::IsInitialized() const {
  return true;
}

void Annotations::InternalSwap(Annotations* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Annotations, crosshair_y_)
      + sizeof(Annotations::crosshair_y_)
      - PROTOBUF_FIELD_OFFSET(Annotations, detections_)>(
          reinterpret_cast<char*>(&detections_),
          reinterpret_cast<char*>(&other->detections_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Annotations::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[0]);
}

// ===================================================================

class Image::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const Image* msg);
  static const ::carbon::frontend::image_stream::Annotations& annotations(const Image* msg);
};

const ::carbon::frontend::util::Timestamp&
Image::_Internal::ts(const Image* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::image_stream::Annotations&
Image::_Internal::annotations(const Image* msg) {
  return *msg->annotations_;
}
void Image::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
Image::Image(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.Image)
}
Image::Image(const Image& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_annotations()) {
    annotations_ = new ::carbon::frontend::image_stream::Annotations(*from.annotations_);
  } else {
    annotations_ = nullptr;
  }
  ::memcpy(&width_, &from.width_,
    static_cast<size_t>(reinterpret_cast<char*>(&focus_) -
    reinterpret_cast<char*>(&width_)) + sizeof(focus_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.Image)
}

inline void Image::SharedCtor() {
data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&focus_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(focus_));
}

Image::~Image() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.Image)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Image::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete annotations_;
}

void Image::ArenaDtor(void* object) {
  Image* _this = reinterpret_cast< Image* >(object);
  (void)_this;
}
void Image::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Image::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Image::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.Image)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && annotations_ != nullptr) {
    delete annotations_;
  }
  annotations_ = nullptr;
  ::memset(&width_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&focus_) -
      reinterpret_cast<char*>(&width_)) + sizeof(focus_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Image::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 width = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 height = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double focus = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          focus_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // bytes data = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.image_stream.Annotations annotations = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_annotations(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Image::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.Image)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // uint32 width = 2;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_width(), target);
  }

  // uint32 height = 3;
  if (this->_internal_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_height(), target);
  }

  // double focus = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_focus = this->_internal_focus();
  uint64_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_focus(), target);
  }

  // bytes data = 5;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        5, this->_internal_data(), target);
  }

  // .carbon.frontend.image_stream.Annotations annotations = 6;
  if (this->_internal_has_annotations()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::annotations(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.Image)
  return target;
}

size_t Image::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.Image)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 5;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.image_stream.Annotations annotations = 6;
  if (this->_internal_has_annotations()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *annotations_);
  }

  // uint32 width = 2;
  if (this->_internal_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_width());
  }

  // uint32 height = 3;
  if (this->_internal_height() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_height());
  }

  // double focus = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_focus = this->_internal_focus();
  uint64_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Image::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Image::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Image::GetClassData() const { return &_class_data_; }

void Image::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Image *>(to)->MergeFrom(
      static_cast<const Image &>(from));
}


void Image::MergeFrom(const Image& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.Image)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _internal_set_data(from._internal_data());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_annotations()) {
    _internal_mutable_annotations()->::carbon::frontend::image_stream::Annotations::MergeFrom(from._internal_annotations());
  }
  if (from._internal_width() != 0) {
    _internal_set_width(from._internal_width());
  }
  if (from._internal_height() != 0) {
    _internal_set_height(from._internal_height());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_focus = from._internal_focus();
  uint64_t raw_focus;
  memcpy(&raw_focus, &tmp_focus, sizeof(tmp_focus));
  if (raw_focus != 0) {
    _internal_set_focus(from._internal_focus());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Image::CopyFrom(const Image& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.Image)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Image::IsInitialized() const {
  return true;
}

void Image::InternalSwap(Image* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &data_, lhs_arena,
      &other->data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Image, focus_)
      + sizeof(Image::focus_)
      - PROTOBUF_FIELD_OFFSET(Image, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Image::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[1]);
}

// ===================================================================

class CameraImageRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const CameraImageRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
CameraImageRequest::_Internal::ts(const CameraImageRequest* msg) {
  return *msg->ts_;
}
void CameraImageRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
CameraImageRequest::CameraImageRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.CameraImageRequest)
}
CameraImageRequest::CameraImageRequest(const CameraImageRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&annotated_, &from.annotated_,
    static_cast<size_t>(reinterpret_cast<char*>(&encode_as_raw_) -
    reinterpret_cast<char*>(&annotated_)) + sizeof(encode_as_raw_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.CameraImageRequest)
}

inline void CameraImageRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&encode_as_raw_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(encode_as_raw_));
}

CameraImageRequest::~CameraImageRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.CameraImageRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CameraImageRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void CameraImageRequest::ArenaDtor(void* object) {
  CameraImageRequest* _this = reinterpret_cast< CameraImageRequest* >(object);
  (void)_this;
}
void CameraImageRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CameraImageRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CameraImageRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.CameraImageRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&annotated_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&encode_as_raw_) -
      reinterpret_cast<char*>(&annotated_)) + sizeof(encode_as_raw_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CameraImageRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.image_stream.CameraImageRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool annotated = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          annotated_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool include_annotations_metadata = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          include_annotations_metadata_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool dont_downsample = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          dont_downsample_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool encode_as_png = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          encode_as_png_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool encode_as_raw = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          encode_as_raw_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CameraImageRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.CameraImageRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.image_stream.CameraImageRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  // bool annotated = 3;
  if (this->_internal_annotated() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_annotated(), target);
  }

  // bool include_annotations_metadata = 4;
  if (this->_internal_include_annotations_metadata() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_include_annotations_metadata(), target);
  }

  // bool dont_downsample = 5;
  if (this->_internal_dont_downsample() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_dont_downsample(), target);
  }

  // bool encode_as_png = 6;
  if (this->_internal_encode_as_png() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_encode_as_png(), target);
  }

  // bool encode_as_raw = 7;
  if (this->_internal_encode_as_raw() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_encode_as_raw(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.CameraImageRequest)
  return target;
}

size_t CameraImageRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.CameraImageRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // bool annotated = 3;
  if (this->_internal_annotated() != 0) {
    total_size += 1 + 1;
  }

  // bool include_annotations_metadata = 4;
  if (this->_internal_include_annotations_metadata() != 0) {
    total_size += 1 + 1;
  }

  // bool dont_downsample = 5;
  if (this->_internal_dont_downsample() != 0) {
    total_size += 1 + 1;
  }

  // bool encode_as_png = 6;
  if (this->_internal_encode_as_png() != 0) {
    total_size += 1 + 1;
  }

  // bool encode_as_raw = 7;
  if (this->_internal_encode_as_raw() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CameraImageRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CameraImageRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CameraImageRequest::GetClassData() const { return &_class_data_; }

void CameraImageRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CameraImageRequest *>(to)->MergeFrom(
      static_cast<const CameraImageRequest &>(from));
}


void CameraImageRequest::MergeFrom(const CameraImageRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.CameraImageRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_annotated() != 0) {
    _internal_set_annotated(from._internal_annotated());
  }
  if (from._internal_include_annotations_metadata() != 0) {
    _internal_set_include_annotations_metadata(from._internal_include_annotations_metadata());
  }
  if (from._internal_dont_downsample() != 0) {
    _internal_set_dont_downsample(from._internal_dont_downsample());
  }
  if (from._internal_encode_as_png() != 0) {
    _internal_set_encode_as_png(from._internal_encode_as_png());
  }
  if (from._internal_encode_as_raw() != 0) {
    _internal_set_encode_as_raw(from._internal_encode_as_raw());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CameraImageRequest::CopyFrom(const CameraImageRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.CameraImageRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CameraImageRequest::IsInitialized() const {
  return true;
}

void CameraImageRequest::InternalSwap(CameraImageRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CameraImageRequest, encode_as_raw_)
      + sizeof(CameraImageRequest::encode_as_raw_)
      - PROTOBUF_FIELD_OFFSET(CameraImageRequest, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CameraImageRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[2]);
}

// ===================================================================

class GetPredictImageByTimestampRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetPredictImageByTimestampRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetPredictImageByTimestampRequest::_Internal::ts(const GetPredictImageByTimestampRequest* msg) {
  return *msg->ts_;
}
void GetPredictImageByTimestampRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetPredictImageByTimestampRequest::GetPredictImageByTimestampRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
}
GetPredictImageByTimestampRequest::GetPredictImageByTimestampRequest(const GetPredictImageByTimestampRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&crop_around_x_, &from.crop_around_x_,
    static_cast<size_t>(reinterpret_cast<char*>(&crop_around_y_) -
    reinterpret_cast<char*>(&crop_around_x_)) + sizeof(crop_around_y_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
}

inline void GetPredictImageByTimestampRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&crop_around_y_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(crop_around_y_));
}

GetPredictImageByTimestampRequest::~GetPredictImageByTimestampRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPredictImageByTimestampRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetPredictImageByTimestampRequest::ArenaDtor(void* object) {
  GetPredictImageByTimestampRequest* _this = reinterpret_cast< GetPredictImageByTimestampRequest* >(object);
  (void)_this;
}
void GetPredictImageByTimestampRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPredictImageByTimestampRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPredictImageByTimestampRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&crop_around_x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&crop_around_y_) -
      reinterpret_cast<char*>(&crop_around_x_)) + sizeof(crop_around_y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPredictImageByTimestampRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.image_stream.GetPredictImageByTimestampRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 crop_around_x = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          crop_around_x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 crop_around_y = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          crop_around_y_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPredictImageByTimestampRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.image_stream.GetPredictImageByTimestampRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  // int32 crop_around_x = 3;
  if (this->_internal_crop_around_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_crop_around_x(), target);
  }

  // int32 crop_around_y = 4;
  if (this->_internal_crop_around_y() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_crop_around_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
  return target;
}

size_t GetPredictImageByTimestampRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // int32 crop_around_x = 3;
  if (this->_internal_crop_around_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_crop_around_x());
  }

  // int32 crop_around_y = 4;
  if (this->_internal_crop_around_y() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_crop_around_y());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPredictImageByTimestampRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPredictImageByTimestampRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPredictImageByTimestampRequest::GetClassData() const { return &_class_data_; }

void GetPredictImageByTimestampRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPredictImageByTimestampRequest *>(to)->MergeFrom(
      static_cast<const GetPredictImageByTimestampRequest &>(from));
}


void GetPredictImageByTimestampRequest::MergeFrom(const GetPredictImageByTimestampRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_crop_around_x() != 0) {
    _internal_set_crop_around_x(from._internal_crop_around_x());
  }
  if (from._internal_crop_around_y() != 0) {
    _internal_set_crop_around_y(from._internal_crop_around_y());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPredictImageByTimestampRequest::CopyFrom(const GetPredictImageByTimestampRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPredictImageByTimestampRequest::IsInitialized() const {
  return true;
}

void GetPredictImageByTimestampRequest::InternalSwap(GetPredictImageByTimestampRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetPredictImageByTimestampRequest, crop_around_y_)
      + sizeof(GetPredictImageByTimestampRequest::crop_around_y_)
      - PROTOBUF_FIELD_OFFSET(GetPredictImageByTimestampRequest, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPredictImageByTimestampRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[3]);
}

// ===================================================================

class GetPredictImageByTimestampResponse::_Internal {
 public:
};

GetPredictImageByTimestampResponse::GetPredictImageByTimestampResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
}
GetPredictImageByTimestampResponse::GetPredictImageByTimestampResponse(const GetPredictImageByTimestampResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArenaForAllocation());
  }
  ::memcpy(&center_x_, &from.center_x_,
    static_cast<size_t>(reinterpret_cast<char*>(&center_y_) -
    reinterpret_cast<char*>(&center_x_)) + sizeof(center_y_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
}

inline void GetPredictImageByTimestampResponse::SharedCtor() {
data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&center_x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&center_y_) -
    reinterpret_cast<char*>(&center_x_)) + sizeof(center_y_));
}

GetPredictImageByTimestampResponse::~GetPredictImageByTimestampResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPredictImageByTimestampResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetPredictImageByTimestampResponse::ArenaDtor(void* object) {
  GetPredictImageByTimestampResponse* _this = reinterpret_cast< GetPredictImageByTimestampResponse* >(object);
  (void)_this;
}
void GetPredictImageByTimestampResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPredictImageByTimestampResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPredictImageByTimestampResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_.ClearToEmpty();
  ::memset(&center_x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&center_y_) -
      reinterpret_cast<char*>(&center_x_)) + sizeof(center_y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPredictImageByTimestampResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes data = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 center_x = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          center_x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 center_y = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          center_y_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPredictImageByTimestampResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes data = 1;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_data(), target);
  }

  // int32 center_x = 2;
  if (this->_internal_center_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_center_x(), target);
  }

  // int32 center_y = 3;
  if (this->_internal_center_y() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_center_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
  return target;
}

size_t GetPredictImageByTimestampResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 1;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  // int32 center_x = 2;
  if (this->_internal_center_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_center_x());
  }

  // int32 center_y = 3;
  if (this->_internal_center_y() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_center_y());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPredictImageByTimestampResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPredictImageByTimestampResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPredictImageByTimestampResponse::GetClassData() const { return &_class_data_; }

void GetPredictImageByTimestampResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPredictImageByTimestampResponse *>(to)->MergeFrom(
      static_cast<const GetPredictImageByTimestampResponse &>(from));
}


void GetPredictImageByTimestampResponse::MergeFrom(const GetPredictImageByTimestampResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _internal_set_data(from._internal_data());
  }
  if (from._internal_center_x() != 0) {
    _internal_set_center_x(from._internal_center_x());
  }
  if (from._internal_center_y() != 0) {
    _internal_set_center_y(from._internal_center_y());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPredictImageByTimestampResponse::CopyFrom(const GetPredictImageByTimestampResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPredictImageByTimestampResponse::IsInitialized() const {
  return true;
}

void GetPredictImageByTimestampResponse::InternalSwap(GetPredictImageByTimestampResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &data_, lhs_arena,
      &other->data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetPredictImageByTimestampResponse, center_y_)
      + sizeof(GetPredictImageByTimestampResponse::center_y_)
      - PROTOBUF_FIELD_OFFSET(GetPredictImageByTimestampResponse, center_x_)>(
          reinterpret_cast<char*>(&center_x_),
          reinterpret_cast<char*>(&other->center_x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPredictImageByTimestampResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[4]);
}

// ===================================================================

class PossiblePerspective::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const PossiblePerspective* msg);
};

const ::carbon::frontend::util::Timestamp&
PossiblePerspective::_Internal::ts(const PossiblePerspective* msg) {
  return *msg->ts_;
}
void PossiblePerspective::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
PossiblePerspective::PossiblePerspective(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.PossiblePerspective)
}
PossiblePerspective::PossiblePerspective(const PossiblePerspective& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&crop_around_x_, &from.crop_around_x_,
    static_cast<size_t>(reinterpret_cast<char*>(&crop_around_y_) -
    reinterpret_cast<char*>(&crop_around_x_)) + sizeof(crop_around_y_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.PossiblePerspective)
}

inline void PossiblePerspective::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&crop_around_y_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(crop_around_y_));
}

PossiblePerspective::~PossiblePerspective() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.PossiblePerspective)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PossiblePerspective::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void PossiblePerspective::ArenaDtor(void* object) {
  PossiblePerspective* _this = reinterpret_cast< PossiblePerspective* >(object);
  (void)_this;
}
void PossiblePerspective::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PossiblePerspective::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PossiblePerspective::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.PossiblePerspective)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&crop_around_x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&crop_around_y_) -
      reinterpret_cast<char*>(&crop_around_x_)) + sizeof(crop_around_y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PossiblePerspective::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 crop_around_x = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          crop_around_x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 crop_around_y = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          crop_around_y_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PossiblePerspective::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.PossiblePerspective)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // int32 crop_around_x = 2;
  if (this->_internal_crop_around_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_crop_around_x(), target);
  }

  // int32 crop_around_y = 3;
  if (this->_internal_crop_around_y() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_crop_around_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.PossiblePerspective)
  return target;
}

size_t PossiblePerspective::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.PossiblePerspective)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // int32 crop_around_x = 2;
  if (this->_internal_crop_around_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_crop_around_x());
  }

  // int32 crop_around_y = 3;
  if (this->_internal_crop_around_y() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_crop_around_y());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PossiblePerspective::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PossiblePerspective::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PossiblePerspective::GetClassData() const { return &_class_data_; }

void PossiblePerspective::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PossiblePerspective *>(to)->MergeFrom(
      static_cast<const PossiblePerspective &>(from));
}


void PossiblePerspective::MergeFrom(const PossiblePerspective& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.PossiblePerspective)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_crop_around_x() != 0) {
    _internal_set_crop_around_x(from._internal_crop_around_x());
  }
  if (from._internal_crop_around_y() != 0) {
    _internal_set_crop_around_y(from._internal_crop_around_y());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PossiblePerspective::CopyFrom(const PossiblePerspective& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.PossiblePerspective)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PossiblePerspective::IsInitialized() const {
  return true;
}

void PossiblePerspective::InternalSwap(PossiblePerspective* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PossiblePerspective, crop_around_y_)
      + sizeof(PossiblePerspective::crop_around_y_)
      - PROTOBUF_FIELD_OFFSET(PossiblePerspective, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PossiblePerspective::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[5]);
}

// ===================================================================

class GetMultiPredictPerspectivesRequest::_Internal {
 public:
};

GetMultiPredictPerspectivesRequest::GetMultiPredictPerspectivesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  perspectives_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
}
GetMultiPredictPerspectivesRequest::GetMultiPredictPerspectivesRequest(const GetMultiPredictPerspectivesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      perspectives_(from.perspectives_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  requested_perspectives_ = from.requested_perspectives_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
}

inline void GetMultiPredictPerspectivesRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
requested_perspectives_ = 0;
}

GetMultiPredictPerspectivesRequest::~GetMultiPredictPerspectivesRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetMultiPredictPerspectivesRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetMultiPredictPerspectivesRequest::ArenaDtor(void* object) {
  GetMultiPredictPerspectivesRequest* _this = reinterpret_cast< GetMultiPredictPerspectivesRequest* >(object);
  (void)_this;
}
void GetMultiPredictPerspectivesRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetMultiPredictPerspectivesRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetMultiPredictPerspectivesRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  perspectives_.Clear();
  cam_id_.ClearToEmpty();
  requested_perspectives_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetMultiPredictPerspectivesRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.image_stream.PossiblePerspective perspectives = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_perspectives(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 requested_perspectives = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          requested_perspectives_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetMultiPredictPerspectivesRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  // repeated .carbon.frontend.image_stream.PossiblePerspective perspectives = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_perspectives_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_perspectives(i), target, stream);
  }

  // int32 requested_perspectives = 3;
  if (this->_internal_requested_perspectives() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_requested_perspectives(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
  return target;
}

size_t GetMultiPredictPerspectivesRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.image_stream.PossiblePerspective perspectives = 2;
  total_size += 1UL * this->_internal_perspectives_size();
  for (const auto& msg : this->perspectives_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // int32 requested_perspectives = 3;
  if (this->_internal_requested_perspectives() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_requested_perspectives());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetMultiPredictPerspectivesRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetMultiPredictPerspectivesRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetMultiPredictPerspectivesRequest::GetClassData() const { return &_class_data_; }

void GetMultiPredictPerspectivesRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetMultiPredictPerspectivesRequest *>(to)->MergeFrom(
      static_cast<const GetMultiPredictPerspectivesRequest &>(from));
}


void GetMultiPredictPerspectivesRequest::MergeFrom(const GetMultiPredictPerspectivesRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  perspectives_.MergeFrom(from.perspectives_);
  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  if (from._internal_requested_perspectives() != 0) {
    _internal_set_requested_perspectives(from._internal_requested_perspectives());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetMultiPredictPerspectivesRequest::CopyFrom(const GetMultiPredictPerspectivesRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetMultiPredictPerspectivesRequest::IsInitialized() const {
  return true;
}

void GetMultiPredictPerspectivesRequest::InternalSwap(GetMultiPredictPerspectivesRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  perspectives_.InternalSwap(&other->perspectives_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  swap(requested_perspectives_, other->requested_perspectives_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetMultiPredictPerspectivesRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[6]);
}

// ===================================================================

class CentroidPerspective::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const CentroidPerspective* msg);
};

const ::carbon::frontend::util::Timestamp&
CentroidPerspective::_Internal::ts(const CentroidPerspective* msg) {
  return *msg->ts_;
}
void CentroidPerspective::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
CentroidPerspective::CentroidPerspective(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.CentroidPerspective)
}
CentroidPerspective::CentroidPerspective(const CentroidPerspective& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_data().empty()) {
    data_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_data(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&center_x_, &from.center_x_,
    static_cast<size_t>(reinterpret_cast<char*>(&center_y_) -
    reinterpret_cast<char*>(&center_x_)) + sizeof(center_y_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.CentroidPerspective)
}

inline void CentroidPerspective::SharedCtor() {
data_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  data_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&center_y_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(center_y_));
}

CentroidPerspective::~CentroidPerspective() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.CentroidPerspective)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CentroidPerspective::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  data_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void CentroidPerspective::ArenaDtor(void* object) {
  CentroidPerspective* _this = reinterpret_cast< CentroidPerspective* >(object);
  (void)_this;
}
void CentroidPerspective::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CentroidPerspective::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CentroidPerspective::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.CentroidPerspective)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  data_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&center_x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&center_y_) -
      reinterpret_cast<char*>(&center_x_)) + sizeof(center_y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CentroidPerspective::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 center_x = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          center_x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 center_y = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          center_y_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bytes data = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_data();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CentroidPerspective::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.CentroidPerspective)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // int32 center_x = 2;
  if (this->_internal_center_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_center_x(), target);
  }

  // int32 center_y = 3;
  if (this->_internal_center_y() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_center_y(), target);
  }

  // bytes data = 4;
  if (!this->_internal_data().empty()) {
    target = stream->WriteBytesMaybeAliased(
        4, this->_internal_data(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.CentroidPerspective)
  return target;
}

size_t CentroidPerspective::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.CentroidPerspective)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bytes data = 4;
  if (!this->_internal_data().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_data());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // int32 center_x = 2;
  if (this->_internal_center_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_center_x());
  }

  // int32 center_y = 3;
  if (this->_internal_center_y() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_center_y());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CentroidPerspective::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CentroidPerspective::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CentroidPerspective::GetClassData() const { return &_class_data_; }

void CentroidPerspective::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CentroidPerspective *>(to)->MergeFrom(
      static_cast<const CentroidPerspective &>(from));
}


void CentroidPerspective::MergeFrom(const CentroidPerspective& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.CentroidPerspective)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_data().empty()) {
    _internal_set_data(from._internal_data());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_center_x() != 0) {
    _internal_set_center_x(from._internal_center_x());
  }
  if (from._internal_center_y() != 0) {
    _internal_set_center_y(from._internal_center_y());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CentroidPerspective::CopyFrom(const CentroidPerspective& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.CentroidPerspective)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CentroidPerspective::IsInitialized() const {
  return true;
}

void CentroidPerspective::InternalSwap(CentroidPerspective* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &data_, lhs_arena,
      &other->data_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CentroidPerspective, center_y_)
      + sizeof(CentroidPerspective::center_y_)
      - PROTOBUF_FIELD_OFFSET(CentroidPerspective, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CentroidPerspective::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[7]);
}

// ===================================================================

class GetMultiPredictPerspectivesResponse::_Internal {
 public:
};

GetMultiPredictPerspectivesResponse::GetMultiPredictPerspectivesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  perspectives_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
}
GetMultiPredictPerspectivesResponse::GetMultiPredictPerspectivesResponse(const GetMultiPredictPerspectivesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      perspectives_(from.perspectives_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
}

inline void GetMultiPredictPerspectivesResponse::SharedCtor() {
}

GetMultiPredictPerspectivesResponse::~GetMultiPredictPerspectivesResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetMultiPredictPerspectivesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetMultiPredictPerspectivesResponse::ArenaDtor(void* object) {
  GetMultiPredictPerspectivesResponse* _this = reinterpret_cast< GetMultiPredictPerspectivesResponse* >(object);
  (void)_this;
}
void GetMultiPredictPerspectivesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetMultiPredictPerspectivesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetMultiPredictPerspectivesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  perspectives_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetMultiPredictPerspectivesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.image_stream.CentroidPerspective perspectives = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_perspectives(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetMultiPredictPerspectivesResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.image_stream.CentroidPerspective perspectives = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_perspectives_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_perspectives(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
  return target;
}

size_t GetMultiPredictPerspectivesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.image_stream.CentroidPerspective perspectives = 1;
  total_size += 1UL * this->_internal_perspectives_size();
  for (const auto& msg : this->perspectives_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetMultiPredictPerspectivesResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetMultiPredictPerspectivesResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetMultiPredictPerspectivesResponse::GetClassData() const { return &_class_data_; }

void GetMultiPredictPerspectivesResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetMultiPredictPerspectivesResponse *>(to)->MergeFrom(
      static_cast<const GetMultiPredictPerspectivesResponse &>(from));
}


void GetMultiPredictPerspectivesResponse::MergeFrom(const GetMultiPredictPerspectivesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  perspectives_.MergeFrom(from.perspectives_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetMultiPredictPerspectivesResponse::CopyFrom(const GetMultiPredictPerspectivesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetMultiPredictPerspectivesResponse::IsInitialized() const {
  return true;
}

void GetMultiPredictPerspectivesResponse::InternalSwap(GetMultiPredictPerspectivesResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  perspectives_.InternalSwap(&other->perspectives_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetMultiPredictPerspectivesResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_getter, &descriptor_table_frontend_2fproto_2fimage_5fstream_2eproto_once,
      file_level_metadata_frontend_2fproto_2fimage_5fstream_2eproto[8]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace image_stream
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::Annotations* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::Annotations >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::Annotations >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::Image* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::Image >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::Image >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::CameraImageRequest* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::CameraImageRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::CameraImageRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::PossiblePerspective* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::PossiblePerspective >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::PossiblePerspective >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::CentroidPerspective* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::CentroidPerspective >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::CentroidPerspective >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* Arena::CreateMaybeMessage< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

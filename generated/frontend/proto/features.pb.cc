// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/features.proto

#include "frontend/proto/features.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace features {
constexpr RowConfiguration::RowConfiguration(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : num_predicts_(0)
  , num_targets_(0){}
struct RowConfigurationDefaultTypeInternal {
  constexpr RowConfigurationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RowConfigurationDefaultTypeInternal() {}
  union {
    RowConfiguration _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RowConfigurationDefaultTypeInternal _RowConfiguration_default_instance_;
constexpr RobotConfiguration_RowConfigurationEntry_DoNotUse::RobotConfiguration_RowConfigurationEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct RobotConfiguration_RowConfigurationEntry_DoNotUseDefaultTypeInternal {
  constexpr RobotConfiguration_RowConfigurationEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RobotConfiguration_RowConfigurationEntry_DoNotUseDefaultTypeInternal() {}
  union {
    RobotConfiguration_RowConfigurationEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RobotConfiguration_RowConfigurationEntry_DoNotUseDefaultTypeInternal _RobotConfiguration_RowConfigurationEntry_DoNotUse_default_instance_;
constexpr RobotConfiguration::RobotConfiguration(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_configuration_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , num_rows_(0)
  , generation_(0)
{}
struct RobotConfigurationDefaultTypeInternal {
  constexpr RobotConfigurationDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RobotConfigurationDefaultTypeInternal() {}
  union {
    RobotConfiguration _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RobotConfigurationDefaultTypeInternal _RobotConfiguration_default_instance_;
}  // namespace features
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2ffeatures_2eproto[3];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2ffeatures_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2ffeatures_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2ffeatures_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RowConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RowConfiguration, num_predicts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RowConfiguration, num_targets_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RobotConfiguration, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RobotConfiguration, num_rows_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RobotConfiguration, row_configuration_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::features::RobotConfiguration, generation_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::features::RowConfiguration)},
  { 8, 16, -1, sizeof(::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse)},
  { 18, -1, -1, sizeof(::carbon::frontend::features::RobotConfiguration)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::features::_RowConfiguration_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::features::_RobotConfiguration_RowConfigurationEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::features::_RobotConfiguration_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2ffeatures_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035frontend/proto/features.proto\022\030carbon."
  "frontend.features\032\031frontend/proto/util.p"
  "roto\"=\n\020RowConfiguration\022\024\n\014num_predicts"
  "\030\001 \001(\005\022\023\n\013num_targets\030\002 \001(\005\"\244\002\n\022RobotCon"
  "figuration\022\020\n\010num_rows\030\001 \001(\005\022]\n\021row_conf"
  "iguration\030\002 \003(\0132B.carbon.frontend.featur"
  "es.RobotConfiguration.RowConfigurationEn"
  "try\0228\n\ngeneration\030\003 \001(\0162$.carbon.fronten"
  "d.features.Generation\032c\n\025RowConfiguratio"
  "nEntry\022\013\n\003key\030\001 \001(\005\0229\n\005value\030\002 \001(\0132*.car"
  "bon.frontend.features.RowConfiguration:\002"
  "8\001*3\n\nGeneration\022\r\n\tUndefined\020\000\022\n\n\006Slaye"
  "r\020\001\022\n\n\006Reaper\020\0022\320\001\n\016FeatureService\022Z\n\023Ge"
  "tNextFeatureFlags\022\037.carbon.frontend.util"
  ".Timestamp\032\".carbon.frontend.util.Featur"
  "eFlags\022b\n\025GetRobotConfiguration\022\033.carbon"
  ".frontend.util.Empty\032,.carbon.frontend.f"
  "eatures.RobotConfigurationB\020Z\016proto/fron"
  "tendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2ffeatures_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2ffeatures_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ffeatures_2eproto = {
  false, false, 732, descriptor_table_protodef_frontend_2fproto_2ffeatures_2eproto, "frontend/proto/features.proto", 
  &descriptor_table_frontend_2fproto_2ffeatures_2eproto_once, descriptor_table_frontend_2fproto_2ffeatures_2eproto_deps, 1, 3,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2ffeatures_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2ffeatures_2eproto, file_level_enum_descriptors_frontend_2fproto_2ffeatures_2eproto, file_level_service_descriptors_frontend_2fproto_2ffeatures_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2ffeatures_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2ffeatures_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2ffeatures_2eproto(&descriptor_table_frontend_2fproto_2ffeatures_2eproto);
namespace carbon {
namespace frontend {
namespace features {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Generation_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2ffeatures_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2ffeatures_2eproto[0];
}
bool Generation_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class RowConfiguration::_Internal {
 public:
};

RowConfiguration::RowConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.features.RowConfiguration)
}
RowConfiguration::RowConfiguration(const RowConfiguration& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&num_predicts_, &from.num_predicts_,
    static_cast<size_t>(reinterpret_cast<char*>(&num_targets_) -
    reinterpret_cast<char*>(&num_predicts_)) + sizeof(num_targets_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.features.RowConfiguration)
}

inline void RowConfiguration::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&num_predicts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&num_targets_) -
    reinterpret_cast<char*>(&num_predicts_)) + sizeof(num_targets_));
}

RowConfiguration::~RowConfiguration() {
  // @@protoc_insertion_point(destructor:carbon.frontend.features.RowConfiguration)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RowConfiguration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RowConfiguration::ArenaDtor(void* object) {
  RowConfiguration* _this = reinterpret_cast< RowConfiguration* >(object);
  (void)_this;
}
void RowConfiguration::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RowConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RowConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.features.RowConfiguration)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&num_predicts_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&num_targets_) -
      reinterpret_cast<char*>(&num_predicts_)) + sizeof(num_targets_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RowConfiguration::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 num_predicts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          num_predicts_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 num_targets = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          num_targets_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RowConfiguration::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.features.RowConfiguration)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_predicts = 1;
  if (this->_internal_num_predicts() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_num_predicts(), target);
  }

  // int32 num_targets = 2;
  if (this->_internal_num_targets() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_num_targets(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.features.RowConfiguration)
  return target;
}

size_t RowConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.features.RowConfiguration)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 num_predicts = 1;
  if (this->_internal_num_predicts() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_num_predicts());
  }

  // int32 num_targets = 2;
  if (this->_internal_num_targets() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_num_targets());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RowConfiguration::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RowConfiguration::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RowConfiguration::GetClassData() const { return &_class_data_; }

void RowConfiguration::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RowConfiguration *>(to)->MergeFrom(
      static_cast<const RowConfiguration &>(from));
}


void RowConfiguration::MergeFrom(const RowConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.features.RowConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_num_predicts() != 0) {
    _internal_set_num_predicts(from._internal_num_predicts());
  }
  if (from._internal_num_targets() != 0) {
    _internal_set_num_targets(from._internal_num_targets());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RowConfiguration::CopyFrom(const RowConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.features.RowConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RowConfiguration::IsInitialized() const {
  return true;
}

void RowConfiguration::InternalSwap(RowConfiguration* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RowConfiguration, num_targets_)
      + sizeof(RowConfiguration::num_targets_)
      - PROTOBUF_FIELD_OFFSET(RowConfiguration, num_predicts_)>(
          reinterpret_cast<char*>(&num_predicts_),
          reinterpret_cast<char*>(&other->num_predicts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RowConfiguration::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ffeatures_2eproto_getter, &descriptor_table_frontend_2fproto_2ffeatures_2eproto_once,
      file_level_metadata_frontend_2fproto_2ffeatures_2eproto[0]);
}

// ===================================================================

RobotConfiguration_RowConfigurationEntry_DoNotUse::RobotConfiguration_RowConfigurationEntry_DoNotUse() {}
RobotConfiguration_RowConfigurationEntry_DoNotUse::RobotConfiguration_RowConfigurationEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void RobotConfiguration_RowConfigurationEntry_DoNotUse::MergeFrom(const RobotConfiguration_RowConfigurationEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata RobotConfiguration_RowConfigurationEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ffeatures_2eproto_getter, &descriptor_table_frontend_2fproto_2ffeatures_2eproto_once,
      file_level_metadata_frontend_2fproto_2ffeatures_2eproto[1]);
}

// ===================================================================

class RobotConfiguration::_Internal {
 public:
};

RobotConfiguration::RobotConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  row_configuration_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.features.RobotConfiguration)
}
RobotConfiguration::RobotConfiguration(const RobotConfiguration& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_configuration_.MergeFrom(from.row_configuration_);
  ::memcpy(&num_rows_, &from.num_rows_,
    static_cast<size_t>(reinterpret_cast<char*>(&generation_) -
    reinterpret_cast<char*>(&num_rows_)) + sizeof(generation_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.features.RobotConfiguration)
}

inline void RobotConfiguration::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&num_rows_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&generation_) -
    reinterpret_cast<char*>(&num_rows_)) + sizeof(generation_));
}

RobotConfiguration::~RobotConfiguration() {
  // @@protoc_insertion_point(destructor:carbon.frontend.features.RobotConfiguration)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RobotConfiguration::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RobotConfiguration::ArenaDtor(void* object) {
  RobotConfiguration* _this = reinterpret_cast< RobotConfiguration* >(object);
  (void)_this;
  _this->row_configuration_. ~MapField();
}
inline void RobotConfiguration::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &RobotConfiguration::ArenaDtor);
  }
}
void RobotConfiguration::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RobotConfiguration::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.features.RobotConfiguration)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  row_configuration_.Clear();
  ::memset(&num_rows_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&generation_) -
      reinterpret_cast<char*>(&num_rows_)) + sizeof(generation_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RobotConfiguration::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 num_rows = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          num_rows_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<int32, .carbon.frontend.features.RowConfiguration> row_configuration = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&row_configuration_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.features.Generation generation = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_generation(static_cast<::carbon::frontend::features::Generation>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RobotConfiguration::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.features.RobotConfiguration)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 num_rows = 1;
  if (this->_internal_num_rows() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_num_rows(), target);
  }

  // map<int32, .carbon.frontend.features.RowConfiguration> row_configuration = 2;
  if (!this->_internal_row_configuration().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_row_configuration().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_row_configuration().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >::const_iterator
          it = this->_internal_row_configuration().begin();
          it != this->_internal_row_configuration().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = RobotConfiguration_RowConfigurationEntry_DoNotUse::Funcs::InternalSerialize(2, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >::const_iterator
          it = this->_internal_row_configuration().begin();
          it != this->_internal_row_configuration().end(); ++it) {
        target = RobotConfiguration_RowConfigurationEntry_DoNotUse::Funcs::InternalSerialize(2, it->first, it->second, target, stream);
      }
    }
  }

  // .carbon.frontend.features.Generation generation = 3;
  if (this->_internal_generation() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_generation(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.features.RobotConfiguration)
  return target;
}

size_t RobotConfiguration::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.features.RobotConfiguration)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<int32, .carbon.frontend.features.RowConfiguration> row_configuration = 2;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_row_configuration_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >::const_iterator
      it = this->_internal_row_configuration().begin();
      it != this->_internal_row_configuration().end(); ++it) {
    total_size += RobotConfiguration_RowConfigurationEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // int32 num_rows = 1;
  if (this->_internal_num_rows() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_num_rows());
  }

  // .carbon.frontend.features.Generation generation = 3;
  if (this->_internal_generation() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_generation());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RobotConfiguration::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RobotConfiguration::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RobotConfiguration::GetClassData() const { return &_class_data_; }

void RobotConfiguration::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RobotConfiguration *>(to)->MergeFrom(
      static_cast<const RobotConfiguration &>(from));
}


void RobotConfiguration::MergeFrom(const RobotConfiguration& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.features.RobotConfiguration)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  row_configuration_.MergeFrom(from.row_configuration_);
  if (from._internal_num_rows() != 0) {
    _internal_set_num_rows(from._internal_num_rows());
  }
  if (from._internal_generation() != 0) {
    _internal_set_generation(from._internal_generation());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RobotConfiguration::CopyFrom(const RobotConfiguration& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.features.RobotConfiguration)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RobotConfiguration::IsInitialized() const {
  return true;
}

void RobotConfiguration::InternalSwap(RobotConfiguration* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  row_configuration_.InternalSwap(&other->row_configuration_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RobotConfiguration, generation_)
      + sizeof(RobotConfiguration::generation_)
      - PROTOBUF_FIELD_OFFSET(RobotConfiguration, num_rows_)>(
          reinterpret_cast<char*>(&num_rows_),
          reinterpret_cast<char*>(&other->num_rows_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RobotConfiguration::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ffeatures_2eproto_getter, &descriptor_table_frontend_2fproto_2ffeatures_2eproto_once,
      file_level_metadata_frontend_2fproto_2ffeatures_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace features
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::features::RowConfiguration* Arena::CreateMaybeMessage< ::carbon::frontend::features::RowConfiguration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::features::RowConfiguration >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::features::RobotConfiguration* Arena::CreateMaybeMessage< ::carbon::frontend::features::RobotConfiguration >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::features::RobotConfiguration >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/debug.proto

#include "frontend/proto/debug.pb.h"
#include "frontend/proto/debug.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace debug {

static const char* DebugService_method_names[] = {
  "/carbon.frontend.debug.DebugService/GetRobot",
  "/carbon.frontend.debug.DebugService/SetLogLevel",
  "/carbon.frontend.debug.DebugService/StartSavingCropLineDetectionReplay",
  "/carbon.frontend.debug.DebugService/StartRecordingAimbotInputs",
  "/carbon.frontend.debug.DebugService/AddMockSpatialMetricsBlock",
  "/carbon.frontend.debug.DebugService/DeleteProfileSyncData",
};

std::unique_ptr< DebugService::Stub> DebugService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< DebugService::Stub> stub(new DebugService::Stub(channel, options));
  return stub;
}

DebugService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetRobot_(DebugService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetLogLevel_(DebugService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartSavingCropLineDetectionReplay_(DebugService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartRecordingAimbotInputs_(DebugService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_AddMockSpatialMetricsBlock_(DebugService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteProfileSyncData_(DebugService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status DebugService::Stub::GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::debug::RobotMessage* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::debug::RobotMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRobot_, context, request, response);
}

void DebugService::Stub::async::GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::debug::RobotMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRobot_, context, request, response, std::move(f));
}

void DebugService::Stub::async::GetRobot(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRobot_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::RobotMessage>* DebugService::Stub::PrepareAsyncGetRobotRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::debug::RobotMessage, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRobot_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::RobotMessage>* DebugService::Stub::AsyncGetRobotRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRobotRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DebugService::Stub::SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::debug::SetLogLevelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetLogLevel_, context, request, response);
}

void DebugService::Stub::async::SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::debug::SetLogLevelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLogLevel_, context, request, response, std::move(f));
}

void DebugService::Stub::async::SetLogLevel(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLogLevel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DebugService::Stub::PrepareAsyncSetLogLevelRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::debug::SetLogLevelRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetLogLevel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DebugService::Stub::AsyncSetLogLevelRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::SetLogLevelRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetLogLevelRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DebugService::Stub::StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartSavingCropLineDetectionReplay_, context, request, response);
}

void DebugService::Stub::async::StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartSavingCropLineDetectionReplay_, context, request, response, std::move(f));
}

void DebugService::Stub::async::StartSavingCropLineDetectionReplay(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartSavingCropLineDetectionReplay_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* DebugService::Stub::PrepareAsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartSavingCropLineDetectionReplay_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* DebugService::Stub::AsyncStartSavingCropLineDetectionReplayRaw(::grpc::ClientContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartSavingCropLineDetectionReplayRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DebugService::Stub::StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::weed_tracking::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartRecordingAimbotInputs_, context, request, response);
}

void DebugService::Stub::async::StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartRecordingAimbotInputs_, context, request, response, std::move(f));
}

void DebugService::Stub::async::StartRecordingAimbotInputs(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartRecordingAimbotInputs_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* DebugService::Stub::PrepareAsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::weed_tracking::Empty, ::weed_tracking::RecordAimbotInputRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartRecordingAimbotInputs_, context, request);
}

::grpc::ClientAsyncResponseReader< ::weed_tracking::Empty>* DebugService::Stub::AsyncStartRecordingAimbotInputsRaw(::grpc::ClientContext* context, const ::weed_tracking::RecordAimbotInputRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartRecordingAimbotInputsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DebugService::Stub::AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_AddMockSpatialMetricsBlock_, context, request, response);
}

void DebugService::Stub::async::AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AddMockSpatialMetricsBlock_, context, request, response, std::move(f));
}

void DebugService::Stub::async::AddMockSpatialMetricsBlock(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AddMockSpatialMetricsBlock_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DebugService::Stub::PrepareAsyncAddMockSpatialMetricsBlockRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_AddMockSpatialMetricsBlock_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DebugService::Stub::AsyncAddMockSpatialMetricsBlockRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncAddMockSpatialMetricsBlockRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DebugService::Stub::DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteProfileSyncData_, context, request, response);
}

void DebugService::Stub::async::DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteProfileSyncData_, context, request, response, std::move(f));
}

void DebugService::Stub::async::DeleteProfileSyncData(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteProfileSyncData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DebugService::Stub::PrepareAsyncDeleteProfileSyncDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteProfileSyncData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DebugService::Stub::AsyncDeleteProfileSyncDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteProfileSyncDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

DebugService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DebugService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DebugService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::debug::RobotMessage, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DebugService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::debug::RobotMessage* resp) {
               return service->GetRobot(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DebugService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DebugService::Service, ::carbon::frontend::debug::SetLogLevelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DebugService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::debug::SetLogLevelRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetLogLevel(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DebugService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DebugService::Service, ::weed_tracking::StartSavingCropLineDetectionReplayRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DebugService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* req,
             ::weed_tracking::Empty* resp) {
               return service->StartSavingCropLineDetectionReplay(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DebugService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DebugService::Service, ::weed_tracking::RecordAimbotInputRequest, ::weed_tracking::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DebugService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::weed_tracking::RecordAimbotInputRequest* req,
             ::weed_tracking::Empty* resp) {
               return service->StartRecordingAimbotInputs(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DebugService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DebugService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DebugService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->AddMockSpatialMetricsBlock(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DebugService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DebugService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DebugService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DeleteProfileSyncData(ctx, req, resp);
             }, this)));
}

DebugService::Service::~Service() {
}

::grpc::Status DebugService::Service::GetRobot(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::debug::RobotMessage* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DebugService::Service::SetLogLevel(::grpc::ServerContext* context, const ::carbon::frontend::debug::SetLogLevelRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DebugService::Service::StartSavingCropLineDetectionReplay(::grpc::ServerContext* context, const ::weed_tracking::StartSavingCropLineDetectionReplayRequest* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DebugService::Service::StartRecordingAimbotInputs(::grpc::ServerContext* context, const ::weed_tracking::RecordAimbotInputRequest* request, ::weed_tracking::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DebugService::Service::AddMockSpatialMetricsBlock(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DebugService::Service::DeleteProfileSyncData(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace debug


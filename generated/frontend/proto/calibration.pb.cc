// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/calibration.proto

#include "frontend/proto/calibration.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace color_calibration {
constexpr ColorCalibrationValues::ColorCalibrationValues(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , red_(0)
  , green_(0)
  , blue_(0){}
struct ColorCalibrationValuesDefaultTypeInternal {
  constexpr ColorCalibrationValuesDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ColorCalibrationValuesDefaultTypeInternal() {}
  union {
    ColorCalibrationValues _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ColorCalibrationValuesDefaultTypeInternal _ColorCalibrationValues_default_instance_;
}  // namespace color_calibration
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fcalibration_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fcalibration_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fcalibration_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fcalibration_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::color_calibration::ColorCalibrationValues, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::color_calibration::ColorCalibrationValues, red_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::color_calibration::ColorCalibrationValues, green_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::color_calibration::ColorCalibrationValues, blue_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::color_calibration::ColorCalibrationValues, cam_id_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::color_calibration::ColorCalibrationValues)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::color_calibration::_ColorCalibrationValues_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fcalibration_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n frontend/proto/calibration.proto\022!carb"
  "on.frontend.color_calibration\032\033frontend/"
  "proto/camera.proto\032\031frontend/proto/util."
  "proto\"R\n\026ColorCalibrationValues\022\013\n\003red\030\001"
  " \001(\002\022\r\n\005green\030\002 \001(\002\022\014\n\004blue\030\003 \001(\002\022\016\n\006cam"
  "_id\030\004 \001(\t2\377\001\n\022CalibrationService\022y\n\025Star"
  "tColorCalibration\022%.carbon.frontend.came"
  "ra.CameraRequest\0329.carbon.frontend.color"
  "_calibration.ColorCalibrationValues\022n\n\024S"
  "aveColorCalibration\0229.carbon.frontend.co"
  "lor_calibration.ColorCalibrationValues\032\033"
  ".carbon.frontend.util.EmptyB\020Z\016proto/fro"
  "ntendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fcalibration_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2fcamera_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fcalibration_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcalibration_2eproto = {
  false, false, 493, descriptor_table_protodef_frontend_2fproto_2fcalibration_2eproto, "frontend/proto/calibration.proto", 
  &descriptor_table_frontend_2fproto_2fcalibration_2eproto_once, descriptor_table_frontend_2fproto_2fcalibration_2eproto_deps, 2, 1,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fcalibration_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fcalibration_2eproto, file_level_enum_descriptors_frontend_2fproto_2fcalibration_2eproto, file_level_service_descriptors_frontend_2fproto_2fcalibration_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fcalibration_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fcalibration_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fcalibration_2eproto(&descriptor_table_frontend_2fproto_2fcalibration_2eproto);
namespace carbon {
namespace frontend {
namespace color_calibration {

// ===================================================================

class ColorCalibrationValues::_Internal {
 public:
};

ColorCalibrationValues::ColorCalibrationValues(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.color_calibration.ColorCalibrationValues)
}
ColorCalibrationValues::ColorCalibrationValues(const ColorCalibrationValues& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&red_, &from.red_,
    static_cast<size_t>(reinterpret_cast<char*>(&blue_) -
    reinterpret_cast<char*>(&red_)) + sizeof(blue_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.color_calibration.ColorCalibrationValues)
}

inline void ColorCalibrationValues::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&red_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&blue_) -
    reinterpret_cast<char*>(&red_)) + sizeof(blue_));
}

ColorCalibrationValues::~ColorCalibrationValues() {
  // @@protoc_insertion_point(destructor:carbon.frontend.color_calibration.ColorCalibrationValues)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ColorCalibrationValues::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ColorCalibrationValues::ArenaDtor(void* object) {
  ColorCalibrationValues* _this = reinterpret_cast< ColorCalibrationValues* >(object);
  (void)_this;
}
void ColorCalibrationValues::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ColorCalibrationValues::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ColorCalibrationValues::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.color_calibration.ColorCalibrationValues)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  ::memset(&red_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&blue_) -
      reinterpret_cast<char*>(&red_)) + sizeof(blue_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ColorCalibrationValues::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float red = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          red_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float green = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          green_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float blue = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          blue_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string cam_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.color_calibration.ColorCalibrationValues.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ColorCalibrationValues::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.color_calibration.ColorCalibrationValues)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float red = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_red = this->_internal_red();
  uint32_t raw_red;
  memcpy(&raw_red, &tmp_red, sizeof(tmp_red));
  if (raw_red != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_red(), target);
  }

  // float green = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_green = this->_internal_green();
  uint32_t raw_green;
  memcpy(&raw_green, &tmp_green, sizeof(tmp_green));
  if (raw_green != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_green(), target);
  }

  // float blue = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_blue = this->_internal_blue();
  uint32_t raw_blue;
  memcpy(&raw_blue, &tmp_blue, sizeof(tmp_blue));
  if (raw_blue != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_blue(), target);
  }

  // string cam_id = 4;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.color_calibration.ColorCalibrationValues.cam_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_cam_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.color_calibration.ColorCalibrationValues)
  return target;
}

size_t ColorCalibrationValues::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.color_calibration.ColorCalibrationValues)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 4;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // float red = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_red = this->_internal_red();
  uint32_t raw_red;
  memcpy(&raw_red, &tmp_red, sizeof(tmp_red));
  if (raw_red != 0) {
    total_size += 1 + 4;
  }

  // float green = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_green = this->_internal_green();
  uint32_t raw_green;
  memcpy(&raw_green, &tmp_green, sizeof(tmp_green));
  if (raw_green != 0) {
    total_size += 1 + 4;
  }

  // float blue = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_blue = this->_internal_blue();
  uint32_t raw_blue;
  memcpy(&raw_blue, &tmp_blue, sizeof(tmp_blue));
  if (raw_blue != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ColorCalibrationValues::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ColorCalibrationValues::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ColorCalibrationValues::GetClassData() const { return &_class_data_; }

void ColorCalibrationValues::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ColorCalibrationValues *>(to)->MergeFrom(
      static_cast<const ColorCalibrationValues &>(from));
}


void ColorCalibrationValues::MergeFrom(const ColorCalibrationValues& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.color_calibration.ColorCalibrationValues)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_red = from._internal_red();
  uint32_t raw_red;
  memcpy(&raw_red, &tmp_red, sizeof(tmp_red));
  if (raw_red != 0) {
    _internal_set_red(from._internal_red());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_green = from._internal_green();
  uint32_t raw_green;
  memcpy(&raw_green, &tmp_green, sizeof(tmp_green));
  if (raw_green != 0) {
    _internal_set_green(from._internal_green());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_blue = from._internal_blue();
  uint32_t raw_blue;
  memcpy(&raw_blue, &tmp_blue, sizeof(tmp_blue));
  if (raw_blue != 0) {
    _internal_set_blue(from._internal_blue());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ColorCalibrationValues::CopyFrom(const ColorCalibrationValues& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.color_calibration.ColorCalibrationValues)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ColorCalibrationValues::IsInitialized() const {
  return true;
}

void ColorCalibrationValues::InternalSwap(ColorCalibrationValues* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ColorCalibrationValues, blue_)
      + sizeof(ColorCalibrationValues::blue_)
      - PROTOBUF_FIELD_OFFSET(ColorCalibrationValues, red_)>(
          reinterpret_cast<char*>(&red_),
          reinterpret_cast<char*>(&other->red_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ColorCalibrationValues::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcalibration_2eproto_getter, &descriptor_table_frontend_2fproto_2fcalibration_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcalibration_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace color_calibration
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::color_calibration::ColorCalibrationValues* Arena::CreateMaybeMessage< ::carbon::frontend::color_calibration::ColorCalibrationValues >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::color_calibration::ColorCalibrationValues >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

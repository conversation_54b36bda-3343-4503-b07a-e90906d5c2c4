// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/laser.proto

#include "frontend/proto/laser.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace laser {
constexpr LaserDescriptor::LaserDescriptor(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : camera_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , row_number_(0u)
  , laser_id_(0u){}
struct LaserDescriptorDefaultTypeInternal {
  constexpr LaserDescriptorDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserDescriptorDefaultTypeInternal() {}
  union {
    LaserDescriptor _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserDescriptorDefaultTypeInternal _LaserDescriptor_default_instance_;
constexpr LaserState::LaserState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : laser_descriptor_(nullptr)
  , firing_(false)
  , enabled_(false)
  , error_(false)
  , delta_temp_(0)
  , total_fire_count_(int64_t{0})
  , total_fire_time_ms_(int64_t{0})
  , current_(0)
  , target_trajectory_id_(0u)
  , lifetime_sec_(uint64_t{0u})
  , installed_at_(int64_t{0})
  , power_level_(0){}
struct LaserStateDefaultTypeInternal {
  constexpr LaserStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserStateDefaultTypeInternal() {}
  union {
    LaserState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserStateDefaultTypeInternal _LaserState_default_instance_;
constexpr LaserStateList::LaserStateList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lasers_()
  , ts_(nullptr){}
struct LaserStateListDefaultTypeInternal {
  constexpr LaserStateListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaserStateListDefaultTypeInternal() {}
  union {
    LaserStateList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaserStateListDefaultTypeInternal _LaserStateList_default_instance_;
constexpr RowRequest::RowRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_number_(0u){}
struct RowRequestDefaultTypeInternal {
  constexpr RowRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RowRequestDefaultTypeInternal() {}
  union {
    RowRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RowRequestDefaultTypeInternal _RowRequest_default_instance_;
constexpr SetLaserPowerRequest::SetLaserPowerRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : laser_descriptor_(nullptr)
  , power_level_(0){}
struct SetLaserPowerRequestDefaultTypeInternal {
  constexpr SetLaserPowerRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetLaserPowerRequestDefaultTypeInternal() {}
  union {
    SetLaserPowerRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetLaserPowerRequestDefaultTypeInternal _SetLaserPowerRequest_default_instance_;
constexpr FixLaserMetricsRequest::FixLaserMetricsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : laser_descriptor_(nullptr)
  , total_fire_count_(int64_t{0})
  , total_fire_time_ms_(int64_t{0})
  , lifetime_sec_(uint64_t{0u}){}
struct FixLaserMetricsRequestDefaultTypeInternal {
  constexpr FixLaserMetricsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FixLaserMetricsRequestDefaultTypeInternal() {}
  union {
    FixLaserMetricsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FixLaserMetricsRequestDefaultTypeInternal _FixLaserMetricsRequest_default_instance_;
}  // namespace laser
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2flaser_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2flaser_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2flaser_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2flaser_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserDescriptor, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserDescriptor, row_number_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserDescriptor, laser_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserDescriptor, camera_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserDescriptor, serial_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, laser_descriptor_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, firing_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, error_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, total_fire_count_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, total_fire_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, delta_temp_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, current_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, target_trajectory_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, lifetime_sec_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, power_level_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserState, installed_at_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserStateList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserStateList, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::LaserStateList, lasers_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::RowRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::RowRequest, row_number_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::SetLaserPowerRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::SetLaserPowerRequest, laser_descriptor_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::SetLaserPowerRequest, power_level_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::FixLaserMetricsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::FixLaserMetricsRequest, laser_descriptor_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::FixLaserMetricsRequest, total_fire_count_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::FixLaserMetricsRequest, total_fire_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::laser::FixLaserMetricsRequest, lifetime_sec_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::laser::LaserDescriptor)},
  { 10, -1, -1, sizeof(::carbon::frontend::laser::LaserState)},
  { 28, -1, -1, sizeof(::carbon::frontend::laser::LaserStateList)},
  { 36, -1, -1, sizeof(::carbon::frontend::laser::RowRequest)},
  { 43, -1, -1, sizeof(::carbon::frontend::laser::SetLaserPowerRequest)},
  { 51, -1, -1, sizeof(::carbon::frontend::laser::FixLaserMetricsRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::laser::_LaserDescriptor_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::laser::_LaserState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::laser::_LaserStateList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::laser::_RowRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::laser::_SetLaserPowerRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::laser::_FixLaserMetricsRequest_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2flaser_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032frontend/proto/laser.proto\022\025carbon.fro"
  "ntend.laser\032\033frontend/proto/camera.proto"
  "\032\031frontend/proto/util.proto\"Z\n\017LaserDesc"
  "riptor\022\022\n\nrow_number\030\001 \001(\r\022\020\n\010laser_id\030\002"
  " \001(\r\022\021\n\tcamera_id\030\003 \001(\t\022\016\n\006serial\030\004 \001(\t\""
  "\270\002\n\nLaserState\022@\n\020laser_descriptor\030\001 \001(\013"
  "2&.carbon.frontend.laser.LaserDescriptor"
  "\022\016\n\006firing\030\002 \001(\010\022\017\n\007enabled\030\003 \001(\010\022\r\n\005err"
  "or\030\004 \001(\010\022\030\n\020total_fire_count\030\005 \001(\003\022\032\n\022to"
  "tal_fire_time_ms\030\006 \001(\003\022\022\n\ndelta_temp\030\007 \001"
  "(\002\022\017\n\007current\030\t \001(\002\022\034\n\024target_trajectory"
  "_id\030\n \001(\r\022\024\n\014lifetime_sec\030\013 \001(\004\022\023\n\013power"
  "_level\030\014 \001(\002\022\024\n\014installed_at\030\r \001(\003\"p\n\016La"
  "serStateList\022+\n\002ts\030\001 \001(\0132\037.carbon.fronte"
  "nd.util.Timestamp\0221\n\006lasers\030\002 \003(\0132!.carb"
  "on.frontend.laser.LaserState\" \n\nRowReque"
  "st\022\022\n\nrow_number\030\001 \001(\r\"m\n\024SetLaserPowerR"
  "equest\022@\n\020laser_descriptor\030\001 \001(\0132&.carbo"
  "n.frontend.laser.LaserDescriptor\022\023\n\013powe"
  "r_level\030\002 \001(\002\"\246\001\n\026FixLaserMetricsRequest"
  "\022@\n\020laser_descriptor\030\001 \001(\0132&.carbon.fron"
  "tend.laser.LaserDescriptor\022\030\n\020total_fire"
  "_count\030\002 \001(\003\022\032\n\022total_fire_time_ms\030\003 \001(\003"
  "\022\024\n\014lifetime_sec\030\004 \001(\0042\310\005\n\014LaserService\022"
  "Q\n\tFireLaser\022%.carbon.frontend.camera.Ca"
  "meraRequest\032\033.carbon.frontend.util.Empty"
  "(\001\022[\n\021GetNextLaserState\022\037.carbon.fronten"
  "d.util.Timestamp\032%.carbon.frontend.laser"
  ".LaserStateList\022Y\n\022ToggleLaserEnabled\022&."
  "carbon.frontend.laser.LaserDescriptor\032\033."
  "carbon.frontend.util.Empty\022K\n\tEnableRow\022"
  "!.carbon.frontend.laser.RowRequest\032\033.car"
  "bon.frontend.util.Empty\022L\n\nDisableRow\022!."
  "carbon.frontend.laser.RowRequest\032\033.carbo"
  "n.frontend.util.Empty\022X\n\021ResetLaserMetri"
  "cs\022&.carbon.frontend.laser.LaserDescript"
  "or\032\033.carbon.frontend.util.Empty\022]\n\017FixLa"
  "serMetrics\022-.carbon.frontend.laser.FixLa"
  "serMetricsRequest\032\033.carbon.frontend.util"
  ".Empty\022Y\n\rSetLaserPower\022+.carbon.fronten"
  "d.laser.SetLaserPowerRequest\032\033.carbon.fr"
  "ontend.util.EmptyB\020Z\016proto/frontendb\006pro"
  "to3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2flaser_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2fcamera_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2flaser_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2flaser_2eproto = {
  false, false, 1683, descriptor_table_protodef_frontend_2fproto_2flaser_2eproto, "frontend/proto/laser.proto", 
  &descriptor_table_frontend_2fproto_2flaser_2eproto_once, descriptor_table_frontend_2fproto_2flaser_2eproto_deps, 2, 6,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2flaser_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2flaser_2eproto, file_level_enum_descriptors_frontend_2fproto_2flaser_2eproto, file_level_service_descriptors_frontend_2fproto_2flaser_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2flaser_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2flaser_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2flaser_2eproto(&descriptor_table_frontend_2fproto_2flaser_2eproto);
namespace carbon {
namespace frontend {
namespace laser {

// ===================================================================

class LaserDescriptor::_Internal {
 public:
};

LaserDescriptor::LaserDescriptor(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.laser.LaserDescriptor)
}
LaserDescriptor::LaserDescriptor(const LaserDescriptor& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  camera_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_camera_id().empty()) {
    camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_camera_id(), 
      GetArenaForAllocation());
  }
  serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serial().empty()) {
    serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serial(), 
      GetArenaForAllocation());
  }
  ::memcpy(&row_number_, &from.row_number_,
    static_cast<size_t>(reinterpret_cast<char*>(&laser_id_) -
    reinterpret_cast<char*>(&row_number_)) + sizeof(laser_id_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.laser.LaserDescriptor)
}

inline void LaserDescriptor::SharedCtor() {
camera_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&row_number_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&laser_id_) -
    reinterpret_cast<char*>(&row_number_)) + sizeof(laser_id_));
}

LaserDescriptor::~LaserDescriptor() {
  // @@protoc_insertion_point(destructor:carbon.frontend.laser.LaserDescriptor)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserDescriptor::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  camera_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void LaserDescriptor::ArenaDtor(void* object) {
  LaserDescriptor* _this = reinterpret_cast< LaserDescriptor* >(object);
  (void)_this;
}
void LaserDescriptor::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserDescriptor::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserDescriptor::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.laser.LaserDescriptor)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  camera_id_.ClearToEmpty();
  serial_.ClearToEmpty();
  ::memset(&row_number_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&laser_id_) -
      reinterpret_cast<char*>(&row_number_)) + sizeof(laser_id_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserDescriptor::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row_number = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_number_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 laser_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          laser_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string camera_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_camera_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.laser.LaserDescriptor.camera_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string serial = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.laser.LaserDescriptor.serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserDescriptor::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.laser.LaserDescriptor)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row_number(), target);
  }

  // uint32 laser_id = 2;
  if (this->_internal_laser_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_laser_id(), target);
  }

  // string camera_id = 3;
  if (!this->_internal_camera_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_camera_id().data(), static_cast<int>(this->_internal_camera_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.laser.LaserDescriptor.camera_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_camera_id(), target);
  }

  // string serial = 4;
  if (!this->_internal_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serial().data(), static_cast<int>(this->_internal_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.laser.LaserDescriptor.serial");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_serial(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.laser.LaserDescriptor)
  return target;
}

size_t LaserDescriptor::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.laser.LaserDescriptor)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string camera_id = 3;
  if (!this->_internal_camera_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_camera_id());
  }

  // string serial = 4;
  if (!this->_internal_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serial());
  }

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_row_number());
  }

  // uint32 laser_id = 2;
  if (this->_internal_laser_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_laser_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserDescriptor::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserDescriptor::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserDescriptor::GetClassData() const { return &_class_data_; }

void LaserDescriptor::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserDescriptor *>(to)->MergeFrom(
      static_cast<const LaserDescriptor &>(from));
}


void LaserDescriptor::MergeFrom(const LaserDescriptor& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.laser.LaserDescriptor)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_camera_id().empty()) {
    _internal_set_camera_id(from._internal_camera_id());
  }
  if (!from._internal_serial().empty()) {
    _internal_set_serial(from._internal_serial());
  }
  if (from._internal_row_number() != 0) {
    _internal_set_row_number(from._internal_row_number());
  }
  if (from._internal_laser_id() != 0) {
    _internal_set_laser_id(from._internal_laser_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserDescriptor::CopyFrom(const LaserDescriptor& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.laser.LaserDescriptor)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserDescriptor::IsInitialized() const {
  return true;
}

void LaserDescriptor::InternalSwap(LaserDescriptor* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &camera_id_, lhs_arena,
      &other->camera_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serial_, lhs_arena,
      &other->serial_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaserDescriptor, laser_id_)
      + sizeof(LaserDescriptor::laser_id_)
      - PROTOBUF_FIELD_OFFSET(LaserDescriptor, row_number_)>(
          reinterpret_cast<char*>(&row_number_),
          reinterpret_cast<char*>(&other->row_number_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserDescriptor::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2flaser_2eproto_getter, &descriptor_table_frontend_2fproto_2flaser_2eproto_once,
      file_level_metadata_frontend_2fproto_2flaser_2eproto[0]);
}

// ===================================================================

class LaserState::_Internal {
 public:
  static const ::carbon::frontend::laser::LaserDescriptor& laser_descriptor(const LaserState* msg);
};

const ::carbon::frontend::laser::LaserDescriptor&
LaserState::_Internal::laser_descriptor(const LaserState* msg) {
  return *msg->laser_descriptor_;
}
LaserState::LaserState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.laser.LaserState)
}
LaserState::LaserState(const LaserState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_laser_descriptor()) {
    laser_descriptor_ = new ::carbon::frontend::laser::LaserDescriptor(*from.laser_descriptor_);
  } else {
    laser_descriptor_ = nullptr;
  }
  ::memcpy(&firing_, &from.firing_,
    static_cast<size_t>(reinterpret_cast<char*>(&power_level_) -
    reinterpret_cast<char*>(&firing_)) + sizeof(power_level_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.laser.LaserState)
}

inline void LaserState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&laser_descriptor_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&power_level_) -
    reinterpret_cast<char*>(&laser_descriptor_)) + sizeof(power_level_));
}

LaserState::~LaserState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.laser.LaserState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete laser_descriptor_;
}

void LaserState::ArenaDtor(void* object) {
  LaserState* _this = reinterpret_cast< LaserState* >(object);
  (void)_this;
}
void LaserState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.laser.LaserState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && laser_descriptor_ != nullptr) {
    delete laser_descriptor_;
  }
  laser_descriptor_ = nullptr;
  ::memset(&firing_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&power_level_) -
      reinterpret_cast<char*>(&firing_)) + sizeof(power_level_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_laser_descriptor(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool firing = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          firing_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool enabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool error = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          error_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 total_fire_count = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          total_fire_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 total_fire_time_ms = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          total_fire_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float delta_temp = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          delta_temp_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float current = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          current_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // uint32 target_trajectory_id = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          target_trajectory_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 lifetime_sec = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          lifetime_sec_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float power_level = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 101)) {
          power_level_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int64 installed_at = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          installed_at_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.laser.LaserState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  if (this->_internal_has_laser_descriptor()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::laser_descriptor(this), target, stream);
  }

  // bool firing = 2;
  if (this->_internal_firing() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_firing(), target);
  }

  // bool enabled = 3;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_enabled(), target);
  }

  // bool error = 4;
  if (this->_internal_error() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_error(), target);
  }

  // int64 total_fire_count = 5;
  if (this->_internal_total_fire_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(5, this->_internal_total_fire_count(), target);
  }

  // int64 total_fire_time_ms = 6;
  if (this->_internal_total_fire_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(6, this->_internal_total_fire_time_ms(), target);
  }

  // float delta_temp = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_delta_temp = this->_internal_delta_temp();
  uint32_t raw_delta_temp;
  memcpy(&raw_delta_temp, &tmp_delta_temp, sizeof(tmp_delta_temp));
  if (raw_delta_temp != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_delta_temp(), target);
  }

  // float current = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_current = this->_internal_current();
  uint32_t raw_current;
  memcpy(&raw_current, &tmp_current, sizeof(tmp_current));
  if (raw_current != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_current(), target);
  }

  // uint32 target_trajectory_id = 10;
  if (this->_internal_target_trajectory_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(10, this->_internal_target_trajectory_id(), target);
  }

  // uint64 lifetime_sec = 11;
  if (this->_internal_lifetime_sec() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(11, this->_internal_lifetime_sec(), target);
  }

  // float power_level = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_power_level = this->_internal_power_level();
  uint32_t raw_power_level;
  memcpy(&raw_power_level, &tmp_power_level, sizeof(tmp_power_level));
  if (raw_power_level != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(12, this->_internal_power_level(), target);
  }

  // int64 installed_at = 13;
  if (this->_internal_installed_at() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(13, this->_internal_installed_at(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.laser.LaserState)
  return target;
}

size_t LaserState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.laser.LaserState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  if (this->_internal_has_laser_descriptor()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *laser_descriptor_);
  }

  // bool firing = 2;
  if (this->_internal_firing() != 0) {
    total_size += 1 + 1;
  }

  // bool enabled = 3;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool error = 4;
  if (this->_internal_error() != 0) {
    total_size += 1 + 1;
  }

  // float delta_temp = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_delta_temp = this->_internal_delta_temp();
  uint32_t raw_delta_temp;
  memcpy(&raw_delta_temp, &tmp_delta_temp, sizeof(tmp_delta_temp));
  if (raw_delta_temp != 0) {
    total_size += 1 + 4;
  }

  // int64 total_fire_count = 5;
  if (this->_internal_total_fire_count() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_total_fire_count());
  }

  // int64 total_fire_time_ms = 6;
  if (this->_internal_total_fire_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_total_fire_time_ms());
  }

  // float current = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_current = this->_internal_current();
  uint32_t raw_current;
  memcpy(&raw_current, &tmp_current, sizeof(tmp_current));
  if (raw_current != 0) {
    total_size += 1 + 4;
  }

  // uint32 target_trajectory_id = 10;
  if (this->_internal_target_trajectory_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_target_trajectory_id());
  }

  // uint64 lifetime_sec = 11;
  if (this->_internal_lifetime_sec() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_lifetime_sec());
  }

  // int64 installed_at = 13;
  if (this->_internal_installed_at() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_installed_at());
  }

  // float power_level = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_power_level = this->_internal_power_level();
  uint32_t raw_power_level;
  memcpy(&raw_power_level, &tmp_power_level, sizeof(tmp_power_level));
  if (raw_power_level != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserState::GetClassData() const { return &_class_data_; }

void LaserState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserState *>(to)->MergeFrom(
      static_cast<const LaserState &>(from));
}


void LaserState::MergeFrom(const LaserState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.laser.LaserState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_laser_descriptor()) {
    _internal_mutable_laser_descriptor()->::carbon::frontend::laser::LaserDescriptor::MergeFrom(from._internal_laser_descriptor());
  }
  if (from._internal_firing() != 0) {
    _internal_set_firing(from._internal_firing());
  }
  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  if (from._internal_error() != 0) {
    _internal_set_error(from._internal_error());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_delta_temp = from._internal_delta_temp();
  uint32_t raw_delta_temp;
  memcpy(&raw_delta_temp, &tmp_delta_temp, sizeof(tmp_delta_temp));
  if (raw_delta_temp != 0) {
    _internal_set_delta_temp(from._internal_delta_temp());
  }
  if (from._internal_total_fire_count() != 0) {
    _internal_set_total_fire_count(from._internal_total_fire_count());
  }
  if (from._internal_total_fire_time_ms() != 0) {
    _internal_set_total_fire_time_ms(from._internal_total_fire_time_ms());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_current = from._internal_current();
  uint32_t raw_current;
  memcpy(&raw_current, &tmp_current, sizeof(tmp_current));
  if (raw_current != 0) {
    _internal_set_current(from._internal_current());
  }
  if (from._internal_target_trajectory_id() != 0) {
    _internal_set_target_trajectory_id(from._internal_target_trajectory_id());
  }
  if (from._internal_lifetime_sec() != 0) {
    _internal_set_lifetime_sec(from._internal_lifetime_sec());
  }
  if (from._internal_installed_at() != 0) {
    _internal_set_installed_at(from._internal_installed_at());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_power_level = from._internal_power_level();
  uint32_t raw_power_level;
  memcpy(&raw_power_level, &tmp_power_level, sizeof(tmp_power_level));
  if (raw_power_level != 0) {
    _internal_set_power_level(from._internal_power_level());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserState::CopyFrom(const LaserState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.laser.LaserState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserState::IsInitialized() const {
  return true;
}

void LaserState::InternalSwap(LaserState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaserState, power_level_)
      + sizeof(LaserState::power_level_)
      - PROTOBUF_FIELD_OFFSET(LaserState, laser_descriptor_)>(
          reinterpret_cast<char*>(&laser_descriptor_),
          reinterpret_cast<char*>(&other->laser_descriptor_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2flaser_2eproto_getter, &descriptor_table_frontend_2fproto_2flaser_2eproto_once,
      file_level_metadata_frontend_2fproto_2flaser_2eproto[1]);
}

// ===================================================================

class LaserStateList::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const LaserStateList* msg);
};

const ::carbon::frontend::util::Timestamp&
LaserStateList::_Internal::ts(const LaserStateList* msg) {
  return *msg->ts_;
}
void LaserStateList::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
LaserStateList::LaserStateList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  lasers_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.laser.LaserStateList)
}
LaserStateList::LaserStateList(const LaserStateList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      lasers_(from.lasers_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.laser.LaserStateList)
}

inline void LaserStateList::SharedCtor() {
ts_ = nullptr;
}

LaserStateList::~LaserStateList() {
  // @@protoc_insertion_point(destructor:carbon.frontend.laser.LaserStateList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaserStateList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void LaserStateList::ArenaDtor(void* object) {
  LaserStateList* _this = reinterpret_cast< LaserStateList* >(object);
  (void)_this;
}
void LaserStateList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaserStateList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaserStateList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.laser.LaserStateList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lasers_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaserStateList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.laser.LaserState lasers = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_lasers(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaserStateList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.laser.LaserStateList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.laser.LaserState lasers = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_lasers_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_lasers(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.laser.LaserStateList)
  return target;
}

size_t LaserStateList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.laser.LaserStateList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.laser.LaserState lasers = 2;
  total_size += 1UL * this->_internal_lasers_size();
  for (const auto& msg : this->lasers_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaserStateList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaserStateList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaserStateList::GetClassData() const { return &_class_data_; }

void LaserStateList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaserStateList *>(to)->MergeFrom(
      static_cast<const LaserStateList &>(from));
}


void LaserStateList::MergeFrom(const LaserStateList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.laser.LaserStateList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  lasers_.MergeFrom(from.lasers_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaserStateList::CopyFrom(const LaserStateList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.laser.LaserStateList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaserStateList::IsInitialized() const {
  return true;
}

void LaserStateList::InternalSwap(LaserStateList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  lasers_.InternalSwap(&other->lasers_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LaserStateList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2flaser_2eproto_getter, &descriptor_table_frontend_2fproto_2flaser_2eproto_once,
      file_level_metadata_frontend_2fproto_2flaser_2eproto[2]);
}

// ===================================================================

class RowRequest::_Internal {
 public:
};

RowRequest::RowRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.laser.RowRequest)
}
RowRequest::RowRequest(const RowRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_number_ = from.row_number_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.laser.RowRequest)
}

inline void RowRequest::SharedCtor() {
row_number_ = 0u;
}

RowRequest::~RowRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.laser.RowRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RowRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RowRequest::ArenaDtor(void* object) {
  RowRequest* _this = reinterpret_cast< RowRequest* >(object);
  (void)_this;
}
void RowRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RowRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RowRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.laser.RowRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  row_number_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RowRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row_number = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_number_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RowRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.laser.RowRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row_number(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.laser.RowRequest)
  return target;
}

size_t RowRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.laser.RowRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_row_number());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RowRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RowRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RowRequest::GetClassData() const { return &_class_data_; }

void RowRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RowRequest *>(to)->MergeFrom(
      static_cast<const RowRequest &>(from));
}


void RowRequest::MergeFrom(const RowRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.laser.RowRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_row_number() != 0) {
    _internal_set_row_number(from._internal_row_number());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RowRequest::CopyFrom(const RowRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.laser.RowRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RowRequest::IsInitialized() const {
  return true;
}

void RowRequest::InternalSwap(RowRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(row_number_, other->row_number_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RowRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2flaser_2eproto_getter, &descriptor_table_frontend_2fproto_2flaser_2eproto_once,
      file_level_metadata_frontend_2fproto_2flaser_2eproto[3]);
}

// ===================================================================

class SetLaserPowerRequest::_Internal {
 public:
  static const ::carbon::frontend::laser::LaserDescriptor& laser_descriptor(const SetLaserPowerRequest* msg);
};

const ::carbon::frontend::laser::LaserDescriptor&
SetLaserPowerRequest::_Internal::laser_descriptor(const SetLaserPowerRequest* msg) {
  return *msg->laser_descriptor_;
}
SetLaserPowerRequest::SetLaserPowerRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.laser.SetLaserPowerRequest)
}
SetLaserPowerRequest::SetLaserPowerRequest(const SetLaserPowerRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_laser_descriptor()) {
    laser_descriptor_ = new ::carbon::frontend::laser::LaserDescriptor(*from.laser_descriptor_);
  } else {
    laser_descriptor_ = nullptr;
  }
  power_level_ = from.power_level_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.laser.SetLaserPowerRequest)
}

inline void SetLaserPowerRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&laser_descriptor_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&power_level_) -
    reinterpret_cast<char*>(&laser_descriptor_)) + sizeof(power_level_));
}

SetLaserPowerRequest::~SetLaserPowerRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.laser.SetLaserPowerRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetLaserPowerRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete laser_descriptor_;
}

void SetLaserPowerRequest::ArenaDtor(void* object) {
  SetLaserPowerRequest* _this = reinterpret_cast< SetLaserPowerRequest* >(object);
  (void)_this;
}
void SetLaserPowerRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetLaserPowerRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetLaserPowerRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.laser.SetLaserPowerRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && laser_descriptor_ != nullptr) {
    delete laser_descriptor_;
  }
  laser_descriptor_ = nullptr;
  power_level_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetLaserPowerRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_laser_descriptor(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float power_level = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          power_level_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetLaserPowerRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.laser.SetLaserPowerRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  if (this->_internal_has_laser_descriptor()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::laser_descriptor(this), target, stream);
  }

  // float power_level = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_power_level = this->_internal_power_level();
  uint32_t raw_power_level;
  memcpy(&raw_power_level, &tmp_power_level, sizeof(tmp_power_level));
  if (raw_power_level != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_power_level(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.laser.SetLaserPowerRequest)
  return target;
}

size_t SetLaserPowerRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.laser.SetLaserPowerRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  if (this->_internal_has_laser_descriptor()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *laser_descriptor_);
  }

  // float power_level = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_power_level = this->_internal_power_level();
  uint32_t raw_power_level;
  memcpy(&raw_power_level, &tmp_power_level, sizeof(tmp_power_level));
  if (raw_power_level != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetLaserPowerRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetLaserPowerRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetLaserPowerRequest::GetClassData() const { return &_class_data_; }

void SetLaserPowerRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetLaserPowerRequest *>(to)->MergeFrom(
      static_cast<const SetLaserPowerRequest &>(from));
}


void SetLaserPowerRequest::MergeFrom(const SetLaserPowerRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.laser.SetLaserPowerRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_laser_descriptor()) {
    _internal_mutable_laser_descriptor()->::carbon::frontend::laser::LaserDescriptor::MergeFrom(from._internal_laser_descriptor());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_power_level = from._internal_power_level();
  uint32_t raw_power_level;
  memcpy(&raw_power_level, &tmp_power_level, sizeof(tmp_power_level));
  if (raw_power_level != 0) {
    _internal_set_power_level(from._internal_power_level());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetLaserPowerRequest::CopyFrom(const SetLaserPowerRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.laser.SetLaserPowerRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetLaserPowerRequest::IsInitialized() const {
  return true;
}

void SetLaserPowerRequest::InternalSwap(SetLaserPowerRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SetLaserPowerRequest, power_level_)
      + sizeof(SetLaserPowerRequest::power_level_)
      - PROTOBUF_FIELD_OFFSET(SetLaserPowerRequest, laser_descriptor_)>(
          reinterpret_cast<char*>(&laser_descriptor_),
          reinterpret_cast<char*>(&other->laser_descriptor_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SetLaserPowerRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2flaser_2eproto_getter, &descriptor_table_frontend_2fproto_2flaser_2eproto_once,
      file_level_metadata_frontend_2fproto_2flaser_2eproto[4]);
}

// ===================================================================

class FixLaserMetricsRequest::_Internal {
 public:
  static const ::carbon::frontend::laser::LaserDescriptor& laser_descriptor(const FixLaserMetricsRequest* msg);
};

const ::carbon::frontend::laser::LaserDescriptor&
FixLaserMetricsRequest::_Internal::laser_descriptor(const FixLaserMetricsRequest* msg) {
  return *msg->laser_descriptor_;
}
FixLaserMetricsRequest::FixLaserMetricsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.laser.FixLaserMetricsRequest)
}
FixLaserMetricsRequest::FixLaserMetricsRequest(const FixLaserMetricsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_laser_descriptor()) {
    laser_descriptor_ = new ::carbon::frontend::laser::LaserDescriptor(*from.laser_descriptor_);
  } else {
    laser_descriptor_ = nullptr;
  }
  ::memcpy(&total_fire_count_, &from.total_fire_count_,
    static_cast<size_t>(reinterpret_cast<char*>(&lifetime_sec_) -
    reinterpret_cast<char*>(&total_fire_count_)) + sizeof(lifetime_sec_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.laser.FixLaserMetricsRequest)
}

inline void FixLaserMetricsRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&laser_descriptor_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&lifetime_sec_) -
    reinterpret_cast<char*>(&laser_descriptor_)) + sizeof(lifetime_sec_));
}

FixLaserMetricsRequest::~FixLaserMetricsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.laser.FixLaserMetricsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FixLaserMetricsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete laser_descriptor_;
}

void FixLaserMetricsRequest::ArenaDtor(void* object) {
  FixLaserMetricsRequest* _this = reinterpret_cast< FixLaserMetricsRequest* >(object);
  (void)_this;
}
void FixLaserMetricsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FixLaserMetricsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FixLaserMetricsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.laser.FixLaserMetricsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && laser_descriptor_ != nullptr) {
    delete laser_descriptor_;
  }
  laser_descriptor_ = nullptr;
  ::memset(&total_fire_count_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&lifetime_sec_) -
      reinterpret_cast<char*>(&total_fire_count_)) + sizeof(lifetime_sec_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FixLaserMetricsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_laser_descriptor(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 total_fire_count = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          total_fire_count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 total_fire_time_ms = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          total_fire_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 lifetime_sec = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          lifetime_sec_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FixLaserMetricsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.laser.FixLaserMetricsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  if (this->_internal_has_laser_descriptor()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::laser_descriptor(this), target, stream);
  }

  // int64 total_fire_count = 2;
  if (this->_internal_total_fire_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_total_fire_count(), target);
  }

  // int64 total_fire_time_ms = 3;
  if (this->_internal_total_fire_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_total_fire_time_ms(), target);
  }

  // uint64 lifetime_sec = 4;
  if (this->_internal_lifetime_sec() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_lifetime_sec(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.laser.FixLaserMetricsRequest)
  return target;
}

size_t FixLaserMetricsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.laser.FixLaserMetricsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.laser.LaserDescriptor laser_descriptor = 1;
  if (this->_internal_has_laser_descriptor()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *laser_descriptor_);
  }

  // int64 total_fire_count = 2;
  if (this->_internal_total_fire_count() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_total_fire_count());
  }

  // int64 total_fire_time_ms = 3;
  if (this->_internal_total_fire_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_total_fire_time_ms());
  }

  // uint64 lifetime_sec = 4;
  if (this->_internal_lifetime_sec() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_lifetime_sec());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FixLaserMetricsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FixLaserMetricsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FixLaserMetricsRequest::GetClassData() const { return &_class_data_; }

void FixLaserMetricsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FixLaserMetricsRequest *>(to)->MergeFrom(
      static_cast<const FixLaserMetricsRequest &>(from));
}


void FixLaserMetricsRequest::MergeFrom(const FixLaserMetricsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.laser.FixLaserMetricsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_laser_descriptor()) {
    _internal_mutable_laser_descriptor()->::carbon::frontend::laser::LaserDescriptor::MergeFrom(from._internal_laser_descriptor());
  }
  if (from._internal_total_fire_count() != 0) {
    _internal_set_total_fire_count(from._internal_total_fire_count());
  }
  if (from._internal_total_fire_time_ms() != 0) {
    _internal_set_total_fire_time_ms(from._internal_total_fire_time_ms());
  }
  if (from._internal_lifetime_sec() != 0) {
    _internal_set_lifetime_sec(from._internal_lifetime_sec());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FixLaserMetricsRequest::CopyFrom(const FixLaserMetricsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.laser.FixLaserMetricsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FixLaserMetricsRequest::IsInitialized() const {
  return true;
}

void FixLaserMetricsRequest::InternalSwap(FixLaserMetricsRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FixLaserMetricsRequest, lifetime_sec_)
      + sizeof(FixLaserMetricsRequest::lifetime_sec_)
      - PROTOBUF_FIELD_OFFSET(FixLaserMetricsRequest, laser_descriptor_)>(
          reinterpret_cast<char*>(&laser_descriptor_),
          reinterpret_cast<char*>(&other->laser_descriptor_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FixLaserMetricsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2flaser_2eproto_getter, &descriptor_table_frontend_2fproto_2flaser_2eproto_once,
      file_level_metadata_frontend_2fproto_2flaser_2eproto[5]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace laser
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::laser::LaserDescriptor* Arena::CreateMaybeMessage< ::carbon::frontend::laser::LaserDescriptor >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::laser::LaserDescriptor >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::laser::LaserState* Arena::CreateMaybeMessage< ::carbon::frontend::laser::LaserState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::laser::LaserState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::laser::LaserStateList* Arena::CreateMaybeMessage< ::carbon::frontend::laser::LaserStateList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::laser::LaserStateList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::laser::RowRequest* Arena::CreateMaybeMessage< ::carbon::frontend::laser::RowRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::laser::RowRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::laser::SetLaserPowerRequest* Arena::CreateMaybeMessage< ::carbon::frontend::laser::SetLaserPowerRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::laser::SetLaserPowerRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::laser::FixLaserMetricsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::laser::FixLaserMetricsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::laser::FixLaserMetricsRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

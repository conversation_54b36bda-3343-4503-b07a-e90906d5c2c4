# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/thinning.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.proto.thinning import thinning_pb2 as proto_dot_thinning_dot_thinning__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/thinning.proto',
  package='carbon.frontend.thinning',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1d\x66rontend/proto/thinning.proto\x12\x18\x63\x61rbon.frontend.thinning\x1a\x1dproto/thinning/thinning.proto\x1a\x19\x66rontend/proto/util.proto\"\x97\x01\n\x1dGetNextConfigurationsResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x36\n\x0b\x64\x65\x66initions\x18\x02 \x03(\x0b\x32!.carbon.thinning.ConfigDefinition\x12\x11\n\tactive_id\x18\x03 \x01(\t\"f\n\x19GetNextActiveConfResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x10\n\x04name\x18\x02 \x01(\tB\x02\x18\x01\x12\n\n\x02id\x18\x03 \x01(\t\"\x9f\x01\n\x1a\x44\x65\x66ineConfigurationRequest\x12\x35\n\ndefinition\x18\x01 \x01(\x0b\x32!.carbon.thinning.ConfigDefinition\x12\x12\n\nset_active\x18\x02 \x01(\x08\x12\x36\n\x03ver\x18\x03 \x01(\x0e\x32).carbon.frontend.thinning.ThinningConfVer\")\n\x1b\x44\x65\x66ineConfigurationResponse\x12\n\n\x02id\x18\x01 \x01(\t\"n\n\x16SetActiveConfigRequest\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\n\n\x02id\x18\x02 \x01(\t\x12\x36\n\x03ver\x18\x03 \x01(\x0e\x32).carbon.frontend.thinning.ThinningConfVer\"\x19\n\x17SetActiveConfigResponse\"\x82\x01\n\x13\x44\x65leteConfigRequest\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\n\n\x02id\x18\x02 \x01(\t\x12\x36\n\x03ver\x18\x03 \x01(\x0e\x32).carbon.frontend.thinning.ThinningConfVer\x12\x15\n\rnew_active_id\x18\x04 \x01(\t\"\x16\n\x14\x44\x65leteConfigResponse*5\n\x0fThinningConfVer\x12\x10\n\x0cTHIN_CONF_V1\x10\x00\x12\x10\n\x0cTHIN_CONF_V2\x10\x01\x32\xdb\x04\n\x0fThinningService\x12q\n\x15GetNextConfigurations\x12\x1f.carbon.frontend.util.Timestamp\x1a\x37.carbon.frontend.thinning.GetNextConfigurationsResponse\x12i\n\x11GetNextActiveConf\x12\x1f.carbon.frontend.util.Timestamp\x1a\x33.carbon.frontend.thinning.GetNextActiveConfResponse\x12\x82\x01\n\x13\x44\x65\x66ineConfiguration\x12\x34.carbon.frontend.thinning.DefineConfigurationRequest\x1a\x35.carbon.frontend.thinning.DefineConfigurationResponse\x12v\n\x0fSetActiveConfig\x12\x30.carbon.frontend.thinning.SetActiveConfigRequest\x1a\x31.carbon.frontend.thinning.SetActiveConfigResponse\x12m\n\x0c\x44\x65leteConfig\x12-.carbon.frontend.thinning.DeleteConfigRequest\x1a..carbon.frontend.thinning.DeleteConfigResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[proto_dot_thinning_dot_thinning__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_THINNINGCONFVER = _descriptor.EnumDescriptor(
  name='ThinningConfVer',
  full_name='carbon.frontend.thinning.ThinningConfVer',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='THIN_CONF_V1', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='THIN_CONF_V2', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=876,
  serialized_end=929,
)
_sym_db.RegisterEnumDescriptor(_THINNINGCONFVER)

ThinningConfVer = enum_type_wrapper.EnumTypeWrapper(_THINNINGCONFVER)
THIN_CONF_V1 = 0
THIN_CONF_V2 = 1



_GETNEXTCONFIGURATIONSRESPONSE = _descriptor.Descriptor(
  name='GetNextConfigurationsResponse',
  full_name='carbon.frontend.thinning.GetNextConfigurationsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.thinning.GetNextConfigurationsResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='definitions', full_name='carbon.frontend.thinning.GetNextConfigurationsResponse.definitions', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_id', full_name='carbon.frontend.thinning.GetNextConfigurationsResponse.active_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=118,
  serialized_end=269,
)


_GETNEXTACTIVECONFRESPONSE = _descriptor.Descriptor(
  name='GetNextActiveConfResponse',
  full_name='carbon.frontend.thinning.GetNextActiveConfResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.thinning.GetNextActiveConfResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.thinning.GetNextActiveConfResponse.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.thinning.GetNextActiveConfResponse.id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=271,
  serialized_end=373,
)


_DEFINECONFIGURATIONREQUEST = _descriptor.Descriptor(
  name='DefineConfigurationRequest',
  full_name='carbon.frontend.thinning.DefineConfigurationRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='definition', full_name='carbon.frontend.thinning.DefineConfigurationRequest.definition', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='set_active', full_name='carbon.frontend.thinning.DefineConfigurationRequest.set_active', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ver', full_name='carbon.frontend.thinning.DefineConfigurationRequest.ver', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=376,
  serialized_end=535,
)


_DEFINECONFIGURATIONRESPONSE = _descriptor.Descriptor(
  name='DefineConfigurationResponse',
  full_name='carbon.frontend.thinning.DefineConfigurationResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.thinning.DefineConfigurationResponse.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=537,
  serialized_end=578,
)


_SETACTIVECONFIGREQUEST = _descriptor.Descriptor(
  name='SetActiveConfigRequest',
  full_name='carbon.frontend.thinning.SetActiveConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.thinning.SetActiveConfigRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.thinning.SetActiveConfigRequest.id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ver', full_name='carbon.frontend.thinning.SetActiveConfigRequest.ver', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=580,
  serialized_end=690,
)


_SETACTIVECONFIGRESPONSE = _descriptor.Descriptor(
  name='SetActiveConfigResponse',
  full_name='carbon.frontend.thinning.SetActiveConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=692,
  serialized_end=717,
)


_DELETECONFIGREQUEST = _descriptor.Descriptor(
  name='DeleteConfigRequest',
  full_name='carbon.frontend.thinning.DeleteConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.thinning.DeleteConfigRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.thinning.DeleteConfigRequest.id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ver', full_name='carbon.frontend.thinning.DeleteConfigRequest.ver', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='new_active_id', full_name='carbon.frontend.thinning.DeleteConfigRequest.new_active_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=720,
  serialized_end=850,
)


_DELETECONFIGRESPONSE = _descriptor.Descriptor(
  name='DeleteConfigResponse',
  full_name='carbon.frontend.thinning.DeleteConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=852,
  serialized_end=874,
)

_GETNEXTCONFIGURATIONSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCONFIGURATIONSRESPONSE.fields_by_name['definitions'].message_type = proto_dot_thinning_dot_thinning__pb2._CONFIGDEFINITION
_GETNEXTACTIVECONFRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_DEFINECONFIGURATIONREQUEST.fields_by_name['definition'].message_type = proto_dot_thinning_dot_thinning__pb2._CONFIGDEFINITION
_DEFINECONFIGURATIONREQUEST.fields_by_name['ver'].enum_type = _THINNINGCONFVER
_SETACTIVECONFIGREQUEST.fields_by_name['ver'].enum_type = _THINNINGCONFVER
_DELETECONFIGREQUEST.fields_by_name['ver'].enum_type = _THINNINGCONFVER
DESCRIPTOR.message_types_by_name['GetNextConfigurationsResponse'] = _GETNEXTCONFIGURATIONSRESPONSE
DESCRIPTOR.message_types_by_name['GetNextActiveConfResponse'] = _GETNEXTACTIVECONFRESPONSE
DESCRIPTOR.message_types_by_name['DefineConfigurationRequest'] = _DEFINECONFIGURATIONREQUEST
DESCRIPTOR.message_types_by_name['DefineConfigurationResponse'] = _DEFINECONFIGURATIONRESPONSE
DESCRIPTOR.message_types_by_name['SetActiveConfigRequest'] = _SETACTIVECONFIGREQUEST
DESCRIPTOR.message_types_by_name['SetActiveConfigResponse'] = _SETACTIVECONFIGRESPONSE
DESCRIPTOR.message_types_by_name['DeleteConfigRequest'] = _DELETECONFIGREQUEST
DESCRIPTOR.message_types_by_name['DeleteConfigResponse'] = _DELETECONFIGRESPONSE
DESCRIPTOR.enum_types_by_name['ThinningConfVer'] = _THINNINGCONFVER
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetNextConfigurationsResponse = _reflection.GeneratedProtocolMessageType('GetNextConfigurationsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCONFIGURATIONSRESPONSE,
  '__module__' : 'frontend.proto.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.thinning.GetNextConfigurationsResponse)
  })
_sym_db.RegisterMessage(GetNextConfigurationsResponse)

GetNextActiveConfResponse = _reflection.GeneratedProtocolMessageType('GetNextActiveConfResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVECONFRESPONSE,
  '__module__' : 'frontend.proto.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.thinning.GetNextActiveConfResponse)
  })
_sym_db.RegisterMessage(GetNextActiveConfResponse)

DefineConfigurationRequest = _reflection.GeneratedProtocolMessageType('DefineConfigurationRequest', (_message.Message,), {
  'DESCRIPTOR' : _DEFINECONFIGURATIONREQUEST,
  '__module__' : 'frontend.proto.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.thinning.DefineConfigurationRequest)
  })
_sym_db.RegisterMessage(DefineConfigurationRequest)

DefineConfigurationResponse = _reflection.GeneratedProtocolMessageType('DefineConfigurationResponse', (_message.Message,), {
  'DESCRIPTOR' : _DEFINECONFIGURATIONRESPONSE,
  '__module__' : 'frontend.proto.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.thinning.DefineConfigurationResponse)
  })
_sym_db.RegisterMessage(DefineConfigurationResponse)

SetActiveConfigRequest = _reflection.GeneratedProtocolMessageType('SetActiveConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVECONFIGREQUEST,
  '__module__' : 'frontend.proto.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.thinning.SetActiveConfigRequest)
  })
_sym_db.RegisterMessage(SetActiveConfigRequest)

SetActiveConfigResponse = _reflection.GeneratedProtocolMessageType('SetActiveConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVECONFIGRESPONSE,
  '__module__' : 'frontend.proto.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.thinning.SetActiveConfigResponse)
  })
_sym_db.RegisterMessage(SetActiveConfigResponse)

DeleteConfigRequest = _reflection.GeneratedProtocolMessageType('DeleteConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETECONFIGREQUEST,
  '__module__' : 'frontend.proto.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.thinning.DeleteConfigRequest)
  })
_sym_db.RegisterMessage(DeleteConfigRequest)

DeleteConfigResponse = _reflection.GeneratedProtocolMessageType('DeleteConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _DELETECONFIGRESPONSE,
  '__module__' : 'frontend.proto.thinning_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.thinning.DeleteConfigResponse)
  })
_sym_db.RegisterMessage(DeleteConfigResponse)


DESCRIPTOR._options = None
_GETNEXTACTIVECONFRESPONSE.fields_by_name['name']._options = None
_SETACTIVECONFIGREQUEST.fields_by_name['name']._options = None
_DELETECONFIGREQUEST.fields_by_name['name']._options = None

_THINNINGSERVICE = _descriptor.ServiceDescriptor(
  name='ThinningService',
  full_name='carbon.frontend.thinning.ThinningService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=932,
  serialized_end=1535,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextConfigurations',
    full_name='carbon.frontend.thinning.ThinningService.GetNextConfigurations',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTCONFIGURATIONSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextActiveConf',
    full_name='carbon.frontend.thinning.ThinningService.GetNextActiveConf',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTACTIVECONFRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DefineConfiguration',
    full_name='carbon.frontend.thinning.ThinningService.DefineConfiguration',
    index=2,
    containing_service=None,
    input_type=_DEFINECONFIGURATIONREQUEST,
    output_type=_DEFINECONFIGURATIONRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetActiveConfig',
    full_name='carbon.frontend.thinning.ThinningService.SetActiveConfig',
    index=3,
    containing_service=None,
    input_type=_SETACTIVECONFIGREQUEST,
    output_type=_SETACTIVECONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteConfig',
    full_name='carbon.frontend.thinning.ThinningService.DeleteConfig',
    index=4,
    containing_service=None,
    input_type=_DELETECONFIGREQUEST,
    output_type=_DELETECONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_THINNINGSERVICE)

DESCRIPTOR.services_by_name['ThinningService'] = _THINNINGSERVICE

# @@protoc_insertion_point(module_scope)

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

DeviceValue = typing___NewType('DeviceValue', builtin___int)
type___DeviceValue = DeviceValue
Device: _Device
class _Device(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[DeviceValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    SENSOR_LIFTED = typing___cast(DeviceValue, 0)
    SENSOR_SERVER_TEMPERATURE = typing___cast(DeviceValue, 1)
    SENSOR_SERVER_HUMIDITY = typing___cast(DeviceValue, 2)
    SENSOR_WATER = typing___cast(DeviceValue, 3)
    SENSOR_12V_BATTERY = typing___cast(DeviceValue, 4)
    SENSOR_POWER_QUALITY = typing___cast(DeviceValue, 5)
    SENSOR_TRACTOR = typing___cast(DeviceValue, 6)
    SENSOR_AC_FREQUENCY = typing___cast(DeviceValue, 7)
    SENSOR_AB_VOLTAGE = typing___cast(DeviceValue, 8)
    SENSOR_BC_VOLTAGE = typing___cast(DeviceValue, 9)
    SENSOR_AC_VOLTAGE = typing___cast(DeviceValue, 10)
    SENSOR_A_CURRENT = typing___cast(DeviceValue, 11)
    SENSOR_B_CURRENT = typing___cast(DeviceValue, 12)
    SENSOR_C_CURRENT = typing___cast(DeviceValue, 13)
    RELAY_SUICIDE = typing___cast(DeviceValue, 14)
    RELAY_REBOOT = typing___cast(DeviceValue, 15)
    RELAY_MAIN = typing___cast(DeviceValue, 16)
    RELAY_ROW_1 = typing___cast(DeviceValue, 17)
    RELAY_ROW_2 = typing___cast(DeviceValue, 18)
    RELAY_ROW_3 = typing___cast(DeviceValue, 19)
    RELAY_LIGHTS_1 = typing___cast(DeviceValue, 20)
    RELAY_LIGHTS_2 = typing___cast(DeviceValue, 21)
    RELAY_LIGHTS_3 = typing___cast(DeviceValue, 22)
    RELAY_SCANNER_1 = typing___cast(DeviceValue, 23)
    RELAY_SCANNER_2 = typing___cast(DeviceValue, 24)
    RELAY_SCANNER_3 = typing___cast(DeviceValue, 25)
    RELAY_AC = typing___cast(DeviceValue, 26)
    RELAY_CHILLER = typing___cast(DeviceValue, 27)
    RELAY_STROBE = typing___cast(DeviceValue, 28)
    RELAY_ENCODER_FRONT_LEFT = typing___cast(DeviceValue, 29)
    RELAY_ENCODER_FRONT_RIGHT = typing___cast(DeviceValue, 30)
    RELAY_ENCODER_BACK_LEFT = typing___cast(DeviceValue, 31)
    RELAY_ENCODER_BACK_RIGHT = typing___cast(DeviceValue, 32)
    SENSOR_ENCODER_FRONT_LEFT = typing___cast(DeviceValue, 33)
    SENSOR_ENCODER_FRONT_RIGHT = typing___cast(DeviceValue, 34)
    SENSOR_ENCODER_BACK_LEFT = typing___cast(DeviceValue, 35)
    SENSOR_ENCODER_BACK_RIGHT = typing___cast(DeviceValue, 36)
    RELAY_GPS = typing___cast(DeviceValue, 37)
    SENSOR_LATITUDE = typing___cast(DeviceValue, 38)
    SENSOR_LONGITUDE = typing___cast(DeviceValue, 39)
    SENSOR_KEY = typing___cast(DeviceValue, 40)
    SENSOR_INTERLOCK = typing___cast(DeviceValue, 41)
    RELAY_ENCODER_BOARD = typing___cast(DeviceValue, 42)
SENSOR_LIFTED = typing___cast(DeviceValue, 0)
SENSOR_SERVER_TEMPERATURE = typing___cast(DeviceValue, 1)
SENSOR_SERVER_HUMIDITY = typing___cast(DeviceValue, 2)
SENSOR_WATER = typing___cast(DeviceValue, 3)
SENSOR_12V_BATTERY = typing___cast(DeviceValue, 4)
SENSOR_POWER_QUALITY = typing___cast(DeviceValue, 5)
SENSOR_TRACTOR = typing___cast(DeviceValue, 6)
SENSOR_AC_FREQUENCY = typing___cast(DeviceValue, 7)
SENSOR_AB_VOLTAGE = typing___cast(DeviceValue, 8)
SENSOR_BC_VOLTAGE = typing___cast(DeviceValue, 9)
SENSOR_AC_VOLTAGE = typing___cast(DeviceValue, 10)
SENSOR_A_CURRENT = typing___cast(DeviceValue, 11)
SENSOR_B_CURRENT = typing___cast(DeviceValue, 12)
SENSOR_C_CURRENT = typing___cast(DeviceValue, 13)
RELAY_SUICIDE = typing___cast(DeviceValue, 14)
RELAY_REBOOT = typing___cast(DeviceValue, 15)
RELAY_MAIN = typing___cast(DeviceValue, 16)
RELAY_ROW_1 = typing___cast(DeviceValue, 17)
RELAY_ROW_2 = typing___cast(DeviceValue, 18)
RELAY_ROW_3 = typing___cast(DeviceValue, 19)
RELAY_LIGHTS_1 = typing___cast(DeviceValue, 20)
RELAY_LIGHTS_2 = typing___cast(DeviceValue, 21)
RELAY_LIGHTS_3 = typing___cast(DeviceValue, 22)
RELAY_SCANNER_1 = typing___cast(DeviceValue, 23)
RELAY_SCANNER_2 = typing___cast(DeviceValue, 24)
RELAY_SCANNER_3 = typing___cast(DeviceValue, 25)
RELAY_AC = typing___cast(DeviceValue, 26)
RELAY_CHILLER = typing___cast(DeviceValue, 27)
RELAY_STROBE = typing___cast(DeviceValue, 28)
RELAY_ENCODER_FRONT_LEFT = typing___cast(DeviceValue, 29)
RELAY_ENCODER_FRONT_RIGHT = typing___cast(DeviceValue, 30)
RELAY_ENCODER_BACK_LEFT = typing___cast(DeviceValue, 31)
RELAY_ENCODER_BACK_RIGHT = typing___cast(DeviceValue, 32)
SENSOR_ENCODER_FRONT_LEFT = typing___cast(DeviceValue, 33)
SENSOR_ENCODER_FRONT_RIGHT = typing___cast(DeviceValue, 34)
SENSOR_ENCODER_BACK_LEFT = typing___cast(DeviceValue, 35)
SENSOR_ENCODER_BACK_RIGHT = typing___cast(DeviceValue, 36)
RELAY_GPS = typing___cast(DeviceValue, 37)
SENSOR_LATITUDE = typing___cast(DeviceValue, 38)
SENSOR_LONGITUDE = typing___cast(DeviceValue, 39)
SENSOR_KEY = typing___cast(DeviceValue, 40)
SENSOR_INTERLOCK = typing___cast(DeviceValue, 41)
RELAY_ENCODER_BOARD = typing___cast(DeviceValue, 42)

DeviceValueColorValue = typing___NewType('DeviceValueColorValue', builtin___int)
type___DeviceValueColorValue = DeviceValueColorValue
DeviceValueColor: _DeviceValueColor
class _DeviceValueColor(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[DeviceValueColorValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    COLOR_GRAY = typing___cast(DeviceValueColorValue, 0)
    COLOR_GREEN = typing___cast(DeviceValueColorValue, 1)
    COLOR_ORANGE = typing___cast(DeviceValueColorValue, 2)
    COLOR_RED = typing___cast(DeviceValueColorValue, 3)
COLOR_GRAY = typing___cast(DeviceValueColorValue, 0)
COLOR_GREEN = typing___cast(DeviceValueColorValue, 1)
COLOR_ORANGE = typing___cast(DeviceValueColorValue, 2)
COLOR_RED = typing___cast(DeviceValueColorValue, 3)

NetworkLinkSpeedValue = typing___NewType('NetworkLinkSpeedValue', builtin___int)
type___NetworkLinkSpeedValue = NetworkLinkSpeedValue
NetworkLinkSpeed: _NetworkLinkSpeed
class _NetworkLinkSpeed(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[NetworkLinkSpeedValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    UNKNOWN = typing___cast(NetworkLinkSpeedValue, 0)
    SPEED_10M_HALF = typing___cast(NetworkLinkSpeedValue, 1)
    SPEED_10M_FULL = typing___cast(NetworkLinkSpeedValue, 2)
    SPEED_100M_HALF = typing___cast(NetworkLinkSpeedValue, 3)
    SPEED_100M_FULL = typing___cast(NetworkLinkSpeedValue, 4)
    SPEED_1G_FULL = typing___cast(NetworkLinkSpeedValue, 5)
    SPEED_2G5_FULL = typing___cast(NetworkLinkSpeedValue, 6)
    SPEED_5G_FULL = typing___cast(NetworkLinkSpeedValue, 7)
    SPEED_10G_FULL = typing___cast(NetworkLinkSpeedValue, 8)
UNKNOWN = typing___cast(NetworkLinkSpeedValue, 0)
SPEED_10M_HALF = typing___cast(NetworkLinkSpeedValue, 1)
SPEED_10M_FULL = typing___cast(NetworkLinkSpeedValue, 2)
SPEED_100M_HALF = typing___cast(NetworkLinkSpeedValue, 3)
SPEED_100M_FULL = typing___cast(NetworkLinkSpeedValue, 4)
SPEED_1G_FULL = typing___cast(NetworkLinkSpeedValue, 5)
SPEED_2G5_FULL = typing___cast(NetworkLinkSpeedValue, 6)
SPEED_5G_FULL = typing___cast(NetworkLinkSpeedValue, 7)
SPEED_10G_FULL = typing___cast(NetworkLinkSpeedValue, 8)

AcPowerStatusValue = typing___NewType('AcPowerStatusValue', builtin___int)
type___AcPowerStatusValue = AcPowerStatusValue
AcPowerStatus: _AcPowerStatus
class _AcPowerStatus(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[AcPowerStatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    POWER_UNKNOWN = typing___cast(AcPowerStatusValue, 0)
    POWER_GOOD = typing___cast(AcPowerStatusValue, 1)
    POWER_BAD = typing___cast(AcPowerStatusValue, 2)
    POWER_VERY_BAD = typing___cast(AcPowerStatusValue, 3)
POWER_UNKNOWN = typing___cast(AcPowerStatusValue, 0)
POWER_GOOD = typing___cast(AcPowerStatusValue, 1)
POWER_BAD = typing___cast(AcPowerStatusValue, 2)
POWER_VERY_BAD = typing___cast(AcPowerStatusValue, 3)

ModuleStatusValue = typing___NewType('ModuleStatusValue', builtin___int)
type___ModuleStatusValue = ModuleStatusValue
ModuleStatus: _ModuleStatus
class _ModuleStatus(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ModuleStatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    OK = typing___cast(ModuleStatusValue, 0)
    Error = typing___cast(ModuleStatusValue, 1)
OK = typing___cast(ModuleStatusValue, 0)
Error = typing___cast(ModuleStatusValue, 1)

class DeviceStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class RelayStatus(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        disabled: builtin___bool = ...

        def __init__(self,
            *,
            disabled : typing___Optional[builtin___bool] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"disabled",b"disabled"]) -> None: ...
    type___RelayStatus = RelayStatus

    class SensorStatus(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        status: typing___Text = ...
        color: type___DeviceValueColorValue = ...

        def __init__(self,
            *,
            status : typing___Optional[typing___Text] = None,
            color : typing___Optional[type___DeviceValueColorValue] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"color",b"color",u"status",b"status"]) -> None: ...
    type___SensorStatus = SensorStatus

    device: type___DeviceValue = ...
    label: typing___Text = ...

    @property
    def relay_type(self) -> type___DeviceStatus.RelayStatus: ...

    @property
    def sensor_type(self) -> type___DeviceStatus.SensorStatus: ...

    def __init__(self,
        *,
        device : typing___Optional[type___DeviceValue] = None,
        label : typing___Optional[typing___Text] = None,
        relay_type : typing___Optional[type___DeviceStatus.RelayStatus] = None,
        sensor_type : typing___Optional[type___DeviceStatus.SensorStatus] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"relay_type",b"relay_type",u"sensor_type",b"sensor_type",u"type",b"type"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"device",b"device",u"label",b"label",u"relay_type",b"relay_type",u"sensor_type",b"sensor_type",u"type",b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"type",b"type"]) -> typing_extensions___Literal["relay_type","sensor_type"]: ...
type___DeviceStatus = DeviceStatus

class PowerStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___PowerStatusRequest = PowerStatusRequest

class PowerStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def devices(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___DeviceStatus]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        devices : typing___Optional[typing___Iterable[type___DeviceStatus]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"devices",b"devices",u"ts",b"ts"]) -> None: ...
type___PowerStatusResponse = PowerStatusResponse

class ValueWithRange(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    value: builtin___float = ...
    is_ok: builtin___bool = ...

    def __init__(self,
        *,
        value : typing___Optional[builtin___float] = None,
        is_ok : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"is_ok",b"is_ok",u"value",b"value"]) -> None: ...
type___ValueWithRange = ValueWithRange

class EnvironmentalSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def temperature_c(self) -> type___ValueWithRange: ...

    @property
    def humidity_rh(self) -> type___ValueWithRange: ...

    @property
    def pressure_hpa(self) -> type___ValueWithRange: ...

    def __init__(self,
        *,
        temperature_c : typing___Optional[type___ValueWithRange] = None,
        humidity_rh : typing___Optional[type___ValueWithRange] = None,
        pressure_hpa : typing___Optional[type___ValueWithRange] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"humidity_rh",b"humidity_rh",u"pressure_hpa",b"pressure_hpa",u"temperature_c",b"temperature_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"humidity_rh",b"humidity_rh",u"pressure_hpa",b"pressure_hpa",u"temperature_c",b"temperature_c"]) -> None: ...
type___EnvironmentalSensorData = EnvironmentalSensorData

class CoolantSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def temperature_c(self) -> type___ValueWithRange: ...

    @property
    def pressure_kpa(self) -> type___ValueWithRange: ...

    def __init__(self,
        *,
        temperature_c : typing___Optional[type___ValueWithRange] = None,
        pressure_kpa : typing___Optional[type___ValueWithRange] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pressure_kpa",b"pressure_kpa",u"temperature_c",b"temperature_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"pressure_kpa",b"pressure_kpa",u"temperature_c",b"temperature_c"]) -> None: ...
type___CoolantSensorData = CoolantSensorData

class NetworkPortState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    link_up: builtin___bool = ...
    actual_link_speed: type___NetworkLinkSpeedValue = ...
    expected_link_speed: type___NetworkLinkSpeedValue = ...

    def __init__(self,
        *,
        link_up : typing___Optional[builtin___bool] = None,
        actual_link_speed : typing___Optional[type___NetworkLinkSpeedValue] = None,
        expected_link_speed : typing___Optional[type___NetworkLinkSpeedValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"actual_link_speed",b"actual_link_speed",u"expected_link_speed",b"expected_link_speed",u"link_up",b"link_up"]) -> None: ...
type___NetworkPortState = NetworkPortState

class ReaperPcSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uptime: builtin___int = ...

    @property
    def temperature_cpu_core_c(self) -> type___ValueWithRange: ...

    @property
    def temperature_system_c(self) -> type___ValueWithRange: ...

    @property
    def temperature_gpu_1_c(self) -> type___ValueWithRange: ...

    @property
    def temperature_gpu_2_c(self) -> type___ValueWithRange: ...

    @property
    def psu_12v(self) -> type___ValueWithRange: ...

    @property
    def psu_5v(self) -> type___ValueWithRange: ...

    @property
    def psu_3v3(self) -> type___ValueWithRange: ...

    @property
    def load(self) -> type___ValueWithRange: ...

    @property
    def ram_usage_percent(self) -> type___ValueWithRange: ...

    @property
    def disk_usage_percent(self) -> type___ValueWithRange: ...

    @property
    def scanner_a_link(self) -> type___NetworkPortState: ...

    @property
    def scanner_b_link(self) -> type___NetworkPortState: ...

    @property
    def target_cam_a_link(self) -> type___NetworkPortState: ...

    @property
    def target_cam_b_link(self) -> type___NetworkPortState: ...

    @property
    def predict_cam_link(self) -> type___NetworkPortState: ...

    @property
    def ipmi_link(self) -> type___NetworkPortState: ...

    @property
    def ext_link(self) -> type___NetworkPortState: ...

    def __init__(self,
        *,
        temperature_cpu_core_c : typing___Optional[type___ValueWithRange] = None,
        temperature_system_c : typing___Optional[type___ValueWithRange] = None,
        temperature_gpu_1_c : typing___Optional[type___ValueWithRange] = None,
        temperature_gpu_2_c : typing___Optional[type___ValueWithRange] = None,
        psu_12v : typing___Optional[type___ValueWithRange] = None,
        psu_5v : typing___Optional[type___ValueWithRange] = None,
        psu_3v3 : typing___Optional[type___ValueWithRange] = None,
        load : typing___Optional[type___ValueWithRange] = None,
        uptime : typing___Optional[builtin___int] = None,
        ram_usage_percent : typing___Optional[type___ValueWithRange] = None,
        disk_usage_percent : typing___Optional[type___ValueWithRange] = None,
        scanner_a_link : typing___Optional[type___NetworkPortState] = None,
        scanner_b_link : typing___Optional[type___NetworkPortState] = None,
        target_cam_a_link : typing___Optional[type___NetworkPortState] = None,
        target_cam_b_link : typing___Optional[type___NetworkPortState] = None,
        predict_cam_link : typing___Optional[type___NetworkPortState] = None,
        ipmi_link : typing___Optional[type___NetworkPortState] = None,
        ext_link : typing___Optional[type___NetworkPortState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_temperature_gpu_1_c",b"_temperature_gpu_1_c",u"_temperature_gpu_2_c",b"_temperature_gpu_2_c",u"disk_usage_percent",b"disk_usage_percent",u"ext_link",b"ext_link",u"ipmi_link",b"ipmi_link",u"load",b"load",u"predict_cam_link",b"predict_cam_link",u"psu_12v",b"psu_12v",u"psu_3v3",b"psu_3v3",u"psu_5v",b"psu_5v",u"ram_usage_percent",b"ram_usage_percent",u"scanner_a_link",b"scanner_a_link",u"scanner_b_link",b"scanner_b_link",u"target_cam_a_link",b"target_cam_a_link",u"target_cam_b_link",b"target_cam_b_link",u"temperature_cpu_core_c",b"temperature_cpu_core_c",u"temperature_gpu_1_c",b"temperature_gpu_1_c",u"temperature_gpu_2_c",b"temperature_gpu_2_c",u"temperature_system_c",b"temperature_system_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_temperature_gpu_1_c",b"_temperature_gpu_1_c",u"_temperature_gpu_2_c",b"_temperature_gpu_2_c",u"disk_usage_percent",b"disk_usage_percent",u"ext_link",b"ext_link",u"ipmi_link",b"ipmi_link",u"load",b"load",u"predict_cam_link",b"predict_cam_link",u"psu_12v",b"psu_12v",u"psu_3v3",b"psu_3v3",u"psu_5v",b"psu_5v",u"ram_usage_percent",b"ram_usage_percent",u"scanner_a_link",b"scanner_a_link",u"scanner_b_link",b"scanner_b_link",u"target_cam_a_link",b"target_cam_a_link",u"target_cam_b_link",b"target_cam_b_link",u"temperature_cpu_core_c",b"temperature_cpu_core_c",u"temperature_gpu_1_c",b"temperature_gpu_1_c",u"temperature_gpu_2_c",b"temperature_gpu_2_c",u"temperature_system_c",b"temperature_system_c",u"uptime",b"uptime"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_temperature_gpu_1_c",b"_temperature_gpu_1_c"]) -> typing_extensions___Literal["temperature_gpu_1_c"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_temperature_gpu_2_c",b"_temperature_gpu_2_c"]) -> typing_extensions___Literal["temperature_gpu_2_c"]: ...
type___ReaperPcSensorData = ReaperPcSensorData

class ReaperScannerLaserStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model: typing___Text = ...
    sn: typing___Text = ...
    rated_power: builtin___int = ...
    faults: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    @property
    def temperature_c(self) -> type___ValueWithRange: ...

    @property
    def humidity(self) -> type___ValueWithRange: ...

    @property
    def laser_current_ma(self) -> type___ValueWithRange: ...

    def __init__(self,
        *,
        model : typing___Optional[typing___Text] = None,
        sn : typing___Optional[typing___Text] = None,
        rated_power : typing___Optional[builtin___int] = None,
        temperature_c : typing___Optional[type___ValueWithRange] = None,
        humidity : typing___Optional[type___ValueWithRange] = None,
        laser_current_ma : typing___Optional[type___ValueWithRange] = None,
        faults : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"humidity",b"humidity",u"laser_current_ma",b"laser_current_ma",u"temperature_c",b"temperature_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"faults",b"faults",u"humidity",b"humidity",u"laser_current_ma",b"laser_current_ma",u"model",b"model",u"rated_power",b"rated_power",u"sn",b"sn",u"temperature_c",b"temperature_c"]) -> None: ...
type___ReaperScannerLaserStatus = ReaperScannerLaserStatus

class ReaperScannerMotorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    controller_sn: typing___Text = ...
    encoder_position: builtin___int = ...

    @property
    def temperature_output_c(self) -> type___ValueWithRange: ...

    @property
    def motor_supply_v(self) -> type___ValueWithRange: ...

    @property
    def motor_current_a(self) -> type___ValueWithRange: ...

    def __init__(self,
        *,
        controller_sn : typing___Optional[typing___Text] = None,
        temperature_output_c : typing___Optional[type___ValueWithRange] = None,
        motor_supply_v : typing___Optional[type___ValueWithRange] = None,
        motor_current_a : typing___Optional[type___ValueWithRange] = None,
        encoder_position : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"motor_current_a",b"motor_current_a",u"motor_supply_v",b"motor_supply_v",u"temperature_output_c",b"temperature_output_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"controller_sn",b"controller_sn",u"encoder_position",b"encoder_position",u"motor_current_a",b"motor_current_a",u"motor_supply_v",b"motor_supply_v",u"temperature_output_c",b"temperature_output_c"]) -> None: ...
type___ReaperScannerMotorData = ReaperScannerMotorData

class ReaperScannerSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    scanner_sn: typing___Text = ...
    fuse_tripped: builtin___bool = ...
    laser_connected: builtin___bool = ...
    target_connected: builtin___bool = ...
    target_sn: typing___Text = ...
    scanner_power_enabled: builtin___bool = ...
    target_cam_power_enabled: builtin___bool = ...

    @property
    def current_a(self) -> type___ValueWithRange: ...

    @property
    def temperature_collimator_c(self) -> type___ValueWithRange: ...

    @property
    def temperature_fiber_c(self) -> type___ValueWithRange: ...

    @property
    def laser_power_w(self) -> type___ValueWithRange: ...

    @property
    def laser_status(self) -> type___ReaperScannerLaserStatus: ...

    @property
    def temperature_target_c(self) -> type___ValueWithRange: ...

    @property
    def motor_pan(self) -> type___ReaperScannerMotorData: ...

    @property
    def motor_tilt(self) -> type___ReaperScannerMotorData: ...

    def __init__(self,
        *,
        scanner_sn : typing___Optional[typing___Text] = None,
        current_a : typing___Optional[type___ValueWithRange] = None,
        fuse_tripped : typing___Optional[builtin___bool] = None,
        temperature_collimator_c : typing___Optional[type___ValueWithRange] = None,
        temperature_fiber_c : typing___Optional[type___ValueWithRange] = None,
        laser_power_w : typing___Optional[type___ValueWithRange] = None,
        laser_connected : typing___Optional[builtin___bool] = None,
        laser_status : typing___Optional[type___ReaperScannerLaserStatus] = None,
        target_connected : typing___Optional[builtin___bool] = None,
        target_sn : typing___Optional[typing___Text] = None,
        temperature_target_c : typing___Optional[type___ValueWithRange] = None,
        motor_pan : typing___Optional[type___ReaperScannerMotorData] = None,
        motor_tilt : typing___Optional[type___ReaperScannerMotorData] = None,
        scanner_power_enabled : typing___Optional[builtin___bool] = None,
        target_cam_power_enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_laser_status",b"_laser_status",u"_motor_pan",b"_motor_pan",u"_motor_tilt",b"_motor_tilt",u"_target_sn",b"_target_sn",u"_temperature_target_c",b"_temperature_target_c",u"current_a",b"current_a",u"laser_power_w",b"laser_power_w",u"laser_status",b"laser_status",u"motor_pan",b"motor_pan",u"motor_tilt",b"motor_tilt",u"target_sn",b"target_sn",u"temperature_collimator_c",b"temperature_collimator_c",u"temperature_fiber_c",b"temperature_fiber_c",u"temperature_target_c",b"temperature_target_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_laser_status",b"_laser_status",u"_motor_pan",b"_motor_pan",u"_motor_tilt",b"_motor_tilt",u"_target_sn",b"_target_sn",u"_temperature_target_c",b"_temperature_target_c",u"current_a",b"current_a",u"fuse_tripped",b"fuse_tripped",u"laser_connected",b"laser_connected",u"laser_power_w",b"laser_power_w",u"laser_status",b"laser_status",u"motor_pan",b"motor_pan",u"motor_tilt",b"motor_tilt",u"scanner_power_enabled",b"scanner_power_enabled",u"scanner_sn",b"scanner_sn",u"target_cam_power_enabled",b"target_cam_power_enabled",u"target_connected",b"target_connected",u"target_sn",b"target_sn",u"temperature_collimator_c",b"temperature_collimator_c",u"temperature_fiber_c",b"temperature_fiber_c",u"temperature_target_c",b"temperature_target_c"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_laser_status",b"_laser_status"]) -> typing_extensions___Literal["laser_status"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_motor_pan",b"_motor_pan"]) -> typing_extensions___Literal["motor_pan"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_motor_tilt",b"_motor_tilt"]) -> typing_extensions___Literal["motor_tilt"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_target_sn",b"_target_sn"]) -> typing_extensions___Literal["target_sn"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_temperature_target_c",b"_temperature_target_c"]) -> typing_extensions___Literal["temperature_target_c"]: ...
type___ReaperScannerSensorData = ReaperScannerSensorData

class ReaperGpsData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    has_fix: builtin___bool = ...
    latitude: builtin___float = ...
    longitude: builtin___float = ...

    def __init__(self,
        *,
        has_fix : typing___Optional[builtin___bool] = None,
        latitude : typing___Optional[builtin___float] = None,
        longitude : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"has_fix",b"has_fix",u"latitude",b"latitude",u"longitude",b"longitude"]) -> None: ...
type___ReaperGpsData = ReaperGpsData

class ReaperWheelEncoderData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    front_left: builtin___int = ...
    front_right: builtin___int = ...

    def __init__(self,
        *,
        front_left : typing___Optional[builtin___int] = None,
        front_right : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"front_left",b"front_left",u"front_right",b"front_right"]) -> None: ...
type___ReaperWheelEncoderData = ReaperWheelEncoderData

class ReaperCenterEnclosureData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    water_protect_status: builtin___bool = ...
    main_contactor_status_fb: builtin___bool = ...
    power_status: type___AcPowerStatusValue = ...
    lifted_status: builtin___bool = ...
    temp_humidity_status: builtin___bool = ...
    tractor_power: builtin___bool = ...
    phase_power_w_3: builtin___int = ...
    phase_power_va_3: builtin___int = ...
    wheel_encoder_disabled: builtin___bool = ...
    strobe_disabled: builtin___bool = ...
    gps_disabled: builtin___bool = ...
    main_contactor_disabled: builtin___bool = ...
    air_conditioner_disabled: builtin___bool = ...
    chiller_disabled: builtin___bool = ...
    chiller_alarms: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    @property
    def ac_frequency(self) -> type___ValueWithRange: ...

    @property
    def ac_voltage_a_b(self) -> type___ValueWithRange: ...

    @property
    def ac_voltage_b_c(self) -> type___ValueWithRange: ...

    @property
    def ac_voltage_a_c(self) -> type___ValueWithRange: ...

    @property
    def ac_voltage_a(self) -> type___ValueWithRange: ...

    @property
    def ac_voltage_b(self) -> type___ValueWithRange: ...

    @property
    def ac_voltage_c(self) -> type___ValueWithRange: ...

    @property
    def power_factor(self) -> type___ValueWithRange: ...

    @property
    def server_cabinet_temp(self) -> type___ValueWithRange: ...

    @property
    def server_cabinet_humidity(self) -> type___ValueWithRange: ...

    @property
    def battery_voltage_12v(self) -> type___ValueWithRange: ...

    @property
    def chiller_temp_c(self) -> type___ValueWithRange: ...

    @property
    def chiller_flow_l_min(self) -> type___ValueWithRange: ...

    @property
    def chiller_pressure_psi(self) -> type___ValueWithRange: ...

    @property
    def chiller_conductivity_us_cm(self) -> type___ValueWithRange: ...

    @property
    def chiller_set_temp_c(self) -> type___ValueWithRange: ...

    @property
    def chiller_heat_transfer_kbtu(self) -> type___ValueWithRange: ...

    @property
    def chiller_fluid_delta_temp_c(self) -> type___ValueWithRange: ...

    @property
    def gps(self) -> type___ReaperGpsData: ...

    @property
    def wheel_encoder(self) -> type___ReaperWheelEncoderData: ...

    def __init__(self,
        *,
        water_protect_status : typing___Optional[builtin___bool] = None,
        main_contactor_status_fb : typing___Optional[builtin___bool] = None,
        power_status : typing___Optional[type___AcPowerStatusValue] = None,
        lifted_status : typing___Optional[builtin___bool] = None,
        temp_humidity_status : typing___Optional[builtin___bool] = None,
        tractor_power : typing___Optional[builtin___bool] = None,
        ac_frequency : typing___Optional[type___ValueWithRange] = None,
        ac_voltage_a_b : typing___Optional[type___ValueWithRange] = None,
        ac_voltage_b_c : typing___Optional[type___ValueWithRange] = None,
        ac_voltage_a_c : typing___Optional[type___ValueWithRange] = None,
        ac_voltage_a : typing___Optional[type___ValueWithRange] = None,
        ac_voltage_b : typing___Optional[type___ValueWithRange] = None,
        ac_voltage_c : typing___Optional[type___ValueWithRange] = None,
        phase_power_w_3 : typing___Optional[builtin___int] = None,
        phase_power_va_3 : typing___Optional[builtin___int] = None,
        power_factor : typing___Optional[type___ValueWithRange] = None,
        server_cabinet_temp : typing___Optional[type___ValueWithRange] = None,
        server_cabinet_humidity : typing___Optional[type___ValueWithRange] = None,
        battery_voltage_12v : typing___Optional[type___ValueWithRange] = None,
        wheel_encoder_disabled : typing___Optional[builtin___bool] = None,
        strobe_disabled : typing___Optional[builtin___bool] = None,
        gps_disabled : typing___Optional[builtin___bool] = None,
        main_contactor_disabled : typing___Optional[builtin___bool] = None,
        air_conditioner_disabled : typing___Optional[builtin___bool] = None,
        chiller_disabled : typing___Optional[builtin___bool] = None,
        chiller_alarms : typing___Optional[typing___Iterable[typing___Text]] = None,
        chiller_temp_c : typing___Optional[type___ValueWithRange] = None,
        chiller_flow_l_min : typing___Optional[type___ValueWithRange] = None,
        chiller_pressure_psi : typing___Optional[type___ValueWithRange] = None,
        chiller_conductivity_us_cm : typing___Optional[type___ValueWithRange] = None,
        chiller_set_temp_c : typing___Optional[type___ValueWithRange] = None,
        chiller_heat_transfer_kbtu : typing___Optional[type___ValueWithRange] = None,
        chiller_fluid_delta_temp_c : typing___Optional[type___ValueWithRange] = None,
        gps : typing___Optional[type___ReaperGpsData] = None,
        wheel_encoder : typing___Optional[type___ReaperWheelEncoderData] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ac_frequency",b"ac_frequency",u"ac_voltage_a",b"ac_voltage_a",u"ac_voltage_a_b",b"ac_voltage_a_b",u"ac_voltage_a_c",b"ac_voltage_a_c",u"ac_voltage_b",b"ac_voltage_b",u"ac_voltage_b_c",b"ac_voltage_b_c",u"ac_voltage_c",b"ac_voltage_c",u"battery_voltage_12v",b"battery_voltage_12v",u"chiller_conductivity_us_cm",b"chiller_conductivity_us_cm",u"chiller_flow_l_min",b"chiller_flow_l_min",u"chiller_fluid_delta_temp_c",b"chiller_fluid_delta_temp_c",u"chiller_heat_transfer_kbtu",b"chiller_heat_transfer_kbtu",u"chiller_pressure_psi",b"chiller_pressure_psi",u"chiller_set_temp_c",b"chiller_set_temp_c",u"chiller_temp_c",b"chiller_temp_c",u"gps",b"gps",u"power_factor",b"power_factor",u"server_cabinet_humidity",b"server_cabinet_humidity",u"server_cabinet_temp",b"server_cabinet_temp",u"wheel_encoder",b"wheel_encoder"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ac_frequency",b"ac_frequency",u"ac_voltage_a",b"ac_voltage_a",u"ac_voltage_a_b",b"ac_voltage_a_b",u"ac_voltage_a_c",b"ac_voltage_a_c",u"ac_voltage_b",b"ac_voltage_b",u"ac_voltage_b_c",b"ac_voltage_b_c",u"ac_voltage_c",b"ac_voltage_c",u"air_conditioner_disabled",b"air_conditioner_disabled",u"battery_voltage_12v",b"battery_voltage_12v",u"chiller_alarms",b"chiller_alarms",u"chiller_conductivity_us_cm",b"chiller_conductivity_us_cm",u"chiller_disabled",b"chiller_disabled",u"chiller_flow_l_min",b"chiller_flow_l_min",u"chiller_fluid_delta_temp_c",b"chiller_fluid_delta_temp_c",u"chiller_heat_transfer_kbtu",b"chiller_heat_transfer_kbtu",u"chiller_pressure_psi",b"chiller_pressure_psi",u"chiller_set_temp_c",b"chiller_set_temp_c",u"chiller_temp_c",b"chiller_temp_c",u"gps",b"gps",u"gps_disabled",b"gps_disabled",u"lifted_status",b"lifted_status",u"main_contactor_disabled",b"main_contactor_disabled",u"main_contactor_status_fb",b"main_contactor_status_fb",u"phase_power_va_3",b"phase_power_va_3",u"phase_power_w_3",b"phase_power_w_3",u"power_factor",b"power_factor",u"power_status",b"power_status",u"server_cabinet_humidity",b"server_cabinet_humidity",u"server_cabinet_temp",b"server_cabinet_temp",u"strobe_disabled",b"strobe_disabled",u"temp_humidity_status",b"temp_humidity_status",u"tractor_power",b"tractor_power",u"water_protect_status",b"water_protect_status",u"wheel_encoder",b"wheel_encoder",u"wheel_encoder_disabled",b"wheel_encoder_disabled"]) -> None: ...
type___ReaperCenterEnclosureData = ReaperCenterEnclosureData

class ReaperModuleSensorData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    module_sn: typing___Text = ...
    pc_power_enabled: builtin___bool = ...
    lasers_power_enabled: builtin___bool = ...
    predict_cam_power_enabled: builtin___bool = ...
    strobe_power_enabled: builtin___bool = ...
    strobe_enabled: builtin___bool = ...
    status: type___ModuleStatusValue = ...

    @property
    def enviro_enclosure(self) -> type___EnvironmentalSensorData: ...

    @property
    def enviro_pc(self) -> type___EnvironmentalSensorData: ...

    @property
    def coolant_inlet(self) -> type___CoolantSensorData: ...

    @property
    def coolant_outlet(self) -> type___CoolantSensorData: ...

    @property
    def strobe_temperature_c(self) -> type___ValueWithRange: ...

    @property
    def strobe_cap_voltage(self) -> type___ValueWithRange: ...

    @property
    def strobe_current(self) -> type___ValueWithRange: ...

    @property
    def pc(self) -> type___ReaperPcSensorData: ...

    @property
    def scanner_a(self) -> type___ReaperScannerSensorData: ...

    @property
    def scanner_b(self) -> type___ReaperScannerSensorData: ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        module_sn : typing___Optional[typing___Text] = None,
        enviro_enclosure : typing___Optional[type___EnvironmentalSensorData] = None,
        enviro_pc : typing___Optional[type___EnvironmentalSensorData] = None,
        coolant_inlet : typing___Optional[type___CoolantSensorData] = None,
        coolant_outlet : typing___Optional[type___CoolantSensorData] = None,
        strobe_temperature_c : typing___Optional[type___ValueWithRange] = None,
        strobe_cap_voltage : typing___Optional[type___ValueWithRange] = None,
        strobe_current : typing___Optional[type___ValueWithRange] = None,
        pc : typing___Optional[type___ReaperPcSensorData] = None,
        scanner_a : typing___Optional[type___ReaperScannerSensorData] = None,
        scanner_b : typing___Optional[type___ReaperScannerSensorData] = None,
        pc_power_enabled : typing___Optional[builtin___bool] = None,
        lasers_power_enabled : typing___Optional[builtin___bool] = None,
        predict_cam_power_enabled : typing___Optional[builtin___bool] = None,
        strobe_power_enabled : typing___Optional[builtin___bool] = None,
        strobe_enabled : typing___Optional[builtin___bool] = None,
        status : typing___Optional[type___ModuleStatusValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_pc",b"_pc",u"_scanner_a",b"_scanner_a",u"_scanner_b",b"_scanner_b",u"coolant_inlet",b"coolant_inlet",u"coolant_outlet",b"coolant_outlet",u"enviro_enclosure",b"enviro_enclosure",u"enviro_pc",b"enviro_pc",u"pc",b"pc",u"scanner_a",b"scanner_a",u"scanner_b",b"scanner_b",u"strobe_cap_voltage",b"strobe_cap_voltage",u"strobe_current",b"strobe_current",u"strobe_temperature_c",b"strobe_temperature_c"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_pc",b"_pc",u"_scanner_a",b"_scanner_a",u"_scanner_b",b"_scanner_b",u"coolant_inlet",b"coolant_inlet",u"coolant_outlet",b"coolant_outlet",u"enviro_enclosure",b"enviro_enclosure",u"enviro_pc",b"enviro_pc",u"lasers_power_enabled",b"lasers_power_enabled",u"module_id",b"module_id",u"module_sn",b"module_sn",u"pc",b"pc",u"pc_power_enabled",b"pc_power_enabled",u"predict_cam_power_enabled",b"predict_cam_power_enabled",u"scanner_a",b"scanner_a",u"scanner_b",b"scanner_b",u"status",b"status",u"strobe_cap_voltage",b"strobe_cap_voltage",u"strobe_current",b"strobe_current",u"strobe_enabled",b"strobe_enabled",u"strobe_power_enabled",b"strobe_power_enabled",u"strobe_temperature_c",b"strobe_temperature_c"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_pc",b"_pc"]) -> typing_extensions___Literal["pc"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scanner_a",b"_scanner_a"]) -> typing_extensions___Literal["scanner_a"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scanner_b",b"_scanner_b"]) -> typing_extensions___Literal["scanner_b"]: ...
type___ReaperModuleSensorData = ReaperModuleSensorData

class CenterEnclosureStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___CenterEnclosureStatusRequest = CenterEnclosureStatusRequest

class ModuleHardwareStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"module_id",b"module_id"]) -> None: ...
type___ModuleHardwareStatusRequest = ModuleHardwareStatusRequest

class GetNextReaperHardwareStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def center_enclosure_status(self) -> type___CenterEnclosureStatusRequest: ...

    @property
    def module_status(self) -> type___ModuleHardwareStatusRequest: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        center_enclosure_status : typing___Optional[type___CenterEnclosureStatusRequest] = None,
        module_status : typing___Optional[type___ModuleHardwareStatusRequest] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"center_enclosure_status",b"center_enclosure_status",u"module_status",b"module_status",u"request",b"request",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_enclosure_status",b"center_enclosure_status",u"module_status",b"module_status",u"request",b"request",u"ts",b"ts"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"request",b"request"]) -> typing_extensions___Literal["center_enclosure_status","module_status"]: ...
type___GetNextReaperHardwareStatusRequest = GetNextReaperHardwareStatusRequest

class GetNextReaperHardwareStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def center_enclosure_status(self) -> type___ReaperCenterEnclosureData: ...

    @property
    def module_status(self) -> type___ReaperModuleSensorData: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        center_enclosure_status : typing___Optional[type___ReaperCenterEnclosureData] = None,
        module_status : typing___Optional[type___ReaperModuleSensorData] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"center_enclosure_status",b"center_enclosure_status",u"module_status",b"module_status",u"response",b"response",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_enclosure_status",b"center_enclosure_status",u"module_status",b"module_status",u"response",b"response",u"ts",b"ts"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"response",b"response"]) -> typing_extensions___Literal["center_enclosure_status","module_status"]: ...
type___GetNextReaperHardwareStatusResponse = GetNextReaperHardwareStatusResponse

class GetNextReaperAllHardwareStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___GetNextReaperAllHardwareStatusRequest = GetNextReaperAllHardwareStatusRequest

class GetNextReaperAllHardwareStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def center_enclosure_status(self) -> type___ReaperCenterEnclosureData: ...

    @property
    def module_status(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ReaperModuleSensorData]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        center_enclosure_status : typing___Optional[type___ReaperCenterEnclosureData] = None,
        module_status : typing___Optional[typing___Iterable[type___ReaperModuleSensorData]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"center_enclosure_status",b"center_enclosure_status",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"center_enclosure_status",b"center_enclosure_status",u"module_status",b"module_status",u"ts",b"ts"]) -> None: ...
type___GetNextReaperAllHardwareStatusResponse = GetNextReaperAllHardwareStatusResponse

class RelayRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    device: type___DeviceValue = ...

    def __init__(self,
        *,
        device : typing___Optional[type___DeviceValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"device",b"device"]) -> None: ...
type___RelayRequest = RelayRequest

class RelayResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___RelayResponse = RelayResponse

class SetReaperScannerPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    scanner_a_power: builtin___bool = ...
    scanner_b_power: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        scanner_a_power : typing___Optional[builtin___bool] = None,
        scanner_b_power : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_scanner_a_power",b"_scanner_a_power",u"_scanner_b_power",b"_scanner_b_power",u"scanner_a_power",b"scanner_a_power",u"scanner_b_power",b"scanner_b_power"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_scanner_a_power",b"_scanner_a_power",u"_scanner_b_power",b"_scanner_b_power",u"module_id",b"module_id",u"scanner_a_power",b"scanner_a_power",u"scanner_b_power",b"scanner_b_power"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scanner_a_power",b"_scanner_a_power"]) -> typing_extensions___Literal["scanner_a_power"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_scanner_b_power",b"_scanner_b_power"]) -> typing_extensions___Literal["scanner_b_power"]: ...
type___SetReaperScannerPowerRequest = SetReaperScannerPowerRequest

class SetReaperScannerPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperScannerPowerResponse = SetReaperScannerPowerResponse

class SetReaperTargetPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    target_a_power: builtin___bool = ...
    target_b_power: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        target_a_power : typing___Optional[builtin___bool] = None,
        target_b_power : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_target_a_power",b"_target_a_power",u"_target_b_power",b"_target_b_power",u"target_a_power",b"target_a_power",u"target_b_power",b"target_b_power"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_target_a_power",b"_target_a_power",u"_target_b_power",b"_target_b_power",u"module_id",b"module_id",u"target_a_power",b"target_a_power",u"target_b_power",b"target_b_power"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_target_a_power",b"_target_a_power"]) -> typing_extensions___Literal["target_a_power"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_target_b_power",b"_target_b_power"]) -> typing_extensions___Literal["target_b_power"]: ...
type___SetReaperTargetPowerRequest = SetReaperTargetPowerRequest

class SetReaperTargetPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperTargetPowerResponse = SetReaperTargetPowerResponse

class SetReaperPredictCamPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"module_id",b"module_id"]) -> None: ...
type___SetReaperPredictCamPowerRequest = SetReaperPredictCamPowerRequest

class SetReaperPredictCamPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperPredictCamPowerResponse = SetReaperPredictCamPowerResponse

class SetReaperStrobeEnableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"module_id",b"module_id"]) -> None: ...
type___SetReaperStrobeEnableRequest = SetReaperStrobeEnableRequest

class SetReaperStrobeEnableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperStrobeEnableResponse = SetReaperStrobeEnableResponse

class SetReaperAllStrobesEnableRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled"]) -> None: ...
type___SetReaperAllStrobesEnableRequest = SetReaperAllStrobesEnableRequest

class SetReaperAllStrobesEnableResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperAllStrobesEnableResponse = SetReaperAllStrobesEnableResponse

class SetReaperModulePcPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"module_id",b"module_id"]) -> None: ...
type___SetReaperModulePcPowerRequest = SetReaperModulePcPowerRequest

class SetReaperModulePcPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperModulePcPowerResponse = SetReaperModulePcPowerResponse

class SetReaperModuleLaserPowerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    module_id: builtin___int = ...
    enabled: builtin___bool = ...

    def __init__(self,
        *,
        module_id : typing___Optional[builtin___int] = None,
        enabled : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"enabled",b"enabled",u"module_id",b"module_id"]) -> None: ...
type___SetReaperModuleLaserPowerRequest = SetReaperModuleLaserPowerRequest

class SetReaperModuleLaserPowerResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    success: builtin___bool = ...

    def __init__(self,
        *,
        success : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"success",b"success"]) -> None: ...
type___SetReaperModuleLaserPowerResponse = SetReaperModuleLaserPowerResponse

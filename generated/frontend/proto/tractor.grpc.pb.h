// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/tractor.proto
#ifndef GRPC_frontend_2fproto_2ftractor_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2ftractor_2eproto__INCLUDED

#include "frontend/proto/tractor.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace tractor {

class TractorService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.tractor.TractorService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>> AsyncGetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>>(AsyncGetNextTractorIfStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>> PrepareAsyncGetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>>(PrepareAsyncGetNextTractorIfStateRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>> AsyncGetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>>(AsyncGetNextTractorSafetyStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>> PrepareAsyncGetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>>(PrepareAsyncGetNextTractorSafetyStateRaw(context, request, cq));
    }
    virtual ::grpc::Status SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>> AsyncSetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>>(AsyncSetEnforcementPolicyRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>> PrepareAsyncSetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>>(PrepareAsyncSetEnforcementPolicyRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* AsyncGetNextTractorIfStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* PrepareAsyncGetNextTractorIfStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* AsyncGetNextTractorSafetyStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* PrepareAsyncGetNextTractorSafetyStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* AsyncSetEnforcementPolicyRaw(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* PrepareAsyncSetEnforcementPolicyRaw(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>> AsyncGetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>>(AsyncGetNextTractorIfStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>> PrepareAsyncGetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>>(PrepareAsyncGetNextTractorIfStateRaw(context, request, cq));
    }
    ::grpc::Status GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>> AsyncGetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>>(AsyncGetNextTractorSafetyStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>> PrepareAsyncGetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>>(PrepareAsyncGetNextTractorSafetyStateRaw(context, request, cq));
    }
    ::grpc::Status SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>> AsyncSetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>>(AsyncSetEnforcementPolicyRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>> PrepareAsyncSetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>>(PrepareAsyncSetEnforcementPolicyRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextTractorIfState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextTractorSafetyState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response, std::function<void(::grpc::Status)>) override;
      void SetEnforcementPolicy(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* AsyncGetNextTractorIfStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* PrepareAsyncGetNextTractorIfStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* AsyncGetNextTractorSafetyStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* PrepareAsyncGetNextTractorSafetyStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* AsyncSetEnforcementPolicyRaw(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* PrepareAsyncSetEnforcementPolicyRaw(::grpc::ClientContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextTractorIfState_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextTractorSafetyState_;
    const ::grpc::internal::RpcMethod rpcmethod_SetEnforcementPolicy_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextTractorIfState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response);
    virtual ::grpc::Status GetNextTractorSafetyState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response);
    virtual ::grpc::Status SetEnforcementPolicy(::grpc::ServerContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextTractorIfState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextTractorIfState() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextTractorIfState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorIfState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextTractorIfState(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextTractorSafetyState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextTractorSafetyState() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextTractorSafetyState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorSafetyState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextTractorSafetyState(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetEnforcementPolicy : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetEnforcementPolicy() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_SetEnforcementPolicy() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEnforcementPolicy(::grpc::ServerContext* /*context*/, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* /*request*/, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetEnforcementPolicy(::grpc::ServerContext* context, ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextTractorIfState<WithAsyncMethod_GetNextTractorSafetyState<WithAsyncMethod_SetEnforcementPolicy<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextTractorIfState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextTractorIfState() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorIfStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* response) { return this->GetNextTractorIfState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextTractorIfState(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorIfStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextTractorIfState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorIfState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextTractorIfState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextTractorSafetyState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextTractorSafetyState() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* response) { return this->GetNextTractorSafetyState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextTractorSafetyState(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextTractorSafetyState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorSafetyState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextTractorSafetyState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetEnforcementPolicy : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetEnforcementPolicy() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::carbon::frontend::tractor::SetEnforcementPolicyResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* request, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* response) { return this->SetEnforcementPolicy(context, request, response); }));}
    void SetMessageAllocatorFor_SetEnforcementPolicy(
        ::grpc::MessageAllocator< ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::carbon::frontend::tractor::SetEnforcementPolicyResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetEnforcementPolicy() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEnforcementPolicy(::grpc::ServerContext* /*context*/, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* /*request*/, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetEnforcementPolicy(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* /*request*/, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextTractorIfState<WithCallbackMethod_GetNextTractorSafetyState<WithCallbackMethod_SetEnforcementPolicy<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextTractorIfState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextTractorIfState() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextTractorIfState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorIfState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextTractorSafetyState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextTractorSafetyState() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextTractorSafetyState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorSafetyState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetEnforcementPolicy : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetEnforcementPolicy() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_SetEnforcementPolicy() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEnforcementPolicy(::grpc::ServerContext* /*context*/, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* /*request*/, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextTractorIfState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextTractorIfState() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextTractorIfState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorIfState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextTractorIfState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextTractorSafetyState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextTractorSafetyState() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextTractorSafetyState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorSafetyState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextTractorSafetyState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetEnforcementPolicy : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetEnforcementPolicy() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_SetEnforcementPolicy() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEnforcementPolicy(::grpc::ServerContext* /*context*/, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* /*request*/, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetEnforcementPolicy(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextTractorIfState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextTractorIfState() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextTractorIfState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextTractorIfState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorIfState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextTractorIfState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextTractorSafetyState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextTractorSafetyState() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextTractorSafetyState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextTractorSafetyState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTractorSafetyState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextTractorSafetyState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetEnforcementPolicy : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetEnforcementPolicy() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetEnforcementPolicy(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetEnforcementPolicy() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetEnforcementPolicy(::grpc::ServerContext* /*context*/, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* /*request*/, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetEnforcementPolicy(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextTractorIfState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextTractorIfState() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorIfStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorIfStateResponse>* streamer) {
                       return this->StreamedGetNextTractorIfState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextTractorIfState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextTractorIfState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorIfStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextTractorIfState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::tractor::GetNextTractorIfStateResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextTractorSafetyState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextTractorSafetyState() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* streamer) {
                       return this->StreamedGetNextTractorSafetyState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextTractorSafetyState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextTractorSafetyState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextTractorSafetyState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::tractor::GetNextTractorSafetyStateResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetEnforcementPolicy : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetEnforcementPolicy() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::carbon::frontend::tractor::SetEnforcementPolicyResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::tractor::SetEnforcementPolicyRequest, ::carbon::frontend::tractor::SetEnforcementPolicyResponse>* streamer) {
                       return this->StreamedSetEnforcementPolicy(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetEnforcementPolicy() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetEnforcementPolicy(::grpc::ServerContext* /*context*/, const ::carbon::frontend::tractor::SetEnforcementPolicyRequest* /*request*/, ::carbon::frontend::tractor::SetEnforcementPolicyResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetEnforcementPolicy(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::tractor::SetEnforcementPolicyRequest,::carbon::frontend::tractor::SetEnforcementPolicyResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextTractorIfState<WithStreamedUnaryMethod_GetNextTractorSafetyState<WithStreamedUnaryMethod_SetEnforcementPolicy<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextTractorIfState<WithStreamedUnaryMethod_GetNextTractorSafetyState<WithStreamedUnaryMethod_SetEnforcementPolicy<Service > > > StreamedService;
};

}  // namespace tractor
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2ftractor_2eproto__INCLUDED

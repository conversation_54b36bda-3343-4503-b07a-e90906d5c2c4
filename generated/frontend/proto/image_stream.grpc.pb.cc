// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/image_stream.proto

#include "frontend/proto/image_stream.pb.h"
#include "frontend/proto/image_stream.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace image_stream {

static const char* ImageStreamService_method_names[] = {
  "/carbon.frontend.image_stream.ImageStreamService/GetNextCameraImage",
  "/carbon.frontend.image_stream.ImageStreamService/GetPredictImageByTimestamp",
  "/carbon.frontend.image_stream.ImageStreamService/GetMultiPredictPerspectives",
};

std::unique_ptr< ImageStreamService::Stub> ImageStreamService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ImageStreamService::Stub> stub(new ImageStreamService::Stub(channel, options));
  return stub;
}

ImageStreamService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextCameraImage_(ImageStreamService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetPredictImageByTimestamp_(ImageStreamService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetMultiPredictPerspectives_(ImageStreamService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ImageStreamService::Stub::GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::carbon::frontend::image_stream::Image* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::image_stream::CameraImageRequest, ::carbon::frontend::image_stream::Image, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextCameraImage_, context, request, response);
}

void ImageStreamService::Stub::async::GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::image_stream::CameraImageRequest, ::carbon::frontend::image_stream::Image, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCameraImage_, context, request, response, std::move(f));
}

void ImageStreamService::Stub::async::GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCameraImage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::Image>* ImageStreamService::Stub::PrepareAsyncGetNextCameraImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::image_stream::Image, ::carbon::frontend::image_stream::CameraImageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextCameraImage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::Image>* ImageStreamService::Stub::AsyncGetNextCameraImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextCameraImageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ImageStreamService::Stub::GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetPredictImageByTimestamp_, context, request, response);
}

void ImageStreamService::Stub::async::GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPredictImageByTimestamp_, context, request, response, std::move(f));
}

void ImageStreamService::Stub::async::GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPredictImageByTimestamp_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* ImageStreamService::Stub::PrepareAsyncGetPredictImageByTimestampRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse, ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetPredictImageByTimestamp_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* ImageStreamService::Stub::AsyncGetPredictImageByTimestampRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetPredictImageByTimestampRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ImageStreamService::Stub::GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetMultiPredictPerspectives_, context, request, response);
}

void ImageStreamService::Stub::async::GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetMultiPredictPerspectives_, context, request, response, std::move(f));
}

void ImageStreamService::Stub::async::GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetMultiPredictPerspectives_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* ImageStreamService::Stub::PrepareAsyncGetMultiPredictPerspectivesRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetMultiPredictPerspectives_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* ImageStreamService::Stub::AsyncGetMultiPredictPerspectivesRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetMultiPredictPerspectivesRaw(context, request, cq);
  result->StartCall();
  return result;
}

ImageStreamService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ImageStreamService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ImageStreamService::Service, ::carbon::frontend::image_stream::CameraImageRequest, ::carbon::frontend::image_stream::Image, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ImageStreamService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::image_stream::CameraImageRequest* req,
             ::carbon::frontend::image_stream::Image* resp) {
               return service->GetNextCameraImage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ImageStreamService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ImageStreamService::Service, ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ImageStreamService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* req,
             ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* resp) {
               return service->GetPredictImageByTimestamp(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ImageStreamService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ImageStreamService::Service, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ImageStreamService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* req,
             ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* resp) {
               return service->GetMultiPredictPerspectives(ctx, req, resp);
             }, this)));
}

ImageStreamService::Service::~Service() {
}

::grpc::Status ImageStreamService::Service::GetNextCameraImage(::grpc::ServerContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ImageStreamService::Service::GetPredictImageByTimestamp(::grpc::ServerContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ImageStreamService::Service::GetMultiPredictPerspectives(::grpc::ServerContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace image_stream


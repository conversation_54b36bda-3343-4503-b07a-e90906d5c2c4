# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import camera_pb2 as frontend_dot_proto_dot_camera__pb2
from generated.frontend.proto import crosshair_pb2 as frontend_dot_proto_dot_crosshair__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class CrosshairServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StartAutoCalibrateCrosshair = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateCrosshair',
                request_serializer=frontend_dot_proto_dot_camera__pb2.CameraRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.StartAutoCalibrateAllCrosshairs = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateAllCrosshairs',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.StopAutoCalibrate = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/StopAutoCalibrate',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextCrosshairState = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/GetNextCrosshairState',
                request_serializer=frontend_dot_proto_dot_crosshair__pb2.CrosshairPositionRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_crosshair__pb2.CrosshairPositionState.FromString,
                )
        self.SetCrosshairPosition = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/SetCrosshairPosition',
                request_serializer=frontend_dot_proto_dot_crosshair__pb2.SetCrosshairPositionRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.MoveScanner = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/MoveScanner',
                request_serializer=frontend_dot_proto_dot_crosshair__pb2.MoveScannerRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextAutoCrossHairCalState = channel.unary_unary(
                '/carbon.frontend.crosshair.CrosshairService/GetNextAutoCrossHairCalState',
                request_serializer=frontend_dot_proto_dot_crosshair__pb2.AutoCrossHairCalStateRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_crosshair__pb2.AutoCrossHairCalStateResponse.FromString,
                )


class CrosshairServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def StartAutoCalibrateCrosshair(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartAutoCalibrateAllCrosshairs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StopAutoCalibrate(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextCrosshairState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetCrosshairPosition(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MoveScanner(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextAutoCrossHairCalState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CrosshairServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StartAutoCalibrateCrosshair': grpc.unary_unary_rpc_method_handler(
                    servicer.StartAutoCalibrateCrosshair,
                    request_deserializer=frontend_dot_proto_dot_camera__pb2.CameraRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartAutoCalibrateAllCrosshairs': grpc.unary_unary_rpc_method_handler(
                    servicer.StartAutoCalibrateAllCrosshairs,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'StopAutoCalibrate': grpc.unary_unary_rpc_method_handler(
                    servicer.StopAutoCalibrate,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextCrosshairState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextCrosshairState,
                    request_deserializer=frontend_dot_proto_dot_crosshair__pb2.CrosshairPositionRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_crosshair__pb2.CrosshairPositionState.SerializeToString,
            ),
            'SetCrosshairPosition': grpc.unary_unary_rpc_method_handler(
                    servicer.SetCrosshairPosition,
                    request_deserializer=frontend_dot_proto_dot_crosshair__pb2.SetCrosshairPositionRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'MoveScanner': grpc.unary_unary_rpc_method_handler(
                    servicer.MoveScanner,
                    request_deserializer=frontend_dot_proto_dot_crosshair__pb2.MoveScannerRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextAutoCrossHairCalState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAutoCrossHairCalState,
                    request_deserializer=frontend_dot_proto_dot_crosshair__pb2.AutoCrossHairCalStateRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_crosshair__pb2.AutoCrossHairCalStateResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.crosshair.CrosshairService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CrosshairService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def StartAutoCalibrateCrosshair(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateCrosshair',
            frontend_dot_proto_dot_camera__pb2.CameraRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartAutoCalibrateAllCrosshairs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateAllCrosshairs',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StopAutoCalibrate(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.crosshair.CrosshairService/StopAutoCalibrate',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextCrosshairState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.crosshair.CrosshairService/GetNextCrosshairState',
            frontend_dot_proto_dot_crosshair__pb2.CrosshairPositionRequest.SerializeToString,
            frontend_dot_proto_dot_crosshair__pb2.CrosshairPositionState.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetCrosshairPosition(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.crosshair.CrosshairService/SetCrosshairPosition',
            frontend_dot_proto_dot_crosshair__pb2.SetCrosshairPositionRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def MoveScanner(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.crosshair.CrosshairService/MoveScanner',
            frontend_dot_proto_dot_crosshair__pb2.MoveScannerRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextAutoCrossHairCalState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.crosshair.CrosshairService/GetNextAutoCrossHairCalState',
            frontend_dot_proto_dot_crosshair__pb2.AutoCrossHairCalStateRequest.SerializeToString,
            frontend_dot_proto_dot_crosshair__pb2.AutoCrossHairCalStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

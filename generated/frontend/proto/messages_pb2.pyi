"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class Message(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...
    message: typing___Text = ...
    from_support: builtin___bool = ...
    read: builtin___bool = ...
    author_user_id: typing___Text = ...
    author_robot_id: builtin___int = ...
    recipient_user_id: typing___Text = ...
    recipient_customer_id: builtin___int = ...
    recipient_robot_id: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        message : typing___Optional[typing___Text] = None,
        from_support : typing___Optional[builtin___bool] = None,
        read : typing___Optional[builtin___bool] = None,
        author_user_id : typing___Optional[typing___Text] = None,
        author_robot_id : typing___Optional[builtin___int] = None,
        recipient_user_id : typing___Optional[typing___Text] = None,
        recipient_customer_id : typing___Optional[builtin___int] = None,
        recipient_robot_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"author_robot_id",b"author_robot_id",u"author_user_id",b"author_user_id",u"from_support",b"from_support",u"id",b"id",u"message",b"message",u"read",b"read",u"recipient_customer_id",b"recipient_customer_id",u"recipient_robot_id",b"recipient_robot_id",u"recipient_user_id",b"recipient_user_id",u"ts",b"ts"]) -> None: ...
type___Message = Message

class MessageRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    message: typing___Text = ...

    def __init__(self,
        *,
        message : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"message",b"message"]) -> None: ...
type___MessageRequest = MessageRequest

class MessagesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___MessagesRequest = MessagesRequest

class MessagesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def messages(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Message]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        messages : typing___Optional[typing___Iterable[type___Message]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"messages",b"messages",u"ts",b"ts"]) -> None: ...
type___MessagesResponse = MessagesResponse

class ReadRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: builtin___int = ...

    def __init__(self,
        *,
        id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___ReadRequest = ReadRequest

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/camera.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcamera_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcamera_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fcamera_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fcamera_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[6]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcamera_2eproto;
namespace carbon {
namespace frontend {
namespace camera {
class Camera;
struct CameraDefaultTypeInternal;
extern CameraDefaultTypeInternal _Camera_default_instance_;
class CameraList;
struct CameraListDefaultTypeInternal;
extern CameraListDefaultTypeInternal _CameraList_default_instance_;
class CameraListRequest;
struct CameraListRequestDefaultTypeInternal;
extern CameraListRequestDefaultTypeInternal _CameraListRequest_default_instance_;
class CameraRequest;
struct CameraRequestDefaultTypeInternal;
extern CameraRequestDefaultTypeInternal _CameraRequest_default_instance_;
class NextCameraListRequest;
struct NextCameraListRequestDefaultTypeInternal;
extern NextCameraListRequestDefaultTypeInternal _NextCameraListRequest_default_instance_;
class RTCInfo;
struct RTCInfoDefaultTypeInternal;
extern RTCInfoDefaultTypeInternal _RTCInfo_default_instance_;
}  // namespace camera
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::camera::Camera* Arena::CreateMaybeMessage<::carbon::frontend::camera::Camera>(Arena*);
template<> ::carbon::frontend::camera::CameraList* Arena::CreateMaybeMessage<::carbon::frontend::camera::CameraList>(Arena*);
template<> ::carbon::frontend::camera::CameraListRequest* Arena::CreateMaybeMessage<::carbon::frontend::camera::CameraListRequest>(Arena*);
template<> ::carbon::frontend::camera::CameraRequest* Arena::CreateMaybeMessage<::carbon::frontend::camera::CameraRequest>(Arena*);
template<> ::carbon::frontend::camera::NextCameraListRequest* Arena::CreateMaybeMessage<::carbon::frontend::camera::NextCameraListRequest>(Arena*);
template<> ::carbon::frontend::camera::RTCInfo* Arena::CreateMaybeMessage<::carbon::frontend::camera::RTCInfo>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace camera {

enum CameraType : int {
  ANY = 0,
  PREDICT = 1,
  TARGET = 2,
  KILLCAM = 3,
  CameraType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  CameraType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool CameraType_IsValid(int value);
constexpr CameraType CameraType_MIN = ANY;
constexpr CameraType CameraType_MAX = KILLCAM;
constexpr int CameraType_ARRAYSIZE = CameraType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* CameraType_descriptor();
template<typename T>
inline const std::string& CameraType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, CameraType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function CameraType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    CameraType_descriptor(), enum_t_value);
}
inline bool CameraType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, CameraType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<CameraType>(
    CameraType_descriptor(), name, value);
}
// ===================================================================

class CameraRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.camera.CameraRequest) */ {
 public:
  inline CameraRequest() : CameraRequest(nullptr) {}
  ~CameraRequest() override;
  explicit constexpr CameraRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CameraRequest(const CameraRequest& from);
  CameraRequest(CameraRequest&& from) noexcept
    : CameraRequest() {
    *this = ::std::move(from);
  }

  inline CameraRequest& operator=(const CameraRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CameraRequest& operator=(CameraRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CameraRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CameraRequest* internal_default_instance() {
    return reinterpret_cast<const CameraRequest*>(
               &_CameraRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CameraRequest& a, CameraRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CameraRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CameraRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CameraRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CameraRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CameraRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CameraRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CameraRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.camera.CameraRequest";
  }
  protected:
  explicit CameraRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 1,
  };
  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.camera.CameraRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcamera_2eproto;
};
// -------------------------------------------------------------------

class Camera final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.camera.Camera) */ {
 public:
  inline Camera() : Camera(nullptr) {}
  ~Camera() override;
  explicit constexpr Camera(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Camera(const Camera& from);
  Camera(Camera&& from) noexcept
    : Camera() {
    *this = ::std::move(from);
  }

  inline Camera& operator=(const Camera& from) {
    CopyFrom(from);
    return *this;
  }
  inline Camera& operator=(Camera&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Camera& default_instance() {
    return *internal_default_instance();
  }
  static inline const Camera* internal_default_instance() {
    return reinterpret_cast<const Camera*>(
               &_Camera_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(Camera& a, Camera& b) {
    a.Swap(&b);
  }
  inline void Swap(Camera* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Camera* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Camera* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Camera>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Camera& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Camera& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Camera* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.camera.Camera";
  }
  protected:
  explicit Camera(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCameraIdFieldNumber = 2,
    kStreamHostFieldNumber = 5,
    kRtcInfoFieldNumber = 11,
    kRowNumberFieldNumber = 1,
    kTypeFieldNumber = 3,
    kStreamPortFieldNumber = 6,
    kWidthFieldNumber = 7,
    kAutoFocusableFieldNumber = 4,
    kTransposeFieldNumber = 9,
    kConnectedFieldNumber = 10,
    kHeightFieldNumber = 8,
  };
  // string camera_id = 2;
  void clear_camera_id();
  const std::string& camera_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_camera_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_camera_id();
  PROTOBUF_NODISCARD std::string* release_camera_id();
  void set_allocated_camera_id(std::string* camera_id);
  private:
  const std::string& _internal_camera_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_camera_id(const std::string& value);
  std::string* _internal_mutable_camera_id();
  public:

  // string stream_host = 5;
  void clear_stream_host();
  const std::string& stream_host() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_stream_host(ArgT0&& arg0, ArgT... args);
  std::string* mutable_stream_host();
  PROTOBUF_NODISCARD std::string* release_stream_host();
  void set_allocated_stream_host(std::string* stream_host);
  private:
  const std::string& _internal_stream_host() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_stream_host(const std::string& value);
  std::string* _internal_mutable_stream_host();
  public:

  // .carbon.frontend.camera.RTCInfo rtc_info = 11;
  bool has_rtc_info() const;
  private:
  bool _internal_has_rtc_info() const;
  public:
  void clear_rtc_info();
  const ::carbon::frontend::camera::RTCInfo& rtc_info() const;
  PROTOBUF_NODISCARD ::carbon::frontend::camera::RTCInfo* release_rtc_info();
  ::carbon::frontend::camera::RTCInfo* mutable_rtc_info();
  void set_allocated_rtc_info(::carbon::frontend::camera::RTCInfo* rtc_info);
  private:
  const ::carbon::frontend::camera::RTCInfo& _internal_rtc_info() const;
  ::carbon::frontend::camera::RTCInfo* _internal_mutable_rtc_info();
  public:
  void unsafe_arena_set_allocated_rtc_info(
      ::carbon::frontend::camera::RTCInfo* rtc_info);
  ::carbon::frontend::camera::RTCInfo* unsafe_arena_release_rtc_info();

  // uint32 row_number = 1;
  void clear_row_number();
  uint32_t row_number() const;
  void set_row_number(uint32_t value);
  private:
  uint32_t _internal_row_number() const;
  void _internal_set_row_number(uint32_t value);
  public:

  // .carbon.frontend.camera.CameraType type = 3;
  void clear_type();
  ::carbon::frontend::camera::CameraType type() const;
  void set_type(::carbon::frontend::camera::CameraType value);
  private:
  ::carbon::frontend::camera::CameraType _internal_type() const;
  void _internal_set_type(::carbon::frontend::camera::CameraType value);
  public:

  // uint32 stream_port = 6;
  void clear_stream_port();
  uint32_t stream_port() const;
  void set_stream_port(uint32_t value);
  private:
  uint32_t _internal_stream_port() const;
  void _internal_set_stream_port(uint32_t value);
  public:

  // uint32 width = 7;
  void clear_width();
  uint32_t width() const;
  void set_width(uint32_t value);
  private:
  uint32_t _internal_width() const;
  void _internal_set_width(uint32_t value);
  public:

  // bool auto_focusable = 4;
  void clear_auto_focusable();
  bool auto_focusable() const;
  void set_auto_focusable(bool value);
  private:
  bool _internal_auto_focusable() const;
  void _internal_set_auto_focusable(bool value);
  public:

  // bool transpose = 9;
  void clear_transpose();
  bool transpose() const;
  void set_transpose(bool value);
  private:
  bool _internal_transpose() const;
  void _internal_set_transpose(bool value);
  public:

  // bool connected = 10;
  void clear_connected();
  bool connected() const;
  void set_connected(bool value);
  private:
  bool _internal_connected() const;
  void _internal_set_connected(bool value);
  public:

  // uint32 height = 8;
  void clear_height();
  uint32_t height() const;
  void set_height(uint32_t value);
  private:
  uint32_t _internal_height() const;
  void _internal_set_height(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.camera.Camera)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr camera_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr stream_host_;
  ::carbon::frontend::camera::RTCInfo* rtc_info_;
  uint32_t row_number_;
  int type_;
  uint32_t stream_port_;
  uint32_t width_;
  bool auto_focusable_;
  bool transpose_;
  bool connected_;
  uint32_t height_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcamera_2eproto;
};
// -------------------------------------------------------------------

class RTCInfo final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.camera.RTCInfo) */ {
 public:
  inline RTCInfo() : RTCInfo(nullptr) {}
  ~RTCInfo() override;
  explicit constexpr RTCInfo(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RTCInfo(const RTCInfo& from);
  RTCInfo(RTCInfo&& from) noexcept
    : RTCInfo() {
    *this = ::std::move(from);
  }

  inline RTCInfo& operator=(const RTCInfo& from) {
    CopyFrom(from);
    return *this;
  }
  inline RTCInfo& operator=(RTCInfo&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RTCInfo& default_instance() {
    return *internal_default_instance();
  }
  static inline const RTCInfo* internal_default_instance() {
    return reinterpret_cast<const RTCInfo*>(
               &_RTCInfo_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(RTCInfo& a, RTCInfo& b) {
    a.Swap(&b);
  }
  inline void Swap(RTCInfo* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RTCInfo* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RTCInfo* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RTCInfo>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RTCInfo& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RTCInfo& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RTCInfo* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.camera.RTCInfo";
  }
  protected:
  explicit RTCInfo(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHostIdFieldNumber = 1,
    kStreamIdFieldNumber = 2,
  };
  // string host_id = 1;
  void clear_host_id();
  const std::string& host_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_host_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_host_id();
  PROTOBUF_NODISCARD std::string* release_host_id();
  void set_allocated_host_id(std::string* host_id);
  private:
  const std::string& _internal_host_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_host_id(const std::string& value);
  std::string* _internal_mutable_host_id();
  public:

  // string stream_id = 2;
  void clear_stream_id();
  const std::string& stream_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_stream_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_stream_id();
  PROTOBUF_NODISCARD std::string* release_stream_id();
  void set_allocated_stream_id(std::string* stream_id);
  private:
  const std::string& _internal_stream_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_stream_id(const std::string& value);
  std::string* _internal_mutable_stream_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.camera.RTCInfo)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr stream_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcamera_2eproto;
};
// -------------------------------------------------------------------

class CameraList final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.camera.CameraList) */ {
 public:
  inline CameraList() : CameraList(nullptr) {}
  ~CameraList() override;
  explicit constexpr CameraList(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CameraList(const CameraList& from);
  CameraList(CameraList&& from) noexcept
    : CameraList() {
    *this = ::std::move(from);
  }

  inline CameraList& operator=(const CameraList& from) {
    CopyFrom(from);
    return *this;
  }
  inline CameraList& operator=(CameraList&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CameraList& default_instance() {
    return *internal_default_instance();
  }
  static inline const CameraList* internal_default_instance() {
    return reinterpret_cast<const CameraList*>(
               &_CameraList_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(CameraList& a, CameraList& b) {
    a.Swap(&b);
  }
  inline void Swap(CameraList* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CameraList* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CameraList* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CameraList>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CameraList& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CameraList& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CameraList* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.camera.CameraList";
  }
  protected:
  explicit CameraList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamerasFieldNumber = 1,
    kTsFieldNumber = 2,
  };
  // repeated .carbon.frontend.camera.Camera cameras = 1;
  int cameras_size() const;
  private:
  int _internal_cameras_size() const;
  public:
  void clear_cameras();
  ::carbon::frontend::camera::Camera* mutable_cameras(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::camera::Camera >*
      mutable_cameras();
  private:
  const ::carbon::frontend::camera::Camera& _internal_cameras(int index) const;
  ::carbon::frontend::camera::Camera* _internal_add_cameras();
  public:
  const ::carbon::frontend::camera::Camera& cameras(int index) const;
  ::carbon::frontend::camera::Camera* add_cameras();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::camera::Camera >&
      cameras() const;

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.camera.CameraList)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::camera::Camera > cameras_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcamera_2eproto;
};
// -------------------------------------------------------------------

class CameraListRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.camera.CameraListRequest) */ {
 public:
  inline CameraListRequest() : CameraListRequest(nullptr) {}
  ~CameraListRequest() override;
  explicit constexpr CameraListRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CameraListRequest(const CameraListRequest& from);
  CameraListRequest(CameraListRequest&& from) noexcept
    : CameraListRequest() {
    *this = ::std::move(from);
  }

  inline CameraListRequest& operator=(const CameraListRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CameraListRequest& operator=(CameraListRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CameraListRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CameraListRequest* internal_default_instance() {
    return reinterpret_cast<const CameraListRequest*>(
               &_CameraListRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(CameraListRequest& a, CameraListRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CameraListRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CameraListRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CameraListRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CameraListRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CameraListRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CameraListRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CameraListRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.camera.CameraListRequest";
  }
  protected:
  explicit CameraListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTypeFieldNumber = 1,
    kIncludeDisconnectedFieldNumber = 2,
  };
  // .carbon.frontend.camera.CameraType type = 1;
  void clear_type();
  ::carbon::frontend::camera::CameraType type() const;
  void set_type(::carbon::frontend::camera::CameraType value);
  private:
  ::carbon::frontend::camera::CameraType _internal_type() const;
  void _internal_set_type(::carbon::frontend::camera::CameraType value);
  public:

  // bool include_disconnected = 2;
  void clear_include_disconnected();
  bool include_disconnected() const;
  void set_include_disconnected(bool value);
  private:
  bool _internal_include_disconnected() const;
  void _internal_set_include_disconnected(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.camera.CameraListRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int type_;
  bool include_disconnected_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcamera_2eproto;
};
// -------------------------------------------------------------------

class NextCameraListRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.camera.NextCameraListRequest) */ {
 public:
  inline NextCameraListRequest() : NextCameraListRequest(nullptr) {}
  ~NextCameraListRequest() override;
  explicit constexpr NextCameraListRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  NextCameraListRequest(const NextCameraListRequest& from);
  NextCameraListRequest(NextCameraListRequest&& from) noexcept
    : NextCameraListRequest() {
    *this = ::std::move(from);
  }

  inline NextCameraListRequest& operator=(const NextCameraListRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline NextCameraListRequest& operator=(NextCameraListRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const NextCameraListRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const NextCameraListRequest* internal_default_instance() {
    return reinterpret_cast<const NextCameraListRequest*>(
               &_NextCameraListRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(NextCameraListRequest& a, NextCameraListRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(NextCameraListRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(NextCameraListRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  NextCameraListRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<NextCameraListRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const NextCameraListRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const NextCameraListRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(NextCameraListRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.camera.NextCameraListRequest";
  }
  protected:
  explicit NextCameraListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 2,
    kTypeFieldNumber = 1,
    kIncludeDisconnectedFieldNumber = 3,
  };
  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.camera.CameraType type = 1;
  void clear_type();
  ::carbon::frontend::camera::CameraType type() const;
  void set_type(::carbon::frontend::camera::CameraType value);
  private:
  ::carbon::frontend::camera::CameraType _internal_type() const;
  void _internal_set_type(::carbon::frontend::camera::CameraType value);
  public:

  // bool include_disconnected = 3;
  void clear_include_disconnected();
  bool include_disconnected() const;
  void set_include_disconnected(bool value);
  private:
  bool _internal_include_disconnected() const;
  void _internal_set_include_disconnected(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.camera.NextCameraListRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  int type_;
  bool include_disconnected_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcamera_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CameraRequest

// string cam_id = 1;
inline void CameraRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& CameraRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.CameraRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CameraRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.CameraRequest.cam_id)
}
inline std::string* CameraRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.CameraRequest.cam_id)
  return _s;
}
inline const std::string& CameraRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void CameraRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CameraRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CameraRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.camera.CameraRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CameraRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.camera.CameraRequest.cam_id)
}

// -------------------------------------------------------------------

// Camera

// uint32 row_number = 1;
inline void Camera::clear_row_number() {
  row_number_ = 0u;
}
inline uint32_t Camera::_internal_row_number() const {
  return row_number_;
}
inline uint32_t Camera::row_number() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.row_number)
  return _internal_row_number();
}
inline void Camera::_internal_set_row_number(uint32_t value) {
  
  row_number_ = value;
}
inline void Camera::set_row_number(uint32_t value) {
  _internal_set_row_number(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.row_number)
}

// string camera_id = 2;
inline void Camera::clear_camera_id() {
  camera_id_.ClearToEmpty();
}
inline const std::string& Camera::camera_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.camera_id)
  return _internal_camera_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Camera::set_camera_id(ArgT0&& arg0, ArgT... args) {
 
 camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.camera_id)
}
inline std::string* Camera::mutable_camera_id() {
  std::string* _s = _internal_mutable_camera_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.Camera.camera_id)
  return _s;
}
inline const std::string& Camera::_internal_camera_id() const {
  return camera_id_.Get();
}
inline void Camera::_internal_set_camera_id(const std::string& value) {
  
  camera_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Camera::_internal_mutable_camera_id() {
  
  return camera_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Camera::release_camera_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.camera.Camera.camera_id)
  return camera_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Camera::set_allocated_camera_id(std::string* camera_id) {
  if (camera_id != nullptr) {
    
  } else {
    
  }
  camera_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), camera_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (camera_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    camera_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.camera.Camera.camera_id)
}

// .carbon.frontend.camera.CameraType type = 3;
inline void Camera::clear_type() {
  type_ = 0;
}
inline ::carbon::frontend::camera::CameraType Camera::_internal_type() const {
  return static_cast< ::carbon::frontend::camera::CameraType >(type_);
}
inline ::carbon::frontend::camera::CameraType Camera::type() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.type)
  return _internal_type();
}
inline void Camera::_internal_set_type(::carbon::frontend::camera::CameraType value) {
  
  type_ = value;
}
inline void Camera::set_type(::carbon::frontend::camera::CameraType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.type)
}

// bool auto_focusable = 4;
inline void Camera::clear_auto_focusable() {
  auto_focusable_ = false;
}
inline bool Camera::_internal_auto_focusable() const {
  return auto_focusable_;
}
inline bool Camera::auto_focusable() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.auto_focusable)
  return _internal_auto_focusable();
}
inline void Camera::_internal_set_auto_focusable(bool value) {
  
  auto_focusable_ = value;
}
inline void Camera::set_auto_focusable(bool value) {
  _internal_set_auto_focusable(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.auto_focusable)
}

// string stream_host = 5;
inline void Camera::clear_stream_host() {
  stream_host_.ClearToEmpty();
}
inline const std::string& Camera::stream_host() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.stream_host)
  return _internal_stream_host();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Camera::set_stream_host(ArgT0&& arg0, ArgT... args) {
 
 stream_host_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.stream_host)
}
inline std::string* Camera::mutable_stream_host() {
  std::string* _s = _internal_mutable_stream_host();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.Camera.stream_host)
  return _s;
}
inline const std::string& Camera::_internal_stream_host() const {
  return stream_host_.Get();
}
inline void Camera::_internal_set_stream_host(const std::string& value) {
  
  stream_host_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Camera::_internal_mutable_stream_host() {
  
  return stream_host_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Camera::release_stream_host() {
  // @@protoc_insertion_point(field_release:carbon.frontend.camera.Camera.stream_host)
  return stream_host_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Camera::set_allocated_stream_host(std::string* stream_host) {
  if (stream_host != nullptr) {
    
  } else {
    
  }
  stream_host_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), stream_host,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (stream_host_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    stream_host_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.camera.Camera.stream_host)
}

// uint32 stream_port = 6;
inline void Camera::clear_stream_port() {
  stream_port_ = 0u;
}
inline uint32_t Camera::_internal_stream_port() const {
  return stream_port_;
}
inline uint32_t Camera::stream_port() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.stream_port)
  return _internal_stream_port();
}
inline void Camera::_internal_set_stream_port(uint32_t value) {
  
  stream_port_ = value;
}
inline void Camera::set_stream_port(uint32_t value) {
  _internal_set_stream_port(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.stream_port)
}

// uint32 width = 7;
inline void Camera::clear_width() {
  width_ = 0u;
}
inline uint32_t Camera::_internal_width() const {
  return width_;
}
inline uint32_t Camera::width() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.width)
  return _internal_width();
}
inline void Camera::_internal_set_width(uint32_t value) {
  
  width_ = value;
}
inline void Camera::set_width(uint32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.width)
}

// uint32 height = 8;
inline void Camera::clear_height() {
  height_ = 0u;
}
inline uint32_t Camera::_internal_height() const {
  return height_;
}
inline uint32_t Camera::height() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.height)
  return _internal_height();
}
inline void Camera::_internal_set_height(uint32_t value) {
  
  height_ = value;
}
inline void Camera::set_height(uint32_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.height)
}

// bool transpose = 9;
inline void Camera::clear_transpose() {
  transpose_ = false;
}
inline bool Camera::_internal_transpose() const {
  return transpose_;
}
inline bool Camera::transpose() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.transpose)
  return _internal_transpose();
}
inline void Camera::_internal_set_transpose(bool value) {
  
  transpose_ = value;
}
inline void Camera::set_transpose(bool value) {
  _internal_set_transpose(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.transpose)
}

// bool connected = 10;
inline void Camera::clear_connected() {
  connected_ = false;
}
inline bool Camera::_internal_connected() const {
  return connected_;
}
inline bool Camera::connected() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.connected)
  return _internal_connected();
}
inline void Camera::_internal_set_connected(bool value) {
  
  connected_ = value;
}
inline void Camera::set_connected(bool value) {
  _internal_set_connected(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.Camera.connected)
}

// .carbon.frontend.camera.RTCInfo rtc_info = 11;
inline bool Camera::_internal_has_rtc_info() const {
  return this != internal_default_instance() && rtc_info_ != nullptr;
}
inline bool Camera::has_rtc_info() const {
  return _internal_has_rtc_info();
}
inline void Camera::clear_rtc_info() {
  if (GetArenaForAllocation() == nullptr && rtc_info_ != nullptr) {
    delete rtc_info_;
  }
  rtc_info_ = nullptr;
}
inline const ::carbon::frontend::camera::RTCInfo& Camera::_internal_rtc_info() const {
  const ::carbon::frontend::camera::RTCInfo* p = rtc_info_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::camera::RTCInfo&>(
      ::carbon::frontend::camera::_RTCInfo_default_instance_);
}
inline const ::carbon::frontend::camera::RTCInfo& Camera::rtc_info() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.Camera.rtc_info)
  return _internal_rtc_info();
}
inline void Camera::unsafe_arena_set_allocated_rtc_info(
    ::carbon::frontend::camera::RTCInfo* rtc_info) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rtc_info_);
  }
  rtc_info_ = rtc_info;
  if (rtc_info) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.camera.Camera.rtc_info)
}
inline ::carbon::frontend::camera::RTCInfo* Camera::release_rtc_info() {
  
  ::carbon::frontend::camera::RTCInfo* temp = rtc_info_;
  rtc_info_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::camera::RTCInfo* Camera::unsafe_arena_release_rtc_info() {
  // @@protoc_insertion_point(field_release:carbon.frontend.camera.Camera.rtc_info)
  
  ::carbon::frontend::camera::RTCInfo* temp = rtc_info_;
  rtc_info_ = nullptr;
  return temp;
}
inline ::carbon::frontend::camera::RTCInfo* Camera::_internal_mutable_rtc_info() {
  
  if (rtc_info_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::camera::RTCInfo>(GetArenaForAllocation());
    rtc_info_ = p;
  }
  return rtc_info_;
}
inline ::carbon::frontend::camera::RTCInfo* Camera::mutable_rtc_info() {
  ::carbon::frontend::camera::RTCInfo* _msg = _internal_mutable_rtc_info();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.Camera.rtc_info)
  return _msg;
}
inline void Camera::set_allocated_rtc_info(::carbon::frontend::camera::RTCInfo* rtc_info) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete rtc_info_;
  }
  if (rtc_info) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::camera::RTCInfo>::GetOwningArena(rtc_info);
    if (message_arena != submessage_arena) {
      rtc_info = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rtc_info, submessage_arena);
    }
    
  } else {
    
  }
  rtc_info_ = rtc_info;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.camera.Camera.rtc_info)
}

// -------------------------------------------------------------------

// RTCInfo

// string host_id = 1;
inline void RTCInfo::clear_host_id() {
  host_id_.ClearToEmpty();
}
inline const std::string& RTCInfo::host_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.RTCInfo.host_id)
  return _internal_host_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RTCInfo::set_host_id(ArgT0&& arg0, ArgT... args) {
 
 host_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.RTCInfo.host_id)
}
inline std::string* RTCInfo::mutable_host_id() {
  std::string* _s = _internal_mutable_host_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.RTCInfo.host_id)
  return _s;
}
inline const std::string& RTCInfo::_internal_host_id() const {
  return host_id_.Get();
}
inline void RTCInfo::_internal_set_host_id(const std::string& value) {
  
  host_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RTCInfo::_internal_mutable_host_id() {
  
  return host_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RTCInfo::release_host_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.camera.RTCInfo.host_id)
  return host_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RTCInfo::set_allocated_host_id(std::string* host_id) {
  if (host_id != nullptr) {
    
  } else {
    
  }
  host_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), host_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (host_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    host_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.camera.RTCInfo.host_id)
}

// string stream_id = 2;
inline void RTCInfo::clear_stream_id() {
  stream_id_.ClearToEmpty();
}
inline const std::string& RTCInfo::stream_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.RTCInfo.stream_id)
  return _internal_stream_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RTCInfo::set_stream_id(ArgT0&& arg0, ArgT... args) {
 
 stream_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.RTCInfo.stream_id)
}
inline std::string* RTCInfo::mutable_stream_id() {
  std::string* _s = _internal_mutable_stream_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.RTCInfo.stream_id)
  return _s;
}
inline const std::string& RTCInfo::_internal_stream_id() const {
  return stream_id_.Get();
}
inline void RTCInfo::_internal_set_stream_id(const std::string& value) {
  
  stream_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RTCInfo::_internal_mutable_stream_id() {
  
  return stream_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RTCInfo::release_stream_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.camera.RTCInfo.stream_id)
  return stream_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RTCInfo::set_allocated_stream_id(std::string* stream_id) {
  if (stream_id != nullptr) {
    
  } else {
    
  }
  stream_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), stream_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (stream_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    stream_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.camera.RTCInfo.stream_id)
}

// -------------------------------------------------------------------

// CameraList

// repeated .carbon.frontend.camera.Camera cameras = 1;
inline int CameraList::_internal_cameras_size() const {
  return cameras_.size();
}
inline int CameraList::cameras_size() const {
  return _internal_cameras_size();
}
inline void CameraList::clear_cameras() {
  cameras_.Clear();
}
inline ::carbon::frontend::camera::Camera* CameraList::mutable_cameras(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.CameraList.cameras)
  return cameras_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::camera::Camera >*
CameraList::mutable_cameras() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.camera.CameraList.cameras)
  return &cameras_;
}
inline const ::carbon::frontend::camera::Camera& CameraList::_internal_cameras(int index) const {
  return cameras_.Get(index);
}
inline const ::carbon::frontend::camera::Camera& CameraList::cameras(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.CameraList.cameras)
  return _internal_cameras(index);
}
inline ::carbon::frontend::camera::Camera* CameraList::_internal_add_cameras() {
  return cameras_.Add();
}
inline ::carbon::frontend::camera::Camera* CameraList::add_cameras() {
  ::carbon::frontend::camera::Camera* _add = _internal_add_cameras();
  // @@protoc_insertion_point(field_add:carbon.frontend.camera.CameraList.cameras)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::camera::Camera >&
CameraList::cameras() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.camera.CameraList.cameras)
  return cameras_;
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool CameraList::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool CameraList::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& CameraList::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& CameraList::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.CameraList.ts)
  return _internal_ts();
}
inline void CameraList::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.camera.CameraList.ts)
}
inline ::carbon::frontend::util::Timestamp* CameraList::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CameraList::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.camera.CameraList.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CameraList::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* CameraList::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.CameraList.ts)
  return _msg;
}
inline void CameraList::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.camera.CameraList.ts)
}

// -------------------------------------------------------------------

// CameraListRequest

// .carbon.frontend.camera.CameraType type = 1;
inline void CameraListRequest::clear_type() {
  type_ = 0;
}
inline ::carbon::frontend::camera::CameraType CameraListRequest::_internal_type() const {
  return static_cast< ::carbon::frontend::camera::CameraType >(type_);
}
inline ::carbon::frontend::camera::CameraType CameraListRequest::type() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.CameraListRequest.type)
  return _internal_type();
}
inline void CameraListRequest::_internal_set_type(::carbon::frontend::camera::CameraType value) {
  
  type_ = value;
}
inline void CameraListRequest::set_type(::carbon::frontend::camera::CameraType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.CameraListRequest.type)
}

// bool include_disconnected = 2;
inline void CameraListRequest::clear_include_disconnected() {
  include_disconnected_ = false;
}
inline bool CameraListRequest::_internal_include_disconnected() const {
  return include_disconnected_;
}
inline bool CameraListRequest::include_disconnected() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.CameraListRequest.include_disconnected)
  return _internal_include_disconnected();
}
inline void CameraListRequest::_internal_set_include_disconnected(bool value) {
  
  include_disconnected_ = value;
}
inline void CameraListRequest::set_include_disconnected(bool value) {
  _internal_set_include_disconnected(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.CameraListRequest.include_disconnected)
}

// -------------------------------------------------------------------

// NextCameraListRequest

// .carbon.frontend.camera.CameraType type = 1;
inline void NextCameraListRequest::clear_type() {
  type_ = 0;
}
inline ::carbon::frontend::camera::CameraType NextCameraListRequest::_internal_type() const {
  return static_cast< ::carbon::frontend::camera::CameraType >(type_);
}
inline ::carbon::frontend::camera::CameraType NextCameraListRequest::type() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.NextCameraListRequest.type)
  return _internal_type();
}
inline void NextCameraListRequest::_internal_set_type(::carbon::frontend::camera::CameraType value) {
  
  type_ = value;
}
inline void NextCameraListRequest::set_type(::carbon::frontend::camera::CameraType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.NextCameraListRequest.type)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool NextCameraListRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool NextCameraListRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& NextCameraListRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& NextCameraListRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.NextCameraListRequest.ts)
  return _internal_ts();
}
inline void NextCameraListRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.camera.NextCameraListRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* NextCameraListRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* NextCameraListRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.camera.NextCameraListRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* NextCameraListRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* NextCameraListRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.camera.NextCameraListRequest.ts)
  return _msg;
}
inline void NextCameraListRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.camera.NextCameraListRequest.ts)
}

// bool include_disconnected = 3;
inline void NextCameraListRequest::clear_include_disconnected() {
  include_disconnected_ = false;
}
inline bool NextCameraListRequest::_internal_include_disconnected() const {
  return include_disconnected_;
}
inline bool NextCameraListRequest::include_disconnected() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.camera.NextCameraListRequest.include_disconnected)
  return _internal_include_disconnected();
}
inline void NextCameraListRequest::_internal_set_include_disconnected(bool value) {
  
  include_disconnected_ = value;
}
inline void NextCameraListRequest::set_include_disconnected(bool value) {
  _internal_set_include_disconnected(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.camera.NextCameraListRequest.include_disconnected)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace camera
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::camera::CameraType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::camera::CameraType>() {
  return ::carbon::frontend::camera::CameraType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcamera_2eproto

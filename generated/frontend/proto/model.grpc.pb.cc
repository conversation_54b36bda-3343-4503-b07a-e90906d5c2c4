// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/model.proto

#include "frontend/proto/model.pb.h"
#include "frontend/proto/model.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace model {

static const char* ModelService_method_names[] = {
  "/carbon.frontend.model.ModelService/PinModel",
  "/carbon.frontend.model.ModelService/UnpinModel",
  "/carbon.frontend.model.ModelService/GetNextModelState",
  "/carbon.frontend.model.ModelService/GetNextAllModelState",
  "/carbon.frontend.model.ModelService/UpdateModel",
  "/carbon.frontend.model.ModelService/ListEnabledCrops",
  "/carbon.frontend.model.ModelService/GetNextEnabledCrops",
  "/carbon.frontend.model.ModelService/ListCaptureCrops",
  "/carbon.frontend.model.ModelService/GetNextCaptureCrops",
  "/carbon.frontend.model.ModelService/GetNextSelectedCropID",
  "/carbon.frontend.model.ModelService/SelectCrop",
  "/carbon.frontend.model.ModelService/DownloadModel",
  "/carbon.frontend.model.ModelService/GetNextModelHistory",
  "/carbon.frontend.model.ModelService/GetModelHistory",
  "/carbon.frontend.model.ModelService/GetModelNicknames",
  "/carbon.frontend.model.ModelService/GetNextModelNicknames",
  "/carbon.frontend.model.ModelService/SetModelNickname",
  "/carbon.frontend.model.ModelService/RefreshDefaultModelParameters",
  "/carbon.frontend.model.ModelService/SyncCropIDs",
  "/carbon.frontend.model.ModelService/TriggerDownload",
};

std::unique_ptr< ModelService::Stub> ModelService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ModelService::Stub> stub(new ModelService::Stub(channel, options));
  return stub;
}

ModelService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_PinModel_(ModelService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UnpinModel_(ModelService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextModelState_(ModelService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextAllModelState_(ModelService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpdateModel_(ModelService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ListEnabledCrops_(ModelService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextEnabledCrops_(ModelService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ListCaptureCrops_(ModelService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextCaptureCrops_(ModelService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextSelectedCropID_(ModelService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SelectCrop_(ModelService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DownloadModel_(ModelService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextModelHistory_(ModelService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetModelHistory_(ModelService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetModelNicknames_(ModelService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextModelNicknames_(ModelService_method_names[15], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetModelNickname_(ModelService_method_names[16], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_RefreshDefaultModelParameters_(ModelService_method_names[17], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SyncCropIDs_(ModelService_method_names[18], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_TriggerDownload_(ModelService_method_names[19], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ModelService::Stub::PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::PinModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_PinModel_, context, request, response);
}

void ModelService::Stub::async::PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::PinModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PinModel_, context, request, response, std::move(f));
}

void ModelService::Stub::async::PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PinModel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncPinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::model::PinModelRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_PinModel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncPinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPinModelRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::UnpinModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UnpinModel_, context, request, response);
}

void ModelService::Stub::async::UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::UnpinModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UnpinModel_, context, request, response, std::move(f));
}

void ModelService::Stub::async::UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UnpinModel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncUnpinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::model::UnpinModelRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UnpinModel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncUnpinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUnpinModelRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::carbon::frontend::model::GetNextModelStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextModelState_, context, request, response);
}

void ModelService::Stub::async::GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModelState_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModelState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>* ModelService::Stub::PrepareAsyncGetNextModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::GetNextModelStateResponse, ::carbon::frontend::model::GetNextModelStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextModelState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>* ModelService::Stub::AsyncGetNextModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextModelStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::carbon::frontend::model::GetNextModelStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAllModelState_, context, request, response);
}

void ModelService::Stub::async::GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAllModelState_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAllModelState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>* ModelService::Stub::PrepareAsyncGetNextAllModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::GetNextModelStateResponse, ::carbon::frontend::model::GetNextModelStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAllModelState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>* ModelService::Stub::AsyncGetNextAllModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAllModelStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UpdateModel_, context, request, response);
}

void ModelService::Stub::async::UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateModel_, context, request, response, std::move(f));
}

void ModelService::Stub::async::UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateModel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncUpdateModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UpdateModel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncUpdateModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpdateModelRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::carbon::frontend::model::EnabledCropList* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ListEnabledCrops_, context, request, response);
}

void ModelService::Stub::async::ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListEnabledCrops_, context, request, response, std::move(f));
}

void ModelService::Stub::async::ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListEnabledCrops_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>* ModelService::Stub::PrepareAsyncListEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::EnabledCropList, ::carbon::frontend::model::ListCropParameters, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ListEnabledCrops_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>* ModelService::Stub::AsyncListEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncListEnabledCropsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::GetNextEnabledCropsRequest, ::carbon::frontend::model::GetNextEnabledCropsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextEnabledCrops_, context, request, response);
}

void ModelService::Stub::async::GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::GetNextEnabledCropsRequest, ::carbon::frontend::model::GetNextEnabledCropsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextEnabledCrops_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextEnabledCrops_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextEnabledCropsResponse>* ModelService::Stub::PrepareAsyncGetNextEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::GetNextEnabledCropsResponse, ::carbon::frontend::model::GetNextEnabledCropsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextEnabledCrops_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextEnabledCropsResponse>* ModelService::Stub::AsyncGetNextEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextEnabledCropsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::carbon::frontend::model::EnabledCropList* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ListCaptureCrops_, context, request, response);
}

void ModelService::Stub::async::ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListCaptureCrops_, context, request, response, std::move(f));
}

void ModelService::Stub::async::ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ListCaptureCrops_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>* ModelService::Stub::PrepareAsyncListCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::EnabledCropList, ::carbon::frontend::model::ListCropParameters, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ListCaptureCrops_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>* ModelService::Stub::AsyncListCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncListCaptureCropsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::GetNextCaptureCropsRequest, ::carbon::frontend::model::GetNextCaptureCropsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextCaptureCrops_, context, request, response);
}

void ModelService::Stub::async::GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::GetNextCaptureCropsRequest, ::carbon::frontend::model::GetNextCaptureCropsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCaptureCrops_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCaptureCrops_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextCaptureCropsResponse>* ModelService::Stub::PrepareAsyncGetNextCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::GetNextCaptureCropsResponse, ::carbon::frontend::model::GetNextCaptureCropsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextCaptureCrops_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextCaptureCropsResponse>* ModelService::Stub::AsyncGetNextCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextCaptureCropsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::model::GetNextSelectedCropIDResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextSelectedCropID_, context, request, response);
}

void ModelService::Stub::async::GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::model::GetNextSelectedCropIDResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextSelectedCropID_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextSelectedCropID_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextSelectedCropIDResponse>* ModelService::Stub::PrepareAsyncGetNextSelectedCropIDRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::GetNextSelectedCropIDResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextSelectedCropID_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextSelectedCropIDResponse>* ModelService::Stub::AsyncGetNextSelectedCropIDRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextSelectedCropIDRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::SelectCropRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SelectCrop_, context, request, response);
}

void ModelService::Stub::async::SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::SelectCropRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SelectCrop_, context, request, response, std::move(f));
}

void ModelService::Stub::async::SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SelectCrop_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncSelectCropRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::model::SelectCropRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SelectCrop_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncSelectCropRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSelectCropRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::DownloadModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DownloadModel_, context, request, response);
}

void ModelService::Stub::async::DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::DownloadModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DownloadModel_, context, request, response, std::move(f));
}

void ModelService::Stub::async::DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DownloadModel_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncDownloadModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::model::DownloadModelRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DownloadModel_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncDownloadModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDownloadModelRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::carbon::frontend::model::ModelHistoryResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextModelHistory_, context, request, response);
}

void ModelService::Stub::async::GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModelHistory_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModelHistory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>* ModelService::Stub::PrepareAsyncGetNextModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::ModelHistoryResponse, ::carbon::frontend::model::ModelHistoryRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextModelHistory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>* ModelService::Stub::AsyncGetNextModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextModelHistoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::carbon::frontend::model::ModelHistoryResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetModelHistory_, context, request, response);
}

void ModelService::Stub::async::GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModelHistory_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModelHistory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>* ModelService::Stub::PrepareAsyncGetModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::ModelHistoryResponse, ::carbon::frontend::model::ModelHistoryRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetModelHistory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>* ModelService::Stub::AsyncGetModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetModelHistoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::carbon::frontend::model::GetModelNicknamesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetModelNicknames_, context, request, response);
}

void ModelService::Stub::async::GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModelNicknames_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetModelNicknames_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>* ModelService::Stub::PrepareAsyncGetModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::GetModelNicknamesResponse, ::carbon::frontend::model::GetModelNicknamesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetModelNicknames_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>* ModelService::Stub::AsyncGetModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetModelNicknamesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::carbon::frontend::model::GetModelNicknamesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextModelNicknames_, context, request, response);
}

void ModelService::Stub::async::GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModelNicknames_, context, request, response, std::move(f));
}

void ModelService::Stub::async::GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextModelNicknames_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>* ModelService::Stub::PrepareAsyncGetNextModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::model::GetModelNicknamesResponse, ::carbon::frontend::model::GetModelNicknamesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextModelNicknames_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>* ModelService::Stub::AsyncGetNextModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextModelNicknamesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::SetModelNicknameRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetModelNickname_, context, request, response);
}

void ModelService::Stub::async::SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::SetModelNicknameRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetModelNickname_, context, request, response, std::move(f));
}

void ModelService::Stub::async::SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetModelNickname_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncSetModelNicknameRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::model::SetModelNicknameRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetModelNickname_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncSetModelNicknameRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetModelNicknameRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_RefreshDefaultModelParameters_, context, request, response);
}

void ModelService::Stub::async::RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RefreshDefaultModelParameters_, context, request, response, std::move(f));
}

void ModelService::Stub::async::RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RefreshDefaultModelParameters_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncRefreshDefaultModelParametersRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_RefreshDefaultModelParameters_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncRefreshDefaultModelParametersRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncRefreshDefaultModelParametersRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::model::SyncCropIDsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SyncCropIDs_, context, request, response);
}

void ModelService::Stub::async::SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::model::SyncCropIDsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SyncCropIDs_, context, request, response, std::move(f));
}

void ModelService::Stub::async::SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SyncCropIDs_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncSyncCropIDsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::model::SyncCropIDsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SyncCropIDs_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncSyncCropIDsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSyncCropIDsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ModelService::Stub::TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_TriggerDownload_, context, request, response);
}

void ModelService::Stub::async::TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TriggerDownload_, context, request, response, std::move(f));
}

void ModelService::Stub::async::TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_TriggerDownload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::PrepareAsyncTriggerDownloadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_TriggerDownload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ModelService::Stub::AsyncTriggerDownloadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncTriggerDownloadRaw(context, request, cq);
  result->StartCall();
  return result;
}

ModelService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::PinModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::PinModelRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->PinModel(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::UnpinModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::UnpinModelRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->UnpinModel(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::GetNextModelStateRequest* req,
             ::carbon::frontend::model::GetNextModelStateResponse* resp) {
               return service->GetNextModelState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::GetNextModelStateRequest* req,
             ::carbon::frontend::model::GetNextModelStateResponse* resp) {
               return service->GetNextAllModelState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->UpdateModel(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::ListCropParameters* req,
             ::carbon::frontend::model::EnabledCropList* resp) {
               return service->ListEnabledCrops(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::GetNextEnabledCropsRequest, ::carbon::frontend::model::GetNextEnabledCropsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::GetNextEnabledCropsRequest* req,
             ::carbon::frontend::model::GetNextEnabledCropsResponse* resp) {
               return service->GetNextEnabledCrops(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::ListCropParameters* req,
             ::carbon::frontend::model::EnabledCropList* resp) {
               return service->ListCaptureCrops(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::GetNextCaptureCropsRequest, ::carbon::frontend::model::GetNextCaptureCropsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::GetNextCaptureCropsRequest* req,
             ::carbon::frontend::model::GetNextCaptureCropsResponse* resp) {
               return service->GetNextCaptureCrops(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::model::GetNextSelectedCropIDResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::model::GetNextSelectedCropIDResponse* resp) {
               return service->GetNextSelectedCropID(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::SelectCropRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::SelectCropRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SelectCrop(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::DownloadModelRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::DownloadModelRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DownloadModel(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::ModelHistoryRequest* req,
             ::carbon::frontend::model::ModelHistoryResponse* resp) {
               return service->GetNextModelHistory(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::ModelHistoryRequest* req,
             ::carbon::frontend::model::ModelHistoryResponse* resp) {
               return service->GetModelHistory(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::GetModelNicknamesRequest* req,
             ::carbon::frontend::model::GetModelNicknamesResponse* resp) {
               return service->GetModelNicknames(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::GetModelNicknamesRequest* req,
             ::carbon::frontend::model::GetModelNicknamesResponse* resp) {
               return service->GetNextModelNicknames(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[16],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::SetModelNicknameRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::SetModelNicknameRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetModelNickname(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[17],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->RefreshDefaultModelParameters(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[18],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::model::SyncCropIDsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::model::SyncCropIDsRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SyncCropIDs(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ModelService_method_names[19],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ModelService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ModelService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->TriggerDownload(ctx, req, resp);
             }, this)));
}

ModelService::Service::~Service() {
}

::grpc::Status ModelService::Service::PinModel(::grpc::ServerContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::UnpinModel(::grpc::ServerContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetNextModelState(::grpc::ServerContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetNextAllModelState(::grpc::ServerContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::UpdateModel(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::ListEnabledCrops(::grpc::ServerContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetNextEnabledCrops(::grpc::ServerContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::ListCaptureCrops(::grpc::ServerContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetNextCaptureCrops(::grpc::ServerContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetNextSelectedCropID(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::SelectCrop(::grpc::ServerContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::DownloadModel(::grpc::ServerContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetNextModelHistory(::grpc::ServerContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetModelHistory(::grpc::ServerContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetModelNicknames(::grpc::ServerContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::GetNextModelNicknames(::grpc::ServerContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::SetModelNickname(::grpc::ServerContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::RefreshDefaultModelParameters(::grpc::ServerContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::SyncCropIDs(::grpc::ServerContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ModelService::Service::TriggerDownload(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace model


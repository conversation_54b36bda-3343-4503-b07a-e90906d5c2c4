"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.translation_pb2 import (
    DurationValue as frontend___proto___translation_pb2___DurationValue,
)

from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

StatusValue = typing___NewType('StatusValue', builtin___int)
type___StatusValue = StatusValue
Status: _Status
class _Status(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[StatusValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    STATUS_ERROR = typing___cast(StatusValue, 0)
    STATUS_ESTOPPED = typing___cast(StatusValue, 1)
    STATUS_PRE_ARMED = typing___cast(StatusValue, 2)
    STATUS_POWERED_DOWN = typing___cast(StatusValue, 3)
    STATUS_POWERING_UP = typing___cast(StatusValue, 4)
    STATUS_UPDATE_INSTALLING = typing___cast(StatusValue, 5)
    STATUS_MODEL_LOADING = typing___cast(StatusValue, 6)
    STATUS_MODEL_INSTALLING = typing___cast(StatusValue, 7)
    STATUS_WEEDING = typing___cast(StatusValue, 8)
    STATUS_STANDBY = typing___cast(StatusValue, 9)
    STATUS_UNKNOWN = typing___cast(StatusValue, 10)
    STATUS_DISCONNECTED = typing___cast(StatusValue, 11)
    STATUS_LIFTED = typing___cast(StatusValue, 12)
    STATUS_LOADING = typing___cast(StatusValue, 13)
    STATUS_ALARM_AUTOFIX_IN_PROGRESS = typing___cast(StatusValue, 14)
    STATUS_FAILED_TO_POWER_UP = typing___cast(StatusValue, 15)
    STATUS_SERVER_CABINET_COOLDOWN = typing___cast(StatusValue, 16)
    STATUS_CHILLER_COOLDOWN = typing___cast(StatusValue, 17)
    STATUS_TRACTOR_NOT_SAFE = typing___cast(StatusValue, 18)
STATUS_ERROR = typing___cast(StatusValue, 0)
STATUS_ESTOPPED = typing___cast(StatusValue, 1)
STATUS_PRE_ARMED = typing___cast(StatusValue, 2)
STATUS_POWERED_DOWN = typing___cast(StatusValue, 3)
STATUS_POWERING_UP = typing___cast(StatusValue, 4)
STATUS_UPDATE_INSTALLING = typing___cast(StatusValue, 5)
STATUS_MODEL_LOADING = typing___cast(StatusValue, 6)
STATUS_MODEL_INSTALLING = typing___cast(StatusValue, 7)
STATUS_WEEDING = typing___cast(StatusValue, 8)
STATUS_STANDBY = typing___cast(StatusValue, 9)
STATUS_UNKNOWN = typing___cast(StatusValue, 10)
STATUS_DISCONNECTED = typing___cast(StatusValue, 11)
STATUS_LIFTED = typing___cast(StatusValue, 12)
STATUS_LOADING = typing___cast(StatusValue, 13)
STATUS_ALARM_AUTOFIX_IN_PROGRESS = typing___cast(StatusValue, 14)
STATUS_FAILED_TO_POWER_UP = typing___cast(StatusValue, 15)
STATUS_SERVER_CABINET_COOLDOWN = typing___cast(StatusValue, 16)
STATUS_CHILLER_COOLDOWN = typing___cast(StatusValue, 17)
STATUS_TRACTOR_NOT_SAFE = typing___cast(StatusValue, 18)

StatusLevelValue = typing___NewType('StatusLevelValue', builtin___int)
type___StatusLevelValue = StatusLevelValue
StatusLevel: _StatusLevel
class _StatusLevel(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[StatusLevelValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    INVALID = typing___cast(StatusLevelValue, 0)
    READY = typing___cast(StatusLevelValue, 1)
    LOADING = typing___cast(StatusLevelValue, 2)
    ESTOPPED = typing___cast(StatusLevelValue, 3)
INVALID = typing___cast(StatusLevelValue, 0)
READY = typing___cast(StatusLevelValue, 1)
LOADING = typing___cast(StatusLevelValue, 2)
ESTOPPED = typing___cast(StatusLevelValue, 3)

class GlobalStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    hint: typing___Text = ...
    icon_name: typing___Text = ...
    icon_color: typing___Text = ...

    def __init__(self,
        *,
        hint : typing___Optional[typing___Text] = None,
        icon_name : typing___Optional[typing___Text] = None,
        icon_color : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"hint",b"hint",u"icon_color",b"icon_color",u"icon_name",b"icon_name"]) -> None: ...
type___GlobalStatus = GlobalStatus

class ServiceStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    status_level: type___StatusLevelValue = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        status_level : typing___Optional[type___StatusLevelValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"status_level",b"status_level"]) -> None: ...
type___ServiceStatus = ServiceStatus

class ServerStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    status_level: type___StatusLevelValue = ...

    @property
    def service_status(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ServiceStatus]: ...

    def __init__(self,
        *,
        status_level : typing___Optional[type___StatusLevelValue] = None,
        service_status : typing___Optional[typing___Iterable[type___ServiceStatus]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"service_status",b"service_status",u"status_level",b"status_level"]) -> None: ...
type___ServerStatus = ServerStatus

class TranslatedStatusMessageDetails(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    details_string_key: typing___Text = ...

    @property
    def timer(self) -> frontend___proto___translation_pb2___DurationValue: ...

    def __init__(self,
        *,
        details_string_key : typing___Optional[typing___Text] = None,
        timer : typing___Optional[frontend___proto___translation_pb2___DurationValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"details",b"details",u"details_string_key",b"details_string_key",u"timer",b"timer"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"details",b"details",u"details_string_key",b"details_string_key",u"timer",b"timer"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"details",b"details"]) -> typing_extensions___Literal["details_string_key","timer"]: ...
type___TranslatedStatusMessageDetails = TranslatedStatusMessageDetails

class TranslatedStatusMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    prefix: typing___Text = ...

    @property
    def details(self) -> type___TranslatedStatusMessageDetails: ...

    def __init__(self,
        *,
        prefix : typing___Optional[typing___Text] = None,
        details : typing___Optional[type___TranslatedStatusMessageDetails] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"details",b"details"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"details",b"details",u"prefix",b"prefix"]) -> None: ...
type___TranslatedStatusMessage = TranslatedStatusMessage

class StatusBarMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class RowStatusEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___ServerStatus: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___ServerStatus] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___RowStatusEntry = RowStatusEntry

    lasers_enabled: builtin___bool = ...
    weeding_enabled: builtin___bool = ...
    status_level: type___StatusValue = ...
    status_message: typing___Text = ...
    serial: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def row_status(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___ServerStatus]: ...

    @property
    def command_status(self) -> type___ServerStatus: ...

    @property
    def global_statuses(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___GlobalStatus]: ...

    @property
    def translated_status_message(self) -> type___TranslatedStatusMessage: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        lasers_enabled : typing___Optional[builtin___bool] = None,
        weeding_enabled : typing___Optional[builtin___bool] = None,
        status_level : typing___Optional[type___StatusValue] = None,
        status_message : typing___Optional[typing___Text] = None,
        serial : typing___Optional[typing___Text] = None,
        row_status : typing___Optional[typing___Mapping[builtin___int, type___ServerStatus]] = None,
        command_status : typing___Optional[type___ServerStatus] = None,
        global_statuses : typing___Optional[typing___Iterable[type___GlobalStatus]] = None,
        translated_status_message : typing___Optional[type___TranslatedStatusMessage] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"command_status",b"command_status",u"translated_status_message",b"translated_status_message",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"command_status",b"command_status",u"global_statuses",b"global_statuses",u"lasers_enabled",b"lasers_enabled",u"row_status",b"row_status",u"serial",b"serial",u"status_level",b"status_level",u"status_message",b"status_message",u"translated_status_message",b"translated_status_message",u"ts",b"ts",u"weeding_enabled",b"weeding_enabled"]) -> None: ...
type___StatusBarMessage = StatusBarMessage

class ReportIssueRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    description: typing___Text = ...
    phone_number: typing___Text = ...

    def __init__(self,
        *,
        description : typing___Optional[typing___Text] = None,
        phone_number : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"description",b"description",u"phone_number",b"phone_number"]) -> None: ...
type___ReportIssueRequest = ReportIssueRequest

class SupportPhoneMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    support_phone: typing___Text = ...

    def __init__(self,
        *,
        support_phone : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"support_phone",b"support_phone"]) -> None: ...
type___SupportPhoneMessage = SupportPhoneMessage

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/jobs.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.frontend.proto import weeding_diagnostics_pb2 as frontend_dot_proto_dot_weeding__diagnostics__pb2
from generated.metrics.proto import metrics_aggregator_service_pb2 as metrics_dot_proto_dot_metrics__aggregator__service__pb2
from generated.frontend.proto import profile_sync_pb2 as frontend_dot_proto_dot_profile__sync__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/jobs.proto',
  package='carbon.frontend.jobs',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19\x66rontend/proto/jobs.proto\x12\x14\x63\x61rbon.frontend.jobs\x1a\x19\x66rontend/proto/util.proto\x1a(frontend/proto/weeding_diagnostics.proto\x1a.metrics/proto/metrics_aggregator_service.proto\x1a!frontend/proto/profile_sync.proto\"B\n\x0eJobDescription\x12\r\n\x05jobId\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\x12\x13\n\x0btimestampMs\x18\x03 \x01(\x03\"j\n\rActiveProfile\x12?\n\x0cprofile_type\x18\x01 \x01(\x0e\x32).carbon.frontend.profile_sync.ProfileType\x12\n\n\x02id\x18\x02 \x01(\t\x12\x0c\n\x04name\x18\x03 \x01(\t\"\xba\x04\n\x03Job\x12<\n\x0ejobDescription\x18\x01 \x01(\x0b\x32$.carbon.frontend.jobs.JobDescription\x12\x16\n\x0e\x62\x61ndingProfile\x18\x02 \x01(\t\x12\x17\n\x0fthinningProfile\x18\x03 \x01(\t\x12\x12\n\nstopTimeMs\x18\x04 \x01(\x03\x12\x18\n\x10lastUpdateTimeMs\x18\x05 \x01(\x03\x12\x17\n\x0f\x65xpectedAcreage\x18\x06 \x01(\x02\x12\x11\n\tcompleted\x18\x07 \x01(\x08\x12\x0f\n\x07\x61lmanac\x18\x08 \x01(\t\x12\x15\n\rdiscriminator\x18\t \x01(\t\x12\x0f\n\x07\x63rop_id\x18\n \x01(\t\x12\x1a\n\x12\x62\x61ndingProfileUUID\x18\x0b \x01(\t\x12\x1b\n\x13thinningProfileUUID\x18\x0c \x01(\t\x12\x1a\n\x12\x61lmanacProfileUUID\x18\r \x01(\t\x12 \n\x18\x64iscriminatorProfileUUID\x18\x0e \x01(\t\x12\x46\n\x0f\x61\x63tive_profiles\x18\x0f \x03(\x0b\x32-.carbon.frontend.jobs.Job.ActiveProfilesEntry\x12\x16\n\x0elastUsedTimeMs\x18\x10 \x01(\x03\x1aZ\n\x13\x41\x63tiveProfilesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x32\n\x05value\x18\x02 \x01(\x0b\x32#.carbon.frontend.jobs.ActiveProfile:\x02\x38\x01\"I\n\x10\x43reateJobRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x0e\n\x06\x61\x63tive\x18\x02 \x01(\x08\x12\x17\n\x0f\x65xpectedAcreage\x18\x03 \x01(\x02\"\"\n\x11\x43reateJobResponse\x12\r\n\x05jobId\x18\x01 \x01(\t\"i\n\x10UpdateJobRequest\x12<\n\x0ejobDescription\x18\x01 \x01(\x0b\x32$.carbon.frontend.jobs.JobDescription\x12\x17\n\x0f\x65xpectedAcreage\x18\x02 \x01(\x02\"H\n\x12GetNextJobsRequest\x12\x32\n\ttimestamp\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"f\n\x0eJobWithMetrics\x12&\n\x03job\x18\x01 \x01(\x0b\x32\x19.carbon.frontend.jobs.Job\x12,\n\x07metrics\x18\x02 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics\"\x92\x01\n\x13GetNextJobsResponse\x12\x32\n\x04jobs\x18\x01 \x03(\x0b\x32$.carbon.frontend.jobs.JobWithMetrics\x12\x13\n\x0b\x61\x63tiveJobId\x18\x02 \x01(\t\x12\x32\n\ttimestamp\x18\x03 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"O\n\x19GetNextActiveJobIdRequest\x12\x32\n\ttimestamp\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"e\n\x1aGetNextActiveJobIdResponse\x12\x13\n\x0b\x61\x63tiveJobId\x18\x01 \x01(\t\x12\x32\n\ttimestamp\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\x1e\n\rGetJobRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"8\n\x0eGetJobResponse\x12&\n\x03job\x18\x01 \x01(\x0b\x32\x19.carbon.frontend.jobs.Job\" \n\x0fStartJobRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"%\n\x14GetConfigDumpRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"d\n\x15GetConfigDumpResponse\x12K\n\nrootConfig\x18\x01 \x01(\x0b\x32\x37.carbon.frontend.weeding_diagnostics.ConfigNodeSnapshot\"N\n\x1bGetActiveJobMetricsResponse\x12/\n\njobMetrics\x18\x01 \x01(\x0b\x32\x1b.metrics_aggregator.Metrics\"!\n\x10\x44\x65leteJobRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"(\n\x17MarkJobCompletedRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\")\n\x18MarkJobIncompleteRequest\x12\r\n\x05jobId\x18\x01 \x01(\t\"O\n\x11GetNextJobRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\r\n\x05jobId\x18\x02 \x01(\t\"t\n\x12GetNextJobResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x31\n\x03job\x18\x02 \x01(\x0b\x32$.carbon.frontend.jobs.JobWithMetrics2\xd0\t\n\x0bJobsService\x12\x62\n\x0bGetNextJobs\x12(.carbon.frontend.jobs.GetNextJobsRequest\x1a).carbon.frontend.jobs.GetNextJobsResponse\x12\\\n\tCreateJob\x12&.carbon.frontend.jobs.CreateJobRequest\x1a\'.carbon.frontend.jobs.CreateJobResponse\x12P\n\tUpdateJob\x12&.carbon.frontend.jobs.UpdateJobRequest\x1a\x1b.carbon.frontend.util.Empty\x12N\n\x08StartJob\x12%.carbon.frontend.jobs.StartJobRequest\x1a\x1b.carbon.frontend.util.Empty\x12I\n\rStopActiveJob\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12w\n\x12GetNextActiveJobId\x12/.carbon.frontend.jobs.GetNextActiveJobIdRequest\x1a\x30.carbon.frontend.jobs.GetNextActiveJobIdResponse\x12S\n\x06GetJob\x12#.carbon.frontend.jobs.GetJobRequest\x1a$.carbon.frontend.jobs.GetJobResponse\x12h\n\rGetConfigDump\x12*.carbon.frontend.jobs.GetConfigDumpRequest\x1a+.carbon.frontend.jobs.GetConfigDumpResponse\x12\x65\n\x13GetActiveJobMetrics\x12\x1b.carbon.frontend.util.Empty\x1a\x31.carbon.frontend.jobs.GetActiveJobMetricsResponse\x12P\n\tDeleteJob\x12&.carbon.frontend.jobs.DeleteJobRequest\x1a\x1b.carbon.frontend.util.Empty\x12^\n\x10MarkJobCompleted\x12-.carbon.frontend.jobs.MarkJobCompletedRequest\x1a\x1b.carbon.frontend.util.Empty\x12`\n\x11MarkJobIncomplete\x12..carbon.frontend.jobs.MarkJobIncompleteRequest\x1a\x1b.carbon.frontend.util.Empty\x12_\n\nGetNextJob\x12\'.carbon.frontend.jobs.GetNextJobRequest\x1a(.carbon.frontend.jobs.GetNextJobResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,frontend_dot_proto_dot_weeding__diagnostics__pb2.DESCRIPTOR,metrics_dot_proto_dot_metrics__aggregator__service__pb2.DESCRIPTOR,frontend_dot_proto_dot_profile__sync__pb2.DESCRIPTOR,])




_JOBDESCRIPTION = _descriptor.Descriptor(
  name='JobDescription',
  full_name='carbon.frontend.jobs.JobDescription',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.JobDescription.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.jobs.JobDescription.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestampMs', full_name='carbon.frontend.jobs.JobDescription.timestampMs', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=203,
  serialized_end=269,
)


_ACTIVEPROFILE = _descriptor.Descriptor(
  name='ActiveProfile',
  full_name='carbon.frontend.jobs.ActiveProfile',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='profile_type', full_name='carbon.frontend.jobs.ActiveProfile.profile_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.jobs.ActiveProfile.id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.jobs.ActiveProfile.name', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=271,
  serialized_end=377,
)


_JOB_ACTIVEPROFILESENTRY = _descriptor.Descriptor(
  name='ActiveProfilesEntry',
  full_name='carbon.frontend.jobs.Job.ActiveProfilesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.jobs.Job.ActiveProfilesEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.jobs.Job.ActiveProfilesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=860,
  serialized_end=950,
)

_JOB = _descriptor.Descriptor(
  name='Job',
  full_name='carbon.frontend.jobs.Job',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobDescription', full_name='carbon.frontend.jobs.Job.jobDescription', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bandingProfile', full_name='carbon.frontend.jobs.Job.bandingProfile', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinningProfile', full_name='carbon.frontend.jobs.Job.thinningProfile', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stopTimeMs', full_name='carbon.frontend.jobs.Job.stopTimeMs', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lastUpdateTimeMs', full_name='carbon.frontend.jobs.Job.lastUpdateTimeMs', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expectedAcreage', full_name='carbon.frontend.jobs.Job.expectedAcreage', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='completed', full_name='carbon.frontend.jobs.Job.completed', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='almanac', full_name='carbon.frontend.jobs.Job.almanac', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='discriminator', full_name='carbon.frontend.jobs.Job.discriminator', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.jobs.Job.crop_id', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bandingProfileUUID', full_name='carbon.frontend.jobs.Job.bandingProfileUUID', index=10,
      number=11, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinningProfileUUID', full_name='carbon.frontend.jobs.Job.thinningProfileUUID', index=11,
      number=12, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='almanacProfileUUID', full_name='carbon.frontend.jobs.Job.almanacProfileUUID', index=12,
      number=13, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='discriminatorProfileUUID', full_name='carbon.frontend.jobs.Job.discriminatorProfileUUID', index=13,
      number=14, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_profiles', full_name='carbon.frontend.jobs.Job.active_profiles', index=14,
      number=15, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lastUsedTimeMs', full_name='carbon.frontend.jobs.Job.lastUsedTimeMs', index=15,
      number=16, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_JOB_ACTIVEPROFILESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=380,
  serialized_end=950,
)


_CREATEJOBREQUEST = _descriptor.Descriptor(
  name='CreateJobRequest',
  full_name='carbon.frontend.jobs.CreateJobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.jobs.CreateJobRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active', full_name='carbon.frontend.jobs.CreateJobRequest.active', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expectedAcreage', full_name='carbon.frontend.jobs.CreateJobRequest.expectedAcreage', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=952,
  serialized_end=1025,
)


_CREATEJOBRESPONSE = _descriptor.Descriptor(
  name='CreateJobResponse',
  full_name='carbon.frontend.jobs.CreateJobResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.CreateJobResponse.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1027,
  serialized_end=1061,
)


_UPDATEJOBREQUEST = _descriptor.Descriptor(
  name='UpdateJobRequest',
  full_name='carbon.frontend.jobs.UpdateJobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobDescription', full_name='carbon.frontend.jobs.UpdateJobRequest.jobDescription', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expectedAcreage', full_name='carbon.frontend.jobs.UpdateJobRequest.expectedAcreage', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1063,
  serialized_end=1168,
)


_GETNEXTJOBSREQUEST = _descriptor.Descriptor(
  name='GetNextJobsRequest',
  full_name='carbon.frontend.jobs.GetNextJobsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='carbon.frontend.jobs.GetNextJobsRequest.timestamp', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1170,
  serialized_end=1242,
)


_JOBWITHMETRICS = _descriptor.Descriptor(
  name='JobWithMetrics',
  full_name='carbon.frontend.jobs.JobWithMetrics',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='job', full_name='carbon.frontend.jobs.JobWithMetrics.job', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metrics', full_name='carbon.frontend.jobs.JobWithMetrics.metrics', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1244,
  serialized_end=1346,
)


_GETNEXTJOBSRESPONSE = _descriptor.Descriptor(
  name='GetNextJobsResponse',
  full_name='carbon.frontend.jobs.GetNextJobsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobs', full_name='carbon.frontend.jobs.GetNextJobsResponse.jobs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='activeJobId', full_name='carbon.frontend.jobs.GetNextJobsResponse.activeJobId', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='carbon.frontend.jobs.GetNextJobsResponse.timestamp', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1349,
  serialized_end=1495,
)


_GETNEXTACTIVEJOBIDREQUEST = _descriptor.Descriptor(
  name='GetNextActiveJobIdRequest',
  full_name='carbon.frontend.jobs.GetNextActiveJobIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='carbon.frontend.jobs.GetNextActiveJobIdRequest.timestamp', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1497,
  serialized_end=1576,
)


_GETNEXTACTIVEJOBIDRESPONSE = _descriptor.Descriptor(
  name='GetNextActiveJobIdResponse',
  full_name='carbon.frontend.jobs.GetNextActiveJobIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='activeJobId', full_name='carbon.frontend.jobs.GetNextActiveJobIdResponse.activeJobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='carbon.frontend.jobs.GetNextActiveJobIdResponse.timestamp', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1578,
  serialized_end=1679,
)


_GETJOBREQUEST = _descriptor.Descriptor(
  name='GetJobRequest',
  full_name='carbon.frontend.jobs.GetJobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.GetJobRequest.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1681,
  serialized_end=1711,
)


_GETJOBRESPONSE = _descriptor.Descriptor(
  name='GetJobResponse',
  full_name='carbon.frontend.jobs.GetJobResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='job', full_name='carbon.frontend.jobs.GetJobResponse.job', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1713,
  serialized_end=1769,
)


_STARTJOBREQUEST = _descriptor.Descriptor(
  name='StartJobRequest',
  full_name='carbon.frontend.jobs.StartJobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.StartJobRequest.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1771,
  serialized_end=1803,
)


_GETCONFIGDUMPREQUEST = _descriptor.Descriptor(
  name='GetConfigDumpRequest',
  full_name='carbon.frontend.jobs.GetConfigDumpRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.GetConfigDumpRequest.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1805,
  serialized_end=1842,
)


_GETCONFIGDUMPRESPONSE = _descriptor.Descriptor(
  name='GetConfigDumpResponse',
  full_name='carbon.frontend.jobs.GetConfigDumpResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='rootConfig', full_name='carbon.frontend.jobs.GetConfigDumpResponse.rootConfig', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1844,
  serialized_end=1944,
)


_GETACTIVEJOBMETRICSRESPONSE = _descriptor.Descriptor(
  name='GetActiveJobMetricsResponse',
  full_name='carbon.frontend.jobs.GetActiveJobMetricsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobMetrics', full_name='carbon.frontend.jobs.GetActiveJobMetricsResponse.jobMetrics', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1946,
  serialized_end=2024,
)


_DELETEJOBREQUEST = _descriptor.Descriptor(
  name='DeleteJobRequest',
  full_name='carbon.frontend.jobs.DeleteJobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.DeleteJobRequest.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2026,
  serialized_end=2059,
)


_MARKJOBCOMPLETEDREQUEST = _descriptor.Descriptor(
  name='MarkJobCompletedRequest',
  full_name='carbon.frontend.jobs.MarkJobCompletedRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.MarkJobCompletedRequest.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2061,
  serialized_end=2101,
)


_MARKJOBINCOMPLETEREQUEST = _descriptor.Descriptor(
  name='MarkJobIncompleteRequest',
  full_name='carbon.frontend.jobs.MarkJobIncompleteRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.MarkJobIncompleteRequest.jobId', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2103,
  serialized_end=2144,
)


_GETNEXTJOBREQUEST = _descriptor.Descriptor(
  name='GetNextJobRequest',
  full_name='carbon.frontend.jobs.GetNextJobRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.jobs.GetNextJobRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='jobId', full_name='carbon.frontend.jobs.GetNextJobRequest.jobId', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2146,
  serialized_end=2225,
)


_GETNEXTJOBRESPONSE = _descriptor.Descriptor(
  name='GetNextJobResponse',
  full_name='carbon.frontend.jobs.GetNextJobResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.jobs.GetNextJobResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='job', full_name='carbon.frontend.jobs.GetNextJobResponse.job', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2227,
  serialized_end=2343,
)

_ACTIVEPROFILE.fields_by_name['profile_type'].enum_type = frontend_dot_proto_dot_profile__sync__pb2._PROFILETYPE
_JOB_ACTIVEPROFILESENTRY.fields_by_name['value'].message_type = _ACTIVEPROFILE
_JOB_ACTIVEPROFILESENTRY.containing_type = _JOB
_JOB.fields_by_name['jobDescription'].message_type = _JOBDESCRIPTION
_JOB.fields_by_name['active_profiles'].message_type = _JOB_ACTIVEPROFILESENTRY
_UPDATEJOBREQUEST.fields_by_name['jobDescription'].message_type = _JOBDESCRIPTION
_GETNEXTJOBSREQUEST.fields_by_name['timestamp'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_JOBWITHMETRICS.fields_by_name['job'].message_type = _JOB
_JOBWITHMETRICS.fields_by_name['metrics'].message_type = metrics_dot_proto_dot_metrics__aggregator__service__pb2._METRICS
_GETNEXTJOBSRESPONSE.fields_by_name['jobs'].message_type = _JOBWITHMETRICS
_GETNEXTJOBSRESPONSE.fields_by_name['timestamp'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTACTIVEJOBIDREQUEST.fields_by_name['timestamp'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTACTIVEJOBIDRESPONSE.fields_by_name['timestamp'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETJOBRESPONSE.fields_by_name['job'].message_type = _JOB
_GETCONFIGDUMPRESPONSE.fields_by_name['rootConfig'].message_type = frontend_dot_proto_dot_weeding__diagnostics__pb2._CONFIGNODESNAPSHOT
_GETACTIVEJOBMETRICSRESPONSE.fields_by_name['jobMetrics'].message_type = metrics_dot_proto_dot_metrics__aggregator__service__pb2._METRICS
_GETNEXTJOBREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTJOBRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTJOBRESPONSE.fields_by_name['job'].message_type = _JOBWITHMETRICS
DESCRIPTOR.message_types_by_name['JobDescription'] = _JOBDESCRIPTION
DESCRIPTOR.message_types_by_name['ActiveProfile'] = _ACTIVEPROFILE
DESCRIPTOR.message_types_by_name['Job'] = _JOB
DESCRIPTOR.message_types_by_name['CreateJobRequest'] = _CREATEJOBREQUEST
DESCRIPTOR.message_types_by_name['CreateJobResponse'] = _CREATEJOBRESPONSE
DESCRIPTOR.message_types_by_name['UpdateJobRequest'] = _UPDATEJOBREQUEST
DESCRIPTOR.message_types_by_name['GetNextJobsRequest'] = _GETNEXTJOBSREQUEST
DESCRIPTOR.message_types_by_name['JobWithMetrics'] = _JOBWITHMETRICS
DESCRIPTOR.message_types_by_name['GetNextJobsResponse'] = _GETNEXTJOBSRESPONSE
DESCRIPTOR.message_types_by_name['GetNextActiveJobIdRequest'] = _GETNEXTACTIVEJOBIDREQUEST
DESCRIPTOR.message_types_by_name['GetNextActiveJobIdResponse'] = _GETNEXTACTIVEJOBIDRESPONSE
DESCRIPTOR.message_types_by_name['GetJobRequest'] = _GETJOBREQUEST
DESCRIPTOR.message_types_by_name['GetJobResponse'] = _GETJOBRESPONSE
DESCRIPTOR.message_types_by_name['StartJobRequest'] = _STARTJOBREQUEST
DESCRIPTOR.message_types_by_name['GetConfigDumpRequest'] = _GETCONFIGDUMPREQUEST
DESCRIPTOR.message_types_by_name['GetConfigDumpResponse'] = _GETCONFIGDUMPRESPONSE
DESCRIPTOR.message_types_by_name['GetActiveJobMetricsResponse'] = _GETACTIVEJOBMETRICSRESPONSE
DESCRIPTOR.message_types_by_name['DeleteJobRequest'] = _DELETEJOBREQUEST
DESCRIPTOR.message_types_by_name['MarkJobCompletedRequest'] = _MARKJOBCOMPLETEDREQUEST
DESCRIPTOR.message_types_by_name['MarkJobIncompleteRequest'] = _MARKJOBINCOMPLETEREQUEST
DESCRIPTOR.message_types_by_name['GetNextJobRequest'] = _GETNEXTJOBREQUEST
DESCRIPTOR.message_types_by_name['GetNextJobResponse'] = _GETNEXTJOBRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

JobDescription = _reflection.GeneratedProtocolMessageType('JobDescription', (_message.Message,), {
  'DESCRIPTOR' : _JOBDESCRIPTION,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.JobDescription)
  })
_sym_db.RegisterMessage(JobDescription)

ActiveProfile = _reflection.GeneratedProtocolMessageType('ActiveProfile', (_message.Message,), {
  'DESCRIPTOR' : _ACTIVEPROFILE,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.ActiveProfile)
  })
_sym_db.RegisterMessage(ActiveProfile)

Job = _reflection.GeneratedProtocolMessageType('Job', (_message.Message,), {

  'ActiveProfilesEntry' : _reflection.GeneratedProtocolMessageType('ActiveProfilesEntry', (_message.Message,), {
    'DESCRIPTOR' : _JOB_ACTIVEPROFILESENTRY,
    '__module__' : 'frontend.proto.jobs_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.Job.ActiveProfilesEntry)
    })
  ,
  'DESCRIPTOR' : _JOB,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.Job)
  })
_sym_db.RegisterMessage(Job)
_sym_db.RegisterMessage(Job.ActiveProfilesEntry)

CreateJobRequest = _reflection.GeneratedProtocolMessageType('CreateJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _CREATEJOBREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.CreateJobRequest)
  })
_sym_db.RegisterMessage(CreateJobRequest)

CreateJobResponse = _reflection.GeneratedProtocolMessageType('CreateJobResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATEJOBRESPONSE,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.CreateJobResponse)
  })
_sym_db.RegisterMessage(CreateJobResponse)

UpdateJobRequest = _reflection.GeneratedProtocolMessageType('UpdateJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEJOBREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.UpdateJobRequest)
  })
_sym_db.RegisterMessage(UpdateJobRequest)

GetNextJobsRequest = _reflection.GeneratedProtocolMessageType('GetNextJobsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTJOBSREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextJobsRequest)
  })
_sym_db.RegisterMessage(GetNextJobsRequest)

JobWithMetrics = _reflection.GeneratedProtocolMessageType('JobWithMetrics', (_message.Message,), {
  'DESCRIPTOR' : _JOBWITHMETRICS,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.JobWithMetrics)
  })
_sym_db.RegisterMessage(JobWithMetrics)

GetNextJobsResponse = _reflection.GeneratedProtocolMessageType('GetNextJobsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTJOBSRESPONSE,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextJobsResponse)
  })
_sym_db.RegisterMessage(GetNextJobsResponse)

GetNextActiveJobIdRequest = _reflection.GeneratedProtocolMessageType('GetNextActiveJobIdRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVEJOBIDREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextActiveJobIdRequest)
  })
_sym_db.RegisterMessage(GetNextActiveJobIdRequest)

GetNextActiveJobIdResponse = _reflection.GeneratedProtocolMessageType('GetNextActiveJobIdResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVEJOBIDRESPONSE,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextActiveJobIdResponse)
  })
_sym_db.RegisterMessage(GetNextActiveJobIdResponse)

GetJobRequest = _reflection.GeneratedProtocolMessageType('GetJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETJOBREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetJobRequest)
  })
_sym_db.RegisterMessage(GetJobRequest)

GetJobResponse = _reflection.GeneratedProtocolMessageType('GetJobResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETJOBRESPONSE,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetJobResponse)
  })
_sym_db.RegisterMessage(GetJobResponse)

StartJobRequest = _reflection.GeneratedProtocolMessageType('StartJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTJOBREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.StartJobRequest)
  })
_sym_db.RegisterMessage(StartJobRequest)

GetConfigDumpRequest = _reflection.GeneratedProtocolMessageType('GetConfigDumpRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETCONFIGDUMPREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetConfigDumpRequest)
  })
_sym_db.RegisterMessage(GetConfigDumpRequest)

GetConfigDumpResponse = _reflection.GeneratedProtocolMessageType('GetConfigDumpResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCONFIGDUMPRESPONSE,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetConfigDumpResponse)
  })
_sym_db.RegisterMessage(GetConfigDumpResponse)

GetActiveJobMetricsResponse = _reflection.GeneratedProtocolMessageType('GetActiveJobMetricsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETACTIVEJOBMETRICSRESPONSE,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetActiveJobMetricsResponse)
  })
_sym_db.RegisterMessage(GetActiveJobMetricsResponse)

DeleteJobRequest = _reflection.GeneratedProtocolMessageType('DeleteJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETEJOBREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.DeleteJobRequest)
  })
_sym_db.RegisterMessage(DeleteJobRequest)

MarkJobCompletedRequest = _reflection.GeneratedProtocolMessageType('MarkJobCompletedRequest', (_message.Message,), {
  'DESCRIPTOR' : _MARKJOBCOMPLETEDREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.MarkJobCompletedRequest)
  })
_sym_db.RegisterMessage(MarkJobCompletedRequest)

MarkJobIncompleteRequest = _reflection.GeneratedProtocolMessageType('MarkJobIncompleteRequest', (_message.Message,), {
  'DESCRIPTOR' : _MARKJOBINCOMPLETEREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.MarkJobIncompleteRequest)
  })
_sym_db.RegisterMessage(MarkJobIncompleteRequest)

GetNextJobRequest = _reflection.GeneratedProtocolMessageType('GetNextJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTJOBREQUEST,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextJobRequest)
  })
_sym_db.RegisterMessage(GetNextJobRequest)

GetNextJobResponse = _reflection.GeneratedProtocolMessageType('GetNextJobResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTJOBRESPONSE,
  '__module__' : 'frontend.proto.jobs_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.jobs.GetNextJobResponse)
  })
_sym_db.RegisterMessage(GetNextJobResponse)


DESCRIPTOR._options = None
_JOB_ACTIVEPROFILESENTRY._options = None

_JOBSSERVICE = _descriptor.ServiceDescriptor(
  name='JobsService',
  full_name='carbon.frontend.jobs.JobsService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=2346,
  serialized_end=3578,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextJobs',
    full_name='carbon.frontend.jobs.JobsService.GetNextJobs',
    index=0,
    containing_service=None,
    input_type=_GETNEXTJOBSREQUEST,
    output_type=_GETNEXTJOBSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CreateJob',
    full_name='carbon.frontend.jobs.JobsService.CreateJob',
    index=1,
    containing_service=None,
    input_type=_CREATEJOBREQUEST,
    output_type=_CREATEJOBRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UpdateJob',
    full_name='carbon.frontend.jobs.JobsService.UpdateJob',
    index=2,
    containing_service=None,
    input_type=_UPDATEJOBREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartJob',
    full_name='carbon.frontend.jobs.JobsService.StartJob',
    index=3,
    containing_service=None,
    input_type=_STARTJOBREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StopActiveJob',
    full_name='carbon.frontend.jobs.JobsService.StopActiveJob',
    index=4,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextActiveJobId',
    full_name='carbon.frontend.jobs.JobsService.GetNextActiveJobId',
    index=5,
    containing_service=None,
    input_type=_GETNEXTACTIVEJOBIDREQUEST,
    output_type=_GETNEXTACTIVEJOBIDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetJob',
    full_name='carbon.frontend.jobs.JobsService.GetJob',
    index=6,
    containing_service=None,
    input_type=_GETJOBREQUEST,
    output_type=_GETJOBRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetConfigDump',
    full_name='carbon.frontend.jobs.JobsService.GetConfigDump',
    index=7,
    containing_service=None,
    input_type=_GETCONFIGDUMPREQUEST,
    output_type=_GETCONFIGDUMPRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetActiveJobMetrics',
    full_name='carbon.frontend.jobs.JobsService.GetActiveJobMetrics',
    index=8,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_GETACTIVEJOBMETRICSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteJob',
    full_name='carbon.frontend.jobs.JobsService.DeleteJob',
    index=9,
    containing_service=None,
    input_type=_DELETEJOBREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='MarkJobCompleted',
    full_name='carbon.frontend.jobs.JobsService.MarkJobCompleted',
    index=10,
    containing_service=None,
    input_type=_MARKJOBCOMPLETEDREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='MarkJobIncomplete',
    full_name='carbon.frontend.jobs.JobsService.MarkJobIncomplete',
    index=11,
    containing_service=None,
    input_type=_MARKJOBINCOMPLETEREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextJob',
    full_name='carbon.frontend.jobs.JobsService.GetNextJob',
    index=12,
    containing_service=None,
    input_type=_GETNEXTJOBREQUEST,
    output_type=_GETNEXTJOBRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_JOBSSERVICE)

DESCRIPTOR.services_by_name['JobsService'] = _JOBSSERVICE

# @@protoc_insertion_point(module_scope)

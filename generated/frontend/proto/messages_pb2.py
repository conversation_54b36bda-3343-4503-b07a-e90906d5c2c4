# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/messages.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import alarm_pb2 as frontend_dot_proto_dot_alarm__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/messages.proto',
  package='carbon.frontend.debug',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1d\x66rontend/proto/messages.proto\x12\x15\x63\x61rbon.frontend.debug\x1a\x1a\x66rontend/proto/alarm.proto\x1a\x19\x66rontend/proto/util.proto\"\xfe\x01\n\x07Message\x12\n\n\x02id\x18\x01 \x01(\x03\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0f\n\x07message\x18\x03 \x01(\t\x12\x14\n\x0c\x66rom_support\x18\x04 \x01(\x08\x12\x0c\n\x04read\x18\x05 \x01(\x08\x12\x16\n\x0e\x61uthor_user_id\x18\x06 \x01(\t\x12\x17\n\x0f\x61uthor_robot_id\x18\x07 \x01(\x03\x12\x19\n\x11recipient_user_id\x18\x08 \x01(\t\x12\x1d\n\x15recipient_customer_id\x18\t \x01(\x03\x12\x1a\n\x12recipient_robot_id\x18\n \x01(\x03\"!\n\x0eMessageRequest\x12\x0f\n\x07message\x18\x01 \x01(\t\">\n\x0fMessagesRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"q\n\x10MessagesResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x30\n\x08messages\x18\x02 \x03(\x0b\x32\x1e.carbon.frontend.debug.Message\"\x19\n\x0bReadRequest\x12\n\n\x02id\x18\x01 \x01(\x03\x32\x91\x02\n\x0fMessagesService\x12N\n\x0bReadMessage\x12\".carbon.frontend.debug.ReadRequest\x1a\x1b.carbon.frontend.util.Empty\x12Q\n\x0bSendMessage\x12%.carbon.frontend.debug.MessageRequest\x1a\x1b.carbon.frontend.util.Empty\x12[\n\x0fGetNextMessages\x12\x1f.carbon.frontend.util.Timestamp\x1a\'.carbon.frontend.debug.MessagesResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_alarm__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_MESSAGE = _descriptor.Descriptor(
  name='Message',
  full_name='carbon.frontend.debug.Message',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.debug.Message.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.debug.Message.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='message', full_name='carbon.frontend.debug.Message.message', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='from_support', full_name='carbon.frontend.debug.Message.from_support', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='read', full_name='carbon.frontend.debug.Message.read', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='author_user_id', full_name='carbon.frontend.debug.Message.author_user_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='author_robot_id', full_name='carbon.frontend.debug.Message.author_robot_id', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recipient_user_id', full_name='carbon.frontend.debug.Message.recipient_user_id', index=7,
      number=8, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recipient_customer_id', full_name='carbon.frontend.debug.Message.recipient_customer_id', index=8,
      number=9, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='recipient_robot_id', full_name='carbon.frontend.debug.Message.recipient_robot_id', index=9,
      number=10, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=112,
  serialized_end=366,
)


_MESSAGEREQUEST = _descriptor.Descriptor(
  name='MessageRequest',
  full_name='carbon.frontend.debug.MessageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='message', full_name='carbon.frontend.debug.MessageRequest.message', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=368,
  serialized_end=401,
)


_MESSAGESREQUEST = _descriptor.Descriptor(
  name='MessagesRequest',
  full_name='carbon.frontend.debug.MessagesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.debug.MessagesRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=403,
  serialized_end=465,
)


_MESSAGESRESPONSE = _descriptor.Descriptor(
  name='MessagesResponse',
  full_name='carbon.frontend.debug.MessagesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.debug.MessagesResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='messages', full_name='carbon.frontend.debug.MessagesResponse.messages', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=467,
  serialized_end=580,
)


_READREQUEST = _descriptor.Descriptor(
  name='ReadRequest',
  full_name='carbon.frontend.debug.ReadRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.debug.ReadRequest.id', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=582,
  serialized_end=607,
)

_MESSAGE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_MESSAGESREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_MESSAGESRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_MESSAGESRESPONSE.fields_by_name['messages'].message_type = _MESSAGE
DESCRIPTOR.message_types_by_name['Message'] = _MESSAGE
DESCRIPTOR.message_types_by_name['MessageRequest'] = _MESSAGEREQUEST
DESCRIPTOR.message_types_by_name['MessagesRequest'] = _MESSAGESREQUEST
DESCRIPTOR.message_types_by_name['MessagesResponse'] = _MESSAGESRESPONSE
DESCRIPTOR.message_types_by_name['ReadRequest'] = _READREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Message = _reflection.GeneratedProtocolMessageType('Message', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGE,
  '__module__' : 'frontend.proto.messages_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.debug.Message)
  })
_sym_db.RegisterMessage(Message)

MessageRequest = _reflection.GeneratedProtocolMessageType('MessageRequest', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGEREQUEST,
  '__module__' : 'frontend.proto.messages_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.debug.MessageRequest)
  })
_sym_db.RegisterMessage(MessageRequest)

MessagesRequest = _reflection.GeneratedProtocolMessageType('MessagesRequest', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGESREQUEST,
  '__module__' : 'frontend.proto.messages_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.debug.MessagesRequest)
  })
_sym_db.RegisterMessage(MessagesRequest)

MessagesResponse = _reflection.GeneratedProtocolMessageType('MessagesResponse', (_message.Message,), {
  'DESCRIPTOR' : _MESSAGESRESPONSE,
  '__module__' : 'frontend.proto.messages_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.debug.MessagesResponse)
  })
_sym_db.RegisterMessage(MessagesResponse)

ReadRequest = _reflection.GeneratedProtocolMessageType('ReadRequest', (_message.Message,), {
  'DESCRIPTOR' : _READREQUEST,
  '__module__' : 'frontend.proto.messages_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.debug.ReadRequest)
  })
_sym_db.RegisterMessage(ReadRequest)


DESCRIPTOR._options = None

_MESSAGESSERVICE = _descriptor.ServiceDescriptor(
  name='MessagesService',
  full_name='carbon.frontend.debug.MessagesService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=610,
  serialized_end=883,
  methods=[
  _descriptor.MethodDescriptor(
    name='ReadMessage',
    full_name='carbon.frontend.debug.MessagesService.ReadMessage',
    index=0,
    containing_service=None,
    input_type=_READREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SendMessage',
    full_name='carbon.frontend.debug.MessagesService.SendMessage',
    index=1,
    containing_service=None,
    input_type=_MESSAGEREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextMessages',
    full_name='carbon.frontend.debug.MessagesService.GetNextMessages',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_MESSAGESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_MESSAGESSERVICE)

DESCRIPTOR.services_by_name['MessagesService'] = _MESSAGESSERVICE

# @@protoc_insertion_point(module_scope)

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/category.proto

#include "frontend/proto/category.pb.h"
#include "frontend/proto/category.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace category {

static const char* CategoryService_method_names[] = {
  "/carbon.frontend.category.CategoryService/GetNextCategoryData",
};

std::unique_ptr< CategoryService::Stub> CategoryService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< CategoryService::Stub> stub(new CategoryService::Stub(channel, options));
  return stub;
}

CategoryService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextCategoryData_(CategoryService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status CategoryService::Stub::GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::carbon::frontend::category::GetNextCategoryDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::category::GetNextCategoryDataRequest, ::carbon::frontend::category::GetNextCategoryDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextCategoryData_, context, request, response);
}

void CategoryService::Stub::async::GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::category::GetNextCategoryDataRequest, ::carbon::frontend::category::GetNextCategoryDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCategoryData_, context, request, response, std::move(f));
}

void CategoryService::Stub::async::GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCategoryData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::category::GetNextCategoryDataResponse>* CategoryService::Stub::PrepareAsyncGetNextCategoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::category::GetNextCategoryDataResponse, ::carbon::frontend::category::GetNextCategoryDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextCategoryData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::category::GetNextCategoryDataResponse>* CategoryService::Stub::AsyncGetNextCategoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextCategoryDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

CategoryService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CategoryService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CategoryService::Service, ::carbon::frontend::category::GetNextCategoryDataRequest, ::carbon::frontend::category::GetNextCategoryDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CategoryService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::category::GetNextCategoryDataRequest* req,
             ::carbon::frontend::category::GetNextCategoryDataResponse* resp) {
               return service->GetNextCategoryData(ctx, req, resp);
             }, this)));
}

CategoryService::Service::~Service() {
}

::grpc::Status CategoryService::Service::GetNextCategoryData(::grpc::ServerContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace category


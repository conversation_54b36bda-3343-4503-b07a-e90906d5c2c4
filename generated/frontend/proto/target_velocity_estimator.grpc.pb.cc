// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/target_velocity_estimator.proto

#include "frontend/proto/target_velocity_estimator.pb.h"
#include "frontend/proto/target_velocity_estimator.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace target_velocity_estimator {

static const char* TargetVelocityEstimatorService_method_names[] = {
  "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextAvailableProfiles",
  "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/GetNextActiveProfile",
  "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/LoadProfile",
  "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SaveProfile",
  "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/SetActive",
  "/carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService/DeleteProfile",
};

std::unique_ptr< TargetVelocityEstimatorService::Stub> TargetVelocityEstimatorService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< TargetVelocityEstimatorService::Stub> stub(new TargetVelocityEstimatorService::Stub(channel, options));
  return stub;
}

TargetVelocityEstimatorService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextAvailableProfiles_(TargetVelocityEstimatorService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextActiveProfile_(TargetVelocityEstimatorService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_LoadProfile_(TargetVelocityEstimatorService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SaveProfile_(TargetVelocityEstimatorService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetActive_(TargetVelocityEstimatorService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteProfile_(TargetVelocityEstimatorService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status TargetVelocityEstimatorService::Stub::GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAvailableProfiles_, context, request, response);
}

void TargetVelocityEstimatorService::Stub::async::GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAvailableProfiles_, context, request, response, std::move(f));
}

void TargetVelocityEstimatorService::Stub::async::GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAvailableProfiles_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* TargetVelocityEstimatorService::Stub::PrepareAsyncGetNextAvailableProfilesRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAvailableProfiles_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* TargetVelocityEstimatorService::Stub::AsyncGetNextAvailableProfilesRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAvailableProfilesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TargetVelocityEstimatorService::Stub::GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextActiveProfile_, context, request, response);
}

void TargetVelocityEstimatorService::Stub::async::GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveProfile_, context, request, response, std::move(f));
}

void TargetVelocityEstimatorService::Stub::async::GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* TargetVelocityEstimatorService::Stub::PrepareAsyncGetNextActiveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextActiveProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* TargetVelocityEstimatorService::Stub::AsyncGetNextActiveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextActiveProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TargetVelocityEstimatorService::Stub::LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_LoadProfile_, context, request, response);
}

void TargetVelocityEstimatorService::Stub::async::LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadProfile_, context, request, response, std::move(f));
}

void TargetVelocityEstimatorService::Stub::async::LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_LoadProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* TargetVelocityEstimatorService::Stub::PrepareAsyncLoadProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_LoadProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* TargetVelocityEstimatorService::Stub::AsyncLoadProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncLoadProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TargetVelocityEstimatorService::Stub::SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SaveProfile_, context, request, response);
}

void TargetVelocityEstimatorService::Stub::async::SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveProfile_, context, request, response, std::move(f));
}

void TargetVelocityEstimatorService::Stub::async::SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SaveProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* TargetVelocityEstimatorService::Stub::PrepareAsyncSaveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SaveProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* TargetVelocityEstimatorService::Stub::AsyncSaveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSaveProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TargetVelocityEstimatorService::Stub::SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetActive_, context, request, response);
}

void TargetVelocityEstimatorService::Stub::async::SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActive_, context, request, response, std::move(f));
}

void TargetVelocityEstimatorService::Stub::async::SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActive_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* TargetVelocityEstimatorService::Stub::PrepareAsyncSetActiveRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetActive_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* TargetVelocityEstimatorService::Stub::AsyncSetActiveRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetActiveRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status TargetVelocityEstimatorService::Stub::DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteProfile_, context, request, response);
}

void TargetVelocityEstimatorService::Stub::async::DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteProfile_, context, request, response, std::move(f));
}

void TargetVelocityEstimatorService::Stub::async::DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* TargetVelocityEstimatorService::Stub::PrepareAsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* TargetVelocityEstimatorService::Stub::AsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

TargetVelocityEstimatorService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TargetVelocityEstimatorService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TargetVelocityEstimatorService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TargetVelocityEstimatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* resp) {
               return service->GetNextAvailableProfiles(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TargetVelocityEstimatorService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TargetVelocityEstimatorService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TargetVelocityEstimatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* resp) {
               return service->GetNextActiveProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TargetVelocityEstimatorService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TargetVelocityEstimatorService::Service, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TargetVelocityEstimatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* req,
             ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* resp) {
               return service->LoadProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TargetVelocityEstimatorService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TargetVelocityEstimatorService::Service, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TargetVelocityEstimatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* req,
             ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* resp) {
               return service->SaveProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TargetVelocityEstimatorService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TargetVelocityEstimatorService::Service, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TargetVelocityEstimatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* req,
             ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* resp) {
               return service->SetActive(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      TargetVelocityEstimatorService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< TargetVelocityEstimatorService::Service, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](TargetVelocityEstimatorService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* req,
             ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* resp) {
               return service->DeleteProfile(ctx, req, resp);
             }, this)));
}

TargetVelocityEstimatorService::Service::~Service() {
}

::grpc::Status TargetVelocityEstimatorService::Service::GetNextAvailableProfiles(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TargetVelocityEstimatorService::Service::GetNextActiveProfile(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TargetVelocityEstimatorService::Service::LoadProfile(::grpc::ServerContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TargetVelocityEstimatorService::Service::SaveProfile(::grpc::ServerContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TargetVelocityEstimatorService::Service::SetActive(::grpc::ServerContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status TargetVelocityEstimatorService::Service::DeleteProfile(::grpc::ServerContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace target_velocity_estimator


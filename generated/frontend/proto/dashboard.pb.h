// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/dashboard.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdashboard_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdashboard_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/translation.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fdashboard_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fdashboard_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[15]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fdashboard_2eproto;
namespace carbon {
namespace frontend {
namespace dashboard {
class CropModel;
struct CropModelDefaultTypeInternal;
extern CropModelDefaultTypeInternal _CropModel_default_instance_;
class CropModelOptions;
struct CropModelOptionsDefaultTypeInternal;
extern CropModelOptionsDefaultTypeInternal _CropModelOptions_default_instance_;
class CruiseEnable;
struct CruiseEnableDefaultTypeInternal;
extern CruiseEnableDefaultTypeInternal _CruiseEnable_default_instance_;
class DashboardStateMessage;
struct DashboardStateMessageDefaultTypeInternal;
extern DashboardStateMessageDefaultTypeInternal _DashboardStateMessage_default_instance_;
class DashboardStateMessage_RowStatesEntry_DoNotUse;
struct DashboardStateMessage_RowStatesEntry_DoNotUseDefaultTypeInternal;
extern DashboardStateMessage_RowStatesEntry_DoNotUseDefaultTypeInternal _DashboardStateMessage_RowStatesEntry_DoNotUse_default_instance_;
class ExtraConclusion;
struct ExtraConclusionDefaultTypeInternal;
extern ExtraConclusionDefaultTypeInternal _ExtraConclusion_default_instance_;
class ExtraStatus;
struct ExtraStatusDefaultTypeInternal;
extern ExtraStatusDefaultTypeInternal _ExtraStatus_default_instance_;
class RowId;
struct RowIdDefaultTypeInternal;
extern RowIdDefaultTypeInternal _RowId_default_instance_;
class RowSpacing;
struct RowSpacingDefaultTypeInternal;
extern RowSpacingDefaultTypeInternal _RowSpacing_default_instance_;
class RowStateMessage;
struct RowStateMessageDefaultTypeInternal;
extern RowStateMessageDefaultTypeInternal _RowStateMessage_default_instance_;
class TargetingState;
struct TargetingStateDefaultTypeInternal;
extern TargetingStateDefaultTypeInternal _TargetingState_default_instance_;
class TargetingState_EnabledRowsEntry_DoNotUse;
struct TargetingState_EnabledRowsEntry_DoNotUseDefaultTypeInternal;
extern TargetingState_EnabledRowsEntry_DoNotUseDefaultTypeInternal _TargetingState_EnabledRowsEntry_DoNotUse_default_instance_;
class ThinningTargeting;
struct ThinningTargetingDefaultTypeInternal;
extern ThinningTargetingDefaultTypeInternal _ThinningTargeting_default_instance_;
class WeedTargeting;
struct WeedTargetingDefaultTypeInternal;
extern WeedTargetingDefaultTypeInternal _WeedTargeting_default_instance_;
class WeedingVelocity;
struct WeedingVelocityDefaultTypeInternal;
extern WeedingVelocityDefaultTypeInternal _WeedingVelocity_default_instance_;
}  // namespace dashboard
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::dashboard::CropModel* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::CropModel>(Arena*);
template<> ::carbon::frontend::dashboard::CropModelOptions* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::CropModelOptions>(Arena*);
template<> ::carbon::frontend::dashboard::CruiseEnable* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::CruiseEnable>(Arena*);
template<> ::carbon::frontend::dashboard::DashboardStateMessage* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::DashboardStateMessage>(Arena*);
template<> ::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::dashboard::ExtraConclusion* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::ExtraConclusion>(Arena*);
template<> ::carbon::frontend::dashboard::ExtraStatus* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::ExtraStatus>(Arena*);
template<> ::carbon::frontend::dashboard::RowId* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::RowId>(Arena*);
template<> ::carbon::frontend::dashboard::RowSpacing* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::RowSpacing>(Arena*);
template<> ::carbon::frontend::dashboard::RowStateMessage* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::RowStateMessage>(Arena*);
template<> ::carbon::frontend::dashboard::TargetingState* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::TargetingState>(Arena*);
template<> ::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::dashboard::ThinningTargeting* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::ThinningTargeting>(Arena*);
template<> ::carbon::frontend::dashboard::WeedTargeting* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::WeedTargeting>(Arena*);
template<> ::carbon::frontend::dashboard::WeedingVelocity* Arena::CreateMaybeMessage<::carbon::frontend::dashboard::WeedingVelocity>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace dashboard {

enum SafetyOverrideState : int {
  SafetyOverrideNone = 0,
  SafetyOverrideVelocityStop = 1,
  SafetyOverrideState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  SafetyOverrideState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool SafetyOverrideState_IsValid(int value);
constexpr SafetyOverrideState SafetyOverrideState_MIN = SafetyOverrideNone;
constexpr SafetyOverrideState SafetyOverrideState_MAX = SafetyOverrideVelocityStop;
constexpr int SafetyOverrideState_ARRAYSIZE = SafetyOverrideState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SafetyOverrideState_descriptor();
template<typename T>
inline const std::string& SafetyOverrideState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, SafetyOverrideState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function SafetyOverrideState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    SafetyOverrideState_descriptor(), enum_t_value);
}
inline bool SafetyOverrideState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, SafetyOverrideState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<SafetyOverrideState>(
    SafetyOverrideState_descriptor(), name, value);
}
enum ImplementState : int {
  RAISED = 0,
  LOWERED = 1,
  ImplementState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ImplementState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ImplementState_IsValid(int value);
constexpr ImplementState ImplementState_MIN = RAISED;
constexpr ImplementState ImplementState_MAX = LOWERED;
constexpr int ImplementState_ARRAYSIZE = ImplementState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ImplementState_descriptor();
template<typename T>
inline const std::string& ImplementState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ImplementState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ImplementState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ImplementState_descriptor(), enum_t_value);
}
inline bool ImplementState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ImplementState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ImplementState>(
    ImplementState_descriptor(), name, value);
}
// ===================================================================

class ExtraStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.ExtraStatus) */ {
 public:
  inline ExtraStatus() : ExtraStatus(nullptr) {}
  ~ExtraStatus() override;
  explicit constexpr ExtraStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExtraStatus(const ExtraStatus& from);
  ExtraStatus(ExtraStatus&& from) noexcept
    : ExtraStatus() {
    *this = ::std::move(from);
  }

  inline ExtraStatus& operator=(const ExtraStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExtraStatus& operator=(ExtraStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExtraStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExtraStatus* internal_default_instance() {
    return reinterpret_cast<const ExtraStatus*>(
               &_ExtraStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ExtraStatus& a, ExtraStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(ExtraStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExtraStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExtraStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExtraStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExtraStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ExtraStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExtraStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.ExtraStatus";
  }
  protected:
  explicit ExtraStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTitleFieldNumber = 1,
    kIconNameFieldNumber = 2,
    kIconColorFieldNumber = 3,
    kStatusTextFieldNumber = 4,
    kStatusColorFieldNumber = 5,
    kGroupIdFieldNumber = 6,
    kSectionIdFieldNumber = 7,
    kBottomTextFieldNumber = 10,
    kProgressFieldNumber = 8,
    kWidthFieldNumber = 9,
  };
  // string title = 1;
  void clear_title();
  const std::string& title() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_title(ArgT0&& arg0, ArgT... args);
  std::string* mutable_title();
  PROTOBUF_NODISCARD std::string* release_title();
  void set_allocated_title(std::string* title);
  private:
  const std::string& _internal_title() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_title(const std::string& value);
  std::string* _internal_mutable_title();
  public:

  // string icon_name = 2;
  void clear_icon_name();
  const std::string& icon_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_icon_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_icon_name();
  PROTOBUF_NODISCARD std::string* release_icon_name();
  void set_allocated_icon_name(std::string* icon_name);
  private:
  const std::string& _internal_icon_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_icon_name(const std::string& value);
  std::string* _internal_mutable_icon_name();
  public:

  // string icon_color = 3;
  void clear_icon_color();
  const std::string& icon_color() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_icon_color(ArgT0&& arg0, ArgT... args);
  std::string* mutable_icon_color();
  PROTOBUF_NODISCARD std::string* release_icon_color();
  void set_allocated_icon_color(std::string* icon_color);
  private:
  const std::string& _internal_icon_color() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_icon_color(const std::string& value);
  std::string* _internal_mutable_icon_color();
  public:

  // string status_text = 4;
  void clear_status_text();
  const std::string& status_text() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status_text(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status_text();
  PROTOBUF_NODISCARD std::string* release_status_text();
  void set_allocated_status_text(std::string* status_text);
  private:
  const std::string& _internal_status_text() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status_text(const std::string& value);
  std::string* _internal_mutable_status_text();
  public:

  // string status_color = 5;
  void clear_status_color();
  const std::string& status_color() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status_color(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status_color();
  PROTOBUF_NODISCARD std::string* release_status_color();
  void set_allocated_status_color(std::string* status_color);
  private:
  const std::string& _internal_status_color() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status_color(const std::string& value);
  std::string* _internal_mutable_status_color();
  public:

  // string group_id = 6;
  void clear_group_id();
  const std::string& group_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_group_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_group_id();
  PROTOBUF_NODISCARD std::string* release_group_id();
  void set_allocated_group_id(std::string* group_id);
  private:
  const std::string& _internal_group_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_group_id(const std::string& value);
  std::string* _internal_mutable_group_id();
  public:

  // string section_id = 7;
  void clear_section_id();
  const std::string& section_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_section_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_section_id();
  PROTOBUF_NODISCARD std::string* release_section_id();
  void set_allocated_section_id(std::string* section_id);
  private:
  const std::string& _internal_section_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_section_id(const std::string& value);
  std::string* _internal_mutable_section_id();
  public:

  // string bottom_text = 10;
  void clear_bottom_text();
  const std::string& bottom_text() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_bottom_text(ArgT0&& arg0, ArgT... args);
  std::string* mutable_bottom_text();
  PROTOBUF_NODISCARD std::string* release_bottom_text();
  void set_allocated_bottom_text(std::string* bottom_text);
  private:
  const std::string& _internal_bottom_text() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_bottom_text(const std::string& value);
  std::string* _internal_mutable_bottom_text();
  public:

  // double progress = 8;
  void clear_progress();
  double progress() const;
  void set_progress(double value);
  private:
  double _internal_progress() const;
  void _internal_set_progress(double value);
  public:

  // uint32 width = 9;
  void clear_width();
  uint32_t width() const;
  void set_width(uint32_t value);
  private:
  uint32_t _internal_width() const;
  void _internal_set_width(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.ExtraStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr title_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr icon_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr icon_color_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_text_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_color_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr group_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr section_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr bottom_text_;
  double progress_;
  uint32_t width_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class WeedTargeting final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.WeedTargeting) */ {
 public:
  inline WeedTargeting() : WeedTargeting(nullptr) {}
  ~WeedTargeting() override;
  explicit constexpr WeedTargeting(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WeedTargeting(const WeedTargeting& from);
  WeedTargeting(WeedTargeting&& from) noexcept
    : WeedTargeting() {
    *this = ::std::move(from);
  }

  inline WeedTargeting& operator=(const WeedTargeting& from) {
    CopyFrom(from);
    return *this;
  }
  inline WeedTargeting& operator=(WeedTargeting&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WeedTargeting& default_instance() {
    return *internal_default_instance();
  }
  static inline const WeedTargeting* internal_default_instance() {
    return reinterpret_cast<const WeedTargeting*>(
               &_WeedTargeting_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(WeedTargeting& a, WeedTargeting& b) {
    a.Swap(&b);
  }
  inline void Swap(WeedTargeting* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WeedTargeting* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WeedTargeting* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WeedTargeting>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WeedTargeting& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const WeedTargeting& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WeedTargeting* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.WeedTargeting";
  }
  protected:
  explicit WeedTargeting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.WeedTargeting)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class ThinningTargeting final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.ThinningTargeting) */ {
 public:
  inline ThinningTargeting() : ThinningTargeting(nullptr) {}
  ~ThinningTargeting() override;
  explicit constexpr ThinningTargeting(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ThinningTargeting(const ThinningTargeting& from);
  ThinningTargeting(ThinningTargeting&& from) noexcept
    : ThinningTargeting() {
    *this = ::std::move(from);
  }

  inline ThinningTargeting& operator=(const ThinningTargeting& from) {
    CopyFrom(from);
    return *this;
  }
  inline ThinningTargeting& operator=(ThinningTargeting&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ThinningTargeting& default_instance() {
    return *internal_default_instance();
  }
  static inline const ThinningTargeting* internal_default_instance() {
    return reinterpret_cast<const ThinningTargeting*>(
               &_ThinningTargeting_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ThinningTargeting& a, ThinningTargeting& b) {
    a.Swap(&b);
  }
  inline void Swap(ThinningTargeting* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ThinningTargeting* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ThinningTargeting* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ThinningTargeting>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ThinningTargeting& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ThinningTargeting& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ThinningTargeting* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.ThinningTargeting";
  }
  protected:
  explicit ThinningTargeting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlgorithmFieldNumber = 2,
    kEnabledFieldNumber = 1,
  };
  // uint64 algorithm = 2 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_algorithm();
  PROTOBUF_DEPRECATED uint64_t algorithm() const;
  PROTOBUF_DEPRECATED void set_algorithm(uint64_t value);
  private:
  uint64_t _internal_algorithm() const;
  void _internal_set_algorithm(uint64_t value);
  public:

  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.ThinningTargeting)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t algorithm_;
  bool enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class TargetingState_EnabledRowsEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TargetingState_EnabledRowsEntry_DoNotUse, 
    int32_t, bool,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<TargetingState_EnabledRowsEntry_DoNotUse, 
    int32_t, bool,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL> SuperType;
  TargetingState_EnabledRowsEntry_DoNotUse();
  explicit constexpr TargetingState_EnabledRowsEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit TargetingState_EnabledRowsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const TargetingState_EnabledRowsEntry_DoNotUse& other);
  static const TargetingState_EnabledRowsEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const TargetingState_EnabledRowsEntry_DoNotUse*>(&_TargetingState_EnabledRowsEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class TargetingState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.TargetingState) */ {
 public:
  inline TargetingState() : TargetingState(nullptr) {}
  ~TargetingState() override;
  explicit constexpr TargetingState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TargetingState(const TargetingState& from);
  TargetingState(TargetingState&& from) noexcept
    : TargetingState() {
    *this = ::std::move(from);
  }

  inline TargetingState& operator=(const TargetingState& from) {
    CopyFrom(from);
    return *this;
  }
  inline TargetingState& operator=(TargetingState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TargetingState& default_instance() {
    return *internal_default_instance();
  }
  static inline const TargetingState* internal_default_instance() {
    return reinterpret_cast<const TargetingState*>(
               &_TargetingState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TargetingState& a, TargetingState& b) {
    a.Swap(&b);
  }
  inline void Swap(TargetingState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TargetingState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TargetingState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TargetingState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TargetingState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TargetingState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TargetingState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.TargetingState";
  }
  protected:
  explicit TargetingState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 3,
    kEnabledRowsFieldNumber = 4,
    kWeedStateFieldNumber = 1,
    kThinningStateFieldNumber = 2,
  };
  // repeated bool enabled = 3 [deprecated = true];
  PROTOBUF_DEPRECATED int enabled_size() const;
  private:
  int _internal_enabled_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_enabled();
  private:
  bool _internal_enabled(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_enabled() const;
  void _internal_add_enabled(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_enabled();
  public:
  PROTOBUF_DEPRECATED bool enabled(int index) const;
  PROTOBUF_DEPRECATED void set_enabled(int index, bool value);
  PROTOBUF_DEPRECATED void add_enabled(bool value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      enabled() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_enabled();

  // map<int32, bool> enabled_rows = 4;
  int enabled_rows_size() const;
  private:
  int _internal_enabled_rows_size() const;
  public:
  void clear_enabled_rows();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >&
      _internal_enabled_rows() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >*
      _internal_mutable_enabled_rows();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >&
      enabled_rows() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >*
      mutable_enabled_rows();

  // .carbon.frontend.dashboard.WeedTargeting weed_state = 1;
  bool has_weed_state() const;
  private:
  bool _internal_has_weed_state() const;
  public:
  void clear_weed_state();
  const ::carbon::frontend::dashboard::WeedTargeting& weed_state() const;
  PROTOBUF_NODISCARD ::carbon::frontend::dashboard::WeedTargeting* release_weed_state();
  ::carbon::frontend::dashboard::WeedTargeting* mutable_weed_state();
  void set_allocated_weed_state(::carbon::frontend::dashboard::WeedTargeting* weed_state);
  private:
  const ::carbon::frontend::dashboard::WeedTargeting& _internal_weed_state() const;
  ::carbon::frontend::dashboard::WeedTargeting* _internal_mutable_weed_state();
  public:
  void unsafe_arena_set_allocated_weed_state(
      ::carbon::frontend::dashboard::WeedTargeting* weed_state);
  ::carbon::frontend::dashboard::WeedTargeting* unsafe_arena_release_weed_state();

  // .carbon.frontend.dashboard.ThinningTargeting thinning_state = 2;
  bool has_thinning_state() const;
  private:
  bool _internal_has_thinning_state() const;
  public:
  void clear_thinning_state();
  const ::carbon::frontend::dashboard::ThinningTargeting& thinning_state() const;
  PROTOBUF_NODISCARD ::carbon::frontend::dashboard::ThinningTargeting* release_thinning_state();
  ::carbon::frontend::dashboard::ThinningTargeting* mutable_thinning_state();
  void set_allocated_thinning_state(::carbon::frontend::dashboard::ThinningTargeting* thinning_state);
  private:
  const ::carbon::frontend::dashboard::ThinningTargeting& _internal_thinning_state() const;
  ::carbon::frontend::dashboard::ThinningTargeting* _internal_mutable_thinning_state();
  public:
  void unsafe_arena_set_allocated_thinning_state(
      ::carbon::frontend::dashboard::ThinningTargeting* thinning_state);
  ::carbon::frontend::dashboard::ThinningTargeting* unsafe_arena_release_thinning_state();

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.TargetingState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > enabled_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      TargetingState_EnabledRowsEntry_DoNotUse,
      int32_t, bool,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_BOOL> enabled_rows_;
  ::carbon::frontend::dashboard::WeedTargeting* weed_state_;
  ::carbon::frontend::dashboard::ThinningTargeting* thinning_state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class ExtraConclusion final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.ExtraConclusion) */ {
 public:
  inline ExtraConclusion() : ExtraConclusion(nullptr) {}
  ~ExtraConclusion() override;
  explicit constexpr ExtraConclusion(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ExtraConclusion(const ExtraConclusion& from);
  ExtraConclusion(ExtraConclusion&& from) noexcept
    : ExtraConclusion() {
    *this = ::std::move(from);
  }

  inline ExtraConclusion& operator=(const ExtraConclusion& from) {
    CopyFrom(from);
    return *this;
  }
  inline ExtraConclusion& operator=(ExtraConclusion&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ExtraConclusion& default_instance() {
    return *internal_default_instance();
  }
  static inline const ExtraConclusion* internal_default_instance() {
    return reinterpret_cast<const ExtraConclusion*>(
               &_ExtraConclusion_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(ExtraConclusion& a, ExtraConclusion& b) {
    a.Swap(&b);
  }
  inline void Swap(ExtraConclusion* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ExtraConclusion* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ExtraConclusion* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ExtraConclusion>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ExtraConclusion& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ExtraConclusion& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ExtraConclusion* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.ExtraConclusion";
  }
  protected:
  explicit ExtraConclusion(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTitleFieldNumber = 1,
    kPercentFieldNumber = 5,
    kFlipThresholdsFieldNumber = 2,
    kGoodThresholdPercentFieldNumber = 3,
    kMediumThresholdPercentFieldNumber = 4,
  };
  // string title = 1;
  void clear_title();
  const std::string& title() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_title(ArgT0&& arg0, ArgT... args);
  std::string* mutable_title();
  PROTOBUF_NODISCARD std::string* release_title();
  void set_allocated_title(std::string* title);
  private:
  const std::string& _internal_title() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_title(const std::string& value);
  std::string* _internal_mutable_title();
  public:

  // .carbon.frontend.translation.PercentValue percent = 5;
  bool has_percent() const;
  private:
  bool _internal_has_percent() const;
  public:
  void clear_percent();
  const ::carbon::frontend::translation::PercentValue& percent() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::PercentValue* release_percent();
  ::carbon::frontend::translation::PercentValue* mutable_percent();
  void set_allocated_percent(::carbon::frontend::translation::PercentValue* percent);
  private:
  const ::carbon::frontend::translation::PercentValue& _internal_percent() const;
  ::carbon::frontend::translation::PercentValue* _internal_mutable_percent();
  public:
  void unsafe_arena_set_allocated_percent(
      ::carbon::frontend::translation::PercentValue* percent);
  ::carbon::frontend::translation::PercentValue* unsafe_arena_release_percent();

  // bool flip_thresholds = 2;
  void clear_flip_thresholds();
  bool flip_thresholds() const;
  void set_flip_thresholds(bool value);
  private:
  bool _internal_flip_thresholds() const;
  void _internal_set_flip_thresholds(bool value);
  public:

  // uint32 good_threshold_percent = 3;
  void clear_good_threshold_percent();
  uint32_t good_threshold_percent() const;
  void set_good_threshold_percent(uint32_t value);
  private:
  uint32_t _internal_good_threshold_percent() const;
  void _internal_set_good_threshold_percent(uint32_t value);
  public:

  // uint32 medium_threshold_percent = 4;
  void clear_medium_threshold_percent();
  uint32_t medium_threshold_percent() const;
  void set_medium_threshold_percent(uint32_t value);
  private:
  uint32_t _internal_medium_threshold_percent() const;
  void _internal_set_medium_threshold_percent(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.ExtraConclusion)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr title_;
  ::carbon::frontend::translation::PercentValue* percent_;
  bool flip_thresholds_;
  uint32_t good_threshold_percent_;
  uint32_t medium_threshold_percent_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class RowStateMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.RowStateMessage) */ {
 public:
  inline RowStateMessage() : RowStateMessage(nullptr) {}
  ~RowStateMessage() override;
  explicit constexpr RowStateMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RowStateMessage(const RowStateMessage& from);
  RowStateMessage(RowStateMessage&& from) noexcept
    : RowStateMessage() {
    *this = ::std::move(from);
  }

  inline RowStateMessage& operator=(const RowStateMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline RowStateMessage& operator=(RowStateMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RowStateMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const RowStateMessage* internal_default_instance() {
    return reinterpret_cast<const RowStateMessage*>(
               &_RowStateMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(RowStateMessage& a, RowStateMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(RowStateMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RowStateMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RowStateMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RowStateMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RowStateMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RowStateMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RowStateMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.RowStateMessage";
  }
  protected:
  explicit RowStateMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
    kTargetStateMismatchFieldNumber = 2,
    kReadyFieldNumber = 3,
    kSafetyOverrideStateFieldNumber = 4,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // bool target_state_mismatch = 2;
  void clear_target_state_mismatch();
  bool target_state_mismatch() const;
  void set_target_state_mismatch(bool value);
  private:
  bool _internal_target_state_mismatch() const;
  void _internal_set_target_state_mismatch(bool value);
  public:

  // bool ready = 3;
  void clear_ready();
  bool ready() const;
  void set_ready(bool value);
  private:
  bool _internal_ready() const;
  void _internal_set_ready(bool value);
  public:

  // .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 4;
  void clear_safety_override_state();
  ::carbon::frontend::dashboard::SafetyOverrideState safety_override_state() const;
  void set_safety_override_state(::carbon::frontend::dashboard::SafetyOverrideState value);
  private:
  ::carbon::frontend::dashboard::SafetyOverrideState _internal_safety_override_state() const;
  void _internal_set_safety_override_state(::carbon::frontend::dashboard::SafetyOverrideState value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.RowStateMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enabled_;
  bool target_state_mismatch_;
  bool ready_;
  int safety_override_state_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class DashboardStateMessage_RowStatesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DashboardStateMessage_RowStatesEntry_DoNotUse, 
    int32_t, ::carbon::frontend::dashboard::RowStateMessage,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<DashboardStateMessage_RowStatesEntry_DoNotUse, 
    int32_t, ::carbon::frontend::dashboard::RowStateMessage,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  DashboardStateMessage_RowStatesEntry_DoNotUse();
  explicit constexpr DashboardStateMessage_RowStatesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit DashboardStateMessage_RowStatesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const DashboardStateMessage_RowStatesEntry_DoNotUse& other);
  static const DashboardStateMessage_RowStatesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const DashboardStateMessage_RowStatesEntry_DoNotUse*>(&_DashboardStateMessage_RowStatesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class DashboardStateMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.DashboardStateMessage) */ {
 public:
  inline DashboardStateMessage() : DashboardStateMessage(nullptr) {}
  ~DashboardStateMessage() override;
  explicit constexpr DashboardStateMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DashboardStateMessage(const DashboardStateMessage& from);
  DashboardStateMessage(DashboardStateMessage&& from) noexcept
    : DashboardStateMessage() {
    *this = ::std::move(from);
  }

  inline DashboardStateMessage& operator=(const DashboardStateMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline DashboardStateMessage& operator=(DashboardStateMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DashboardStateMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const DashboardStateMessage* internal_default_instance() {
    return reinterpret_cast<const DashboardStateMessage*>(
               &_DashboardStateMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DashboardStateMessage& a, DashboardStateMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(DashboardStateMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DashboardStateMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DashboardStateMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DashboardStateMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DashboardStateMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DashboardStateMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DashboardStateMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.DashboardStateMessage";
  }
  protected:
  explicit DashboardStateMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kRowEnabledFieldNumber = 3,
    kExtrasFieldNumber = 4,
    kTargetStateMismatchFieldNumber = 7,
    kRowReadyFieldNumber = 8,
    kRowExistsFieldNumber = 9,
    kSafetyOverrideStateFieldNumber = 11,
    kExtraConclusionsFieldNumber = 18,
    kRowStatesFieldNumber = 30,
    kCruiseDisallowedReasonFieldNumber = 32,
    kTsFieldNumber = 1,
    kSelectedModelFieldNumber = 5,
    kTargetingStateFieldNumber = 6,
    kEfficiencyPercentFieldNumber = 15,
    kErrorRateFieldNumber = 17,
    kAreaWeededTodayFieldNumber = 19,
    kAreaWeededTotalFieldNumber = 20,
    kWeedsKilledTodayFieldNumber = 21,
    kWeedsKilledTotalFieldNumber = 22,
    kTimeWeededTodayFieldNumber = 23,
    kTimeWeededTotalFieldNumber = 24,
    kCropsKilledTodayFieldNumber = 27,
    kCropsKilledTotalFieldNumber = 28,
    kRowWidthInFieldNumber = 10,
    kImplementStateFieldNumber = 13,
    kLasersEnabledFieldNumber = 2,
    kEfficiencyEnabledFieldNumber = 14,
    kErrorRateEnabledFieldNumber = 16,
    kWeedingEnabledFieldNumber = 25,
    kDebugModeFieldNumber = 26,
    kCruiseEnabledFieldNumber = 29,
    kCruiseAllowEnableFieldNumber = 31,
  };
  // repeated bool row_enabled = 3 [deprecated = true];
  PROTOBUF_DEPRECATED int row_enabled_size() const;
  private:
  int _internal_row_enabled_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_row_enabled();
  private:
  bool _internal_row_enabled(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_row_enabled() const;
  void _internal_add_row_enabled(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_row_enabled();
  public:
  PROTOBUF_DEPRECATED bool row_enabled(int index) const;
  PROTOBUF_DEPRECATED void set_row_enabled(int index, bool value);
  PROTOBUF_DEPRECATED void add_row_enabled(bool value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      row_enabled() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_row_enabled();

  // repeated .carbon.frontend.dashboard.ExtraStatus extras = 4;
  int extras_size() const;
  private:
  int _internal_extras_size() const;
  public:
  void clear_extras();
  ::carbon::frontend::dashboard::ExtraStatus* mutable_extras(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraStatus >*
      mutable_extras();
  private:
  const ::carbon::frontend::dashboard::ExtraStatus& _internal_extras(int index) const;
  ::carbon::frontend::dashboard::ExtraStatus* _internal_add_extras();
  public:
  const ::carbon::frontend::dashboard::ExtraStatus& extras(int index) const;
  ::carbon::frontend::dashboard::ExtraStatus* add_extras();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraStatus >&
      extras() const;

  // repeated bool target_state_mismatch = 7 [deprecated = true];
  PROTOBUF_DEPRECATED int target_state_mismatch_size() const;
  private:
  int _internal_target_state_mismatch_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_target_state_mismatch();
  private:
  bool _internal_target_state_mismatch(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_target_state_mismatch() const;
  void _internal_add_target_state_mismatch(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_target_state_mismatch();
  public:
  PROTOBUF_DEPRECATED bool target_state_mismatch(int index) const;
  PROTOBUF_DEPRECATED void set_target_state_mismatch(int index, bool value);
  PROTOBUF_DEPRECATED void add_target_state_mismatch(bool value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      target_state_mismatch() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_target_state_mismatch();

  // repeated bool row_ready = 8 [deprecated = true];
  PROTOBUF_DEPRECATED int row_ready_size() const;
  private:
  int _internal_row_ready_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_row_ready();
  private:
  bool _internal_row_ready(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_row_ready() const;
  void _internal_add_row_ready(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_row_ready();
  public:
  PROTOBUF_DEPRECATED bool row_ready(int index) const;
  PROTOBUF_DEPRECATED void set_row_ready(int index, bool value);
  PROTOBUF_DEPRECATED void add_row_ready(bool value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      row_ready() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_row_ready();

  // repeated bool row_exists = 9 [deprecated = true];
  PROTOBUF_DEPRECATED int row_exists_size() const;
  private:
  int _internal_row_exists_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_row_exists();
  private:
  bool _internal_row_exists(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      _internal_row_exists() const;
  void _internal_add_row_exists(bool value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      _internal_mutable_row_exists();
  public:
  PROTOBUF_DEPRECATED bool row_exists(int index) const;
  PROTOBUF_DEPRECATED void set_row_exists(int index, bool value);
  PROTOBUF_DEPRECATED void add_row_exists(bool value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
      row_exists() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
      mutable_row_exists();

  // repeated .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 11 [deprecated = true];
  PROTOBUF_DEPRECATED int safety_override_state_size() const;
  private:
  int _internal_safety_override_state_size() const;
  public:
  PROTOBUF_DEPRECATED void clear_safety_override_state();
  private:
  ::carbon::frontend::dashboard::SafetyOverrideState _internal_safety_override_state(int index) const;
  void _internal_add_safety_override_state(::carbon::frontend::dashboard::SafetyOverrideState value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* _internal_mutable_safety_override_state();
  public:
  PROTOBUF_DEPRECATED ::carbon::frontend::dashboard::SafetyOverrideState safety_override_state(int index) const;
  PROTOBUF_DEPRECATED void set_safety_override_state(int index, ::carbon::frontend::dashboard::SafetyOverrideState value);
  PROTOBUF_DEPRECATED void add_safety_override_state(::carbon::frontend::dashboard::SafetyOverrideState value);
  PROTOBUF_DEPRECATED const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>& safety_override_state() const;
  PROTOBUF_DEPRECATED ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>* mutable_safety_override_state();

  // repeated .carbon.frontend.dashboard.ExtraConclusion extra_conclusions = 18;
  int extra_conclusions_size() const;
  private:
  int _internal_extra_conclusions_size() const;
  public:
  void clear_extra_conclusions();
  ::carbon::frontend::dashboard::ExtraConclusion* mutable_extra_conclusions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraConclusion >*
      mutable_extra_conclusions();
  private:
  const ::carbon::frontend::dashboard::ExtraConclusion& _internal_extra_conclusions(int index) const;
  ::carbon::frontend::dashboard::ExtraConclusion* _internal_add_extra_conclusions();
  public:
  const ::carbon::frontend::dashboard::ExtraConclusion& extra_conclusions(int index) const;
  ::carbon::frontend::dashboard::ExtraConclusion* add_extra_conclusions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraConclusion >&
      extra_conclusions() const;

  // map<int32, .carbon.frontend.dashboard.RowStateMessage> row_states = 30;
  int row_states_size() const;
  private:
  int _internal_row_states_size() const;
  public:
  void clear_row_states();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >&
      _internal_row_states() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >*
      _internal_mutable_row_states();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >&
      row_states() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >*
      mutable_row_states();

  // string cruise_disallowed_reason = 32;
  void clear_cruise_disallowed_reason();
  const std::string& cruise_disallowed_reason() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cruise_disallowed_reason(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cruise_disallowed_reason();
  PROTOBUF_NODISCARD std::string* release_cruise_disallowed_reason();
  void set_allocated_cruise_disallowed_reason(std::string* cruise_disallowed_reason);
  private:
  const std::string& _internal_cruise_disallowed_reason() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cruise_disallowed_reason(const std::string& value);
  std::string* _internal_mutable_cruise_disallowed_reason();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.dashboard.CropModel selected_model = 5;
  bool has_selected_model() const;
  private:
  bool _internal_has_selected_model() const;
  public:
  void clear_selected_model();
  const ::carbon::frontend::dashboard::CropModel& selected_model() const;
  PROTOBUF_NODISCARD ::carbon::frontend::dashboard::CropModel* release_selected_model();
  ::carbon::frontend::dashboard::CropModel* mutable_selected_model();
  void set_allocated_selected_model(::carbon::frontend::dashboard::CropModel* selected_model);
  private:
  const ::carbon::frontend::dashboard::CropModel& _internal_selected_model() const;
  ::carbon::frontend::dashboard::CropModel* _internal_mutable_selected_model();
  public:
  void unsafe_arena_set_allocated_selected_model(
      ::carbon::frontend::dashboard::CropModel* selected_model);
  ::carbon::frontend::dashboard::CropModel* unsafe_arena_release_selected_model();

  // .carbon.frontend.dashboard.TargetingState targeting_state = 6;
  bool has_targeting_state() const;
  private:
  bool _internal_has_targeting_state() const;
  public:
  void clear_targeting_state();
  const ::carbon::frontend::dashboard::TargetingState& targeting_state() const;
  PROTOBUF_NODISCARD ::carbon::frontend::dashboard::TargetingState* release_targeting_state();
  ::carbon::frontend::dashboard::TargetingState* mutable_targeting_state();
  void set_allocated_targeting_state(::carbon::frontend::dashboard::TargetingState* targeting_state);
  private:
  const ::carbon::frontend::dashboard::TargetingState& _internal_targeting_state() const;
  ::carbon::frontend::dashboard::TargetingState* _internal_mutable_targeting_state();
  public:
  void unsafe_arena_set_allocated_targeting_state(
      ::carbon::frontend::dashboard::TargetingState* targeting_state);
  ::carbon::frontend::dashboard::TargetingState* unsafe_arena_release_targeting_state();

  // .carbon.frontend.translation.PercentValue efficiency_percent = 15;
  bool has_efficiency_percent() const;
  private:
  bool _internal_has_efficiency_percent() const;
  public:
  void clear_efficiency_percent();
  const ::carbon::frontend::translation::PercentValue& efficiency_percent() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::PercentValue* release_efficiency_percent();
  ::carbon::frontend::translation::PercentValue* mutable_efficiency_percent();
  void set_allocated_efficiency_percent(::carbon::frontend::translation::PercentValue* efficiency_percent);
  private:
  const ::carbon::frontend::translation::PercentValue& _internal_efficiency_percent() const;
  ::carbon::frontend::translation::PercentValue* _internal_mutable_efficiency_percent();
  public:
  void unsafe_arena_set_allocated_efficiency_percent(
      ::carbon::frontend::translation::PercentValue* efficiency_percent);
  ::carbon::frontend::translation::PercentValue* unsafe_arena_release_efficiency_percent();

  // .carbon.frontend.translation.PercentValue error_rate = 17;
  bool has_error_rate() const;
  private:
  bool _internal_has_error_rate() const;
  public:
  void clear_error_rate();
  const ::carbon::frontend::translation::PercentValue& error_rate() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::PercentValue* release_error_rate();
  ::carbon::frontend::translation::PercentValue* mutable_error_rate();
  void set_allocated_error_rate(::carbon::frontend::translation::PercentValue* error_rate);
  private:
  const ::carbon::frontend::translation::PercentValue& _internal_error_rate() const;
  ::carbon::frontend::translation::PercentValue* _internal_mutable_error_rate();
  public:
  void unsafe_arena_set_allocated_error_rate(
      ::carbon::frontend::translation::PercentValue* error_rate);
  ::carbon::frontend::translation::PercentValue* unsafe_arena_release_error_rate();

  // .carbon.frontend.translation.AreaValue area_weeded_today = 19;
  bool has_area_weeded_today() const;
  private:
  bool _internal_has_area_weeded_today() const;
  public:
  void clear_area_weeded_today();
  const ::carbon::frontend::translation::AreaValue& area_weeded_today() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::AreaValue* release_area_weeded_today();
  ::carbon::frontend::translation::AreaValue* mutable_area_weeded_today();
  void set_allocated_area_weeded_today(::carbon::frontend::translation::AreaValue* area_weeded_today);
  private:
  const ::carbon::frontend::translation::AreaValue& _internal_area_weeded_today() const;
  ::carbon::frontend::translation::AreaValue* _internal_mutable_area_weeded_today();
  public:
  void unsafe_arena_set_allocated_area_weeded_today(
      ::carbon::frontend::translation::AreaValue* area_weeded_today);
  ::carbon::frontend::translation::AreaValue* unsafe_arena_release_area_weeded_today();

  // .carbon.frontend.translation.AreaValue area_weeded_total = 20;
  bool has_area_weeded_total() const;
  private:
  bool _internal_has_area_weeded_total() const;
  public:
  void clear_area_weeded_total();
  const ::carbon::frontend::translation::AreaValue& area_weeded_total() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::AreaValue* release_area_weeded_total();
  ::carbon::frontend::translation::AreaValue* mutable_area_weeded_total();
  void set_allocated_area_weeded_total(::carbon::frontend::translation::AreaValue* area_weeded_total);
  private:
  const ::carbon::frontend::translation::AreaValue& _internal_area_weeded_total() const;
  ::carbon::frontend::translation::AreaValue* _internal_mutable_area_weeded_total();
  public:
  void unsafe_arena_set_allocated_area_weeded_total(
      ::carbon::frontend::translation::AreaValue* area_weeded_total);
  ::carbon::frontend::translation::AreaValue* unsafe_arena_release_area_weeded_total();

  // .carbon.frontend.translation.IntegerValue weeds_killed_today = 21;
  bool has_weeds_killed_today() const;
  private:
  bool _internal_has_weeds_killed_today() const;
  public:
  void clear_weeds_killed_today();
  const ::carbon::frontend::translation::IntegerValue& weeds_killed_today() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::IntegerValue* release_weeds_killed_today();
  ::carbon::frontend::translation::IntegerValue* mutable_weeds_killed_today();
  void set_allocated_weeds_killed_today(::carbon::frontend::translation::IntegerValue* weeds_killed_today);
  private:
  const ::carbon::frontend::translation::IntegerValue& _internal_weeds_killed_today() const;
  ::carbon::frontend::translation::IntegerValue* _internal_mutable_weeds_killed_today();
  public:
  void unsafe_arena_set_allocated_weeds_killed_today(
      ::carbon::frontend::translation::IntegerValue* weeds_killed_today);
  ::carbon::frontend::translation::IntegerValue* unsafe_arena_release_weeds_killed_today();

  // .carbon.frontend.translation.IntegerValue weeds_killed_total = 22;
  bool has_weeds_killed_total() const;
  private:
  bool _internal_has_weeds_killed_total() const;
  public:
  void clear_weeds_killed_total();
  const ::carbon::frontend::translation::IntegerValue& weeds_killed_total() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::IntegerValue* release_weeds_killed_total();
  ::carbon::frontend::translation::IntegerValue* mutable_weeds_killed_total();
  void set_allocated_weeds_killed_total(::carbon::frontend::translation::IntegerValue* weeds_killed_total);
  private:
  const ::carbon::frontend::translation::IntegerValue& _internal_weeds_killed_total() const;
  ::carbon::frontend::translation::IntegerValue* _internal_mutable_weeds_killed_total();
  public:
  void unsafe_arena_set_allocated_weeds_killed_total(
      ::carbon::frontend::translation::IntegerValue* weeds_killed_total);
  ::carbon::frontend::translation::IntegerValue* unsafe_arena_release_weeds_killed_total();

  // .carbon.frontend.translation.DurationValue time_weeded_today = 23;
  bool has_time_weeded_today() const;
  private:
  bool _internal_has_time_weeded_today() const;
  public:
  void clear_time_weeded_today();
  const ::carbon::frontend::translation::DurationValue& time_weeded_today() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::DurationValue* release_time_weeded_today();
  ::carbon::frontend::translation::DurationValue* mutable_time_weeded_today();
  void set_allocated_time_weeded_today(::carbon::frontend::translation::DurationValue* time_weeded_today);
  private:
  const ::carbon::frontend::translation::DurationValue& _internal_time_weeded_today() const;
  ::carbon::frontend::translation::DurationValue* _internal_mutable_time_weeded_today();
  public:
  void unsafe_arena_set_allocated_time_weeded_today(
      ::carbon::frontend::translation::DurationValue* time_weeded_today);
  ::carbon::frontend::translation::DurationValue* unsafe_arena_release_time_weeded_today();

  // .carbon.frontend.translation.DurationValue time_weeded_total = 24;
  bool has_time_weeded_total() const;
  private:
  bool _internal_has_time_weeded_total() const;
  public:
  void clear_time_weeded_total();
  const ::carbon::frontend::translation::DurationValue& time_weeded_total() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::DurationValue* release_time_weeded_total();
  ::carbon::frontend::translation::DurationValue* mutable_time_weeded_total();
  void set_allocated_time_weeded_total(::carbon::frontend::translation::DurationValue* time_weeded_total);
  private:
  const ::carbon::frontend::translation::DurationValue& _internal_time_weeded_total() const;
  ::carbon::frontend::translation::DurationValue* _internal_mutable_time_weeded_total();
  public:
  void unsafe_arena_set_allocated_time_weeded_total(
      ::carbon::frontend::translation::DurationValue* time_weeded_total);
  ::carbon::frontend::translation::DurationValue* unsafe_arena_release_time_weeded_total();

  // .carbon.frontend.translation.IntegerValue crops_killed_today = 27;
  bool has_crops_killed_today() const;
  private:
  bool _internal_has_crops_killed_today() const;
  public:
  void clear_crops_killed_today();
  const ::carbon::frontend::translation::IntegerValue& crops_killed_today() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::IntegerValue* release_crops_killed_today();
  ::carbon::frontend::translation::IntegerValue* mutable_crops_killed_today();
  void set_allocated_crops_killed_today(::carbon::frontend::translation::IntegerValue* crops_killed_today);
  private:
  const ::carbon::frontend::translation::IntegerValue& _internal_crops_killed_today() const;
  ::carbon::frontend::translation::IntegerValue* _internal_mutable_crops_killed_today();
  public:
  void unsafe_arena_set_allocated_crops_killed_today(
      ::carbon::frontend::translation::IntegerValue* crops_killed_today);
  ::carbon::frontend::translation::IntegerValue* unsafe_arena_release_crops_killed_today();

  // .carbon.frontend.translation.IntegerValue crops_killed_total = 28;
  bool has_crops_killed_total() const;
  private:
  bool _internal_has_crops_killed_total() const;
  public:
  void clear_crops_killed_total();
  const ::carbon::frontend::translation::IntegerValue& crops_killed_total() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::IntegerValue* release_crops_killed_total();
  ::carbon::frontend::translation::IntegerValue* mutable_crops_killed_total();
  void set_allocated_crops_killed_total(::carbon::frontend::translation::IntegerValue* crops_killed_total);
  private:
  const ::carbon::frontend::translation::IntegerValue& _internal_crops_killed_total() const;
  ::carbon::frontend::translation::IntegerValue* _internal_mutable_crops_killed_total();
  public:
  void unsafe_arena_set_allocated_crops_killed_total(
      ::carbon::frontend::translation::IntegerValue* crops_killed_total);
  ::carbon::frontend::translation::IntegerValue* unsafe_arena_release_crops_killed_total();

  // double row_width_in = 10;
  void clear_row_width_in();
  double row_width_in() const;
  void set_row_width_in(double value);
  private:
  double _internal_row_width_in() const;
  void _internal_set_row_width_in(double value);
  public:

  // .carbon.frontend.dashboard.ImplementState implement_state = 13;
  void clear_implement_state();
  ::carbon::frontend::dashboard::ImplementState implement_state() const;
  void set_implement_state(::carbon::frontend::dashboard::ImplementState value);
  private:
  ::carbon::frontend::dashboard::ImplementState _internal_implement_state() const;
  void _internal_set_implement_state(::carbon::frontend::dashboard::ImplementState value);
  public:

  // bool lasers_enabled = 2;
  void clear_lasers_enabled();
  bool lasers_enabled() const;
  void set_lasers_enabled(bool value);
  private:
  bool _internal_lasers_enabled() const;
  void _internal_set_lasers_enabled(bool value);
  public:

  // bool efficiency_enabled = 14;
  void clear_efficiency_enabled();
  bool efficiency_enabled() const;
  void set_efficiency_enabled(bool value);
  private:
  bool _internal_efficiency_enabled() const;
  void _internal_set_efficiency_enabled(bool value);
  public:

  // bool error_rate_enabled = 16;
  void clear_error_rate_enabled();
  bool error_rate_enabled() const;
  void set_error_rate_enabled(bool value);
  private:
  bool _internal_error_rate_enabled() const;
  void _internal_set_error_rate_enabled(bool value);
  public:

  // bool weeding_enabled = 25;
  void clear_weeding_enabled();
  bool weeding_enabled() const;
  void set_weeding_enabled(bool value);
  private:
  bool _internal_weeding_enabled() const;
  void _internal_set_weeding_enabled(bool value);
  public:

  // bool debug_mode = 26;
  void clear_debug_mode();
  bool debug_mode() const;
  void set_debug_mode(bool value);
  private:
  bool _internal_debug_mode() const;
  void _internal_set_debug_mode(bool value);
  public:

  // bool cruise_enabled = 29;
  void clear_cruise_enabled();
  bool cruise_enabled() const;
  void set_cruise_enabled(bool value);
  private:
  bool _internal_cruise_enabled() const;
  void _internal_set_cruise_enabled(bool value);
  public:

  // bool cruise_allow_enable = 31;
  void clear_cruise_allow_enable();
  bool cruise_allow_enable() const;
  void set_cruise_allow_enable(bool value);
  private:
  bool _internal_cruise_allow_enable() const;
  void _internal_set_cruise_allow_enable(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.DashboardStateMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > row_enabled_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraStatus > extras_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > target_state_mismatch_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > row_ready_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool > row_exists_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField<int> safety_override_state_;
  mutable std::atomic<int> _safety_override_state_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraConclusion > extra_conclusions_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      DashboardStateMessage_RowStatesEntry_DoNotUse,
      int32_t, ::carbon::frontend::dashboard::RowStateMessage,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> row_states_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cruise_disallowed_reason_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::dashboard::CropModel* selected_model_;
  ::carbon::frontend::dashboard::TargetingState* targeting_state_;
  ::carbon::frontend::translation::PercentValue* efficiency_percent_;
  ::carbon::frontend::translation::PercentValue* error_rate_;
  ::carbon::frontend::translation::AreaValue* area_weeded_today_;
  ::carbon::frontend::translation::AreaValue* area_weeded_total_;
  ::carbon::frontend::translation::IntegerValue* weeds_killed_today_;
  ::carbon::frontend::translation::IntegerValue* weeds_killed_total_;
  ::carbon::frontend::translation::DurationValue* time_weeded_today_;
  ::carbon::frontend::translation::DurationValue* time_weeded_total_;
  ::carbon::frontend::translation::IntegerValue* crops_killed_today_;
  ::carbon::frontend::translation::IntegerValue* crops_killed_total_;
  double row_width_in_;
  int implement_state_;
  bool lasers_enabled_;
  bool efficiency_enabled_;
  bool error_rate_enabled_;
  bool weeding_enabled_;
  bool debug_mode_;
  bool cruise_enabled_;
  bool cruise_allow_enable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class CropModel final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.CropModel) */ {
 public:
  inline CropModel() : CropModel(nullptr) {}
  ~CropModel() override;
  explicit constexpr CropModel(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CropModel(const CropModel& from);
  CropModel(CropModel&& from) noexcept
    : CropModel() {
    *this = ::std::move(from);
  }

  inline CropModel& operator=(const CropModel& from) {
    CopyFrom(from);
    return *this;
  }
  inline CropModel& operator=(CropModel&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CropModel& default_instance() {
    return *internal_default_instance();
  }
  static inline const CropModel* internal_default_instance() {
    return reinterpret_cast<const CropModel*>(
               &_CropModel_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(CropModel& a, CropModel& b) {
    a.Swap(&b);
  }
  inline void Swap(CropModel* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CropModel* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CropModel* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CropModel>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CropModel& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CropModel& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CropModel* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.CropModel";
  }
  protected:
  explicit CropModel(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropFieldNumber = 1,
    kCropIdFieldNumber = 4,
    kHasModelFieldNumber = 2,
    kPreferredFieldNumber = 3,
  };
  // string crop = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_crop();
  PROTOBUF_DEPRECATED const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_crop(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_crop();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_crop();
  PROTOBUF_DEPRECATED void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string crop_id = 4;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // bool has_model = 2;
  void clear_has_model();
  bool has_model() const;
  void set_has_model(bool value);
  private:
  bool _internal_has_model() const;
  void _internal_set_has_model(bool value);
  public:

  // bool preferred = 3;
  void clear_preferred();
  bool preferred() const;
  void set_preferred(bool value);
  private:
  bool _internal_preferred() const;
  void _internal_set_preferred(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.CropModel)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  bool has_model_;
  bool preferred_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class CropModelOptions final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.CropModelOptions) */ {
 public:
  inline CropModelOptions() : CropModelOptions(nullptr) {}
  ~CropModelOptions() override;
  explicit constexpr CropModelOptions(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CropModelOptions(const CropModelOptions& from);
  CropModelOptions(CropModelOptions&& from) noexcept
    : CropModelOptions() {
    *this = ::std::move(from);
  }

  inline CropModelOptions& operator=(const CropModelOptions& from) {
    CopyFrom(from);
    return *this;
  }
  inline CropModelOptions& operator=(CropModelOptions&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CropModelOptions& default_instance() {
    return *internal_default_instance();
  }
  static inline const CropModelOptions* internal_default_instance() {
    return reinterpret_cast<const CropModelOptions*>(
               &_CropModelOptions_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(CropModelOptions& a, CropModelOptions& b) {
    a.Swap(&b);
  }
  inline void Swap(CropModelOptions* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CropModelOptions* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CropModelOptions* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CropModelOptions>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CropModelOptions& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CropModelOptions& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CropModelOptions* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.CropModelOptions";
  }
  protected:
  explicit CropModelOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelsFieldNumber = 1,
  };
  // repeated .carbon.frontend.dashboard.CropModel models = 1;
  int models_size() const;
  private:
  int _internal_models_size() const;
  public:
  void clear_models();
  ::carbon::frontend::dashboard::CropModel* mutable_models(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::CropModel >*
      mutable_models();
  private:
  const ::carbon::frontend::dashboard::CropModel& _internal_models(int index) const;
  ::carbon::frontend::dashboard::CropModel* _internal_add_models();
  public:
  const ::carbon::frontend::dashboard::CropModel& models(int index) const;
  ::carbon::frontend::dashboard::CropModel* add_models();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::CropModel >&
      models() const;

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.CropModelOptions)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::CropModel > models_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class RowId final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.RowId) */ {
 public:
  inline RowId() : RowId(nullptr) {}
  ~RowId() override;
  explicit constexpr RowId(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RowId(const RowId& from);
  RowId(RowId&& from) noexcept
    : RowId() {
    *this = ::std::move(from);
  }

  inline RowId& operator=(const RowId& from) {
    CopyFrom(from);
    return *this;
  }
  inline RowId& operator=(RowId&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RowId& default_instance() {
    return *internal_default_instance();
  }
  static inline const RowId* internal_default_instance() {
    return reinterpret_cast<const RowId*>(
               &_RowId_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(RowId& a, RowId& b) {
    a.Swap(&b);
  }
  inline void Swap(RowId* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RowId* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RowId* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RowId>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RowId& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RowId& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RowId* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.RowId";
  }
  protected:
  explicit RowId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowNumberFieldNumber = 1,
  };
  // uint32 row_number = 1;
  void clear_row_number();
  uint32_t row_number() const;
  void set_row_number(uint32_t value);
  private:
  uint32_t _internal_row_number() const;
  void _internal_set_row_number(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.RowId)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t row_number_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class WeedingVelocity final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.WeedingVelocity) */ {
 public:
  inline WeedingVelocity() : WeedingVelocity(nullptr) {}
  ~WeedingVelocity() override;
  explicit constexpr WeedingVelocity(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  WeedingVelocity(const WeedingVelocity& from);
  WeedingVelocity(WeedingVelocity&& from) noexcept
    : WeedingVelocity() {
    *this = ::std::move(from);
  }

  inline WeedingVelocity& operator=(const WeedingVelocity& from) {
    CopyFrom(from);
    return *this;
  }
  inline WeedingVelocity& operator=(WeedingVelocity&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const WeedingVelocity& default_instance() {
    return *internal_default_instance();
  }
  static inline const WeedingVelocity* internal_default_instance() {
    return reinterpret_cast<const WeedingVelocity*>(
               &_WeedingVelocity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(WeedingVelocity& a, WeedingVelocity& b) {
    a.Swap(&b);
  }
  inline void Swap(WeedingVelocity* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(WeedingVelocity* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  WeedingVelocity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<WeedingVelocity>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const WeedingVelocity& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const WeedingVelocity& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(WeedingVelocity* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.WeedingVelocity";
  }
  protected:
  explicit WeedingVelocity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kCurrentVelocityMphFieldNumber = 2,
    kTargetVelocityMphFieldNumber = 3,
    kToleranceMphFieldNumber = 4,
    kPrimaryTargetVelocityTopMphFieldNumber = 5,
    kPrimaryTargetVelocityBottomMphFieldNumber = 6,
    kSecondaryTargetVelocityTopMphFieldNumber = 7,
    kSecondaryTargetVelocityBottomMphFieldNumber = 8,
    kCruiseControlVelocityMphFieldNumber = 9,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // double current_velocity_mph = 2;
  void clear_current_velocity_mph();
  double current_velocity_mph() const;
  void set_current_velocity_mph(double value);
  private:
  double _internal_current_velocity_mph() const;
  void _internal_set_current_velocity_mph(double value);
  public:

  // double target_velocity_mph = 3;
  void clear_target_velocity_mph();
  double target_velocity_mph() const;
  void set_target_velocity_mph(double value);
  private:
  double _internal_target_velocity_mph() const;
  void _internal_set_target_velocity_mph(double value);
  public:

  // double tolerance_mph = 4;
  void clear_tolerance_mph();
  double tolerance_mph() const;
  void set_tolerance_mph(double value);
  private:
  double _internal_tolerance_mph() const;
  void _internal_set_tolerance_mph(double value);
  public:

  // double primary_target_velocity_top_mph = 5;
  void clear_primary_target_velocity_top_mph();
  double primary_target_velocity_top_mph() const;
  void set_primary_target_velocity_top_mph(double value);
  private:
  double _internal_primary_target_velocity_top_mph() const;
  void _internal_set_primary_target_velocity_top_mph(double value);
  public:

  // double primary_target_velocity_bottom_mph = 6;
  void clear_primary_target_velocity_bottom_mph();
  double primary_target_velocity_bottom_mph() const;
  void set_primary_target_velocity_bottom_mph(double value);
  private:
  double _internal_primary_target_velocity_bottom_mph() const;
  void _internal_set_primary_target_velocity_bottom_mph(double value);
  public:

  // double secondary_target_velocity_top_mph = 7;
  void clear_secondary_target_velocity_top_mph();
  double secondary_target_velocity_top_mph() const;
  void set_secondary_target_velocity_top_mph(double value);
  private:
  double _internal_secondary_target_velocity_top_mph() const;
  void _internal_set_secondary_target_velocity_top_mph(double value);
  public:

  // double secondary_target_velocity_bottom_mph = 8;
  void clear_secondary_target_velocity_bottom_mph();
  double secondary_target_velocity_bottom_mph() const;
  void set_secondary_target_velocity_bottom_mph(double value);
  private:
  double _internal_secondary_target_velocity_bottom_mph() const;
  void _internal_set_secondary_target_velocity_bottom_mph(double value);
  public:

  // double cruise_control_velocity_mph = 9;
  void clear_cruise_control_velocity_mph();
  double cruise_control_velocity_mph() const;
  void set_cruise_control_velocity_mph(double value);
  private:
  double _internal_cruise_control_velocity_mph() const;
  void _internal_set_cruise_control_velocity_mph(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.WeedingVelocity)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  double current_velocity_mph_;
  double target_velocity_mph_;
  double tolerance_mph_;
  double primary_target_velocity_top_mph_;
  double primary_target_velocity_bottom_mph_;
  double secondary_target_velocity_top_mph_;
  double secondary_target_velocity_bottom_mph_;
  double cruise_control_velocity_mph_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class RowSpacing final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.RowSpacing) */ {
 public:
  inline RowSpacing() : RowSpacing(nullptr) {}
  ~RowSpacing() override;
  explicit constexpr RowSpacing(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RowSpacing(const RowSpacing& from);
  RowSpacing(RowSpacing&& from) noexcept
    : RowSpacing() {
    *this = ::std::move(from);
  }

  inline RowSpacing& operator=(const RowSpacing& from) {
    CopyFrom(from);
    return *this;
  }
  inline RowSpacing& operator=(RowSpacing&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RowSpacing& default_instance() {
    return *internal_default_instance();
  }
  static inline const RowSpacing* internal_default_instance() {
    return reinterpret_cast<const RowSpacing*>(
               &_RowSpacing_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(RowSpacing& a, RowSpacing& b) {
    a.Swap(&b);
  }
  inline void Swap(RowSpacing* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RowSpacing* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RowSpacing* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RowSpacing>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RowSpacing& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RowSpacing& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RowSpacing* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.RowSpacing";
  }
  protected:
  explicit RowSpacing(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWidthFieldNumber = 1,
  };
  // double width = 1;
  void clear_width();
  double width() const;
  void set_width(double value);
  private:
  double _internal_width() const;
  void _internal_set_width(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.RowSpacing)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double width_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// -------------------------------------------------------------------

class CruiseEnable final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.dashboard.CruiseEnable) */ {
 public:
  inline CruiseEnable() : CruiseEnable(nullptr) {}
  ~CruiseEnable() override;
  explicit constexpr CruiseEnable(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CruiseEnable(const CruiseEnable& from);
  CruiseEnable(CruiseEnable&& from) noexcept
    : CruiseEnable() {
    *this = ::std::move(from);
  }

  inline CruiseEnable& operator=(const CruiseEnable& from) {
    CopyFrom(from);
    return *this;
  }
  inline CruiseEnable& operator=(CruiseEnable&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CruiseEnable& default_instance() {
    return *internal_default_instance();
  }
  static inline const CruiseEnable* internal_default_instance() {
    return reinterpret_cast<const CruiseEnable*>(
               &_CruiseEnable_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(CruiseEnable& a, CruiseEnable& b) {
    a.Swap(&b);
  }
  inline void Swap(CruiseEnable* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CruiseEnable* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CruiseEnable* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CruiseEnable>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CruiseEnable& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CruiseEnable& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CruiseEnable* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.dashboard.CruiseEnable";
  }
  protected:
  explicit CruiseEnable(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kEnabledFieldNumber = 1,
  };
  // bool enabled = 1;
  void clear_enabled();
  bool enabled() const;
  void set_enabled(bool value);
  private:
  bool _internal_enabled() const;
  void _internal_set_enabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.CruiseEnable)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  bool enabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdashboard_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ExtraStatus

// string title = 1;
inline void ExtraStatus::clear_title() {
  title_.ClearToEmpty();
}
inline const std::string& ExtraStatus::title() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.title)
  return _internal_title();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraStatus::set_title(ArgT0&& arg0, ArgT... args) {
 
 title_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.title)
}
inline std::string* ExtraStatus::mutable_title() {
  std::string* _s = _internal_mutable_title();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraStatus.title)
  return _s;
}
inline const std::string& ExtraStatus::_internal_title() const {
  return title_.Get();
}
inline void ExtraStatus::_internal_set_title(const std::string& value) {
  
  title_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraStatus::_internal_mutable_title() {
  
  return title_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraStatus::release_title() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraStatus.title)
  return title_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraStatus::set_allocated_title(std::string* title) {
  if (title != nullptr) {
    
  } else {
    
  }
  title_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), title,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (title_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    title_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraStatus.title)
}

// string icon_name = 2;
inline void ExtraStatus::clear_icon_name() {
  icon_name_.ClearToEmpty();
}
inline const std::string& ExtraStatus::icon_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.icon_name)
  return _internal_icon_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraStatus::set_icon_name(ArgT0&& arg0, ArgT... args) {
 
 icon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.icon_name)
}
inline std::string* ExtraStatus::mutable_icon_name() {
  std::string* _s = _internal_mutable_icon_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraStatus.icon_name)
  return _s;
}
inline const std::string& ExtraStatus::_internal_icon_name() const {
  return icon_name_.Get();
}
inline void ExtraStatus::_internal_set_icon_name(const std::string& value) {
  
  icon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraStatus::_internal_mutable_icon_name() {
  
  return icon_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraStatus::release_icon_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraStatus.icon_name)
  return icon_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraStatus::set_allocated_icon_name(std::string* icon_name) {
  if (icon_name != nullptr) {
    
  } else {
    
  }
  icon_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), icon_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (icon_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    icon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraStatus.icon_name)
}

// string icon_color = 3;
inline void ExtraStatus::clear_icon_color() {
  icon_color_.ClearToEmpty();
}
inline const std::string& ExtraStatus::icon_color() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.icon_color)
  return _internal_icon_color();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraStatus::set_icon_color(ArgT0&& arg0, ArgT... args) {
 
 icon_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.icon_color)
}
inline std::string* ExtraStatus::mutable_icon_color() {
  std::string* _s = _internal_mutable_icon_color();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraStatus.icon_color)
  return _s;
}
inline const std::string& ExtraStatus::_internal_icon_color() const {
  return icon_color_.Get();
}
inline void ExtraStatus::_internal_set_icon_color(const std::string& value) {
  
  icon_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraStatus::_internal_mutable_icon_color() {
  
  return icon_color_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraStatus::release_icon_color() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraStatus.icon_color)
  return icon_color_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraStatus::set_allocated_icon_color(std::string* icon_color) {
  if (icon_color != nullptr) {
    
  } else {
    
  }
  icon_color_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), icon_color,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (icon_color_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    icon_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraStatus.icon_color)
}

// string status_text = 4;
inline void ExtraStatus::clear_status_text() {
  status_text_.ClearToEmpty();
}
inline const std::string& ExtraStatus::status_text() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.status_text)
  return _internal_status_text();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraStatus::set_status_text(ArgT0&& arg0, ArgT... args) {
 
 status_text_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.status_text)
}
inline std::string* ExtraStatus::mutable_status_text() {
  std::string* _s = _internal_mutable_status_text();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraStatus.status_text)
  return _s;
}
inline const std::string& ExtraStatus::_internal_status_text() const {
  return status_text_.Get();
}
inline void ExtraStatus::_internal_set_status_text(const std::string& value) {
  
  status_text_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraStatus::_internal_mutable_status_text() {
  
  return status_text_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraStatus::release_status_text() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraStatus.status_text)
  return status_text_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraStatus::set_allocated_status_text(std::string* status_text) {
  if (status_text != nullptr) {
    
  } else {
    
  }
  status_text_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), status_text,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (status_text_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    status_text_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraStatus.status_text)
}

// string status_color = 5;
inline void ExtraStatus::clear_status_color() {
  status_color_.ClearToEmpty();
}
inline const std::string& ExtraStatus::status_color() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.status_color)
  return _internal_status_color();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraStatus::set_status_color(ArgT0&& arg0, ArgT... args) {
 
 status_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.status_color)
}
inline std::string* ExtraStatus::mutable_status_color() {
  std::string* _s = _internal_mutable_status_color();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraStatus.status_color)
  return _s;
}
inline const std::string& ExtraStatus::_internal_status_color() const {
  return status_color_.Get();
}
inline void ExtraStatus::_internal_set_status_color(const std::string& value) {
  
  status_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraStatus::_internal_mutable_status_color() {
  
  return status_color_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraStatus::release_status_color() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraStatus.status_color)
  return status_color_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraStatus::set_allocated_status_color(std::string* status_color) {
  if (status_color != nullptr) {
    
  } else {
    
  }
  status_color_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), status_color,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (status_color_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    status_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraStatus.status_color)
}

// string group_id = 6;
inline void ExtraStatus::clear_group_id() {
  group_id_.ClearToEmpty();
}
inline const std::string& ExtraStatus::group_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.group_id)
  return _internal_group_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraStatus::set_group_id(ArgT0&& arg0, ArgT... args) {
 
 group_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.group_id)
}
inline std::string* ExtraStatus::mutable_group_id() {
  std::string* _s = _internal_mutable_group_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraStatus.group_id)
  return _s;
}
inline const std::string& ExtraStatus::_internal_group_id() const {
  return group_id_.Get();
}
inline void ExtraStatus::_internal_set_group_id(const std::string& value) {
  
  group_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraStatus::_internal_mutable_group_id() {
  
  return group_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraStatus::release_group_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraStatus.group_id)
  return group_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraStatus::set_allocated_group_id(std::string* group_id) {
  if (group_id != nullptr) {
    
  } else {
    
  }
  group_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), group_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (group_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    group_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraStatus.group_id)
}

// string section_id = 7;
inline void ExtraStatus::clear_section_id() {
  section_id_.ClearToEmpty();
}
inline const std::string& ExtraStatus::section_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.section_id)
  return _internal_section_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraStatus::set_section_id(ArgT0&& arg0, ArgT... args) {
 
 section_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.section_id)
}
inline std::string* ExtraStatus::mutable_section_id() {
  std::string* _s = _internal_mutable_section_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraStatus.section_id)
  return _s;
}
inline const std::string& ExtraStatus::_internal_section_id() const {
  return section_id_.Get();
}
inline void ExtraStatus::_internal_set_section_id(const std::string& value) {
  
  section_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraStatus::_internal_mutable_section_id() {
  
  return section_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraStatus::release_section_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraStatus.section_id)
  return section_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraStatus::set_allocated_section_id(std::string* section_id) {
  if (section_id != nullptr) {
    
  } else {
    
  }
  section_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), section_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (section_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    section_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraStatus.section_id)
}

// double progress = 8;
inline void ExtraStatus::clear_progress() {
  progress_ = 0;
}
inline double ExtraStatus::_internal_progress() const {
  return progress_;
}
inline double ExtraStatus::progress() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.progress)
  return _internal_progress();
}
inline void ExtraStatus::_internal_set_progress(double value) {
  
  progress_ = value;
}
inline void ExtraStatus::set_progress(double value) {
  _internal_set_progress(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.progress)
}

// uint32 width = 9;
inline void ExtraStatus::clear_width() {
  width_ = 0u;
}
inline uint32_t ExtraStatus::_internal_width() const {
  return width_;
}
inline uint32_t ExtraStatus::width() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.width)
  return _internal_width();
}
inline void ExtraStatus::_internal_set_width(uint32_t value) {
  
  width_ = value;
}
inline void ExtraStatus::set_width(uint32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.width)
}

// string bottom_text = 10;
inline void ExtraStatus::clear_bottom_text() {
  bottom_text_.ClearToEmpty();
}
inline const std::string& ExtraStatus::bottom_text() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraStatus.bottom_text)
  return _internal_bottom_text();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraStatus::set_bottom_text(ArgT0&& arg0, ArgT... args) {
 
 bottom_text_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraStatus.bottom_text)
}
inline std::string* ExtraStatus::mutable_bottom_text() {
  std::string* _s = _internal_mutable_bottom_text();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraStatus.bottom_text)
  return _s;
}
inline const std::string& ExtraStatus::_internal_bottom_text() const {
  return bottom_text_.Get();
}
inline void ExtraStatus::_internal_set_bottom_text(const std::string& value) {
  
  bottom_text_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraStatus::_internal_mutable_bottom_text() {
  
  return bottom_text_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraStatus::release_bottom_text() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraStatus.bottom_text)
  return bottom_text_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraStatus::set_allocated_bottom_text(std::string* bottom_text) {
  if (bottom_text != nullptr) {
    
  } else {
    
  }
  bottom_text_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), bottom_text,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (bottom_text_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    bottom_text_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraStatus.bottom_text)
}

// -------------------------------------------------------------------

// WeedTargeting

// bool enabled = 1;
inline void WeedTargeting::clear_enabled() {
  enabled_ = false;
}
inline bool WeedTargeting::_internal_enabled() const {
  return enabled_;
}
inline bool WeedTargeting::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedTargeting.enabled)
  return _internal_enabled();
}
inline void WeedTargeting::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void WeedTargeting::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedTargeting.enabled)
}

// -------------------------------------------------------------------

// ThinningTargeting

// bool enabled = 1;
inline void ThinningTargeting::clear_enabled() {
  enabled_ = false;
}
inline bool ThinningTargeting::_internal_enabled() const {
  return enabled_;
}
inline bool ThinningTargeting::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ThinningTargeting.enabled)
  return _internal_enabled();
}
inline void ThinningTargeting::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void ThinningTargeting::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ThinningTargeting.enabled)
}

// uint64 algorithm = 2 [deprecated = true];
inline void ThinningTargeting::clear_algorithm() {
  algorithm_ = uint64_t{0u};
}
inline uint64_t ThinningTargeting::_internal_algorithm() const {
  return algorithm_;
}
inline uint64_t ThinningTargeting::algorithm() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ThinningTargeting.algorithm)
  return _internal_algorithm();
}
inline void ThinningTargeting::_internal_set_algorithm(uint64_t value) {
  
  algorithm_ = value;
}
inline void ThinningTargeting::set_algorithm(uint64_t value) {
  _internal_set_algorithm(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ThinningTargeting.algorithm)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// TargetingState

// .carbon.frontend.dashboard.WeedTargeting weed_state = 1;
inline bool TargetingState::_internal_has_weed_state() const {
  return this != internal_default_instance() && weed_state_ != nullptr;
}
inline bool TargetingState::has_weed_state() const {
  return _internal_has_weed_state();
}
inline void TargetingState::clear_weed_state() {
  if (GetArenaForAllocation() == nullptr && weed_state_ != nullptr) {
    delete weed_state_;
  }
  weed_state_ = nullptr;
}
inline const ::carbon::frontend::dashboard::WeedTargeting& TargetingState::_internal_weed_state() const {
  const ::carbon::frontend::dashboard::WeedTargeting* p = weed_state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::dashboard::WeedTargeting&>(
      ::carbon::frontend::dashboard::_WeedTargeting_default_instance_);
}
inline const ::carbon::frontend::dashboard::WeedTargeting& TargetingState::weed_state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.TargetingState.weed_state)
  return _internal_weed_state();
}
inline void TargetingState::unsafe_arena_set_allocated_weed_state(
    ::carbon::frontend::dashboard::WeedTargeting* weed_state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weed_state_);
  }
  weed_state_ = weed_state;
  if (weed_state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.TargetingState.weed_state)
}
inline ::carbon::frontend::dashboard::WeedTargeting* TargetingState::release_weed_state() {
  
  ::carbon::frontend::dashboard::WeedTargeting* temp = weed_state_;
  weed_state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::dashboard::WeedTargeting* TargetingState::unsafe_arena_release_weed_state() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.TargetingState.weed_state)
  
  ::carbon::frontend::dashboard::WeedTargeting* temp = weed_state_;
  weed_state_ = nullptr;
  return temp;
}
inline ::carbon::frontend::dashboard::WeedTargeting* TargetingState::_internal_mutable_weed_state() {
  
  if (weed_state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::dashboard::WeedTargeting>(GetArenaForAllocation());
    weed_state_ = p;
  }
  return weed_state_;
}
inline ::carbon::frontend::dashboard::WeedTargeting* TargetingState::mutable_weed_state() {
  ::carbon::frontend::dashboard::WeedTargeting* _msg = _internal_mutable_weed_state();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.TargetingState.weed_state)
  return _msg;
}
inline void TargetingState::set_allocated_weed_state(::carbon::frontend::dashboard::WeedTargeting* weed_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete weed_state_;
  }
  if (weed_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::dashboard::WeedTargeting>::GetOwningArena(weed_state);
    if (message_arena != submessage_arena) {
      weed_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, weed_state, submessage_arena);
    }
    
  } else {
    
  }
  weed_state_ = weed_state;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.TargetingState.weed_state)
}

// .carbon.frontend.dashboard.ThinningTargeting thinning_state = 2;
inline bool TargetingState::_internal_has_thinning_state() const {
  return this != internal_default_instance() && thinning_state_ != nullptr;
}
inline bool TargetingState::has_thinning_state() const {
  return _internal_has_thinning_state();
}
inline void TargetingState::clear_thinning_state() {
  if (GetArenaForAllocation() == nullptr && thinning_state_ != nullptr) {
    delete thinning_state_;
  }
  thinning_state_ = nullptr;
}
inline const ::carbon::frontend::dashboard::ThinningTargeting& TargetingState::_internal_thinning_state() const {
  const ::carbon::frontend::dashboard::ThinningTargeting* p = thinning_state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::dashboard::ThinningTargeting&>(
      ::carbon::frontend::dashboard::_ThinningTargeting_default_instance_);
}
inline const ::carbon::frontend::dashboard::ThinningTargeting& TargetingState::thinning_state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.TargetingState.thinning_state)
  return _internal_thinning_state();
}
inline void TargetingState::unsafe_arena_set_allocated_thinning_state(
    ::carbon::frontend::dashboard::ThinningTargeting* thinning_state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(thinning_state_);
  }
  thinning_state_ = thinning_state;
  if (thinning_state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.TargetingState.thinning_state)
}
inline ::carbon::frontend::dashboard::ThinningTargeting* TargetingState::release_thinning_state() {
  
  ::carbon::frontend::dashboard::ThinningTargeting* temp = thinning_state_;
  thinning_state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::dashboard::ThinningTargeting* TargetingState::unsafe_arena_release_thinning_state() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.TargetingState.thinning_state)
  
  ::carbon::frontend::dashboard::ThinningTargeting* temp = thinning_state_;
  thinning_state_ = nullptr;
  return temp;
}
inline ::carbon::frontend::dashboard::ThinningTargeting* TargetingState::_internal_mutable_thinning_state() {
  
  if (thinning_state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::dashboard::ThinningTargeting>(GetArenaForAllocation());
    thinning_state_ = p;
  }
  return thinning_state_;
}
inline ::carbon::frontend::dashboard::ThinningTargeting* TargetingState::mutable_thinning_state() {
  ::carbon::frontend::dashboard::ThinningTargeting* _msg = _internal_mutable_thinning_state();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.TargetingState.thinning_state)
  return _msg;
}
inline void TargetingState::set_allocated_thinning_state(::carbon::frontend::dashboard::ThinningTargeting* thinning_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete thinning_state_;
  }
  if (thinning_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::dashboard::ThinningTargeting>::GetOwningArena(thinning_state);
    if (message_arena != submessage_arena) {
      thinning_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, thinning_state, submessage_arena);
    }
    
  } else {
    
  }
  thinning_state_ = thinning_state;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.TargetingState.thinning_state)
}

// repeated bool enabled = 3 [deprecated = true];
inline int TargetingState::_internal_enabled_size() const {
  return enabled_.size();
}
inline int TargetingState::enabled_size() const {
  return _internal_enabled_size();
}
inline void TargetingState::clear_enabled() {
  enabled_.Clear();
}
inline bool TargetingState::_internal_enabled(int index) const {
  return enabled_.Get(index);
}
inline bool TargetingState::enabled(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.TargetingState.enabled)
  return _internal_enabled(index);
}
inline void TargetingState::set_enabled(int index, bool value) {
  enabled_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.TargetingState.enabled)
}
inline void TargetingState::_internal_add_enabled(bool value) {
  enabled_.Add(value);
}
inline void TargetingState::add_enabled(bool value) {
  _internal_add_enabled(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.TargetingState.enabled)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
TargetingState::_internal_enabled() const {
  return enabled_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
TargetingState::enabled() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.TargetingState.enabled)
  return _internal_enabled();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
TargetingState::_internal_mutable_enabled() {
  return &enabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
TargetingState::mutable_enabled() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.TargetingState.enabled)
  return _internal_mutable_enabled();
}

// map<int32, bool> enabled_rows = 4;
inline int TargetingState::_internal_enabled_rows_size() const {
  return enabled_rows_.size();
}
inline int TargetingState::enabled_rows_size() const {
  return _internal_enabled_rows_size();
}
inline void TargetingState::clear_enabled_rows() {
  enabled_rows_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >&
TargetingState::_internal_enabled_rows() const {
  return enabled_rows_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >&
TargetingState::enabled_rows() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.dashboard.TargetingState.enabled_rows)
  return _internal_enabled_rows();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >*
TargetingState::_internal_mutable_enabled_rows() {
  return enabled_rows_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >*
TargetingState::mutable_enabled_rows() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.dashboard.TargetingState.enabled_rows)
  return _internal_mutable_enabled_rows();
}

// -------------------------------------------------------------------

// ExtraConclusion

// string title = 1;
inline void ExtraConclusion::clear_title() {
  title_.ClearToEmpty();
}
inline const std::string& ExtraConclusion::title() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraConclusion.title)
  return _internal_title();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ExtraConclusion::set_title(ArgT0&& arg0, ArgT... args) {
 
 title_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraConclusion.title)
}
inline std::string* ExtraConclusion::mutable_title() {
  std::string* _s = _internal_mutable_title();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraConclusion.title)
  return _s;
}
inline const std::string& ExtraConclusion::_internal_title() const {
  return title_.Get();
}
inline void ExtraConclusion::_internal_set_title(const std::string& value) {
  
  title_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ExtraConclusion::_internal_mutable_title() {
  
  return title_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ExtraConclusion::release_title() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraConclusion.title)
  return title_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ExtraConclusion::set_allocated_title(std::string* title) {
  if (title != nullptr) {
    
  } else {
    
  }
  title_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), title,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (title_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    title_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraConclusion.title)
}

// bool flip_thresholds = 2;
inline void ExtraConclusion::clear_flip_thresholds() {
  flip_thresholds_ = false;
}
inline bool ExtraConclusion::_internal_flip_thresholds() const {
  return flip_thresholds_;
}
inline bool ExtraConclusion::flip_thresholds() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraConclusion.flip_thresholds)
  return _internal_flip_thresholds();
}
inline void ExtraConclusion::_internal_set_flip_thresholds(bool value) {
  
  flip_thresholds_ = value;
}
inline void ExtraConclusion::set_flip_thresholds(bool value) {
  _internal_set_flip_thresholds(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraConclusion.flip_thresholds)
}

// uint32 good_threshold_percent = 3;
inline void ExtraConclusion::clear_good_threshold_percent() {
  good_threshold_percent_ = 0u;
}
inline uint32_t ExtraConclusion::_internal_good_threshold_percent() const {
  return good_threshold_percent_;
}
inline uint32_t ExtraConclusion::good_threshold_percent() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraConclusion.good_threshold_percent)
  return _internal_good_threshold_percent();
}
inline void ExtraConclusion::_internal_set_good_threshold_percent(uint32_t value) {
  
  good_threshold_percent_ = value;
}
inline void ExtraConclusion::set_good_threshold_percent(uint32_t value) {
  _internal_set_good_threshold_percent(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraConclusion.good_threshold_percent)
}

// uint32 medium_threshold_percent = 4;
inline void ExtraConclusion::clear_medium_threshold_percent() {
  medium_threshold_percent_ = 0u;
}
inline uint32_t ExtraConclusion::_internal_medium_threshold_percent() const {
  return medium_threshold_percent_;
}
inline uint32_t ExtraConclusion::medium_threshold_percent() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraConclusion.medium_threshold_percent)
  return _internal_medium_threshold_percent();
}
inline void ExtraConclusion::_internal_set_medium_threshold_percent(uint32_t value) {
  
  medium_threshold_percent_ = value;
}
inline void ExtraConclusion::set_medium_threshold_percent(uint32_t value) {
  _internal_set_medium_threshold_percent(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.ExtraConclusion.medium_threshold_percent)
}

// .carbon.frontend.translation.PercentValue percent = 5;
inline bool ExtraConclusion::_internal_has_percent() const {
  return this != internal_default_instance() && percent_ != nullptr;
}
inline bool ExtraConclusion::has_percent() const {
  return _internal_has_percent();
}
inline const ::carbon::frontend::translation::PercentValue& ExtraConclusion::_internal_percent() const {
  const ::carbon::frontend::translation::PercentValue* p = percent_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::PercentValue&>(
      ::carbon::frontend::translation::_PercentValue_default_instance_);
}
inline const ::carbon::frontend::translation::PercentValue& ExtraConclusion::percent() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.ExtraConclusion.percent)
  return _internal_percent();
}
inline void ExtraConclusion::unsafe_arena_set_allocated_percent(
    ::carbon::frontend::translation::PercentValue* percent) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(percent_);
  }
  percent_ = percent;
  if (percent) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.ExtraConclusion.percent)
}
inline ::carbon::frontend::translation::PercentValue* ExtraConclusion::release_percent() {
  
  ::carbon::frontend::translation::PercentValue* temp = percent_;
  percent_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::PercentValue* ExtraConclusion::unsafe_arena_release_percent() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.ExtraConclusion.percent)
  
  ::carbon::frontend::translation::PercentValue* temp = percent_;
  percent_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::PercentValue* ExtraConclusion::_internal_mutable_percent() {
  
  if (percent_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::PercentValue>(GetArenaForAllocation());
    percent_ = p;
  }
  return percent_;
}
inline ::carbon::frontend::translation::PercentValue* ExtraConclusion::mutable_percent() {
  ::carbon::frontend::translation::PercentValue* _msg = _internal_mutable_percent();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.ExtraConclusion.percent)
  return _msg;
}
inline void ExtraConclusion::set_allocated_percent(::carbon::frontend::translation::PercentValue* percent) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(percent_);
  }
  if (percent) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(percent));
    if (message_arena != submessage_arena) {
      percent = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, percent, submessage_arena);
    }
    
  } else {
    
  }
  percent_ = percent;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.ExtraConclusion.percent)
}

// -------------------------------------------------------------------

// RowStateMessage

// bool enabled = 1;
inline void RowStateMessage::clear_enabled() {
  enabled_ = false;
}
inline bool RowStateMessage::_internal_enabled() const {
  return enabled_;
}
inline bool RowStateMessage::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.RowStateMessage.enabled)
  return _internal_enabled();
}
inline void RowStateMessage::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void RowStateMessage::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.RowStateMessage.enabled)
}

// bool target_state_mismatch = 2;
inline void RowStateMessage::clear_target_state_mismatch() {
  target_state_mismatch_ = false;
}
inline bool RowStateMessage::_internal_target_state_mismatch() const {
  return target_state_mismatch_;
}
inline bool RowStateMessage::target_state_mismatch() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.RowStateMessage.target_state_mismatch)
  return _internal_target_state_mismatch();
}
inline void RowStateMessage::_internal_set_target_state_mismatch(bool value) {
  
  target_state_mismatch_ = value;
}
inline void RowStateMessage::set_target_state_mismatch(bool value) {
  _internal_set_target_state_mismatch(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.RowStateMessage.target_state_mismatch)
}

// bool ready = 3;
inline void RowStateMessage::clear_ready() {
  ready_ = false;
}
inline bool RowStateMessage::_internal_ready() const {
  return ready_;
}
inline bool RowStateMessage::ready() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.RowStateMessage.ready)
  return _internal_ready();
}
inline void RowStateMessage::_internal_set_ready(bool value) {
  
  ready_ = value;
}
inline void RowStateMessage::set_ready(bool value) {
  _internal_set_ready(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.RowStateMessage.ready)
}

// .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 4;
inline void RowStateMessage::clear_safety_override_state() {
  safety_override_state_ = 0;
}
inline ::carbon::frontend::dashboard::SafetyOverrideState RowStateMessage::_internal_safety_override_state() const {
  return static_cast< ::carbon::frontend::dashboard::SafetyOverrideState >(safety_override_state_);
}
inline ::carbon::frontend::dashboard::SafetyOverrideState RowStateMessage::safety_override_state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.RowStateMessage.safety_override_state)
  return _internal_safety_override_state();
}
inline void RowStateMessage::_internal_set_safety_override_state(::carbon::frontend::dashboard::SafetyOverrideState value) {
  
  safety_override_state_ = value;
}
inline void RowStateMessage::set_safety_override_state(::carbon::frontend::dashboard::SafetyOverrideState value) {
  _internal_set_safety_override_state(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.RowStateMessage.safety_override_state)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// DashboardStateMessage

// .carbon.frontend.util.Timestamp ts = 1;
inline bool DashboardStateMessage::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool DashboardStateMessage::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& DashboardStateMessage::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& DashboardStateMessage::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.ts)
  return _internal_ts();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.ts)
}
inline ::carbon::frontend::util::Timestamp* DashboardStateMessage::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* DashboardStateMessage::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* DashboardStateMessage::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* DashboardStateMessage::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.ts)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.ts)
}

// bool lasers_enabled = 2;
inline void DashboardStateMessage::clear_lasers_enabled() {
  lasers_enabled_ = false;
}
inline bool DashboardStateMessage::_internal_lasers_enabled() const {
  return lasers_enabled_;
}
inline bool DashboardStateMessage::lasers_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.lasers_enabled)
  return _internal_lasers_enabled();
}
inline void DashboardStateMessage::_internal_set_lasers_enabled(bool value) {
  
  lasers_enabled_ = value;
}
inline void DashboardStateMessage::set_lasers_enabled(bool value) {
  _internal_set_lasers_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.lasers_enabled)
}

// repeated bool row_enabled = 3 [deprecated = true];
inline int DashboardStateMessage::_internal_row_enabled_size() const {
  return row_enabled_.size();
}
inline int DashboardStateMessage::row_enabled_size() const {
  return _internal_row_enabled_size();
}
inline void DashboardStateMessage::clear_row_enabled() {
  row_enabled_.Clear();
}
inline bool DashboardStateMessage::_internal_row_enabled(int index) const {
  return row_enabled_.Get(index);
}
inline bool DashboardStateMessage::row_enabled(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.row_enabled)
  return _internal_row_enabled(index);
}
inline void DashboardStateMessage::set_row_enabled(int index, bool value) {
  row_enabled_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.row_enabled)
}
inline void DashboardStateMessage::_internal_add_row_enabled(bool value) {
  row_enabled_.Add(value);
}
inline void DashboardStateMessage::add_row_enabled(bool value) {
  _internal_add_row_enabled(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.DashboardStateMessage.row_enabled)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
DashboardStateMessage::_internal_row_enabled() const {
  return row_enabled_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
DashboardStateMessage::row_enabled() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.DashboardStateMessage.row_enabled)
  return _internal_row_enabled();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
DashboardStateMessage::_internal_mutable_row_enabled() {
  return &row_enabled_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
DashboardStateMessage::mutable_row_enabled() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.DashboardStateMessage.row_enabled)
  return _internal_mutable_row_enabled();
}

// repeated .carbon.frontend.dashboard.ExtraStatus extras = 4;
inline int DashboardStateMessage::_internal_extras_size() const {
  return extras_.size();
}
inline int DashboardStateMessage::extras_size() const {
  return _internal_extras_size();
}
inline void DashboardStateMessage::clear_extras() {
  extras_.Clear();
}
inline ::carbon::frontend::dashboard::ExtraStatus* DashboardStateMessage::mutable_extras(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.extras)
  return extras_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraStatus >*
DashboardStateMessage::mutable_extras() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.DashboardStateMessage.extras)
  return &extras_;
}
inline const ::carbon::frontend::dashboard::ExtraStatus& DashboardStateMessage::_internal_extras(int index) const {
  return extras_.Get(index);
}
inline const ::carbon::frontend::dashboard::ExtraStatus& DashboardStateMessage::extras(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.extras)
  return _internal_extras(index);
}
inline ::carbon::frontend::dashboard::ExtraStatus* DashboardStateMessage::_internal_add_extras() {
  return extras_.Add();
}
inline ::carbon::frontend::dashboard::ExtraStatus* DashboardStateMessage::add_extras() {
  ::carbon::frontend::dashboard::ExtraStatus* _add = _internal_add_extras();
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.DashboardStateMessage.extras)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraStatus >&
DashboardStateMessage::extras() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.DashboardStateMessage.extras)
  return extras_;
}

// .carbon.frontend.dashboard.CropModel selected_model = 5;
inline bool DashboardStateMessage::_internal_has_selected_model() const {
  return this != internal_default_instance() && selected_model_ != nullptr;
}
inline bool DashboardStateMessage::has_selected_model() const {
  return _internal_has_selected_model();
}
inline void DashboardStateMessage::clear_selected_model() {
  if (GetArenaForAllocation() == nullptr && selected_model_ != nullptr) {
    delete selected_model_;
  }
  selected_model_ = nullptr;
}
inline const ::carbon::frontend::dashboard::CropModel& DashboardStateMessage::_internal_selected_model() const {
  const ::carbon::frontend::dashboard::CropModel* p = selected_model_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::dashboard::CropModel&>(
      ::carbon::frontend::dashboard::_CropModel_default_instance_);
}
inline const ::carbon::frontend::dashboard::CropModel& DashboardStateMessage::selected_model() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.selected_model)
  return _internal_selected_model();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_selected_model(
    ::carbon::frontend::dashboard::CropModel* selected_model) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(selected_model_);
  }
  selected_model_ = selected_model;
  if (selected_model) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.selected_model)
}
inline ::carbon::frontend::dashboard::CropModel* DashboardStateMessage::release_selected_model() {
  
  ::carbon::frontend::dashboard::CropModel* temp = selected_model_;
  selected_model_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::dashboard::CropModel* DashboardStateMessage::unsafe_arena_release_selected_model() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.selected_model)
  
  ::carbon::frontend::dashboard::CropModel* temp = selected_model_;
  selected_model_ = nullptr;
  return temp;
}
inline ::carbon::frontend::dashboard::CropModel* DashboardStateMessage::_internal_mutable_selected_model() {
  
  if (selected_model_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::dashboard::CropModel>(GetArenaForAllocation());
    selected_model_ = p;
  }
  return selected_model_;
}
inline ::carbon::frontend::dashboard::CropModel* DashboardStateMessage::mutable_selected_model() {
  ::carbon::frontend::dashboard::CropModel* _msg = _internal_mutable_selected_model();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.selected_model)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_selected_model(::carbon::frontend::dashboard::CropModel* selected_model) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete selected_model_;
  }
  if (selected_model) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::dashboard::CropModel>::GetOwningArena(selected_model);
    if (message_arena != submessage_arena) {
      selected_model = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, selected_model, submessage_arena);
    }
    
  } else {
    
  }
  selected_model_ = selected_model;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.selected_model)
}

// .carbon.frontend.dashboard.TargetingState targeting_state = 6;
inline bool DashboardStateMessage::_internal_has_targeting_state() const {
  return this != internal_default_instance() && targeting_state_ != nullptr;
}
inline bool DashboardStateMessage::has_targeting_state() const {
  return _internal_has_targeting_state();
}
inline void DashboardStateMessage::clear_targeting_state() {
  if (GetArenaForAllocation() == nullptr && targeting_state_ != nullptr) {
    delete targeting_state_;
  }
  targeting_state_ = nullptr;
}
inline const ::carbon::frontend::dashboard::TargetingState& DashboardStateMessage::_internal_targeting_state() const {
  const ::carbon::frontend::dashboard::TargetingState* p = targeting_state_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::dashboard::TargetingState&>(
      ::carbon::frontend::dashboard::_TargetingState_default_instance_);
}
inline const ::carbon::frontend::dashboard::TargetingState& DashboardStateMessage::targeting_state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.targeting_state)
  return _internal_targeting_state();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_targeting_state(
    ::carbon::frontend::dashboard::TargetingState* targeting_state) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(targeting_state_);
  }
  targeting_state_ = targeting_state;
  if (targeting_state) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.targeting_state)
}
inline ::carbon::frontend::dashboard::TargetingState* DashboardStateMessage::release_targeting_state() {
  
  ::carbon::frontend::dashboard::TargetingState* temp = targeting_state_;
  targeting_state_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::dashboard::TargetingState* DashboardStateMessage::unsafe_arena_release_targeting_state() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.targeting_state)
  
  ::carbon::frontend::dashboard::TargetingState* temp = targeting_state_;
  targeting_state_ = nullptr;
  return temp;
}
inline ::carbon::frontend::dashboard::TargetingState* DashboardStateMessage::_internal_mutable_targeting_state() {
  
  if (targeting_state_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::dashboard::TargetingState>(GetArenaForAllocation());
    targeting_state_ = p;
  }
  return targeting_state_;
}
inline ::carbon::frontend::dashboard::TargetingState* DashboardStateMessage::mutable_targeting_state() {
  ::carbon::frontend::dashboard::TargetingState* _msg = _internal_mutable_targeting_state();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.targeting_state)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_targeting_state(::carbon::frontend::dashboard::TargetingState* targeting_state) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete targeting_state_;
  }
  if (targeting_state) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::dashboard::TargetingState>::GetOwningArena(targeting_state);
    if (message_arena != submessage_arena) {
      targeting_state = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, targeting_state, submessage_arena);
    }
    
  } else {
    
  }
  targeting_state_ = targeting_state;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.targeting_state)
}

// repeated bool target_state_mismatch = 7 [deprecated = true];
inline int DashboardStateMessage::_internal_target_state_mismatch_size() const {
  return target_state_mismatch_.size();
}
inline int DashboardStateMessage::target_state_mismatch_size() const {
  return _internal_target_state_mismatch_size();
}
inline void DashboardStateMessage::clear_target_state_mismatch() {
  target_state_mismatch_.Clear();
}
inline bool DashboardStateMessage::_internal_target_state_mismatch(int index) const {
  return target_state_mismatch_.Get(index);
}
inline bool DashboardStateMessage::target_state_mismatch(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.target_state_mismatch)
  return _internal_target_state_mismatch(index);
}
inline void DashboardStateMessage::set_target_state_mismatch(int index, bool value) {
  target_state_mismatch_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.target_state_mismatch)
}
inline void DashboardStateMessage::_internal_add_target_state_mismatch(bool value) {
  target_state_mismatch_.Add(value);
}
inline void DashboardStateMessage::add_target_state_mismatch(bool value) {
  _internal_add_target_state_mismatch(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.DashboardStateMessage.target_state_mismatch)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
DashboardStateMessage::_internal_target_state_mismatch() const {
  return target_state_mismatch_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
DashboardStateMessage::target_state_mismatch() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.DashboardStateMessage.target_state_mismatch)
  return _internal_target_state_mismatch();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
DashboardStateMessage::_internal_mutable_target_state_mismatch() {
  return &target_state_mismatch_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
DashboardStateMessage::mutable_target_state_mismatch() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.DashboardStateMessage.target_state_mismatch)
  return _internal_mutable_target_state_mismatch();
}

// repeated bool row_ready = 8 [deprecated = true];
inline int DashboardStateMessage::_internal_row_ready_size() const {
  return row_ready_.size();
}
inline int DashboardStateMessage::row_ready_size() const {
  return _internal_row_ready_size();
}
inline void DashboardStateMessage::clear_row_ready() {
  row_ready_.Clear();
}
inline bool DashboardStateMessage::_internal_row_ready(int index) const {
  return row_ready_.Get(index);
}
inline bool DashboardStateMessage::row_ready(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.row_ready)
  return _internal_row_ready(index);
}
inline void DashboardStateMessage::set_row_ready(int index, bool value) {
  row_ready_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.row_ready)
}
inline void DashboardStateMessage::_internal_add_row_ready(bool value) {
  row_ready_.Add(value);
}
inline void DashboardStateMessage::add_row_ready(bool value) {
  _internal_add_row_ready(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.DashboardStateMessage.row_ready)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
DashboardStateMessage::_internal_row_ready() const {
  return row_ready_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
DashboardStateMessage::row_ready() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.DashboardStateMessage.row_ready)
  return _internal_row_ready();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
DashboardStateMessage::_internal_mutable_row_ready() {
  return &row_ready_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
DashboardStateMessage::mutable_row_ready() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.DashboardStateMessage.row_ready)
  return _internal_mutable_row_ready();
}

// repeated bool row_exists = 9 [deprecated = true];
inline int DashboardStateMessage::_internal_row_exists_size() const {
  return row_exists_.size();
}
inline int DashboardStateMessage::row_exists_size() const {
  return _internal_row_exists_size();
}
inline void DashboardStateMessage::clear_row_exists() {
  row_exists_.Clear();
}
inline bool DashboardStateMessage::_internal_row_exists(int index) const {
  return row_exists_.Get(index);
}
inline bool DashboardStateMessage::row_exists(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.row_exists)
  return _internal_row_exists(index);
}
inline void DashboardStateMessage::set_row_exists(int index, bool value) {
  row_exists_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.row_exists)
}
inline void DashboardStateMessage::_internal_add_row_exists(bool value) {
  row_exists_.Add(value);
}
inline void DashboardStateMessage::add_row_exists(bool value) {
  _internal_add_row_exists(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.DashboardStateMessage.row_exists)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
DashboardStateMessage::_internal_row_exists() const {
  return row_exists_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >&
DashboardStateMessage::row_exists() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.DashboardStateMessage.row_exists)
  return _internal_row_exists();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
DashboardStateMessage::_internal_mutable_row_exists() {
  return &row_exists_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< bool >*
DashboardStateMessage::mutable_row_exists() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.DashboardStateMessage.row_exists)
  return _internal_mutable_row_exists();
}

// double row_width_in = 10;
inline void DashboardStateMessage::clear_row_width_in() {
  row_width_in_ = 0;
}
inline double DashboardStateMessage::_internal_row_width_in() const {
  return row_width_in_;
}
inline double DashboardStateMessage::row_width_in() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.row_width_in)
  return _internal_row_width_in();
}
inline void DashboardStateMessage::_internal_set_row_width_in(double value) {
  
  row_width_in_ = value;
}
inline void DashboardStateMessage::set_row_width_in(double value) {
  _internal_set_row_width_in(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.row_width_in)
}

// repeated .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 11 [deprecated = true];
inline int DashboardStateMessage::_internal_safety_override_state_size() const {
  return safety_override_state_.size();
}
inline int DashboardStateMessage::safety_override_state_size() const {
  return _internal_safety_override_state_size();
}
inline void DashboardStateMessage::clear_safety_override_state() {
  safety_override_state_.Clear();
}
inline ::carbon::frontend::dashboard::SafetyOverrideState DashboardStateMessage::_internal_safety_override_state(int index) const {
  return static_cast< ::carbon::frontend::dashboard::SafetyOverrideState >(safety_override_state_.Get(index));
}
inline ::carbon::frontend::dashboard::SafetyOverrideState DashboardStateMessage::safety_override_state(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.safety_override_state)
  return _internal_safety_override_state(index);
}
inline void DashboardStateMessage::set_safety_override_state(int index, ::carbon::frontend::dashboard::SafetyOverrideState value) {
  safety_override_state_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.safety_override_state)
}
inline void DashboardStateMessage::_internal_add_safety_override_state(::carbon::frontend::dashboard::SafetyOverrideState value) {
  safety_override_state_.Add(value);
}
inline void DashboardStateMessage::add_safety_override_state(::carbon::frontend::dashboard::SafetyOverrideState value) {
  _internal_add_safety_override_state(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.DashboardStateMessage.safety_override_state)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>&
DashboardStateMessage::safety_override_state() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.DashboardStateMessage.safety_override_state)
  return safety_override_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
DashboardStateMessage::_internal_mutable_safety_override_state() {
  return &safety_override_state_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField<int>*
DashboardStateMessage::mutable_safety_override_state() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.DashboardStateMessage.safety_override_state)
  return _internal_mutable_safety_override_state();
}

// .carbon.frontend.dashboard.ImplementState implement_state = 13;
inline void DashboardStateMessage::clear_implement_state() {
  implement_state_ = 0;
}
inline ::carbon::frontend::dashboard::ImplementState DashboardStateMessage::_internal_implement_state() const {
  return static_cast< ::carbon::frontend::dashboard::ImplementState >(implement_state_);
}
inline ::carbon::frontend::dashboard::ImplementState DashboardStateMessage::implement_state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.implement_state)
  return _internal_implement_state();
}
inline void DashboardStateMessage::_internal_set_implement_state(::carbon::frontend::dashboard::ImplementState value) {
  
  implement_state_ = value;
}
inline void DashboardStateMessage::set_implement_state(::carbon::frontend::dashboard::ImplementState value) {
  _internal_set_implement_state(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.implement_state)
}

// bool efficiency_enabled = 14;
inline void DashboardStateMessage::clear_efficiency_enabled() {
  efficiency_enabled_ = false;
}
inline bool DashboardStateMessage::_internal_efficiency_enabled() const {
  return efficiency_enabled_;
}
inline bool DashboardStateMessage::efficiency_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.efficiency_enabled)
  return _internal_efficiency_enabled();
}
inline void DashboardStateMessage::_internal_set_efficiency_enabled(bool value) {
  
  efficiency_enabled_ = value;
}
inline void DashboardStateMessage::set_efficiency_enabled(bool value) {
  _internal_set_efficiency_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.efficiency_enabled)
}

// .carbon.frontend.translation.PercentValue efficiency_percent = 15;
inline bool DashboardStateMessage::_internal_has_efficiency_percent() const {
  return this != internal_default_instance() && efficiency_percent_ != nullptr;
}
inline bool DashboardStateMessage::has_efficiency_percent() const {
  return _internal_has_efficiency_percent();
}
inline const ::carbon::frontend::translation::PercentValue& DashboardStateMessage::_internal_efficiency_percent() const {
  const ::carbon::frontend::translation::PercentValue* p = efficiency_percent_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::PercentValue&>(
      ::carbon::frontend::translation::_PercentValue_default_instance_);
}
inline const ::carbon::frontend::translation::PercentValue& DashboardStateMessage::efficiency_percent() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.efficiency_percent)
  return _internal_efficiency_percent();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_efficiency_percent(
    ::carbon::frontend::translation::PercentValue* efficiency_percent) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(efficiency_percent_);
  }
  efficiency_percent_ = efficiency_percent;
  if (efficiency_percent) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.efficiency_percent)
}
inline ::carbon::frontend::translation::PercentValue* DashboardStateMessage::release_efficiency_percent() {
  
  ::carbon::frontend::translation::PercentValue* temp = efficiency_percent_;
  efficiency_percent_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::PercentValue* DashboardStateMessage::unsafe_arena_release_efficiency_percent() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.efficiency_percent)
  
  ::carbon::frontend::translation::PercentValue* temp = efficiency_percent_;
  efficiency_percent_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::PercentValue* DashboardStateMessage::_internal_mutable_efficiency_percent() {
  
  if (efficiency_percent_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::PercentValue>(GetArenaForAllocation());
    efficiency_percent_ = p;
  }
  return efficiency_percent_;
}
inline ::carbon::frontend::translation::PercentValue* DashboardStateMessage::mutable_efficiency_percent() {
  ::carbon::frontend::translation::PercentValue* _msg = _internal_mutable_efficiency_percent();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.efficiency_percent)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_efficiency_percent(::carbon::frontend::translation::PercentValue* efficiency_percent) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(efficiency_percent_);
  }
  if (efficiency_percent) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(efficiency_percent));
    if (message_arena != submessage_arena) {
      efficiency_percent = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, efficiency_percent, submessage_arena);
    }
    
  } else {
    
  }
  efficiency_percent_ = efficiency_percent;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.efficiency_percent)
}

// bool error_rate_enabled = 16;
inline void DashboardStateMessage::clear_error_rate_enabled() {
  error_rate_enabled_ = false;
}
inline bool DashboardStateMessage::_internal_error_rate_enabled() const {
  return error_rate_enabled_;
}
inline bool DashboardStateMessage::error_rate_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.error_rate_enabled)
  return _internal_error_rate_enabled();
}
inline void DashboardStateMessage::_internal_set_error_rate_enabled(bool value) {
  
  error_rate_enabled_ = value;
}
inline void DashboardStateMessage::set_error_rate_enabled(bool value) {
  _internal_set_error_rate_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.error_rate_enabled)
}

// .carbon.frontend.translation.PercentValue error_rate = 17;
inline bool DashboardStateMessage::_internal_has_error_rate() const {
  return this != internal_default_instance() && error_rate_ != nullptr;
}
inline bool DashboardStateMessage::has_error_rate() const {
  return _internal_has_error_rate();
}
inline const ::carbon::frontend::translation::PercentValue& DashboardStateMessage::_internal_error_rate() const {
  const ::carbon::frontend::translation::PercentValue* p = error_rate_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::PercentValue&>(
      ::carbon::frontend::translation::_PercentValue_default_instance_);
}
inline const ::carbon::frontend::translation::PercentValue& DashboardStateMessage::error_rate() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.error_rate)
  return _internal_error_rate();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_error_rate(
    ::carbon::frontend::translation::PercentValue* error_rate) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(error_rate_);
  }
  error_rate_ = error_rate;
  if (error_rate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.error_rate)
}
inline ::carbon::frontend::translation::PercentValue* DashboardStateMessage::release_error_rate() {
  
  ::carbon::frontend::translation::PercentValue* temp = error_rate_;
  error_rate_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::PercentValue* DashboardStateMessage::unsafe_arena_release_error_rate() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.error_rate)
  
  ::carbon::frontend::translation::PercentValue* temp = error_rate_;
  error_rate_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::PercentValue* DashboardStateMessage::_internal_mutable_error_rate() {
  
  if (error_rate_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::PercentValue>(GetArenaForAllocation());
    error_rate_ = p;
  }
  return error_rate_;
}
inline ::carbon::frontend::translation::PercentValue* DashboardStateMessage::mutable_error_rate() {
  ::carbon::frontend::translation::PercentValue* _msg = _internal_mutable_error_rate();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.error_rate)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_error_rate(::carbon::frontend::translation::PercentValue* error_rate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(error_rate_);
  }
  if (error_rate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(error_rate));
    if (message_arena != submessage_arena) {
      error_rate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, error_rate, submessage_arena);
    }
    
  } else {
    
  }
  error_rate_ = error_rate;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.error_rate)
}

// repeated .carbon.frontend.dashboard.ExtraConclusion extra_conclusions = 18;
inline int DashboardStateMessage::_internal_extra_conclusions_size() const {
  return extra_conclusions_.size();
}
inline int DashboardStateMessage::extra_conclusions_size() const {
  return _internal_extra_conclusions_size();
}
inline void DashboardStateMessage::clear_extra_conclusions() {
  extra_conclusions_.Clear();
}
inline ::carbon::frontend::dashboard::ExtraConclusion* DashboardStateMessage::mutable_extra_conclusions(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.extra_conclusions)
  return extra_conclusions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraConclusion >*
DashboardStateMessage::mutable_extra_conclusions() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.DashboardStateMessage.extra_conclusions)
  return &extra_conclusions_;
}
inline const ::carbon::frontend::dashboard::ExtraConclusion& DashboardStateMessage::_internal_extra_conclusions(int index) const {
  return extra_conclusions_.Get(index);
}
inline const ::carbon::frontend::dashboard::ExtraConclusion& DashboardStateMessage::extra_conclusions(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.extra_conclusions)
  return _internal_extra_conclusions(index);
}
inline ::carbon::frontend::dashboard::ExtraConclusion* DashboardStateMessage::_internal_add_extra_conclusions() {
  return extra_conclusions_.Add();
}
inline ::carbon::frontend::dashboard::ExtraConclusion* DashboardStateMessage::add_extra_conclusions() {
  ::carbon::frontend::dashboard::ExtraConclusion* _add = _internal_add_extra_conclusions();
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.DashboardStateMessage.extra_conclusions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::ExtraConclusion >&
DashboardStateMessage::extra_conclusions() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.DashboardStateMessage.extra_conclusions)
  return extra_conclusions_;
}

// .carbon.frontend.translation.AreaValue area_weeded_today = 19;
inline bool DashboardStateMessage::_internal_has_area_weeded_today() const {
  return this != internal_default_instance() && area_weeded_today_ != nullptr;
}
inline bool DashboardStateMessage::has_area_weeded_today() const {
  return _internal_has_area_weeded_today();
}
inline const ::carbon::frontend::translation::AreaValue& DashboardStateMessage::_internal_area_weeded_today() const {
  const ::carbon::frontend::translation::AreaValue* p = area_weeded_today_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::AreaValue&>(
      ::carbon::frontend::translation::_AreaValue_default_instance_);
}
inline const ::carbon::frontend::translation::AreaValue& DashboardStateMessage::area_weeded_today() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_today)
  return _internal_area_weeded_today();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_area_weeded_today(
    ::carbon::frontend::translation::AreaValue* area_weeded_today) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(area_weeded_today_);
  }
  area_weeded_today_ = area_weeded_today;
  if (area_weeded_today) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_today)
}
inline ::carbon::frontend::translation::AreaValue* DashboardStateMessage::release_area_weeded_today() {
  
  ::carbon::frontend::translation::AreaValue* temp = area_weeded_today_;
  area_weeded_today_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::AreaValue* DashboardStateMessage::unsafe_arena_release_area_weeded_today() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_today)
  
  ::carbon::frontend::translation::AreaValue* temp = area_weeded_today_;
  area_weeded_today_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::AreaValue* DashboardStateMessage::_internal_mutable_area_weeded_today() {
  
  if (area_weeded_today_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::AreaValue>(GetArenaForAllocation());
    area_weeded_today_ = p;
  }
  return area_weeded_today_;
}
inline ::carbon::frontend::translation::AreaValue* DashboardStateMessage::mutable_area_weeded_today() {
  ::carbon::frontend::translation::AreaValue* _msg = _internal_mutable_area_weeded_today();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_today)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_area_weeded_today(::carbon::frontend::translation::AreaValue* area_weeded_today) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(area_weeded_today_);
  }
  if (area_weeded_today) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(area_weeded_today));
    if (message_arena != submessage_arena) {
      area_weeded_today = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, area_weeded_today, submessage_arena);
    }
    
  } else {
    
  }
  area_weeded_today_ = area_weeded_today;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_today)
}

// .carbon.frontend.translation.AreaValue area_weeded_total = 20;
inline bool DashboardStateMessage::_internal_has_area_weeded_total() const {
  return this != internal_default_instance() && area_weeded_total_ != nullptr;
}
inline bool DashboardStateMessage::has_area_weeded_total() const {
  return _internal_has_area_weeded_total();
}
inline const ::carbon::frontend::translation::AreaValue& DashboardStateMessage::_internal_area_weeded_total() const {
  const ::carbon::frontend::translation::AreaValue* p = area_weeded_total_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::AreaValue&>(
      ::carbon::frontend::translation::_AreaValue_default_instance_);
}
inline const ::carbon::frontend::translation::AreaValue& DashboardStateMessage::area_weeded_total() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_total)
  return _internal_area_weeded_total();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_area_weeded_total(
    ::carbon::frontend::translation::AreaValue* area_weeded_total) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(area_weeded_total_);
  }
  area_weeded_total_ = area_weeded_total;
  if (area_weeded_total) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_total)
}
inline ::carbon::frontend::translation::AreaValue* DashboardStateMessage::release_area_weeded_total() {
  
  ::carbon::frontend::translation::AreaValue* temp = area_weeded_total_;
  area_weeded_total_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::AreaValue* DashboardStateMessage::unsafe_arena_release_area_weeded_total() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_total)
  
  ::carbon::frontend::translation::AreaValue* temp = area_weeded_total_;
  area_weeded_total_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::AreaValue* DashboardStateMessage::_internal_mutable_area_weeded_total() {
  
  if (area_weeded_total_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::AreaValue>(GetArenaForAllocation());
    area_weeded_total_ = p;
  }
  return area_weeded_total_;
}
inline ::carbon::frontend::translation::AreaValue* DashboardStateMessage::mutable_area_weeded_total() {
  ::carbon::frontend::translation::AreaValue* _msg = _internal_mutable_area_weeded_total();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_total)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_area_weeded_total(::carbon::frontend::translation::AreaValue* area_weeded_total) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(area_weeded_total_);
  }
  if (area_weeded_total) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(area_weeded_total));
    if (message_arena != submessage_arena) {
      area_weeded_total = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, area_weeded_total, submessage_arena);
    }
    
  } else {
    
  }
  area_weeded_total_ = area_weeded_total;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.area_weeded_total)
}

// .carbon.frontend.translation.IntegerValue weeds_killed_today = 21;
inline bool DashboardStateMessage::_internal_has_weeds_killed_today() const {
  return this != internal_default_instance() && weeds_killed_today_ != nullptr;
}
inline bool DashboardStateMessage::has_weeds_killed_today() const {
  return _internal_has_weeds_killed_today();
}
inline const ::carbon::frontend::translation::IntegerValue& DashboardStateMessage::_internal_weeds_killed_today() const {
  const ::carbon::frontend::translation::IntegerValue* p = weeds_killed_today_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::IntegerValue&>(
      ::carbon::frontend::translation::_IntegerValue_default_instance_);
}
inline const ::carbon::frontend::translation::IntegerValue& DashboardStateMessage::weeds_killed_today() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_today)
  return _internal_weeds_killed_today();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_weeds_killed_today(
    ::carbon::frontend::translation::IntegerValue* weeds_killed_today) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weeds_killed_today_);
  }
  weeds_killed_today_ = weeds_killed_today;
  if (weeds_killed_today) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_today)
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::release_weeds_killed_today() {
  
  ::carbon::frontend::translation::IntegerValue* temp = weeds_killed_today_;
  weeds_killed_today_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::unsafe_arena_release_weeds_killed_today() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_today)
  
  ::carbon::frontend::translation::IntegerValue* temp = weeds_killed_today_;
  weeds_killed_today_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::_internal_mutable_weeds_killed_today() {
  
  if (weeds_killed_today_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::IntegerValue>(GetArenaForAllocation());
    weeds_killed_today_ = p;
  }
  return weeds_killed_today_;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::mutable_weeds_killed_today() {
  ::carbon::frontend::translation::IntegerValue* _msg = _internal_mutable_weeds_killed_today();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_today)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_weeds_killed_today(::carbon::frontend::translation::IntegerValue* weeds_killed_today) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(weeds_killed_today_);
  }
  if (weeds_killed_today) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weeds_killed_today));
    if (message_arena != submessage_arena) {
      weeds_killed_today = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, weeds_killed_today, submessage_arena);
    }
    
  } else {
    
  }
  weeds_killed_today_ = weeds_killed_today;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_today)
}

// .carbon.frontend.translation.IntegerValue weeds_killed_total = 22;
inline bool DashboardStateMessage::_internal_has_weeds_killed_total() const {
  return this != internal_default_instance() && weeds_killed_total_ != nullptr;
}
inline bool DashboardStateMessage::has_weeds_killed_total() const {
  return _internal_has_weeds_killed_total();
}
inline const ::carbon::frontend::translation::IntegerValue& DashboardStateMessage::_internal_weeds_killed_total() const {
  const ::carbon::frontend::translation::IntegerValue* p = weeds_killed_total_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::IntegerValue&>(
      ::carbon::frontend::translation::_IntegerValue_default_instance_);
}
inline const ::carbon::frontend::translation::IntegerValue& DashboardStateMessage::weeds_killed_total() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_total)
  return _internal_weeds_killed_total();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_weeds_killed_total(
    ::carbon::frontend::translation::IntegerValue* weeds_killed_total) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weeds_killed_total_);
  }
  weeds_killed_total_ = weeds_killed_total;
  if (weeds_killed_total) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_total)
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::release_weeds_killed_total() {
  
  ::carbon::frontend::translation::IntegerValue* temp = weeds_killed_total_;
  weeds_killed_total_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::unsafe_arena_release_weeds_killed_total() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_total)
  
  ::carbon::frontend::translation::IntegerValue* temp = weeds_killed_total_;
  weeds_killed_total_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::_internal_mutable_weeds_killed_total() {
  
  if (weeds_killed_total_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::IntegerValue>(GetArenaForAllocation());
    weeds_killed_total_ = p;
  }
  return weeds_killed_total_;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::mutable_weeds_killed_total() {
  ::carbon::frontend::translation::IntegerValue* _msg = _internal_mutable_weeds_killed_total();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_total)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_weeds_killed_total(::carbon::frontend::translation::IntegerValue* weeds_killed_total) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(weeds_killed_total_);
  }
  if (weeds_killed_total) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(weeds_killed_total));
    if (message_arena != submessage_arena) {
      weeds_killed_total = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, weeds_killed_total, submessage_arena);
    }
    
  } else {
    
  }
  weeds_killed_total_ = weeds_killed_total;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_total)
}

// .carbon.frontend.translation.DurationValue time_weeded_today = 23;
inline bool DashboardStateMessage::_internal_has_time_weeded_today() const {
  return this != internal_default_instance() && time_weeded_today_ != nullptr;
}
inline bool DashboardStateMessage::has_time_weeded_today() const {
  return _internal_has_time_weeded_today();
}
inline const ::carbon::frontend::translation::DurationValue& DashboardStateMessage::_internal_time_weeded_today() const {
  const ::carbon::frontend::translation::DurationValue* p = time_weeded_today_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::DurationValue&>(
      ::carbon::frontend::translation::_DurationValue_default_instance_);
}
inline const ::carbon::frontend::translation::DurationValue& DashboardStateMessage::time_weeded_today() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_today)
  return _internal_time_weeded_today();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_time_weeded_today(
    ::carbon::frontend::translation::DurationValue* time_weeded_today) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(time_weeded_today_);
  }
  time_weeded_today_ = time_weeded_today;
  if (time_weeded_today) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_today)
}
inline ::carbon::frontend::translation::DurationValue* DashboardStateMessage::release_time_weeded_today() {
  
  ::carbon::frontend::translation::DurationValue* temp = time_weeded_today_;
  time_weeded_today_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::DurationValue* DashboardStateMessage::unsafe_arena_release_time_weeded_today() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_today)
  
  ::carbon::frontend::translation::DurationValue* temp = time_weeded_today_;
  time_weeded_today_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::DurationValue* DashboardStateMessage::_internal_mutable_time_weeded_today() {
  
  if (time_weeded_today_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::DurationValue>(GetArenaForAllocation());
    time_weeded_today_ = p;
  }
  return time_weeded_today_;
}
inline ::carbon::frontend::translation::DurationValue* DashboardStateMessage::mutable_time_weeded_today() {
  ::carbon::frontend::translation::DurationValue* _msg = _internal_mutable_time_weeded_today();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_today)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_time_weeded_today(::carbon::frontend::translation::DurationValue* time_weeded_today) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(time_weeded_today_);
  }
  if (time_weeded_today) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(time_weeded_today));
    if (message_arena != submessage_arena) {
      time_weeded_today = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, time_weeded_today, submessage_arena);
    }
    
  } else {
    
  }
  time_weeded_today_ = time_weeded_today;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_today)
}

// .carbon.frontend.translation.DurationValue time_weeded_total = 24;
inline bool DashboardStateMessage::_internal_has_time_weeded_total() const {
  return this != internal_default_instance() && time_weeded_total_ != nullptr;
}
inline bool DashboardStateMessage::has_time_weeded_total() const {
  return _internal_has_time_weeded_total();
}
inline const ::carbon::frontend::translation::DurationValue& DashboardStateMessage::_internal_time_weeded_total() const {
  const ::carbon::frontend::translation::DurationValue* p = time_weeded_total_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::DurationValue&>(
      ::carbon::frontend::translation::_DurationValue_default_instance_);
}
inline const ::carbon::frontend::translation::DurationValue& DashboardStateMessage::time_weeded_total() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_total)
  return _internal_time_weeded_total();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_time_weeded_total(
    ::carbon::frontend::translation::DurationValue* time_weeded_total) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(time_weeded_total_);
  }
  time_weeded_total_ = time_weeded_total;
  if (time_weeded_total) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_total)
}
inline ::carbon::frontend::translation::DurationValue* DashboardStateMessage::release_time_weeded_total() {
  
  ::carbon::frontend::translation::DurationValue* temp = time_weeded_total_;
  time_weeded_total_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::DurationValue* DashboardStateMessage::unsafe_arena_release_time_weeded_total() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_total)
  
  ::carbon::frontend::translation::DurationValue* temp = time_weeded_total_;
  time_weeded_total_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::DurationValue* DashboardStateMessage::_internal_mutable_time_weeded_total() {
  
  if (time_weeded_total_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::DurationValue>(GetArenaForAllocation());
    time_weeded_total_ = p;
  }
  return time_weeded_total_;
}
inline ::carbon::frontend::translation::DurationValue* DashboardStateMessage::mutable_time_weeded_total() {
  ::carbon::frontend::translation::DurationValue* _msg = _internal_mutable_time_weeded_total();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_total)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_time_weeded_total(::carbon::frontend::translation::DurationValue* time_weeded_total) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(time_weeded_total_);
  }
  if (time_weeded_total) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(time_weeded_total));
    if (message_arena != submessage_arena) {
      time_weeded_total = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, time_weeded_total, submessage_arena);
    }
    
  } else {
    
  }
  time_weeded_total_ = time_weeded_total;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.time_weeded_total)
}

// bool weeding_enabled = 25;
inline void DashboardStateMessage::clear_weeding_enabled() {
  weeding_enabled_ = false;
}
inline bool DashboardStateMessage::_internal_weeding_enabled() const {
  return weeding_enabled_;
}
inline bool DashboardStateMessage::weeding_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.weeding_enabled)
  return _internal_weeding_enabled();
}
inline void DashboardStateMessage::_internal_set_weeding_enabled(bool value) {
  
  weeding_enabled_ = value;
}
inline void DashboardStateMessage::set_weeding_enabled(bool value) {
  _internal_set_weeding_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.weeding_enabled)
}

// bool debug_mode = 26;
inline void DashboardStateMessage::clear_debug_mode() {
  debug_mode_ = false;
}
inline bool DashboardStateMessage::_internal_debug_mode() const {
  return debug_mode_;
}
inline bool DashboardStateMessage::debug_mode() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.debug_mode)
  return _internal_debug_mode();
}
inline void DashboardStateMessage::_internal_set_debug_mode(bool value) {
  
  debug_mode_ = value;
}
inline void DashboardStateMessage::set_debug_mode(bool value) {
  _internal_set_debug_mode(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.debug_mode)
}

// .carbon.frontend.translation.IntegerValue crops_killed_today = 27;
inline bool DashboardStateMessage::_internal_has_crops_killed_today() const {
  return this != internal_default_instance() && crops_killed_today_ != nullptr;
}
inline bool DashboardStateMessage::has_crops_killed_today() const {
  return _internal_has_crops_killed_today();
}
inline const ::carbon::frontend::translation::IntegerValue& DashboardStateMessage::_internal_crops_killed_today() const {
  const ::carbon::frontend::translation::IntegerValue* p = crops_killed_today_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::IntegerValue&>(
      ::carbon::frontend::translation::_IntegerValue_default_instance_);
}
inline const ::carbon::frontend::translation::IntegerValue& DashboardStateMessage::crops_killed_today() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_today)
  return _internal_crops_killed_today();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_crops_killed_today(
    ::carbon::frontend::translation::IntegerValue* crops_killed_today) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(crops_killed_today_);
  }
  crops_killed_today_ = crops_killed_today;
  if (crops_killed_today) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_today)
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::release_crops_killed_today() {
  
  ::carbon::frontend::translation::IntegerValue* temp = crops_killed_today_;
  crops_killed_today_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::unsafe_arena_release_crops_killed_today() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_today)
  
  ::carbon::frontend::translation::IntegerValue* temp = crops_killed_today_;
  crops_killed_today_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::_internal_mutable_crops_killed_today() {
  
  if (crops_killed_today_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::IntegerValue>(GetArenaForAllocation());
    crops_killed_today_ = p;
  }
  return crops_killed_today_;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::mutable_crops_killed_today() {
  ::carbon::frontend::translation::IntegerValue* _msg = _internal_mutable_crops_killed_today();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_today)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_crops_killed_today(::carbon::frontend::translation::IntegerValue* crops_killed_today) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(crops_killed_today_);
  }
  if (crops_killed_today) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(crops_killed_today));
    if (message_arena != submessage_arena) {
      crops_killed_today = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, crops_killed_today, submessage_arena);
    }
    
  } else {
    
  }
  crops_killed_today_ = crops_killed_today;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_today)
}

// .carbon.frontend.translation.IntegerValue crops_killed_total = 28;
inline bool DashboardStateMessage::_internal_has_crops_killed_total() const {
  return this != internal_default_instance() && crops_killed_total_ != nullptr;
}
inline bool DashboardStateMessage::has_crops_killed_total() const {
  return _internal_has_crops_killed_total();
}
inline const ::carbon::frontend::translation::IntegerValue& DashboardStateMessage::_internal_crops_killed_total() const {
  const ::carbon::frontend::translation::IntegerValue* p = crops_killed_total_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::translation::IntegerValue&>(
      ::carbon::frontend::translation::_IntegerValue_default_instance_);
}
inline const ::carbon::frontend::translation::IntegerValue& DashboardStateMessage::crops_killed_total() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_total)
  return _internal_crops_killed_total();
}
inline void DashboardStateMessage::unsafe_arena_set_allocated_crops_killed_total(
    ::carbon::frontend::translation::IntegerValue* crops_killed_total) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(crops_killed_total_);
  }
  crops_killed_total_ = crops_killed_total;
  if (crops_killed_total) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_total)
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::release_crops_killed_total() {
  
  ::carbon::frontend::translation::IntegerValue* temp = crops_killed_total_;
  crops_killed_total_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::unsafe_arena_release_crops_killed_total() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_total)
  
  ::carbon::frontend::translation::IntegerValue* temp = crops_killed_total_;
  crops_killed_total_ = nullptr;
  return temp;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::_internal_mutable_crops_killed_total() {
  
  if (crops_killed_total_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::translation::IntegerValue>(GetArenaForAllocation());
    crops_killed_total_ = p;
  }
  return crops_killed_total_;
}
inline ::carbon::frontend::translation::IntegerValue* DashboardStateMessage::mutable_crops_killed_total() {
  ::carbon::frontend::translation::IntegerValue* _msg = _internal_mutable_crops_killed_total();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_total)
  return _msg;
}
inline void DashboardStateMessage::set_allocated_crops_killed_total(::carbon::frontend::translation::IntegerValue* crops_killed_total) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(crops_killed_total_);
  }
  if (crops_killed_total) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(crops_killed_total));
    if (message_arena != submessage_arena) {
      crops_killed_total = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, crops_killed_total, submessage_arena);
    }
    
  } else {
    
  }
  crops_killed_total_ = crops_killed_total;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.crops_killed_total)
}

// bool cruise_enabled = 29;
inline void DashboardStateMessage::clear_cruise_enabled() {
  cruise_enabled_ = false;
}
inline bool DashboardStateMessage::_internal_cruise_enabled() const {
  return cruise_enabled_;
}
inline bool DashboardStateMessage::cruise_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.cruise_enabled)
  return _internal_cruise_enabled();
}
inline void DashboardStateMessage::_internal_set_cruise_enabled(bool value) {
  
  cruise_enabled_ = value;
}
inline void DashboardStateMessage::set_cruise_enabled(bool value) {
  _internal_set_cruise_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.cruise_enabled)
}

// map<int32, .carbon.frontend.dashboard.RowStateMessage> row_states = 30;
inline int DashboardStateMessage::_internal_row_states_size() const {
  return row_states_.size();
}
inline int DashboardStateMessage::row_states_size() const {
  return _internal_row_states_size();
}
inline void DashboardStateMessage::clear_row_states() {
  row_states_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >&
DashboardStateMessage::_internal_row_states() const {
  return row_states_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >&
DashboardStateMessage::row_states() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.dashboard.DashboardStateMessage.row_states)
  return _internal_row_states();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >*
DashboardStateMessage::_internal_mutable_row_states() {
  return row_states_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >*
DashboardStateMessage::mutable_row_states() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.dashboard.DashboardStateMessage.row_states)
  return _internal_mutable_row_states();
}

// bool cruise_allow_enable = 31;
inline void DashboardStateMessage::clear_cruise_allow_enable() {
  cruise_allow_enable_ = false;
}
inline bool DashboardStateMessage::_internal_cruise_allow_enable() const {
  return cruise_allow_enable_;
}
inline bool DashboardStateMessage::cruise_allow_enable() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.cruise_allow_enable)
  return _internal_cruise_allow_enable();
}
inline void DashboardStateMessage::_internal_set_cruise_allow_enable(bool value) {
  
  cruise_allow_enable_ = value;
}
inline void DashboardStateMessage::set_cruise_allow_enable(bool value) {
  _internal_set_cruise_allow_enable(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.cruise_allow_enable)
}

// string cruise_disallowed_reason = 32;
inline void DashboardStateMessage::clear_cruise_disallowed_reason() {
  cruise_disallowed_reason_.ClearToEmpty();
}
inline const std::string& DashboardStateMessage::cruise_disallowed_reason() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.DashboardStateMessage.cruise_disallowed_reason)
  return _internal_cruise_disallowed_reason();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DashboardStateMessage::set_cruise_disallowed_reason(ArgT0&& arg0, ArgT... args) {
 
 cruise_disallowed_reason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.DashboardStateMessage.cruise_disallowed_reason)
}
inline std::string* DashboardStateMessage::mutable_cruise_disallowed_reason() {
  std::string* _s = _internal_mutable_cruise_disallowed_reason();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.DashboardStateMessage.cruise_disallowed_reason)
  return _s;
}
inline const std::string& DashboardStateMessage::_internal_cruise_disallowed_reason() const {
  return cruise_disallowed_reason_.Get();
}
inline void DashboardStateMessage::_internal_set_cruise_disallowed_reason(const std::string& value) {
  
  cruise_disallowed_reason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DashboardStateMessage::_internal_mutable_cruise_disallowed_reason() {
  
  return cruise_disallowed_reason_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DashboardStateMessage::release_cruise_disallowed_reason() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.DashboardStateMessage.cruise_disallowed_reason)
  return cruise_disallowed_reason_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DashboardStateMessage::set_allocated_cruise_disallowed_reason(std::string* cruise_disallowed_reason) {
  if (cruise_disallowed_reason != nullptr) {
    
  } else {
    
  }
  cruise_disallowed_reason_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cruise_disallowed_reason,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cruise_disallowed_reason_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cruise_disallowed_reason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.DashboardStateMessage.cruise_disallowed_reason)
}

// -------------------------------------------------------------------

// CropModel

// string crop = 1 [deprecated = true];
inline void CropModel::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& CropModel::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.CropModel.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CropModel::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.CropModel.crop)
}
inline std::string* CropModel::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.CropModel.crop)
  return _s;
}
inline const std::string& CropModel::_internal_crop() const {
  return crop_.Get();
}
inline void CropModel::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CropModel::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CropModel::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.CropModel.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CropModel::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.CropModel.crop)
}

// bool has_model = 2;
inline void CropModel::clear_has_model() {
  has_model_ = false;
}
inline bool CropModel::_internal_has_model() const {
  return has_model_;
}
inline bool CropModel::has_model() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.CropModel.has_model)
  return _internal_has_model();
}
inline void CropModel::_internal_set_has_model(bool value) {
  
  has_model_ = value;
}
inline void CropModel::set_has_model(bool value) {
  _internal_set_has_model(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.CropModel.has_model)
}

// bool preferred = 3;
inline void CropModel::clear_preferred() {
  preferred_ = false;
}
inline bool CropModel::_internal_preferred() const {
  return preferred_;
}
inline bool CropModel::preferred() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.CropModel.preferred)
  return _internal_preferred();
}
inline void CropModel::_internal_set_preferred(bool value) {
  
  preferred_ = value;
}
inline void CropModel::set_preferred(bool value) {
  _internal_set_preferred(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.CropModel.preferred)
}

// string crop_id = 4;
inline void CropModel::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& CropModel::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.CropModel.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CropModel::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.CropModel.crop_id)
}
inline std::string* CropModel::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.CropModel.crop_id)
  return _s;
}
inline const std::string& CropModel::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void CropModel::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CropModel::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CropModel::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.CropModel.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CropModel::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.CropModel.crop_id)
}

// -------------------------------------------------------------------

// CropModelOptions

// repeated .carbon.frontend.dashboard.CropModel models = 1;
inline int CropModelOptions::_internal_models_size() const {
  return models_.size();
}
inline int CropModelOptions::models_size() const {
  return _internal_models_size();
}
inline void CropModelOptions::clear_models() {
  models_.Clear();
}
inline ::carbon::frontend::dashboard::CropModel* CropModelOptions::mutable_models(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.CropModelOptions.models)
  return models_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::CropModel >*
CropModelOptions::mutable_models() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.dashboard.CropModelOptions.models)
  return &models_;
}
inline const ::carbon::frontend::dashboard::CropModel& CropModelOptions::_internal_models(int index) const {
  return models_.Get(index);
}
inline const ::carbon::frontend::dashboard::CropModel& CropModelOptions::models(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.CropModelOptions.models)
  return _internal_models(index);
}
inline ::carbon::frontend::dashboard::CropModel* CropModelOptions::_internal_add_models() {
  return models_.Add();
}
inline ::carbon::frontend::dashboard::CropModel* CropModelOptions::add_models() {
  ::carbon::frontend::dashboard::CropModel* _add = _internal_add_models();
  // @@protoc_insertion_point(field_add:carbon.frontend.dashboard.CropModelOptions.models)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::dashboard::CropModel >&
CropModelOptions::models() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.dashboard.CropModelOptions.models)
  return models_;
}

// -------------------------------------------------------------------

// RowId

// uint32 row_number = 1;
inline void RowId::clear_row_number() {
  row_number_ = 0u;
}
inline uint32_t RowId::_internal_row_number() const {
  return row_number_;
}
inline uint32_t RowId::row_number() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.RowId.row_number)
  return _internal_row_number();
}
inline void RowId::_internal_set_row_number(uint32_t value) {
  
  row_number_ = value;
}
inline void RowId::set_row_number(uint32_t value) {
  _internal_set_row_number(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.RowId.row_number)
}

// -------------------------------------------------------------------

// WeedingVelocity

// .carbon.frontend.util.Timestamp ts = 1;
inline bool WeedingVelocity::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool WeedingVelocity::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& WeedingVelocity::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& WeedingVelocity::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.ts)
  return _internal_ts();
}
inline void WeedingVelocity::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.dashboard.WeedingVelocity.ts)
}
inline ::carbon::frontend::util::Timestamp* WeedingVelocity::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* WeedingVelocity::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.dashboard.WeedingVelocity.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* WeedingVelocity::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* WeedingVelocity::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.dashboard.WeedingVelocity.ts)
  return _msg;
}
inline void WeedingVelocity::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.dashboard.WeedingVelocity.ts)
}

// double current_velocity_mph = 2;
inline void WeedingVelocity::clear_current_velocity_mph() {
  current_velocity_mph_ = 0;
}
inline double WeedingVelocity::_internal_current_velocity_mph() const {
  return current_velocity_mph_;
}
inline double WeedingVelocity::current_velocity_mph() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.current_velocity_mph)
  return _internal_current_velocity_mph();
}
inline void WeedingVelocity::_internal_set_current_velocity_mph(double value) {
  
  current_velocity_mph_ = value;
}
inline void WeedingVelocity::set_current_velocity_mph(double value) {
  _internal_set_current_velocity_mph(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedingVelocity.current_velocity_mph)
}

// double target_velocity_mph = 3;
inline void WeedingVelocity::clear_target_velocity_mph() {
  target_velocity_mph_ = 0;
}
inline double WeedingVelocity::_internal_target_velocity_mph() const {
  return target_velocity_mph_;
}
inline double WeedingVelocity::target_velocity_mph() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.target_velocity_mph)
  return _internal_target_velocity_mph();
}
inline void WeedingVelocity::_internal_set_target_velocity_mph(double value) {
  
  target_velocity_mph_ = value;
}
inline void WeedingVelocity::set_target_velocity_mph(double value) {
  _internal_set_target_velocity_mph(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedingVelocity.target_velocity_mph)
}

// double tolerance_mph = 4;
inline void WeedingVelocity::clear_tolerance_mph() {
  tolerance_mph_ = 0;
}
inline double WeedingVelocity::_internal_tolerance_mph() const {
  return tolerance_mph_;
}
inline double WeedingVelocity::tolerance_mph() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.tolerance_mph)
  return _internal_tolerance_mph();
}
inline void WeedingVelocity::_internal_set_tolerance_mph(double value) {
  
  tolerance_mph_ = value;
}
inline void WeedingVelocity::set_tolerance_mph(double value) {
  _internal_set_tolerance_mph(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedingVelocity.tolerance_mph)
}

// double primary_target_velocity_top_mph = 5;
inline void WeedingVelocity::clear_primary_target_velocity_top_mph() {
  primary_target_velocity_top_mph_ = 0;
}
inline double WeedingVelocity::_internal_primary_target_velocity_top_mph() const {
  return primary_target_velocity_top_mph_;
}
inline double WeedingVelocity::primary_target_velocity_top_mph() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.primary_target_velocity_top_mph)
  return _internal_primary_target_velocity_top_mph();
}
inline void WeedingVelocity::_internal_set_primary_target_velocity_top_mph(double value) {
  
  primary_target_velocity_top_mph_ = value;
}
inline void WeedingVelocity::set_primary_target_velocity_top_mph(double value) {
  _internal_set_primary_target_velocity_top_mph(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedingVelocity.primary_target_velocity_top_mph)
}

// double primary_target_velocity_bottom_mph = 6;
inline void WeedingVelocity::clear_primary_target_velocity_bottom_mph() {
  primary_target_velocity_bottom_mph_ = 0;
}
inline double WeedingVelocity::_internal_primary_target_velocity_bottom_mph() const {
  return primary_target_velocity_bottom_mph_;
}
inline double WeedingVelocity::primary_target_velocity_bottom_mph() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.primary_target_velocity_bottom_mph)
  return _internal_primary_target_velocity_bottom_mph();
}
inline void WeedingVelocity::_internal_set_primary_target_velocity_bottom_mph(double value) {
  
  primary_target_velocity_bottom_mph_ = value;
}
inline void WeedingVelocity::set_primary_target_velocity_bottom_mph(double value) {
  _internal_set_primary_target_velocity_bottom_mph(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedingVelocity.primary_target_velocity_bottom_mph)
}

// double secondary_target_velocity_top_mph = 7;
inline void WeedingVelocity::clear_secondary_target_velocity_top_mph() {
  secondary_target_velocity_top_mph_ = 0;
}
inline double WeedingVelocity::_internal_secondary_target_velocity_top_mph() const {
  return secondary_target_velocity_top_mph_;
}
inline double WeedingVelocity::secondary_target_velocity_top_mph() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.secondary_target_velocity_top_mph)
  return _internal_secondary_target_velocity_top_mph();
}
inline void WeedingVelocity::_internal_set_secondary_target_velocity_top_mph(double value) {
  
  secondary_target_velocity_top_mph_ = value;
}
inline void WeedingVelocity::set_secondary_target_velocity_top_mph(double value) {
  _internal_set_secondary_target_velocity_top_mph(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedingVelocity.secondary_target_velocity_top_mph)
}

// double secondary_target_velocity_bottom_mph = 8;
inline void WeedingVelocity::clear_secondary_target_velocity_bottom_mph() {
  secondary_target_velocity_bottom_mph_ = 0;
}
inline double WeedingVelocity::_internal_secondary_target_velocity_bottom_mph() const {
  return secondary_target_velocity_bottom_mph_;
}
inline double WeedingVelocity::secondary_target_velocity_bottom_mph() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.secondary_target_velocity_bottom_mph)
  return _internal_secondary_target_velocity_bottom_mph();
}
inline void WeedingVelocity::_internal_set_secondary_target_velocity_bottom_mph(double value) {
  
  secondary_target_velocity_bottom_mph_ = value;
}
inline void WeedingVelocity::set_secondary_target_velocity_bottom_mph(double value) {
  _internal_set_secondary_target_velocity_bottom_mph(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedingVelocity.secondary_target_velocity_bottom_mph)
}

// double cruise_control_velocity_mph = 9;
inline void WeedingVelocity::clear_cruise_control_velocity_mph() {
  cruise_control_velocity_mph_ = 0;
}
inline double WeedingVelocity::_internal_cruise_control_velocity_mph() const {
  return cruise_control_velocity_mph_;
}
inline double WeedingVelocity::cruise_control_velocity_mph() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.WeedingVelocity.cruise_control_velocity_mph)
  return _internal_cruise_control_velocity_mph();
}
inline void WeedingVelocity::_internal_set_cruise_control_velocity_mph(double value) {
  
  cruise_control_velocity_mph_ = value;
}
inline void WeedingVelocity::set_cruise_control_velocity_mph(double value) {
  _internal_set_cruise_control_velocity_mph(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.WeedingVelocity.cruise_control_velocity_mph)
}

// -------------------------------------------------------------------

// RowSpacing

// double width = 1;
inline void RowSpacing::clear_width() {
  width_ = 0;
}
inline double RowSpacing::_internal_width() const {
  return width_;
}
inline double RowSpacing::width() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.RowSpacing.width)
  return _internal_width();
}
inline void RowSpacing::_internal_set_width(double value) {
  
  width_ = value;
}
inline void RowSpacing::set_width(double value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.RowSpacing.width)
}

// -------------------------------------------------------------------

// CruiseEnable

// bool enabled = 1;
inline void CruiseEnable::clear_enabled() {
  enabled_ = false;
}
inline bool CruiseEnable::_internal_enabled() const {
  return enabled_;
}
inline bool CruiseEnable::enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.dashboard.CruiseEnable.enabled)
  return _internal_enabled();
}
inline void CruiseEnable::_internal_set_enabled(bool value) {
  
  enabled_ = value;
}
inline void CruiseEnable::set_enabled(bool value) {
  _internal_set_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.dashboard.CruiseEnable.enabled)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace dashboard
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::dashboard::SafetyOverrideState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::dashboard::SafetyOverrideState>() {
  return ::carbon::frontend::dashboard::SafetyOverrideState_descriptor();
}
template <> struct is_proto_enum< ::carbon::frontend::dashboard::ImplementState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::dashboard::ImplementState>() {
  return ::carbon::frontend::dashboard::ImplementState_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdashboard_2eproto

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import tractor_pb2 as frontend_dot_proto_dot_tractor__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class TractorServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextTractorIfState = channel.unary_unary(
                '/carbon.frontend.tractor.TractorService/GetNextTractorIfState',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_tractor__pb2.GetNextTractorIfStateResponse.FromString,
                )
        self.GetNextTractorSafetyState = channel.unary_unary(
                '/carbon.frontend.tractor.TractorService/GetNextTractorSafetyState',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_tractor__pb2.GetNextTractorSafetyStateResponse.FromString,
                )
        self.SetEnforcementPolicy = channel.unary_unary(
                '/carbon.frontend.tractor.TractorService/SetEnforcementPolicy',
                request_serializer=frontend_dot_proto_dot_tractor__pb2.SetEnforcementPolicyRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_tractor__pb2.SetEnforcementPolicyResponse.FromString,
                )


class TractorServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextTractorIfState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextTractorSafetyState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetEnforcementPolicy(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_TractorServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextTractorIfState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextTractorIfState,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_tractor__pb2.GetNextTractorIfStateResponse.SerializeToString,
            ),
            'GetNextTractorSafetyState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextTractorSafetyState,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_tractor__pb2.GetNextTractorSafetyStateResponse.SerializeToString,
            ),
            'SetEnforcementPolicy': grpc.unary_unary_rpc_method_handler(
                    servicer.SetEnforcementPolicy,
                    request_deserializer=frontend_dot_proto_dot_tractor__pb2.SetEnforcementPolicyRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_tractor__pb2.SetEnforcementPolicyResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.tractor.TractorService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class TractorService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextTractorIfState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.tractor.TractorService/GetNextTractorIfState',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_tractor__pb2.GetNextTractorIfStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextTractorSafetyState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.tractor.TractorService/GetNextTractorSafetyState',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_tractor__pb2.GetNextTractorSafetyStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetEnforcementPolicy(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.tractor.TractorService/SetEnforcementPolicy',
            frontend_dot_proto_dot_tractor__pb2.SetEnforcementPolicyRequest.SerializeToString,
            frontend_dot_proto_dot_tractor__pb2.SetEnforcementPolicyResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

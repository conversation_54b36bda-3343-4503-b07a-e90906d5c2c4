# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/plant_captcha.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2
from generated.proto.almanac import almanac_pb2 as proto_dot_almanac_dot_almanac__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/plant_captcha.proto',
  package='carbon.frontend.plant_captcha',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\"frontend/proto/plant_captcha.proto\x12\x1d\x63\x61rbon.frontend.plant_captcha\x1a\x19\x66rontend/proto/util.proto\x1a\'weed_tracking/proto/weed_tracking.proto\x1a\x1bproto/almanac/almanac.proto\"|\n\x0cPlantCaptcha\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08model_id\x18\x02 \x01(\t\x12\x0f\n\x07\x63rop_id\x18\x03 \x01(\t\x12\x11\n\tcrop_name\x18\x04 \x01(\t\x12\x15\n\rstart_time_ms\x18\x05 \x01(\x03\x12\x11\n\trows_used\x18\x06 \x03(\x05\"^\n\x18StartPlantCaptchaRequest\x12\x42\n\rplant_captcha\x18\x01 \x01(\x0b\x32+.carbon.frontend.plant_captcha.PlantCaptcha\"\x1b\n\x19StartPlantCaptchaResponse\"O\n GetNextPlantCaptchaStatusRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\xc7\x01\n!GetNextPlantCaptchaStatusResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x31\n\x06status\x18\x02 \x01(\x0e\x32!.weed_tracking.PlantCaptchaStatus\x12\x14\n\x0ctotal_images\x18\x03 \x01(\x05\x12\x14\n\x0cimages_taken\x18\x04 \x01(\x05\x12\x16\n\x0emetadata_taken\x18\x05 \x01(\x05\"N\n\x1fGetNextPlantCaptchasListRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\x8a\x01\n\x14PlantCaptchaListItem\x12\x42\n\rplant_captcha\x18\x01 \x01(\x0b\x32+.carbon.frontend.plant_captcha.PlantCaptcha\x12\x14\n\x0cimages_taken\x18\x02 \x01(\x05\x12\x18\n\x10images_processed\x18\x03 \x01(\x05\"\x9c\x01\n GetNextPlantCaptchasListResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12K\n\x0eplant_captchas\x18\x02 \x03(\x0b\x32\x33.carbon.frontend.plant_captcha.PlantCaptchaListItem\")\n\x19\x44\x65letePlantCaptchaRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"&\n\x16GetPlantCaptchaRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\xc6\x01\n\x10PlantCaptchaItem\x12\x11\n\timage_url\x18\x01 \x01(\t\x12\x39\n\x08metadata\x18\x02 \x01(\x0b\x32\'.weed_tracking.PlantCaptchaItemMetadata\x12\x1d\n\x15\x61\x64\x64itional_image_urls\x18\x03 \x03(\t\x12\x45\n\x14\x61\x64\x64itional_metadatas\x18\x04 \x03(\x0b\x32\'.weed_tracking.PlantCaptchaItemMetadata\"\x9d\x01\n\x17GetPlantCaptchaResponse\x12\x42\n\rplant_captcha\x18\x01 \x01(\x0b\x32+.carbon.frontend.plant_captcha.PlantCaptcha\x12>\n\x05items\x18\x02 \x03(\x0b\x32/.carbon.frontend.plant_captcha.PlantCaptchaItem\".\n\x1eStartPlantCaptchaUploadRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"b\n%GetNextPlantCaptchaUploadStateRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0c\n\x04name\x18\x02 \x01(\t\"\xb4\x01\n&GetNextPlantCaptchaUploadStateResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12L\n\x0cupload_state\x18\x02 \x01(\x0e\x32\x36.carbon.frontend.plant_captcha.PlantCaptchaUploadState\x12\x0f\n\x07percent\x18\x03 \x01(\x05\"h\n\x16PlantCaptchaItemResult\x12\n\n\x02id\x18\x01 \x01(\t\x12\x42\n\x0fuser_prediction\x18\x02 \x01(\x0e\x32).weed_tracking.PlantCaptchaUserPrediction\"x\n SubmitPlantCaptchaResultsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x46\n\x07results\x18\x02 \x03(\x0b\x32\x35.carbon.frontend.plant_captcha.PlantCaptchaItemResult\"=\n!GetPlantCaptchaItemResultsRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\n\n\x02id\x18\x02 \x03(\t\"l\n\"GetPlantCaptchaItemResultsResponse\x12\x46\n\x07results\x18\x01 \x03(\x0b\x32\x35.carbon.frontend.plant_captcha.PlantCaptchaItemResult\",\n\x1c\x43\x61lculatePlantCaptchaRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"\xd1\x01\n\x1d\x43\x61lculatePlantCaptchaResponse\x12\x44\n\x12modelinator_config\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\x12\x11\n\tsucceeded\x18\x02 \x01(\x08\x12W\n\x0e\x66\x61ilure_reason\x18\x03 \x01(\x0e\x32?.carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason\"\x89\x01\n\x12PlantCaptchaResult\x12\x38\n\x05label\x18\x01 \x01(\x0e\x32).weed_tracking.PlantCaptchaUserPrediction\x12\x39\n\x08metadata\x18\x02 \x01(\x0b\x32\'.weed_tracking.PlantCaptchaItemMetadata\"\xc5\x08\n\x13PlantCaptchaResults\x12\x44\n\x12\x63urrent_parameters\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\x12J\n\x0f\x63\x61ptcha_results\x18\x02 \x03(\x0b\x32\x31.carbon.frontend.plant_captcha.PlantCaptchaResult\x12\x11\n\talgorithm\x18\x03 \x01(\t\x12\x1b\n\x13goal_crops_targeted\x18\x04 \x01(\x02\x12\x1b\n\x13goal_weeds_targeted\x18\x05 \x01(\x02\x12\x1d\n\x15goal_unknown_targeted\x18\x06 \x01(\x02\x12\x35\n\x07\x61lmanac\x18\x07 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfig\x12\x1e\n\x16max_recommended_mindoo\x18\x08 \x01(\x02\x12$\n\x1cmin_items_for_recommendation\x18\t \x01(\x05\x12+\n#use_weed_categories_for_weed_labels\x18\n \x01(\x08\x12\x1e\n\x16min_recommended_mindoo\x18\x0b \x01(\x02\x12&\n\x1emin_recommended_weed_threshold\x18\x0c \x01(\x02\x12&\n\x1emax_recommended_weed_threshold\x18\r \x01(\x02\x12&\n\x1emin_recommended_crop_threshold\x18\x0e \x01(\x02\x12&\n\x1emax_recommended_crop_threshold\x18\x0f \x01(\x02\x12\"\n\x1amin_doo_for_recommendation\x18\x10 \x01(\x02\x12\x1f\n\x17use_other_as_tiebreaker\x18\x11 \x01(\x08\x12\x1d\n\x15limit_by_crops_missed\x18\x12 \x01(\x08\x12%\n\x1dnumber_of_crop_configurations\x18\x13 \x01(\x05\x12\x12\n\ntiebreaker\x18\x14 \x01(\t\x12\x1f\n\x17pad_crop_configurations\x18\x15 \x01(\x08\x12\x19\n\x11mindoo_tiebreaker\x18\x16 \x01(\t\x12 \n\x18use_beneficials_as_crops\x18\x17 \x01(\x08\x12\x1f\n\x17use_volunteers_as_weeds\x18\x18 \x01(\x08\x12*\n\"tiebreaker_strategy_threshold_weed\x18\x19 \x01(\t\x12*\n\"tiebreaker_strategy_threshold_crop\x18\x1a \x01(\t\x12\'\n\x1ftiebreaker_strategy_mindoo_weed\x18\x1b \x01(\t\x12\'\n\x1ftiebreaker_strategy_mindoo_crop\x18\x1c \x01(\t\"x\n\x1bVeselkaPlantCaptchaResponse\x12\x46\n\x14new_model_parameters\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\x12\x11\n\tsucceeded\x18\x02 \x01(\x08\"3\n#GetOriginalModelinatorConfigRequest\x12\x0c\n\x04name\x18\x01 \x01(\t\"l\n$GetOriginalModelinatorConfigResponse\x12\x44\n\x12modelinator_config\x18\x01 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfig\"\xd9\x01\n\x1bGetCaptchaRowStatusResponse\x12]\n\nrow_status\x18\x01 \x03(\x0b\x32I.carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.RowStatusEntry\x1a[\n\x0eRowStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x38\n\x05value\x18\x02 \x01(\x0b\x32).weed_tracking.PlantCaptchaStatusResponse:\x02\x38\x01\"0\n\x1e\x43\x61ncelPlantCaptchaOnRowRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x05*>\n\x17PlantCaptchaUploadState\x12\x08\n\x04NONE\x10\x00\x12\x0f\n\x0bIN_PROGRESS\x10\x01\x12\x08\n\x04\x44ONE\x10\x02*]\n PlantLabelAlgorithmFailureReason\x12\x0e\n\nNO_FAILURE\x10\x00\x12\x13\n\x0fMETRICS_NOT_MET\x10\x01\x12\x14\n\x10NOT_ENOUGH_ITEMS\x10\x02\x32\x89\x0f\n\x13PlantCaptchaService\x12\x86\x01\n\x11StartPlantCaptcha\x12\x37.carbon.frontend.plant_captcha.StartPlantCaptchaRequest\x1a\x38.carbon.frontend.plant_captcha.StartPlantCaptchaResponse\x12\x9e\x01\n\x19GetNextPlantCaptchaStatus\x12?.carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest\<EMAIL>.plant_captcha.GetNextPlantCaptchaStatusResponse\x12\x9b\x01\n\x18GetNextPlantCaptchasList\x12>.carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest\x1a?.carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse\x12k\n\x12\x44\x65letePlantCaptcha\x12\x38.carbon.frontend.plant_captcha.DeletePlantCaptchaRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x80\x01\n\x0fGetPlantCaptcha\x12\x35.carbon.frontend.plant_captcha.GetPlantCaptchaRequest\x1a\x36.carbon.frontend.plant_captcha.GetPlantCaptchaResponse\x12N\n\x12\x43\x61ncelPlantCaptcha\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12u\n\x17StartPlantCaptchaUpload\x12=.carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest\x1a\x1b.carbon.frontend.util.Empty\x12\xad\x01\n\x1eGetNextPlantCaptchaUploadState\x12\x44.carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest\x1a\x45.carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse\x12y\n\x19SubmitPlantCaptchaResults\x12?.carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest\x1a\x1b.carbon.frontend.util.Empty\x12\xa1\x01\n\x1aGetPlantCaptchaItemResults\<EMAIL>.plant_captcha.GetPlantCaptchaItemResultsRequest\x1a\x41.carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse\x12\x92\x01\n\x15\x43\x61lculatePlantCaptcha\x12;.carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest\x1a<.carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse\x12\xa7\x01\n\x1cGetOriginalModelinatorConfig\x12\x42.carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest\x1a\x43.carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse\x12n\n\x13GetCaptchaRowStatus\x12\x1b.carbon.frontend.util.Empty\x1a:.carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse\x12u\n\x17\x43\x61ncelPlantCaptchaOnRow\x12=.carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,weed__tracking_dot_proto_dot_weed__tracking__pb2.DESCRIPTOR,proto_dot_almanac_dot_almanac__pb2.DESCRIPTOR,])

_PLANTCAPTCHAUPLOADSTATE = _descriptor.EnumDescriptor(
  name='PlantCaptchaUploadState',
  full_name='carbon.frontend.plant_captcha.PlantCaptchaUploadState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NONE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IN_PROGRESS', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DONE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4305,
  serialized_end=4367,
)
_sym_db.RegisterEnumDescriptor(_PLANTCAPTCHAUPLOADSTATE)

PlantCaptchaUploadState = enum_type_wrapper.EnumTypeWrapper(_PLANTCAPTCHAUPLOADSTATE)
_PLANTLABELALGORITHMFAILUREREASON = _descriptor.EnumDescriptor(
  name='PlantLabelAlgorithmFailureReason',
  full_name='carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NO_FAILURE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='METRICS_NOT_MET', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='NOT_ENOUGH_ITEMS', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=4369,
  serialized_end=4462,
)
_sym_db.RegisterEnumDescriptor(_PLANTLABELALGORITHMFAILUREREASON)

PlantLabelAlgorithmFailureReason = enum_type_wrapper.EnumTypeWrapper(_PLANTLABELALGORITHMFAILUREREASON)
NONE = 0
IN_PROGRESS = 1
DONE = 2
NO_FAILURE = 0
METRICS_NOT_MET = 1
NOT_ENOUGH_ITEMS = 2



_PLANTCAPTCHA = _descriptor.Descriptor(
  name='PlantCaptcha',
  full_name='carbon.frontend.plant_captcha.PlantCaptcha',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.PlantCaptcha.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.frontend.plant_captcha.PlantCaptcha.model_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.plant_captcha.PlantCaptcha.crop_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_name', full_name='carbon.frontend.plant_captcha.PlantCaptcha.crop_name', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='start_time_ms', full_name='carbon.frontend.plant_captcha.PlantCaptcha.start_time_ms', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rows_used', full_name='carbon.frontend.plant_captcha.PlantCaptcha.rows_used', index=5,
      number=6, type=5, cpp_type=1, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=166,
  serialized_end=290,
)


_STARTPLANTCAPTCHAREQUEST = _descriptor.Descriptor(
  name='StartPlantCaptchaRequest',
  full_name='carbon.frontend.plant_captcha.StartPlantCaptchaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='plant_captcha', full_name='carbon.frontend.plant_captcha.StartPlantCaptchaRequest.plant_captcha', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=292,
  serialized_end=386,
)


_STARTPLANTCAPTCHARESPONSE = _descriptor.Descriptor(
  name='StartPlantCaptchaResponse',
  full_name='carbon.frontend.plant_captcha.StartPlantCaptchaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=388,
  serialized_end=415,
)


_GETNEXTPLANTCAPTCHASTATUSREQUEST = _descriptor.Descriptor(
  name='GetNextPlantCaptchaStatusRequest',
  full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=417,
  serialized_end=496,
)


_GETNEXTPLANTCAPTCHASTATUSRESPONSE = _descriptor.Descriptor(
  name='GetNextPlantCaptchaStatusResponse',
  full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.status', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='total_images', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.total_images', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='images_taken', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.images_taken', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metadata_taken', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.metadata_taken', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=499,
  serialized_end=698,
)


_GETNEXTPLANTCAPTCHASLISTREQUEST = _descriptor.Descriptor(
  name='GetNextPlantCaptchasListRequest',
  full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=700,
  serialized_end=778,
)


_PLANTCAPTCHALISTITEM = _descriptor.Descriptor(
  name='PlantCaptchaListItem',
  full_name='carbon.frontend.plant_captcha.PlantCaptchaListItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='plant_captcha', full_name='carbon.frontend.plant_captcha.PlantCaptchaListItem.plant_captcha', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='images_taken', full_name='carbon.frontend.plant_captcha.PlantCaptchaListItem.images_taken', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='images_processed', full_name='carbon.frontend.plant_captcha.PlantCaptchaListItem.images_processed', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=781,
  serialized_end=919,
)


_GETNEXTPLANTCAPTCHASLISTRESPONSE = _descriptor.Descriptor(
  name='GetNextPlantCaptchasListResponse',
  full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_captchas', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.plant_captchas', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=922,
  serialized_end=1078,
)


_DELETEPLANTCAPTCHAREQUEST = _descriptor.Descriptor(
  name='DeletePlantCaptchaRequest',
  full_name='carbon.frontend.plant_captcha.DeletePlantCaptchaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.DeletePlantCaptchaRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1080,
  serialized_end=1121,
)


_GETPLANTCAPTCHAREQUEST = _descriptor.Descriptor(
  name='GetPlantCaptchaRequest',
  full_name='carbon.frontend.plant_captcha.GetPlantCaptchaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.GetPlantCaptchaRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1123,
  serialized_end=1161,
)


_PLANTCAPTCHAITEM = _descriptor.Descriptor(
  name='PlantCaptchaItem',
  full_name='carbon.frontend.plant_captcha.PlantCaptchaItem',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='image_url', full_name='carbon.frontend.plant_captcha.PlantCaptchaItem.image_url', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='carbon.frontend.plant_captcha.PlantCaptchaItem.metadata', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='additional_image_urls', full_name='carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='additional_metadatas', full_name='carbon.frontend.plant_captcha.PlantCaptchaItem.additional_metadatas', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1164,
  serialized_end=1362,
)


_GETPLANTCAPTCHARESPONSE = _descriptor.Descriptor(
  name='GetPlantCaptchaResponse',
  full_name='carbon.frontend.plant_captcha.GetPlantCaptchaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='plant_captcha', full_name='carbon.frontend.plant_captcha.GetPlantCaptchaResponse.plant_captcha', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='items', full_name='carbon.frontend.plant_captcha.GetPlantCaptchaResponse.items', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1365,
  serialized_end=1522,
)


_STARTPLANTCAPTCHAUPLOADREQUEST = _descriptor.Descriptor(
  name='StartPlantCaptchaUploadRequest',
  full_name='carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1524,
  serialized_end=1570,
)


_GETNEXTPLANTCAPTCHAUPLOADSTATEREQUEST = _descriptor.Descriptor(
  name='GetNextPlantCaptchaUploadStateRequest',
  full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1572,
  serialized_end=1670,
)


_GETNEXTPLANTCAPTCHAUPLOADSTATERESPONSE = _descriptor.Descriptor(
  name='GetNextPlantCaptchaUploadStateResponse',
  full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='upload_state', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.upload_state', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='percent', full_name='carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.percent', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1673,
  serialized_end=1853,
)


_PLANTCAPTCHAITEMRESULT = _descriptor.Descriptor(
  name='PlantCaptchaItemResult',
  full_name='carbon.frontend.plant_captcha.PlantCaptchaItemResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.plant_captcha.PlantCaptchaItemResult.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='user_prediction', full_name='carbon.frontend.plant_captcha.PlantCaptchaItemResult.user_prediction', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1855,
  serialized_end=1959,
)


_SUBMITPLANTCAPTCHARESULTSREQUEST = _descriptor.Descriptor(
  name='SubmitPlantCaptchaResultsRequest',
  full_name='carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='results', full_name='carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.results', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1961,
  serialized_end=2081,
)


_GETPLANTCAPTCHAITEMRESULTSREQUEST = _descriptor.Descriptor(
  name='GetPlantCaptchaItemResultsRequest',
  full_name='carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id', index=1,
      number=2, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2083,
  serialized_end=2144,
)


_GETPLANTCAPTCHAITEMRESULTSRESPONSE = _descriptor.Descriptor(
  name='GetPlantCaptchaItemResultsResponse',
  full_name='carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='results', full_name='carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse.results', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2146,
  serialized_end=2254,
)


_CALCULATEPLANTCAPTCHAREQUEST = _descriptor.Descriptor(
  name='CalculatePlantCaptchaRequest',
  full_name='carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2256,
  serialized_end=2300,
)


_CALCULATEPLANTCAPTCHARESPONSE = _descriptor.Descriptor(
  name='CalculatePlantCaptchaResponse',
  full_name='carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='modelinator_config', full_name='carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.modelinator_config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='succeeded', full_name='carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.succeeded', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='failure_reason', full_name='carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.failure_reason', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2303,
  serialized_end=2512,
)


_PLANTCAPTCHARESULT = _descriptor.Descriptor(
  name='PlantCaptchaResult',
  full_name='carbon.frontend.plant_captcha.PlantCaptchaResult',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='label', full_name='carbon.frontend.plant_captcha.PlantCaptchaResult.label', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='carbon.frontend.plant_captcha.PlantCaptchaResult.metadata', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2515,
  serialized_end=2652,
)


_PLANTCAPTCHARESULTS = _descriptor.Descriptor(
  name='PlantCaptchaResults',
  full_name='carbon.frontend.plant_captcha.PlantCaptchaResults',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_parameters', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.current_parameters', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='captcha_results', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.captcha_results', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='algorithm', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.algorithm', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='goal_crops_targeted', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.goal_crops_targeted', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='goal_weeds_targeted', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.goal_weeds_targeted', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='goal_unknown_targeted', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.goal_unknown_targeted', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='almanac', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.almanac', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_recommended_mindoo', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_mindoo', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_items_for_recommendation', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.min_items_for_recommendation', index=8,
      number=9, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_weed_categories_for_weed_labels', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.use_weed_categories_for_weed_labels', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_recommended_mindoo', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_mindoo', index=10,
      number=11, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_recommended_weed_threshold', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_weed_threshold', index=11,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_recommended_weed_threshold', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_weed_threshold', index=12,
      number=13, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_recommended_crop_threshold', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_crop_threshold', index=13,
      number=14, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_recommended_crop_threshold', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_crop_threshold', index=14,
      number=15, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_doo_for_recommendation', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.min_doo_for_recommendation', index=15,
      number=16, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_other_as_tiebreaker', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.use_other_as_tiebreaker', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='limit_by_crops_missed', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.limit_by_crops_missed', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='number_of_crop_configurations', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.number_of_crop_configurations', index=18,
      number=19, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tiebreaker', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker', index=19,
      number=20, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pad_crop_configurations', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.pad_crop_configurations', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mindoo_tiebreaker', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.mindoo_tiebreaker', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_beneficials_as_crops', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.use_beneficials_as_crops', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_volunteers_as_weeds', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.use_volunteers_as_weeds', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tiebreaker_strategy_threshold_weed', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_weed', index=24,
      number=25, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tiebreaker_strategy_threshold_crop', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_crop', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tiebreaker_strategy_mindoo_weed', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_weed', index=26,
      number=27, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tiebreaker_strategy_mindoo_crop', full_name='carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_crop', index=27,
      number=28, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2655,
  serialized_end=3748,
)


_VESELKAPLANTCAPTCHARESPONSE = _descriptor.Descriptor(
  name='VeselkaPlantCaptchaResponse',
  full_name='carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='new_model_parameters', full_name='carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.new_model_parameters', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='succeeded', full_name='carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.succeeded', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3750,
  serialized_end=3870,
)


_GETORIGINALMODELINATORCONFIGREQUEST = _descriptor.Descriptor(
  name='GetOriginalModelinatorConfigRequest',
  full_name='carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3872,
  serialized_end=3923,
)


_GETORIGINALMODELINATORCONFIGRESPONSE = _descriptor.Descriptor(
  name='GetOriginalModelinatorConfigResponse',
  full_name='carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='modelinator_config', full_name='carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse.modelinator_config', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3925,
  serialized_end=4033,
)


_GETCAPTCHAROWSTATUSRESPONSE_ROWSTATUSENTRY = _descriptor.Descriptor(
  name='RowStatusEntry',
  full_name='carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.RowStatusEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.RowStatusEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.RowStatusEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4162,
  serialized_end=4253,
)

_GETCAPTCHAROWSTATUSRESPONSE = _descriptor.Descriptor(
  name='GetCaptchaRowStatusResponse',
  full_name='carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_status', full_name='carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.row_status', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETCAPTCHAROWSTATUSRESPONSE_ROWSTATUSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4036,
  serialized_end=4253,
)


_CANCELPLANTCAPTCHAONROWREQUEST = _descriptor.Descriptor(
  name='CancelPlantCaptchaOnRowRequest',
  full_name='carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest.row_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4255,
  serialized_end=4303,
)

_STARTPLANTCAPTCHAREQUEST.fields_by_name['plant_captcha'].message_type = _PLANTCAPTCHA
_GETNEXTPLANTCAPTCHASTATUSREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTPLANTCAPTCHASTATUSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTPLANTCAPTCHASTATUSRESPONSE.fields_by_name['status'].enum_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._PLANTCAPTCHASTATUS
_GETNEXTPLANTCAPTCHASLISTREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_PLANTCAPTCHALISTITEM.fields_by_name['plant_captcha'].message_type = _PLANTCAPTCHA
_GETNEXTPLANTCAPTCHASLISTRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTPLANTCAPTCHASLISTRESPONSE.fields_by_name['plant_captchas'].message_type = _PLANTCAPTCHALISTITEM
_PLANTCAPTCHAITEM.fields_by_name['metadata'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._PLANTCAPTCHAITEMMETADATA
_PLANTCAPTCHAITEM.fields_by_name['additional_metadatas'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._PLANTCAPTCHAITEMMETADATA
_GETPLANTCAPTCHARESPONSE.fields_by_name['plant_captcha'].message_type = _PLANTCAPTCHA
_GETPLANTCAPTCHARESPONSE.fields_by_name['items'].message_type = _PLANTCAPTCHAITEM
_GETNEXTPLANTCAPTCHAUPLOADSTATEREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTPLANTCAPTCHAUPLOADSTATERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTPLANTCAPTCHAUPLOADSTATERESPONSE.fields_by_name['upload_state'].enum_type = _PLANTCAPTCHAUPLOADSTATE
_PLANTCAPTCHAITEMRESULT.fields_by_name['user_prediction'].enum_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._PLANTCAPTCHAUSERPREDICTION
_SUBMITPLANTCAPTCHARESULTSREQUEST.fields_by_name['results'].message_type = _PLANTCAPTCHAITEMRESULT
_GETPLANTCAPTCHAITEMRESULTSRESPONSE.fields_by_name['results'].message_type = _PLANTCAPTCHAITEMRESULT
_CALCULATEPLANTCAPTCHARESPONSE.fields_by_name['modelinator_config'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_CALCULATEPLANTCAPTCHARESPONSE.fields_by_name['failure_reason'].enum_type = _PLANTLABELALGORITHMFAILUREREASON
_PLANTCAPTCHARESULT.fields_by_name['label'].enum_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._PLANTCAPTCHAUSERPREDICTION
_PLANTCAPTCHARESULT.fields_by_name['metadata'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._PLANTCAPTCHAITEMMETADATA
_PLANTCAPTCHARESULTS.fields_by_name['current_parameters'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_PLANTCAPTCHARESULTS.fields_by_name['captcha_results'].message_type = _PLANTCAPTCHARESULT
_PLANTCAPTCHARESULTS.fields_by_name['almanac'].message_type = proto_dot_almanac_dot_almanac__pb2._ALMANACCONFIG
_VESELKAPLANTCAPTCHARESPONSE.fields_by_name['new_model_parameters'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_GETORIGINALMODELINATORCONFIGRESPONSE.fields_by_name['modelinator_config'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_GETCAPTCHAROWSTATUSRESPONSE_ROWSTATUSENTRY.fields_by_name['value'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._PLANTCAPTCHASTATUSRESPONSE
_GETCAPTCHAROWSTATUSRESPONSE_ROWSTATUSENTRY.containing_type = _GETCAPTCHAROWSTATUSRESPONSE
_GETCAPTCHAROWSTATUSRESPONSE.fields_by_name['row_status'].message_type = _GETCAPTCHAROWSTATUSRESPONSE_ROWSTATUSENTRY
DESCRIPTOR.message_types_by_name['PlantCaptcha'] = _PLANTCAPTCHA
DESCRIPTOR.message_types_by_name['StartPlantCaptchaRequest'] = _STARTPLANTCAPTCHAREQUEST
DESCRIPTOR.message_types_by_name['StartPlantCaptchaResponse'] = _STARTPLANTCAPTCHARESPONSE
DESCRIPTOR.message_types_by_name['GetNextPlantCaptchaStatusRequest'] = _GETNEXTPLANTCAPTCHASTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetNextPlantCaptchaStatusResponse'] = _GETNEXTPLANTCAPTCHASTATUSRESPONSE
DESCRIPTOR.message_types_by_name['GetNextPlantCaptchasListRequest'] = _GETNEXTPLANTCAPTCHASLISTREQUEST
DESCRIPTOR.message_types_by_name['PlantCaptchaListItem'] = _PLANTCAPTCHALISTITEM
DESCRIPTOR.message_types_by_name['GetNextPlantCaptchasListResponse'] = _GETNEXTPLANTCAPTCHASLISTRESPONSE
DESCRIPTOR.message_types_by_name['DeletePlantCaptchaRequest'] = _DELETEPLANTCAPTCHAREQUEST
DESCRIPTOR.message_types_by_name['GetPlantCaptchaRequest'] = _GETPLANTCAPTCHAREQUEST
DESCRIPTOR.message_types_by_name['PlantCaptchaItem'] = _PLANTCAPTCHAITEM
DESCRIPTOR.message_types_by_name['GetPlantCaptchaResponse'] = _GETPLANTCAPTCHARESPONSE
DESCRIPTOR.message_types_by_name['StartPlantCaptchaUploadRequest'] = _STARTPLANTCAPTCHAUPLOADREQUEST
DESCRIPTOR.message_types_by_name['GetNextPlantCaptchaUploadStateRequest'] = _GETNEXTPLANTCAPTCHAUPLOADSTATEREQUEST
DESCRIPTOR.message_types_by_name['GetNextPlantCaptchaUploadStateResponse'] = _GETNEXTPLANTCAPTCHAUPLOADSTATERESPONSE
DESCRIPTOR.message_types_by_name['PlantCaptchaItemResult'] = _PLANTCAPTCHAITEMRESULT
DESCRIPTOR.message_types_by_name['SubmitPlantCaptchaResultsRequest'] = _SUBMITPLANTCAPTCHARESULTSREQUEST
DESCRIPTOR.message_types_by_name['GetPlantCaptchaItemResultsRequest'] = _GETPLANTCAPTCHAITEMRESULTSREQUEST
DESCRIPTOR.message_types_by_name['GetPlantCaptchaItemResultsResponse'] = _GETPLANTCAPTCHAITEMRESULTSRESPONSE
DESCRIPTOR.message_types_by_name['CalculatePlantCaptchaRequest'] = _CALCULATEPLANTCAPTCHAREQUEST
DESCRIPTOR.message_types_by_name['CalculatePlantCaptchaResponse'] = _CALCULATEPLANTCAPTCHARESPONSE
DESCRIPTOR.message_types_by_name['PlantCaptchaResult'] = _PLANTCAPTCHARESULT
DESCRIPTOR.message_types_by_name['PlantCaptchaResults'] = _PLANTCAPTCHARESULTS
DESCRIPTOR.message_types_by_name['VeselkaPlantCaptchaResponse'] = _VESELKAPLANTCAPTCHARESPONSE
DESCRIPTOR.message_types_by_name['GetOriginalModelinatorConfigRequest'] = _GETORIGINALMODELINATORCONFIGREQUEST
DESCRIPTOR.message_types_by_name['GetOriginalModelinatorConfigResponse'] = _GETORIGINALMODELINATORCONFIGRESPONSE
DESCRIPTOR.message_types_by_name['GetCaptchaRowStatusResponse'] = _GETCAPTCHAROWSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['CancelPlantCaptchaOnRowRequest'] = _CANCELPLANTCAPTCHAONROWREQUEST
DESCRIPTOR.enum_types_by_name['PlantCaptchaUploadState'] = _PLANTCAPTCHAUPLOADSTATE
DESCRIPTOR.enum_types_by_name['PlantLabelAlgorithmFailureReason'] = _PLANTLABELALGORITHMFAILUREREASON
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PlantCaptcha = _reflection.GeneratedProtocolMessageType('PlantCaptcha', (_message.Message,), {
  'DESCRIPTOR' : _PLANTCAPTCHA,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptcha)
  })
_sym_db.RegisterMessage(PlantCaptcha)

StartPlantCaptchaRequest = _reflection.GeneratedProtocolMessageType('StartPlantCaptchaRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTPLANTCAPTCHAREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
  })
_sym_db.RegisterMessage(StartPlantCaptchaRequest)

StartPlantCaptchaResponse = _reflection.GeneratedProtocolMessageType('StartPlantCaptchaResponse', (_message.Message,), {
  'DESCRIPTOR' : _STARTPLANTCAPTCHARESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.StartPlantCaptchaResponse)
  })
_sym_db.RegisterMessage(StartPlantCaptchaResponse)

GetNextPlantCaptchaStatusRequest = _reflection.GeneratedProtocolMessageType('GetNextPlantCaptchaStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTPLANTCAPTCHASTATUSREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
  })
_sym_db.RegisterMessage(GetNextPlantCaptchaStatusRequest)

GetNextPlantCaptchaStatusResponse = _reflection.GeneratedProtocolMessageType('GetNextPlantCaptchaStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTPLANTCAPTCHASTATUSRESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
  })
_sym_db.RegisterMessage(GetNextPlantCaptchaStatusResponse)

GetNextPlantCaptchasListRequest = _reflection.GeneratedProtocolMessageType('GetNextPlantCaptchasListRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTPLANTCAPTCHASLISTREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
  })
_sym_db.RegisterMessage(GetNextPlantCaptchasListRequest)

PlantCaptchaListItem = _reflection.GeneratedProtocolMessageType('PlantCaptchaListItem', (_message.Message,), {
  'DESCRIPTOR' : _PLANTCAPTCHALISTITEM,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaListItem)
  })
_sym_db.RegisterMessage(PlantCaptchaListItem)

GetNextPlantCaptchasListResponse = _reflection.GeneratedProtocolMessageType('GetNextPlantCaptchasListResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTPLANTCAPTCHASLISTRESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
  })
_sym_db.RegisterMessage(GetNextPlantCaptchasListResponse)

DeletePlantCaptchaRequest = _reflection.GeneratedProtocolMessageType('DeletePlantCaptchaRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETEPLANTCAPTCHAREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
  })
_sym_db.RegisterMessage(DeletePlantCaptchaRequest)

GetPlantCaptchaRequest = _reflection.GeneratedProtocolMessageType('GetPlantCaptchaRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPLANTCAPTCHAREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
  })
_sym_db.RegisterMessage(GetPlantCaptchaRequest)

PlantCaptchaItem = _reflection.GeneratedProtocolMessageType('PlantCaptchaItem', (_message.Message,), {
  'DESCRIPTOR' : _PLANTCAPTCHAITEM,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaItem)
  })
_sym_db.RegisterMessage(PlantCaptchaItem)

GetPlantCaptchaResponse = _reflection.GeneratedProtocolMessageType('GetPlantCaptchaResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETPLANTCAPTCHARESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
  })
_sym_db.RegisterMessage(GetPlantCaptchaResponse)

StartPlantCaptchaUploadRequest = _reflection.GeneratedProtocolMessageType('StartPlantCaptchaUploadRequest', (_message.Message,), {
  'DESCRIPTOR' : _STARTPLANTCAPTCHAUPLOADREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
  })
_sym_db.RegisterMessage(StartPlantCaptchaUploadRequest)

GetNextPlantCaptchaUploadStateRequest = _reflection.GeneratedProtocolMessageType('GetNextPlantCaptchaUploadStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTPLANTCAPTCHAUPLOADSTATEREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
  })
_sym_db.RegisterMessage(GetNextPlantCaptchaUploadStateRequest)

GetNextPlantCaptchaUploadStateResponse = _reflection.GeneratedProtocolMessageType('GetNextPlantCaptchaUploadStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTPLANTCAPTCHAUPLOADSTATERESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
  })
_sym_db.RegisterMessage(GetNextPlantCaptchaUploadStateResponse)

PlantCaptchaItemResult = _reflection.GeneratedProtocolMessageType('PlantCaptchaItemResult', (_message.Message,), {
  'DESCRIPTOR' : _PLANTCAPTCHAITEMRESULT,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
  })
_sym_db.RegisterMessage(PlantCaptchaItemResult)

SubmitPlantCaptchaResultsRequest = _reflection.GeneratedProtocolMessageType('SubmitPlantCaptchaResultsRequest', (_message.Message,), {
  'DESCRIPTOR' : _SUBMITPLANTCAPTCHARESULTSREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
  })
_sym_db.RegisterMessage(SubmitPlantCaptchaResultsRequest)

GetPlantCaptchaItemResultsRequest = _reflection.GeneratedProtocolMessageType('GetPlantCaptchaItemResultsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPLANTCAPTCHAITEMRESULTSREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
  })
_sym_db.RegisterMessage(GetPlantCaptchaItemResultsRequest)

GetPlantCaptchaItemResultsResponse = _reflection.GeneratedProtocolMessageType('GetPlantCaptchaItemResultsResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETPLANTCAPTCHAITEMRESULTSRESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
  })
_sym_db.RegisterMessage(GetPlantCaptchaItemResultsResponse)

CalculatePlantCaptchaRequest = _reflection.GeneratedProtocolMessageType('CalculatePlantCaptchaRequest', (_message.Message,), {
  'DESCRIPTOR' : _CALCULATEPLANTCAPTCHAREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
  })
_sym_db.RegisterMessage(CalculatePlantCaptchaRequest)

CalculatePlantCaptchaResponse = _reflection.GeneratedProtocolMessageType('CalculatePlantCaptchaResponse', (_message.Message,), {
  'DESCRIPTOR' : _CALCULATEPLANTCAPTCHARESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
  })
_sym_db.RegisterMessage(CalculatePlantCaptchaResponse)

PlantCaptchaResult = _reflection.GeneratedProtocolMessageType('PlantCaptchaResult', (_message.Message,), {
  'DESCRIPTOR' : _PLANTCAPTCHARESULT,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaResult)
  })
_sym_db.RegisterMessage(PlantCaptchaResult)

PlantCaptchaResults = _reflection.GeneratedProtocolMessageType('PlantCaptchaResults', (_message.Message,), {
  'DESCRIPTOR' : _PLANTCAPTCHARESULTS,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaResults)
  })
_sym_db.RegisterMessage(PlantCaptchaResults)

VeselkaPlantCaptchaResponse = _reflection.GeneratedProtocolMessageType('VeselkaPlantCaptchaResponse', (_message.Message,), {
  'DESCRIPTOR' : _VESELKAPLANTCAPTCHARESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
  })
_sym_db.RegisterMessage(VeselkaPlantCaptchaResponse)

GetOriginalModelinatorConfigRequest = _reflection.GeneratedProtocolMessageType('GetOriginalModelinatorConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETORIGINALMODELINATORCONFIGREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
  })
_sym_db.RegisterMessage(GetOriginalModelinatorConfigRequest)

GetOriginalModelinatorConfigResponse = _reflection.GeneratedProtocolMessageType('GetOriginalModelinatorConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETORIGINALMODELINATORCONFIGRESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
  })
_sym_db.RegisterMessage(GetOriginalModelinatorConfigResponse)

GetCaptchaRowStatusResponse = _reflection.GeneratedProtocolMessageType('GetCaptchaRowStatusResponse', (_message.Message,), {

  'RowStatusEntry' : _reflection.GeneratedProtocolMessageType('RowStatusEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETCAPTCHAROWSTATUSRESPONSE_ROWSTATUSENTRY,
    '__module__' : 'frontend.proto.plant_captcha_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.RowStatusEntry)
    })
  ,
  'DESCRIPTOR' : _GETCAPTCHAROWSTATUSRESPONSE,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
  })
_sym_db.RegisterMessage(GetCaptchaRowStatusResponse)
_sym_db.RegisterMessage(GetCaptchaRowStatusResponse.RowStatusEntry)

CancelPlantCaptchaOnRowRequest = _reflection.GeneratedProtocolMessageType('CancelPlantCaptchaOnRowRequest', (_message.Message,), {
  'DESCRIPTOR' : _CANCELPLANTCAPTCHAONROWREQUEST,
  '__module__' : 'frontend.proto.plant_captcha_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
  })
_sym_db.RegisterMessage(CancelPlantCaptchaOnRowRequest)


DESCRIPTOR._options = None
_GETCAPTCHAROWSTATUSRESPONSE_ROWSTATUSENTRY._options = None

_PLANTCAPTCHASERVICE = _descriptor.ServiceDescriptor(
  name='PlantCaptchaService',
  full_name='carbon.frontend.plant_captcha.PlantCaptchaService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=4465,
  serialized_end=6394,
  methods=[
  _descriptor.MethodDescriptor(
    name='StartPlantCaptcha',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.StartPlantCaptcha',
    index=0,
    containing_service=None,
    input_type=_STARTPLANTCAPTCHAREQUEST,
    output_type=_STARTPLANTCAPTCHARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextPlantCaptchaStatus',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchaStatus',
    index=1,
    containing_service=None,
    input_type=_GETNEXTPLANTCAPTCHASTATUSREQUEST,
    output_type=_GETNEXTPLANTCAPTCHASTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextPlantCaptchasList',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchasList',
    index=2,
    containing_service=None,
    input_type=_GETNEXTPLANTCAPTCHASLISTREQUEST,
    output_type=_GETNEXTPLANTCAPTCHASLISTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeletePlantCaptcha',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.DeletePlantCaptcha',
    index=3,
    containing_service=None,
    input_type=_DELETEPLANTCAPTCHAREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetPlantCaptcha',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.GetPlantCaptcha',
    index=4,
    containing_service=None,
    input_type=_GETPLANTCAPTCHAREQUEST,
    output_type=_GETPLANTCAPTCHARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CancelPlantCaptcha',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.CancelPlantCaptcha',
    index=5,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartPlantCaptchaUpload',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.StartPlantCaptchaUpload',
    index=6,
    containing_service=None,
    input_type=_STARTPLANTCAPTCHAUPLOADREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextPlantCaptchaUploadState',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.GetNextPlantCaptchaUploadState',
    index=7,
    containing_service=None,
    input_type=_GETNEXTPLANTCAPTCHAUPLOADSTATEREQUEST,
    output_type=_GETNEXTPLANTCAPTCHAUPLOADSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SubmitPlantCaptchaResults',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.SubmitPlantCaptchaResults',
    index=8,
    containing_service=None,
    input_type=_SUBMITPLANTCAPTCHARESULTSREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetPlantCaptchaItemResults',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.GetPlantCaptchaItemResults',
    index=9,
    containing_service=None,
    input_type=_GETPLANTCAPTCHAITEMRESULTSREQUEST,
    output_type=_GETPLANTCAPTCHAITEMRESULTSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CalculatePlantCaptcha',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.CalculatePlantCaptcha',
    index=10,
    containing_service=None,
    input_type=_CALCULATEPLANTCAPTCHAREQUEST,
    output_type=_CALCULATEPLANTCAPTCHARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetOriginalModelinatorConfig',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.GetOriginalModelinatorConfig',
    index=11,
    containing_service=None,
    input_type=_GETORIGINALMODELINATORCONFIGREQUEST,
    output_type=_GETORIGINALMODELINATORCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCaptchaRowStatus',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.GetCaptchaRowStatus',
    index=12,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_GETCAPTCHAROWSTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CancelPlantCaptchaOnRow',
    full_name='carbon.frontend.plant_captcha.PlantCaptchaService.CancelPlantCaptchaOnRow',
    index=13,
    containing_service=None,
    input_type=_CANCELPLANTCAPTCHAONROWREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_PLANTCAPTCHASERVICE)

DESCRIPTOR.services_by_name['PlantCaptchaService'] = _PLANTCAPTCHASERVICE

# @@protoc_insertion_point(module_scope)

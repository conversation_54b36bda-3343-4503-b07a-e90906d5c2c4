"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class CrosshairPosition(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___float = ...
    y: builtin___float = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x",u"y",b"y"]) -> None: ...
type___CrosshairPosition = CrosshairPosition

class CrosshairPositionState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    calibrating: builtin___bool = ...
    calibration_failed: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def pos(self) -> type___CrosshairPosition: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        pos : typing___Optional[type___CrosshairPosition] = None,
        calibrating : typing___Optional[builtin___bool] = None,
        calibration_failed : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pos",b"pos",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"calibrating",b"calibrating",u"calibration_failed",b"calibration_failed",u"pos",b"pos",u"ts",b"ts"]) -> None: ...
type___CrosshairPositionState = CrosshairPositionState

class CrosshairPositionRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"ts",b"ts"]) -> None: ...
type___CrosshairPositionRequest = CrosshairPositionRequest

class SetCrosshairPositionRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...

    @property
    def pos(self) -> type___CrosshairPosition: ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        pos : typing___Optional[type___CrosshairPosition] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"pos",b"pos"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"pos",b"pos"]) -> None: ...
type___SetCrosshairPositionRequest = SetCrosshairPositionRequest

class MoveScannerRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    cam_id: typing___Text = ...
    x: builtin___float = ...
    y: builtin___float = ...

    def __init__(self,
        *,
        cam_id : typing___Optional[typing___Text] = None,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"cam_id",b"cam_id",u"x",b"x",u"y",b"y"]) -> None: ...
type___MoveScannerRequest = MoveScannerRequest

class AutoCrossHairCalStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___AutoCrossHairCalStateRequest = AutoCrossHairCalStateRequest

class AutoCrossHairCalStateResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    in_progress: builtin___bool = ...
    progress: builtin___float = ...
    failed: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        in_progress : typing___Optional[builtin___bool] = None,
        progress : typing___Optional[builtin___float] = None,
        failed : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"failed",b"failed",u"in_progress",b"in_progress",u"progress",b"progress",u"ts",b"ts"]) -> None: ...
type___AutoCrossHairCalStateResponse = AutoCrossHairCalStateResponse

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/dashboard.proto

#include "frontend/proto/dashboard.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace dashboard {
constexpr ExtraStatus::ExtraStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : title_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , icon_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , icon_color_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , status_text_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , status_color_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , group_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , section_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , bottom_text_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , progress_(0)
  , width_(0u){}
struct ExtraStatusDefaultTypeInternal {
  constexpr ExtraStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ExtraStatusDefaultTypeInternal() {}
  union {
    ExtraStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ExtraStatusDefaultTypeInternal _ExtraStatus_default_instance_;
constexpr WeedTargeting::WeedTargeting(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabled_(false){}
struct WeedTargetingDefaultTypeInternal {
  constexpr WeedTargetingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~WeedTargetingDefaultTypeInternal() {}
  union {
    WeedTargeting _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT WeedTargetingDefaultTypeInternal _WeedTargeting_default_instance_;
constexpr ThinningTargeting::ThinningTargeting(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : algorithm_(uint64_t{0u})
  , enabled_(false){}
struct ThinningTargetingDefaultTypeInternal {
  constexpr ThinningTargetingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ThinningTargetingDefaultTypeInternal() {}
  union {
    ThinningTargeting _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ThinningTargetingDefaultTypeInternal _ThinningTargeting_default_instance_;
constexpr TargetingState_EnabledRowsEntry_DoNotUse::TargetingState_EnabledRowsEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct TargetingState_EnabledRowsEntry_DoNotUseDefaultTypeInternal {
  constexpr TargetingState_EnabledRowsEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TargetingState_EnabledRowsEntry_DoNotUseDefaultTypeInternal() {}
  union {
    TargetingState_EnabledRowsEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TargetingState_EnabledRowsEntry_DoNotUseDefaultTypeInternal _TargetingState_EnabledRowsEntry_DoNotUse_default_instance_;
constexpr TargetingState::TargetingState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabled_()
  , enabled_rows_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , weed_state_(nullptr)
  , thinning_state_(nullptr){}
struct TargetingStateDefaultTypeInternal {
  constexpr TargetingStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TargetingStateDefaultTypeInternal() {}
  union {
    TargetingState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TargetingStateDefaultTypeInternal _TargetingState_default_instance_;
constexpr ExtraConclusion::ExtraConclusion(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : title_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , percent_(nullptr)
  , flip_thresholds_(false)
  , good_threshold_percent_(0u)
  , medium_threshold_percent_(0u){}
struct ExtraConclusionDefaultTypeInternal {
  constexpr ExtraConclusionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ExtraConclusionDefaultTypeInternal() {}
  union {
    ExtraConclusion _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ExtraConclusionDefaultTypeInternal _ExtraConclusion_default_instance_;
constexpr RowStateMessage::RowStateMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabled_(false)
  , target_state_mismatch_(false)
  , ready_(false)
  , safety_override_state_(0)
{}
struct RowStateMessageDefaultTypeInternal {
  constexpr RowStateMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RowStateMessageDefaultTypeInternal() {}
  union {
    RowStateMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RowStateMessageDefaultTypeInternal _RowStateMessage_default_instance_;
constexpr DashboardStateMessage_RowStatesEntry_DoNotUse::DashboardStateMessage_RowStatesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct DashboardStateMessage_RowStatesEntry_DoNotUseDefaultTypeInternal {
  constexpr DashboardStateMessage_RowStatesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DashboardStateMessage_RowStatesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    DashboardStateMessage_RowStatesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DashboardStateMessage_RowStatesEntry_DoNotUseDefaultTypeInternal _DashboardStateMessage_RowStatesEntry_DoNotUse_default_instance_;
constexpr DashboardStateMessage::DashboardStateMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_enabled_()
  , extras_()
  , target_state_mismatch_()
  , row_ready_()
  , row_exists_()
  , safety_override_state_()
  , _safety_override_state_cached_byte_size_(0)
  , extra_conclusions_()
  , row_states_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , cruise_disallowed_reason_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , selected_model_(nullptr)
  , targeting_state_(nullptr)
  , efficiency_percent_(nullptr)
  , error_rate_(nullptr)
  , area_weeded_today_(nullptr)
  , area_weeded_total_(nullptr)
  , weeds_killed_today_(nullptr)
  , weeds_killed_total_(nullptr)
  , time_weeded_today_(nullptr)
  , time_weeded_total_(nullptr)
  , crops_killed_today_(nullptr)
  , crops_killed_total_(nullptr)
  , row_width_in_(0)
  , implement_state_(0)

  , lasers_enabled_(false)
  , efficiency_enabled_(false)
  , error_rate_enabled_(false)
  , weeding_enabled_(false)
  , debug_mode_(false)
  , cruise_enabled_(false)
  , cruise_allow_enable_(false){}
struct DashboardStateMessageDefaultTypeInternal {
  constexpr DashboardStateMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DashboardStateMessageDefaultTypeInternal() {}
  union {
    DashboardStateMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DashboardStateMessageDefaultTypeInternal _DashboardStateMessage_default_instance_;
constexpr CropModel::CropModel(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , has_model_(false)
  , preferred_(false){}
struct CropModelDefaultTypeInternal {
  constexpr CropModelDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CropModelDefaultTypeInternal() {}
  union {
    CropModel _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CropModelDefaultTypeInternal _CropModel_default_instance_;
constexpr CropModelOptions::CropModelOptions(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : models_(){}
struct CropModelOptionsDefaultTypeInternal {
  constexpr CropModelOptionsDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CropModelOptionsDefaultTypeInternal() {}
  union {
    CropModelOptions _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CropModelOptionsDefaultTypeInternal _CropModelOptions_default_instance_;
constexpr RowId::RowId(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : row_number_(0u){}
struct RowIdDefaultTypeInternal {
  constexpr RowIdDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RowIdDefaultTypeInternal() {}
  union {
    RowId _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RowIdDefaultTypeInternal _RowId_default_instance_;
constexpr WeedingVelocity::WeedingVelocity(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , current_velocity_mph_(0)
  , target_velocity_mph_(0)
  , tolerance_mph_(0)
  , primary_target_velocity_top_mph_(0)
  , primary_target_velocity_bottom_mph_(0)
  , secondary_target_velocity_top_mph_(0)
  , secondary_target_velocity_bottom_mph_(0)
  , cruise_control_velocity_mph_(0){}
struct WeedingVelocityDefaultTypeInternal {
  constexpr WeedingVelocityDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~WeedingVelocityDefaultTypeInternal() {}
  union {
    WeedingVelocity _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT WeedingVelocityDefaultTypeInternal _WeedingVelocity_default_instance_;
constexpr RowSpacing::RowSpacing(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : width_(0){}
struct RowSpacingDefaultTypeInternal {
  constexpr RowSpacingDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RowSpacingDefaultTypeInternal() {}
  union {
    RowSpacing _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RowSpacingDefaultTypeInternal _RowSpacing_default_instance_;
constexpr CruiseEnable::CruiseEnable(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabled_(false){}
struct CruiseEnableDefaultTypeInternal {
  constexpr CruiseEnableDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CruiseEnableDefaultTypeInternal() {}
  union {
    CruiseEnable _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CruiseEnableDefaultTypeInternal _CruiseEnable_default_instance_;
}  // namespace dashboard
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fdashboard_2eproto[15];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fdashboard_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fdashboard_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fdashboard_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, title_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, icon_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, icon_color_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, status_text_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, status_color_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, group_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, section_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, progress_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, width_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraStatus, bottom_text_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedTargeting, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedTargeting, enabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ThinningTargeting, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ThinningTargeting, enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ThinningTargeting, algorithm_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState, weed_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState, thinning_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState, enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::TargetingState, enabled_rows_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraConclusion, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraConclusion, title_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraConclusion, flip_thresholds_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraConclusion, good_threshold_percent_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraConclusion, medium_threshold_percent_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::ExtraConclusion, percent_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowStateMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowStateMessage, enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowStateMessage, target_state_mismatch_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowStateMessage, ready_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowStateMessage, safety_override_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, lasers_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, row_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, extras_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, selected_model_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, targeting_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, target_state_mismatch_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, row_ready_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, row_exists_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, row_width_in_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, safety_override_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, implement_state_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, efficiency_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, efficiency_percent_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, error_rate_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, error_rate_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, extra_conclusions_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, area_weeded_today_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, area_weeded_total_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, weeds_killed_today_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, weeds_killed_total_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, time_weeded_today_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, time_weeded_total_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, weeding_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, debug_mode_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, crops_killed_today_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, crops_killed_total_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, cruise_enabled_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, row_states_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, cruise_allow_enable_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::DashboardStateMessage, cruise_disallowed_reason_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CropModel, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CropModel, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CropModel, has_model_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CropModel, preferred_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CropModel, crop_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CropModelOptions, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CropModelOptions, models_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowId, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowId, row_number_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, current_velocity_mph_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, target_velocity_mph_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, tolerance_mph_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, primary_target_velocity_top_mph_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, primary_target_velocity_bottom_mph_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, secondary_target_velocity_top_mph_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, secondary_target_velocity_bottom_mph_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::WeedingVelocity, cruise_control_velocity_mph_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowSpacing, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::RowSpacing, width_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CruiseEnable, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::dashboard::CruiseEnable, enabled_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::dashboard::ExtraStatus)},
  { 16, -1, -1, sizeof(::carbon::frontend::dashboard::WeedTargeting)},
  { 23, -1, -1, sizeof(::carbon::frontend::dashboard::ThinningTargeting)},
  { 31, 39, -1, sizeof(::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse)},
  { 41, -1, -1, sizeof(::carbon::frontend::dashboard::TargetingState)},
  { 51, -1, -1, sizeof(::carbon::frontend::dashboard::ExtraConclusion)},
  { 62, -1, -1, sizeof(::carbon::frontend::dashboard::RowStateMessage)},
  { 72, 80, -1, sizeof(::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse)},
  { 82, -1, -1, sizeof(::carbon::frontend::dashboard::DashboardStateMessage)},
  { 119, -1, -1, sizeof(::carbon::frontend::dashboard::CropModel)},
  { 129, -1, -1, sizeof(::carbon::frontend::dashboard::CropModelOptions)},
  { 136, -1, -1, sizeof(::carbon::frontend::dashboard::RowId)},
  { 143, -1, -1, sizeof(::carbon::frontend::dashboard::WeedingVelocity)},
  { 158, -1, -1, sizeof(::carbon::frontend::dashboard::RowSpacing)},
  { 165, -1, -1, sizeof(::carbon::frontend::dashboard::CruiseEnable)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_ExtraStatus_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_WeedTargeting_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_ThinningTargeting_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_TargetingState_EnabledRowsEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_TargetingState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_ExtraConclusion_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_RowStateMessage_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_DashboardStateMessage_RowStatesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_DashboardStateMessage_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_CropModel_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_CropModelOptions_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_RowId_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_WeedingVelocity_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_RowSpacing_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::dashboard::_CruiseEnable_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fdashboard_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\036frontend/proto/dashboard.proto\022\031carbon"
  ".frontend.dashboard\032 frontend/proto/tran"
  "slation.proto\032\031frontend/proto/util.proto"
  "\"\312\001\n\013ExtraStatus\022\r\n\005title\030\001 \001(\t\022\021\n\ticon_"
  "name\030\002 \001(\t\022\022\n\nicon_color\030\003 \001(\t\022\023\n\013status"
  "_text\030\004 \001(\t\022\024\n\014status_color\030\005 \001(\t\022\020\n\010gro"
  "up_id\030\006 \001(\t\022\022\n\nsection_id\030\007 \001(\t\022\020\n\010progr"
  "ess\030\010 \001(\001\022\r\n\005width\030\t \001(\r\022\023\n\013bottom_text\030"
  "\n \001(\t\" \n\rWeedTargeting\022\017\n\007enabled\030\001 \001(\010\""
  ";\n\021ThinningTargeting\022\017\n\007enabled\030\001 \001(\010\022\025\n"
  "\talgorithm\030\002 \001(\004B\002\030\001\"\257\002\n\016TargetingState\022"
  "<\n\nweed_state\030\001 \001(\0132(.carbon.frontend.da"
  "shboard.WeedTargeting\022D\n\016thinning_state\030"
  "\002 \001(\0132,.carbon.frontend.dashboard.Thinni"
  "ngTargeting\022\023\n\007enabled\030\003 \003(\010B\002\030\001\022P\n\014enab"
  "led_rows\030\004 \003(\0132:.carbon.frontend.dashboa"
  "rd.TargetingState.EnabledRowsEntry\0322\n\020En"
  "abledRowsEntry\022\013\n\003key\030\001 \001(\005\022\r\n\005value\030\002 \001"
  "(\010:\0028\001\"\267\001\n\017ExtraConclusion\022\r\n\005title\030\001 \001("
  "\t\022\027\n\017flip_thresholds\030\002 \001(\010\022\036\n\026good_thres"
  "hold_percent\030\003 \001(\r\022 \n\030medium_threshold_p"
  "ercent\030\004 \001(\r\022:\n\007percent\030\005 \001(\0132).carbon.f"
  "rontend.translation.PercentValue\"\237\001\n\017Row"
  "StateMessage\022\017\n\007enabled\030\001 \001(\010\022\035\n\025target_"
  "state_mismatch\030\002 \001(\010\022\r\n\005ready\030\003 \001(\010\022M\n\025s"
  "afety_override_state\030\004 \001(\0162..carbon.fron"
  "tend.dashboard.SafetyOverrideState\"\240\r\n\025D"
  "ashboardStateMessage\022+\n\002ts\030\001 \001(\0132\037.carbo"
  "n.frontend.util.Timestamp\022\026\n\016lasers_enab"
  "led\030\002 \001(\010\022\027\n\013row_enabled\030\003 \003(\010B\002\030\001\0226\n\006ex"
  "tras\030\004 \003(\0132&.carbon.frontend.dashboard.E"
  "xtraStatus\022<\n\016selected_model\030\005 \001(\0132$.car"
  "bon.frontend.dashboard.CropModel\022B\n\017targ"
  "eting_state\030\006 \001(\0132).carbon.frontend.dash"
  "board.TargetingState\022!\n\025target_state_mis"
  "match\030\007 \003(\010B\002\030\001\022\025\n\trow_ready\030\010 \003(\010B\002\030\001\022\026"
  "\n\nrow_exists\030\t \003(\010B\002\030\001\022\024\n\014row_width_in\030\n"
  " \001(\001\022Q\n\025safety_override_state\030\013 \003(\0162..ca"
  "rbon.frontend.dashboard.SafetyOverrideSt"
  "ateB\002\030\001\022B\n\017implement_state\030\r \001(\0162).carbo"
  "n.frontend.dashboard.ImplementState\022\032\n\022e"
  "fficiency_enabled\030\016 \001(\010\022E\n\022efficiency_pe"
  "rcent\030\017 \001(\0132).carbon.frontend.translatio"
  "n.PercentValue\022\032\n\022error_rate_enabled\030\020 \001"
  "(\010\022=\n\nerror_rate\030\021 \001(\0132).carbon.frontend"
  ".translation.PercentValue\022E\n\021extra_concl"
  "usions\030\022 \003(\0132*.carbon.frontend.dashboard"
  ".ExtraConclusion\022A\n\021area_weeded_today\030\023 "
  "\001(\0132&.carbon.frontend.translation.AreaVa"
  "lue\022A\n\021area_weeded_total\030\024 \001(\0132&.carbon."
  "frontend.translation.AreaValue\022E\n\022weeds_"
  "killed_today\030\025 \001(\0132).carbon.frontend.tra"
  "nslation.IntegerValue\022E\n\022weeds_killed_to"
  "tal\030\026 \001(\0132).carbon.frontend.translation."
  "IntegerValue\022E\n\021time_weeded_today\030\027 \001(\0132"
  "*.carbon.frontend.translation.DurationVa"
  "lue\022E\n\021time_weeded_total\030\030 \001(\0132*.carbon."
  "frontend.translation.DurationValue\022\027\n\017we"
  "eding_enabled\030\031 \001(\010\022\022\n\ndebug_mode\030\032 \001(\010\022"
  "E\n\022crops_killed_today\030\033 \001(\0132).carbon.fro"
  "ntend.translation.IntegerValue\022E\n\022crops_"
  "killed_total\030\034 \001(\0132).carbon.frontend.tra"
  "nslation.IntegerValue\022\026\n\016cruise_enabled\030"
  "\035 \001(\010\022S\n\nrow_states\030\036 \003(\0132\?.carbon.front"
  "end.dashboard.DashboardStateMessage.RowS"
  "tatesEntry\022\033\n\023cruise_allow_enable\030\037 \001(\010\022"
  " \n\030cruise_disallowed_reason\030  \001(\t\032\\\n\016Row"
  "StatesEntry\022\013\n\003key\030\001 \001(\005\0229\n\005value\030\002 \001(\0132"
  "*.carbon.frontend.dashboard.RowStateMess"
  "age:\0028\001J\004\010\014\020\r\"T\n\tCropModel\022\020\n\004crop\030\001 \001(\t"
  "B\002\030\001\022\021\n\thas_model\030\002 \001(\010\022\021\n\tpreferred\030\003 \001"
  "(\010\022\017\n\007crop_id\030\004 \001(\t\"H\n\020CropModelOptions\022"
  "4\n\006models\030\001 \003(\0132$.carbon.frontend.dashbo"
  "ard.CropModel\"\033\n\005RowId\022\022\n\nrow_number\030\001 \001"
  "(\r\"\343\002\n\017WeedingVelocity\022+\n\002ts\030\001 \001(\0132\037.car"
  "bon.frontend.util.Timestamp\022\034\n\024current_v"
  "elocity_mph\030\002 \001(\001\022\033\n\023target_velocity_mph"
  "\030\003 \001(\001\022\025\n\rtolerance_mph\030\004 \001(\001\022\'\n\037primary"
  "_target_velocity_top_mph\030\005 \001(\001\022*\n\"primar"
  "y_target_velocity_bottom_mph\030\006 \001(\001\022)\n!se"
  "condary_target_velocity_top_mph\030\007 \001(\001\022,\n"
  "$secondary_target_velocity_bottom_mph\030\010 "
  "\001(\001\022#\n\033cruise_control_velocity_mph\030\t \001(\001"
  "\"\033\n\nRowSpacing\022\r\n\005width\030\001 \001(\001\"\037\n\014CruiseE"
  "nable\022\017\n\007enabled\030\001 \001(\010*M\n\023SafetyOverride"
  "State\022\026\n\022SafetyOverrideNone\020\000\022\036\n\032SafetyO"
  "verrideVelocityStop\020\001*)\n\016ImplementState\022"
  "\n\n\006RAISED\020\000\022\013\n\007LOWERED\020\0012\305\006\n\020DashboardSe"
  "rvice\022J\n\tToggleRow\022 .carbon.frontend.das"
  "hboard.RowId\032\033.carbon.frontend.util.Empt"
  "y\022H\n\014ToggleLasers\022\033.carbon.frontend.util"
  ".Empty\032\033.carbon.frontend.util.Empty\022j\n\025G"
  "etNextDashboardState\022\037.carbon.frontend.u"
  "til.Timestamp\0320.carbon.frontend.dashboar"
  "d.DashboardStateMessage\022d\n\023GetCropModelO"
  "ptions\022\033.carbon.frontend.util.Empty\032+.ca"
  "rbon.frontend.dashboard.CropModelOptions"
  "\"\003\210\002\001\022V\n\014SetCropModel\022$.carbon.frontend."
  "dashboard.CropModel\032\033.carbon.frontend.ut"
  "il.Empty\"\003\210\002\001\022e\n\026GetNextWeedingVelocity\022"
  "\037.carbon.frontend.util.Timestamp\032*.carbo"
  "n.frontend.dashboard.WeedingVelocity\022[\n\021"
  "SetTargetingState\022).carbon.frontend.dash"
  "board.TargetingState\032\033.carbon.frontend.u"
  "til.Empty\022S\n\rSetRowSpacing\022%.carbon.fron"
  "tend.dashboard.RowSpacing\032\033.carbon.front"
  "end.util.Empty\022X\n\020SetCruiseEnabled\022\'.car"
  "bon.frontend.dashboard.CruiseEnable\032\033.ca"
  "rbon.frontend.util.EmptyB\020Z\016proto/fronte"
  "ndb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fdashboard_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2ftranslation_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fdashboard_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fdashboard_2eproto = {
  false, false, 4370, descriptor_table_protodef_frontend_2fproto_2fdashboard_2eproto, "frontend/proto/dashboard.proto", 
  &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once, descriptor_table_frontend_2fproto_2fdashboard_2eproto_deps, 2, 15,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fdashboard_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fdashboard_2eproto, file_level_enum_descriptors_frontend_2fproto_2fdashboard_2eproto, file_level_service_descriptors_frontend_2fproto_2fdashboard_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fdashboard_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fdashboard_2eproto(&descriptor_table_frontend_2fproto_2fdashboard_2eproto);
namespace carbon {
namespace frontend {
namespace dashboard {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* SafetyOverrideState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fdashboard_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fdashboard_2eproto[0];
}
bool SafetyOverrideState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ImplementState_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fdashboard_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fdashboard_2eproto[1];
}
bool ImplementState_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ExtraStatus::_Internal {
 public:
};

ExtraStatus::ExtraStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.ExtraStatus)
}
ExtraStatus::ExtraStatus(const ExtraStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  title_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    title_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_title().empty()) {
    title_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_title(), 
      GetArenaForAllocation());
  }
  icon_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    icon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_icon_name().empty()) {
    icon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_icon_name(), 
      GetArenaForAllocation());
  }
  icon_color_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    icon_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_icon_color().empty()) {
    icon_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_icon_color(), 
      GetArenaForAllocation());
  }
  status_text_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    status_text_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_status_text().empty()) {
    status_text_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_status_text(), 
      GetArenaForAllocation());
  }
  status_color_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    status_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_status_color().empty()) {
    status_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_status_color(), 
      GetArenaForAllocation());
  }
  group_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    group_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_group_id().empty()) {
    group_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_group_id(), 
      GetArenaForAllocation());
  }
  section_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    section_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_section_id().empty()) {
    section_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_section_id(), 
      GetArenaForAllocation());
  }
  bottom_text_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    bottom_text_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_bottom_text().empty()) {
    bottom_text_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_bottom_text(), 
      GetArenaForAllocation());
  }
  ::memcpy(&progress_, &from.progress_,
    static_cast<size_t>(reinterpret_cast<char*>(&width_) -
    reinterpret_cast<char*>(&progress_)) + sizeof(width_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.ExtraStatus)
}

inline void ExtraStatus::SharedCtor() {
title_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  title_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
icon_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  icon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
icon_color_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  icon_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
status_text_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  status_text_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
status_color_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  status_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
group_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  group_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
section_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  section_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
bottom_text_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  bottom_text_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&progress_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&width_) -
    reinterpret_cast<char*>(&progress_)) + sizeof(width_));
}

ExtraStatus::~ExtraStatus() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.ExtraStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ExtraStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  title_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  icon_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  icon_color_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  status_text_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  status_color_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  group_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  section_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  bottom_text_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ExtraStatus::ArenaDtor(void* object) {
  ExtraStatus* _this = reinterpret_cast< ExtraStatus* >(object);
  (void)_this;
}
void ExtraStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ExtraStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ExtraStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.ExtraStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  title_.ClearToEmpty();
  icon_name_.ClearToEmpty();
  icon_color_.ClearToEmpty();
  status_text_.ClearToEmpty();
  status_color_.ClearToEmpty();
  group_id_.ClearToEmpty();
  section_id_.ClearToEmpty();
  bottom_text_.ClearToEmpty();
  ::memset(&progress_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&width_) -
      reinterpret_cast<char*>(&progress_)) + sizeof(width_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ExtraStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string title = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_title();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraStatus.title"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string icon_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_icon_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraStatus.icon_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string icon_color = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_icon_color();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraStatus.icon_color"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string status_text = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_status_text();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraStatus.status_text"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string status_color = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_status_color();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraStatus.status_color"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string group_id = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_group_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraStatus.group_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string section_id = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_section_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraStatus.section_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double progress = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 65)) {
          progress_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // uint32 width = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string bottom_text = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_bottom_text();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraStatus.bottom_text"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ExtraStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.ExtraStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string title = 1;
  if (!this->_internal_title().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_title().data(), static_cast<int>(this->_internal_title().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraStatus.title");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_title(), target);
  }

  // string icon_name = 2;
  if (!this->_internal_icon_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_icon_name().data(), static_cast<int>(this->_internal_icon_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraStatus.icon_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_icon_name(), target);
  }

  // string icon_color = 3;
  if (!this->_internal_icon_color().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_icon_color().data(), static_cast<int>(this->_internal_icon_color().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraStatus.icon_color");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_icon_color(), target);
  }

  // string status_text = 4;
  if (!this->_internal_status_text().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_status_text().data(), static_cast<int>(this->_internal_status_text().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraStatus.status_text");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_status_text(), target);
  }

  // string status_color = 5;
  if (!this->_internal_status_color().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_status_color().data(), static_cast<int>(this->_internal_status_color().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraStatus.status_color");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_status_color(), target);
  }

  // string group_id = 6;
  if (!this->_internal_group_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_group_id().data(), static_cast<int>(this->_internal_group_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraStatus.group_id");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_group_id(), target);
  }

  // string section_id = 7;
  if (!this->_internal_section_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_section_id().data(), static_cast<int>(this->_internal_section_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraStatus.section_id");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_section_id(), target);
  }

  // double progress = 8;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_progress = this->_internal_progress();
  uint64_t raw_progress;
  memcpy(&raw_progress, &tmp_progress, sizeof(tmp_progress));
  if (raw_progress != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(8, this->_internal_progress(), target);
  }

  // uint32 width = 9;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(9, this->_internal_width(), target);
  }

  // string bottom_text = 10;
  if (!this->_internal_bottom_text().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_bottom_text().data(), static_cast<int>(this->_internal_bottom_text().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraStatus.bottom_text");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_bottom_text(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.ExtraStatus)
  return target;
}

size_t ExtraStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.ExtraStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string title = 1;
  if (!this->_internal_title().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_title());
  }

  // string icon_name = 2;
  if (!this->_internal_icon_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icon_name());
  }

  // string icon_color = 3;
  if (!this->_internal_icon_color().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_icon_color());
  }

  // string status_text = 4;
  if (!this->_internal_status_text().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_status_text());
  }

  // string status_color = 5;
  if (!this->_internal_status_color().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_status_color());
  }

  // string group_id = 6;
  if (!this->_internal_group_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_group_id());
  }

  // string section_id = 7;
  if (!this->_internal_section_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_section_id());
  }

  // string bottom_text = 10;
  if (!this->_internal_bottom_text().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_bottom_text());
  }

  // double progress = 8;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_progress = this->_internal_progress();
  uint64_t raw_progress;
  memcpy(&raw_progress, &tmp_progress, sizeof(tmp_progress));
  if (raw_progress != 0) {
    total_size += 1 + 8;
  }

  // uint32 width = 9;
  if (this->_internal_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_width());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ExtraStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ExtraStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ExtraStatus::GetClassData() const { return &_class_data_; }

void ExtraStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ExtraStatus *>(to)->MergeFrom(
      static_cast<const ExtraStatus &>(from));
}


void ExtraStatus::MergeFrom(const ExtraStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.ExtraStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_title().empty()) {
    _internal_set_title(from._internal_title());
  }
  if (!from._internal_icon_name().empty()) {
    _internal_set_icon_name(from._internal_icon_name());
  }
  if (!from._internal_icon_color().empty()) {
    _internal_set_icon_color(from._internal_icon_color());
  }
  if (!from._internal_status_text().empty()) {
    _internal_set_status_text(from._internal_status_text());
  }
  if (!from._internal_status_color().empty()) {
    _internal_set_status_color(from._internal_status_color());
  }
  if (!from._internal_group_id().empty()) {
    _internal_set_group_id(from._internal_group_id());
  }
  if (!from._internal_section_id().empty()) {
    _internal_set_section_id(from._internal_section_id());
  }
  if (!from._internal_bottom_text().empty()) {
    _internal_set_bottom_text(from._internal_bottom_text());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_progress = from._internal_progress();
  uint64_t raw_progress;
  memcpy(&raw_progress, &tmp_progress, sizeof(tmp_progress));
  if (raw_progress != 0) {
    _internal_set_progress(from._internal_progress());
  }
  if (from._internal_width() != 0) {
    _internal_set_width(from._internal_width());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ExtraStatus::CopyFrom(const ExtraStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.ExtraStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExtraStatus::IsInitialized() const {
  return true;
}

void ExtraStatus::InternalSwap(ExtraStatus* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &title_, lhs_arena,
      &other->title_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &icon_name_, lhs_arena,
      &other->icon_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &icon_color_, lhs_arena,
      &other->icon_color_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &status_text_, lhs_arena,
      &other->status_text_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &status_color_, lhs_arena,
      &other->status_color_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &group_id_, lhs_arena,
      &other->group_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &section_id_, lhs_arena,
      &other->section_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &bottom_text_, lhs_arena,
      &other->bottom_text_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ExtraStatus, width_)
      + sizeof(ExtraStatus::width_)
      - PROTOBUF_FIELD_OFFSET(ExtraStatus, progress_)>(
          reinterpret_cast<char*>(&progress_),
          reinterpret_cast<char*>(&other->progress_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ExtraStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[0]);
}

// ===================================================================

class WeedTargeting::_Internal {
 public:
};

WeedTargeting::WeedTargeting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.WeedTargeting)
}
WeedTargeting::WeedTargeting(const WeedTargeting& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enabled_ = from.enabled_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.WeedTargeting)
}

inline void WeedTargeting::SharedCtor() {
enabled_ = false;
}

WeedTargeting::~WeedTargeting() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.WeedTargeting)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void WeedTargeting::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void WeedTargeting::ArenaDtor(void* object) {
  WeedTargeting* _this = reinterpret_cast< WeedTargeting* >(object);
  (void)_this;
}
void WeedTargeting::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WeedTargeting::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void WeedTargeting::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.WeedTargeting)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabled_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WeedTargeting::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WeedTargeting::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.WeedTargeting)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.WeedTargeting)
  return target;
}

size_t WeedTargeting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.WeedTargeting)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WeedTargeting::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    WeedTargeting::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WeedTargeting::GetClassData() const { return &_class_data_; }

void WeedTargeting::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<WeedTargeting *>(to)->MergeFrom(
      static_cast<const WeedTargeting &>(from));
}


void WeedTargeting::MergeFrom(const WeedTargeting& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.WeedTargeting)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WeedTargeting::CopyFrom(const WeedTargeting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.WeedTargeting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WeedTargeting::IsInitialized() const {
  return true;
}

void WeedTargeting::InternalSwap(WeedTargeting* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(enabled_, other->enabled_);
}

::PROTOBUF_NAMESPACE_ID::Metadata WeedTargeting::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[1]);
}

// ===================================================================

class ThinningTargeting::_Internal {
 public:
};

ThinningTargeting::ThinningTargeting(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.ThinningTargeting)
}
ThinningTargeting::ThinningTargeting(const ThinningTargeting& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&algorithm_, &from.algorithm_,
    static_cast<size_t>(reinterpret_cast<char*>(&enabled_) -
    reinterpret_cast<char*>(&algorithm_)) + sizeof(enabled_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.ThinningTargeting)
}

inline void ThinningTargeting::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&algorithm_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&enabled_) -
    reinterpret_cast<char*>(&algorithm_)) + sizeof(enabled_));
}

ThinningTargeting::~ThinningTargeting() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.ThinningTargeting)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ThinningTargeting::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ThinningTargeting::ArenaDtor(void* object) {
  ThinningTargeting* _this = reinterpret_cast< ThinningTargeting* >(object);
  (void)_this;
}
void ThinningTargeting::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ThinningTargeting::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ThinningTargeting::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.ThinningTargeting)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&algorithm_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&enabled_) -
      reinterpret_cast<char*>(&algorithm_)) + sizeof(enabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ThinningTargeting::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 algorithm = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          algorithm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ThinningTargeting::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.ThinningTargeting)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  // uint64 algorithm = 2 [deprecated = true];
  if (this->_internal_algorithm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_algorithm(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.ThinningTargeting)
  return target;
}

size_t ThinningTargeting::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.ThinningTargeting)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 algorithm = 2 [deprecated = true];
  if (this->_internal_algorithm() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_algorithm());
  }

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ThinningTargeting::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ThinningTargeting::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ThinningTargeting::GetClassData() const { return &_class_data_; }

void ThinningTargeting::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ThinningTargeting *>(to)->MergeFrom(
      static_cast<const ThinningTargeting &>(from));
}


void ThinningTargeting::MergeFrom(const ThinningTargeting& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.ThinningTargeting)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_algorithm() != 0) {
    _internal_set_algorithm(from._internal_algorithm());
  }
  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ThinningTargeting::CopyFrom(const ThinningTargeting& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.ThinningTargeting)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ThinningTargeting::IsInitialized() const {
  return true;
}

void ThinningTargeting::InternalSwap(ThinningTargeting* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ThinningTargeting, enabled_)
      + sizeof(ThinningTargeting::enabled_)
      - PROTOBUF_FIELD_OFFSET(ThinningTargeting, algorithm_)>(
          reinterpret_cast<char*>(&algorithm_),
          reinterpret_cast<char*>(&other->algorithm_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ThinningTargeting::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[2]);
}

// ===================================================================

TargetingState_EnabledRowsEntry_DoNotUse::TargetingState_EnabledRowsEntry_DoNotUse() {}
TargetingState_EnabledRowsEntry_DoNotUse::TargetingState_EnabledRowsEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void TargetingState_EnabledRowsEntry_DoNotUse::MergeFrom(const TargetingState_EnabledRowsEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata TargetingState_EnabledRowsEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[3]);
}

// ===================================================================

class TargetingState::_Internal {
 public:
  static const ::carbon::frontend::dashboard::WeedTargeting& weed_state(const TargetingState* msg);
  static const ::carbon::frontend::dashboard::ThinningTargeting& thinning_state(const TargetingState* msg);
};

const ::carbon::frontend::dashboard::WeedTargeting&
TargetingState::_Internal::weed_state(const TargetingState* msg) {
  return *msg->weed_state_;
}
const ::carbon::frontend::dashboard::ThinningTargeting&
TargetingState::_Internal::thinning_state(const TargetingState* msg) {
  return *msg->thinning_state_;
}
TargetingState::TargetingState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  enabled_(arena),
  enabled_rows_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.TargetingState)
}
TargetingState::TargetingState(const TargetingState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      enabled_(from.enabled_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enabled_rows_.MergeFrom(from.enabled_rows_);
  if (from._internal_has_weed_state()) {
    weed_state_ = new ::carbon::frontend::dashboard::WeedTargeting(*from.weed_state_);
  } else {
    weed_state_ = nullptr;
  }
  if (from._internal_has_thinning_state()) {
    thinning_state_ = new ::carbon::frontend::dashboard::ThinningTargeting(*from.thinning_state_);
  } else {
    thinning_state_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.TargetingState)
}

inline void TargetingState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&weed_state_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&thinning_state_) -
    reinterpret_cast<char*>(&weed_state_)) + sizeof(thinning_state_));
}

TargetingState::~TargetingState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.TargetingState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TargetingState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete weed_state_;
  if (this != internal_default_instance()) delete thinning_state_;
}

void TargetingState::ArenaDtor(void* object) {
  TargetingState* _this = reinterpret_cast< TargetingState* >(object);
  (void)_this;
  _this->enabled_rows_. ~MapField();
}
inline void TargetingState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &TargetingState::ArenaDtor);
  }
}
void TargetingState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TargetingState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.TargetingState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabled_.Clear();
  enabled_rows_.Clear();
  if (GetArenaForAllocation() == nullptr && weed_state_ != nullptr) {
    delete weed_state_;
  }
  weed_state_ = nullptr;
  if (GetArenaForAllocation() == nullptr && thinning_state_ != nullptr) {
    delete thinning_state_;
  }
  thinning_state_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TargetingState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.dashboard.WeedTargeting weed_state = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_weed_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.dashboard.ThinningTargeting thinning_state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_thinning_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bool enabled = 3 [deprecated = true];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedBoolParser(_internal_mutable_enabled(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 24) {
          _internal_add_enabled(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<int32, bool> enabled_rows = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&enabled_rows_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TargetingState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.TargetingState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.dashboard.WeedTargeting weed_state = 1;
  if (this->_internal_has_weed_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::weed_state(this), target, stream);
  }

  // .carbon.frontend.dashboard.ThinningTargeting thinning_state = 2;
  if (this->_internal_has_thinning_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::thinning_state(this), target, stream);
  }

  // repeated bool enabled = 3 [deprecated = true];
  if (this->_internal_enabled_size() > 0) {
    target = stream->WriteFixedPacked(3, _internal_enabled(), target);
  }

  // map<int32, bool> enabled_rows = 4;
  if (!this->_internal_enabled_rows().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_enabled_rows().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_enabled_rows().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >::const_iterator
          it = this->_internal_enabled_rows().begin();
          it != this->_internal_enabled_rows().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = TargetingState_EnabledRowsEntry_DoNotUse::Funcs::InternalSerialize(4, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >::const_iterator
          it = this->_internal_enabled_rows().begin();
          it != this->_internal_enabled_rows().end(); ++it) {
        target = TargetingState_EnabledRowsEntry_DoNotUse::Funcs::InternalSerialize(4, it->first, it->second, target, stream);
      }
    }
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.TargetingState)
  return target;
}

size_t TargetingState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.TargetingState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated bool enabled = 3 [deprecated = true];
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_enabled_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // map<int32, bool> enabled_rows = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_enabled_rows_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, bool >::const_iterator
      it = this->_internal_enabled_rows().begin();
      it != this->_internal_enabled_rows().end(); ++it) {
    total_size += TargetingState_EnabledRowsEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // .carbon.frontend.dashboard.WeedTargeting weed_state = 1;
  if (this->_internal_has_weed_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *weed_state_);
  }

  // .carbon.frontend.dashboard.ThinningTargeting thinning_state = 2;
  if (this->_internal_has_thinning_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *thinning_state_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TargetingState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TargetingState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TargetingState::GetClassData() const { return &_class_data_; }

void TargetingState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TargetingState *>(to)->MergeFrom(
      static_cast<const TargetingState &>(from));
}


void TargetingState::MergeFrom(const TargetingState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.TargetingState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  enabled_.MergeFrom(from.enabled_);
  enabled_rows_.MergeFrom(from.enabled_rows_);
  if (from._internal_has_weed_state()) {
    _internal_mutable_weed_state()->::carbon::frontend::dashboard::WeedTargeting::MergeFrom(from._internal_weed_state());
  }
  if (from._internal_has_thinning_state()) {
    _internal_mutable_thinning_state()->::carbon::frontend::dashboard::ThinningTargeting::MergeFrom(from._internal_thinning_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TargetingState::CopyFrom(const TargetingState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.TargetingState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TargetingState::IsInitialized() const {
  return true;
}

void TargetingState::InternalSwap(TargetingState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  enabled_.InternalSwap(&other->enabled_);
  enabled_rows_.InternalSwap(&other->enabled_rows_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TargetingState, thinning_state_)
      + sizeof(TargetingState::thinning_state_)
      - PROTOBUF_FIELD_OFFSET(TargetingState, weed_state_)>(
          reinterpret_cast<char*>(&weed_state_),
          reinterpret_cast<char*>(&other->weed_state_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TargetingState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[4]);
}

// ===================================================================

class ExtraConclusion::_Internal {
 public:
  static const ::carbon::frontend::translation::PercentValue& percent(const ExtraConclusion* msg);
};

const ::carbon::frontend::translation::PercentValue&
ExtraConclusion::_Internal::percent(const ExtraConclusion* msg) {
  return *msg->percent_;
}
void ExtraConclusion::clear_percent() {
  if (GetArenaForAllocation() == nullptr && percent_ != nullptr) {
    delete percent_;
  }
  percent_ = nullptr;
}
ExtraConclusion::ExtraConclusion(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.ExtraConclusion)
}
ExtraConclusion::ExtraConclusion(const ExtraConclusion& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  title_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    title_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_title().empty()) {
    title_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_title(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_percent()) {
    percent_ = new ::carbon::frontend::translation::PercentValue(*from.percent_);
  } else {
    percent_ = nullptr;
  }
  ::memcpy(&flip_thresholds_, &from.flip_thresholds_,
    static_cast<size_t>(reinterpret_cast<char*>(&medium_threshold_percent_) -
    reinterpret_cast<char*>(&flip_thresholds_)) + sizeof(medium_threshold_percent_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.ExtraConclusion)
}

inline void ExtraConclusion::SharedCtor() {
title_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  title_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&percent_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&medium_threshold_percent_) -
    reinterpret_cast<char*>(&percent_)) + sizeof(medium_threshold_percent_));
}

ExtraConclusion::~ExtraConclusion() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.ExtraConclusion)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ExtraConclusion::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  title_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete percent_;
}

void ExtraConclusion::ArenaDtor(void* object) {
  ExtraConclusion* _this = reinterpret_cast< ExtraConclusion* >(object);
  (void)_this;
}
void ExtraConclusion::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ExtraConclusion::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ExtraConclusion::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.ExtraConclusion)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  title_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && percent_ != nullptr) {
    delete percent_;
  }
  percent_ = nullptr;
  ::memset(&flip_thresholds_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&medium_threshold_percent_) -
      reinterpret_cast<char*>(&flip_thresholds_)) + sizeof(medium_threshold_percent_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ExtraConclusion::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string title = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_title();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.ExtraConclusion.title"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool flip_thresholds = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          flip_thresholds_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 good_threshold_percent = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          good_threshold_percent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 medium_threshold_percent = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          medium_threshold_percent_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.PercentValue percent = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_percent(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ExtraConclusion::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.ExtraConclusion)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string title = 1;
  if (!this->_internal_title().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_title().data(), static_cast<int>(this->_internal_title().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.ExtraConclusion.title");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_title(), target);
  }

  // bool flip_thresholds = 2;
  if (this->_internal_flip_thresholds() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_flip_thresholds(), target);
  }

  // uint32 good_threshold_percent = 3;
  if (this->_internal_good_threshold_percent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_good_threshold_percent(), target);
  }

  // uint32 medium_threshold_percent = 4;
  if (this->_internal_medium_threshold_percent() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(4, this->_internal_medium_threshold_percent(), target);
  }

  // .carbon.frontend.translation.PercentValue percent = 5;
  if (this->_internal_has_percent()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::percent(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.ExtraConclusion)
  return target;
}

size_t ExtraConclusion::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.ExtraConclusion)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string title = 1;
  if (!this->_internal_title().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_title());
  }

  // .carbon.frontend.translation.PercentValue percent = 5;
  if (this->_internal_has_percent()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *percent_);
  }

  // bool flip_thresholds = 2;
  if (this->_internal_flip_thresholds() != 0) {
    total_size += 1 + 1;
  }

  // uint32 good_threshold_percent = 3;
  if (this->_internal_good_threshold_percent() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_good_threshold_percent());
  }

  // uint32 medium_threshold_percent = 4;
  if (this->_internal_medium_threshold_percent() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_medium_threshold_percent());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ExtraConclusion::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ExtraConclusion::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ExtraConclusion::GetClassData() const { return &_class_data_; }

void ExtraConclusion::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ExtraConclusion *>(to)->MergeFrom(
      static_cast<const ExtraConclusion &>(from));
}


void ExtraConclusion::MergeFrom(const ExtraConclusion& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.ExtraConclusion)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_title().empty()) {
    _internal_set_title(from._internal_title());
  }
  if (from._internal_has_percent()) {
    _internal_mutable_percent()->::carbon::frontend::translation::PercentValue::MergeFrom(from._internal_percent());
  }
  if (from._internal_flip_thresholds() != 0) {
    _internal_set_flip_thresholds(from._internal_flip_thresholds());
  }
  if (from._internal_good_threshold_percent() != 0) {
    _internal_set_good_threshold_percent(from._internal_good_threshold_percent());
  }
  if (from._internal_medium_threshold_percent() != 0) {
    _internal_set_medium_threshold_percent(from._internal_medium_threshold_percent());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ExtraConclusion::CopyFrom(const ExtraConclusion& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.ExtraConclusion)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ExtraConclusion::IsInitialized() const {
  return true;
}

void ExtraConclusion::InternalSwap(ExtraConclusion* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &title_, lhs_arena,
      &other->title_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ExtraConclusion, medium_threshold_percent_)
      + sizeof(ExtraConclusion::medium_threshold_percent_)
      - PROTOBUF_FIELD_OFFSET(ExtraConclusion, percent_)>(
          reinterpret_cast<char*>(&percent_),
          reinterpret_cast<char*>(&other->percent_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ExtraConclusion::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[5]);
}

// ===================================================================

class RowStateMessage::_Internal {
 public:
};

RowStateMessage::RowStateMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.RowStateMessage)
}
RowStateMessage::RowStateMessage(const RowStateMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&enabled_, &from.enabled_,
    static_cast<size_t>(reinterpret_cast<char*>(&safety_override_state_) -
    reinterpret_cast<char*>(&enabled_)) + sizeof(safety_override_state_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.RowStateMessage)
}

inline void RowStateMessage::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&enabled_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&safety_override_state_) -
    reinterpret_cast<char*>(&enabled_)) + sizeof(safety_override_state_));
}

RowStateMessage::~RowStateMessage() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.RowStateMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RowStateMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RowStateMessage::ArenaDtor(void* object) {
  RowStateMessage* _this = reinterpret_cast< RowStateMessage* >(object);
  (void)_this;
}
void RowStateMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RowStateMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RowStateMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.RowStateMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&enabled_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&safety_override_state_) -
      reinterpret_cast<char*>(&enabled_)) + sizeof(safety_override_state_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RowStateMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool target_state_mismatch = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          target_state_mismatch_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool ready = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          ready_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_safety_override_state(static_cast<::carbon::frontend::dashboard::SafetyOverrideState>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RowStateMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.RowStateMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  // bool target_state_mismatch = 2;
  if (this->_internal_target_state_mismatch() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_target_state_mismatch(), target);
  }

  // bool ready = 3;
  if (this->_internal_ready() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_ready(), target);
  }

  // .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 4;
  if (this->_internal_safety_override_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_safety_override_state(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.RowStateMessage)
  return target;
}

size_t RowStateMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.RowStateMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool target_state_mismatch = 2;
  if (this->_internal_target_state_mismatch() != 0) {
    total_size += 1 + 1;
  }

  // bool ready = 3;
  if (this->_internal_ready() != 0) {
    total_size += 1 + 1;
  }

  // .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 4;
  if (this->_internal_safety_override_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_safety_override_state());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RowStateMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RowStateMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RowStateMessage::GetClassData() const { return &_class_data_; }

void RowStateMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RowStateMessage *>(to)->MergeFrom(
      static_cast<const RowStateMessage &>(from));
}


void RowStateMessage::MergeFrom(const RowStateMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.RowStateMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  if (from._internal_target_state_mismatch() != 0) {
    _internal_set_target_state_mismatch(from._internal_target_state_mismatch());
  }
  if (from._internal_ready() != 0) {
    _internal_set_ready(from._internal_ready());
  }
  if (from._internal_safety_override_state() != 0) {
    _internal_set_safety_override_state(from._internal_safety_override_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RowStateMessage::CopyFrom(const RowStateMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.RowStateMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RowStateMessage::IsInitialized() const {
  return true;
}

void RowStateMessage::InternalSwap(RowStateMessage* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RowStateMessage, safety_override_state_)
      + sizeof(RowStateMessage::safety_override_state_)
      - PROTOBUF_FIELD_OFFSET(RowStateMessage, enabled_)>(
          reinterpret_cast<char*>(&enabled_),
          reinterpret_cast<char*>(&other->enabled_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RowStateMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[6]);
}

// ===================================================================

DashboardStateMessage_RowStatesEntry_DoNotUse::DashboardStateMessage_RowStatesEntry_DoNotUse() {}
DashboardStateMessage_RowStatesEntry_DoNotUse::DashboardStateMessage_RowStatesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void DashboardStateMessage_RowStatesEntry_DoNotUse::MergeFrom(const DashboardStateMessage_RowStatesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata DashboardStateMessage_RowStatesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[7]);
}

// ===================================================================

class DashboardStateMessage::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const DashboardStateMessage* msg);
  static const ::carbon::frontend::dashboard::CropModel& selected_model(const DashboardStateMessage* msg);
  static const ::carbon::frontend::dashboard::TargetingState& targeting_state(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::PercentValue& efficiency_percent(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::PercentValue& error_rate(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::AreaValue& area_weeded_today(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::AreaValue& area_weeded_total(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::IntegerValue& weeds_killed_today(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::IntegerValue& weeds_killed_total(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::DurationValue& time_weeded_today(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::DurationValue& time_weeded_total(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::IntegerValue& crops_killed_today(const DashboardStateMessage* msg);
  static const ::carbon::frontend::translation::IntegerValue& crops_killed_total(const DashboardStateMessage* msg);
};

const ::carbon::frontend::util::Timestamp&
DashboardStateMessage::_Internal::ts(const DashboardStateMessage* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::dashboard::CropModel&
DashboardStateMessage::_Internal::selected_model(const DashboardStateMessage* msg) {
  return *msg->selected_model_;
}
const ::carbon::frontend::dashboard::TargetingState&
DashboardStateMessage::_Internal::targeting_state(const DashboardStateMessage* msg) {
  return *msg->targeting_state_;
}
const ::carbon::frontend::translation::PercentValue&
DashboardStateMessage::_Internal::efficiency_percent(const DashboardStateMessage* msg) {
  return *msg->efficiency_percent_;
}
const ::carbon::frontend::translation::PercentValue&
DashboardStateMessage::_Internal::error_rate(const DashboardStateMessage* msg) {
  return *msg->error_rate_;
}
const ::carbon::frontend::translation::AreaValue&
DashboardStateMessage::_Internal::area_weeded_today(const DashboardStateMessage* msg) {
  return *msg->area_weeded_today_;
}
const ::carbon::frontend::translation::AreaValue&
DashboardStateMessage::_Internal::area_weeded_total(const DashboardStateMessage* msg) {
  return *msg->area_weeded_total_;
}
const ::carbon::frontend::translation::IntegerValue&
DashboardStateMessage::_Internal::weeds_killed_today(const DashboardStateMessage* msg) {
  return *msg->weeds_killed_today_;
}
const ::carbon::frontend::translation::IntegerValue&
DashboardStateMessage::_Internal::weeds_killed_total(const DashboardStateMessage* msg) {
  return *msg->weeds_killed_total_;
}
const ::carbon::frontend::translation::DurationValue&
DashboardStateMessage::_Internal::time_weeded_today(const DashboardStateMessage* msg) {
  return *msg->time_weeded_today_;
}
const ::carbon::frontend::translation::DurationValue&
DashboardStateMessage::_Internal::time_weeded_total(const DashboardStateMessage* msg) {
  return *msg->time_weeded_total_;
}
const ::carbon::frontend::translation::IntegerValue&
DashboardStateMessage::_Internal::crops_killed_today(const DashboardStateMessage* msg) {
  return *msg->crops_killed_today_;
}
const ::carbon::frontend::translation::IntegerValue&
DashboardStateMessage::_Internal::crops_killed_total(const DashboardStateMessage* msg) {
  return *msg->crops_killed_total_;
}
void DashboardStateMessage::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void DashboardStateMessage::clear_efficiency_percent() {
  if (GetArenaForAllocation() == nullptr && efficiency_percent_ != nullptr) {
    delete efficiency_percent_;
  }
  efficiency_percent_ = nullptr;
}
void DashboardStateMessage::clear_error_rate() {
  if (GetArenaForAllocation() == nullptr && error_rate_ != nullptr) {
    delete error_rate_;
  }
  error_rate_ = nullptr;
}
void DashboardStateMessage::clear_area_weeded_today() {
  if (GetArenaForAllocation() == nullptr && area_weeded_today_ != nullptr) {
    delete area_weeded_today_;
  }
  area_weeded_today_ = nullptr;
}
void DashboardStateMessage::clear_area_weeded_total() {
  if (GetArenaForAllocation() == nullptr && area_weeded_total_ != nullptr) {
    delete area_weeded_total_;
  }
  area_weeded_total_ = nullptr;
}
void DashboardStateMessage::clear_weeds_killed_today() {
  if (GetArenaForAllocation() == nullptr && weeds_killed_today_ != nullptr) {
    delete weeds_killed_today_;
  }
  weeds_killed_today_ = nullptr;
}
void DashboardStateMessage::clear_weeds_killed_total() {
  if (GetArenaForAllocation() == nullptr && weeds_killed_total_ != nullptr) {
    delete weeds_killed_total_;
  }
  weeds_killed_total_ = nullptr;
}
void DashboardStateMessage::clear_time_weeded_today() {
  if (GetArenaForAllocation() == nullptr && time_weeded_today_ != nullptr) {
    delete time_weeded_today_;
  }
  time_weeded_today_ = nullptr;
}
void DashboardStateMessage::clear_time_weeded_total() {
  if (GetArenaForAllocation() == nullptr && time_weeded_total_ != nullptr) {
    delete time_weeded_total_;
  }
  time_weeded_total_ = nullptr;
}
void DashboardStateMessage::clear_crops_killed_today() {
  if (GetArenaForAllocation() == nullptr && crops_killed_today_ != nullptr) {
    delete crops_killed_today_;
  }
  crops_killed_today_ = nullptr;
}
void DashboardStateMessage::clear_crops_killed_total() {
  if (GetArenaForAllocation() == nullptr && crops_killed_total_ != nullptr) {
    delete crops_killed_total_;
  }
  crops_killed_total_ = nullptr;
}
DashboardStateMessage::DashboardStateMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  row_enabled_(arena),
  extras_(arena),
  target_state_mismatch_(arena),
  row_ready_(arena),
  row_exists_(arena),
  safety_override_state_(arena),
  extra_conclusions_(arena),
  row_states_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.DashboardStateMessage)
}
DashboardStateMessage::DashboardStateMessage(const DashboardStateMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      row_enabled_(from.row_enabled_),
      extras_(from.extras_),
      target_state_mismatch_(from.target_state_mismatch_),
      row_ready_(from.row_ready_),
      row_exists_(from.row_exists_),
      safety_override_state_(from.safety_override_state_),
      extra_conclusions_(from.extra_conclusions_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_states_.MergeFrom(from.row_states_);
  cruise_disallowed_reason_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cruise_disallowed_reason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cruise_disallowed_reason().empty()) {
    cruise_disallowed_reason_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cruise_disallowed_reason(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_selected_model()) {
    selected_model_ = new ::carbon::frontend::dashboard::CropModel(*from.selected_model_);
  } else {
    selected_model_ = nullptr;
  }
  if (from._internal_has_targeting_state()) {
    targeting_state_ = new ::carbon::frontend::dashboard::TargetingState(*from.targeting_state_);
  } else {
    targeting_state_ = nullptr;
  }
  if (from._internal_has_efficiency_percent()) {
    efficiency_percent_ = new ::carbon::frontend::translation::PercentValue(*from.efficiency_percent_);
  } else {
    efficiency_percent_ = nullptr;
  }
  if (from._internal_has_error_rate()) {
    error_rate_ = new ::carbon::frontend::translation::PercentValue(*from.error_rate_);
  } else {
    error_rate_ = nullptr;
  }
  if (from._internal_has_area_weeded_today()) {
    area_weeded_today_ = new ::carbon::frontend::translation::AreaValue(*from.area_weeded_today_);
  } else {
    area_weeded_today_ = nullptr;
  }
  if (from._internal_has_area_weeded_total()) {
    area_weeded_total_ = new ::carbon::frontend::translation::AreaValue(*from.area_weeded_total_);
  } else {
    area_weeded_total_ = nullptr;
  }
  if (from._internal_has_weeds_killed_today()) {
    weeds_killed_today_ = new ::carbon::frontend::translation::IntegerValue(*from.weeds_killed_today_);
  } else {
    weeds_killed_today_ = nullptr;
  }
  if (from._internal_has_weeds_killed_total()) {
    weeds_killed_total_ = new ::carbon::frontend::translation::IntegerValue(*from.weeds_killed_total_);
  } else {
    weeds_killed_total_ = nullptr;
  }
  if (from._internal_has_time_weeded_today()) {
    time_weeded_today_ = new ::carbon::frontend::translation::DurationValue(*from.time_weeded_today_);
  } else {
    time_weeded_today_ = nullptr;
  }
  if (from._internal_has_time_weeded_total()) {
    time_weeded_total_ = new ::carbon::frontend::translation::DurationValue(*from.time_weeded_total_);
  } else {
    time_weeded_total_ = nullptr;
  }
  if (from._internal_has_crops_killed_today()) {
    crops_killed_today_ = new ::carbon::frontend::translation::IntegerValue(*from.crops_killed_today_);
  } else {
    crops_killed_today_ = nullptr;
  }
  if (from._internal_has_crops_killed_total()) {
    crops_killed_total_ = new ::carbon::frontend::translation::IntegerValue(*from.crops_killed_total_);
  } else {
    crops_killed_total_ = nullptr;
  }
  ::memcpy(&row_width_in_, &from.row_width_in_,
    static_cast<size_t>(reinterpret_cast<char*>(&cruise_allow_enable_) -
    reinterpret_cast<char*>(&row_width_in_)) + sizeof(cruise_allow_enable_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.DashboardStateMessage)
}

inline void DashboardStateMessage::SharedCtor() {
cruise_disallowed_reason_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cruise_disallowed_reason_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&cruise_allow_enable_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(cruise_allow_enable_));
}

DashboardStateMessage::~DashboardStateMessage() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.DashboardStateMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DashboardStateMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cruise_disallowed_reason_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete selected_model_;
  if (this != internal_default_instance()) delete targeting_state_;
  if (this != internal_default_instance()) delete efficiency_percent_;
  if (this != internal_default_instance()) delete error_rate_;
  if (this != internal_default_instance()) delete area_weeded_today_;
  if (this != internal_default_instance()) delete area_weeded_total_;
  if (this != internal_default_instance()) delete weeds_killed_today_;
  if (this != internal_default_instance()) delete weeds_killed_total_;
  if (this != internal_default_instance()) delete time_weeded_today_;
  if (this != internal_default_instance()) delete time_weeded_total_;
  if (this != internal_default_instance()) delete crops_killed_today_;
  if (this != internal_default_instance()) delete crops_killed_total_;
}

void DashboardStateMessage::ArenaDtor(void* object) {
  DashboardStateMessage* _this = reinterpret_cast< DashboardStateMessage* >(object);
  (void)_this;
  _this->row_states_. ~MapField();
}
inline void DashboardStateMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &DashboardStateMessage::ArenaDtor);
  }
}
void DashboardStateMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DashboardStateMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.DashboardStateMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  row_enabled_.Clear();
  extras_.Clear();
  target_state_mismatch_.Clear();
  row_ready_.Clear();
  row_exists_.Clear();
  safety_override_state_.Clear();
  extra_conclusions_.Clear();
  row_states_.Clear();
  cruise_disallowed_reason_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && selected_model_ != nullptr) {
    delete selected_model_;
  }
  selected_model_ = nullptr;
  if (GetArenaForAllocation() == nullptr && targeting_state_ != nullptr) {
    delete targeting_state_;
  }
  targeting_state_ = nullptr;
  if (GetArenaForAllocation() == nullptr && efficiency_percent_ != nullptr) {
    delete efficiency_percent_;
  }
  efficiency_percent_ = nullptr;
  if (GetArenaForAllocation() == nullptr && error_rate_ != nullptr) {
    delete error_rate_;
  }
  error_rate_ = nullptr;
  if (GetArenaForAllocation() == nullptr && area_weeded_today_ != nullptr) {
    delete area_weeded_today_;
  }
  area_weeded_today_ = nullptr;
  if (GetArenaForAllocation() == nullptr && area_weeded_total_ != nullptr) {
    delete area_weeded_total_;
  }
  area_weeded_total_ = nullptr;
  if (GetArenaForAllocation() == nullptr && weeds_killed_today_ != nullptr) {
    delete weeds_killed_today_;
  }
  weeds_killed_today_ = nullptr;
  if (GetArenaForAllocation() == nullptr && weeds_killed_total_ != nullptr) {
    delete weeds_killed_total_;
  }
  weeds_killed_total_ = nullptr;
  if (GetArenaForAllocation() == nullptr && time_weeded_today_ != nullptr) {
    delete time_weeded_today_;
  }
  time_weeded_today_ = nullptr;
  if (GetArenaForAllocation() == nullptr && time_weeded_total_ != nullptr) {
    delete time_weeded_total_;
  }
  time_weeded_total_ = nullptr;
  if (GetArenaForAllocation() == nullptr && crops_killed_today_ != nullptr) {
    delete crops_killed_today_;
  }
  crops_killed_today_ = nullptr;
  if (GetArenaForAllocation() == nullptr && crops_killed_total_ != nullptr) {
    delete crops_killed_total_;
  }
  crops_killed_total_ = nullptr;
  ::memset(&row_width_in_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cruise_allow_enable_) -
      reinterpret_cast<char*>(&row_width_in_)) + sizeof(cruise_allow_enable_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DashboardStateMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool lasers_enabled = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          lasers_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bool row_enabled = 3 [deprecated = true];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedBoolParser(_internal_mutable_row_enabled(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 24) {
          _internal_add_row_enabled(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.dashboard.ExtraStatus extras = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_extras(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.dashboard.CropModel selected_model = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_selected_model(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.dashboard.TargetingState targeting_state = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_targeting_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bool target_state_mismatch = 7 [deprecated = true];
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedBoolParser(_internal_mutable_target_state_mismatch(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 56) {
          _internal_add_target_state_mismatch(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bool row_ready = 8 [deprecated = true];
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedBoolParser(_internal_mutable_row_ready(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 64) {
          _internal_add_row_ready(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bool row_exists = 9 [deprecated = true];
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedBoolParser(_internal_mutable_row_exists(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 72) {
          _internal_add_row_exists(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double row_width_in = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 81)) {
          row_width_in_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 11 [deprecated = true];
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedEnumParser(_internal_mutable_safety_override_state(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 88) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_add_safety_override_state(static_cast<::carbon::frontend::dashboard::SafetyOverrideState>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.dashboard.ImplementState implement_state = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_implement_state(static_cast<::carbon::frontend::dashboard::ImplementState>(val));
        } else
          goto handle_unusual;
        continue;
      // bool efficiency_enabled = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 112)) {
          efficiency_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.PercentValue efficiency_percent = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          ptr = ctx->ParseMessage(_internal_mutable_efficiency_percent(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool error_rate_enabled = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          error_rate_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.PercentValue error_rate = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          ptr = ctx->ParseMessage(_internal_mutable_error_rate(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.dashboard.ExtraConclusion extra_conclusions = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(_internal_add_extra_conclusions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<146>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.AreaValue area_weeded_today = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 154)) {
          ptr = ctx->ParseMessage(_internal_mutable_area_weeded_today(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.AreaValue area_weeded_total = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 162)) {
          ptr = ctx->ParseMessage(_internal_mutable_area_weeded_total(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.IntegerValue weeds_killed_today = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 170)) {
          ptr = ctx->ParseMessage(_internal_mutable_weeds_killed_today(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.IntegerValue weeds_killed_total = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 178)) {
          ptr = ctx->ParseMessage(_internal_mutable_weeds_killed_total(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.DurationValue time_weeded_today = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 186)) {
          ptr = ctx->ParseMessage(_internal_mutable_time_weeded_today(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.DurationValue time_weeded_total = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 194)) {
          ptr = ctx->ParseMessage(_internal_mutable_time_weeded_total(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool weeding_enabled = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 200)) {
          weeding_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool debug_mode = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 208)) {
          debug_mode_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.IntegerValue crops_killed_today = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 218)) {
          ptr = ctx->ParseMessage(_internal_mutable_crops_killed_today(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.translation.IntegerValue crops_killed_total = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 226)) {
          ptr = ctx->ParseMessage(_internal_mutable_crops_killed_total(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool cruise_enabled = 29;
      case 29:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 232)) {
          cruise_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // map<int32, .carbon.frontend.dashboard.RowStateMessage> row_states = 30;
      case 30:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 242)) {
          ptr -= 2;
          do {
            ptr += 2;
            ptr = ctx->ParseMessage(&row_states_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<242>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool cruise_allow_enable = 31;
      case 31:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 248)) {
          cruise_allow_enable_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string cruise_disallowed_reason = 32;
      case 32:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 2)) {
          auto str = _internal_mutable_cruise_disallowed_reason();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.DashboardStateMessage.cruise_disallowed_reason"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DashboardStateMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.DashboardStateMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // bool lasers_enabled = 2;
  if (this->_internal_lasers_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_lasers_enabled(), target);
  }

  // repeated bool row_enabled = 3 [deprecated = true];
  if (this->_internal_row_enabled_size() > 0) {
    target = stream->WriteFixedPacked(3, _internal_row_enabled(), target);
  }

  // repeated .carbon.frontend.dashboard.ExtraStatus extras = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_extras_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_extras(i), target, stream);
  }

  // .carbon.frontend.dashboard.CropModel selected_model = 5;
  if (this->_internal_has_selected_model()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::selected_model(this), target, stream);
  }

  // .carbon.frontend.dashboard.TargetingState targeting_state = 6;
  if (this->_internal_has_targeting_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::targeting_state(this), target, stream);
  }

  // repeated bool target_state_mismatch = 7 [deprecated = true];
  if (this->_internal_target_state_mismatch_size() > 0) {
    target = stream->WriteFixedPacked(7, _internal_target_state_mismatch(), target);
  }

  // repeated bool row_ready = 8 [deprecated = true];
  if (this->_internal_row_ready_size() > 0) {
    target = stream->WriteFixedPacked(8, _internal_row_ready(), target);
  }

  // repeated bool row_exists = 9 [deprecated = true];
  if (this->_internal_row_exists_size() > 0) {
    target = stream->WriteFixedPacked(9, _internal_row_exists(), target);
  }

  // double row_width_in = 10;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_row_width_in = this->_internal_row_width_in();
  uint64_t raw_row_width_in;
  memcpy(&raw_row_width_in, &tmp_row_width_in, sizeof(tmp_row_width_in));
  if (raw_row_width_in != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(10, this->_internal_row_width_in(), target);
  }

  // repeated .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 11 [deprecated = true];
  {
    int byte_size = _safety_override_state_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteEnumPacked(
          11, safety_override_state_, byte_size, target);
    }
  }

  // .carbon.frontend.dashboard.ImplementState implement_state = 13;
  if (this->_internal_implement_state() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      13, this->_internal_implement_state(), target);
  }

  // bool efficiency_enabled = 14;
  if (this->_internal_efficiency_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(14, this->_internal_efficiency_enabled(), target);
  }

  // .carbon.frontend.translation.PercentValue efficiency_percent = 15;
  if (this->_internal_has_efficiency_percent()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        15, _Internal::efficiency_percent(this), target, stream);
  }

  // bool error_rate_enabled = 16;
  if (this->_internal_error_rate_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(16, this->_internal_error_rate_enabled(), target);
  }

  // .carbon.frontend.translation.PercentValue error_rate = 17;
  if (this->_internal_has_error_rate()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        17, _Internal::error_rate(this), target, stream);
  }

  // repeated .carbon.frontend.dashboard.ExtraConclusion extra_conclusions = 18;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_extra_conclusions_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(18, this->_internal_extra_conclusions(i), target, stream);
  }

  // .carbon.frontend.translation.AreaValue area_weeded_today = 19;
  if (this->_internal_has_area_weeded_today()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        19, _Internal::area_weeded_today(this), target, stream);
  }

  // .carbon.frontend.translation.AreaValue area_weeded_total = 20;
  if (this->_internal_has_area_weeded_total()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        20, _Internal::area_weeded_total(this), target, stream);
  }

  // .carbon.frontend.translation.IntegerValue weeds_killed_today = 21;
  if (this->_internal_has_weeds_killed_today()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        21, _Internal::weeds_killed_today(this), target, stream);
  }

  // .carbon.frontend.translation.IntegerValue weeds_killed_total = 22;
  if (this->_internal_has_weeds_killed_total()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        22, _Internal::weeds_killed_total(this), target, stream);
  }

  // .carbon.frontend.translation.DurationValue time_weeded_today = 23;
  if (this->_internal_has_time_weeded_today()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        23, _Internal::time_weeded_today(this), target, stream);
  }

  // .carbon.frontend.translation.DurationValue time_weeded_total = 24;
  if (this->_internal_has_time_weeded_total()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        24, _Internal::time_weeded_total(this), target, stream);
  }

  // bool weeding_enabled = 25;
  if (this->_internal_weeding_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(25, this->_internal_weeding_enabled(), target);
  }

  // bool debug_mode = 26;
  if (this->_internal_debug_mode() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(26, this->_internal_debug_mode(), target);
  }

  // .carbon.frontend.translation.IntegerValue crops_killed_today = 27;
  if (this->_internal_has_crops_killed_today()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        27, _Internal::crops_killed_today(this), target, stream);
  }

  // .carbon.frontend.translation.IntegerValue crops_killed_total = 28;
  if (this->_internal_has_crops_killed_total()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        28, _Internal::crops_killed_total(this), target, stream);
  }

  // bool cruise_enabled = 29;
  if (this->_internal_cruise_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(29, this->_internal_cruise_enabled(), target);
  }

  // map<int32, .carbon.frontend.dashboard.RowStateMessage> row_states = 30;
  if (!this->_internal_row_states().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >::const_pointer
        ConstPtr;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::SortItem< int32_t, ConstPtr > SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByFirstField<SortItem> Less;

    if (stream->IsSerializationDeterministic() &&
        this->_internal_row_states().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_row_states().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >::const_iterator
          it = this->_internal_row_states().begin();
          it != this->_internal_row_states().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = DashboardStateMessage_RowStatesEntry_DoNotUse::Funcs::InternalSerialize(30, items[static_cast<ptrdiff_t>(i)].second->first, items[static_cast<ptrdiff_t>(i)].second->second, target, stream);
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >::const_iterator
          it = this->_internal_row_states().begin();
          it != this->_internal_row_states().end(); ++it) {
        target = DashboardStateMessage_RowStatesEntry_DoNotUse::Funcs::InternalSerialize(30, it->first, it->second, target, stream);
      }
    }
  }

  // bool cruise_allow_enable = 31;
  if (this->_internal_cruise_allow_enable() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(31, this->_internal_cruise_allow_enable(), target);
  }

  // string cruise_disallowed_reason = 32;
  if (!this->_internal_cruise_disallowed_reason().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cruise_disallowed_reason().data(), static_cast<int>(this->_internal_cruise_disallowed_reason().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.DashboardStateMessage.cruise_disallowed_reason");
    target = stream->WriteStringMaybeAliased(
        32, this->_internal_cruise_disallowed_reason(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.DashboardStateMessage)
  return target;
}

size_t DashboardStateMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.DashboardStateMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated bool row_enabled = 3 [deprecated = true];
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_row_enabled_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated .carbon.frontend.dashboard.ExtraStatus extras = 4;
  total_size += 1UL * this->_internal_extras_size();
  for (const auto& msg : this->extras_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated bool target_state_mismatch = 7 [deprecated = true];
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_target_state_mismatch_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated bool row_ready = 8 [deprecated = true];
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_row_ready_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated bool row_exists = 9 [deprecated = true];
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_row_exists_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated .carbon.frontend.dashboard.SafetyOverrideState safety_override_state = 11 [deprecated = true];
  {
    size_t data_size = 0;
    unsigned int count = static_cast<unsigned int>(this->_internal_safety_override_state_size());for (unsigned int i = 0; i < count; i++) {
      data_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(
        this->_internal_safety_override_state(static_cast<int>(i)));
    }
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _safety_override_state_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated .carbon.frontend.dashboard.ExtraConclusion extra_conclusions = 18;
  total_size += 2UL * this->_internal_extra_conclusions_size();
  for (const auto& msg : this->extra_conclusions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // map<int32, .carbon.frontend.dashboard.RowStateMessage> row_states = 30;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_row_states_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::dashboard::RowStateMessage >::const_iterator
      it = this->_internal_row_states().begin();
      it != this->_internal_row_states().end(); ++it) {
    total_size += DashboardStateMessage_RowStatesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // string cruise_disallowed_reason = 32;
  if (!this->_internal_cruise_disallowed_reason().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cruise_disallowed_reason());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.dashboard.CropModel selected_model = 5;
  if (this->_internal_has_selected_model()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *selected_model_);
  }

  // .carbon.frontend.dashboard.TargetingState targeting_state = 6;
  if (this->_internal_has_targeting_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *targeting_state_);
  }

  // .carbon.frontend.translation.PercentValue efficiency_percent = 15;
  if (this->_internal_has_efficiency_percent()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *efficiency_percent_);
  }

  // .carbon.frontend.translation.PercentValue error_rate = 17;
  if (this->_internal_has_error_rate()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *error_rate_);
  }

  // .carbon.frontend.translation.AreaValue area_weeded_today = 19;
  if (this->_internal_has_area_weeded_today()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *area_weeded_today_);
  }

  // .carbon.frontend.translation.AreaValue area_weeded_total = 20;
  if (this->_internal_has_area_weeded_total()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *area_weeded_total_);
  }

  // .carbon.frontend.translation.IntegerValue weeds_killed_today = 21;
  if (this->_internal_has_weeds_killed_today()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *weeds_killed_today_);
  }

  // .carbon.frontend.translation.IntegerValue weeds_killed_total = 22;
  if (this->_internal_has_weeds_killed_total()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *weeds_killed_total_);
  }

  // .carbon.frontend.translation.DurationValue time_weeded_today = 23;
  if (this->_internal_has_time_weeded_today()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *time_weeded_today_);
  }

  // .carbon.frontend.translation.DurationValue time_weeded_total = 24;
  if (this->_internal_has_time_weeded_total()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *time_weeded_total_);
  }

  // .carbon.frontend.translation.IntegerValue crops_killed_today = 27;
  if (this->_internal_has_crops_killed_today()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *crops_killed_today_);
  }

  // .carbon.frontend.translation.IntegerValue crops_killed_total = 28;
  if (this->_internal_has_crops_killed_total()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *crops_killed_total_);
  }

  // double row_width_in = 10;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_row_width_in = this->_internal_row_width_in();
  uint64_t raw_row_width_in;
  memcpy(&raw_row_width_in, &tmp_row_width_in, sizeof(tmp_row_width_in));
  if (raw_row_width_in != 0) {
    total_size += 1 + 8;
  }

  // .carbon.frontend.dashboard.ImplementState implement_state = 13;
  if (this->_internal_implement_state() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_implement_state());
  }

  // bool lasers_enabled = 2;
  if (this->_internal_lasers_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool efficiency_enabled = 14;
  if (this->_internal_efficiency_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool error_rate_enabled = 16;
  if (this->_internal_error_rate_enabled() != 0) {
    total_size += 2 + 1;
  }

  // bool weeding_enabled = 25;
  if (this->_internal_weeding_enabled() != 0) {
    total_size += 2 + 1;
  }

  // bool debug_mode = 26;
  if (this->_internal_debug_mode() != 0) {
    total_size += 2 + 1;
  }

  // bool cruise_enabled = 29;
  if (this->_internal_cruise_enabled() != 0) {
    total_size += 2 + 1;
  }

  // bool cruise_allow_enable = 31;
  if (this->_internal_cruise_allow_enable() != 0) {
    total_size += 2 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DashboardStateMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DashboardStateMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DashboardStateMessage::GetClassData() const { return &_class_data_; }

void DashboardStateMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DashboardStateMessage *>(to)->MergeFrom(
      static_cast<const DashboardStateMessage &>(from));
}


void DashboardStateMessage::MergeFrom(const DashboardStateMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.DashboardStateMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  row_enabled_.MergeFrom(from.row_enabled_);
  extras_.MergeFrom(from.extras_);
  target_state_mismatch_.MergeFrom(from.target_state_mismatch_);
  row_ready_.MergeFrom(from.row_ready_);
  row_exists_.MergeFrom(from.row_exists_);
  safety_override_state_.MergeFrom(from.safety_override_state_);
  extra_conclusions_.MergeFrom(from.extra_conclusions_);
  row_states_.MergeFrom(from.row_states_);
  if (!from._internal_cruise_disallowed_reason().empty()) {
    _internal_set_cruise_disallowed_reason(from._internal_cruise_disallowed_reason());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_selected_model()) {
    _internal_mutable_selected_model()->::carbon::frontend::dashboard::CropModel::MergeFrom(from._internal_selected_model());
  }
  if (from._internal_has_targeting_state()) {
    _internal_mutable_targeting_state()->::carbon::frontend::dashboard::TargetingState::MergeFrom(from._internal_targeting_state());
  }
  if (from._internal_has_efficiency_percent()) {
    _internal_mutable_efficiency_percent()->::carbon::frontend::translation::PercentValue::MergeFrom(from._internal_efficiency_percent());
  }
  if (from._internal_has_error_rate()) {
    _internal_mutable_error_rate()->::carbon::frontend::translation::PercentValue::MergeFrom(from._internal_error_rate());
  }
  if (from._internal_has_area_weeded_today()) {
    _internal_mutable_area_weeded_today()->::carbon::frontend::translation::AreaValue::MergeFrom(from._internal_area_weeded_today());
  }
  if (from._internal_has_area_weeded_total()) {
    _internal_mutable_area_weeded_total()->::carbon::frontend::translation::AreaValue::MergeFrom(from._internal_area_weeded_total());
  }
  if (from._internal_has_weeds_killed_today()) {
    _internal_mutable_weeds_killed_today()->::carbon::frontend::translation::IntegerValue::MergeFrom(from._internal_weeds_killed_today());
  }
  if (from._internal_has_weeds_killed_total()) {
    _internal_mutable_weeds_killed_total()->::carbon::frontend::translation::IntegerValue::MergeFrom(from._internal_weeds_killed_total());
  }
  if (from._internal_has_time_weeded_today()) {
    _internal_mutable_time_weeded_today()->::carbon::frontend::translation::DurationValue::MergeFrom(from._internal_time_weeded_today());
  }
  if (from._internal_has_time_weeded_total()) {
    _internal_mutable_time_weeded_total()->::carbon::frontend::translation::DurationValue::MergeFrom(from._internal_time_weeded_total());
  }
  if (from._internal_has_crops_killed_today()) {
    _internal_mutable_crops_killed_today()->::carbon::frontend::translation::IntegerValue::MergeFrom(from._internal_crops_killed_today());
  }
  if (from._internal_has_crops_killed_total()) {
    _internal_mutable_crops_killed_total()->::carbon::frontend::translation::IntegerValue::MergeFrom(from._internal_crops_killed_total());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_row_width_in = from._internal_row_width_in();
  uint64_t raw_row_width_in;
  memcpy(&raw_row_width_in, &tmp_row_width_in, sizeof(tmp_row_width_in));
  if (raw_row_width_in != 0) {
    _internal_set_row_width_in(from._internal_row_width_in());
  }
  if (from._internal_implement_state() != 0) {
    _internal_set_implement_state(from._internal_implement_state());
  }
  if (from._internal_lasers_enabled() != 0) {
    _internal_set_lasers_enabled(from._internal_lasers_enabled());
  }
  if (from._internal_efficiency_enabled() != 0) {
    _internal_set_efficiency_enabled(from._internal_efficiency_enabled());
  }
  if (from._internal_error_rate_enabled() != 0) {
    _internal_set_error_rate_enabled(from._internal_error_rate_enabled());
  }
  if (from._internal_weeding_enabled() != 0) {
    _internal_set_weeding_enabled(from._internal_weeding_enabled());
  }
  if (from._internal_debug_mode() != 0) {
    _internal_set_debug_mode(from._internal_debug_mode());
  }
  if (from._internal_cruise_enabled() != 0) {
    _internal_set_cruise_enabled(from._internal_cruise_enabled());
  }
  if (from._internal_cruise_allow_enable() != 0) {
    _internal_set_cruise_allow_enable(from._internal_cruise_allow_enable());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DashboardStateMessage::CopyFrom(const DashboardStateMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.DashboardStateMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DashboardStateMessage::IsInitialized() const {
  return true;
}

void DashboardStateMessage::InternalSwap(DashboardStateMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  row_enabled_.InternalSwap(&other->row_enabled_);
  extras_.InternalSwap(&other->extras_);
  target_state_mismatch_.InternalSwap(&other->target_state_mismatch_);
  row_ready_.InternalSwap(&other->row_ready_);
  row_exists_.InternalSwap(&other->row_exists_);
  safety_override_state_.InternalSwap(&other->safety_override_state_);
  extra_conclusions_.InternalSwap(&other->extra_conclusions_);
  row_states_.InternalSwap(&other->row_states_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cruise_disallowed_reason_, lhs_arena,
      &other->cruise_disallowed_reason_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DashboardStateMessage, cruise_allow_enable_)
      + sizeof(DashboardStateMessage::cruise_allow_enable_)
      - PROTOBUF_FIELD_OFFSET(DashboardStateMessage, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DashboardStateMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[8]);
}

// ===================================================================

class CropModel::_Internal {
 public:
};

CropModel::CropModel(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.CropModel)
}
CropModel::CropModel(const CropModel& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&has_model_, &from.has_model_,
    static_cast<size_t>(reinterpret_cast<char*>(&preferred_) -
    reinterpret_cast<char*>(&has_model_)) + sizeof(preferred_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.CropModel)
}

inline void CropModel::SharedCtor() {
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&has_model_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&preferred_) -
    reinterpret_cast<char*>(&has_model_)) + sizeof(preferred_));
}

CropModel::~CropModel() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.CropModel)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CropModel::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CropModel::ArenaDtor(void* object) {
  CropModel* _this = reinterpret_cast< CropModel* >(object);
  (void)_this;
}
void CropModel::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CropModel::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CropModel::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.CropModel)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  ::memset(&has_model_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&preferred_) -
      reinterpret_cast<char*>(&has_model_)) + sizeof(preferred_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CropModel::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string crop = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.CropModel.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool has_model = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          has_model_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool preferred = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          preferred_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.dashboard.CropModel.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CropModel::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.CropModel)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string crop = 1 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.CropModel.crop");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_crop(), target);
  }

  // bool has_model = 2;
  if (this->_internal_has_model() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_has_model(), target);
  }

  // bool preferred = 3;
  if (this->_internal_preferred() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_preferred(), target);
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.dashboard.CropModel.crop_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_crop_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.CropModel)
  return target;
}

size_t CropModel::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.CropModel)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string crop = 1 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // bool has_model = 2;
  if (this->_internal_has_model() != 0) {
    total_size += 1 + 1;
  }

  // bool preferred = 3;
  if (this->_internal_preferred() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CropModel::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CropModel::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CropModel::GetClassData() const { return &_class_data_; }

void CropModel::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CropModel *>(to)->MergeFrom(
      static_cast<const CropModel &>(from));
}


void CropModel::MergeFrom(const CropModel& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.CropModel)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_has_model() != 0) {
    _internal_set_has_model(from._internal_has_model());
  }
  if (from._internal_preferred() != 0) {
    _internal_set_preferred(from._internal_preferred());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CropModel::CopyFrom(const CropModel& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.CropModel)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CropModel::IsInitialized() const {
  return true;
}

void CropModel::InternalSwap(CropModel* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CropModel, preferred_)
      + sizeof(CropModel::preferred_)
      - PROTOBUF_FIELD_OFFSET(CropModel, has_model_)>(
          reinterpret_cast<char*>(&has_model_),
          reinterpret_cast<char*>(&other->has_model_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CropModel::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[9]);
}

// ===================================================================

class CropModelOptions::_Internal {
 public:
};

CropModelOptions::CropModelOptions(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  models_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.CropModelOptions)
}
CropModelOptions::CropModelOptions(const CropModelOptions& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      models_(from.models_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.CropModelOptions)
}

inline void CropModelOptions::SharedCtor() {
}

CropModelOptions::~CropModelOptions() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.CropModelOptions)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CropModelOptions::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CropModelOptions::ArenaDtor(void* object) {
  CropModelOptions* _this = reinterpret_cast< CropModelOptions* >(object);
  (void)_this;
}
void CropModelOptions::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CropModelOptions::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CropModelOptions::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.CropModelOptions)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  models_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CropModelOptions::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.dashboard.CropModel models = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_models(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CropModelOptions::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.CropModelOptions)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.dashboard.CropModel models = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_models_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_models(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.CropModelOptions)
  return target;
}

size_t CropModelOptions::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.CropModelOptions)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.dashboard.CropModel models = 1;
  total_size += 1UL * this->_internal_models_size();
  for (const auto& msg : this->models_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CropModelOptions::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CropModelOptions::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CropModelOptions::GetClassData() const { return &_class_data_; }

void CropModelOptions::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CropModelOptions *>(to)->MergeFrom(
      static_cast<const CropModelOptions &>(from));
}


void CropModelOptions::MergeFrom(const CropModelOptions& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.CropModelOptions)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  models_.MergeFrom(from.models_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CropModelOptions::CopyFrom(const CropModelOptions& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.CropModelOptions)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CropModelOptions::IsInitialized() const {
  return true;
}

void CropModelOptions::InternalSwap(CropModelOptions* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  models_.InternalSwap(&other->models_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CropModelOptions::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[10]);
}

// ===================================================================

class RowId::_Internal {
 public:
};

RowId::RowId(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.RowId)
}
RowId::RowId(const RowId& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  row_number_ = from.row_number_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.RowId)
}

inline void RowId::SharedCtor() {
row_number_ = 0u;
}

RowId::~RowId() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.RowId)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RowId::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RowId::ArenaDtor(void* object) {
  RowId* _this = reinterpret_cast< RowId* >(object);
  (void)_this;
}
void RowId::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RowId::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RowId::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.RowId)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  row_number_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RowId::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row_number = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_number_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RowId::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.RowId)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row_number(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.RowId)
  return target;
}

size_t RowId::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.RowId)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 row_number = 1;
  if (this->_internal_row_number() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_row_number());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RowId::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RowId::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RowId::GetClassData() const { return &_class_data_; }

void RowId::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RowId *>(to)->MergeFrom(
      static_cast<const RowId &>(from));
}


void RowId::MergeFrom(const RowId& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.RowId)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_row_number() != 0) {
    _internal_set_row_number(from._internal_row_number());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RowId::CopyFrom(const RowId& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.RowId)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RowId::IsInitialized() const {
  return true;
}

void RowId::InternalSwap(RowId* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(row_number_, other->row_number_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RowId::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[11]);
}

// ===================================================================

class WeedingVelocity::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const WeedingVelocity* msg);
};

const ::carbon::frontend::util::Timestamp&
WeedingVelocity::_Internal::ts(const WeedingVelocity* msg) {
  return *msg->ts_;
}
void WeedingVelocity::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
WeedingVelocity::WeedingVelocity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.WeedingVelocity)
}
WeedingVelocity::WeedingVelocity(const WeedingVelocity& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&current_velocity_mph_, &from.current_velocity_mph_,
    static_cast<size_t>(reinterpret_cast<char*>(&cruise_control_velocity_mph_) -
    reinterpret_cast<char*>(&current_velocity_mph_)) + sizeof(cruise_control_velocity_mph_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.WeedingVelocity)
}

inline void WeedingVelocity::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&cruise_control_velocity_mph_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(cruise_control_velocity_mph_));
}

WeedingVelocity::~WeedingVelocity() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.WeedingVelocity)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void WeedingVelocity::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void WeedingVelocity::ArenaDtor(void* object) {
  WeedingVelocity* _this = reinterpret_cast< WeedingVelocity* >(object);
  (void)_this;
}
void WeedingVelocity::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void WeedingVelocity::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void WeedingVelocity::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.WeedingVelocity)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&current_velocity_mph_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&cruise_control_velocity_mph_) -
      reinterpret_cast<char*>(&current_velocity_mph_)) + sizeof(cruise_control_velocity_mph_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* WeedingVelocity::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double current_velocity_mph = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          current_velocity_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double target_velocity_mph = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          target_velocity_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double tolerance_mph = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          tolerance_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double primary_target_velocity_top_mph = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 41)) {
          primary_target_velocity_top_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double primary_target_velocity_bottom_mph = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 49)) {
          primary_target_velocity_bottom_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double secondary_target_velocity_top_mph = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 57)) {
          secondary_target_velocity_top_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double secondary_target_velocity_bottom_mph = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 65)) {
          secondary_target_velocity_bottom_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double cruise_control_velocity_mph = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 73)) {
          cruise_control_velocity_mph_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* WeedingVelocity::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.WeedingVelocity)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // double current_velocity_mph = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_current_velocity_mph = this->_internal_current_velocity_mph();
  uint64_t raw_current_velocity_mph;
  memcpy(&raw_current_velocity_mph, &tmp_current_velocity_mph, sizeof(tmp_current_velocity_mph));
  if (raw_current_velocity_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_current_velocity_mph(), target);
  }

  // double target_velocity_mph = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_target_velocity_mph = this->_internal_target_velocity_mph();
  uint64_t raw_target_velocity_mph;
  memcpy(&raw_target_velocity_mph, &tmp_target_velocity_mph, sizeof(tmp_target_velocity_mph));
  if (raw_target_velocity_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_target_velocity_mph(), target);
  }

  // double tolerance_mph = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_tolerance_mph = this->_internal_tolerance_mph();
  uint64_t raw_tolerance_mph;
  memcpy(&raw_tolerance_mph, &tmp_tolerance_mph, sizeof(tmp_tolerance_mph));
  if (raw_tolerance_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_tolerance_mph(), target);
  }

  // double primary_target_velocity_top_mph = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_primary_target_velocity_top_mph = this->_internal_primary_target_velocity_top_mph();
  uint64_t raw_primary_target_velocity_top_mph;
  memcpy(&raw_primary_target_velocity_top_mph, &tmp_primary_target_velocity_top_mph, sizeof(tmp_primary_target_velocity_top_mph));
  if (raw_primary_target_velocity_top_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(5, this->_internal_primary_target_velocity_top_mph(), target);
  }

  // double primary_target_velocity_bottom_mph = 6;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_primary_target_velocity_bottom_mph = this->_internal_primary_target_velocity_bottom_mph();
  uint64_t raw_primary_target_velocity_bottom_mph;
  memcpy(&raw_primary_target_velocity_bottom_mph, &tmp_primary_target_velocity_bottom_mph, sizeof(tmp_primary_target_velocity_bottom_mph));
  if (raw_primary_target_velocity_bottom_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(6, this->_internal_primary_target_velocity_bottom_mph(), target);
  }

  // double secondary_target_velocity_top_mph = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_secondary_target_velocity_top_mph = this->_internal_secondary_target_velocity_top_mph();
  uint64_t raw_secondary_target_velocity_top_mph;
  memcpy(&raw_secondary_target_velocity_top_mph, &tmp_secondary_target_velocity_top_mph, sizeof(tmp_secondary_target_velocity_top_mph));
  if (raw_secondary_target_velocity_top_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(7, this->_internal_secondary_target_velocity_top_mph(), target);
  }

  // double secondary_target_velocity_bottom_mph = 8;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_secondary_target_velocity_bottom_mph = this->_internal_secondary_target_velocity_bottom_mph();
  uint64_t raw_secondary_target_velocity_bottom_mph;
  memcpy(&raw_secondary_target_velocity_bottom_mph, &tmp_secondary_target_velocity_bottom_mph, sizeof(tmp_secondary_target_velocity_bottom_mph));
  if (raw_secondary_target_velocity_bottom_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(8, this->_internal_secondary_target_velocity_bottom_mph(), target);
  }

  // double cruise_control_velocity_mph = 9;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cruise_control_velocity_mph = this->_internal_cruise_control_velocity_mph();
  uint64_t raw_cruise_control_velocity_mph;
  memcpy(&raw_cruise_control_velocity_mph, &tmp_cruise_control_velocity_mph, sizeof(tmp_cruise_control_velocity_mph));
  if (raw_cruise_control_velocity_mph != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(9, this->_internal_cruise_control_velocity_mph(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.WeedingVelocity)
  return target;
}

size_t WeedingVelocity::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.WeedingVelocity)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // double current_velocity_mph = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_current_velocity_mph = this->_internal_current_velocity_mph();
  uint64_t raw_current_velocity_mph;
  memcpy(&raw_current_velocity_mph, &tmp_current_velocity_mph, sizeof(tmp_current_velocity_mph));
  if (raw_current_velocity_mph != 0) {
    total_size += 1 + 8;
  }

  // double target_velocity_mph = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_target_velocity_mph = this->_internal_target_velocity_mph();
  uint64_t raw_target_velocity_mph;
  memcpy(&raw_target_velocity_mph, &tmp_target_velocity_mph, sizeof(tmp_target_velocity_mph));
  if (raw_target_velocity_mph != 0) {
    total_size += 1 + 8;
  }

  // double tolerance_mph = 4;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_tolerance_mph = this->_internal_tolerance_mph();
  uint64_t raw_tolerance_mph;
  memcpy(&raw_tolerance_mph, &tmp_tolerance_mph, sizeof(tmp_tolerance_mph));
  if (raw_tolerance_mph != 0) {
    total_size += 1 + 8;
  }

  // double primary_target_velocity_top_mph = 5;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_primary_target_velocity_top_mph = this->_internal_primary_target_velocity_top_mph();
  uint64_t raw_primary_target_velocity_top_mph;
  memcpy(&raw_primary_target_velocity_top_mph, &tmp_primary_target_velocity_top_mph, sizeof(tmp_primary_target_velocity_top_mph));
  if (raw_primary_target_velocity_top_mph != 0) {
    total_size += 1 + 8;
  }

  // double primary_target_velocity_bottom_mph = 6;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_primary_target_velocity_bottom_mph = this->_internal_primary_target_velocity_bottom_mph();
  uint64_t raw_primary_target_velocity_bottom_mph;
  memcpy(&raw_primary_target_velocity_bottom_mph, &tmp_primary_target_velocity_bottom_mph, sizeof(tmp_primary_target_velocity_bottom_mph));
  if (raw_primary_target_velocity_bottom_mph != 0) {
    total_size += 1 + 8;
  }

  // double secondary_target_velocity_top_mph = 7;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_secondary_target_velocity_top_mph = this->_internal_secondary_target_velocity_top_mph();
  uint64_t raw_secondary_target_velocity_top_mph;
  memcpy(&raw_secondary_target_velocity_top_mph, &tmp_secondary_target_velocity_top_mph, sizeof(tmp_secondary_target_velocity_top_mph));
  if (raw_secondary_target_velocity_top_mph != 0) {
    total_size += 1 + 8;
  }

  // double secondary_target_velocity_bottom_mph = 8;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_secondary_target_velocity_bottom_mph = this->_internal_secondary_target_velocity_bottom_mph();
  uint64_t raw_secondary_target_velocity_bottom_mph;
  memcpy(&raw_secondary_target_velocity_bottom_mph, &tmp_secondary_target_velocity_bottom_mph, sizeof(tmp_secondary_target_velocity_bottom_mph));
  if (raw_secondary_target_velocity_bottom_mph != 0) {
    total_size += 1 + 8;
  }

  // double cruise_control_velocity_mph = 9;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cruise_control_velocity_mph = this->_internal_cruise_control_velocity_mph();
  uint64_t raw_cruise_control_velocity_mph;
  memcpy(&raw_cruise_control_velocity_mph, &tmp_cruise_control_velocity_mph, sizeof(tmp_cruise_control_velocity_mph));
  if (raw_cruise_control_velocity_mph != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData WeedingVelocity::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    WeedingVelocity::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*WeedingVelocity::GetClassData() const { return &_class_data_; }

void WeedingVelocity::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<WeedingVelocity *>(to)->MergeFrom(
      static_cast<const WeedingVelocity &>(from));
}


void WeedingVelocity::MergeFrom(const WeedingVelocity& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.WeedingVelocity)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_current_velocity_mph = from._internal_current_velocity_mph();
  uint64_t raw_current_velocity_mph;
  memcpy(&raw_current_velocity_mph, &tmp_current_velocity_mph, sizeof(tmp_current_velocity_mph));
  if (raw_current_velocity_mph != 0) {
    _internal_set_current_velocity_mph(from._internal_current_velocity_mph());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_target_velocity_mph = from._internal_target_velocity_mph();
  uint64_t raw_target_velocity_mph;
  memcpy(&raw_target_velocity_mph, &tmp_target_velocity_mph, sizeof(tmp_target_velocity_mph));
  if (raw_target_velocity_mph != 0) {
    _internal_set_target_velocity_mph(from._internal_target_velocity_mph());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_tolerance_mph = from._internal_tolerance_mph();
  uint64_t raw_tolerance_mph;
  memcpy(&raw_tolerance_mph, &tmp_tolerance_mph, sizeof(tmp_tolerance_mph));
  if (raw_tolerance_mph != 0) {
    _internal_set_tolerance_mph(from._internal_tolerance_mph());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_primary_target_velocity_top_mph = from._internal_primary_target_velocity_top_mph();
  uint64_t raw_primary_target_velocity_top_mph;
  memcpy(&raw_primary_target_velocity_top_mph, &tmp_primary_target_velocity_top_mph, sizeof(tmp_primary_target_velocity_top_mph));
  if (raw_primary_target_velocity_top_mph != 0) {
    _internal_set_primary_target_velocity_top_mph(from._internal_primary_target_velocity_top_mph());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_primary_target_velocity_bottom_mph = from._internal_primary_target_velocity_bottom_mph();
  uint64_t raw_primary_target_velocity_bottom_mph;
  memcpy(&raw_primary_target_velocity_bottom_mph, &tmp_primary_target_velocity_bottom_mph, sizeof(tmp_primary_target_velocity_bottom_mph));
  if (raw_primary_target_velocity_bottom_mph != 0) {
    _internal_set_primary_target_velocity_bottom_mph(from._internal_primary_target_velocity_bottom_mph());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_secondary_target_velocity_top_mph = from._internal_secondary_target_velocity_top_mph();
  uint64_t raw_secondary_target_velocity_top_mph;
  memcpy(&raw_secondary_target_velocity_top_mph, &tmp_secondary_target_velocity_top_mph, sizeof(tmp_secondary_target_velocity_top_mph));
  if (raw_secondary_target_velocity_top_mph != 0) {
    _internal_set_secondary_target_velocity_top_mph(from._internal_secondary_target_velocity_top_mph());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_secondary_target_velocity_bottom_mph = from._internal_secondary_target_velocity_bottom_mph();
  uint64_t raw_secondary_target_velocity_bottom_mph;
  memcpy(&raw_secondary_target_velocity_bottom_mph, &tmp_secondary_target_velocity_bottom_mph, sizeof(tmp_secondary_target_velocity_bottom_mph));
  if (raw_secondary_target_velocity_bottom_mph != 0) {
    _internal_set_secondary_target_velocity_bottom_mph(from._internal_secondary_target_velocity_bottom_mph());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_cruise_control_velocity_mph = from._internal_cruise_control_velocity_mph();
  uint64_t raw_cruise_control_velocity_mph;
  memcpy(&raw_cruise_control_velocity_mph, &tmp_cruise_control_velocity_mph, sizeof(tmp_cruise_control_velocity_mph));
  if (raw_cruise_control_velocity_mph != 0) {
    _internal_set_cruise_control_velocity_mph(from._internal_cruise_control_velocity_mph());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void WeedingVelocity::CopyFrom(const WeedingVelocity& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.WeedingVelocity)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool WeedingVelocity::IsInitialized() const {
  return true;
}

void WeedingVelocity::InternalSwap(WeedingVelocity* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(WeedingVelocity, cruise_control_velocity_mph_)
      + sizeof(WeedingVelocity::cruise_control_velocity_mph_)
      - PROTOBUF_FIELD_OFFSET(WeedingVelocity, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata WeedingVelocity::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[12]);
}

// ===================================================================

class RowSpacing::_Internal {
 public:
};

RowSpacing::RowSpacing(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.RowSpacing)
}
RowSpacing::RowSpacing(const RowSpacing& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  width_ = from.width_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.RowSpacing)
}

inline void RowSpacing::SharedCtor() {
width_ = 0;
}

RowSpacing::~RowSpacing() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.RowSpacing)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RowSpacing::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RowSpacing::ArenaDtor(void* object) {
  RowSpacing* _this = reinterpret_cast< RowSpacing* >(object);
  (void)_this;
}
void RowSpacing::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RowSpacing::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RowSpacing::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.RowSpacing)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  width_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RowSpacing::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double width = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RowSpacing::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.RowSpacing)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double width = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width = this->_internal_width();
  uint64_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_width(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.RowSpacing)
  return target;
}

size_t RowSpacing::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.RowSpacing)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double width = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width = this->_internal_width();
  uint64_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RowSpacing::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RowSpacing::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RowSpacing::GetClassData() const { return &_class_data_; }

void RowSpacing::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RowSpacing *>(to)->MergeFrom(
      static_cast<const RowSpacing &>(from));
}


void RowSpacing::MergeFrom(const RowSpacing& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.RowSpacing)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_width = from._internal_width();
  uint64_t raw_width;
  memcpy(&raw_width, &tmp_width, sizeof(tmp_width));
  if (raw_width != 0) {
    _internal_set_width(from._internal_width());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RowSpacing::CopyFrom(const RowSpacing& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.RowSpacing)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RowSpacing::IsInitialized() const {
  return true;
}

void RowSpacing::InternalSwap(RowSpacing* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(width_, other->width_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RowSpacing::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[13]);
}

// ===================================================================

class CruiseEnable::_Internal {
 public:
};

CruiseEnable::CruiseEnable(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.dashboard.CruiseEnable)
}
CruiseEnable::CruiseEnable(const CruiseEnable& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enabled_ = from.enabled_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.dashboard.CruiseEnable)
}

inline void CruiseEnable::SharedCtor() {
enabled_ = false;
}

CruiseEnable::~CruiseEnable() {
  // @@protoc_insertion_point(destructor:carbon.frontend.dashboard.CruiseEnable)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CruiseEnable::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CruiseEnable::ArenaDtor(void* object) {
  CruiseEnable* _this = reinterpret_cast< CruiseEnable* >(object);
  (void)_this;
}
void CruiseEnable::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CruiseEnable::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CruiseEnable::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.dashboard.CruiseEnable)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabled_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CruiseEnable::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enabled = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CruiseEnable::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.dashboard.CruiseEnable)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.dashboard.CruiseEnable)
  return target;
}

size_t CruiseEnable::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.dashboard.CruiseEnable)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool enabled = 1;
  if (this->_internal_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CruiseEnable::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CruiseEnable::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CruiseEnable::GetClassData() const { return &_class_data_; }

void CruiseEnable::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CruiseEnable *>(to)->MergeFrom(
      static_cast<const CruiseEnable &>(from));
}


void CruiseEnable::MergeFrom(const CruiseEnable& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.dashboard.CruiseEnable)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enabled() != 0) {
    _internal_set_enabled(from._internal_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CruiseEnable::CopyFrom(const CruiseEnable& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.dashboard.CruiseEnable)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CruiseEnable::IsInitialized() const {
  return true;
}

void CruiseEnable::InternalSwap(CruiseEnable* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(enabled_, other->enabled_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CruiseEnable::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdashboard_2eproto_getter, &descriptor_table_frontend_2fproto_2fdashboard_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdashboard_2eproto[14]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace dashboard
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::ExtraStatus* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::ExtraStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::ExtraStatus >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::WeedTargeting* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::WeedTargeting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::WeedTargeting >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::ThinningTargeting* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::ThinningTargeting >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::ThinningTargeting >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::TargetingState_EnabledRowsEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::TargetingState* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::TargetingState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::TargetingState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::ExtraConclusion* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::ExtraConclusion >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::ExtraConclusion >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::RowStateMessage* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::RowStateMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::RowStateMessage >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::DashboardStateMessage_RowStatesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::DashboardStateMessage* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::DashboardStateMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::DashboardStateMessage >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::CropModel* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::CropModel >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::CropModel >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::CropModelOptions* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::CropModelOptions >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::CropModelOptions >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::RowId* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::RowId >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::RowId >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::WeedingVelocity* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::WeedingVelocity >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::WeedingVelocity >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::RowSpacing* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::RowSpacing >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::RowSpacing >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::dashboard::CruiseEnable* Arena::CreateMaybeMessage< ::carbon::frontend::dashboard::CruiseEnable >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::dashboard::CruiseEnable >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

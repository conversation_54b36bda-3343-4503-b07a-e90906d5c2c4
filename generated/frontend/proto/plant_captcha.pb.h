// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/plant_captcha.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fplant_5fcaptcha_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fplant_5fcaptcha_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
#include "weed_tracking/proto/weed_tracking.pb.h"
#include "proto/almanac/almanac.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fplant_5fcaptcha_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[29]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fplant_5fcaptcha_2eproto;
namespace carbon {
namespace frontend {
namespace plant_captcha {
class CalculatePlantCaptchaRequest;
struct CalculatePlantCaptchaRequestDefaultTypeInternal;
extern CalculatePlantCaptchaRequestDefaultTypeInternal _CalculatePlantCaptchaRequest_default_instance_;
class CalculatePlantCaptchaResponse;
struct CalculatePlantCaptchaResponseDefaultTypeInternal;
extern CalculatePlantCaptchaResponseDefaultTypeInternal _CalculatePlantCaptchaResponse_default_instance_;
class CancelPlantCaptchaOnRowRequest;
struct CancelPlantCaptchaOnRowRequestDefaultTypeInternal;
extern CancelPlantCaptchaOnRowRequestDefaultTypeInternal _CancelPlantCaptchaOnRowRequest_default_instance_;
class DeletePlantCaptchaRequest;
struct DeletePlantCaptchaRequestDefaultTypeInternal;
extern DeletePlantCaptchaRequestDefaultTypeInternal _DeletePlantCaptchaRequest_default_instance_;
class GetCaptchaRowStatusResponse;
struct GetCaptchaRowStatusResponseDefaultTypeInternal;
extern GetCaptchaRowStatusResponseDefaultTypeInternal _GetCaptchaRowStatusResponse_default_instance_;
class GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse;
struct GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUseDefaultTypeInternal;
extern GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUseDefaultTypeInternal _GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse_default_instance_;
class GetNextPlantCaptchaStatusRequest;
struct GetNextPlantCaptchaStatusRequestDefaultTypeInternal;
extern GetNextPlantCaptchaStatusRequestDefaultTypeInternal _GetNextPlantCaptchaStatusRequest_default_instance_;
class GetNextPlantCaptchaStatusResponse;
struct GetNextPlantCaptchaStatusResponseDefaultTypeInternal;
extern GetNextPlantCaptchaStatusResponseDefaultTypeInternal _GetNextPlantCaptchaStatusResponse_default_instance_;
class GetNextPlantCaptchaUploadStateRequest;
struct GetNextPlantCaptchaUploadStateRequestDefaultTypeInternal;
extern GetNextPlantCaptchaUploadStateRequestDefaultTypeInternal _GetNextPlantCaptchaUploadStateRequest_default_instance_;
class GetNextPlantCaptchaUploadStateResponse;
struct GetNextPlantCaptchaUploadStateResponseDefaultTypeInternal;
extern GetNextPlantCaptchaUploadStateResponseDefaultTypeInternal _GetNextPlantCaptchaUploadStateResponse_default_instance_;
class GetNextPlantCaptchasListRequest;
struct GetNextPlantCaptchasListRequestDefaultTypeInternal;
extern GetNextPlantCaptchasListRequestDefaultTypeInternal _GetNextPlantCaptchasListRequest_default_instance_;
class GetNextPlantCaptchasListResponse;
struct GetNextPlantCaptchasListResponseDefaultTypeInternal;
extern GetNextPlantCaptchasListResponseDefaultTypeInternal _GetNextPlantCaptchasListResponse_default_instance_;
class GetOriginalModelinatorConfigRequest;
struct GetOriginalModelinatorConfigRequestDefaultTypeInternal;
extern GetOriginalModelinatorConfigRequestDefaultTypeInternal _GetOriginalModelinatorConfigRequest_default_instance_;
class GetOriginalModelinatorConfigResponse;
struct GetOriginalModelinatorConfigResponseDefaultTypeInternal;
extern GetOriginalModelinatorConfigResponseDefaultTypeInternal _GetOriginalModelinatorConfigResponse_default_instance_;
class GetPlantCaptchaItemResultsRequest;
struct GetPlantCaptchaItemResultsRequestDefaultTypeInternal;
extern GetPlantCaptchaItemResultsRequestDefaultTypeInternal _GetPlantCaptchaItemResultsRequest_default_instance_;
class GetPlantCaptchaItemResultsResponse;
struct GetPlantCaptchaItemResultsResponseDefaultTypeInternal;
extern GetPlantCaptchaItemResultsResponseDefaultTypeInternal _GetPlantCaptchaItemResultsResponse_default_instance_;
class GetPlantCaptchaRequest;
struct GetPlantCaptchaRequestDefaultTypeInternal;
extern GetPlantCaptchaRequestDefaultTypeInternal _GetPlantCaptchaRequest_default_instance_;
class GetPlantCaptchaResponse;
struct GetPlantCaptchaResponseDefaultTypeInternal;
extern GetPlantCaptchaResponseDefaultTypeInternal _GetPlantCaptchaResponse_default_instance_;
class PlantCaptcha;
struct PlantCaptchaDefaultTypeInternal;
extern PlantCaptchaDefaultTypeInternal _PlantCaptcha_default_instance_;
class PlantCaptchaItem;
struct PlantCaptchaItemDefaultTypeInternal;
extern PlantCaptchaItemDefaultTypeInternal _PlantCaptchaItem_default_instance_;
class PlantCaptchaItemResult;
struct PlantCaptchaItemResultDefaultTypeInternal;
extern PlantCaptchaItemResultDefaultTypeInternal _PlantCaptchaItemResult_default_instance_;
class PlantCaptchaListItem;
struct PlantCaptchaListItemDefaultTypeInternal;
extern PlantCaptchaListItemDefaultTypeInternal _PlantCaptchaListItem_default_instance_;
class PlantCaptchaResult;
struct PlantCaptchaResultDefaultTypeInternal;
extern PlantCaptchaResultDefaultTypeInternal _PlantCaptchaResult_default_instance_;
class PlantCaptchaResults;
struct PlantCaptchaResultsDefaultTypeInternal;
extern PlantCaptchaResultsDefaultTypeInternal _PlantCaptchaResults_default_instance_;
class StartPlantCaptchaRequest;
struct StartPlantCaptchaRequestDefaultTypeInternal;
extern StartPlantCaptchaRequestDefaultTypeInternal _StartPlantCaptchaRequest_default_instance_;
class StartPlantCaptchaResponse;
struct StartPlantCaptchaResponseDefaultTypeInternal;
extern StartPlantCaptchaResponseDefaultTypeInternal _StartPlantCaptchaResponse_default_instance_;
class StartPlantCaptchaUploadRequest;
struct StartPlantCaptchaUploadRequestDefaultTypeInternal;
extern StartPlantCaptchaUploadRequestDefaultTypeInternal _StartPlantCaptchaUploadRequest_default_instance_;
class SubmitPlantCaptchaResultsRequest;
struct SubmitPlantCaptchaResultsRequestDefaultTypeInternal;
extern SubmitPlantCaptchaResultsRequestDefaultTypeInternal _SubmitPlantCaptchaResultsRequest_default_instance_;
class VeselkaPlantCaptchaResponse;
struct VeselkaPlantCaptchaResponseDefaultTypeInternal;
extern VeselkaPlantCaptchaResponseDefaultTypeInternal _VeselkaPlantCaptchaResponse_default_instance_;
}  // namespace plant_captcha
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetPlantCaptchaRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::PlantCaptcha* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptcha>(Arena*);
template<> ::carbon::frontend::plant_captcha::PlantCaptchaItem* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptchaItem>(Arena*);
template<> ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptchaItemResult>(Arena*);
template<> ::carbon::frontend::plant_captcha::PlantCaptchaListItem* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptchaListItem>(Arena*);
template<> ::carbon::frontend::plant_captcha::PlantCaptchaResult* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptchaResult>(Arena*);
template<> ::carbon::frontend::plant_captcha::PlantCaptchaResults* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptchaResults>(Arena*);
template<> ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::StartPlantCaptchaRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>(Arena*);
template<> ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest>(Arena*);
template<> ::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse* Arena::CreateMaybeMessage<::carbon::frontend::plant_captcha::VeselkaPlantCaptchaResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace plant_captcha {

enum PlantCaptchaUploadState : int {
  NONE = 0,
  IN_PROGRESS = 1,
  DONE = 2,
  PlantCaptchaUploadState_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  PlantCaptchaUploadState_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool PlantCaptchaUploadState_IsValid(int value);
constexpr PlantCaptchaUploadState PlantCaptchaUploadState_MIN = NONE;
constexpr PlantCaptchaUploadState PlantCaptchaUploadState_MAX = DONE;
constexpr int PlantCaptchaUploadState_ARRAYSIZE = PlantCaptchaUploadState_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PlantCaptchaUploadState_descriptor();
template<typename T>
inline const std::string& PlantCaptchaUploadState_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PlantCaptchaUploadState>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PlantCaptchaUploadState_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PlantCaptchaUploadState_descriptor(), enum_t_value);
}
inline bool PlantCaptchaUploadState_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PlantCaptchaUploadState* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PlantCaptchaUploadState>(
    PlantCaptchaUploadState_descriptor(), name, value);
}
enum PlantLabelAlgorithmFailureReason : int {
  NO_FAILURE = 0,
  METRICS_NOT_MET = 1,
  NOT_ENOUGH_ITEMS = 2,
  PlantLabelAlgorithmFailureReason_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  PlantLabelAlgorithmFailureReason_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool PlantLabelAlgorithmFailureReason_IsValid(int value);
constexpr PlantLabelAlgorithmFailureReason PlantLabelAlgorithmFailureReason_MIN = NO_FAILURE;
constexpr PlantLabelAlgorithmFailureReason PlantLabelAlgorithmFailureReason_MAX = NOT_ENOUGH_ITEMS;
constexpr int PlantLabelAlgorithmFailureReason_ARRAYSIZE = PlantLabelAlgorithmFailureReason_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* PlantLabelAlgorithmFailureReason_descriptor();
template<typename T>
inline const std::string& PlantLabelAlgorithmFailureReason_Name(T enum_t_value) {
  static_assert(::std::is_same<T, PlantLabelAlgorithmFailureReason>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function PlantLabelAlgorithmFailureReason_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    PlantLabelAlgorithmFailureReason_descriptor(), enum_t_value);
}
inline bool PlantLabelAlgorithmFailureReason_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, PlantLabelAlgorithmFailureReason* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<PlantLabelAlgorithmFailureReason>(
    PlantLabelAlgorithmFailureReason_descriptor(), name, value);
}
// ===================================================================

class PlantCaptcha final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.PlantCaptcha) */ {
 public:
  inline PlantCaptcha() : PlantCaptcha(nullptr) {}
  ~PlantCaptcha() override;
  explicit constexpr PlantCaptcha(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlantCaptcha(const PlantCaptcha& from);
  PlantCaptcha(PlantCaptcha&& from) noexcept
    : PlantCaptcha() {
    *this = ::std::move(from);
  }

  inline PlantCaptcha& operator=(const PlantCaptcha& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlantCaptcha& operator=(PlantCaptcha&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PlantCaptcha& default_instance() {
    return *internal_default_instance();
  }
  static inline const PlantCaptcha* internal_default_instance() {
    return reinterpret_cast<const PlantCaptcha*>(
               &_PlantCaptcha_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PlantCaptcha& a, PlantCaptcha& b) {
    a.Swap(&b);
  }
  inline void Swap(PlantCaptcha* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlantCaptcha* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlantCaptcha* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlantCaptcha>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PlantCaptcha& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PlantCaptcha& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlantCaptcha* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.PlantCaptcha";
  }
  protected:
  explicit PlantCaptcha(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowsUsedFieldNumber = 6,
    kNameFieldNumber = 1,
    kModelIdFieldNumber = 2,
    kCropIdFieldNumber = 3,
    kCropNameFieldNumber = 4,
    kStartTimeMsFieldNumber = 5,
  };
  // repeated int32 rows_used = 6;
  int rows_used_size() const;
  private:
  int _internal_rows_used_size() const;
  public:
  void clear_rows_used();
  private:
  int32_t _internal_rows_used(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      _internal_rows_used() const;
  void _internal_add_rows_used(int32_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      _internal_mutable_rows_used();
  public:
  int32_t rows_used(int index) const;
  void set_rows_used(int index, int32_t value);
  void add_rows_used(int32_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
      rows_used() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
      mutable_rows_used();

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string model_id = 2;
  void clear_model_id();
  const std::string& model_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_id();
  PROTOBUF_NODISCARD std::string* release_model_id();
  void set_allocated_model_id(std::string* model_id);
  private:
  const std::string& _internal_model_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_id(const std::string& value);
  std::string* _internal_mutable_model_id();
  public:

  // string crop_id = 3;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // string crop_name = 4;
  void clear_crop_name();
  const std::string& crop_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_name();
  PROTOBUF_NODISCARD std::string* release_crop_name();
  void set_allocated_crop_name(std::string* crop_name);
  private:
  const std::string& _internal_crop_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_name(const std::string& value);
  std::string* _internal_mutable_crop_name();
  public:

  // int64 start_time_ms = 5;
  void clear_start_time_ms();
  int64_t start_time_ms() const;
  void set_start_time_ms(int64_t value);
  private:
  int64_t _internal_start_time_ms() const;
  void _internal_set_start_time_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptcha)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t > rows_used_;
  mutable std::atomic<int> _rows_used_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_name_;
  int64_t start_time_ms_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class StartPlantCaptchaRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.StartPlantCaptchaRequest) */ {
 public:
  inline StartPlantCaptchaRequest() : StartPlantCaptchaRequest(nullptr) {}
  ~StartPlantCaptchaRequest() override;
  explicit constexpr StartPlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StartPlantCaptchaRequest(const StartPlantCaptchaRequest& from);
  StartPlantCaptchaRequest(StartPlantCaptchaRequest&& from) noexcept
    : StartPlantCaptchaRequest() {
    *this = ::std::move(from);
  }

  inline StartPlantCaptchaRequest& operator=(const StartPlantCaptchaRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline StartPlantCaptchaRequest& operator=(StartPlantCaptchaRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StartPlantCaptchaRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const StartPlantCaptchaRequest* internal_default_instance() {
    return reinterpret_cast<const StartPlantCaptchaRequest*>(
               &_StartPlantCaptchaRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(StartPlantCaptchaRequest& a, StartPlantCaptchaRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(StartPlantCaptchaRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StartPlantCaptchaRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StartPlantCaptchaRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StartPlantCaptchaRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StartPlantCaptchaRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StartPlantCaptchaRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StartPlantCaptchaRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.StartPlantCaptchaRequest";
  }
  protected:
  explicit StartPlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlantCaptchaFieldNumber = 1,
  };
  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  bool has_plant_captcha() const;
  private:
  bool _internal_has_plant_captcha() const;
  public:
  void clear_plant_captcha();
  const ::carbon::frontend::plant_captcha::PlantCaptcha& plant_captcha() const;
  PROTOBUF_NODISCARD ::carbon::frontend::plant_captcha::PlantCaptcha* release_plant_captcha();
  ::carbon::frontend::plant_captcha::PlantCaptcha* mutable_plant_captcha();
  void set_allocated_plant_captcha(::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha);
  private:
  const ::carbon::frontend::plant_captcha::PlantCaptcha& _internal_plant_captcha() const;
  ::carbon::frontend::plant_captcha::PlantCaptcha* _internal_mutable_plant_captcha();
  public:
  void unsafe_arena_set_allocated_plant_captcha(
      ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha);
  ::carbon::frontend::plant_captcha::PlantCaptcha* unsafe_arena_release_plant_captcha();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.StartPlantCaptchaRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class StartPlantCaptchaResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.StartPlantCaptchaResponse) */ {
 public:
  inline StartPlantCaptchaResponse() : StartPlantCaptchaResponse(nullptr) {}
  explicit constexpr StartPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StartPlantCaptchaResponse(const StartPlantCaptchaResponse& from);
  StartPlantCaptchaResponse(StartPlantCaptchaResponse&& from) noexcept
    : StartPlantCaptchaResponse() {
    *this = ::std::move(from);
  }

  inline StartPlantCaptchaResponse& operator=(const StartPlantCaptchaResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline StartPlantCaptchaResponse& operator=(StartPlantCaptchaResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StartPlantCaptchaResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const StartPlantCaptchaResponse* internal_default_instance() {
    return reinterpret_cast<const StartPlantCaptchaResponse*>(
               &_StartPlantCaptchaResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(StartPlantCaptchaResponse& a, StartPlantCaptchaResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(StartPlantCaptchaResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StartPlantCaptchaResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StartPlantCaptchaResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StartPlantCaptchaResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const StartPlantCaptchaResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const StartPlantCaptchaResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.StartPlantCaptchaResponse";
  }
  protected:
  explicit StartPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.StartPlantCaptchaResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetNextPlantCaptchaStatusRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest) */ {
 public:
  inline GetNextPlantCaptchaStatusRequest() : GetNextPlantCaptchaStatusRequest(nullptr) {}
  ~GetNextPlantCaptchaStatusRequest() override;
  explicit constexpr GetNextPlantCaptchaStatusRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextPlantCaptchaStatusRequest(const GetNextPlantCaptchaStatusRequest& from);
  GetNextPlantCaptchaStatusRequest(GetNextPlantCaptchaStatusRequest&& from) noexcept
    : GetNextPlantCaptchaStatusRequest() {
    *this = ::std::move(from);
  }

  inline GetNextPlantCaptchaStatusRequest& operator=(const GetNextPlantCaptchaStatusRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextPlantCaptchaStatusRequest& operator=(GetNextPlantCaptchaStatusRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextPlantCaptchaStatusRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextPlantCaptchaStatusRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextPlantCaptchaStatusRequest*>(
               &_GetNextPlantCaptchaStatusRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetNextPlantCaptchaStatusRequest& a, GetNextPlantCaptchaStatusRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextPlantCaptchaStatusRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextPlantCaptchaStatusRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextPlantCaptchaStatusRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextPlantCaptchaStatusRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextPlantCaptchaStatusRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextPlantCaptchaStatusRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextPlantCaptchaStatusRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest";
  }
  protected:
  explicit GetNextPlantCaptchaStatusRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetNextPlantCaptchaStatusResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse) */ {
 public:
  inline GetNextPlantCaptchaStatusResponse() : GetNextPlantCaptchaStatusResponse(nullptr) {}
  ~GetNextPlantCaptchaStatusResponse() override;
  explicit constexpr GetNextPlantCaptchaStatusResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextPlantCaptchaStatusResponse(const GetNextPlantCaptchaStatusResponse& from);
  GetNextPlantCaptchaStatusResponse(GetNextPlantCaptchaStatusResponse&& from) noexcept
    : GetNextPlantCaptchaStatusResponse() {
    *this = ::std::move(from);
  }

  inline GetNextPlantCaptchaStatusResponse& operator=(const GetNextPlantCaptchaStatusResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextPlantCaptchaStatusResponse& operator=(GetNextPlantCaptchaStatusResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextPlantCaptchaStatusResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextPlantCaptchaStatusResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextPlantCaptchaStatusResponse*>(
               &_GetNextPlantCaptchaStatusResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetNextPlantCaptchaStatusResponse& a, GetNextPlantCaptchaStatusResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextPlantCaptchaStatusResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextPlantCaptchaStatusResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextPlantCaptchaStatusResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextPlantCaptchaStatusResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextPlantCaptchaStatusResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextPlantCaptchaStatusResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextPlantCaptchaStatusResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse";
  }
  protected:
  explicit GetNextPlantCaptchaStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kStatusFieldNumber = 2,
    kTotalImagesFieldNumber = 3,
    kImagesTakenFieldNumber = 4,
    kMetadataTakenFieldNumber = 5,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .weed_tracking.PlantCaptchaStatus status = 2;
  void clear_status();
  ::weed_tracking::PlantCaptchaStatus status() const;
  void set_status(::weed_tracking::PlantCaptchaStatus value);
  private:
  ::weed_tracking::PlantCaptchaStatus _internal_status() const;
  void _internal_set_status(::weed_tracking::PlantCaptchaStatus value);
  public:

  // int32 total_images = 3;
  void clear_total_images();
  int32_t total_images() const;
  void set_total_images(int32_t value);
  private:
  int32_t _internal_total_images() const;
  void _internal_set_total_images(int32_t value);
  public:

  // int32 images_taken = 4;
  void clear_images_taken();
  int32_t images_taken() const;
  void set_images_taken(int32_t value);
  private:
  int32_t _internal_images_taken() const;
  void _internal_set_images_taken(int32_t value);
  public:

  // int32 metadata_taken = 5;
  void clear_metadata_taken();
  int32_t metadata_taken() const;
  void set_metadata_taken(int32_t value);
  private:
  int32_t _internal_metadata_taken() const;
  void _internal_set_metadata_taken(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  int status_;
  int32_t total_images_;
  int32_t images_taken_;
  int32_t metadata_taken_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetNextPlantCaptchasListRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest) */ {
 public:
  inline GetNextPlantCaptchasListRequest() : GetNextPlantCaptchasListRequest(nullptr) {}
  ~GetNextPlantCaptchasListRequest() override;
  explicit constexpr GetNextPlantCaptchasListRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextPlantCaptchasListRequest(const GetNextPlantCaptchasListRequest& from);
  GetNextPlantCaptchasListRequest(GetNextPlantCaptchasListRequest&& from) noexcept
    : GetNextPlantCaptchasListRequest() {
    *this = ::std::move(from);
  }

  inline GetNextPlantCaptchasListRequest& operator=(const GetNextPlantCaptchasListRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextPlantCaptchasListRequest& operator=(GetNextPlantCaptchasListRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextPlantCaptchasListRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextPlantCaptchasListRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextPlantCaptchasListRequest*>(
               &_GetNextPlantCaptchasListRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetNextPlantCaptchasListRequest& a, GetNextPlantCaptchasListRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextPlantCaptchasListRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextPlantCaptchasListRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextPlantCaptchasListRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextPlantCaptchasListRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextPlantCaptchasListRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextPlantCaptchasListRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextPlantCaptchasListRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest";
  }
  protected:
  explicit GetNextPlantCaptchasListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class PlantCaptchaListItem final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.PlantCaptchaListItem) */ {
 public:
  inline PlantCaptchaListItem() : PlantCaptchaListItem(nullptr) {}
  ~PlantCaptchaListItem() override;
  explicit constexpr PlantCaptchaListItem(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlantCaptchaListItem(const PlantCaptchaListItem& from);
  PlantCaptchaListItem(PlantCaptchaListItem&& from) noexcept
    : PlantCaptchaListItem() {
    *this = ::std::move(from);
  }

  inline PlantCaptchaListItem& operator=(const PlantCaptchaListItem& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlantCaptchaListItem& operator=(PlantCaptchaListItem&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PlantCaptchaListItem& default_instance() {
    return *internal_default_instance();
  }
  static inline const PlantCaptchaListItem* internal_default_instance() {
    return reinterpret_cast<const PlantCaptchaListItem*>(
               &_PlantCaptchaListItem_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(PlantCaptchaListItem& a, PlantCaptchaListItem& b) {
    a.Swap(&b);
  }
  inline void Swap(PlantCaptchaListItem* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlantCaptchaListItem* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlantCaptchaListItem* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlantCaptchaListItem>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PlantCaptchaListItem& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PlantCaptchaListItem& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlantCaptchaListItem* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.PlantCaptchaListItem";
  }
  protected:
  explicit PlantCaptchaListItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlantCaptchaFieldNumber = 1,
    kImagesTakenFieldNumber = 2,
    kImagesProcessedFieldNumber = 3,
  };
  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  bool has_plant_captcha() const;
  private:
  bool _internal_has_plant_captcha() const;
  public:
  void clear_plant_captcha();
  const ::carbon::frontend::plant_captcha::PlantCaptcha& plant_captcha() const;
  PROTOBUF_NODISCARD ::carbon::frontend::plant_captcha::PlantCaptcha* release_plant_captcha();
  ::carbon::frontend::plant_captcha::PlantCaptcha* mutable_plant_captcha();
  void set_allocated_plant_captcha(::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha);
  private:
  const ::carbon::frontend::plant_captcha::PlantCaptcha& _internal_plant_captcha() const;
  ::carbon::frontend::plant_captcha::PlantCaptcha* _internal_mutable_plant_captcha();
  public:
  void unsafe_arena_set_allocated_plant_captcha(
      ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha);
  ::carbon::frontend::plant_captcha::PlantCaptcha* unsafe_arena_release_plant_captcha();

  // int32 images_taken = 2;
  void clear_images_taken();
  int32_t images_taken() const;
  void set_images_taken(int32_t value);
  private:
  int32_t _internal_images_taken() const;
  void _internal_set_images_taken(int32_t value);
  public:

  // int32 images_processed = 3;
  void clear_images_processed();
  int32_t images_processed() const;
  void set_images_processed(int32_t value);
  private:
  int32_t _internal_images_processed() const;
  void _internal_set_images_processed(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaListItem)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha_;
  int32_t images_taken_;
  int32_t images_processed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetNextPlantCaptchasListResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse) */ {
 public:
  inline GetNextPlantCaptchasListResponse() : GetNextPlantCaptchasListResponse(nullptr) {}
  ~GetNextPlantCaptchasListResponse() override;
  explicit constexpr GetNextPlantCaptchasListResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextPlantCaptchasListResponse(const GetNextPlantCaptchasListResponse& from);
  GetNextPlantCaptchasListResponse(GetNextPlantCaptchasListResponse&& from) noexcept
    : GetNextPlantCaptchasListResponse() {
    *this = ::std::move(from);
  }

  inline GetNextPlantCaptchasListResponse& operator=(const GetNextPlantCaptchasListResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextPlantCaptchasListResponse& operator=(GetNextPlantCaptchasListResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextPlantCaptchasListResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextPlantCaptchasListResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextPlantCaptchasListResponse*>(
               &_GetNextPlantCaptchasListResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(GetNextPlantCaptchasListResponse& a, GetNextPlantCaptchasListResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextPlantCaptchasListResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextPlantCaptchasListResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextPlantCaptchasListResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextPlantCaptchasListResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextPlantCaptchasListResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextPlantCaptchasListResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextPlantCaptchasListResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse";
  }
  protected:
  explicit GetNextPlantCaptchasListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPlantCaptchasFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.frontend.plant_captcha.PlantCaptchaListItem plant_captchas = 2;
  int plant_captchas_size() const;
  private:
  int _internal_plant_captchas_size() const;
  public:
  void clear_plant_captchas();
  ::carbon::frontend::plant_captcha::PlantCaptchaListItem* mutable_plant_captchas(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaListItem >*
      mutable_plant_captchas();
  private:
  const ::carbon::frontend::plant_captcha::PlantCaptchaListItem& _internal_plant_captchas(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaListItem* _internal_add_plant_captchas();
  public:
  const ::carbon::frontend::plant_captcha::PlantCaptchaListItem& plant_captchas(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaListItem* add_plant_captchas();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaListItem >&
      plant_captchas() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaListItem > plant_captchas_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class DeletePlantCaptchaRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest) */ {
 public:
  inline DeletePlantCaptchaRequest() : DeletePlantCaptchaRequest(nullptr) {}
  ~DeletePlantCaptchaRequest() override;
  explicit constexpr DeletePlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeletePlantCaptchaRequest(const DeletePlantCaptchaRequest& from);
  DeletePlantCaptchaRequest(DeletePlantCaptchaRequest&& from) noexcept
    : DeletePlantCaptchaRequest() {
    *this = ::std::move(from);
  }

  inline DeletePlantCaptchaRequest& operator=(const DeletePlantCaptchaRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeletePlantCaptchaRequest& operator=(DeletePlantCaptchaRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeletePlantCaptchaRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeletePlantCaptchaRequest* internal_default_instance() {
    return reinterpret_cast<const DeletePlantCaptchaRequest*>(
               &_DeletePlantCaptchaRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(DeletePlantCaptchaRequest& a, DeletePlantCaptchaRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeletePlantCaptchaRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeletePlantCaptchaRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeletePlantCaptchaRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeletePlantCaptchaRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeletePlantCaptchaRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeletePlantCaptchaRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeletePlantCaptchaRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.DeletePlantCaptchaRequest";
  }
  protected:
  explicit DeletePlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetPlantCaptchaRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetPlantCaptchaRequest) */ {
 public:
  inline GetPlantCaptchaRequest() : GetPlantCaptchaRequest(nullptr) {}
  ~GetPlantCaptchaRequest() override;
  explicit constexpr GetPlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPlantCaptchaRequest(const GetPlantCaptchaRequest& from);
  GetPlantCaptchaRequest(GetPlantCaptchaRequest&& from) noexcept
    : GetPlantCaptchaRequest() {
    *this = ::std::move(from);
  }

  inline GetPlantCaptchaRequest& operator=(const GetPlantCaptchaRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPlantCaptchaRequest& operator=(GetPlantCaptchaRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPlantCaptchaRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPlantCaptchaRequest* internal_default_instance() {
    return reinterpret_cast<const GetPlantCaptchaRequest*>(
               &_GetPlantCaptchaRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(GetPlantCaptchaRequest& a, GetPlantCaptchaRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPlantCaptchaRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPlantCaptchaRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPlantCaptchaRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPlantCaptchaRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPlantCaptchaRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPlantCaptchaRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPlantCaptchaRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetPlantCaptchaRequest";
  }
  protected:
  explicit GetPlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetPlantCaptchaRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class PlantCaptchaItem final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.PlantCaptchaItem) */ {
 public:
  inline PlantCaptchaItem() : PlantCaptchaItem(nullptr) {}
  ~PlantCaptchaItem() override;
  explicit constexpr PlantCaptchaItem(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlantCaptchaItem(const PlantCaptchaItem& from);
  PlantCaptchaItem(PlantCaptchaItem&& from) noexcept
    : PlantCaptchaItem() {
    *this = ::std::move(from);
  }

  inline PlantCaptchaItem& operator=(const PlantCaptchaItem& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlantCaptchaItem& operator=(PlantCaptchaItem&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PlantCaptchaItem& default_instance() {
    return *internal_default_instance();
  }
  static inline const PlantCaptchaItem* internal_default_instance() {
    return reinterpret_cast<const PlantCaptchaItem*>(
               &_PlantCaptchaItem_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(PlantCaptchaItem& a, PlantCaptchaItem& b) {
    a.Swap(&b);
  }
  inline void Swap(PlantCaptchaItem* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlantCaptchaItem* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlantCaptchaItem* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlantCaptchaItem>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PlantCaptchaItem& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PlantCaptchaItem& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlantCaptchaItem* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.PlantCaptchaItem";
  }
  protected:
  explicit PlantCaptchaItem(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAdditionalImageUrlsFieldNumber = 3,
    kAdditionalMetadatasFieldNumber = 4,
    kImageUrlFieldNumber = 1,
    kMetadataFieldNumber = 2,
  };
  // repeated string additional_image_urls = 3;
  int additional_image_urls_size() const;
  private:
  int _internal_additional_image_urls_size() const;
  public:
  void clear_additional_image_urls();
  const std::string& additional_image_urls(int index) const;
  std::string* mutable_additional_image_urls(int index);
  void set_additional_image_urls(int index, const std::string& value);
  void set_additional_image_urls(int index, std::string&& value);
  void set_additional_image_urls(int index, const char* value);
  void set_additional_image_urls(int index, const char* value, size_t size);
  std::string* add_additional_image_urls();
  void add_additional_image_urls(const std::string& value);
  void add_additional_image_urls(std::string&& value);
  void add_additional_image_urls(const char* value);
  void add_additional_image_urls(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& additional_image_urls() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_additional_image_urls();
  private:
  const std::string& _internal_additional_image_urls(int index) const;
  std::string* _internal_add_additional_image_urls();
  public:

  // repeated .weed_tracking.PlantCaptchaItemMetadata additional_metadatas = 4;
  int additional_metadatas_size() const;
  private:
  int _internal_additional_metadatas_size() const;
  public:
  void clear_additional_metadatas();
  ::weed_tracking::PlantCaptchaItemMetadata* mutable_additional_metadatas(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::PlantCaptchaItemMetadata >*
      mutable_additional_metadatas();
  private:
  const ::weed_tracking::PlantCaptchaItemMetadata& _internal_additional_metadatas(int index) const;
  ::weed_tracking::PlantCaptchaItemMetadata* _internal_add_additional_metadatas();
  public:
  const ::weed_tracking::PlantCaptchaItemMetadata& additional_metadatas(int index) const;
  ::weed_tracking::PlantCaptchaItemMetadata* add_additional_metadatas();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::PlantCaptchaItemMetadata >&
      additional_metadatas() const;

  // string image_url = 1;
  void clear_image_url();
  const std::string& image_url() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_image_url(ArgT0&& arg0, ArgT... args);
  std::string* mutable_image_url();
  PROTOBUF_NODISCARD std::string* release_image_url();
  void set_allocated_image_url(std::string* image_url);
  private:
  const std::string& _internal_image_url() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_image_url(const std::string& value);
  std::string* _internal_mutable_image_url();
  public:

  // .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
  bool has_metadata() const;
  private:
  bool _internal_has_metadata() const;
  public:
  void clear_metadata();
  const ::weed_tracking::PlantCaptchaItemMetadata& metadata() const;
  PROTOBUF_NODISCARD ::weed_tracking::PlantCaptchaItemMetadata* release_metadata();
  ::weed_tracking::PlantCaptchaItemMetadata* mutable_metadata();
  void set_allocated_metadata(::weed_tracking::PlantCaptchaItemMetadata* metadata);
  private:
  const ::weed_tracking::PlantCaptchaItemMetadata& _internal_metadata() const;
  ::weed_tracking::PlantCaptchaItemMetadata* _internal_mutable_metadata();
  public:
  void unsafe_arena_set_allocated_metadata(
      ::weed_tracking::PlantCaptchaItemMetadata* metadata);
  ::weed_tracking::PlantCaptchaItemMetadata* unsafe_arena_release_metadata();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaItem)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> additional_image_urls_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::PlantCaptchaItemMetadata > additional_metadatas_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr image_url_;
  ::weed_tracking::PlantCaptchaItemMetadata* metadata_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetPlantCaptchaResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetPlantCaptchaResponse) */ {
 public:
  inline GetPlantCaptchaResponse() : GetPlantCaptchaResponse(nullptr) {}
  ~GetPlantCaptchaResponse() override;
  explicit constexpr GetPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPlantCaptchaResponse(const GetPlantCaptchaResponse& from);
  GetPlantCaptchaResponse(GetPlantCaptchaResponse&& from) noexcept
    : GetPlantCaptchaResponse() {
    *this = ::std::move(from);
  }

  inline GetPlantCaptchaResponse& operator=(const GetPlantCaptchaResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPlantCaptchaResponse& operator=(GetPlantCaptchaResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPlantCaptchaResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPlantCaptchaResponse* internal_default_instance() {
    return reinterpret_cast<const GetPlantCaptchaResponse*>(
               &_GetPlantCaptchaResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(GetPlantCaptchaResponse& a, GetPlantCaptchaResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPlantCaptchaResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPlantCaptchaResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPlantCaptchaResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPlantCaptchaResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPlantCaptchaResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPlantCaptchaResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPlantCaptchaResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetPlantCaptchaResponse";
  }
  protected:
  explicit GetPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kItemsFieldNumber = 2,
    kPlantCaptchaFieldNumber = 1,
  };
  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItem items = 2;
  int items_size() const;
  private:
  int _internal_items_size() const;
  public:
  void clear_items();
  ::carbon::frontend::plant_captcha::PlantCaptchaItem* mutable_items(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItem >*
      mutable_items();
  private:
  const ::carbon::frontend::plant_captcha::PlantCaptchaItem& _internal_items(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaItem* _internal_add_items();
  public:
  const ::carbon::frontend::plant_captcha::PlantCaptchaItem& items(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaItem* add_items();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItem >&
      items() const;

  // .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
  bool has_plant_captcha() const;
  private:
  bool _internal_has_plant_captcha() const;
  public:
  void clear_plant_captcha();
  const ::carbon::frontend::plant_captcha::PlantCaptcha& plant_captcha() const;
  PROTOBUF_NODISCARD ::carbon::frontend::plant_captcha::PlantCaptcha* release_plant_captcha();
  ::carbon::frontend::plant_captcha::PlantCaptcha* mutable_plant_captcha();
  void set_allocated_plant_captcha(::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha);
  private:
  const ::carbon::frontend::plant_captcha::PlantCaptcha& _internal_plant_captcha() const;
  ::carbon::frontend::plant_captcha::PlantCaptcha* _internal_mutable_plant_captcha();
  public:
  void unsafe_arena_set_allocated_plant_captcha(
      ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha);
  ::carbon::frontend::plant_captcha::PlantCaptcha* unsafe_arena_release_plant_captcha();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetPlantCaptchaResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItem > items_;
  ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class StartPlantCaptchaUploadRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest) */ {
 public:
  inline StartPlantCaptchaUploadRequest() : StartPlantCaptchaUploadRequest(nullptr) {}
  ~StartPlantCaptchaUploadRequest() override;
  explicit constexpr StartPlantCaptchaUploadRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StartPlantCaptchaUploadRequest(const StartPlantCaptchaUploadRequest& from);
  StartPlantCaptchaUploadRequest(StartPlantCaptchaUploadRequest&& from) noexcept
    : StartPlantCaptchaUploadRequest() {
    *this = ::std::move(from);
  }

  inline StartPlantCaptchaUploadRequest& operator=(const StartPlantCaptchaUploadRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline StartPlantCaptchaUploadRequest& operator=(StartPlantCaptchaUploadRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StartPlantCaptchaUploadRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const StartPlantCaptchaUploadRequest* internal_default_instance() {
    return reinterpret_cast<const StartPlantCaptchaUploadRequest*>(
               &_StartPlantCaptchaUploadRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(StartPlantCaptchaUploadRequest& a, StartPlantCaptchaUploadRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(StartPlantCaptchaUploadRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StartPlantCaptchaUploadRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StartPlantCaptchaUploadRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StartPlantCaptchaUploadRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StartPlantCaptchaUploadRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StartPlantCaptchaUploadRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StartPlantCaptchaUploadRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest";
  }
  protected:
  explicit StartPlantCaptchaUploadRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetNextPlantCaptchaUploadStateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest) */ {
 public:
  inline GetNextPlantCaptchaUploadStateRequest() : GetNextPlantCaptchaUploadStateRequest(nullptr) {}
  ~GetNextPlantCaptchaUploadStateRequest() override;
  explicit constexpr GetNextPlantCaptchaUploadStateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextPlantCaptchaUploadStateRequest(const GetNextPlantCaptchaUploadStateRequest& from);
  GetNextPlantCaptchaUploadStateRequest(GetNextPlantCaptchaUploadStateRequest&& from) noexcept
    : GetNextPlantCaptchaUploadStateRequest() {
    *this = ::std::move(from);
  }

  inline GetNextPlantCaptchaUploadStateRequest& operator=(const GetNextPlantCaptchaUploadStateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextPlantCaptchaUploadStateRequest& operator=(GetNextPlantCaptchaUploadStateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextPlantCaptchaUploadStateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextPlantCaptchaUploadStateRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextPlantCaptchaUploadStateRequest*>(
               &_GetNextPlantCaptchaUploadStateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(GetNextPlantCaptchaUploadStateRequest& a, GetNextPlantCaptchaUploadStateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextPlantCaptchaUploadStateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextPlantCaptchaUploadStateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextPlantCaptchaUploadStateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextPlantCaptchaUploadStateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextPlantCaptchaUploadStateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextPlantCaptchaUploadStateRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextPlantCaptchaUploadStateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest";
  }
  protected:
  explicit GetNextPlantCaptchaUploadStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetNextPlantCaptchaUploadStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse) */ {
 public:
  inline GetNextPlantCaptchaUploadStateResponse() : GetNextPlantCaptchaUploadStateResponse(nullptr) {}
  ~GetNextPlantCaptchaUploadStateResponse() override;
  explicit constexpr GetNextPlantCaptchaUploadStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextPlantCaptchaUploadStateResponse(const GetNextPlantCaptchaUploadStateResponse& from);
  GetNextPlantCaptchaUploadStateResponse(GetNextPlantCaptchaUploadStateResponse&& from) noexcept
    : GetNextPlantCaptchaUploadStateResponse() {
    *this = ::std::move(from);
  }

  inline GetNextPlantCaptchaUploadStateResponse& operator=(const GetNextPlantCaptchaUploadStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextPlantCaptchaUploadStateResponse& operator=(GetNextPlantCaptchaUploadStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextPlantCaptchaUploadStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextPlantCaptchaUploadStateResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextPlantCaptchaUploadStateResponse*>(
               &_GetNextPlantCaptchaUploadStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(GetNextPlantCaptchaUploadStateResponse& a, GetNextPlantCaptchaUploadStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextPlantCaptchaUploadStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextPlantCaptchaUploadStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextPlantCaptchaUploadStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextPlantCaptchaUploadStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextPlantCaptchaUploadStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextPlantCaptchaUploadStateResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextPlantCaptchaUploadStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse";
  }
  protected:
  explicit GetNextPlantCaptchaUploadStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kUploadStateFieldNumber = 2,
    kPercentFieldNumber = 3,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.plant_captcha.PlantCaptchaUploadState upload_state = 2;
  void clear_upload_state();
  ::carbon::frontend::plant_captcha::PlantCaptchaUploadState upload_state() const;
  void set_upload_state(::carbon::frontend::plant_captcha::PlantCaptchaUploadState value);
  private:
  ::carbon::frontend::plant_captcha::PlantCaptchaUploadState _internal_upload_state() const;
  void _internal_set_upload_state(::carbon::frontend::plant_captcha::PlantCaptchaUploadState value);
  public:

  // int32 percent = 3;
  void clear_percent();
  int32_t percent() const;
  void set_percent(int32_t value);
  private:
  int32_t _internal_percent() const;
  void _internal_set_percent(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  int upload_state_;
  int32_t percent_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class PlantCaptchaItemResult final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.PlantCaptchaItemResult) */ {
 public:
  inline PlantCaptchaItemResult() : PlantCaptchaItemResult(nullptr) {}
  ~PlantCaptchaItemResult() override;
  explicit constexpr PlantCaptchaItemResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlantCaptchaItemResult(const PlantCaptchaItemResult& from);
  PlantCaptchaItemResult(PlantCaptchaItemResult&& from) noexcept
    : PlantCaptchaItemResult() {
    *this = ::std::move(from);
  }

  inline PlantCaptchaItemResult& operator=(const PlantCaptchaItemResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlantCaptchaItemResult& operator=(PlantCaptchaItemResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PlantCaptchaItemResult& default_instance() {
    return *internal_default_instance();
  }
  static inline const PlantCaptchaItemResult* internal_default_instance() {
    return reinterpret_cast<const PlantCaptchaItemResult*>(
               &_PlantCaptchaItemResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(PlantCaptchaItemResult& a, PlantCaptchaItemResult& b) {
    a.Swap(&b);
  }
  inline void Swap(PlantCaptchaItemResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlantCaptchaItemResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlantCaptchaItemResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlantCaptchaItemResult>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PlantCaptchaItemResult& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PlantCaptchaItemResult& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlantCaptchaItemResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.PlantCaptchaItemResult";
  }
  protected:
  explicit PlantCaptchaItemResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
    kUserPredictionFieldNumber = 2,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // .weed_tracking.PlantCaptchaUserPrediction user_prediction = 2;
  void clear_user_prediction();
  ::weed_tracking::PlantCaptchaUserPrediction user_prediction() const;
  void set_user_prediction(::weed_tracking::PlantCaptchaUserPrediction value);
  private:
  ::weed_tracking::PlantCaptchaUserPrediction _internal_user_prediction() const;
  void _internal_set_user_prediction(::weed_tracking::PlantCaptchaUserPrediction value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaItemResult)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  int user_prediction_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class SubmitPlantCaptchaResultsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest) */ {
 public:
  inline SubmitPlantCaptchaResultsRequest() : SubmitPlantCaptchaResultsRequest(nullptr) {}
  ~SubmitPlantCaptchaResultsRequest() override;
  explicit constexpr SubmitPlantCaptchaResultsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SubmitPlantCaptchaResultsRequest(const SubmitPlantCaptchaResultsRequest& from);
  SubmitPlantCaptchaResultsRequest(SubmitPlantCaptchaResultsRequest&& from) noexcept
    : SubmitPlantCaptchaResultsRequest() {
    *this = ::std::move(from);
  }

  inline SubmitPlantCaptchaResultsRequest& operator=(const SubmitPlantCaptchaResultsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SubmitPlantCaptchaResultsRequest& operator=(SubmitPlantCaptchaResultsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SubmitPlantCaptchaResultsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SubmitPlantCaptchaResultsRequest* internal_default_instance() {
    return reinterpret_cast<const SubmitPlantCaptchaResultsRequest*>(
               &_SubmitPlantCaptchaResultsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(SubmitPlantCaptchaResultsRequest& a, SubmitPlantCaptchaResultsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SubmitPlantCaptchaResultsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SubmitPlantCaptchaResultsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SubmitPlantCaptchaResultsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SubmitPlantCaptchaResultsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SubmitPlantCaptchaResultsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SubmitPlantCaptchaResultsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SubmitPlantCaptchaResultsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest";
  }
  protected:
  explicit SubmitPlantCaptchaResultsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResultsFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 2;
  int results_size() const;
  private:
  int _internal_results_size() const;
  public:
  void clear_results();
  ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* mutable_results(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >*
      mutable_results();
  private:
  const ::carbon::frontend::plant_captcha::PlantCaptchaItemResult& _internal_results(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* _internal_add_results();
  public:
  const ::carbon::frontend::plant_captcha::PlantCaptchaItemResult& results(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* add_results();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >&
      results() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult > results_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetPlantCaptchaItemResultsRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest) */ {
 public:
  inline GetPlantCaptchaItemResultsRequest() : GetPlantCaptchaItemResultsRequest(nullptr) {}
  ~GetPlantCaptchaItemResultsRequest() override;
  explicit constexpr GetPlantCaptchaItemResultsRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPlantCaptchaItemResultsRequest(const GetPlantCaptchaItemResultsRequest& from);
  GetPlantCaptchaItemResultsRequest(GetPlantCaptchaItemResultsRequest&& from) noexcept
    : GetPlantCaptchaItemResultsRequest() {
    *this = ::std::move(from);
  }

  inline GetPlantCaptchaItemResultsRequest& operator=(const GetPlantCaptchaItemResultsRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPlantCaptchaItemResultsRequest& operator=(GetPlantCaptchaItemResultsRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPlantCaptchaItemResultsRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPlantCaptchaItemResultsRequest* internal_default_instance() {
    return reinterpret_cast<const GetPlantCaptchaItemResultsRequest*>(
               &_GetPlantCaptchaItemResultsRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(GetPlantCaptchaItemResultsRequest& a, GetPlantCaptchaItemResultsRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPlantCaptchaItemResultsRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPlantCaptchaItemResultsRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPlantCaptchaItemResultsRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPlantCaptchaItemResultsRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPlantCaptchaItemResultsRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPlantCaptchaItemResultsRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPlantCaptchaItemResultsRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest";
  }
  protected:
  explicit GetPlantCaptchaItemResultsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 2,
    kNameFieldNumber = 1,
  };
  // repeated string id = 2;
  int id_size() const;
  private:
  int _internal_id_size() const;
  public:
  void clear_id();
  const std::string& id(int index) const;
  std::string* mutable_id(int index);
  void set_id(int index, const std::string& value);
  void set_id(int index, std::string&& value);
  void set_id(int index, const char* value);
  void set_id(int index, const char* value, size_t size);
  std::string* add_id();
  void add_id(const std::string& value);
  void add_id(std::string&& value);
  void add_id(const char* value);
  void add_id(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& id() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_id();
  private:
  const std::string& _internal_id(int index) const;
  std::string* _internal_add_id();
  public:

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetPlantCaptchaItemResultsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse) */ {
 public:
  inline GetPlantCaptchaItemResultsResponse() : GetPlantCaptchaItemResultsResponse(nullptr) {}
  ~GetPlantCaptchaItemResultsResponse() override;
  explicit constexpr GetPlantCaptchaItemResultsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPlantCaptchaItemResultsResponse(const GetPlantCaptchaItemResultsResponse& from);
  GetPlantCaptchaItemResultsResponse(GetPlantCaptchaItemResultsResponse&& from) noexcept
    : GetPlantCaptchaItemResultsResponse() {
    *this = ::std::move(from);
  }

  inline GetPlantCaptchaItemResultsResponse& operator=(const GetPlantCaptchaItemResultsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPlantCaptchaItemResultsResponse& operator=(GetPlantCaptchaItemResultsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPlantCaptchaItemResultsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPlantCaptchaItemResultsResponse* internal_default_instance() {
    return reinterpret_cast<const GetPlantCaptchaItemResultsResponse*>(
               &_GetPlantCaptchaItemResultsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(GetPlantCaptchaItemResultsResponse& a, GetPlantCaptchaItemResultsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPlantCaptchaItemResultsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPlantCaptchaItemResultsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPlantCaptchaItemResultsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPlantCaptchaItemResultsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPlantCaptchaItemResultsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPlantCaptchaItemResultsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPlantCaptchaItemResultsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse";
  }
  protected:
  explicit GetPlantCaptchaItemResultsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kResultsFieldNumber = 1,
  };
  // repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 1;
  int results_size() const;
  private:
  int _internal_results_size() const;
  public:
  void clear_results();
  ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* mutable_results(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >*
      mutable_results();
  private:
  const ::carbon::frontend::plant_captcha::PlantCaptchaItemResult& _internal_results(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* _internal_add_results();
  public:
  const ::carbon::frontend::plant_captcha::PlantCaptchaItemResult& results(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* add_results();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >&
      results() const;

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult > results_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class CalculatePlantCaptchaRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest) */ {
 public:
  inline CalculatePlantCaptchaRequest() : CalculatePlantCaptchaRequest(nullptr) {}
  ~CalculatePlantCaptchaRequest() override;
  explicit constexpr CalculatePlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalculatePlantCaptchaRequest(const CalculatePlantCaptchaRequest& from);
  CalculatePlantCaptchaRequest(CalculatePlantCaptchaRequest&& from) noexcept
    : CalculatePlantCaptchaRequest() {
    *this = ::std::move(from);
  }

  inline CalculatePlantCaptchaRequest& operator=(const CalculatePlantCaptchaRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalculatePlantCaptchaRequest& operator=(CalculatePlantCaptchaRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalculatePlantCaptchaRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalculatePlantCaptchaRequest* internal_default_instance() {
    return reinterpret_cast<const CalculatePlantCaptchaRequest*>(
               &_CalculatePlantCaptchaRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(CalculatePlantCaptchaRequest& a, CalculatePlantCaptchaRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CalculatePlantCaptchaRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalculatePlantCaptchaRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalculatePlantCaptchaRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalculatePlantCaptchaRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalculatePlantCaptchaRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CalculatePlantCaptchaRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalculatePlantCaptchaRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest";
  }
  protected:
  explicit CalculatePlantCaptchaRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class CalculatePlantCaptchaResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse) */ {
 public:
  inline CalculatePlantCaptchaResponse() : CalculatePlantCaptchaResponse(nullptr) {}
  ~CalculatePlantCaptchaResponse() override;
  explicit constexpr CalculatePlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CalculatePlantCaptchaResponse(const CalculatePlantCaptchaResponse& from);
  CalculatePlantCaptchaResponse(CalculatePlantCaptchaResponse&& from) noexcept
    : CalculatePlantCaptchaResponse() {
    *this = ::std::move(from);
  }

  inline CalculatePlantCaptchaResponse& operator=(const CalculatePlantCaptchaResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline CalculatePlantCaptchaResponse& operator=(CalculatePlantCaptchaResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CalculatePlantCaptchaResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const CalculatePlantCaptchaResponse* internal_default_instance() {
    return reinterpret_cast<const CalculatePlantCaptchaResponse*>(
               &_CalculatePlantCaptchaResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(CalculatePlantCaptchaResponse& a, CalculatePlantCaptchaResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(CalculatePlantCaptchaResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CalculatePlantCaptchaResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CalculatePlantCaptchaResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CalculatePlantCaptchaResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CalculatePlantCaptchaResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CalculatePlantCaptchaResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CalculatePlantCaptchaResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse";
  }
  protected:
  explicit CalculatePlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelinatorConfigFieldNumber = 1,
    kSucceededFieldNumber = 2,
    kFailureReasonFieldNumber = 3,
  };
  // .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
  bool has_modelinator_config() const;
  private:
  bool _internal_has_modelinator_config() const;
  public:
  void clear_modelinator_config();
  const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator_config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_modelinator_config();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_modelinator_config();
  void set_allocated_modelinator_config(::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_modelinator_config() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_modelinator_config();
  public:
  void unsafe_arena_set_allocated_modelinator_config(
      ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_modelinator_config();

  // bool succeeded = 2;
  void clear_succeeded();
  bool succeeded() const;
  void set_succeeded(bool value);
  private:
  bool _internal_succeeded() const;
  void _internal_set_succeeded(bool value);
  public:

  // .carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason failure_reason = 3;
  void clear_failure_reason();
  ::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason failure_reason() const;
  void set_failure_reason(::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason value);
  private:
  ::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason _internal_failure_reason() const;
  void _internal_set_failure_reason(::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config_;
  bool succeeded_;
  int failure_reason_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class PlantCaptchaResult final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.PlantCaptchaResult) */ {
 public:
  inline PlantCaptchaResult() : PlantCaptchaResult(nullptr) {}
  ~PlantCaptchaResult() override;
  explicit constexpr PlantCaptchaResult(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlantCaptchaResult(const PlantCaptchaResult& from);
  PlantCaptchaResult(PlantCaptchaResult&& from) noexcept
    : PlantCaptchaResult() {
    *this = ::std::move(from);
  }

  inline PlantCaptchaResult& operator=(const PlantCaptchaResult& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlantCaptchaResult& operator=(PlantCaptchaResult&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PlantCaptchaResult& default_instance() {
    return *internal_default_instance();
  }
  static inline const PlantCaptchaResult* internal_default_instance() {
    return reinterpret_cast<const PlantCaptchaResult*>(
               &_PlantCaptchaResult_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(PlantCaptchaResult& a, PlantCaptchaResult& b) {
    a.Swap(&b);
  }
  inline void Swap(PlantCaptchaResult* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlantCaptchaResult* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlantCaptchaResult* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlantCaptchaResult>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PlantCaptchaResult& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PlantCaptchaResult& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlantCaptchaResult* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.PlantCaptchaResult";
  }
  protected:
  explicit PlantCaptchaResult(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMetadataFieldNumber = 2,
    kLabelFieldNumber = 1,
  };
  // .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
  bool has_metadata() const;
  private:
  bool _internal_has_metadata() const;
  public:
  void clear_metadata();
  const ::weed_tracking::PlantCaptchaItemMetadata& metadata() const;
  PROTOBUF_NODISCARD ::weed_tracking::PlantCaptchaItemMetadata* release_metadata();
  ::weed_tracking::PlantCaptchaItemMetadata* mutable_metadata();
  void set_allocated_metadata(::weed_tracking::PlantCaptchaItemMetadata* metadata);
  private:
  const ::weed_tracking::PlantCaptchaItemMetadata& _internal_metadata() const;
  ::weed_tracking::PlantCaptchaItemMetadata* _internal_mutable_metadata();
  public:
  void unsafe_arena_set_allocated_metadata(
      ::weed_tracking::PlantCaptchaItemMetadata* metadata);
  ::weed_tracking::PlantCaptchaItemMetadata* unsafe_arena_release_metadata();

  // .weed_tracking.PlantCaptchaUserPrediction label = 1;
  void clear_label();
  ::weed_tracking::PlantCaptchaUserPrediction label() const;
  void set_label(::weed_tracking::PlantCaptchaUserPrediction value);
  private:
  ::weed_tracking::PlantCaptchaUserPrediction _internal_label() const;
  void _internal_set_label(::weed_tracking::PlantCaptchaUserPrediction value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaResult)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::weed_tracking::PlantCaptchaItemMetadata* metadata_;
  int label_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class PlantCaptchaResults final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.PlantCaptchaResults) */ {
 public:
  inline PlantCaptchaResults() : PlantCaptchaResults(nullptr) {}
  ~PlantCaptchaResults() override;
  explicit constexpr PlantCaptchaResults(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PlantCaptchaResults(const PlantCaptchaResults& from);
  PlantCaptchaResults(PlantCaptchaResults&& from) noexcept
    : PlantCaptchaResults() {
    *this = ::std::move(from);
  }

  inline PlantCaptchaResults& operator=(const PlantCaptchaResults& from) {
    CopyFrom(from);
    return *this;
  }
  inline PlantCaptchaResults& operator=(PlantCaptchaResults&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PlantCaptchaResults& default_instance() {
    return *internal_default_instance();
  }
  static inline const PlantCaptchaResults* internal_default_instance() {
    return reinterpret_cast<const PlantCaptchaResults*>(
               &_PlantCaptchaResults_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(PlantCaptchaResults& a, PlantCaptchaResults& b) {
    a.Swap(&b);
  }
  inline void Swap(PlantCaptchaResults* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PlantCaptchaResults* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PlantCaptchaResults* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PlantCaptchaResults>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PlantCaptchaResults& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PlantCaptchaResults& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PlantCaptchaResults* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.PlantCaptchaResults";
  }
  protected:
  explicit PlantCaptchaResults(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCaptchaResultsFieldNumber = 2,
    kAlgorithmFieldNumber = 3,
    kTiebreakerFieldNumber = 20,
    kMindooTiebreakerFieldNumber = 22,
    kTiebreakerStrategyThresholdWeedFieldNumber = 25,
    kTiebreakerStrategyThresholdCropFieldNumber = 26,
    kTiebreakerStrategyMindooWeedFieldNumber = 27,
    kTiebreakerStrategyMindooCropFieldNumber = 28,
    kCurrentParametersFieldNumber = 1,
    kAlmanacFieldNumber = 7,
    kGoalCropsTargetedFieldNumber = 4,
    kGoalWeedsTargetedFieldNumber = 5,
    kGoalUnknownTargetedFieldNumber = 6,
    kMaxRecommendedMindooFieldNumber = 8,
    kMinItemsForRecommendationFieldNumber = 9,
    kMinRecommendedMindooFieldNumber = 11,
    kMinRecommendedWeedThresholdFieldNumber = 12,
    kMaxRecommendedWeedThresholdFieldNumber = 13,
    kMinRecommendedCropThresholdFieldNumber = 14,
    kMaxRecommendedCropThresholdFieldNumber = 15,
    kMinDooForRecommendationFieldNumber = 16,
    kUseWeedCategoriesForWeedLabelsFieldNumber = 10,
    kUseOtherAsTiebreakerFieldNumber = 17,
    kLimitByCropsMissedFieldNumber = 18,
    kPadCropConfigurationsFieldNumber = 21,
    kNumberOfCropConfigurationsFieldNumber = 19,
    kUseBeneficialsAsCropsFieldNumber = 23,
    kUseVolunteersAsWeedsFieldNumber = 24,
  };
  // repeated .carbon.frontend.plant_captcha.PlantCaptchaResult captcha_results = 2;
  int captcha_results_size() const;
  private:
  int _internal_captcha_results_size() const;
  public:
  void clear_captcha_results();
  ::carbon::frontend::plant_captcha::PlantCaptchaResult* mutable_captcha_results(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaResult >*
      mutable_captcha_results();
  private:
  const ::carbon::frontend::plant_captcha::PlantCaptchaResult& _internal_captcha_results(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaResult* _internal_add_captcha_results();
  public:
  const ::carbon::frontend::plant_captcha::PlantCaptchaResult& captcha_results(int index) const;
  ::carbon::frontend::plant_captcha::PlantCaptchaResult* add_captcha_results();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaResult >&
      captcha_results() const;

  // string algorithm = 3;
  void clear_algorithm();
  const std::string& algorithm() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_algorithm(ArgT0&& arg0, ArgT... args);
  std::string* mutable_algorithm();
  PROTOBUF_NODISCARD std::string* release_algorithm();
  void set_allocated_algorithm(std::string* algorithm);
  private:
  const std::string& _internal_algorithm() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_algorithm(const std::string& value);
  std::string* _internal_mutable_algorithm();
  public:

  // string tiebreaker = 20;
  void clear_tiebreaker();
  const std::string& tiebreaker() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tiebreaker(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tiebreaker();
  PROTOBUF_NODISCARD std::string* release_tiebreaker();
  void set_allocated_tiebreaker(std::string* tiebreaker);
  private:
  const std::string& _internal_tiebreaker() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tiebreaker(const std::string& value);
  std::string* _internal_mutable_tiebreaker();
  public:

  // string mindoo_tiebreaker = 22;
  void clear_mindoo_tiebreaker();
  const std::string& mindoo_tiebreaker() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_mindoo_tiebreaker(ArgT0&& arg0, ArgT... args);
  std::string* mutable_mindoo_tiebreaker();
  PROTOBUF_NODISCARD std::string* release_mindoo_tiebreaker();
  void set_allocated_mindoo_tiebreaker(std::string* mindoo_tiebreaker);
  private:
  const std::string& _internal_mindoo_tiebreaker() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_mindoo_tiebreaker(const std::string& value);
  std::string* _internal_mutable_mindoo_tiebreaker();
  public:

  // string tiebreaker_strategy_threshold_weed = 25;
  void clear_tiebreaker_strategy_threshold_weed();
  const std::string& tiebreaker_strategy_threshold_weed() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tiebreaker_strategy_threshold_weed(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tiebreaker_strategy_threshold_weed();
  PROTOBUF_NODISCARD std::string* release_tiebreaker_strategy_threshold_weed();
  void set_allocated_tiebreaker_strategy_threshold_weed(std::string* tiebreaker_strategy_threshold_weed);
  private:
  const std::string& _internal_tiebreaker_strategy_threshold_weed() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tiebreaker_strategy_threshold_weed(const std::string& value);
  std::string* _internal_mutable_tiebreaker_strategy_threshold_weed();
  public:

  // string tiebreaker_strategy_threshold_crop = 26;
  void clear_tiebreaker_strategy_threshold_crop();
  const std::string& tiebreaker_strategy_threshold_crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tiebreaker_strategy_threshold_crop(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tiebreaker_strategy_threshold_crop();
  PROTOBUF_NODISCARD std::string* release_tiebreaker_strategy_threshold_crop();
  void set_allocated_tiebreaker_strategy_threshold_crop(std::string* tiebreaker_strategy_threshold_crop);
  private:
  const std::string& _internal_tiebreaker_strategy_threshold_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tiebreaker_strategy_threshold_crop(const std::string& value);
  std::string* _internal_mutable_tiebreaker_strategy_threshold_crop();
  public:

  // string tiebreaker_strategy_mindoo_weed = 27;
  void clear_tiebreaker_strategy_mindoo_weed();
  const std::string& tiebreaker_strategy_mindoo_weed() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tiebreaker_strategy_mindoo_weed(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tiebreaker_strategy_mindoo_weed();
  PROTOBUF_NODISCARD std::string* release_tiebreaker_strategy_mindoo_weed();
  void set_allocated_tiebreaker_strategy_mindoo_weed(std::string* tiebreaker_strategy_mindoo_weed);
  private:
  const std::string& _internal_tiebreaker_strategy_mindoo_weed() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tiebreaker_strategy_mindoo_weed(const std::string& value);
  std::string* _internal_mutable_tiebreaker_strategy_mindoo_weed();
  public:

  // string tiebreaker_strategy_mindoo_crop = 28;
  void clear_tiebreaker_strategy_mindoo_crop();
  const std::string& tiebreaker_strategy_mindoo_crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tiebreaker_strategy_mindoo_crop(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tiebreaker_strategy_mindoo_crop();
  PROTOBUF_NODISCARD std::string* release_tiebreaker_strategy_mindoo_crop();
  void set_allocated_tiebreaker_strategy_mindoo_crop(std::string* tiebreaker_strategy_mindoo_crop);
  private:
  const std::string& _internal_tiebreaker_strategy_mindoo_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tiebreaker_strategy_mindoo_crop(const std::string& value);
  std::string* _internal_mutable_tiebreaker_strategy_mindoo_crop();
  public:

  // .carbon.aimbot.almanac.ModelinatorConfig current_parameters = 1;
  bool has_current_parameters() const;
  private:
  bool _internal_has_current_parameters() const;
  public:
  void clear_current_parameters();
  const ::carbon::aimbot::almanac::ModelinatorConfig& current_parameters() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_current_parameters();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_current_parameters();
  void set_allocated_current_parameters(::carbon::aimbot::almanac::ModelinatorConfig* current_parameters);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_current_parameters() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_current_parameters();
  public:
  void unsafe_arena_set_allocated_current_parameters(
      ::carbon::aimbot::almanac::ModelinatorConfig* current_parameters);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_current_parameters();

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 7;
  bool has_almanac() const;
  private:
  bool _internal_has_almanac() const;
  public:
  void clear_almanac();
  const ::carbon::aimbot::almanac::AlmanacConfig& almanac() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::AlmanacConfig* release_almanac();
  ::carbon::aimbot::almanac::AlmanacConfig* mutable_almanac();
  void set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac);
  private:
  const ::carbon::aimbot::almanac::AlmanacConfig& _internal_almanac() const;
  ::carbon::aimbot::almanac::AlmanacConfig* _internal_mutable_almanac();
  public:
  void unsafe_arena_set_allocated_almanac(
      ::carbon::aimbot::almanac::AlmanacConfig* almanac);
  ::carbon::aimbot::almanac::AlmanacConfig* unsafe_arena_release_almanac();

  // float goal_crops_targeted = 4;
  void clear_goal_crops_targeted();
  float goal_crops_targeted() const;
  void set_goal_crops_targeted(float value);
  private:
  float _internal_goal_crops_targeted() const;
  void _internal_set_goal_crops_targeted(float value);
  public:

  // float goal_weeds_targeted = 5;
  void clear_goal_weeds_targeted();
  float goal_weeds_targeted() const;
  void set_goal_weeds_targeted(float value);
  private:
  float _internal_goal_weeds_targeted() const;
  void _internal_set_goal_weeds_targeted(float value);
  public:

  // float goal_unknown_targeted = 6;
  void clear_goal_unknown_targeted();
  float goal_unknown_targeted() const;
  void set_goal_unknown_targeted(float value);
  private:
  float _internal_goal_unknown_targeted() const;
  void _internal_set_goal_unknown_targeted(float value);
  public:

  // float max_recommended_mindoo = 8;
  void clear_max_recommended_mindoo();
  float max_recommended_mindoo() const;
  void set_max_recommended_mindoo(float value);
  private:
  float _internal_max_recommended_mindoo() const;
  void _internal_set_max_recommended_mindoo(float value);
  public:

  // int32 min_items_for_recommendation = 9;
  void clear_min_items_for_recommendation();
  int32_t min_items_for_recommendation() const;
  void set_min_items_for_recommendation(int32_t value);
  private:
  int32_t _internal_min_items_for_recommendation() const;
  void _internal_set_min_items_for_recommendation(int32_t value);
  public:

  // float min_recommended_mindoo = 11;
  void clear_min_recommended_mindoo();
  float min_recommended_mindoo() const;
  void set_min_recommended_mindoo(float value);
  private:
  float _internal_min_recommended_mindoo() const;
  void _internal_set_min_recommended_mindoo(float value);
  public:

  // float min_recommended_weed_threshold = 12;
  void clear_min_recommended_weed_threshold();
  float min_recommended_weed_threshold() const;
  void set_min_recommended_weed_threshold(float value);
  private:
  float _internal_min_recommended_weed_threshold() const;
  void _internal_set_min_recommended_weed_threshold(float value);
  public:

  // float max_recommended_weed_threshold = 13;
  void clear_max_recommended_weed_threshold();
  float max_recommended_weed_threshold() const;
  void set_max_recommended_weed_threshold(float value);
  private:
  float _internal_max_recommended_weed_threshold() const;
  void _internal_set_max_recommended_weed_threshold(float value);
  public:

  // float min_recommended_crop_threshold = 14;
  void clear_min_recommended_crop_threshold();
  float min_recommended_crop_threshold() const;
  void set_min_recommended_crop_threshold(float value);
  private:
  float _internal_min_recommended_crop_threshold() const;
  void _internal_set_min_recommended_crop_threshold(float value);
  public:

  // float max_recommended_crop_threshold = 15;
  void clear_max_recommended_crop_threshold();
  float max_recommended_crop_threshold() const;
  void set_max_recommended_crop_threshold(float value);
  private:
  float _internal_max_recommended_crop_threshold() const;
  void _internal_set_max_recommended_crop_threshold(float value);
  public:

  // float min_doo_for_recommendation = 16;
  void clear_min_doo_for_recommendation();
  float min_doo_for_recommendation() const;
  void set_min_doo_for_recommendation(float value);
  private:
  float _internal_min_doo_for_recommendation() const;
  void _internal_set_min_doo_for_recommendation(float value);
  public:

  // bool use_weed_categories_for_weed_labels = 10;
  void clear_use_weed_categories_for_weed_labels();
  bool use_weed_categories_for_weed_labels() const;
  void set_use_weed_categories_for_weed_labels(bool value);
  private:
  bool _internal_use_weed_categories_for_weed_labels() const;
  void _internal_set_use_weed_categories_for_weed_labels(bool value);
  public:

  // bool use_other_as_tiebreaker = 17;
  void clear_use_other_as_tiebreaker();
  bool use_other_as_tiebreaker() const;
  void set_use_other_as_tiebreaker(bool value);
  private:
  bool _internal_use_other_as_tiebreaker() const;
  void _internal_set_use_other_as_tiebreaker(bool value);
  public:

  // bool limit_by_crops_missed = 18;
  void clear_limit_by_crops_missed();
  bool limit_by_crops_missed() const;
  void set_limit_by_crops_missed(bool value);
  private:
  bool _internal_limit_by_crops_missed() const;
  void _internal_set_limit_by_crops_missed(bool value);
  public:

  // bool pad_crop_configurations = 21;
  void clear_pad_crop_configurations();
  bool pad_crop_configurations() const;
  void set_pad_crop_configurations(bool value);
  private:
  bool _internal_pad_crop_configurations() const;
  void _internal_set_pad_crop_configurations(bool value);
  public:

  // int32 number_of_crop_configurations = 19;
  void clear_number_of_crop_configurations();
  int32_t number_of_crop_configurations() const;
  void set_number_of_crop_configurations(int32_t value);
  private:
  int32_t _internal_number_of_crop_configurations() const;
  void _internal_set_number_of_crop_configurations(int32_t value);
  public:

  // bool use_beneficials_as_crops = 23;
  void clear_use_beneficials_as_crops();
  bool use_beneficials_as_crops() const;
  void set_use_beneficials_as_crops(bool value);
  private:
  bool _internal_use_beneficials_as_crops() const;
  void _internal_set_use_beneficials_as_crops(bool value);
  public:

  // bool use_volunteers_as_weeds = 24;
  void clear_use_volunteers_as_weeds();
  bool use_volunteers_as_weeds() const;
  void set_use_volunteers_as_weeds(bool value);
  private:
  bool _internal_use_volunteers_as_weeds() const;
  void _internal_set_use_volunteers_as_weeds(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.PlantCaptchaResults)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaResult > captcha_results_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr algorithm_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tiebreaker_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr mindoo_tiebreaker_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tiebreaker_strategy_threshold_weed_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tiebreaker_strategy_threshold_crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tiebreaker_strategy_mindoo_weed_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tiebreaker_strategy_mindoo_crop_;
  ::carbon::aimbot::almanac::ModelinatorConfig* current_parameters_;
  ::carbon::aimbot::almanac::AlmanacConfig* almanac_;
  float goal_crops_targeted_;
  float goal_weeds_targeted_;
  float goal_unknown_targeted_;
  float max_recommended_mindoo_;
  int32_t min_items_for_recommendation_;
  float min_recommended_mindoo_;
  float min_recommended_weed_threshold_;
  float max_recommended_weed_threshold_;
  float min_recommended_crop_threshold_;
  float max_recommended_crop_threshold_;
  float min_doo_for_recommendation_;
  bool use_weed_categories_for_weed_labels_;
  bool use_other_as_tiebreaker_;
  bool limit_by_crops_missed_;
  bool pad_crop_configurations_;
  int32_t number_of_crop_configurations_;
  bool use_beneficials_as_crops_;
  bool use_volunteers_as_weeds_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class VeselkaPlantCaptchaResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse) */ {
 public:
  inline VeselkaPlantCaptchaResponse() : VeselkaPlantCaptchaResponse(nullptr) {}
  ~VeselkaPlantCaptchaResponse() override;
  explicit constexpr VeselkaPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  VeselkaPlantCaptchaResponse(const VeselkaPlantCaptchaResponse& from);
  VeselkaPlantCaptchaResponse(VeselkaPlantCaptchaResponse&& from) noexcept
    : VeselkaPlantCaptchaResponse() {
    *this = ::std::move(from);
  }

  inline VeselkaPlantCaptchaResponse& operator=(const VeselkaPlantCaptchaResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline VeselkaPlantCaptchaResponse& operator=(VeselkaPlantCaptchaResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const VeselkaPlantCaptchaResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const VeselkaPlantCaptchaResponse* internal_default_instance() {
    return reinterpret_cast<const VeselkaPlantCaptchaResponse*>(
               &_VeselkaPlantCaptchaResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(VeselkaPlantCaptchaResponse& a, VeselkaPlantCaptchaResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(VeselkaPlantCaptchaResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(VeselkaPlantCaptchaResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  VeselkaPlantCaptchaResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<VeselkaPlantCaptchaResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const VeselkaPlantCaptchaResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const VeselkaPlantCaptchaResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(VeselkaPlantCaptchaResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse";
  }
  protected:
  explicit VeselkaPlantCaptchaResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNewModelParametersFieldNumber = 1,
    kSucceededFieldNumber = 2,
  };
  // .carbon.aimbot.almanac.ModelinatorConfig new_model_parameters = 1;
  bool has_new_model_parameters() const;
  private:
  bool _internal_has_new_model_parameters() const;
  public:
  void clear_new_model_parameters();
  const ::carbon::aimbot::almanac::ModelinatorConfig& new_model_parameters() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_new_model_parameters();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_new_model_parameters();
  void set_allocated_new_model_parameters(::carbon::aimbot::almanac::ModelinatorConfig* new_model_parameters);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_new_model_parameters() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_new_model_parameters();
  public:
  void unsafe_arena_set_allocated_new_model_parameters(
      ::carbon::aimbot::almanac::ModelinatorConfig* new_model_parameters);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_new_model_parameters();

  // bool succeeded = 2;
  void clear_succeeded();
  bool succeeded() const;
  void set_succeeded(bool value);
  private:
  bool _internal_succeeded() const;
  void _internal_set_succeeded(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::ModelinatorConfig* new_model_parameters_;
  bool succeeded_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetOriginalModelinatorConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest) */ {
 public:
  inline GetOriginalModelinatorConfigRequest() : GetOriginalModelinatorConfigRequest(nullptr) {}
  ~GetOriginalModelinatorConfigRequest() override;
  explicit constexpr GetOriginalModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetOriginalModelinatorConfigRequest(const GetOriginalModelinatorConfigRequest& from);
  GetOriginalModelinatorConfigRequest(GetOriginalModelinatorConfigRequest&& from) noexcept
    : GetOriginalModelinatorConfigRequest() {
    *this = ::std::move(from);
  }

  inline GetOriginalModelinatorConfigRequest& operator=(const GetOriginalModelinatorConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetOriginalModelinatorConfigRequest& operator=(GetOriginalModelinatorConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetOriginalModelinatorConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetOriginalModelinatorConfigRequest* internal_default_instance() {
    return reinterpret_cast<const GetOriginalModelinatorConfigRequest*>(
               &_GetOriginalModelinatorConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(GetOriginalModelinatorConfigRequest& a, GetOriginalModelinatorConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetOriginalModelinatorConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetOriginalModelinatorConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetOriginalModelinatorConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetOriginalModelinatorConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetOriginalModelinatorConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetOriginalModelinatorConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetOriginalModelinatorConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest";
  }
  protected:
  explicit GetOriginalModelinatorConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetOriginalModelinatorConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse) */ {
 public:
  inline GetOriginalModelinatorConfigResponse() : GetOriginalModelinatorConfigResponse(nullptr) {}
  ~GetOriginalModelinatorConfigResponse() override;
  explicit constexpr GetOriginalModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetOriginalModelinatorConfigResponse(const GetOriginalModelinatorConfigResponse& from);
  GetOriginalModelinatorConfigResponse(GetOriginalModelinatorConfigResponse&& from) noexcept
    : GetOriginalModelinatorConfigResponse() {
    *this = ::std::move(from);
  }

  inline GetOriginalModelinatorConfigResponse& operator=(const GetOriginalModelinatorConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetOriginalModelinatorConfigResponse& operator=(GetOriginalModelinatorConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetOriginalModelinatorConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetOriginalModelinatorConfigResponse* internal_default_instance() {
    return reinterpret_cast<const GetOriginalModelinatorConfigResponse*>(
               &_GetOriginalModelinatorConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(GetOriginalModelinatorConfigResponse& a, GetOriginalModelinatorConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetOriginalModelinatorConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetOriginalModelinatorConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetOriginalModelinatorConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetOriginalModelinatorConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetOriginalModelinatorConfigResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetOriginalModelinatorConfigResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetOriginalModelinatorConfigResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse";
  }
  protected:
  explicit GetOriginalModelinatorConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModelinatorConfigFieldNumber = 1,
  };
  // .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
  bool has_modelinator_config() const;
  private:
  bool _internal_has_modelinator_config() const;
  public:
  void clear_modelinator_config();
  const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator_config() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_modelinator_config();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_modelinator_config();
  void set_allocated_modelinator_config(::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_modelinator_config() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_modelinator_config();
  public:
  void unsafe_arena_set_allocated_modelinator_config(
      ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_modelinator_config();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse, 
    int32_t, ::weed_tracking::PlantCaptchaStatusResponse,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse, 
    int32_t, ::weed_tracking::PlantCaptchaStatusResponse,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse();
  explicit constexpr GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse& other);
  static const GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse*>(&_GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetCaptchaRowStatusResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse) */ {
 public:
  inline GetCaptchaRowStatusResponse() : GetCaptchaRowStatusResponse(nullptr) {}
  ~GetCaptchaRowStatusResponse() override;
  explicit constexpr GetCaptchaRowStatusResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetCaptchaRowStatusResponse(const GetCaptchaRowStatusResponse& from);
  GetCaptchaRowStatusResponse(GetCaptchaRowStatusResponse&& from) noexcept
    : GetCaptchaRowStatusResponse() {
    *this = ::std::move(from);
  }

  inline GetCaptchaRowStatusResponse& operator=(const GetCaptchaRowStatusResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetCaptchaRowStatusResponse& operator=(GetCaptchaRowStatusResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetCaptchaRowStatusResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetCaptchaRowStatusResponse* internal_default_instance() {
    return reinterpret_cast<const GetCaptchaRowStatusResponse*>(
               &_GetCaptchaRowStatusResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    27;

  friend void swap(GetCaptchaRowStatusResponse& a, GetCaptchaRowStatusResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetCaptchaRowStatusResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetCaptchaRowStatusResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetCaptchaRowStatusResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetCaptchaRowStatusResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetCaptchaRowStatusResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetCaptchaRowStatusResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetCaptchaRowStatusResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse";
  }
  protected:
  explicit GetCaptchaRowStatusResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kRowStatusFieldNumber = 1,
  };
  // map<int32, .weed_tracking.PlantCaptchaStatusResponse> row_status = 1;
  int row_status_size() const;
  private:
  int _internal_row_status_size() const;
  public:
  void clear_row_status();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >&
      _internal_row_status() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >*
      _internal_mutable_row_status();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >&
      row_status() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >*
      mutable_row_status();

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetCaptchaRowStatusResponse_RowStatusEntry_DoNotUse,
      int32_t, ::weed_tracking::PlantCaptchaStatusResponse,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> row_status_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// -------------------------------------------------------------------

class CancelPlantCaptchaOnRowRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest) */ {
 public:
  inline CancelPlantCaptchaOnRowRequest() : CancelPlantCaptchaOnRowRequest(nullptr) {}
  ~CancelPlantCaptchaOnRowRequest() override;
  explicit constexpr CancelPlantCaptchaOnRowRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CancelPlantCaptchaOnRowRequest(const CancelPlantCaptchaOnRowRequest& from);
  CancelPlantCaptchaOnRowRequest(CancelPlantCaptchaOnRowRequest&& from) noexcept
    : CancelPlantCaptchaOnRowRequest() {
    *this = ::std::move(from);
  }

  inline CancelPlantCaptchaOnRowRequest& operator=(const CancelPlantCaptchaOnRowRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CancelPlantCaptchaOnRowRequest& operator=(CancelPlantCaptchaOnRowRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CancelPlantCaptchaOnRowRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CancelPlantCaptchaOnRowRequest* internal_default_instance() {
    return reinterpret_cast<const CancelPlantCaptchaOnRowRequest*>(
               &_CancelPlantCaptchaOnRowRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    28;

  friend void swap(CancelPlantCaptchaOnRowRequest& a, CancelPlantCaptchaOnRowRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CancelPlantCaptchaOnRowRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CancelPlantCaptchaOnRowRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CancelPlantCaptchaOnRowRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CancelPlantCaptchaOnRowRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CancelPlantCaptchaOnRowRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CancelPlantCaptchaOnRowRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CancelPlantCaptchaOnRowRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest";
  }
  protected:
  explicit CancelPlantCaptchaOnRowRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowIdFieldNumber = 1,
  };
  // int32 row_id = 1;
  void clear_row_id();
  int32_t row_id() const;
  void set_row_id(int32_t value);
  private:
  int32_t _internal_row_id() const;
  void _internal_set_row_id(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t row_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fplant_5fcaptcha_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PlantCaptcha

// string name = 1;
inline void PlantCaptcha::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& PlantCaptcha::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptcha.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptcha::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptcha.name)
}
inline std::string* PlantCaptcha::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptcha.name)
  return _s;
}
inline const std::string& PlantCaptcha::_internal_name() const {
  return name_.Get();
}
inline void PlantCaptcha::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptcha::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptcha::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptcha.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptcha::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptcha.name)
}

// string model_id = 2;
inline void PlantCaptcha::clear_model_id() {
  model_id_.ClearToEmpty();
}
inline const std::string& PlantCaptcha::model_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptcha.model_id)
  return _internal_model_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptcha::set_model_id(ArgT0&& arg0, ArgT... args) {
 
 model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptcha.model_id)
}
inline std::string* PlantCaptcha::mutable_model_id() {
  std::string* _s = _internal_mutable_model_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptcha.model_id)
  return _s;
}
inline const std::string& PlantCaptcha::_internal_model_id() const {
  return model_id_.Get();
}
inline void PlantCaptcha::_internal_set_model_id(const std::string& value) {
  
  model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptcha::_internal_mutable_model_id() {
  
  return model_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptcha::release_model_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptcha.model_id)
  return model_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptcha::set_allocated_model_id(std::string* model_id) {
  if (model_id != nullptr) {
    
  } else {
    
  }
  model_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptcha.model_id)
}

// string crop_id = 3;
inline void PlantCaptcha::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& PlantCaptcha::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptcha.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptcha::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptcha.crop_id)
}
inline std::string* PlantCaptcha::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptcha.crop_id)
  return _s;
}
inline const std::string& PlantCaptcha::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void PlantCaptcha::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptcha::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptcha::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptcha.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptcha::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptcha.crop_id)
}

// string crop_name = 4;
inline void PlantCaptcha::clear_crop_name() {
  crop_name_.ClearToEmpty();
}
inline const std::string& PlantCaptcha::crop_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptcha.crop_name)
  return _internal_crop_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptcha::set_crop_name(ArgT0&& arg0, ArgT... args) {
 
 crop_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptcha.crop_name)
}
inline std::string* PlantCaptcha::mutable_crop_name() {
  std::string* _s = _internal_mutable_crop_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptcha.crop_name)
  return _s;
}
inline const std::string& PlantCaptcha::_internal_crop_name() const {
  return crop_name_.Get();
}
inline void PlantCaptcha::_internal_set_crop_name(const std::string& value) {
  
  crop_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptcha::_internal_mutable_crop_name() {
  
  return crop_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptcha::release_crop_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptcha.crop_name)
  return crop_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptcha::set_allocated_crop_name(std::string* crop_name) {
  if (crop_name != nullptr) {
    
  } else {
    
  }
  crop_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptcha.crop_name)
}

// int64 start_time_ms = 5;
inline void PlantCaptcha::clear_start_time_ms() {
  start_time_ms_ = int64_t{0};
}
inline int64_t PlantCaptcha::_internal_start_time_ms() const {
  return start_time_ms_;
}
inline int64_t PlantCaptcha::start_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptcha.start_time_ms)
  return _internal_start_time_ms();
}
inline void PlantCaptcha::_internal_set_start_time_ms(int64_t value) {
  
  start_time_ms_ = value;
}
inline void PlantCaptcha::set_start_time_ms(int64_t value) {
  _internal_set_start_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptcha.start_time_ms)
}

// repeated int32 rows_used = 6;
inline int PlantCaptcha::_internal_rows_used_size() const {
  return rows_used_.size();
}
inline int PlantCaptcha::rows_used_size() const {
  return _internal_rows_used_size();
}
inline void PlantCaptcha::clear_rows_used() {
  rows_used_.Clear();
}
inline int32_t PlantCaptcha::_internal_rows_used(int index) const {
  return rows_used_.Get(index);
}
inline int32_t PlantCaptcha::rows_used(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptcha.rows_used)
  return _internal_rows_used(index);
}
inline void PlantCaptcha::set_rows_used(int index, int32_t value) {
  rows_used_.Set(index, value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptcha.rows_used)
}
inline void PlantCaptcha::_internal_add_rows_used(int32_t value) {
  rows_used_.Add(value);
}
inline void PlantCaptcha::add_rows_used(int32_t value) {
  _internal_add_rows_used(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.PlantCaptcha.rows_used)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
PlantCaptcha::_internal_rows_used() const {
  return rows_used_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >&
PlantCaptcha::rows_used() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.PlantCaptcha.rows_used)
  return _internal_rows_used();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
PlantCaptcha::_internal_mutable_rows_used() {
  return &rows_used_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int32_t >*
PlantCaptcha::mutable_rows_used() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.PlantCaptcha.rows_used)
  return _internal_mutable_rows_used();
}

// -------------------------------------------------------------------

// StartPlantCaptchaRequest

// .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
inline bool StartPlantCaptchaRequest::_internal_has_plant_captcha() const {
  return this != internal_default_instance() && plant_captcha_ != nullptr;
}
inline bool StartPlantCaptchaRequest::has_plant_captcha() const {
  return _internal_has_plant_captcha();
}
inline void StartPlantCaptchaRequest::clear_plant_captcha() {
  if (GetArenaForAllocation() == nullptr && plant_captcha_ != nullptr) {
    delete plant_captcha_;
  }
  plant_captcha_ = nullptr;
}
inline const ::carbon::frontend::plant_captcha::PlantCaptcha& StartPlantCaptchaRequest::_internal_plant_captcha() const {
  const ::carbon::frontend::plant_captcha::PlantCaptcha* p = plant_captcha_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::plant_captcha::PlantCaptcha&>(
      ::carbon::frontend::plant_captcha::_PlantCaptcha_default_instance_);
}
inline const ::carbon::frontend::plant_captcha::PlantCaptcha& StartPlantCaptchaRequest::plant_captcha() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.StartPlantCaptchaRequest.plant_captcha)
  return _internal_plant_captcha();
}
inline void StartPlantCaptchaRequest::unsafe_arena_set_allocated_plant_captcha(
    ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(plant_captcha_);
  }
  plant_captcha_ = plant_captcha;
  if (plant_captcha) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.StartPlantCaptchaRequest.plant_captcha)
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* StartPlantCaptchaRequest::release_plant_captcha() {
  
  ::carbon::frontend::plant_captcha::PlantCaptcha* temp = plant_captcha_;
  plant_captcha_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* StartPlantCaptchaRequest::unsafe_arena_release_plant_captcha() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.StartPlantCaptchaRequest.plant_captcha)
  
  ::carbon::frontend::plant_captcha::PlantCaptcha* temp = plant_captcha_;
  plant_captcha_ = nullptr;
  return temp;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* StartPlantCaptchaRequest::_internal_mutable_plant_captcha() {
  
  if (plant_captcha_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptcha>(GetArenaForAllocation());
    plant_captcha_ = p;
  }
  return plant_captcha_;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* StartPlantCaptchaRequest::mutable_plant_captcha() {
  ::carbon::frontend::plant_captcha::PlantCaptcha* _msg = _internal_mutable_plant_captcha();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.StartPlantCaptchaRequest.plant_captcha)
  return _msg;
}
inline void StartPlantCaptchaRequest::set_allocated_plant_captcha(::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete plant_captcha_;
  }
  if (plant_captcha) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::plant_captcha::PlantCaptcha>::GetOwningArena(plant_captcha);
    if (message_arena != submessage_arena) {
      plant_captcha = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, plant_captcha, submessage_arena);
    }
    
  } else {
    
  }
  plant_captcha_ = plant_captcha;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.StartPlantCaptchaRequest.plant_captcha)
}

// -------------------------------------------------------------------

// StartPlantCaptchaResponse

// -------------------------------------------------------------------

// GetNextPlantCaptchaStatusRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextPlantCaptchaStatusRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextPlantCaptchaStatusRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchaStatusRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchaStatusRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest.ts)
  return _internal_ts();
}
inline void GetNextPlantCaptchaStatusRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaStatusRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaStatusRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaStatusRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaStatusRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest.ts)
  return _msg;
}
inline void GetNextPlantCaptchaStatusRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusRequest.ts)
}

// -------------------------------------------------------------------

// GetNextPlantCaptchaStatusResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextPlantCaptchaStatusResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextPlantCaptchaStatusResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchaStatusResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchaStatusResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.ts)
  return _internal_ts();
}
inline void GetNextPlantCaptchaStatusResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaStatusResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaStatusResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaStatusResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaStatusResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.ts)
  return _msg;
}
inline void GetNextPlantCaptchaStatusResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.ts)
}

// .weed_tracking.PlantCaptchaStatus status = 2;
inline void GetNextPlantCaptchaStatusResponse::clear_status() {
  status_ = 0;
}
inline ::weed_tracking::PlantCaptchaStatus GetNextPlantCaptchaStatusResponse::_internal_status() const {
  return static_cast< ::weed_tracking::PlantCaptchaStatus >(status_);
}
inline ::weed_tracking::PlantCaptchaStatus GetNextPlantCaptchaStatusResponse::status() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.status)
  return _internal_status();
}
inline void GetNextPlantCaptchaStatusResponse::_internal_set_status(::weed_tracking::PlantCaptchaStatus value) {
  
  status_ = value;
}
inline void GetNextPlantCaptchaStatusResponse::set_status(::weed_tracking::PlantCaptchaStatus value) {
  _internal_set_status(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.status)
}

// int32 total_images = 3;
inline void GetNextPlantCaptchaStatusResponse::clear_total_images() {
  total_images_ = 0;
}
inline int32_t GetNextPlantCaptchaStatusResponse::_internal_total_images() const {
  return total_images_;
}
inline int32_t GetNextPlantCaptchaStatusResponse::total_images() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.total_images)
  return _internal_total_images();
}
inline void GetNextPlantCaptchaStatusResponse::_internal_set_total_images(int32_t value) {
  
  total_images_ = value;
}
inline void GetNextPlantCaptchaStatusResponse::set_total_images(int32_t value) {
  _internal_set_total_images(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.total_images)
}

// int32 images_taken = 4;
inline void GetNextPlantCaptchaStatusResponse::clear_images_taken() {
  images_taken_ = 0;
}
inline int32_t GetNextPlantCaptchaStatusResponse::_internal_images_taken() const {
  return images_taken_;
}
inline int32_t GetNextPlantCaptchaStatusResponse::images_taken() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.images_taken)
  return _internal_images_taken();
}
inline void GetNextPlantCaptchaStatusResponse::_internal_set_images_taken(int32_t value) {
  
  images_taken_ = value;
}
inline void GetNextPlantCaptchaStatusResponse::set_images_taken(int32_t value) {
  _internal_set_images_taken(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.images_taken)
}

// int32 metadata_taken = 5;
inline void GetNextPlantCaptchaStatusResponse::clear_metadata_taken() {
  metadata_taken_ = 0;
}
inline int32_t GetNextPlantCaptchaStatusResponse::_internal_metadata_taken() const {
  return metadata_taken_;
}
inline int32_t GetNextPlantCaptchaStatusResponse::metadata_taken() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.metadata_taken)
  return _internal_metadata_taken();
}
inline void GetNextPlantCaptchaStatusResponse::_internal_set_metadata_taken(int32_t value) {
  
  metadata_taken_ = value;
}
inline void GetNextPlantCaptchaStatusResponse::set_metadata_taken(int32_t value) {
  _internal_set_metadata_taken(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetNextPlantCaptchaStatusResponse.metadata_taken)
}

// -------------------------------------------------------------------

// GetNextPlantCaptchasListRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextPlantCaptchasListRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextPlantCaptchasListRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchasListRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchasListRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest.ts)
  return _internal_ts();
}
inline void GetNextPlantCaptchasListRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchasListRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchasListRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchasListRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchasListRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest.ts)
  return _msg;
}
inline void GetNextPlantCaptchasListRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchasListRequest.ts)
}

// -------------------------------------------------------------------

// PlantCaptchaListItem

// .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
inline bool PlantCaptchaListItem::_internal_has_plant_captcha() const {
  return this != internal_default_instance() && plant_captcha_ != nullptr;
}
inline bool PlantCaptchaListItem::has_plant_captcha() const {
  return _internal_has_plant_captcha();
}
inline void PlantCaptchaListItem::clear_plant_captcha() {
  if (GetArenaForAllocation() == nullptr && plant_captcha_ != nullptr) {
    delete plant_captcha_;
  }
  plant_captcha_ = nullptr;
}
inline const ::carbon::frontend::plant_captcha::PlantCaptcha& PlantCaptchaListItem::_internal_plant_captcha() const {
  const ::carbon::frontend::plant_captcha::PlantCaptcha* p = plant_captcha_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::plant_captcha::PlantCaptcha&>(
      ::carbon::frontend::plant_captcha::_PlantCaptcha_default_instance_);
}
inline const ::carbon::frontend::plant_captcha::PlantCaptcha& PlantCaptchaListItem::plant_captcha() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaListItem.plant_captcha)
  return _internal_plant_captcha();
}
inline void PlantCaptchaListItem::unsafe_arena_set_allocated_plant_captcha(
    ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(plant_captcha_);
  }
  plant_captcha_ = plant_captcha;
  if (plant_captcha) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaListItem.plant_captcha)
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* PlantCaptchaListItem::release_plant_captcha() {
  
  ::carbon::frontend::plant_captcha::PlantCaptcha* temp = plant_captcha_;
  plant_captcha_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* PlantCaptchaListItem::unsafe_arena_release_plant_captcha() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaListItem.plant_captcha)
  
  ::carbon::frontend::plant_captcha::PlantCaptcha* temp = plant_captcha_;
  plant_captcha_ = nullptr;
  return temp;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* PlantCaptchaListItem::_internal_mutable_plant_captcha() {
  
  if (plant_captcha_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptcha>(GetArenaForAllocation());
    plant_captcha_ = p;
  }
  return plant_captcha_;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* PlantCaptchaListItem::mutable_plant_captcha() {
  ::carbon::frontend::plant_captcha::PlantCaptcha* _msg = _internal_mutable_plant_captcha();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaListItem.plant_captcha)
  return _msg;
}
inline void PlantCaptchaListItem::set_allocated_plant_captcha(::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete plant_captcha_;
  }
  if (plant_captcha) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::plant_captcha::PlantCaptcha>::GetOwningArena(plant_captcha);
    if (message_arena != submessage_arena) {
      plant_captcha = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, plant_captcha, submessage_arena);
    }
    
  } else {
    
  }
  plant_captcha_ = plant_captcha;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaListItem.plant_captcha)
}

// int32 images_taken = 2;
inline void PlantCaptchaListItem::clear_images_taken() {
  images_taken_ = 0;
}
inline int32_t PlantCaptchaListItem::_internal_images_taken() const {
  return images_taken_;
}
inline int32_t PlantCaptchaListItem::images_taken() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaListItem.images_taken)
  return _internal_images_taken();
}
inline void PlantCaptchaListItem::_internal_set_images_taken(int32_t value) {
  
  images_taken_ = value;
}
inline void PlantCaptchaListItem::set_images_taken(int32_t value) {
  _internal_set_images_taken(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaListItem.images_taken)
}

// int32 images_processed = 3;
inline void PlantCaptchaListItem::clear_images_processed() {
  images_processed_ = 0;
}
inline int32_t PlantCaptchaListItem::_internal_images_processed() const {
  return images_processed_;
}
inline int32_t PlantCaptchaListItem::images_processed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaListItem.images_processed)
  return _internal_images_processed();
}
inline void PlantCaptchaListItem::_internal_set_images_processed(int32_t value) {
  
  images_processed_ = value;
}
inline void PlantCaptchaListItem::set_images_processed(int32_t value) {
  _internal_set_images_processed(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaListItem.images_processed)
}

// -------------------------------------------------------------------

// GetNextPlantCaptchasListResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextPlantCaptchasListResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextPlantCaptchasListResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchasListResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchasListResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.ts)
  return _internal_ts();
}
inline void GetNextPlantCaptchasListResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchasListResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchasListResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchasListResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchasListResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.ts)
  return _msg;
}
inline void GetNextPlantCaptchasListResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.ts)
}

// repeated .carbon.frontend.plant_captcha.PlantCaptchaListItem plant_captchas = 2;
inline int GetNextPlantCaptchasListResponse::_internal_plant_captchas_size() const {
  return plant_captchas_.size();
}
inline int GetNextPlantCaptchasListResponse::plant_captchas_size() const {
  return _internal_plant_captchas_size();
}
inline void GetNextPlantCaptchasListResponse::clear_plant_captchas() {
  plant_captchas_.Clear();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaListItem* GetNextPlantCaptchasListResponse::mutable_plant_captchas(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.plant_captchas)
  return plant_captchas_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaListItem >*
GetNextPlantCaptchasListResponse::mutable_plant_captchas() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.plant_captchas)
  return &plant_captchas_;
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaListItem& GetNextPlantCaptchasListResponse::_internal_plant_captchas(int index) const {
  return plant_captchas_.Get(index);
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaListItem& GetNextPlantCaptchasListResponse::plant_captchas(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.plant_captchas)
  return _internal_plant_captchas(index);
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaListItem* GetNextPlantCaptchasListResponse::_internal_add_plant_captchas() {
  return plant_captchas_.Add();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaListItem* GetNextPlantCaptchasListResponse::add_plant_captchas() {
  ::carbon::frontend::plant_captcha::PlantCaptchaListItem* _add = _internal_add_plant_captchas();
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.plant_captchas)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaListItem >&
GetNextPlantCaptchasListResponse::plant_captchas() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.GetNextPlantCaptchasListResponse.plant_captchas)
  return plant_captchas_;
}

// -------------------------------------------------------------------

// DeletePlantCaptchaRequest

// string name = 1;
inline void DeletePlantCaptchaRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& DeletePlantCaptchaRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeletePlantCaptchaRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest.name)
}
inline std::string* DeletePlantCaptchaRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest.name)
  return _s;
}
inline const std::string& DeletePlantCaptchaRequest::_internal_name() const {
  return name_.Get();
}
inline void DeletePlantCaptchaRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeletePlantCaptchaRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeletePlantCaptchaRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeletePlantCaptchaRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.DeletePlantCaptchaRequest.name)
}

// -------------------------------------------------------------------

// GetPlantCaptchaRequest

// string name = 1;
inline void GetPlantCaptchaRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& GetPlantCaptchaRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetPlantCaptchaRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetPlantCaptchaRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetPlantCaptchaRequest.name)
}
inline std::string* GetPlantCaptchaRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetPlantCaptchaRequest.name)
  return _s;
}
inline const std::string& GetPlantCaptchaRequest::_internal_name() const {
  return name_.Get();
}
inline void GetPlantCaptchaRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetPlantCaptchaRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetPlantCaptchaRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetPlantCaptchaRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetPlantCaptchaRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetPlantCaptchaRequest.name)
}

// -------------------------------------------------------------------

// PlantCaptchaItem

// string image_url = 1;
inline void PlantCaptchaItem::clear_image_url() {
  image_url_.ClearToEmpty();
}
inline const std::string& PlantCaptchaItem::image_url() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaItem.image_url)
  return _internal_image_url();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaItem::set_image_url(ArgT0&& arg0, ArgT... args) {
 
 image_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaItem.image_url)
}
inline std::string* PlantCaptchaItem::mutable_image_url() {
  std::string* _s = _internal_mutable_image_url();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaItem.image_url)
  return _s;
}
inline const std::string& PlantCaptchaItem::_internal_image_url() const {
  return image_url_.Get();
}
inline void PlantCaptchaItem::_internal_set_image_url(const std::string& value) {
  
  image_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaItem::_internal_mutable_image_url() {
  
  return image_url_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaItem::release_image_url() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaItem.image_url)
  return image_url_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaItem::set_allocated_image_url(std::string* image_url) {
  if (image_url != nullptr) {
    
  } else {
    
  }
  image_url_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), image_url,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (image_url_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    image_url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaItem.image_url)
}

// .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
inline bool PlantCaptchaItem::_internal_has_metadata() const {
  return this != internal_default_instance() && metadata_ != nullptr;
}
inline bool PlantCaptchaItem::has_metadata() const {
  return _internal_has_metadata();
}
inline const ::weed_tracking::PlantCaptchaItemMetadata& PlantCaptchaItem::_internal_metadata() const {
  const ::weed_tracking::PlantCaptchaItemMetadata* p = metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::weed_tracking::PlantCaptchaItemMetadata&>(
      ::weed_tracking::_PlantCaptchaItemMetadata_default_instance_);
}
inline const ::weed_tracking::PlantCaptchaItemMetadata& PlantCaptchaItem::metadata() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaItem.metadata)
  return _internal_metadata();
}
inline void PlantCaptchaItem::unsafe_arena_set_allocated_metadata(
    ::weed_tracking::PlantCaptchaItemMetadata* metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaItem.metadata)
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaItem::release_metadata() {
  
  ::weed_tracking::PlantCaptchaItemMetadata* temp = metadata_;
  metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaItem::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaItem.metadata)
  
  ::weed_tracking::PlantCaptchaItemMetadata* temp = metadata_;
  metadata_ = nullptr;
  return temp;
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaItem::_internal_mutable_metadata() {
  
  if (metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::weed_tracking::PlantCaptchaItemMetadata>(GetArenaForAllocation());
    metadata_ = p;
  }
  return metadata_;
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaItem::mutable_metadata() {
  ::weed_tracking::PlantCaptchaItemMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaItem.metadata)
  return _msg;
}
inline void PlantCaptchaItem::set_allocated_metadata(::weed_tracking::PlantCaptchaItemMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata));
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaItem.metadata)
}

// repeated string additional_image_urls = 3;
inline int PlantCaptchaItem::_internal_additional_image_urls_size() const {
  return additional_image_urls_.size();
}
inline int PlantCaptchaItem::additional_image_urls_size() const {
  return _internal_additional_image_urls_size();
}
inline void PlantCaptchaItem::clear_additional_image_urls() {
  additional_image_urls_.Clear();
}
inline std::string* PlantCaptchaItem::add_additional_image_urls() {
  std::string* _s = _internal_add_additional_image_urls();
  // @@protoc_insertion_point(field_add_mutable:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
  return _s;
}
inline const std::string& PlantCaptchaItem::_internal_additional_image_urls(int index) const {
  return additional_image_urls_.Get(index);
}
inline const std::string& PlantCaptchaItem::additional_image_urls(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
  return _internal_additional_image_urls(index);
}
inline std::string* PlantCaptchaItem::mutable_additional_image_urls(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
  return additional_image_urls_.Mutable(index);
}
inline void PlantCaptchaItem::set_additional_image_urls(int index, const std::string& value) {
  additional_image_urls_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
}
inline void PlantCaptchaItem::set_additional_image_urls(int index, std::string&& value) {
  additional_image_urls_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
}
inline void PlantCaptchaItem::set_additional_image_urls(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  additional_image_urls_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
}
inline void PlantCaptchaItem::set_additional_image_urls(int index, const char* value, size_t size) {
  additional_image_urls_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
}
inline std::string* PlantCaptchaItem::_internal_add_additional_image_urls() {
  return additional_image_urls_.Add();
}
inline void PlantCaptchaItem::add_additional_image_urls(const std::string& value) {
  additional_image_urls_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
}
inline void PlantCaptchaItem::add_additional_image_urls(std::string&& value) {
  additional_image_urls_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
}
inline void PlantCaptchaItem::add_additional_image_urls(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  additional_image_urls_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
}
inline void PlantCaptchaItem::add_additional_image_urls(const char* value, size_t size) {
  additional_image_urls_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
PlantCaptchaItem::additional_image_urls() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
  return additional_image_urls_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
PlantCaptchaItem::mutable_additional_image_urls() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_image_urls)
  return &additional_image_urls_;
}

// repeated .weed_tracking.PlantCaptchaItemMetadata additional_metadatas = 4;
inline int PlantCaptchaItem::_internal_additional_metadatas_size() const {
  return additional_metadatas_.size();
}
inline int PlantCaptchaItem::additional_metadatas_size() const {
  return _internal_additional_metadatas_size();
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaItem::mutable_additional_metadatas(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_metadatas)
  return additional_metadatas_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::PlantCaptchaItemMetadata >*
PlantCaptchaItem::mutable_additional_metadatas() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_metadatas)
  return &additional_metadatas_;
}
inline const ::weed_tracking::PlantCaptchaItemMetadata& PlantCaptchaItem::_internal_additional_metadatas(int index) const {
  return additional_metadatas_.Get(index);
}
inline const ::weed_tracking::PlantCaptchaItemMetadata& PlantCaptchaItem::additional_metadatas(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_metadatas)
  return _internal_additional_metadatas(index);
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaItem::_internal_add_additional_metadatas() {
  return additional_metadatas_.Add();
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaItem::add_additional_metadatas() {
  ::weed_tracking::PlantCaptchaItemMetadata* _add = _internal_add_additional_metadatas();
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_metadatas)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::weed_tracking::PlantCaptchaItemMetadata >&
PlantCaptchaItem::additional_metadatas() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.PlantCaptchaItem.additional_metadatas)
  return additional_metadatas_;
}

// -------------------------------------------------------------------

// GetPlantCaptchaResponse

// .carbon.frontend.plant_captcha.PlantCaptcha plant_captcha = 1;
inline bool GetPlantCaptchaResponse::_internal_has_plant_captcha() const {
  return this != internal_default_instance() && plant_captcha_ != nullptr;
}
inline bool GetPlantCaptchaResponse::has_plant_captcha() const {
  return _internal_has_plant_captcha();
}
inline void GetPlantCaptchaResponse::clear_plant_captcha() {
  if (GetArenaForAllocation() == nullptr && plant_captcha_ != nullptr) {
    delete plant_captcha_;
  }
  plant_captcha_ = nullptr;
}
inline const ::carbon::frontend::plant_captcha::PlantCaptcha& GetPlantCaptchaResponse::_internal_plant_captcha() const {
  const ::carbon::frontend::plant_captcha::PlantCaptcha* p = plant_captcha_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::plant_captcha::PlantCaptcha&>(
      ::carbon::frontend::plant_captcha::_PlantCaptcha_default_instance_);
}
inline const ::carbon::frontend::plant_captcha::PlantCaptcha& GetPlantCaptchaResponse::plant_captcha() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.plant_captcha)
  return _internal_plant_captcha();
}
inline void GetPlantCaptchaResponse::unsafe_arena_set_allocated_plant_captcha(
    ::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(plant_captcha_);
  }
  plant_captcha_ = plant_captcha;
  if (plant_captcha) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.plant_captcha)
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* GetPlantCaptchaResponse::release_plant_captcha() {
  
  ::carbon::frontend::plant_captcha::PlantCaptcha* temp = plant_captcha_;
  plant_captcha_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* GetPlantCaptchaResponse::unsafe_arena_release_plant_captcha() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.plant_captcha)
  
  ::carbon::frontend::plant_captcha::PlantCaptcha* temp = plant_captcha_;
  plant_captcha_ = nullptr;
  return temp;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* GetPlantCaptchaResponse::_internal_mutable_plant_captcha() {
  
  if (plant_captcha_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::plant_captcha::PlantCaptcha>(GetArenaForAllocation());
    plant_captcha_ = p;
  }
  return plant_captcha_;
}
inline ::carbon::frontend::plant_captcha::PlantCaptcha* GetPlantCaptchaResponse::mutable_plant_captcha() {
  ::carbon::frontend::plant_captcha::PlantCaptcha* _msg = _internal_mutable_plant_captcha();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.plant_captcha)
  return _msg;
}
inline void GetPlantCaptchaResponse::set_allocated_plant_captcha(::carbon::frontend::plant_captcha::PlantCaptcha* plant_captcha) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete plant_captcha_;
  }
  if (plant_captcha) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::plant_captcha::PlantCaptcha>::GetOwningArena(plant_captcha);
    if (message_arena != submessage_arena) {
      plant_captcha = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, plant_captcha, submessage_arena);
    }
    
  } else {
    
  }
  plant_captcha_ = plant_captcha;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.plant_captcha)
}

// repeated .carbon.frontend.plant_captcha.PlantCaptchaItem items = 2;
inline int GetPlantCaptchaResponse::_internal_items_size() const {
  return items_.size();
}
inline int GetPlantCaptchaResponse::items_size() const {
  return _internal_items_size();
}
inline void GetPlantCaptchaResponse::clear_items() {
  items_.Clear();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItem* GetPlantCaptchaResponse::mutable_items(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.items)
  return items_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItem >*
GetPlantCaptchaResponse::mutable_items() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.items)
  return &items_;
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaItem& GetPlantCaptchaResponse::_internal_items(int index) const {
  return items_.Get(index);
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaItem& GetPlantCaptchaResponse::items(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.items)
  return _internal_items(index);
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItem* GetPlantCaptchaResponse::_internal_add_items() {
  return items_.Add();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItem* GetPlantCaptchaResponse::add_items() {
  ::carbon::frontend::plant_captcha::PlantCaptchaItem* _add = _internal_add_items();
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.items)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItem >&
GetPlantCaptchaResponse::items() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.GetPlantCaptchaResponse.items)
  return items_;
}

// -------------------------------------------------------------------

// StartPlantCaptchaUploadRequest

// string name = 1;
inline void StartPlantCaptchaUploadRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& StartPlantCaptchaUploadRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StartPlantCaptchaUploadRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest.name)
}
inline std::string* StartPlantCaptchaUploadRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest.name)
  return _s;
}
inline const std::string& StartPlantCaptchaUploadRequest::_internal_name() const {
  return name_.Get();
}
inline void StartPlantCaptchaUploadRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StartPlantCaptchaUploadRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StartPlantCaptchaUploadRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StartPlantCaptchaUploadRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.StartPlantCaptchaUploadRequest.name)
}

// -------------------------------------------------------------------

// GetNextPlantCaptchaUploadStateRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextPlantCaptchaUploadStateRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextPlantCaptchaUploadStateRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchaUploadStateRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchaUploadStateRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.ts)
  return _internal_ts();
}
inline void GetNextPlantCaptchaUploadStateRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaUploadStateRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaUploadStateRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaUploadStateRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaUploadStateRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.ts)
  return _msg;
}
inline void GetNextPlantCaptchaUploadStateRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.ts)
}

// string name = 2;
inline void GetNextPlantCaptchaUploadStateRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& GetNextPlantCaptchaUploadStateRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextPlantCaptchaUploadStateRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.name)
}
inline std::string* GetNextPlantCaptchaUploadStateRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.name)
  return _s;
}
inline const std::string& GetNextPlantCaptchaUploadStateRequest::_internal_name() const {
  return name_.Get();
}
inline void GetNextPlantCaptchaUploadStateRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextPlantCaptchaUploadStateRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextPlantCaptchaUploadStateRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextPlantCaptchaUploadStateRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateRequest.name)
}

// -------------------------------------------------------------------

// GetNextPlantCaptchaUploadStateResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextPlantCaptchaUploadStateResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextPlantCaptchaUploadStateResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchaUploadStateResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextPlantCaptchaUploadStateResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.ts)
  return _internal_ts();
}
inline void GetNextPlantCaptchaUploadStateResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaUploadStateResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaUploadStateResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaUploadStateResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextPlantCaptchaUploadStateResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.ts)
  return _msg;
}
inline void GetNextPlantCaptchaUploadStateResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.ts)
}

// .carbon.frontend.plant_captcha.PlantCaptchaUploadState upload_state = 2;
inline void GetNextPlantCaptchaUploadStateResponse::clear_upload_state() {
  upload_state_ = 0;
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaUploadState GetNextPlantCaptchaUploadStateResponse::_internal_upload_state() const {
  return static_cast< ::carbon::frontend::plant_captcha::PlantCaptchaUploadState >(upload_state_);
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaUploadState GetNextPlantCaptchaUploadStateResponse::upload_state() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.upload_state)
  return _internal_upload_state();
}
inline void GetNextPlantCaptchaUploadStateResponse::_internal_set_upload_state(::carbon::frontend::plant_captcha::PlantCaptchaUploadState value) {
  
  upload_state_ = value;
}
inline void GetNextPlantCaptchaUploadStateResponse::set_upload_state(::carbon::frontend::plant_captcha::PlantCaptchaUploadState value) {
  _internal_set_upload_state(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.upload_state)
}

// int32 percent = 3;
inline void GetNextPlantCaptchaUploadStateResponse::clear_percent() {
  percent_ = 0;
}
inline int32_t GetNextPlantCaptchaUploadStateResponse::_internal_percent() const {
  return percent_;
}
inline int32_t GetNextPlantCaptchaUploadStateResponse::percent() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.percent)
  return _internal_percent();
}
inline void GetNextPlantCaptchaUploadStateResponse::_internal_set_percent(int32_t value) {
  
  percent_ = value;
}
inline void GetNextPlantCaptchaUploadStateResponse::set_percent(int32_t value) {
  _internal_set_percent(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetNextPlantCaptchaUploadStateResponse.percent)
}

// -------------------------------------------------------------------

// PlantCaptchaItemResult

// string id = 1;
inline void PlantCaptchaItemResult::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& PlantCaptchaItemResult::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaItemResult.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaItemResult::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaItemResult.id)
}
inline std::string* PlantCaptchaItemResult::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaItemResult.id)
  return _s;
}
inline const std::string& PlantCaptchaItemResult::_internal_id() const {
  return id_.Get();
}
inline void PlantCaptchaItemResult::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaItemResult::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaItemResult::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaItemResult.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaItemResult::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaItemResult.id)
}

// .weed_tracking.PlantCaptchaUserPrediction user_prediction = 2;
inline void PlantCaptchaItemResult::clear_user_prediction() {
  user_prediction_ = 0;
}
inline ::weed_tracking::PlantCaptchaUserPrediction PlantCaptchaItemResult::_internal_user_prediction() const {
  return static_cast< ::weed_tracking::PlantCaptchaUserPrediction >(user_prediction_);
}
inline ::weed_tracking::PlantCaptchaUserPrediction PlantCaptchaItemResult::user_prediction() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaItemResult.user_prediction)
  return _internal_user_prediction();
}
inline void PlantCaptchaItemResult::_internal_set_user_prediction(::weed_tracking::PlantCaptchaUserPrediction value) {
  
  user_prediction_ = value;
}
inline void PlantCaptchaItemResult::set_user_prediction(::weed_tracking::PlantCaptchaUserPrediction value) {
  _internal_set_user_prediction(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaItemResult.user_prediction)
}

// -------------------------------------------------------------------

// SubmitPlantCaptchaResultsRequest

// string name = 1;
inline void SubmitPlantCaptchaResultsRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& SubmitPlantCaptchaResultsRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SubmitPlantCaptchaResultsRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.name)
}
inline std::string* SubmitPlantCaptchaResultsRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.name)
  return _s;
}
inline const std::string& SubmitPlantCaptchaResultsRequest::_internal_name() const {
  return name_.Get();
}
inline void SubmitPlantCaptchaResultsRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SubmitPlantCaptchaResultsRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SubmitPlantCaptchaResultsRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SubmitPlantCaptchaResultsRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.name)
}

// repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 2;
inline int SubmitPlantCaptchaResultsRequest::_internal_results_size() const {
  return results_.size();
}
inline int SubmitPlantCaptchaResultsRequest::results_size() const {
  return _internal_results_size();
}
inline void SubmitPlantCaptchaResultsRequest::clear_results() {
  results_.Clear();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* SubmitPlantCaptchaResultsRequest::mutable_results(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.results)
  return results_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >*
SubmitPlantCaptchaResultsRequest::mutable_results() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.results)
  return &results_;
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaItemResult& SubmitPlantCaptchaResultsRequest::_internal_results(int index) const {
  return results_.Get(index);
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaItemResult& SubmitPlantCaptchaResultsRequest::results(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.results)
  return _internal_results(index);
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* SubmitPlantCaptchaResultsRequest::_internal_add_results() {
  return results_.Add();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* SubmitPlantCaptchaResultsRequest::add_results() {
  ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* _add = _internal_add_results();
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.results)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >&
SubmitPlantCaptchaResultsRequest::results() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.SubmitPlantCaptchaResultsRequest.results)
  return results_;
}

// -------------------------------------------------------------------

// GetPlantCaptchaItemResultsRequest

// string name = 1;
inline void GetPlantCaptchaItemResultsRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& GetPlantCaptchaItemResultsRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetPlantCaptchaItemResultsRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.name)
}
inline std::string* GetPlantCaptchaItemResultsRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.name)
  return _s;
}
inline const std::string& GetPlantCaptchaItemResultsRequest::_internal_name() const {
  return name_.Get();
}
inline void GetPlantCaptchaItemResultsRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetPlantCaptchaItemResultsRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetPlantCaptchaItemResultsRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetPlantCaptchaItemResultsRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.name)
}

// repeated string id = 2;
inline int GetPlantCaptchaItemResultsRequest::_internal_id_size() const {
  return id_.size();
}
inline int GetPlantCaptchaItemResultsRequest::id_size() const {
  return _internal_id_size();
}
inline void GetPlantCaptchaItemResultsRequest::clear_id() {
  id_.Clear();
}
inline std::string* GetPlantCaptchaItemResultsRequest::add_id() {
  std::string* _s = _internal_add_id();
  // @@protoc_insertion_point(field_add_mutable:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
  return _s;
}
inline const std::string& GetPlantCaptchaItemResultsRequest::_internal_id(int index) const {
  return id_.Get(index);
}
inline const std::string& GetPlantCaptchaItemResultsRequest::id(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
  return _internal_id(index);
}
inline std::string* GetPlantCaptchaItemResultsRequest::mutable_id(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
  return id_.Mutable(index);
}
inline void GetPlantCaptchaItemResultsRequest::set_id(int index, const std::string& value) {
  id_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
}
inline void GetPlantCaptchaItemResultsRequest::set_id(int index, std::string&& value) {
  id_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
}
inline void GetPlantCaptchaItemResultsRequest::set_id(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  id_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
}
inline void GetPlantCaptchaItemResultsRequest::set_id(int index, const char* value, size_t size) {
  id_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
}
inline std::string* GetPlantCaptchaItemResultsRequest::_internal_add_id() {
  return id_.Add();
}
inline void GetPlantCaptchaItemResultsRequest::add_id(const std::string& value) {
  id_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
}
inline void GetPlantCaptchaItemResultsRequest::add_id(std::string&& value) {
  id_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
}
inline void GetPlantCaptchaItemResultsRequest::add_id(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  id_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
}
inline void GetPlantCaptchaItemResultsRequest::add_id(const char* value, size_t size) {
  id_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
GetPlantCaptchaItemResultsRequest::id() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
  return id_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
GetPlantCaptchaItemResultsRequest::mutable_id() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsRequest.id)
  return &id_;
}

// -------------------------------------------------------------------

// GetPlantCaptchaItemResultsResponse

// repeated .carbon.frontend.plant_captcha.PlantCaptchaItemResult results = 1;
inline int GetPlantCaptchaItemResultsResponse::_internal_results_size() const {
  return results_.size();
}
inline int GetPlantCaptchaItemResultsResponse::results_size() const {
  return _internal_results_size();
}
inline void GetPlantCaptchaItemResultsResponse::clear_results() {
  results_.Clear();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* GetPlantCaptchaItemResultsResponse::mutable_results(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse.results)
  return results_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >*
GetPlantCaptchaItemResultsResponse::mutable_results() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse.results)
  return &results_;
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaItemResult& GetPlantCaptchaItemResultsResponse::_internal_results(int index) const {
  return results_.Get(index);
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaItemResult& GetPlantCaptchaItemResultsResponse::results(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse.results)
  return _internal_results(index);
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* GetPlantCaptchaItemResultsResponse::_internal_add_results() {
  return results_.Add();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* GetPlantCaptchaItemResultsResponse::add_results() {
  ::carbon::frontend::plant_captcha::PlantCaptchaItemResult* _add = _internal_add_results();
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse.results)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaItemResult >&
GetPlantCaptchaItemResultsResponse::results() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.GetPlantCaptchaItemResultsResponse.results)
  return results_;
}

// -------------------------------------------------------------------

// CalculatePlantCaptchaRequest

// string name = 1;
inline void CalculatePlantCaptchaRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& CalculatePlantCaptchaRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CalculatePlantCaptchaRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest.name)
}
inline std::string* CalculatePlantCaptchaRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest.name)
  return _s;
}
inline const std::string& CalculatePlantCaptchaRequest::_internal_name() const {
  return name_.Get();
}
inline void CalculatePlantCaptchaRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CalculatePlantCaptchaRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CalculatePlantCaptchaRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CalculatePlantCaptchaRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.CalculatePlantCaptchaRequest.name)
}

// -------------------------------------------------------------------

// CalculatePlantCaptchaResponse

// .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
inline bool CalculatePlantCaptchaResponse::_internal_has_modelinator_config() const {
  return this != internal_default_instance() && modelinator_config_ != nullptr;
}
inline bool CalculatePlantCaptchaResponse::has_modelinator_config() const {
  return _internal_has_modelinator_config();
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& CalculatePlantCaptchaResponse::_internal_modelinator_config() const {
  const ::carbon::aimbot::almanac::ModelinatorConfig* p = modelinator_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::ModelinatorConfig&>(
      ::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& CalculatePlantCaptchaResponse::modelinator_config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.modelinator_config)
  return _internal_modelinator_config();
}
inline void CalculatePlantCaptchaResponse::unsafe_arena_set_allocated_modelinator_config(
    ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator_config_);
  }
  modelinator_config_ = modelinator_config;
  if (modelinator_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.modelinator_config)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* CalculatePlantCaptchaResponse::release_modelinator_config() {
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = modelinator_config_;
  modelinator_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* CalculatePlantCaptchaResponse::unsafe_arena_release_modelinator_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.modelinator_config)
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = modelinator_config_;
  modelinator_config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* CalculatePlantCaptchaResponse::_internal_mutable_modelinator_config() {
  
  if (modelinator_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorConfig>(GetArenaForAllocation());
    modelinator_config_ = p;
  }
  return modelinator_config_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* CalculatePlantCaptchaResponse::mutable_modelinator_config() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_modelinator_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.modelinator_config)
  return _msg;
}
inline void CalculatePlantCaptchaResponse::set_allocated_modelinator_config(::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator_config_);
  }
  if (modelinator_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator_config));
    if (message_arena != submessage_arena) {
      modelinator_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, modelinator_config, submessage_arena);
    }
    
  } else {
    
  }
  modelinator_config_ = modelinator_config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.modelinator_config)
}

// bool succeeded = 2;
inline void CalculatePlantCaptchaResponse::clear_succeeded() {
  succeeded_ = false;
}
inline bool CalculatePlantCaptchaResponse::_internal_succeeded() const {
  return succeeded_;
}
inline bool CalculatePlantCaptchaResponse::succeeded() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.succeeded)
  return _internal_succeeded();
}
inline void CalculatePlantCaptchaResponse::_internal_set_succeeded(bool value) {
  
  succeeded_ = value;
}
inline void CalculatePlantCaptchaResponse::set_succeeded(bool value) {
  _internal_set_succeeded(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.succeeded)
}

// .carbon.frontend.plant_captcha.PlantLabelAlgorithmFailureReason failure_reason = 3;
inline void CalculatePlantCaptchaResponse::clear_failure_reason() {
  failure_reason_ = 0;
}
inline ::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason CalculatePlantCaptchaResponse::_internal_failure_reason() const {
  return static_cast< ::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason >(failure_reason_);
}
inline ::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason CalculatePlantCaptchaResponse::failure_reason() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.failure_reason)
  return _internal_failure_reason();
}
inline void CalculatePlantCaptchaResponse::_internal_set_failure_reason(::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason value) {
  
  failure_reason_ = value;
}
inline void CalculatePlantCaptchaResponse::set_failure_reason(::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason value) {
  _internal_set_failure_reason(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.CalculatePlantCaptchaResponse.failure_reason)
}

// -------------------------------------------------------------------

// PlantCaptchaResult

// .weed_tracking.PlantCaptchaUserPrediction label = 1;
inline void PlantCaptchaResult::clear_label() {
  label_ = 0;
}
inline ::weed_tracking::PlantCaptchaUserPrediction PlantCaptchaResult::_internal_label() const {
  return static_cast< ::weed_tracking::PlantCaptchaUserPrediction >(label_);
}
inline ::weed_tracking::PlantCaptchaUserPrediction PlantCaptchaResult::label() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResult.label)
  return _internal_label();
}
inline void PlantCaptchaResult::_internal_set_label(::weed_tracking::PlantCaptchaUserPrediction value) {
  
  label_ = value;
}
inline void PlantCaptchaResult::set_label(::weed_tracking::PlantCaptchaUserPrediction value) {
  _internal_set_label(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResult.label)
}

// .weed_tracking.PlantCaptchaItemMetadata metadata = 2;
inline bool PlantCaptchaResult::_internal_has_metadata() const {
  return this != internal_default_instance() && metadata_ != nullptr;
}
inline bool PlantCaptchaResult::has_metadata() const {
  return _internal_has_metadata();
}
inline const ::weed_tracking::PlantCaptchaItemMetadata& PlantCaptchaResult::_internal_metadata() const {
  const ::weed_tracking::PlantCaptchaItemMetadata* p = metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::weed_tracking::PlantCaptchaItemMetadata&>(
      ::weed_tracking::_PlantCaptchaItemMetadata_default_instance_);
}
inline const ::weed_tracking::PlantCaptchaItemMetadata& PlantCaptchaResult::metadata() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResult.metadata)
  return _internal_metadata();
}
inline void PlantCaptchaResult::unsafe_arena_set_allocated_metadata(
    ::weed_tracking::PlantCaptchaItemMetadata* metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResult.metadata)
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaResult::release_metadata() {
  
  ::weed_tracking::PlantCaptchaItemMetadata* temp = metadata_;
  metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaResult::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResult.metadata)
  
  ::weed_tracking::PlantCaptchaItemMetadata* temp = metadata_;
  metadata_ = nullptr;
  return temp;
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaResult::_internal_mutable_metadata() {
  
  if (metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::weed_tracking::PlantCaptchaItemMetadata>(GetArenaForAllocation());
    metadata_ = p;
  }
  return metadata_;
}
inline ::weed_tracking::PlantCaptchaItemMetadata* PlantCaptchaResult::mutable_metadata() {
  ::weed_tracking::PlantCaptchaItemMetadata* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResult.metadata)
  return _msg;
}
inline void PlantCaptchaResult::set_allocated_metadata(::weed_tracking::PlantCaptchaItemMetadata* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata));
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResult.metadata)
}

// -------------------------------------------------------------------

// PlantCaptchaResults

// .carbon.aimbot.almanac.ModelinatorConfig current_parameters = 1;
inline bool PlantCaptchaResults::_internal_has_current_parameters() const {
  return this != internal_default_instance() && current_parameters_ != nullptr;
}
inline bool PlantCaptchaResults::has_current_parameters() const {
  return _internal_has_current_parameters();
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& PlantCaptchaResults::_internal_current_parameters() const {
  const ::carbon::aimbot::almanac::ModelinatorConfig* p = current_parameters_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::ModelinatorConfig&>(
      ::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& PlantCaptchaResults::current_parameters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.current_parameters)
  return _internal_current_parameters();
}
inline void PlantCaptchaResults::unsafe_arena_set_allocated_current_parameters(
    ::carbon::aimbot::almanac::ModelinatorConfig* current_parameters) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_parameters_);
  }
  current_parameters_ = current_parameters;
  if (current_parameters) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.current_parameters)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* PlantCaptchaResults::release_current_parameters() {
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = current_parameters_;
  current_parameters_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* PlantCaptchaResults::unsafe_arena_release_current_parameters() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.current_parameters)
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = current_parameters_;
  current_parameters_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* PlantCaptchaResults::_internal_mutable_current_parameters() {
  
  if (current_parameters_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorConfig>(GetArenaForAllocation());
    current_parameters_ = p;
  }
  return current_parameters_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* PlantCaptchaResults::mutable_current_parameters() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_current_parameters();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.current_parameters)
  return _msg;
}
inline void PlantCaptchaResults::set_allocated_current_parameters(::carbon::aimbot::almanac::ModelinatorConfig* current_parameters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_parameters_);
  }
  if (current_parameters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_parameters));
    if (message_arena != submessage_arena) {
      current_parameters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, current_parameters, submessage_arena);
    }
    
  } else {
    
  }
  current_parameters_ = current_parameters;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.current_parameters)
}

// repeated .carbon.frontend.plant_captcha.PlantCaptchaResult captcha_results = 2;
inline int PlantCaptchaResults::_internal_captcha_results_size() const {
  return captcha_results_.size();
}
inline int PlantCaptchaResults::captcha_results_size() const {
  return _internal_captcha_results_size();
}
inline void PlantCaptchaResults::clear_captcha_results() {
  captcha_results_.Clear();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaResult* PlantCaptchaResults::mutable_captcha_results(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.captcha_results)
  return captcha_results_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaResult >*
PlantCaptchaResults::mutable_captcha_results() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.plant_captcha.PlantCaptchaResults.captcha_results)
  return &captcha_results_;
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaResult& PlantCaptchaResults::_internal_captcha_results(int index) const {
  return captcha_results_.Get(index);
}
inline const ::carbon::frontend::plant_captcha::PlantCaptchaResult& PlantCaptchaResults::captcha_results(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.captcha_results)
  return _internal_captcha_results(index);
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaResult* PlantCaptchaResults::_internal_add_captcha_results() {
  return captcha_results_.Add();
}
inline ::carbon::frontend::plant_captcha::PlantCaptchaResult* PlantCaptchaResults::add_captcha_results() {
  ::carbon::frontend::plant_captcha::PlantCaptchaResult* _add = _internal_add_captcha_results();
  // @@protoc_insertion_point(field_add:carbon.frontend.plant_captcha.PlantCaptchaResults.captcha_results)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::plant_captcha::PlantCaptchaResult >&
PlantCaptchaResults::captcha_results() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.plant_captcha.PlantCaptchaResults.captcha_results)
  return captcha_results_;
}

// string algorithm = 3;
inline void PlantCaptchaResults::clear_algorithm() {
  algorithm_.ClearToEmpty();
}
inline const std::string& PlantCaptchaResults::algorithm() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.algorithm)
  return _internal_algorithm();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaResults::set_algorithm(ArgT0&& arg0, ArgT... args) {
 
 algorithm_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.algorithm)
}
inline std::string* PlantCaptchaResults::mutable_algorithm() {
  std::string* _s = _internal_mutable_algorithm();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.algorithm)
  return _s;
}
inline const std::string& PlantCaptchaResults::_internal_algorithm() const {
  return algorithm_.Get();
}
inline void PlantCaptchaResults::_internal_set_algorithm(const std::string& value) {
  
  algorithm_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::_internal_mutable_algorithm() {
  
  return algorithm_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::release_algorithm() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.algorithm)
  return algorithm_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaResults::set_allocated_algorithm(std::string* algorithm) {
  if (algorithm != nullptr) {
    
  } else {
    
  }
  algorithm_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), algorithm,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (algorithm_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    algorithm_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.algorithm)
}

// float goal_crops_targeted = 4;
inline void PlantCaptchaResults::clear_goal_crops_targeted() {
  goal_crops_targeted_ = 0;
}
inline float PlantCaptchaResults::_internal_goal_crops_targeted() const {
  return goal_crops_targeted_;
}
inline float PlantCaptchaResults::goal_crops_targeted() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.goal_crops_targeted)
  return _internal_goal_crops_targeted();
}
inline void PlantCaptchaResults::_internal_set_goal_crops_targeted(float value) {
  
  goal_crops_targeted_ = value;
}
inline void PlantCaptchaResults::set_goal_crops_targeted(float value) {
  _internal_set_goal_crops_targeted(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.goal_crops_targeted)
}

// float goal_weeds_targeted = 5;
inline void PlantCaptchaResults::clear_goal_weeds_targeted() {
  goal_weeds_targeted_ = 0;
}
inline float PlantCaptchaResults::_internal_goal_weeds_targeted() const {
  return goal_weeds_targeted_;
}
inline float PlantCaptchaResults::goal_weeds_targeted() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.goal_weeds_targeted)
  return _internal_goal_weeds_targeted();
}
inline void PlantCaptchaResults::_internal_set_goal_weeds_targeted(float value) {
  
  goal_weeds_targeted_ = value;
}
inline void PlantCaptchaResults::set_goal_weeds_targeted(float value) {
  _internal_set_goal_weeds_targeted(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.goal_weeds_targeted)
}

// float goal_unknown_targeted = 6;
inline void PlantCaptchaResults::clear_goal_unknown_targeted() {
  goal_unknown_targeted_ = 0;
}
inline float PlantCaptchaResults::_internal_goal_unknown_targeted() const {
  return goal_unknown_targeted_;
}
inline float PlantCaptchaResults::goal_unknown_targeted() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.goal_unknown_targeted)
  return _internal_goal_unknown_targeted();
}
inline void PlantCaptchaResults::_internal_set_goal_unknown_targeted(float value) {
  
  goal_unknown_targeted_ = value;
}
inline void PlantCaptchaResults::set_goal_unknown_targeted(float value) {
  _internal_set_goal_unknown_targeted(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.goal_unknown_targeted)
}

// .carbon.aimbot.almanac.AlmanacConfig almanac = 7;
inline bool PlantCaptchaResults::_internal_has_almanac() const {
  return this != internal_default_instance() && almanac_ != nullptr;
}
inline bool PlantCaptchaResults::has_almanac() const {
  return _internal_has_almanac();
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& PlantCaptchaResults::_internal_almanac() const {
  const ::carbon::aimbot::almanac::AlmanacConfig* p = almanac_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::AlmanacConfig&>(
      ::carbon::aimbot::almanac::_AlmanacConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& PlantCaptchaResults::almanac() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.almanac)
  return _internal_almanac();
}
inline void PlantCaptchaResults::unsafe_arena_set_allocated_almanac(
    ::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(almanac_);
  }
  almanac_ = almanac;
  if (almanac) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.almanac)
}
inline ::carbon::aimbot::almanac::AlmanacConfig* PlantCaptchaResults::release_almanac() {
  
  ::carbon::aimbot::almanac::AlmanacConfig* temp = almanac_;
  almanac_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* PlantCaptchaResults::unsafe_arena_release_almanac() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.almanac)
  
  ::carbon::aimbot::almanac::AlmanacConfig* temp = almanac_;
  almanac_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* PlantCaptchaResults::_internal_mutable_almanac() {
  
  if (almanac_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::AlmanacConfig>(GetArenaForAllocation());
    almanac_ = p;
  }
  return almanac_;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* PlantCaptchaResults::mutable_almanac() {
  ::carbon::aimbot::almanac::AlmanacConfig* _msg = _internal_mutable_almanac();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.almanac)
  return _msg;
}
inline void PlantCaptchaResults::set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(almanac_);
  }
  if (almanac) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(almanac));
    if (message_arena != submessage_arena) {
      almanac = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, almanac, submessage_arena);
    }
    
  } else {
    
  }
  almanac_ = almanac;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.almanac)
}

// float max_recommended_mindoo = 8;
inline void PlantCaptchaResults::clear_max_recommended_mindoo() {
  max_recommended_mindoo_ = 0;
}
inline float PlantCaptchaResults::_internal_max_recommended_mindoo() const {
  return max_recommended_mindoo_;
}
inline float PlantCaptchaResults::max_recommended_mindoo() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_mindoo)
  return _internal_max_recommended_mindoo();
}
inline void PlantCaptchaResults::_internal_set_max_recommended_mindoo(float value) {
  
  max_recommended_mindoo_ = value;
}
inline void PlantCaptchaResults::set_max_recommended_mindoo(float value) {
  _internal_set_max_recommended_mindoo(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_mindoo)
}

// int32 min_items_for_recommendation = 9;
inline void PlantCaptchaResults::clear_min_items_for_recommendation() {
  min_items_for_recommendation_ = 0;
}
inline int32_t PlantCaptchaResults::_internal_min_items_for_recommendation() const {
  return min_items_for_recommendation_;
}
inline int32_t PlantCaptchaResults::min_items_for_recommendation() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.min_items_for_recommendation)
  return _internal_min_items_for_recommendation();
}
inline void PlantCaptchaResults::_internal_set_min_items_for_recommendation(int32_t value) {
  
  min_items_for_recommendation_ = value;
}
inline void PlantCaptchaResults::set_min_items_for_recommendation(int32_t value) {
  _internal_set_min_items_for_recommendation(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.min_items_for_recommendation)
}

// bool use_weed_categories_for_weed_labels = 10;
inline void PlantCaptchaResults::clear_use_weed_categories_for_weed_labels() {
  use_weed_categories_for_weed_labels_ = false;
}
inline bool PlantCaptchaResults::_internal_use_weed_categories_for_weed_labels() const {
  return use_weed_categories_for_weed_labels_;
}
inline bool PlantCaptchaResults::use_weed_categories_for_weed_labels() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.use_weed_categories_for_weed_labels)
  return _internal_use_weed_categories_for_weed_labels();
}
inline void PlantCaptchaResults::_internal_set_use_weed_categories_for_weed_labels(bool value) {
  
  use_weed_categories_for_weed_labels_ = value;
}
inline void PlantCaptchaResults::set_use_weed_categories_for_weed_labels(bool value) {
  _internal_set_use_weed_categories_for_weed_labels(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.use_weed_categories_for_weed_labels)
}

// float min_recommended_mindoo = 11;
inline void PlantCaptchaResults::clear_min_recommended_mindoo() {
  min_recommended_mindoo_ = 0;
}
inline float PlantCaptchaResults::_internal_min_recommended_mindoo() const {
  return min_recommended_mindoo_;
}
inline float PlantCaptchaResults::min_recommended_mindoo() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_mindoo)
  return _internal_min_recommended_mindoo();
}
inline void PlantCaptchaResults::_internal_set_min_recommended_mindoo(float value) {
  
  min_recommended_mindoo_ = value;
}
inline void PlantCaptchaResults::set_min_recommended_mindoo(float value) {
  _internal_set_min_recommended_mindoo(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_mindoo)
}

// float min_recommended_weed_threshold = 12;
inline void PlantCaptchaResults::clear_min_recommended_weed_threshold() {
  min_recommended_weed_threshold_ = 0;
}
inline float PlantCaptchaResults::_internal_min_recommended_weed_threshold() const {
  return min_recommended_weed_threshold_;
}
inline float PlantCaptchaResults::min_recommended_weed_threshold() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_weed_threshold)
  return _internal_min_recommended_weed_threshold();
}
inline void PlantCaptchaResults::_internal_set_min_recommended_weed_threshold(float value) {
  
  min_recommended_weed_threshold_ = value;
}
inline void PlantCaptchaResults::set_min_recommended_weed_threshold(float value) {
  _internal_set_min_recommended_weed_threshold(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_weed_threshold)
}

// float max_recommended_weed_threshold = 13;
inline void PlantCaptchaResults::clear_max_recommended_weed_threshold() {
  max_recommended_weed_threshold_ = 0;
}
inline float PlantCaptchaResults::_internal_max_recommended_weed_threshold() const {
  return max_recommended_weed_threshold_;
}
inline float PlantCaptchaResults::max_recommended_weed_threshold() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_weed_threshold)
  return _internal_max_recommended_weed_threshold();
}
inline void PlantCaptchaResults::_internal_set_max_recommended_weed_threshold(float value) {
  
  max_recommended_weed_threshold_ = value;
}
inline void PlantCaptchaResults::set_max_recommended_weed_threshold(float value) {
  _internal_set_max_recommended_weed_threshold(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_weed_threshold)
}

// float min_recommended_crop_threshold = 14;
inline void PlantCaptchaResults::clear_min_recommended_crop_threshold() {
  min_recommended_crop_threshold_ = 0;
}
inline float PlantCaptchaResults::_internal_min_recommended_crop_threshold() const {
  return min_recommended_crop_threshold_;
}
inline float PlantCaptchaResults::min_recommended_crop_threshold() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_crop_threshold)
  return _internal_min_recommended_crop_threshold();
}
inline void PlantCaptchaResults::_internal_set_min_recommended_crop_threshold(float value) {
  
  min_recommended_crop_threshold_ = value;
}
inline void PlantCaptchaResults::set_min_recommended_crop_threshold(float value) {
  _internal_set_min_recommended_crop_threshold(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.min_recommended_crop_threshold)
}

// float max_recommended_crop_threshold = 15;
inline void PlantCaptchaResults::clear_max_recommended_crop_threshold() {
  max_recommended_crop_threshold_ = 0;
}
inline float PlantCaptchaResults::_internal_max_recommended_crop_threshold() const {
  return max_recommended_crop_threshold_;
}
inline float PlantCaptchaResults::max_recommended_crop_threshold() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_crop_threshold)
  return _internal_max_recommended_crop_threshold();
}
inline void PlantCaptchaResults::_internal_set_max_recommended_crop_threshold(float value) {
  
  max_recommended_crop_threshold_ = value;
}
inline void PlantCaptchaResults::set_max_recommended_crop_threshold(float value) {
  _internal_set_max_recommended_crop_threshold(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.max_recommended_crop_threshold)
}

// float min_doo_for_recommendation = 16;
inline void PlantCaptchaResults::clear_min_doo_for_recommendation() {
  min_doo_for_recommendation_ = 0;
}
inline float PlantCaptchaResults::_internal_min_doo_for_recommendation() const {
  return min_doo_for_recommendation_;
}
inline float PlantCaptchaResults::min_doo_for_recommendation() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.min_doo_for_recommendation)
  return _internal_min_doo_for_recommendation();
}
inline void PlantCaptchaResults::_internal_set_min_doo_for_recommendation(float value) {
  
  min_doo_for_recommendation_ = value;
}
inline void PlantCaptchaResults::set_min_doo_for_recommendation(float value) {
  _internal_set_min_doo_for_recommendation(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.min_doo_for_recommendation)
}

// bool use_other_as_tiebreaker = 17;
inline void PlantCaptchaResults::clear_use_other_as_tiebreaker() {
  use_other_as_tiebreaker_ = false;
}
inline bool PlantCaptchaResults::_internal_use_other_as_tiebreaker() const {
  return use_other_as_tiebreaker_;
}
inline bool PlantCaptchaResults::use_other_as_tiebreaker() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.use_other_as_tiebreaker)
  return _internal_use_other_as_tiebreaker();
}
inline void PlantCaptchaResults::_internal_set_use_other_as_tiebreaker(bool value) {
  
  use_other_as_tiebreaker_ = value;
}
inline void PlantCaptchaResults::set_use_other_as_tiebreaker(bool value) {
  _internal_set_use_other_as_tiebreaker(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.use_other_as_tiebreaker)
}

// bool limit_by_crops_missed = 18;
inline void PlantCaptchaResults::clear_limit_by_crops_missed() {
  limit_by_crops_missed_ = false;
}
inline bool PlantCaptchaResults::_internal_limit_by_crops_missed() const {
  return limit_by_crops_missed_;
}
inline bool PlantCaptchaResults::limit_by_crops_missed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.limit_by_crops_missed)
  return _internal_limit_by_crops_missed();
}
inline void PlantCaptchaResults::_internal_set_limit_by_crops_missed(bool value) {
  
  limit_by_crops_missed_ = value;
}
inline void PlantCaptchaResults::set_limit_by_crops_missed(bool value) {
  _internal_set_limit_by_crops_missed(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.limit_by_crops_missed)
}

// int32 number_of_crop_configurations = 19;
inline void PlantCaptchaResults::clear_number_of_crop_configurations() {
  number_of_crop_configurations_ = 0;
}
inline int32_t PlantCaptchaResults::_internal_number_of_crop_configurations() const {
  return number_of_crop_configurations_;
}
inline int32_t PlantCaptchaResults::number_of_crop_configurations() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.number_of_crop_configurations)
  return _internal_number_of_crop_configurations();
}
inline void PlantCaptchaResults::_internal_set_number_of_crop_configurations(int32_t value) {
  
  number_of_crop_configurations_ = value;
}
inline void PlantCaptchaResults::set_number_of_crop_configurations(int32_t value) {
  _internal_set_number_of_crop_configurations(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.number_of_crop_configurations)
}

// string tiebreaker = 20;
inline void PlantCaptchaResults::clear_tiebreaker() {
  tiebreaker_.ClearToEmpty();
}
inline const std::string& PlantCaptchaResults::tiebreaker() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker)
  return _internal_tiebreaker();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaResults::set_tiebreaker(ArgT0&& arg0, ArgT... args) {
 
 tiebreaker_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker)
}
inline std::string* PlantCaptchaResults::mutable_tiebreaker() {
  std::string* _s = _internal_mutable_tiebreaker();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker)
  return _s;
}
inline const std::string& PlantCaptchaResults::_internal_tiebreaker() const {
  return tiebreaker_.Get();
}
inline void PlantCaptchaResults::_internal_set_tiebreaker(const std::string& value) {
  
  tiebreaker_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::_internal_mutable_tiebreaker() {
  
  return tiebreaker_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::release_tiebreaker() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker)
  return tiebreaker_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaResults::set_allocated_tiebreaker(std::string* tiebreaker) {
  if (tiebreaker != nullptr) {
    
  } else {
    
  }
  tiebreaker_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tiebreaker,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (tiebreaker_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    tiebreaker_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker)
}

// bool pad_crop_configurations = 21;
inline void PlantCaptchaResults::clear_pad_crop_configurations() {
  pad_crop_configurations_ = false;
}
inline bool PlantCaptchaResults::_internal_pad_crop_configurations() const {
  return pad_crop_configurations_;
}
inline bool PlantCaptchaResults::pad_crop_configurations() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.pad_crop_configurations)
  return _internal_pad_crop_configurations();
}
inline void PlantCaptchaResults::_internal_set_pad_crop_configurations(bool value) {
  
  pad_crop_configurations_ = value;
}
inline void PlantCaptchaResults::set_pad_crop_configurations(bool value) {
  _internal_set_pad_crop_configurations(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.pad_crop_configurations)
}

// string mindoo_tiebreaker = 22;
inline void PlantCaptchaResults::clear_mindoo_tiebreaker() {
  mindoo_tiebreaker_.ClearToEmpty();
}
inline const std::string& PlantCaptchaResults::mindoo_tiebreaker() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.mindoo_tiebreaker)
  return _internal_mindoo_tiebreaker();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaResults::set_mindoo_tiebreaker(ArgT0&& arg0, ArgT... args) {
 
 mindoo_tiebreaker_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.mindoo_tiebreaker)
}
inline std::string* PlantCaptchaResults::mutable_mindoo_tiebreaker() {
  std::string* _s = _internal_mutable_mindoo_tiebreaker();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.mindoo_tiebreaker)
  return _s;
}
inline const std::string& PlantCaptchaResults::_internal_mindoo_tiebreaker() const {
  return mindoo_tiebreaker_.Get();
}
inline void PlantCaptchaResults::_internal_set_mindoo_tiebreaker(const std::string& value) {
  
  mindoo_tiebreaker_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::_internal_mutable_mindoo_tiebreaker() {
  
  return mindoo_tiebreaker_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::release_mindoo_tiebreaker() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.mindoo_tiebreaker)
  return mindoo_tiebreaker_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaResults::set_allocated_mindoo_tiebreaker(std::string* mindoo_tiebreaker) {
  if (mindoo_tiebreaker != nullptr) {
    
  } else {
    
  }
  mindoo_tiebreaker_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), mindoo_tiebreaker,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (mindoo_tiebreaker_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    mindoo_tiebreaker_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.mindoo_tiebreaker)
}

// bool use_beneficials_as_crops = 23;
inline void PlantCaptchaResults::clear_use_beneficials_as_crops() {
  use_beneficials_as_crops_ = false;
}
inline bool PlantCaptchaResults::_internal_use_beneficials_as_crops() const {
  return use_beneficials_as_crops_;
}
inline bool PlantCaptchaResults::use_beneficials_as_crops() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.use_beneficials_as_crops)
  return _internal_use_beneficials_as_crops();
}
inline void PlantCaptchaResults::_internal_set_use_beneficials_as_crops(bool value) {
  
  use_beneficials_as_crops_ = value;
}
inline void PlantCaptchaResults::set_use_beneficials_as_crops(bool value) {
  _internal_set_use_beneficials_as_crops(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.use_beneficials_as_crops)
}

// bool use_volunteers_as_weeds = 24;
inline void PlantCaptchaResults::clear_use_volunteers_as_weeds() {
  use_volunteers_as_weeds_ = false;
}
inline bool PlantCaptchaResults::_internal_use_volunteers_as_weeds() const {
  return use_volunteers_as_weeds_;
}
inline bool PlantCaptchaResults::use_volunteers_as_weeds() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.use_volunteers_as_weeds)
  return _internal_use_volunteers_as_weeds();
}
inline void PlantCaptchaResults::_internal_set_use_volunteers_as_weeds(bool value) {
  
  use_volunteers_as_weeds_ = value;
}
inline void PlantCaptchaResults::set_use_volunteers_as_weeds(bool value) {
  _internal_set_use_volunteers_as_weeds(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.use_volunteers_as_weeds)
}

// string tiebreaker_strategy_threshold_weed = 25;
inline void PlantCaptchaResults::clear_tiebreaker_strategy_threshold_weed() {
  tiebreaker_strategy_threshold_weed_.ClearToEmpty();
}
inline const std::string& PlantCaptchaResults::tiebreaker_strategy_threshold_weed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_weed)
  return _internal_tiebreaker_strategy_threshold_weed();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaResults::set_tiebreaker_strategy_threshold_weed(ArgT0&& arg0, ArgT... args) {
 
 tiebreaker_strategy_threshold_weed_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_weed)
}
inline std::string* PlantCaptchaResults::mutable_tiebreaker_strategy_threshold_weed() {
  std::string* _s = _internal_mutable_tiebreaker_strategy_threshold_weed();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_weed)
  return _s;
}
inline const std::string& PlantCaptchaResults::_internal_tiebreaker_strategy_threshold_weed() const {
  return tiebreaker_strategy_threshold_weed_.Get();
}
inline void PlantCaptchaResults::_internal_set_tiebreaker_strategy_threshold_weed(const std::string& value) {
  
  tiebreaker_strategy_threshold_weed_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::_internal_mutable_tiebreaker_strategy_threshold_weed() {
  
  return tiebreaker_strategy_threshold_weed_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::release_tiebreaker_strategy_threshold_weed() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_weed)
  return tiebreaker_strategy_threshold_weed_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaResults::set_allocated_tiebreaker_strategy_threshold_weed(std::string* tiebreaker_strategy_threshold_weed) {
  if (tiebreaker_strategy_threshold_weed != nullptr) {
    
  } else {
    
  }
  tiebreaker_strategy_threshold_weed_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tiebreaker_strategy_threshold_weed,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (tiebreaker_strategy_threshold_weed_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    tiebreaker_strategy_threshold_weed_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_weed)
}

// string tiebreaker_strategy_threshold_crop = 26;
inline void PlantCaptchaResults::clear_tiebreaker_strategy_threshold_crop() {
  tiebreaker_strategy_threshold_crop_.ClearToEmpty();
}
inline const std::string& PlantCaptchaResults::tiebreaker_strategy_threshold_crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_crop)
  return _internal_tiebreaker_strategy_threshold_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaResults::set_tiebreaker_strategy_threshold_crop(ArgT0&& arg0, ArgT... args) {
 
 tiebreaker_strategy_threshold_crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_crop)
}
inline std::string* PlantCaptchaResults::mutable_tiebreaker_strategy_threshold_crop() {
  std::string* _s = _internal_mutable_tiebreaker_strategy_threshold_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_crop)
  return _s;
}
inline const std::string& PlantCaptchaResults::_internal_tiebreaker_strategy_threshold_crop() const {
  return tiebreaker_strategy_threshold_crop_.Get();
}
inline void PlantCaptchaResults::_internal_set_tiebreaker_strategy_threshold_crop(const std::string& value) {
  
  tiebreaker_strategy_threshold_crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::_internal_mutable_tiebreaker_strategy_threshold_crop() {
  
  return tiebreaker_strategy_threshold_crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::release_tiebreaker_strategy_threshold_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_crop)
  return tiebreaker_strategy_threshold_crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaResults::set_allocated_tiebreaker_strategy_threshold_crop(std::string* tiebreaker_strategy_threshold_crop) {
  if (tiebreaker_strategy_threshold_crop != nullptr) {
    
  } else {
    
  }
  tiebreaker_strategy_threshold_crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tiebreaker_strategy_threshold_crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (tiebreaker_strategy_threshold_crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    tiebreaker_strategy_threshold_crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_threshold_crop)
}

// string tiebreaker_strategy_mindoo_weed = 27;
inline void PlantCaptchaResults::clear_tiebreaker_strategy_mindoo_weed() {
  tiebreaker_strategy_mindoo_weed_.ClearToEmpty();
}
inline const std::string& PlantCaptchaResults::tiebreaker_strategy_mindoo_weed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_weed)
  return _internal_tiebreaker_strategy_mindoo_weed();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaResults::set_tiebreaker_strategy_mindoo_weed(ArgT0&& arg0, ArgT... args) {
 
 tiebreaker_strategy_mindoo_weed_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_weed)
}
inline std::string* PlantCaptchaResults::mutable_tiebreaker_strategy_mindoo_weed() {
  std::string* _s = _internal_mutable_tiebreaker_strategy_mindoo_weed();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_weed)
  return _s;
}
inline const std::string& PlantCaptchaResults::_internal_tiebreaker_strategy_mindoo_weed() const {
  return tiebreaker_strategy_mindoo_weed_.Get();
}
inline void PlantCaptchaResults::_internal_set_tiebreaker_strategy_mindoo_weed(const std::string& value) {
  
  tiebreaker_strategy_mindoo_weed_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::_internal_mutable_tiebreaker_strategy_mindoo_weed() {
  
  return tiebreaker_strategy_mindoo_weed_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::release_tiebreaker_strategy_mindoo_weed() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_weed)
  return tiebreaker_strategy_mindoo_weed_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaResults::set_allocated_tiebreaker_strategy_mindoo_weed(std::string* tiebreaker_strategy_mindoo_weed) {
  if (tiebreaker_strategy_mindoo_weed != nullptr) {
    
  } else {
    
  }
  tiebreaker_strategy_mindoo_weed_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tiebreaker_strategy_mindoo_weed,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (tiebreaker_strategy_mindoo_weed_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    tiebreaker_strategy_mindoo_weed_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_weed)
}

// string tiebreaker_strategy_mindoo_crop = 28;
inline void PlantCaptchaResults::clear_tiebreaker_strategy_mindoo_crop() {
  tiebreaker_strategy_mindoo_crop_.ClearToEmpty();
}
inline const std::string& PlantCaptchaResults::tiebreaker_strategy_mindoo_crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_crop)
  return _internal_tiebreaker_strategy_mindoo_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PlantCaptchaResults::set_tiebreaker_strategy_mindoo_crop(ArgT0&& arg0, ArgT... args) {
 
 tiebreaker_strategy_mindoo_crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_crop)
}
inline std::string* PlantCaptchaResults::mutable_tiebreaker_strategy_mindoo_crop() {
  std::string* _s = _internal_mutable_tiebreaker_strategy_mindoo_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_crop)
  return _s;
}
inline const std::string& PlantCaptchaResults::_internal_tiebreaker_strategy_mindoo_crop() const {
  return tiebreaker_strategy_mindoo_crop_.Get();
}
inline void PlantCaptchaResults::_internal_set_tiebreaker_strategy_mindoo_crop(const std::string& value) {
  
  tiebreaker_strategy_mindoo_crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::_internal_mutable_tiebreaker_strategy_mindoo_crop() {
  
  return tiebreaker_strategy_mindoo_crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PlantCaptchaResults::release_tiebreaker_strategy_mindoo_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_crop)
  return tiebreaker_strategy_mindoo_crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PlantCaptchaResults::set_allocated_tiebreaker_strategy_mindoo_crop(std::string* tiebreaker_strategy_mindoo_crop) {
  if (tiebreaker_strategy_mindoo_crop != nullptr) {
    
  } else {
    
  }
  tiebreaker_strategy_mindoo_crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tiebreaker_strategy_mindoo_crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (tiebreaker_strategy_mindoo_crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    tiebreaker_strategy_mindoo_crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.PlantCaptchaResults.tiebreaker_strategy_mindoo_crop)
}

// -------------------------------------------------------------------

// VeselkaPlantCaptchaResponse

// .carbon.aimbot.almanac.ModelinatorConfig new_model_parameters = 1;
inline bool VeselkaPlantCaptchaResponse::_internal_has_new_model_parameters() const {
  return this != internal_default_instance() && new_model_parameters_ != nullptr;
}
inline bool VeselkaPlantCaptchaResponse::has_new_model_parameters() const {
  return _internal_has_new_model_parameters();
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& VeselkaPlantCaptchaResponse::_internal_new_model_parameters() const {
  const ::carbon::aimbot::almanac::ModelinatorConfig* p = new_model_parameters_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::ModelinatorConfig&>(
      ::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& VeselkaPlantCaptchaResponse::new_model_parameters() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.new_model_parameters)
  return _internal_new_model_parameters();
}
inline void VeselkaPlantCaptchaResponse::unsafe_arena_set_allocated_new_model_parameters(
    ::carbon::aimbot::almanac::ModelinatorConfig* new_model_parameters) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(new_model_parameters_);
  }
  new_model_parameters_ = new_model_parameters;
  if (new_model_parameters) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.new_model_parameters)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* VeselkaPlantCaptchaResponse::release_new_model_parameters() {
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = new_model_parameters_;
  new_model_parameters_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* VeselkaPlantCaptchaResponse::unsafe_arena_release_new_model_parameters() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.new_model_parameters)
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = new_model_parameters_;
  new_model_parameters_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* VeselkaPlantCaptchaResponse::_internal_mutable_new_model_parameters() {
  
  if (new_model_parameters_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorConfig>(GetArenaForAllocation());
    new_model_parameters_ = p;
  }
  return new_model_parameters_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* VeselkaPlantCaptchaResponse::mutable_new_model_parameters() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_new_model_parameters();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.new_model_parameters)
  return _msg;
}
inline void VeselkaPlantCaptchaResponse::set_allocated_new_model_parameters(::carbon::aimbot::almanac::ModelinatorConfig* new_model_parameters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(new_model_parameters_);
  }
  if (new_model_parameters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(new_model_parameters));
    if (message_arena != submessage_arena) {
      new_model_parameters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, new_model_parameters, submessage_arena);
    }
    
  } else {
    
  }
  new_model_parameters_ = new_model_parameters;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.new_model_parameters)
}

// bool succeeded = 2;
inline void VeselkaPlantCaptchaResponse::clear_succeeded() {
  succeeded_ = false;
}
inline bool VeselkaPlantCaptchaResponse::_internal_succeeded() const {
  return succeeded_;
}
inline bool VeselkaPlantCaptchaResponse::succeeded() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.succeeded)
  return _internal_succeeded();
}
inline void VeselkaPlantCaptchaResponse::_internal_set_succeeded(bool value) {
  
  succeeded_ = value;
}
inline void VeselkaPlantCaptchaResponse::set_succeeded(bool value) {
  _internal_set_succeeded(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.VeselkaPlantCaptchaResponse.succeeded)
}

// -------------------------------------------------------------------

// GetOriginalModelinatorConfigRequest

// string name = 1;
inline void GetOriginalModelinatorConfigRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& GetOriginalModelinatorConfigRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetOriginalModelinatorConfigRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest.name)
}
inline std::string* GetOriginalModelinatorConfigRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest.name)
  return _s;
}
inline const std::string& GetOriginalModelinatorConfigRequest::_internal_name() const {
  return name_.Get();
}
inline void GetOriginalModelinatorConfigRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetOriginalModelinatorConfigRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetOriginalModelinatorConfigRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetOriginalModelinatorConfigRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigRequest.name)
}

// -------------------------------------------------------------------

// GetOriginalModelinatorConfigResponse

// .carbon.aimbot.almanac.ModelinatorConfig modelinator_config = 1;
inline bool GetOriginalModelinatorConfigResponse::_internal_has_modelinator_config() const {
  return this != internal_default_instance() && modelinator_config_ != nullptr;
}
inline bool GetOriginalModelinatorConfigResponse::has_modelinator_config() const {
  return _internal_has_modelinator_config();
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& GetOriginalModelinatorConfigResponse::_internal_modelinator_config() const {
  const ::carbon::aimbot::almanac::ModelinatorConfig* p = modelinator_config_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::aimbot::almanac::ModelinatorConfig&>(
      ::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& GetOriginalModelinatorConfigResponse::modelinator_config() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse.modelinator_config)
  return _internal_modelinator_config();
}
inline void GetOriginalModelinatorConfigResponse::unsafe_arena_set_allocated_modelinator_config(
    ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator_config_);
  }
  modelinator_config_ = modelinator_config;
  if (modelinator_config) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse.modelinator_config)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetOriginalModelinatorConfigResponse::release_modelinator_config() {
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = modelinator_config_;
  modelinator_config_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetOriginalModelinatorConfigResponse::unsafe_arena_release_modelinator_config() {
  // @@protoc_insertion_point(field_release:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse.modelinator_config)
  
  ::carbon::aimbot::almanac::ModelinatorConfig* temp = modelinator_config_;
  modelinator_config_ = nullptr;
  return temp;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetOriginalModelinatorConfigResponse::_internal_mutable_modelinator_config() {
  
  if (modelinator_config_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::aimbot::almanac::ModelinatorConfig>(GetArenaForAllocation());
    modelinator_config_ = p;
  }
  return modelinator_config_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetOriginalModelinatorConfigResponse::mutable_modelinator_config() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_modelinator_config();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse.modelinator_config)
  return _msg;
}
inline void GetOriginalModelinatorConfigResponse::set_allocated_modelinator_config(::carbon::aimbot::almanac::ModelinatorConfig* modelinator_config) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator_config_);
  }
  if (modelinator_config) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(modelinator_config));
    if (message_arena != submessage_arena) {
      modelinator_config = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, modelinator_config, submessage_arena);
    }
    
  } else {
    
  }
  modelinator_config_ = modelinator_config;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.plant_captcha.GetOriginalModelinatorConfigResponse.modelinator_config)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetCaptchaRowStatusResponse

// map<int32, .weed_tracking.PlantCaptchaStatusResponse> row_status = 1;
inline int GetCaptchaRowStatusResponse::_internal_row_status_size() const {
  return row_status_.size();
}
inline int GetCaptchaRowStatusResponse::row_status_size() const {
  return _internal_row_status_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >&
GetCaptchaRowStatusResponse::_internal_row_status() const {
  return row_status_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >&
GetCaptchaRowStatusResponse::row_status() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.row_status)
  return _internal_row_status();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >*
GetCaptchaRowStatusResponse::_internal_mutable_row_status() {
  return row_status_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::weed_tracking::PlantCaptchaStatusResponse >*
GetCaptchaRowStatusResponse::mutable_row_status() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.plant_captcha.GetCaptchaRowStatusResponse.row_status)
  return _internal_mutable_row_status();
}

// -------------------------------------------------------------------

// CancelPlantCaptchaOnRowRequest

// int32 row_id = 1;
inline void CancelPlantCaptchaOnRowRequest::clear_row_id() {
  row_id_ = 0;
}
inline int32_t CancelPlantCaptchaOnRowRequest::_internal_row_id() const {
  return row_id_;
}
inline int32_t CancelPlantCaptchaOnRowRequest::row_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest.row_id)
  return _internal_row_id();
}
inline void CancelPlantCaptchaOnRowRequest::_internal_set_row_id(int32_t value) {
  
  row_id_ = value;
}
inline void CancelPlantCaptchaOnRowRequest::set_row_id(int32_t value) {
  _internal_set_row_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.plant_captcha.CancelPlantCaptchaOnRowRequest.row_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace plant_captcha
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::plant_captcha::PlantCaptchaUploadState> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::plant_captcha::PlantCaptchaUploadState>() {
  return ::carbon::frontend::plant_captcha::PlantCaptchaUploadState_descriptor();
}
template <> struct is_proto_enum< ::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason>() {
  return ::carbon::frontend::plant_captcha::PlantLabelAlgorithmFailureReason_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fplant_5fcaptcha_2eproto

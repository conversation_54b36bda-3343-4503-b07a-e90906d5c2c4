// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/profile_sync.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fprofile_5fsync_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fprofile_5fsync_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fprofile_5fsync_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fprofile_5fsync_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto;
namespace carbon {
namespace frontend {
namespace profile_sync {
class ProfileSyncData;
struct ProfileSyncDataDefaultTypeInternal;
extern ProfileSyncDataDefaultTypeInternal _ProfileSyncData_default_instance_;
}  // namespace profile_sync
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::profile_sync::ProfileSyncData* Arena::CreateMaybeMessage<::carbon::frontend::profile_sync::ProfileSyncData>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace profile_sync {

enum ProfileType : int {
  ALMANAC = 0,
  DISCRIMINATOR = 1,
  MODELINATOR = 3,
  BANDING = 4,
  THINNING = 5,
  TARGET_VELOCITY_ESTIMATOR = 6,
  CATEGORY_COLLECTION = 7,
  CATEGORY = 8,
  ProfileType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ProfileType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ProfileType_IsValid(int value);
constexpr ProfileType ProfileType_MIN = ALMANAC;
constexpr ProfileType ProfileType_MAX = CATEGORY;
constexpr int ProfileType_ARRAYSIZE = ProfileType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ProfileType_descriptor();
template<typename T>
inline const std::string& ProfileType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ProfileType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ProfileType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ProfileType_descriptor(), enum_t_value);
}
inline bool ProfileType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ProfileType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ProfileType>(
    ProfileType_descriptor(), name, value);
}
// ===================================================================

class ProfileSyncData final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.profile_sync.ProfileSyncData) */ {
 public:
  inline ProfileSyncData() : ProfileSyncData(nullptr) {}
  ~ProfileSyncData() override;
  explicit constexpr ProfileSyncData(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ProfileSyncData(const ProfileSyncData& from);
  ProfileSyncData(ProfileSyncData&& from) noexcept
    : ProfileSyncData() {
    *this = ::std::move(from);
  }

  inline ProfileSyncData& operator=(const ProfileSyncData& from) {
    CopyFrom(from);
    return *this;
  }
  inline ProfileSyncData& operator=(ProfileSyncData&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ProfileSyncData& default_instance() {
    return *internal_default_instance();
  }
  static inline const ProfileSyncData* internal_default_instance() {
    return reinterpret_cast<const ProfileSyncData*>(
               &_ProfileSyncData_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ProfileSyncData& a, ProfileSyncData& b) {
    a.Swap(&b);
  }
  inline void Swap(ProfileSyncData* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ProfileSyncData* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ProfileSyncData* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ProfileSyncData>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ProfileSyncData& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ProfileSyncData& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ProfileSyncData* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.profile_sync.ProfileSyncData";
  }
  protected:
  explicit ProfileSyncData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLastUpdatedTsMsFieldNumber = 2,
    kProfileTypeFieldNumber = 1,
    kDeletedFieldNumber = 3,
    kProtectedFieldNumber = 4,
  };
  // int64 last_updated_ts_ms = 2;
  void clear_last_updated_ts_ms();
  int64_t last_updated_ts_ms() const;
  void set_last_updated_ts_ms(int64_t value);
  private:
  int64_t _internal_last_updated_ts_ms() const;
  void _internal_set_last_updated_ts_ms(int64_t value);
  public:

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  void clear_profile_type();
  ::carbon::frontend::profile_sync::ProfileType profile_type() const;
  void set_profile_type(::carbon::frontend::profile_sync::ProfileType value);
  private:
  ::carbon::frontend::profile_sync::ProfileType _internal_profile_type() const;
  void _internal_set_profile_type(::carbon::frontend::profile_sync::ProfileType value);
  public:

  // bool deleted = 3;
  void clear_deleted();
  bool deleted() const;
  void set_deleted(bool value);
  private:
  bool _internal_deleted() const;
  void _internal_set_deleted(bool value);
  public:

  // bool protected = 4;
  void clear_protected_();
  bool protected_() const;
  void set_protected_(bool value);
  private:
  bool _internal_protected_() const;
  void _internal_set_protected_(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.profile_sync.ProfileSyncData)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int64_t last_updated_ts_ms_;
  int profile_type_;
  bool deleted_;
  bool protected__;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fprofile_5fsync_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ProfileSyncData

// .carbon.frontend.profile_sync.ProfileType profile_type = 1;
inline void ProfileSyncData::clear_profile_type() {
  profile_type_ = 0;
}
inline ::carbon::frontend::profile_sync::ProfileType ProfileSyncData::_internal_profile_type() const {
  return static_cast< ::carbon::frontend::profile_sync::ProfileType >(profile_type_);
}
inline ::carbon::frontend::profile_sync::ProfileType ProfileSyncData::profile_type() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.profile_sync.ProfileSyncData.profile_type)
  return _internal_profile_type();
}
inline void ProfileSyncData::_internal_set_profile_type(::carbon::frontend::profile_sync::ProfileType value) {
  
  profile_type_ = value;
}
inline void ProfileSyncData::set_profile_type(::carbon::frontend::profile_sync::ProfileType value) {
  _internal_set_profile_type(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.profile_sync.ProfileSyncData.profile_type)
}

// int64 last_updated_ts_ms = 2;
inline void ProfileSyncData::clear_last_updated_ts_ms() {
  last_updated_ts_ms_ = int64_t{0};
}
inline int64_t ProfileSyncData::_internal_last_updated_ts_ms() const {
  return last_updated_ts_ms_;
}
inline int64_t ProfileSyncData::last_updated_ts_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.profile_sync.ProfileSyncData.last_updated_ts_ms)
  return _internal_last_updated_ts_ms();
}
inline void ProfileSyncData::_internal_set_last_updated_ts_ms(int64_t value) {
  
  last_updated_ts_ms_ = value;
}
inline void ProfileSyncData::set_last_updated_ts_ms(int64_t value) {
  _internal_set_last_updated_ts_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.profile_sync.ProfileSyncData.last_updated_ts_ms)
}

// bool deleted = 3;
inline void ProfileSyncData::clear_deleted() {
  deleted_ = false;
}
inline bool ProfileSyncData::_internal_deleted() const {
  return deleted_;
}
inline bool ProfileSyncData::deleted() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.profile_sync.ProfileSyncData.deleted)
  return _internal_deleted();
}
inline void ProfileSyncData::_internal_set_deleted(bool value) {
  
  deleted_ = value;
}
inline void ProfileSyncData::set_deleted(bool value) {
  _internal_set_deleted(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.profile_sync.ProfileSyncData.deleted)
}

// bool protected = 4;
inline void ProfileSyncData::clear_protected_() {
  protected__ = false;
}
inline bool ProfileSyncData::_internal_protected_() const {
  return protected__;
}
inline bool ProfileSyncData::protected_() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.profile_sync.ProfileSyncData.protected)
  return _internal_protected_();
}
inline void ProfileSyncData::_internal_set_protected_(bool value) {
  
  protected__ = value;
}
inline void ProfileSyncData::set_protected_(bool value) {
  _internal_set_protected_(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.profile_sync.ProfileSyncData.protected)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace profile_sync
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::profile_sync::ProfileType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::profile_sync::ProfileType>() {
  return ::carbon::frontend::profile_sync::ProfileType_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fprofile_5fsync_2eproto

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/profile_sync.proto

#include "frontend/proto/profile_sync.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace profile_sync {
constexpr ProfileSyncData::ProfileSyncData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : last_updated_ts_ms_(int64_t{0})
  , profile_type_(0)

  , deleted_(false)
  , protected__(false){}
struct ProfileSyncDataDefaultTypeInternal {
  constexpr ProfileSyncDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ProfileSyncDataDefaultTypeInternal() {}
  union {
    ProfileSyncData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ProfileSyncDataDefaultTypeInternal _ProfileSyncData_default_instance_;
}  // namespace profile_sync
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fprofile_5fsync_2eproto[1];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fprofile_5fsync_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fprofile_5fsync_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fprofile_5fsync_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::profile_sync::ProfileSyncData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::profile_sync::ProfileSyncData, profile_type_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::profile_sync::ProfileSyncData, last_updated_ts_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::profile_sync::ProfileSyncData, deleted_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::profile_sync::ProfileSyncData, protected__),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::profile_sync::ProfileSyncData)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::profile_sync::_ProfileSyncData_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fprofile_5fsync_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n!frontend/proto/profile_sync.proto\022\034car"
  "bon.frontend.profile_sync\"\222\001\n\017ProfileSyn"
  "cData\022\?\n\014profile_type\030\001 \001(\0162).carbon.fro"
  "ntend.profile_sync.ProfileType\022\032\n\022last_u"
  "pdated_ts_ms\030\002 \001(\003\022\017\n\007deleted\030\003 \001(\010\022\021\n\tp"
  "rotected\030\004 \001(\010*\237\001\n\013ProfileType\022\013\n\007ALMANA"
  "C\020\000\022\021\n\rDISCRIMINATOR\020\001\022\017\n\013MODELINATOR\020\003\022"
  "\013\n\007BANDING\020\004\022\014\n\010THINNING\020\005\022\035\n\031TARGET_VEL"
  "OCITY_ESTIMATOR\020\006\022\027\n\023CATEGORY_COLLECTION"
  "\020\007\022\014\n\010CATEGORY\020\010B\020Z\016proto/frontendb\006prot"
  "o3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto = {
  false, false, 402, descriptor_table_protodef_frontend_2fproto_2fprofile_5fsync_2eproto, "frontend/proto/profile_sync.proto", 
  &descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto_once, nullptr, 0, 1,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fprofile_5fsync_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fprofile_5fsync_2eproto, file_level_enum_descriptors_frontend_2fproto_2fprofile_5fsync_2eproto, file_level_service_descriptors_frontend_2fproto_2fprofile_5fsync_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fprofile_5fsync_2eproto(&descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto);
namespace carbon {
namespace frontend {
namespace profile_sync {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ProfileType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fprofile_5fsync_2eproto[0];
}
bool ProfileType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class ProfileSyncData::_Internal {
 public:
};

ProfileSyncData::ProfileSyncData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.profile_sync.ProfileSyncData)
}
ProfileSyncData::ProfileSyncData(const ProfileSyncData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&last_updated_ts_ms_, &from.last_updated_ts_ms_,
    static_cast<size_t>(reinterpret_cast<char*>(&protected__) -
    reinterpret_cast<char*>(&last_updated_ts_ms_)) + sizeof(protected__));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.profile_sync.ProfileSyncData)
}

inline void ProfileSyncData::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&last_updated_ts_ms_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&protected__) -
    reinterpret_cast<char*>(&last_updated_ts_ms_)) + sizeof(protected__));
}

ProfileSyncData::~ProfileSyncData() {
  // @@protoc_insertion_point(destructor:carbon.frontend.profile_sync.ProfileSyncData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ProfileSyncData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ProfileSyncData::ArenaDtor(void* object) {
  ProfileSyncData* _this = reinterpret_cast< ProfileSyncData* >(object);
  (void)_this;
}
void ProfileSyncData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ProfileSyncData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ProfileSyncData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.profile_sync.ProfileSyncData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&last_updated_ts_ms_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&protected__) -
      reinterpret_cast<char*>(&last_updated_ts_ms_)) + sizeof(protected__));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ProfileSyncData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_profile_type(static_cast<::carbon::frontend::profile_sync::ProfileType>(val));
        } else
          goto handle_unusual;
        continue;
      // int64 last_updated_ts_ms = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          last_updated_ts_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool deleted = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          deleted_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool protected = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          protected__ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ProfileSyncData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.profile_sync.ProfileSyncData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  if (this->_internal_profile_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_profile_type(), target);
  }

  // int64 last_updated_ts_ms = 2;
  if (this->_internal_last_updated_ts_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_last_updated_ts_ms(), target);
  }

  // bool deleted = 3;
  if (this->_internal_deleted() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_deleted(), target);
  }

  // bool protected = 4;
  if (this->_internal_protected_() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_protected_(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.profile_sync.ProfileSyncData)
  return target;
}

size_t ProfileSyncData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.profile_sync.ProfileSyncData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 last_updated_ts_ms = 2;
  if (this->_internal_last_updated_ts_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_last_updated_ts_ms());
  }

  // .carbon.frontend.profile_sync.ProfileType profile_type = 1;
  if (this->_internal_profile_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_profile_type());
  }

  // bool deleted = 3;
  if (this->_internal_deleted() != 0) {
    total_size += 1 + 1;
  }

  // bool protected = 4;
  if (this->_internal_protected_() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ProfileSyncData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ProfileSyncData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ProfileSyncData::GetClassData() const { return &_class_data_; }

void ProfileSyncData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ProfileSyncData *>(to)->MergeFrom(
      static_cast<const ProfileSyncData &>(from));
}


void ProfileSyncData::MergeFrom(const ProfileSyncData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.profile_sync.ProfileSyncData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_last_updated_ts_ms() != 0) {
    _internal_set_last_updated_ts_ms(from._internal_last_updated_ts_ms());
  }
  if (from._internal_profile_type() != 0) {
    _internal_set_profile_type(from._internal_profile_type());
  }
  if (from._internal_deleted() != 0) {
    _internal_set_deleted(from._internal_deleted());
  }
  if (from._internal_protected_() != 0) {
    _internal_set_protected_(from._internal_protected_());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ProfileSyncData::CopyFrom(const ProfileSyncData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.profile_sync.ProfileSyncData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ProfileSyncData::IsInitialized() const {
  return true;
}

void ProfileSyncData::InternalSwap(ProfileSyncData* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ProfileSyncData, protected__)
      + sizeof(ProfileSyncData::protected__)
      - PROTOBUF_FIELD_OFFSET(ProfileSyncData, last_updated_ts_ms_)>(
          reinterpret_cast<char*>(&last_updated_ts_ms_),
          reinterpret_cast<char*>(&other->last_updated_ts_ms_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ProfileSyncData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto_getter, &descriptor_table_frontend_2fproto_2fprofile_5fsync_2eproto_once,
      file_level_metadata_frontend_2fproto_2fprofile_5fsync_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace profile_sync
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::profile_sync::ProfileSyncData* Arena::CreateMaybeMessage< ::carbon::frontend::profile_sync::ProfileSyncData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::profile_sync::ProfileSyncData >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class SoftwareVersion(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    tag: typing___Text = ...
    available: builtin___bool = ...
    ready: builtin___bool = ...

    def __init__(self,
        *,
        tag : typing___Optional[typing___Text] = None,
        available : typing___Optional[builtin___bool] = None,
        ready : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"available",b"available",u"ready",b"ready",u"tag",b"tag"]) -> None: ...
type___SoftwareVersion = SoftwareVersion

class HostSoftwareVersionState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    host_name: typing___Text = ...
    host_id: builtin___int = ...
    active: builtin___bool = ...
    updating: builtin___bool = ...

    @property
    def current(self) -> type___SoftwareVersion: ...

    @property
    def target(self) -> type___SoftwareVersion: ...

    @property
    def previous(self) -> type___SoftwareVersion: ...

    def __init__(self,
        *,
        host_name : typing___Optional[typing___Text] = None,
        host_id : typing___Optional[builtin___int] = None,
        active : typing___Optional[builtin___bool] = None,
        current : typing___Optional[type___SoftwareVersion] = None,
        target : typing___Optional[type___SoftwareVersion] = None,
        previous : typing___Optional[type___SoftwareVersion] = None,
        updating : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current",b"current",u"previous",b"previous",u"target",b"target"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active",b"active",u"current",b"current",u"host_id",b"host_id",u"host_name",b"host_name",u"previous",b"previous",u"target",b"target",u"updating",b"updating"]) -> None: ...
type___HostSoftwareVersionState = HostSoftwareVersionState

class SoftwareVersionState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    updating: builtin___bool = ...
    show_software_update_to_user: builtin___bool = ...
    version_mismatch: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def current(self) -> type___SoftwareVersion: ...

    @property
    def target(self) -> type___SoftwareVersion: ...

    @property
    def previous(self) -> type___SoftwareVersion: ...

    @property
    def host_states(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___HostSoftwareVersionState]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        current : typing___Optional[type___SoftwareVersion] = None,
        target : typing___Optional[type___SoftwareVersion] = None,
        previous : typing___Optional[type___SoftwareVersion] = None,
        updating : typing___Optional[builtin___bool] = None,
        show_software_update_to_user : typing___Optional[builtin___bool] = None,
        host_states : typing___Optional[typing___Iterable[type___HostSoftwareVersionState]] = None,
        version_mismatch : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current",b"current",u"previous",b"previous",u"target",b"target",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"host_states",b"host_states",u"previous",b"previous",u"show_software_update_to_user",b"show_software_update_to_user",u"target",b"target",u"ts",b"ts",u"updating",b"updating",u"version_mismatch",b"version_mismatch"]) -> None: ...
type___SoftwareVersionState = SoftwareVersionState

class SoftwareVersionStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    get_host_states: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        get_host_states : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"get_host_states",b"get_host_states",u"ts",b"ts"]) -> None: ...
type___SoftwareVersionStateRequest = SoftwareVersionStateRequest

class UpdateHostRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    host_id: builtin___int = ...

    def __init__(self,
        *,
        host_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"host_id",b"host_id"]) -> None: ...
type___UpdateHostRequest = UpdateHostRequest

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import alarm_pb2 as frontend_dot_proto_dot_alarm__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class AlarmServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextAlarmList = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/GetNextAlarmList',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_alarm__pb2.AlarmTable.FromString,
                )
        self.GetNextAlarmCount = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/GetNextAlarmCount',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_alarm__pb2.AlarmCount.FromString,
                )
        self.GetNextNewAlarmList = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/GetNextNewAlarmList',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_alarm__pb2.AlarmTable.FromString,
                )
        self.AcknowledgeAlarm = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/AcknowledgeAlarm',
                request_serializer=frontend_dot_proto_dot_alarm__pb2.AcknowledgeRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.ResetAlarms = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/ResetAlarms',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextAlarmLog = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/GetNextAlarmLog',
                request_serializer=frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogResponse.FromString,
                )
        self.GetNextAlarmLogCount = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/GetNextAlarmLogCount',
                request_serializer=frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogCountRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogCountResponse.FromString,
                )
        self.AttemptAutofixAlarm = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/AttemptAutofixAlarm',
                request_serializer=frontend_dot_proto_dot_alarm__pb2.AttemptAutofixAlarmRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextAutofixAlarmStatus = channel.unary_unary(
                '/carbon.frontend.alarm.AlarmService/GetNextAutofixAlarmStatus',
                request_serializer=frontend_dot_proto_dot_alarm__pb2.GetNextAutofixAlarmStatusRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_alarm__pb2.GetNextAutofixAlarmStatusResponse.FromString,
                )


class AlarmServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextAlarmList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextAlarmCount(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextNewAlarmList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AcknowledgeAlarm(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ResetAlarms(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextAlarmLog(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextAlarmLogCount(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AttemptAutofixAlarm(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextAutofixAlarmStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_AlarmServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextAlarmList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAlarmList,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_alarm__pb2.AlarmTable.SerializeToString,
            ),
            'GetNextAlarmCount': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAlarmCount,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_alarm__pb2.AlarmCount.SerializeToString,
            ),
            'GetNextNewAlarmList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextNewAlarmList,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_alarm__pb2.AlarmTable.SerializeToString,
            ),
            'AcknowledgeAlarm': grpc.unary_unary_rpc_method_handler(
                    servicer.AcknowledgeAlarm,
                    request_deserializer=frontend_dot_proto_dot_alarm__pb2.AcknowledgeRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'ResetAlarms': grpc.unary_unary_rpc_method_handler(
                    servicer.ResetAlarms,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextAlarmLog': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAlarmLog,
                    request_deserializer=frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogResponse.SerializeToString,
            ),
            'GetNextAlarmLogCount': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAlarmLogCount,
                    request_deserializer=frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogCountRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogCountResponse.SerializeToString,
            ),
            'AttemptAutofixAlarm': grpc.unary_unary_rpc_method_handler(
                    servicer.AttemptAutofixAlarm,
                    request_deserializer=frontend_dot_proto_dot_alarm__pb2.AttemptAutofixAlarmRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextAutofixAlarmStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextAutofixAlarmStatus,
                    request_deserializer=frontend_dot_proto_dot_alarm__pb2.GetNextAutofixAlarmStatusRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_alarm__pb2.GetNextAutofixAlarmStatusResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.alarm.AlarmService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class AlarmService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextAlarmList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/GetNextAlarmList',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_alarm__pb2.AlarmTable.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextAlarmCount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/GetNextAlarmCount',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_alarm__pb2.AlarmCount.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextNewAlarmList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/GetNextNewAlarmList',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_alarm__pb2.AlarmTable.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AcknowledgeAlarm(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/AcknowledgeAlarm',
            frontend_dot_proto_dot_alarm__pb2.AcknowledgeRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ResetAlarms(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/ResetAlarms',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextAlarmLog(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/GetNextAlarmLog',
            frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogRequest.SerializeToString,
            frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextAlarmLogCount(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/GetNextAlarmLogCount',
            frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogCountRequest.SerializeToString,
            frontend_dot_proto_dot_alarm__pb2.GetNextAlarmLogCountResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AttemptAutofixAlarm(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/AttemptAutofixAlarm',
            frontend_dot_proto_dot_alarm__pb2.AttemptAutofixAlarmRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextAutofixAlarmStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.alarm.AlarmService/GetNextAutofixAlarmStatus',
            frontend_dot_proto_dot_alarm__pb2.GetNextAutofixAlarmStatusRequest.SerializeToString,
            frontend_dot_proto_dot_alarm__pb2.GetNextAutofixAlarmStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

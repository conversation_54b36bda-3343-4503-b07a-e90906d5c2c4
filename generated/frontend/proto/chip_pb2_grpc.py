# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import chip_pb2 as frontend_dot_proto_dot_chip__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class ChipServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetChipMetadata = channel.unary_unary(
                '/carbon.frontend.chip.ChipService/GetChipMetadata',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_chip__pb2.GetChipMetadataResponse.FromString,
                )
        self.GetDownloadedChipIds = channel.unary_unary(
                '/carbon.frontend.chip.ChipService/GetDownloadedChipIds',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_chip__pb2.ChipIdsResponse.FromString,
                )
        self.GetSyncedChipIds = channel.unary_unary(
                '/carbon.frontend.chip.ChipService/GetSyncedChipIds',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_chip__pb2.ChipIdsResponse.FromString,
                )


class ChipServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetChipMetadata(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetDownloadedChipIds(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetSyncedChipIds(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ChipServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetChipMetadata': grpc.unary_unary_rpc_method_handler(
                    servicer.GetChipMetadata,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_chip__pb2.GetChipMetadataResponse.SerializeToString,
            ),
            'GetDownloadedChipIds': grpc.unary_unary_rpc_method_handler(
                    servicer.GetDownloadedChipIds,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_chip__pb2.ChipIdsResponse.SerializeToString,
            ),
            'GetSyncedChipIds': grpc.unary_unary_rpc_method_handler(
                    servicer.GetSyncedChipIds,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_chip__pb2.ChipIdsResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.chip.ChipService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ChipService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetChipMetadata(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.chip.ChipService/GetChipMetadata',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_chip__pb2.GetChipMetadataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetDownloadedChipIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.chip.ChipService/GetDownloadedChipIds',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_chip__pb2.ChipIdsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetSyncedChipIds(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.chip.ChipService/GetSyncedChipIds',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_chip__pb2.ChipIdsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

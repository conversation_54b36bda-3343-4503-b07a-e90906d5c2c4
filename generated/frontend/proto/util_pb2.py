# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/util.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/util.proto',
  package='carbon.frontend.util',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19\x66rontend/proto/util.proto\x12\x14\x63\x61rbon.frontend.util\"\x07\n\x05\x45mpty\"!\n\tTimestamp\x12\x14\n\x0ctimestamp_ms\x18\x01 \x01(\x03\"\xa7\x01\n\x0c\x46\x65\x61tureFlags\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12<\n\x05\x66lags\x18\x02 \x03(\x0b\x32-.carbon.frontend.util.FeatureFlags.FlagsEntry\x1a,\n\nFlagsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\x42\x10Z\x0eproto/frontendb\x06proto3'
)




_EMPTY = _descriptor.Descriptor(
  name='Empty',
  full_name='carbon.frontend.util.Empty',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=51,
  serialized_end=58,
)


_TIMESTAMP = _descriptor.Descriptor(
  name='Timestamp',
  full_name='carbon.frontend.util.Timestamp',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.frontend.util.Timestamp.timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=60,
  serialized_end=93,
)


_FEATUREFLAGS_FLAGSENTRY = _descriptor.Descriptor(
  name='FlagsEntry',
  full_name='carbon.frontend.util.FeatureFlags.FlagsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.util.FeatureFlags.FlagsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.util.FeatureFlags.FlagsEntry.value', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=219,
  serialized_end=263,
)

_FEATUREFLAGS = _descriptor.Descriptor(
  name='FeatureFlags',
  full_name='carbon.frontend.util.FeatureFlags',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.util.FeatureFlags.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='flags', full_name='carbon.frontend.util.FeatureFlags.flags', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_FEATUREFLAGS_FLAGSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=96,
  serialized_end=263,
)

_FEATUREFLAGS_FLAGSENTRY.containing_type = _FEATUREFLAGS
_FEATUREFLAGS.fields_by_name['ts'].message_type = _TIMESTAMP
_FEATUREFLAGS.fields_by_name['flags'].message_type = _FEATUREFLAGS_FLAGSENTRY
DESCRIPTOR.message_types_by_name['Empty'] = _EMPTY
DESCRIPTOR.message_types_by_name['Timestamp'] = _TIMESTAMP
DESCRIPTOR.message_types_by_name['FeatureFlags'] = _FEATUREFLAGS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Empty = _reflection.GeneratedProtocolMessageType('Empty', (_message.Message,), {
  'DESCRIPTOR' : _EMPTY,
  '__module__' : 'frontend.proto.util_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.util.Empty)
  })
_sym_db.RegisterMessage(Empty)

Timestamp = _reflection.GeneratedProtocolMessageType('Timestamp', (_message.Message,), {
  'DESCRIPTOR' : _TIMESTAMP,
  '__module__' : 'frontend.proto.util_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.util.Timestamp)
  })
_sym_db.RegisterMessage(Timestamp)

FeatureFlags = _reflection.GeneratedProtocolMessageType('FeatureFlags', (_message.Message,), {

  'FlagsEntry' : _reflection.GeneratedProtocolMessageType('FlagsEntry', (_message.Message,), {
    'DESCRIPTOR' : _FEATUREFLAGS_FLAGSENTRY,
    '__module__' : 'frontend.proto.util_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.util.FeatureFlags.FlagsEntry)
    })
  ,
  'DESCRIPTOR' : _FEATUREFLAGS,
  '__module__' : 'frontend.proto.util_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.util.FeatureFlags)
  })
_sym_db.RegisterMessage(FeatureFlags)
_sym_db.RegisterMessage(FeatureFlags.FlagsEntry)


DESCRIPTOR._options = None
_FEATUREFLAGS_FLAGSENTRY._options = None
# @@protoc_insertion_point(module_scope)

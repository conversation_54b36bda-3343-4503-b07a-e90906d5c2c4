// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/features.proto

#include "frontend/proto/features.pb.h"
#include "frontend/proto/features.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace features {

static const char* FeatureService_method_names[] = {
  "/carbon.frontend.features.FeatureService/GetNextFeatureFlags",
  "/carbon.frontend.features.FeatureService/GetRobotConfiguration",
};

std::unique_ptr< FeatureService::Stub> FeatureService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< FeatureService::Stub> stub(new FeatureService::Stub(channel, options));
  return stub;
}

FeatureService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextFeatureFlags_(FeatureService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetRobotConfiguration_(FeatureService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status FeatureService::Stub::GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::util::FeatureFlags* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::util::FeatureFlags, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextFeatureFlags_, context, request, response);
}

void FeatureService::Stub::async::GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::util::FeatureFlags, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextFeatureFlags_, context, request, response, std::move(f));
}

void FeatureService::Stub::async::GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextFeatureFlags_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::FeatureFlags>* FeatureService::Stub::PrepareAsyncGetNextFeatureFlagsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::FeatureFlags, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextFeatureFlags_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::FeatureFlags>* FeatureService::Stub::AsyncGetNextFeatureFlagsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextFeatureFlagsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status FeatureService::Stub::GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::features::RobotConfiguration* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::features::RobotConfiguration, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRobotConfiguration_, context, request, response);
}

void FeatureService::Stub::async::GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::features::RobotConfiguration, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRobotConfiguration_, context, request, response, std::move(f));
}

void FeatureService::Stub::async::GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRobotConfiguration_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::RobotConfiguration>* FeatureService::Stub::PrepareAsyncGetRobotConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::features::RobotConfiguration, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRobotConfiguration_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::RobotConfiguration>* FeatureService::Stub::AsyncGetRobotConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRobotConfigurationRaw(context, request, cq);
  result->StartCall();
  return result;
}

FeatureService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FeatureService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FeatureService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::util::FeatureFlags, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FeatureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::util::FeatureFlags* resp) {
               return service->GetNextFeatureFlags(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      FeatureService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< FeatureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::features::RobotConfiguration, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](FeatureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::features::RobotConfiguration* resp) {
               return service->GetRobotConfiguration(ctx, req, resp);
             }, this)));
}

FeatureService::Service::~Service() {
}

::grpc::Status FeatureService::Service::GetNextFeatureFlags(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status FeatureService::Service::GetRobotConfiguration(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace features


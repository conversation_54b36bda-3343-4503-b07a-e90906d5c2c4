"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
    overload as typing___overload,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

UploadMethodValue = typing___NewType('UploadMethodValue', builtin___int)
type___UploadMethodValue = UploadMethodValue
UploadMethod: _UploadMethod
class _UploadMethod(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[UploadMethodValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    WIRELESS = typing___cast(UploadMethodValue, 0)
    USB = typing___cast(UploadMethodValue, 1)
WIRELESS = typing___cast(UploadMethodValue, 0)
USB = typing___cast(UploadMethodValue, 1)

ProcedureStepValue = typing___NewType('ProcedureStepValue', builtin___int)
type___ProcedureStepValue = ProcedureStepValue
ProcedureStep: _ProcedureStep
class _ProcedureStep(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ProcedureStepValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NEW = typing___cast(ProcedureStepValue, 0)
    CAPTURING = typing___cast(ProcedureStepValue, 1)
    CAPTURE_PAUSED = typing___cast(ProcedureStepValue, 2)
    CAPTURE_COMPLETE = typing___cast(ProcedureStepValue, 3)
    UPLOADING_WIRELESS = typing___cast(ProcedureStepValue, 4)
    UPLOADING_WIRELESS_PAUSED = typing___cast(ProcedureStepValue, 5)
    UPLOADING_USB = typing___cast(ProcedureStepValue, 6)
    UPLOADING_USB_PAUSED = typing___cast(ProcedureStepValue, 7)
    UPLOADING_COMPLETE = typing___cast(ProcedureStepValue, 8)
NEW = typing___cast(ProcedureStepValue, 0)
CAPTURING = typing___cast(ProcedureStepValue, 1)
CAPTURE_PAUSED = typing___cast(ProcedureStepValue, 2)
CAPTURE_COMPLETE = typing___cast(ProcedureStepValue, 3)
UPLOADING_WIRELESS = typing___cast(ProcedureStepValue, 4)
UPLOADING_WIRELESS_PAUSED = typing___cast(ProcedureStepValue, 5)
UPLOADING_USB = typing___cast(ProcedureStepValue, 6)
UPLOADING_USB_PAUSED = typing___cast(ProcedureStepValue, 7)
UPLOADING_COMPLETE = typing___cast(ProcedureStepValue, 8)

class DataCaptureRate(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    rate: builtin___float = ...

    def __init__(self,
        *,
        rate : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"rate",b"rate"]) -> None: ...
type___DataCaptureRate = DataCaptureRate

class DataCaptureState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    images_taken: builtin___int = ...
    target_images_taken: builtin___int = ...
    estimated_capture_remaining_time_ms: builtin___int = ...
    images_uploaded: builtin___int = ...
    target_images_uploaded: builtin___int = ...
    estimated_upload_remaining_time_ms: builtin___int = ...
    wireless_upload_available: builtin___bool = ...
    usb_storage_connected: builtin___bool = ...
    capture_status: typing___Text = ...
    upload_status: typing___Text = ...
    session_name: typing___Text = ...
    step: type___ProcedureStepValue = ...
    crop: typing___Text = ...
    error_message: typing___Text = ...
    crop_id: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def rate(self) -> type___DataCaptureRate: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        images_taken : typing___Optional[builtin___int] = None,
        target_images_taken : typing___Optional[builtin___int] = None,
        estimated_capture_remaining_time_ms : typing___Optional[builtin___int] = None,
        images_uploaded : typing___Optional[builtin___int] = None,
        target_images_uploaded : typing___Optional[builtin___int] = None,
        estimated_upload_remaining_time_ms : typing___Optional[builtin___int] = None,
        rate : typing___Optional[type___DataCaptureRate] = None,
        wireless_upload_available : typing___Optional[builtin___bool] = None,
        usb_storage_connected : typing___Optional[builtin___bool] = None,
        capture_status : typing___Optional[typing___Text] = None,
        upload_status : typing___Optional[typing___Text] = None,
        session_name : typing___Optional[typing___Text] = None,
        step : typing___Optional[type___ProcedureStepValue] = None,
        crop : typing___Optional[typing___Text] = None,
        error_message : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"rate",b"rate",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"capture_status",b"capture_status",u"crop",b"crop",u"crop_id",b"crop_id",u"error_message",b"error_message",u"estimated_capture_remaining_time_ms",b"estimated_capture_remaining_time_ms",u"estimated_upload_remaining_time_ms",b"estimated_upload_remaining_time_ms",u"images_taken",b"images_taken",u"images_uploaded",b"images_uploaded",u"rate",b"rate",u"session_name",b"session_name",u"step",b"step",u"target_images_taken",b"target_images_taken",u"target_images_uploaded",b"target_images_uploaded",u"ts",b"ts",u"upload_status",b"upload_status",u"usb_storage_connected",b"usb_storage_connected",u"wireless_upload_available",b"wireless_upload_available"]) -> None: ...
type___DataCaptureState = DataCaptureState

class DataCaptureSession(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___DataCaptureSession = DataCaptureSession

class StartDataCaptureRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    rate: builtin___float = ...
    crop: typing___Text = ...
    crop_id: typing___Text = ...
    snap_capture: builtin___bool = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        rate : typing___Optional[builtin___float] = None,
        crop : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        snap_capture : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop",b"crop",u"crop_id",b"crop_id",u"name",b"name",u"rate",b"rate",u"snap_capture",b"snap_capture"]) -> None: ...
type___StartDataCaptureRequest = StartDataCaptureRequest

class SnapImagesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    crop: typing___Text = ...
    crop_id: typing___Text = ...
    cam_id: typing___Text = ...
    timestamp_ms: builtin___int = ...
    session_name: typing___Text = ...

    def __init__(self,
        *,
        crop : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        cam_id : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        session_name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"_cam_id",b"_cam_id",u"_timestamp_ms",b"_timestamp_ms",u"cam_id",b"cam_id",u"timestamp_ms",b"timestamp_ms"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"_cam_id",b"_cam_id",u"_timestamp_ms",b"_timestamp_ms",u"cam_id",b"cam_id",u"crop",b"crop",u"crop_id",b"crop_id",u"session_name",b"session_name",u"timestamp_ms",b"timestamp_ms"]) -> None: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_cam_id",b"_cam_id"]) -> typing_extensions___Literal["cam_id"]: ...
    @typing___overload
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"_timestamp_ms",b"_timestamp_ms"]) -> typing_extensions___Literal["timestamp_ms"]: ...
type___SnapImagesRequest = SnapImagesRequest

class Session(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    images_remaining: builtin___int = ...
    is_uploading: builtin___bool = ...
    has_completed: builtin___bool = ...
    is_capturing: builtin___bool = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        images_remaining : typing___Optional[builtin___int] = None,
        is_uploading : typing___Optional[builtin___bool] = None,
        has_completed : typing___Optional[builtin___bool] = None,
        is_capturing : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"has_completed",b"has_completed",u"images_remaining",b"images_remaining",u"is_capturing",b"is_capturing",u"is_uploading",b"is_uploading",u"name",b"name"]) -> None: ...
type___Session = Session

class AvailableSessionResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def sessions(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___Session]: ...

    def __init__(self,
        *,
        sessions : typing___Optional[typing___Iterable[type___Session]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"sessions",b"sessions"]) -> None: ...
type___AvailableSessionResponse = AvailableSessionResponse

class SessionName(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name"]) -> None: ...
type___SessionName = SessionName

class RegularCaptureStatus(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uploaded: builtin___int = ...
    budget: builtin___int = ...
    last_upload_timestamp: builtin___int = ...

    def __init__(self,
        *,
        uploaded : typing___Optional[builtin___int] = None,
        budget : typing___Optional[builtin___int] = None,
        last_upload_timestamp : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"budget",b"budget",u"last_upload_timestamp",b"last_upload_timestamp",u"uploaded",b"uploaded"]) -> None: ...
type___RegularCaptureStatus = RegularCaptureStatus

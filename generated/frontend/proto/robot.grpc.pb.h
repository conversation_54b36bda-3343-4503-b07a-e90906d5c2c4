// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/robot.proto
#ifndef GRPC_frontend_2fproto_2frobot_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2frobot_2eproto__INCLUDED

#include "frontend/proto/robot.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace robot {

class RobotDiagnosticService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.robot.RobotDiagnosticService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::carbon::frontend::robot::TimestampedRobotState* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::robot::TimestampedRobotState>> AsyncGetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::robot::TimestampedRobotState>>(AsyncGetNextRobotStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::robot::TimestampedRobotState>> PrepareAsyncGetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::robot::TimestampedRobotState>>(PrepareAsyncGetNextRobotStateRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::robot::TimestampedRobotState>* AsyncGetNextRobotStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::robot::TimestampedRobotState>* PrepareAsyncGetNextRobotStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::carbon::frontend::robot::TimestampedRobotState* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::robot::TimestampedRobotState>> AsyncGetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::robot::TimestampedRobotState>>(AsyncGetNextRobotStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::robot::TimestampedRobotState>> PrepareAsyncGetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::robot::TimestampedRobotState>>(PrepareAsyncGetNextRobotStateRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response, std::function<void(::grpc::Status)>) override;
      void GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::robot::TimestampedRobotState>* AsyncGetNextRobotStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::robot::TimestampedRobotState>* PrepareAsyncGetNextRobotStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextRobotState_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextRobotState(::grpc::ServerContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextRobotState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextRobotState() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextRobotState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRobotState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::robot::RobotStateRequest* /*request*/, ::carbon::frontend::robot::TimestampedRobotState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextRobotState(::grpc::ServerContext* context, ::carbon::frontend::robot::RobotStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::robot::TimestampedRobotState>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextRobotState<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextRobotState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextRobotState() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::robot::RobotStateRequest, ::carbon::frontend::robot::TimestampedRobotState>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response) { return this->GetNextRobotState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextRobotState(
        ::grpc::MessageAllocator< ::carbon::frontend::robot::RobotStateRequest, ::carbon::frontend::robot::TimestampedRobotState>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::robot::RobotStateRequest, ::carbon::frontend::robot::TimestampedRobotState>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextRobotState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRobotState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::robot::RobotStateRequest* /*request*/, ::carbon::frontend::robot::TimestampedRobotState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextRobotState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::robot::RobotStateRequest* /*request*/, ::carbon::frontend::robot::TimestampedRobotState* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextRobotState<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextRobotState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextRobotState() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextRobotState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRobotState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::robot::RobotStateRequest* /*request*/, ::carbon::frontend::robot::TimestampedRobotState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextRobotState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextRobotState() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextRobotState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRobotState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::robot::RobotStateRequest* /*request*/, ::carbon::frontend::robot::TimestampedRobotState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextRobotState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextRobotState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextRobotState() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextRobotState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextRobotState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextRobotState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::robot::RobotStateRequest* /*request*/, ::carbon::frontend::robot::TimestampedRobotState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextRobotState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextRobotState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextRobotState() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::robot::RobotStateRequest, ::carbon::frontend::robot::TimestampedRobotState>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::robot::RobotStateRequest, ::carbon::frontend::robot::TimestampedRobotState>* streamer) {
                       return this->StreamedGetNextRobotState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextRobotState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextRobotState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::robot::RobotStateRequest* /*request*/, ::carbon::frontend::robot::TimestampedRobotState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextRobotState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::robot::RobotStateRequest,::carbon::frontend::robot::TimestampedRobotState>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextRobotState<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextRobotState<Service > StreamedService;
};

}  // namespace robot
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2frobot_2eproto__INCLUDED

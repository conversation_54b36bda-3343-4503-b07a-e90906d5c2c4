"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.category.proto.category_pb2 import (
    CategoryCollection as category___proto___category_pb2___CategoryCollection,
)

from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GetNextCategoryCollectionsDataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___GetNextCategoryCollectionsDataRequest = GetNextCategoryCollectionsDataRequest

class GetNextCategoryCollectionsDataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def category_collections(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[category___proto___category_pb2___CategoryCollection]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        category_collections : typing___Optional[typing___Iterable[category___proto___category_pb2___CategoryCollection]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"category_collections",b"category_collections",u"ts",b"ts"]) -> None: ...
type___GetNextCategoryCollectionsDataResponse = GetNextCategoryCollectionsDataResponse

class GetNextActiveCategoryCollectionIdRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___GetNextActiveCategoryCollectionIdRequest = GetNextActiveCategoryCollectionIdRequest

class GetNextActiveCategoryCollectionIdResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uuid: typing___Text = ...
    reload_required: builtin___bool = ...
    last_updated_timestamp_ms: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        uuid : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        reload_required : typing___Optional[builtin___bool] = None,
        last_updated_timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"last_updated_timestamp_ms",b"last_updated_timestamp_ms",u"reload_required",b"reload_required",u"ts",b"ts",u"uuid",b"uuid"]) -> None: ...
type___GetNextActiveCategoryCollectionIdResponse = GetNextActiveCategoryCollectionIdResponse

class SetActiveCategoryCollectionIdRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uuid: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        uuid : typing___Optional[typing___Text] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts",u"uuid",b"uuid"]) -> None: ...
type___SetActiveCategoryCollectionIdRequest = SetActiveCategoryCollectionIdRequest

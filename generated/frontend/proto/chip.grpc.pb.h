// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/chip.proto
#ifndef GRPC_frontend_2fproto_2fchip_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fchip_2eproto__INCLUDED

#include "frontend/proto/chip.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace chip {

class ChipService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.chip.ChipService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::GetChipMetadataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::GetChipMetadataResponse>> AsyncGetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::GetChipMetadataResponse>>(AsyncGetChipMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::GetChipMetadataResponse>> PrepareAsyncGetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::GetChipMetadataResponse>>(PrepareAsyncGetChipMetadataRaw(context, request, cq));
    }
    virtual ::grpc::Status GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::ChipIdsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>> AsyncGetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>>(AsyncGetDownloadedChipIdsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>> PrepareAsyncGetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>>(PrepareAsyncGetDownloadedChipIdsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::ChipIdsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>> AsyncGetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>>(AsyncGetSyncedChipIdsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>> PrepareAsyncGetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>>(PrepareAsyncGetSyncedChipIdsRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::GetChipMetadataResponse>* AsyncGetChipMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::GetChipMetadataResponse>* PrepareAsyncGetChipMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>* AsyncGetDownloadedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>* PrepareAsyncGetDownloadedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>* AsyncGetSyncedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::chip::ChipIdsResponse>* PrepareAsyncGetSyncedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::GetChipMetadataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::GetChipMetadataResponse>> AsyncGetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::GetChipMetadataResponse>>(AsyncGetChipMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::GetChipMetadataResponse>> PrepareAsyncGetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::GetChipMetadataResponse>>(PrepareAsyncGetChipMetadataRaw(context, request, cq));
    }
    ::grpc::Status GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::ChipIdsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>> AsyncGetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>>(AsyncGetDownloadedChipIdsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>> PrepareAsyncGetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>>(PrepareAsyncGetDownloadedChipIdsRaw(context, request, cq));
    }
    ::grpc::Status GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::ChipIdsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>> AsyncGetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>>(AsyncGetSyncedChipIdsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>> PrepareAsyncGetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>>(PrepareAsyncGetSyncedChipIdsRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::GetChipMetadataResponse>* AsyncGetChipMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::GetChipMetadataResponse>* PrepareAsyncGetChipMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>* AsyncGetDownloadedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>* PrepareAsyncGetDownloadedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>* AsyncGetSyncedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>* PrepareAsyncGetSyncedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetChipMetadata_;
    const ::grpc::internal::RpcMethod rpcmethod_GetDownloadedChipIds_;
    const ::grpc::internal::RpcMethod rpcmethod_GetSyncedChipIds_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetChipMetadata(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response);
    virtual ::grpc::Status GetDownloadedChipIds(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response);
    virtual ::grpc::Status GetSyncedChipIds(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetChipMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetChipMetadata() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetChipMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::GetChipMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetChipMetadata(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::chip::GetChipMetadataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetDownloadedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetDownloadedChipIds() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetDownloadedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDownloadedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDownloadedChipIds(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::chip::ChipIdsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetSyncedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetSyncedChipIds() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetSyncedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSyncedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSyncedChipIds(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::chip::ChipIdsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetChipMetadata<WithAsyncMethod_GetDownloadedChipIds<WithAsyncMethod_GetSyncedChipIds<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetChipMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetChipMetadata() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::GetChipMetadataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response) { return this->GetChipMetadata(context, request, response); }));}
    void SetMessageAllocatorFor_GetChipMetadata(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::GetChipMetadataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::GetChipMetadataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetChipMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::GetChipMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetChipMetadata(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::GetChipMetadataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetDownloadedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetDownloadedChipIds() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response) { return this->GetDownloadedChipIds(context, request, response); }));}
    void SetMessageAllocatorFor_GetDownloadedChipIds(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetDownloadedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDownloadedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDownloadedChipIds(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetSyncedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetSyncedChipIds() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response) { return this->GetSyncedChipIds(context, request, response); }));}
    void SetMessageAllocatorFor_GetSyncedChipIds(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetSyncedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSyncedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSyncedChipIds(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetChipMetadata<WithCallbackMethod_GetDownloadedChipIds<WithCallbackMethod_GetSyncedChipIds<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetChipMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetChipMetadata() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetChipMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::GetChipMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetDownloadedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetDownloadedChipIds() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetDownloadedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDownloadedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetSyncedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetSyncedChipIds() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetSyncedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSyncedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetChipMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetChipMetadata() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetChipMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::GetChipMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetChipMetadata(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetDownloadedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetDownloadedChipIds() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetDownloadedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDownloadedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDownloadedChipIds(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetSyncedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetSyncedChipIds() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetSyncedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSyncedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetSyncedChipIds(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetChipMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetChipMetadata() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetChipMetadata(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetChipMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetChipMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::GetChipMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetChipMetadata(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetDownloadedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetDownloadedChipIds() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetDownloadedChipIds(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetDownloadedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDownloadedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDownloadedChipIds(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetSyncedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetSyncedChipIds() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetSyncedChipIds(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetSyncedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetSyncedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetSyncedChipIds(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetChipMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetChipMetadata() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::chip::GetChipMetadataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::chip::GetChipMetadataResponse>* streamer) {
                       return this->StreamedGetChipMetadata(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetChipMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetChipMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::GetChipMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetChipMetadata(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::chip::GetChipMetadataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetDownloadedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetDownloadedChipIds() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>* streamer) {
                       return this->StreamedGetDownloadedChipIds(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetDownloadedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetDownloadedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetDownloadedChipIds(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::chip::ChipIdsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetSyncedChipIds : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetSyncedChipIds() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse>* streamer) {
                       return this->StreamedGetSyncedChipIds(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetSyncedChipIds() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetSyncedChipIds(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::chip::ChipIdsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetSyncedChipIds(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::chip::ChipIdsResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetChipMetadata<WithStreamedUnaryMethod_GetDownloadedChipIds<WithStreamedUnaryMethod_GetSyncedChipIds<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetChipMetadata<WithStreamedUnaryMethod_GetDownloadedChipIds<WithStreamedUnaryMethod_GetSyncedChipIds<Service > > > StreamedService;
};

}  // namespace chip
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fchip_2eproto__INCLUDED

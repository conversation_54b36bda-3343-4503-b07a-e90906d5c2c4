"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
    ScalarMap as google___protobuf___internal___containers___ScalarMap,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

SoftwareProcessStageValue = typing___NewType('SoftwareProcessStageValue', builtin___int)
type___SoftwareProcessStageValue = SoftwareProcessStageValue
SoftwareProcessStage: _SoftwareProcessStage
class _SoftwareProcessStage(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[SoftwareProcessStageValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    SPS_UNKNOWN = typing___cast(SoftwareProcessStageValue, 0)
    SPS_ERROR = typing___cast(SoftwareProcessStageValue, 1)
    SPS_BOOTING = typing___cast(SoftwareProcessStageValue, 2)
    SPS_READY = typing___cast(SoftwareProcessStageValue, 3)
SPS_UNKNOWN = typing___cast(SoftwareProcessStageValue, 0)
SPS_ERROR = typing___cast(SoftwareProcessStageValue, 1)
SPS_BOOTING = typing___cast(SoftwareProcessStageValue, 2)
SPS_READY = typing___cast(SoftwareProcessStageValue, 3)

BootStageValue = typing___NewType('BootStageValue', builtin___int)
type___BootStageValue = BootStageValue
BootStage: _BootStage
class _BootStage(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[BootStageValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    BS_UPDATE_INSTALLING = typing___cast(BootStageValue, 0)
    BS_POWERED_DOWN = typing___cast(BootStageValue, 1)
    BS_POWERING_UP = typing___cast(BootStageValue, 2)
    BS_SOFTWARE_LOADING = typing___cast(BootStageValue, 3)
    BS_BOOT_FAILURE = typing___cast(BootStageValue, 4)
    BS_MODEL_LOADING = typing___cast(BootStageValue, 5)
    BS_CRITICAL_FAILURE = typing___cast(BootStageValue, 6)
    BS_LIFTED = typing___cast(BootStageValue, 7)
    BS_ESTOPPED = typing___cast(BootStageValue, 8)
    BS_WEEDING = typing___cast(BootStageValue, 9)
    BS_STANDBY = typing___cast(BootStageValue, 10)
BS_UPDATE_INSTALLING = typing___cast(BootStageValue, 0)
BS_POWERED_DOWN = typing___cast(BootStageValue, 1)
BS_POWERING_UP = typing___cast(BootStageValue, 2)
BS_SOFTWARE_LOADING = typing___cast(BootStageValue, 3)
BS_BOOT_FAILURE = typing___cast(BootStageValue, 4)
BS_MODEL_LOADING = typing___cast(BootStageValue, 5)
BS_CRITICAL_FAILURE = typing___cast(BootStageValue, 6)
BS_LIFTED = typing___cast(BootStageValue, 7)
BS_ESTOPPED = typing___cast(BootStageValue, 8)
BS_WEEDING = typing___cast(BootStageValue, 9)
BS_STANDBY = typing___cast(BootStageValue, 10)

class BoardState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    board_rev: typing___Text = ...
    connected: builtin___bool = ...
    booted: builtin___bool = ...
    error: builtin___bool = ...
    timestamp: builtin___int = ...
    firmware_version: typing___Text = ...

    def __init__(self,
        *,
        board_rev : typing___Optional[typing___Text] = None,
        connected : typing___Optional[builtin___bool] = None,
        booted : typing___Optional[builtin___bool] = None,
        error : typing___Optional[builtin___bool] = None,
        timestamp : typing___Optional[builtin___int] = None,
        firmware_version : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"board_rev",b"board_rev",u"booted",b"booted",u"connected",b"connected",u"error",b"error",u"firmware_version",b"firmware_version",u"timestamp",b"timestamp"]) -> None: ...
type___BoardState = BoardState

class GPSCoord(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    latitude: builtin___float = ...
    longitude: builtin___float = ...
    altitude_mm: builtin___float = ...
    ecef_x: builtin___float = ...
    ecef_y: builtin___float = ...
    ecef_z: builtin___float = ...

    def __init__(self,
        *,
        latitude : typing___Optional[builtin___float] = None,
        longitude : typing___Optional[builtin___float] = None,
        altitude_mm : typing___Optional[builtin___float] = None,
        ecef_x : typing___Optional[builtin___float] = None,
        ecef_y : typing___Optional[builtin___float] = None,
        ecef_z : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"altitude_mm",b"altitude_mm",u"ecef_x",b"ecef_x",u"ecef_y",b"ecef_y",u"ecef_z",b"ecef_z",u"latitude",b"latitude",u"longitude",b"longitude"]) -> None: ...
type___GPSCoord = GPSCoord

class GPSBoardState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    have_fix: builtin___bool = ...
    have_approx_fix: builtin___bool = ...
    num_sats: builtin___int = ...
    hdop: builtin___float = ...

    @property
    def board(self) -> type___BoardState: ...

    @property
    def position(self) -> type___GPSCoord: ...

    def __init__(self,
        *,
        board : typing___Optional[type___BoardState] = None,
        have_fix : typing___Optional[builtin___bool] = None,
        have_approx_fix : typing___Optional[builtin___bool] = None,
        num_sats : typing___Optional[builtin___int] = None,
        hdop : typing___Optional[builtin___float] = None,
        position : typing___Optional[type___GPSCoord] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"board",b"board",u"position",b"position"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"board",b"board",u"have_approx_fix",b"have_approx_fix",u"have_fix",b"have_fix",u"hdop",b"hdop",u"num_sats",b"num_sats",u"position",b"position"]) -> None: ...
type___GPSBoardState = GPSBoardState

class WheelState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    last_tick_position: builtin___int = ...
    vel_mph: builtin___float = ...
    filtered: builtin___bool = ...
    enabled: builtin___bool = ...
    diameter_in: builtin___float = ...

    def __init__(self,
        *,
        last_tick_position : typing___Optional[builtin___int] = None,
        vel_mph : typing___Optional[builtin___float] = None,
        filtered : typing___Optional[builtin___bool] = None,
        enabled : typing___Optional[builtin___bool] = None,
        diameter_in : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"diameter_in",b"diameter_in",u"enabled",b"enabled",u"filtered",b"filtered",u"last_tick_position",b"last_tick_position",u"vel_mph",b"vel_mph"]) -> None: ...
type___WheelState = WheelState

class WheelEncoderBoardState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def board(self) -> type___BoardState: ...

    @property
    def wheels(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___WheelState]: ...

    def __init__(self,
        *,
        board : typing___Optional[type___BoardState] = None,
        wheels : typing___Optional[typing___Iterable[type___WheelState]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"board",b"board"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"board",b"board",u"wheels",b"wheels"]) -> None: ...
type___WheelEncoderBoardState = WheelEncoderBoardState

class StrobeBoardState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    exposure_us: builtin___int = ...
    period_us: builtin___int = ...
    targets_per_predict_ratio: builtin___int = ...

    @property
    def board(self) -> type___BoardState: ...

    def __init__(self,
        *,
        board : typing___Optional[type___BoardState] = None,
        exposure_us : typing___Optional[builtin___int] = None,
        period_us : typing___Optional[builtin___int] = None,
        targets_per_predict_ratio : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"board",b"board"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"board",b"board",u"exposure_us",b"exposure_us",u"period_us",b"period_us",u"targets_per_predict_ratio",b"targets_per_predict_ratio"]) -> None: ...
type___StrobeBoardState = StrobeBoardState

class ISOBusState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ISOBusState = ISOBusState

class ChillerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___ChillerState = ChillerState

class AirConditionerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___AirConditionerState = AirConditionerState

class USBDriveState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    connected: builtin___bool = ...
    utilization_pct: builtin___float = ...

    def __init__(self,
        *,
        connected : typing___Optional[builtin___bool] = None,
        utilization_pct : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"connected",b"connected",u"utilization_pct",b"utilization_pct"]) -> None: ...
type___USBDriveState = USBDriveState

class SupervisoryPLCState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ac_frequency: builtin___float = ...
    ac_voltage_a: builtin___float = ...
    ac_voltage_a_b: builtin___float = ...
    ac_voltage_a_c: builtin___float = ...
    ac_voltage_b: builtin___float = ...
    ac_voltage_b_c: builtin___float = ...
    ac_voltage_c: builtin___float = ...
    phase_power_va_3: builtin___float = ...
    phase_power_w_3: builtin___float = ...
    power_factor: builtin___float = ...
    power_bad: builtin___bool = ...
    power_good: builtin___bool = ...
    power_very_bad: builtin___bool = ...
    battery_voltage_12v: builtin___float = ...
    air_conditioner_disabled: builtin___bool = ...
    chiller_disabled: builtin___bool = ...
    gps_disabled: builtin___bool = ...
    strobe_disabled: builtin___bool = ...
    wheel_encoder_disabled: builtin___bool = ...
    btl_disabled: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    server_disabled: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    scanners_disabled: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    main_contactor_disabled: builtin___bool = ...
    main_contactor_status_fb: builtin___bool = ...
    server_cabinet_humidity: builtin___float = ...
    server_cabinet_temp: builtin___float = ...
    humidity_bypassed: builtin___bool = ...
    temp_bypassed: builtin___bool = ...
    humidity_status: builtin___bool = ...
    temp_status: builtin___bool = ...
    temp_humidity_status: builtin___bool = ...
    lifted_status: builtin___bool = ...
    tractor_power: builtin___bool = ...
    water_protect_status: builtin___bool = ...

    def __init__(self,
        *,
        ac_frequency : typing___Optional[builtin___float] = None,
        ac_voltage_a : typing___Optional[builtin___float] = None,
        ac_voltage_a_b : typing___Optional[builtin___float] = None,
        ac_voltage_a_c : typing___Optional[builtin___float] = None,
        ac_voltage_b : typing___Optional[builtin___float] = None,
        ac_voltage_b_c : typing___Optional[builtin___float] = None,
        ac_voltage_c : typing___Optional[builtin___float] = None,
        phase_power_va_3 : typing___Optional[builtin___float] = None,
        phase_power_w_3 : typing___Optional[builtin___float] = None,
        power_factor : typing___Optional[builtin___float] = None,
        power_bad : typing___Optional[builtin___bool] = None,
        power_good : typing___Optional[builtin___bool] = None,
        power_very_bad : typing___Optional[builtin___bool] = None,
        battery_voltage_12v : typing___Optional[builtin___float] = None,
        air_conditioner_disabled : typing___Optional[builtin___bool] = None,
        chiller_disabled : typing___Optional[builtin___bool] = None,
        gps_disabled : typing___Optional[builtin___bool] = None,
        strobe_disabled : typing___Optional[builtin___bool] = None,
        wheel_encoder_disabled : typing___Optional[builtin___bool] = None,
        btl_disabled : typing___Optional[typing___Iterable[builtin___bool]] = None,
        server_disabled : typing___Optional[typing___Iterable[builtin___bool]] = None,
        scanners_disabled : typing___Optional[typing___Iterable[builtin___bool]] = None,
        main_contactor_disabled : typing___Optional[builtin___bool] = None,
        main_contactor_status_fb : typing___Optional[builtin___bool] = None,
        server_cabinet_humidity : typing___Optional[builtin___float] = None,
        server_cabinet_temp : typing___Optional[builtin___float] = None,
        humidity_bypassed : typing___Optional[builtin___bool] = None,
        temp_bypassed : typing___Optional[builtin___bool] = None,
        humidity_status : typing___Optional[builtin___bool] = None,
        temp_status : typing___Optional[builtin___bool] = None,
        temp_humidity_status : typing___Optional[builtin___bool] = None,
        lifted_status : typing___Optional[builtin___bool] = None,
        tractor_power : typing___Optional[builtin___bool] = None,
        water_protect_status : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ac_frequency",b"ac_frequency",u"ac_voltage_a",b"ac_voltage_a",u"ac_voltage_a_b",b"ac_voltage_a_b",u"ac_voltage_a_c",b"ac_voltage_a_c",u"ac_voltage_b",b"ac_voltage_b",u"ac_voltage_b_c",b"ac_voltage_b_c",u"ac_voltage_c",b"ac_voltage_c",u"air_conditioner_disabled",b"air_conditioner_disabled",u"battery_voltage_12v",b"battery_voltage_12v",u"btl_disabled",b"btl_disabled",u"chiller_disabled",b"chiller_disabled",u"gps_disabled",b"gps_disabled",u"humidity_bypassed",b"humidity_bypassed",u"humidity_status",b"humidity_status",u"lifted_status",b"lifted_status",u"main_contactor_disabled",b"main_contactor_disabled",u"main_contactor_status_fb",b"main_contactor_status_fb",u"phase_power_va_3",b"phase_power_va_3",u"phase_power_w_3",b"phase_power_w_3",u"power_bad",b"power_bad",u"power_factor",b"power_factor",u"power_good",b"power_good",u"power_very_bad",b"power_very_bad",u"scanners_disabled",b"scanners_disabled",u"server_cabinet_humidity",b"server_cabinet_humidity",u"server_cabinet_temp",b"server_cabinet_temp",u"server_disabled",b"server_disabled",u"strobe_disabled",b"strobe_disabled",u"temp_bypassed",b"temp_bypassed",u"temp_humidity_status",b"temp_humidity_status",u"temp_status",b"temp_status",u"tractor_power",b"tractor_power",u"water_protect_status",b"water_protect_status",u"wheel_encoder_disabled",b"wheel_encoder_disabled"]) -> None: ...
type___SupervisoryPLCState = SupervisoryPLCState

class SafetyPLCState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    estopped: builtin___bool = ...
    in_cab_estopped: builtin___bool = ...
    interlock: builtin___bool = ...
    laser_key: builtin___bool = ...
    left_estopped: builtin___bool = ...
    right_estopped: builtin___bool = ...
    lifted: builtin___bool = ...
    water_protect: builtin___bool = ...
    lift_sensor_bypassed: builtin___bool = ...

    def __init__(self,
        *,
        estopped : typing___Optional[builtin___bool] = None,
        in_cab_estopped : typing___Optional[builtin___bool] = None,
        interlock : typing___Optional[builtin___bool] = None,
        laser_key : typing___Optional[builtin___bool] = None,
        left_estopped : typing___Optional[builtin___bool] = None,
        right_estopped : typing___Optional[builtin___bool] = None,
        lifted : typing___Optional[builtin___bool] = None,
        water_protect : typing___Optional[builtin___bool] = None,
        lift_sensor_bypassed : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"estopped",b"estopped",u"in_cab_estopped",b"in_cab_estopped",u"interlock",b"interlock",u"laser_key",b"laser_key",u"left_estopped",b"left_estopped",u"lift_sensor_bypassed",b"lift_sensor_bypassed",u"lifted",b"lifted",u"right_estopped",b"right_estopped",u"water_protect",b"water_protect"]) -> None: ...
type___SafetyPLCState = SafetyPLCState

class NicState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    ptp_online: builtin___bool = ...
    ptp_offset: builtin___int = ...
    link_online: builtin___bool = ...
    link_correct: builtin___bool = ...
    link_speed: builtin___int = ...

    def __init__(self,
        *,
        ptp_online : typing___Optional[builtin___bool] = None,
        ptp_offset : typing___Optional[builtin___int] = None,
        link_online : typing___Optional[builtin___bool] = None,
        link_correct : typing___Optional[builtin___bool] = None,
        link_speed : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"link_correct",b"link_correct",u"link_online",b"link_online",u"link_speed",b"link_speed",u"ptp_offset",b"ptp_offset",u"ptp_online",b"ptp_online"]) -> None: ...
type___NicState = NicState

class StorageDriveState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    capacity: builtin___int = ...
    used: builtin___int = ...

    def __init__(self,
        *,
        capacity : typing___Optional[builtin___int] = None,
        used : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"capacity",b"capacity",u"used",b"used"]) -> None: ...
type___StorageDriveState = StorageDriveState

class GPUState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    online: builtin___bool = ...
    index: builtin___int = ...
    model: typing___Text = ...
    temperature_C: builtin___float = ...
    power_used: builtin___float = ...
    power_capacity: builtin___float = ...
    memory_used: builtin___float = ...
    memory_capacity: builtin___float = ...
    fan: builtin___float = ...
    gpu_util: builtin___float = ...

    def __init__(self,
        *,
        online : typing___Optional[builtin___bool] = None,
        index : typing___Optional[builtin___int] = None,
        model : typing___Optional[typing___Text] = None,
        temperature_C : typing___Optional[builtin___float] = None,
        power_used : typing___Optional[builtin___float] = None,
        power_capacity : typing___Optional[builtin___float] = None,
        memory_used : typing___Optional[builtin___float] = None,
        memory_capacity : typing___Optional[builtin___float] = None,
        fan : typing___Optional[builtin___float] = None,
        gpu_util : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"fan",b"fan",u"gpu_util",b"gpu_util",u"index",b"index",u"memory_capacity",b"memory_capacity",u"memory_used",b"memory_used",u"model",b"model",u"online",b"online",u"power_capacity",b"power_capacity",u"power_used",b"power_used",u"temperature_C",b"temperature_C"]) -> None: ...
type___GPUState = GPUState

class HostState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class NicsEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...

        @property
        def value(self) -> type___NicState: ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[type___NicState] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___NicsEntry = NicsEntry

    online: builtin___bool = ...
    ptp_checker_online: builtin___bool = ...
    ethernet_checker_online: builtin___bool = ...
    storage_checker_online: builtin___bool = ...
    gpu_checker_online: builtin___bool = ...

    @property
    def nics(self) -> google___protobuf___internal___containers___MessageMap[typing___Text, type___NicState]: ...

    @property
    def main_partition(self) -> type___StorageDriveState: ...

    @property
    def data_partition(self) -> type___StorageDriveState: ...

    @property
    def gpus(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___GPUState]: ...

    def __init__(self,
        *,
        online : typing___Optional[builtin___bool] = None,
        ptp_checker_online : typing___Optional[builtin___bool] = None,
        ethernet_checker_online : typing___Optional[builtin___bool] = None,
        storage_checker_online : typing___Optional[builtin___bool] = None,
        gpu_checker_online : typing___Optional[builtin___bool] = None,
        nics : typing___Optional[typing___Mapping[typing___Text, type___NicState]] = None,
        main_partition : typing___Optional[type___StorageDriveState] = None,
        data_partition : typing___Optional[type___StorageDriveState] = None,
        gpus : typing___Optional[typing___Iterable[type___GPUState]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"data_partition",b"data_partition",u"main_partition",b"main_partition"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"data_partition",b"data_partition",u"ethernet_checker_online",b"ethernet_checker_online",u"gpu_checker_online",b"gpu_checker_online",u"gpus",b"gpus",u"main_partition",b"main_partition",u"nics",b"nics",u"online",b"online",u"ptp_checker_online",b"ptp_checker_online",u"storage_checker_online",b"storage_checker_online"]) -> None: ...
type___HostState = HostState

class SoftwareUpdateVersionState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ImagesRequiredEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...
        value: builtin___bool = ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[builtin___bool] = None,
            ) -> None: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ImagesRequiredEntry = ImagesRequiredEntry

    tag: typing___Text = ...
    system: typing___Text = ...
    available: builtin___bool = ...
    ready: builtin___bool = ...

    @property
    def images_required(self) -> google___protobuf___internal___containers___ScalarMap[typing___Text, builtin___bool]: ...

    def __init__(self,
        *,
        tag : typing___Optional[typing___Text] = None,
        system : typing___Optional[typing___Text] = None,
        available : typing___Optional[builtin___bool] = None,
        ready : typing___Optional[builtin___bool] = None,
        images_required : typing___Optional[typing___Mapping[typing___Text, builtin___bool]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"available",b"available",u"images_required",b"images_required",u"ready",b"ready",u"system",b"system",u"tag",b"tag"]) -> None: ...
type___SoftwareUpdateVersionState = SoftwareUpdateVersionState

class OperatingSystemState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    partition: typing___Text = ...
    version: typing___Text = ...

    def __init__(self,
        *,
        partition : typing___Optional[typing___Text] = None,
        version : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"partition",b"partition",u"version",b"version"]) -> None: ...
type___OperatingSystemState = OperatingSystemState

class SystemVersionState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def current(self) -> type___OperatingSystemState: ...

    @property
    def other(self) -> type___OperatingSystemState: ...

    def __init__(self,
        *,
        current : typing___Optional[type___OperatingSystemState] = None,
        other : typing___Optional[type___OperatingSystemState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current",b"current",u"other",b"other"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"other",b"other"]) -> None: ...
type___SystemVersionState = SystemVersionState

class SoftwareVersionsState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def current(self) -> type___SoftwareUpdateVersionState: ...

    @property
    def previous(self) -> type___SoftwareUpdateVersionState: ...

    @property
    def target(self) -> type___SoftwareUpdateVersionState: ...

    @property
    def system(self) -> type___SystemVersionState: ...

    def __init__(self,
        *,
        current : typing___Optional[type___SoftwareUpdateVersionState] = None,
        previous : typing___Optional[type___SoftwareUpdateVersionState] = None,
        target : typing___Optional[type___SoftwareUpdateVersionState] = None,
        system : typing___Optional[type___SystemVersionState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"current",b"current",u"previous",b"previous",u"system",b"system",u"target",b"target"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current",b"current",u"previous",b"previous",u"system",b"system",u"target",b"target"]) -> None: ...
type___SoftwareVersionsState = SoftwareVersionsState

class SoftwareProcessState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    stage: type___SoftwareProcessStageValue = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        stage : typing___Optional[type___SoftwareProcessStageValue] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"stage",b"stage"]) -> None: ...
type___SoftwareProcessState = SoftwareProcessState

class SoftwareState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ProcessesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...

        @property
        def value(self) -> type___SoftwareProcessState: ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[type___SoftwareProcessState] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ProcessesEntry = ProcessesEntry

    restarting: builtin___bool = ...
    last_restart_time_ms: builtin___int = ...
    updating: builtin___bool = ...
    last_update_time_ms: builtin___int = ...

    @property
    def processes(self) -> google___protobuf___internal___containers___MessageMap[typing___Text, type___SoftwareProcessState]: ...

    @property
    def versions(self) -> type___SoftwareVersionsState: ...

    def __init__(self,
        *,
        processes : typing___Optional[typing___Mapping[typing___Text, type___SoftwareProcessState]] = None,
        versions : typing___Optional[type___SoftwareVersionsState] = None,
        restarting : typing___Optional[builtin___bool] = None,
        last_restart_time_ms : typing___Optional[builtin___int] = None,
        updating : typing___Optional[builtin___bool] = None,
        last_update_time_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"versions",b"versions"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"last_restart_time_ms",b"last_restart_time_ms",u"last_update_time_ms",b"last_update_time_ms",u"processes",b"processes",u"restarting",b"restarting",u"updating",b"updating",u"versions",b"versions"]) -> None: ...
type___SoftwareState = SoftwareState

class DeepweedModelState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    viable_crop_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        viable_crop_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"viable_crop_ids",b"viable_crop_ids"]) -> None: ...
type___DeepweedModelState = DeepweedModelState

class P2PModelState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___P2PModelState = P2PModelState

class ModelState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    model_id: typing___Text = ...
    type: typing___Text = ...
    ready: builtin___bool = ...
    loaded: builtin___bool = ...

    @property
    def deepweed(self) -> type___DeepweedModelState: ...

    @property
    def p2p(self) -> type___P2PModelState: ...

    def __init__(self,
        *,
        model_id : typing___Optional[typing___Text] = None,
        type : typing___Optional[typing___Text] = None,
        deepweed : typing___Optional[type___DeepweedModelState] = None,
        p2p : typing___Optional[type___P2PModelState] = None,
        ready : typing___Optional[builtin___bool] = None,
        loaded : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"deepweed",b"deepweed",u"p2p",b"p2p",u"specific",b"specific"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"deepweed",b"deepweed",u"loaded",b"loaded",u"model_id",b"model_id",u"p2p",b"p2p",u"ready",b"ready",u"specific",b"specific",u"type",b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"specific",b"specific"]) -> typing_extensions___Literal["deepweed","p2p"]: ...
type___ModelState = ModelState

class RowModelState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def models(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModelState]: ...

    def __init__(self,
        *,
        models : typing___Optional[typing___Iterable[type___ModelState]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"models",b"models"]) -> None: ...
type___RowModelState = RowModelState

class ModelManagerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    active_crop_id: typing___Text = ...
    active_deepweed_model: typing___Text = ...
    active_p2p_model: typing___Text = ...

    @property
    def local_models(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ModelState]: ...

    @property
    def row_models(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___RowModelState]: ...

    def __init__(self,
        *,
        local_models : typing___Optional[typing___Iterable[type___ModelState]] = None,
        row_models : typing___Optional[typing___Iterable[type___RowModelState]] = None,
        active_crop_id : typing___Optional[typing___Text] = None,
        active_deepweed_model : typing___Optional[typing___Text] = None,
        active_p2p_model : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active_crop_id",b"active_crop_id",u"active_deepweed_model",b"active_deepweed_model",u"active_p2p_model",b"active_p2p_model",u"local_models",b"local_models",u"row_models",b"row_models"]) -> None: ...
type___ModelManagerState = ModelManagerState

class ComputerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def software(self) -> type___SoftwareState: ...

    @property
    def host(self) -> type___HostState: ...

    def __init__(self,
        *,
        software : typing___Optional[type___SoftwareState] = None,
        host : typing___Optional[type___HostState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"host",b"host",u"software",b"software"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"host",b"host",u"software",b"software"]) -> None: ...
type___ComputerState = ComputerState

class PowerTimeState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    commander_on_time_ms: builtin___int = ...
    power_on_time_ms: builtin___int = ...
    air_conditioner_enabled_time_ms: builtin___int = ...
    chiller_enabled_time_ms: builtin___int = ...
    gps_enabled_time_ms: builtin___int = ...
    strobe_enabled_time_ms: builtin___int = ...
    wheel_encoder_enabled_time_ms: builtin___int = ...
    btl_enabled_time_ms: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    server_enabled_time_ms: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    scanners_enabled_time_ms: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    main_contactor_enabled_time_ms: builtin___int = ...

    def __init__(self,
        *,
        commander_on_time_ms : typing___Optional[builtin___int] = None,
        power_on_time_ms : typing___Optional[builtin___int] = None,
        air_conditioner_enabled_time_ms : typing___Optional[builtin___int] = None,
        chiller_enabled_time_ms : typing___Optional[builtin___int] = None,
        gps_enabled_time_ms : typing___Optional[builtin___int] = None,
        strobe_enabled_time_ms : typing___Optional[builtin___int] = None,
        wheel_encoder_enabled_time_ms : typing___Optional[builtin___int] = None,
        btl_enabled_time_ms : typing___Optional[typing___Iterable[builtin___int]] = None,
        server_enabled_time_ms : typing___Optional[typing___Iterable[builtin___int]] = None,
        scanners_enabled_time_ms : typing___Optional[typing___Iterable[builtin___int]] = None,
        main_contactor_enabled_time_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"air_conditioner_enabled_time_ms",b"air_conditioner_enabled_time_ms",u"btl_enabled_time_ms",b"btl_enabled_time_ms",u"chiller_enabled_time_ms",b"chiller_enabled_time_ms",u"commander_on_time_ms",b"commander_on_time_ms",u"gps_enabled_time_ms",b"gps_enabled_time_ms",u"main_contactor_enabled_time_ms",b"main_contactor_enabled_time_ms",u"power_on_time_ms",b"power_on_time_ms",u"scanners_enabled_time_ms",b"scanners_enabled_time_ms",u"server_enabled_time_ms",b"server_enabled_time_ms",u"strobe_enabled_time_ms",b"strobe_enabled_time_ms",u"wheel_encoder_enabled_time_ms",b"wheel_encoder_enabled_time_ms"]) -> None: ...
type___PowerTimeState = PowerTimeState

class VelocityState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current_velocity_mph: builtin___float = ...
    target_velocity_mph: builtin___float = ...
    row_target_velocity_mph: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...

    def __init__(self,
        *,
        current_velocity_mph : typing___Optional[builtin___float] = None,
        target_velocity_mph : typing___Optional[builtin___float] = None,
        row_target_velocity_mph : typing___Optional[typing___Iterable[builtin___float]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_velocity_mph",b"current_velocity_mph",u"row_target_velocity_mph",b"row_target_velocity_mph",u"target_velocity_mph",b"target_velocity_mph"]) -> None: ...
type___VelocityState = VelocityState

class WeedingState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    weeding: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___bool] = ...
    stage: type___BootStageValue = ...

    @property
    def velocity(self) -> type___VelocityState: ...

    def __init__(self,
        *,
        weeding : typing___Optional[typing___Iterable[builtin___bool]] = None,
        stage : typing___Optional[type___BootStageValue] = None,
        velocity : typing___Optional[type___VelocityState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"velocity",b"velocity"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"stage",b"stage",u"velocity",b"velocity",u"weeding",b"weeding"]) -> None: ...
type___WeedingState = WeedingState

class GlobalState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def computer(self) -> type___ComputerState: ...

    @property
    def gps(self) -> type___GPSBoardState: ...

    @property
    def wheel(self) -> type___WheelEncoderBoardState: ...

    @property
    def strobe(self) -> type___StrobeBoardState: ...

    @property
    def supervisory(self) -> type___SupervisoryPLCState: ...

    @property
    def safety(self) -> type___SafetyPLCState: ...

    @property
    def chiller(self) -> type___ChillerState: ...

    @property
    def air_conditioner(self) -> type___AirConditionerState: ...

    @property
    def isobus(self) -> type___ISOBusState: ...

    @property
    def power_time(self) -> type___PowerTimeState: ...

    @property
    def models(self) -> type___ModelManagerState: ...

    @property
    def weeding(self) -> type___WeedingState: ...

    @property
    def usb(self) -> type___USBDriveState: ...

    def __init__(self,
        *,
        computer : typing___Optional[type___ComputerState] = None,
        gps : typing___Optional[type___GPSBoardState] = None,
        wheel : typing___Optional[type___WheelEncoderBoardState] = None,
        strobe : typing___Optional[type___StrobeBoardState] = None,
        supervisory : typing___Optional[type___SupervisoryPLCState] = None,
        safety : typing___Optional[type___SafetyPLCState] = None,
        chiller : typing___Optional[type___ChillerState] = None,
        air_conditioner : typing___Optional[type___AirConditionerState] = None,
        isobus : typing___Optional[type___ISOBusState] = None,
        power_time : typing___Optional[type___PowerTimeState] = None,
        models : typing___Optional[type___ModelManagerState] = None,
        weeding : typing___Optional[type___WeedingState] = None,
        usb : typing___Optional[type___USBDriveState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"air_conditioner",b"air_conditioner",u"chiller",b"chiller",u"computer",b"computer",u"gps",b"gps",u"isobus",b"isobus",u"models",b"models",u"power_time",b"power_time",u"safety",b"safety",u"strobe",b"strobe",u"supervisory",b"supervisory",u"usb",b"usb",u"weeding",b"weeding",u"wheel",b"wheel"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"air_conditioner",b"air_conditioner",u"chiller",b"chiller",u"computer",b"computer",u"gps",b"gps",u"isobus",b"isobus",u"models",b"models",u"power_time",b"power_time",u"safety",b"safety",u"strobe",b"strobe",u"supervisory",b"supervisory",u"usb",b"usb",u"weeding",b"weeding",u"wheel",b"wheel"]) -> None: ...
type___GlobalState = GlobalState

class CameraState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    vendor: typing___Text = ...
    model: typing___Text = ...
    ip: typing___Text = ...
    serial: typing___Text = ...
    id: typing___Text = ...
    connected: builtin___bool = ...
    exposure_us: builtin___float = ...
    gpu_id: builtin___int = ...

    def __init__(self,
        *,
        vendor : typing___Optional[typing___Text] = None,
        model : typing___Optional[typing___Text] = None,
        ip : typing___Optional[typing___Text] = None,
        serial : typing___Optional[typing___Text] = None,
        id : typing___Optional[typing___Text] = None,
        connected : typing___Optional[builtin___bool] = None,
        exposure_us : typing___Optional[builtin___float] = None,
        gpu_id : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"connected",b"connected",u"exposure_us",b"exposure_us",u"gpu_id",b"gpu_id",u"id",b"id",u"ip",b"ip",u"model",b"model",u"serial",b"serial",u"vendor",b"vendor"]) -> None: ...
type___CameraState = CameraState

class MaxonPidState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    current_p: builtin___int = ...
    current_i: builtin___int = ...
    position_p: builtin___int = ...
    position_i: builtin___int = ...
    position_d: builtin___int = ...
    position_ffv: builtin___int = ...
    position_ffa: builtin___int = ...

    def __init__(self,
        *,
        current_p : typing___Optional[builtin___int] = None,
        current_i : typing___Optional[builtin___int] = None,
        position_p : typing___Optional[builtin___int] = None,
        position_i : typing___Optional[builtin___int] = None,
        position_d : typing___Optional[builtin___int] = None,
        position_ffv : typing___Optional[builtin___int] = None,
        position_ffa : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"current_i",b"current_i",u"current_p",b"current_p",u"position_d",b"position_d",u"position_ffa",b"position_ffa",u"position_ffv",b"position_ffv",u"position_i",b"position_i",u"position_p",b"position_p"]) -> None: ...
type___MaxonPidState = MaxonPidState

class ServoState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: typing___Text = ...
    min: builtin___int = ...
    max: builtin___int = ...
    last_read_position: builtin___int = ...
    last_requested_position: builtin___int = ...
    last_requested_velocity: builtin___int = ...
    last_requested_follow_velocity: builtin___int = ...

    @property
    def maxon(self) -> type___MaxonPidState: ...

    def __init__(self,
        *,
        type : typing___Optional[typing___Text] = None,
        min : typing___Optional[builtin___int] = None,
        max : typing___Optional[builtin___int] = None,
        last_read_position : typing___Optional[builtin___int] = None,
        last_requested_position : typing___Optional[builtin___int] = None,
        last_requested_velocity : typing___Optional[builtin___int] = None,
        last_requested_follow_velocity : typing___Optional[builtin___int] = None,
        maxon : typing___Optional[type___MaxonPidState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"maxon",b"maxon",u"pids",b"pids"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"last_read_position",b"last_read_position",u"last_requested_follow_velocity",b"last_requested_follow_velocity",u"last_requested_position",b"last_requested_position",u"last_requested_velocity",b"last_requested_velocity",u"max",b"max",u"maxon",b"maxon",u"min",b"min",u"pids",b"pids",u"type",b"type"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"pids",b"pids"]) -> typing_extensions___Literal["maxon"]: ...
type___ServoState = ServoState

class ScannerMetricState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    n_entities_targeted: builtin___float = ...
    avg_accuracy: builtin___float = ...
    avg_initial_accuracy: builtin___float = ...
    avg_overhead_time: builtin___float = ...
    error_rate: builtin___float = ...
    not_found_rate: builtin___float = ...
    out_of_range_rate: builtin___float = ...
    target_changed_rate: builtin___float = ...

    def __init__(self,
        *,
        n_entities_targeted : typing___Optional[builtin___float] = None,
        avg_accuracy : typing___Optional[builtin___float] = None,
        avg_initial_accuracy : typing___Optional[builtin___float] = None,
        avg_overhead_time : typing___Optional[builtin___float] = None,
        error_rate : typing___Optional[builtin___float] = None,
        not_found_rate : typing___Optional[builtin___float] = None,
        out_of_range_rate : typing___Optional[builtin___float] = None,
        target_changed_rate : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"avg_accuracy",b"avg_accuracy",u"avg_initial_accuracy",b"avg_initial_accuracy",u"avg_overhead_time",b"avg_overhead_time",u"error_rate",b"error_rate",u"n_entities_targeted",b"n_entities_targeted",u"not_found_rate",b"not_found_rate",u"out_of_range_rate",b"out_of_range_rate",u"target_changed_rate",b"target_changed_rate"]) -> None: ...
type___ScannerMetricState = ScannerMetricState

class ScannerLaserState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    serial: typing___Text = ...
    n_shots: builtin___int = ...
    fire_time_ms: builtin___int = ...
    controlled_time_ms: builtin___int = ...
    lpsu_power: builtin___bool = ...
    lpsu_current: builtin___float = ...
    therm_sensor: builtin___float = ...
    therm_ref: builtin___float = ...
    delta_temp: builtin___float = ...
    power_bypassed: builtin___bool = ...
    armed: builtin___bool = ...
    watchdog: builtin___bool = ...
    firing_requested: builtin___bool = ...
    firing: builtin___bool = ...
    intensity: builtin___float = ...
    wattage: builtin___float = ...
    nominal_wattage: builtin___float = ...

    def __init__(self,
        *,
        serial : typing___Optional[typing___Text] = None,
        n_shots : typing___Optional[builtin___int] = None,
        fire_time_ms : typing___Optional[builtin___int] = None,
        controlled_time_ms : typing___Optional[builtin___int] = None,
        lpsu_power : typing___Optional[builtin___bool] = None,
        lpsu_current : typing___Optional[builtin___float] = None,
        therm_sensor : typing___Optional[builtin___float] = None,
        therm_ref : typing___Optional[builtin___float] = None,
        delta_temp : typing___Optional[builtin___float] = None,
        power_bypassed : typing___Optional[builtin___bool] = None,
        armed : typing___Optional[builtin___bool] = None,
        watchdog : typing___Optional[builtin___bool] = None,
        firing_requested : typing___Optional[builtin___bool] = None,
        firing : typing___Optional[builtin___bool] = None,
        intensity : typing___Optional[builtin___float] = None,
        wattage : typing___Optional[builtin___float] = None,
        nominal_wattage : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"armed",b"armed",u"controlled_time_ms",b"controlled_time_ms",u"delta_temp",b"delta_temp",u"fire_time_ms",b"fire_time_ms",u"firing",b"firing",u"firing_requested",b"firing_requested",u"intensity",b"intensity",u"lpsu_current",b"lpsu_current",u"lpsu_power",b"lpsu_power",u"n_shots",b"n_shots",u"nominal_wattage",b"nominal_wattage",u"power_bypassed",b"power_bypassed",u"serial",b"serial",u"therm_ref",b"therm_ref",u"therm_sensor",b"therm_sensor",u"watchdog",b"watchdog",u"wattage",b"wattage"]) -> None: ...
type___ScannerLaserState = ScannerLaserState

class ScannerCrosshairState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    last_update_timestamp_ms: builtin___int = ...
    x: builtin___float = ...
    y: builtin___float = ...

    def __init__(self,
        *,
        last_update_timestamp_ms : typing___Optional[builtin___int] = None,
        x : typing___Optional[builtin___float] = None,
        y : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"last_update_timestamp_ms",b"last_update_timestamp_ms",u"x",b"x",u"y",b"y"]) -> None: ...
type___ScannerCrosshairState = ScannerCrosshairState

class ScannerLensState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    last_requested_value: builtin___float = ...
    actual_value: builtin___float = ...

    def __init__(self,
        *,
        last_requested_value : typing___Optional[builtin___float] = None,
        actual_value : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"actual_value",b"actual_value",u"last_requested_value",b"last_requested_value"]) -> None: ...
type___ScannerLensState = ScannerLensState

class ScannerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    enabled: builtin___bool = ...

    @property
    def board(self) -> type___BoardState: ...

    @property
    def pan(self) -> type___ServoState: ...

    @property
    def tilt(self) -> type___ServoState: ...

    @property
    def crosshair(self) -> type___ScannerCrosshairState: ...

    @property
    def lens(self) -> type___ScannerLensState: ...

    @property
    def laser(self) -> type___ScannerLaserState: ...

    @property
    def metrics(self) -> type___ScannerMetricState: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        board : typing___Optional[type___BoardState] = None,
        enabled : typing___Optional[builtin___bool] = None,
        pan : typing___Optional[type___ServoState] = None,
        tilt : typing___Optional[type___ServoState] = None,
        crosshair : typing___Optional[type___ScannerCrosshairState] = None,
        lens : typing___Optional[type___ScannerLensState] = None,
        laser : typing___Optional[type___ScannerLaserState] = None,
        metrics : typing___Optional[type___ScannerMetricState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"board",b"board",u"crosshair",b"crosshair",u"laser",b"laser",u"lens",b"lens",u"metrics",b"metrics",u"pan",b"pan",u"tilt",b"tilt"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"board",b"board",u"crosshair",b"crosshair",u"enabled",b"enabled",u"laser",b"laser",u"lens",b"lens",u"metrics",b"metrics",u"name",b"name",u"pan",b"pan",u"tilt",b"tilt"]) -> None: ...
type___ScannerState = ScannerState

class CVNodeState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    mean_fps: builtin___float = ...
    mean_output_latency: builtin___float = ...
    p99_output_latency: builtin___float = ...
    mean_real_latency: builtin___float = ...
    p99_real_latency: builtin___float = ...
    state: typing___Text = ...
    pull_ms: builtin___float = ...
    processing_ms: builtin___float = ...
    pushing_ms: builtin___float = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        mean_fps : typing___Optional[builtin___float] = None,
        mean_output_latency : typing___Optional[builtin___float] = None,
        p99_output_latency : typing___Optional[builtin___float] = None,
        mean_real_latency : typing___Optional[builtin___float] = None,
        p99_real_latency : typing___Optional[builtin___float] = None,
        state : typing___Optional[typing___Text] = None,
        pull_ms : typing___Optional[builtin___float] = None,
        processing_ms : typing___Optional[builtin___float] = None,
        pushing_ms : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"mean_fps",b"mean_fps",u"mean_output_latency",b"mean_output_latency",u"mean_real_latency",b"mean_real_latency",u"name",b"name",u"p99_output_latency",b"p99_output_latency",u"p99_real_latency",b"p99_real_latency",u"processing_ms",b"processing_ms",u"pull_ms",b"pull_ms",u"pushing_ms",b"pushing_ms",u"state",b"state"]) -> None: ...
type___CVNodeState = CVNodeState

class CVState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def nodes(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CVNodeState]: ...

    def __init__(self,
        *,
        nodes : typing___Optional[typing___Iterable[type___CVNodeState]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"nodes",b"nodes"]) -> None: ...
type___CVState = CVState

class TrackerState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    n_trajectories: builtin___int = ...
    capacity: builtin___int = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        n_trajectories : typing___Optional[builtin___int] = None,
        capacity : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"capacity",b"capacity",u"n_trajectories",b"n_trajectories",u"name",b"name"]) -> None: ...
type___TrackerState = TrackerState

class IngestClientState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    over_capacity: builtin___bool = ...
    over_constraints: builtin___bool = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        over_capacity : typing___Optional[builtin___bool] = None,
        over_constraints : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"name",b"name",u"over_capacity",b"over_capacity",u"over_constraints",b"over_constraints"]) -> None: ...
type___IngestClientState = IngestClientState

class AimbotState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def trackers(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___TrackerState]: ...

    @property
    def ingests(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___IngestClientState]: ...

    def __init__(self,
        *,
        trackers : typing___Optional[typing___Iterable[type___TrackerState]] = None,
        ingests : typing___Optional[typing___Iterable[type___IngestClientState]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ingests",b"ingests",u"trackers",b"trackers"]) -> None: ...
type___AimbotState = AimbotState

class RowState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def computer(self) -> type___ComputerState: ...

    @property
    def scanners(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ScannerState]: ...

    @property
    def predicts(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CameraState]: ...

    @property
    def targets(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CameraState]: ...

    @property
    def extras(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___CameraState]: ...

    @property
    def cv(self) -> type___CVState: ...

    @property
    def aimbot(self) -> type___AimbotState: ...

    def __init__(self,
        *,
        computer : typing___Optional[type___ComputerState] = None,
        scanners : typing___Optional[typing___Iterable[type___ScannerState]] = None,
        predicts : typing___Optional[typing___Iterable[type___CameraState]] = None,
        targets : typing___Optional[typing___Iterable[type___CameraState]] = None,
        extras : typing___Optional[typing___Iterable[type___CameraState]] = None,
        cv : typing___Optional[type___CVState] = None,
        aimbot : typing___Optional[type___AimbotState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"aimbot",b"aimbot",u"computer",b"computer",u"cv",b"cv"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"aimbot",b"aimbot",u"computer",b"computer",u"cv",b"cv",u"extras",b"extras",u"predicts",b"predicts",u"scanners",b"scanners",u"targets",b"targets"]) -> None: ...
type___RowState = RowState

class RobotState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def rows(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___RowState]: ...

    def __init__(self,
        *,
        rows : typing___Optional[typing___Iterable[type___RowState]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"global",b"global"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"global",b"global",u"rows",b"rows"]) -> None: ...
type___RobotState = RobotState

class TimestampedRobotState(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def state(self) -> type___RobotState: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        state : typing___Optional[type___RobotState] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"state",b"state",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"state",b"state",u"ts",b"ts"]) -> None: ...
type___TimestampedRobotState = TimestampedRobotState

class RobotStateRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___RobotStateRequest = RobotStateRequest

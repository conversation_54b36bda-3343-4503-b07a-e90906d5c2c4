// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/alarm.proto
#ifndef GRPC_frontend_2fproto_2falarm_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2falarm_2eproto__INCLUDED

#include "frontend/proto/alarm.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace alarm {

class AlarmService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.alarm.AlarmService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmTable* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>> AsyncGetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>>(AsyncGetNextAlarmListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>> PrepareAsyncGetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>>(PrepareAsyncGetNextAlarmListRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmCount* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmCount>> AsyncGetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmCount>>(AsyncGetNextAlarmCountRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmCount>> PrepareAsyncGetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmCount>>(PrepareAsyncGetNextAlarmCountRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmTable* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>> AsyncGetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>>(AsyncGetNextNewAlarmListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>> PrepareAsyncGetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>>(PrepareAsyncGetNextNewAlarmListRaw(context, request, cq));
    }
    virtual ::grpc::Status AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncAcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncAcknowledgeAlarmRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncAcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncAcknowledgeAlarmRaw(context, request, cq));
    }
    virtual ::grpc::Status ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncResetAlarmsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncResetAlarmsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogResponse>> AsyncGetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogResponse>>(AsyncGetNextAlarmLogRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogResponse>> PrepareAsyncGetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogResponse>>(PrepareAsyncGetNextAlarmLogRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>> AsyncGetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>>(AsyncGetNextAlarmLogCountRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>> PrepareAsyncGetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>>(PrepareAsyncGetNextAlarmLogCountRaw(context, request, cq));
    }
    virtual ::grpc::Status AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncAttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncAttemptAutofixAlarmRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncAttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncAttemptAutofixAlarmRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>> AsyncGetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>>(AsyncGetNextAutofixAlarmStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>> PrepareAsyncGetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>>(PrepareAsyncGetNextAutofixAlarmStatusRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>* AsyncGetNextAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>* PrepareAsyncGetNextAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmCount>* AsyncGetNextAlarmCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmCount>* PrepareAsyncGetNextAlarmCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>* AsyncGetNextNewAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::AlarmTable>* PrepareAsyncGetNextNewAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncAcknowledgeAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncAcknowledgeAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncResetAlarmsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncResetAlarmsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogResponse>* AsyncGetNextAlarmLogRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogResponse>* PrepareAsyncGetNextAlarmLogRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* AsyncGetNextAlarmLogCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* PrepareAsyncGetNextAlarmLogCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncAttemptAutofixAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncAttemptAutofixAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* AsyncGetNextAutofixAlarmStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* PrepareAsyncGetNextAutofixAlarmStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmTable* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>> AsyncGetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>>(AsyncGetNextAlarmListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>> PrepareAsyncGetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>>(PrepareAsyncGetNextAlarmListRaw(context, request, cq));
    }
    ::grpc::Status GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmCount* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmCount>> AsyncGetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmCount>>(AsyncGetNextAlarmCountRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmCount>> PrepareAsyncGetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmCount>>(PrepareAsyncGetNextAlarmCountRaw(context, request, cq));
    }
    ::grpc::Status GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmTable* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>> AsyncGetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>>(AsyncGetNextNewAlarmListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>> PrepareAsyncGetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>>(PrepareAsyncGetNextNewAlarmListRaw(context, request, cq));
    }
    ::grpc::Status AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncAcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncAcknowledgeAlarmRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncAcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncAcknowledgeAlarmRaw(context, request, cq));
    }
    ::grpc::Status ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncResetAlarmsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncResetAlarmsRaw(context, request, cq));
    }
    ::grpc::Status GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogResponse>> AsyncGetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogResponse>>(AsyncGetNextAlarmLogRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogResponse>> PrepareAsyncGetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogResponse>>(PrepareAsyncGetNextAlarmLogRaw(context, request, cq));
    }
    ::grpc::Status GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>> AsyncGetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>>(AsyncGetNextAlarmLogCountRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>> PrepareAsyncGetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>>(PrepareAsyncGetNextAlarmLogCountRaw(context, request, cq));
    }
    ::grpc::Status AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncAttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncAttemptAutofixAlarmRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncAttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncAttemptAutofixAlarmRaw(context, request, cq));
    }
    ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>> AsyncGetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>>(AsyncGetNextAutofixAlarmStatusRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>> PrepareAsyncGetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>>(PrepareAsyncGetNextAutofixAlarmStatusRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, std::function<void(::grpc::Status)>) override;
      void GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response, std::function<void(::grpc::Status)>) override;
      void GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, std::function<void(::grpc::Status)>) override;
      void GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, ::grpc::ClientUnaryReactor* reactor) override;
      void AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>* AsyncGetNextAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>* PrepareAsyncGetNextAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmCount>* AsyncGetNextAlarmCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmCount>* PrepareAsyncGetNextAlarmCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>* AsyncGetNextNewAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>* PrepareAsyncGetNextNewAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncAcknowledgeAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncAcknowledgeAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncResetAlarmsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncResetAlarmsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogResponse>* AsyncGetNextAlarmLogRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogResponse>* PrepareAsyncGetNextAlarmLogRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* AsyncGetNextAlarmLogCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* PrepareAsyncGetNextAlarmLogCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncAttemptAutofixAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncAttemptAutofixAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* AsyncGetNextAutofixAlarmStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* PrepareAsyncGetNextAutofixAlarmStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAlarmList_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAlarmCount_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextNewAlarmList_;
    const ::grpc::internal::RpcMethod rpcmethod_AcknowledgeAlarm_;
    const ::grpc::internal::RpcMethod rpcmethod_ResetAlarms_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAlarmLog_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAlarmLogCount_;
    const ::grpc::internal::RpcMethod rpcmethod_AttemptAutofixAlarm_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAutofixAlarmStatus_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextAlarmList(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response);
    virtual ::grpc::Status GetNextAlarmCount(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response);
    virtual ::grpc::Status GetNextNewAlarmList(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response);
    virtual ::grpc::Status AcknowledgeAlarm(::grpc::ServerContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status ResetAlarms(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextAlarmLog(::grpc::ServerContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response);
    virtual ::grpc::Status GetNextAlarmLogCount(::grpc::ServerContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response);
    virtual ::grpc::Status AttemptAutofixAlarm(::grpc::ServerContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ServerContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAlarmList() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlarmList(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::alarm::AlarmTable>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAlarmCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAlarmCount() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextAlarmCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmCount* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlarmCount(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::alarm::AlarmCount>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextNewAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextNewAlarmList() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetNextNewAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextNewAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextNewAlarmList(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::alarm::AlarmTable>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_AcknowledgeAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_AcknowledgeAlarm() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_AcknowledgeAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AcknowledgeRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAcknowledgeAlarm(::grpc::ServerContext* context, ::carbon::frontend::alarm::AcknowledgeRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ResetAlarms : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ResetAlarms() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_ResetAlarms() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetAlarms(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResetAlarms(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAlarmLog : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAlarmLog() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_GetNextAlarmLog() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLog(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlarmLog(::grpc::ServerContext* context, ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::alarm::GetNextAlarmLogResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAlarmLogCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAlarmLogCount() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetNextAlarmLogCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLogCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlarmLogCount(::grpc::ServerContext* context, ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_AttemptAutofixAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_AttemptAutofixAlarm() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_AttemptAutofixAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AttemptAutofixAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAttemptAutofixAlarm(::grpc::ServerContext* context, ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAutofixAlarmStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAutofixAlarmStatus() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_GetNextAutofixAlarmStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* /*request*/, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAutofixAlarmStatus(::grpc::ServerContext* context, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextAlarmList<WithAsyncMethod_GetNextAlarmCount<WithAsyncMethod_GetNextNewAlarmList<WithAsyncMethod_AcknowledgeAlarm<WithAsyncMethod_ResetAlarms<WithAsyncMethod_GetNextAlarmLog<WithAsyncMethod_GetNextAlarmLogCount<WithAsyncMethod_AttemptAutofixAlarm<WithAsyncMethod_GetNextAutofixAlarmStatus<Service > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAlarmList() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response) { return this->GetNextAlarmList(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAlarmList(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlarmList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextAlarmCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAlarmCount() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmCount>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response) { return this->GetNextAlarmCount(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAlarmCount(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmCount>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmCount>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAlarmCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmCount* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlarmCount(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmCount* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextNewAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextNewAlarmList() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response) { return this->GetNextNewAlarmList(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextNewAlarmList(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextNewAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextNewAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextNewAlarmList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_AcknowledgeAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_AcknowledgeAlarm() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::AcknowledgeRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response) { return this->AcknowledgeAlarm(context, request, response); }));}
    void SetMessageAllocatorFor_AcknowledgeAlarm(
        ::grpc::MessageAllocator< ::carbon::frontend::alarm::AcknowledgeRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::AcknowledgeRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_AcknowledgeAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AcknowledgeRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AcknowledgeAlarm(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::alarm::AcknowledgeRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ResetAlarms : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ResetAlarms() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->ResetAlarms(context, request, response); }));}
    void SetMessageAllocatorFor_ResetAlarms(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ResetAlarms() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetAlarms(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ResetAlarms(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextAlarmLog : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAlarmLog() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::carbon::frontend::alarm::GetNextAlarmLogResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response) { return this->GetNextAlarmLog(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAlarmLog(
        ::grpc::MessageAllocator< ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::carbon::frontend::alarm::GetNextAlarmLogResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::carbon::frontend::alarm::GetNextAlarmLogResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAlarmLog() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLog(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlarmLog(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextAlarmLogCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAlarmLogCount() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response) { return this->GetNextAlarmLogCount(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAlarmLogCount(
        ::grpc::MessageAllocator< ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAlarmLogCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLogCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlarmLogCount(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_AttemptAutofixAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_AttemptAutofixAlarm() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response) { return this->AttemptAutofixAlarm(context, request, response); }));}
    void SetMessageAllocatorFor_AttemptAutofixAlarm(
        ::grpc::MessageAllocator< ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_AttemptAutofixAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AttemptAutofixAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AttemptAutofixAlarm(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextAutofixAlarmStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAutofixAlarmStatus() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response) { return this->GetNextAutofixAlarmStatus(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAutofixAlarmStatus(
        ::grpc::MessageAllocator< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAutofixAlarmStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* /*request*/, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAutofixAlarmStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* /*request*/, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextAlarmList<WithCallbackMethod_GetNextAlarmCount<WithCallbackMethod_GetNextNewAlarmList<WithCallbackMethod_AcknowledgeAlarm<WithCallbackMethod_ResetAlarms<WithCallbackMethod_GetNextAlarmLog<WithCallbackMethod_GetNextAlarmLogCount<WithCallbackMethod_AttemptAutofixAlarm<WithCallbackMethod_GetNextAutofixAlarmStatus<Service > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAlarmList() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextAlarmCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAlarmCount() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextAlarmCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmCount* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextNewAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextNewAlarmList() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetNextNewAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextNewAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_AcknowledgeAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_AcknowledgeAlarm() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_AcknowledgeAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AcknowledgeRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ResetAlarms : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ResetAlarms() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_ResetAlarms() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetAlarms(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextAlarmLog : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAlarmLog() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_GetNextAlarmLog() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLog(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextAlarmLogCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAlarmLogCount() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetNextAlarmLogCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLogCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_AttemptAutofixAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_AttemptAutofixAlarm() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_AttemptAutofixAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AttemptAutofixAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextAutofixAlarmStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAutofixAlarmStatus() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_GetNextAutofixAlarmStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* /*request*/, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAlarmList() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlarmList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAlarmCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAlarmCount() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextAlarmCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmCount* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlarmCount(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextNewAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextNewAlarmList() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetNextNewAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextNewAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextNewAlarmList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_AcknowledgeAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_AcknowledgeAlarm() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_AcknowledgeAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AcknowledgeRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAcknowledgeAlarm(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ResetAlarms : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ResetAlarms() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_ResetAlarms() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetAlarms(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResetAlarms(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAlarmLog : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAlarmLog() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_GetNextAlarmLog() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLog(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlarmLog(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAlarmLogCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAlarmLogCount() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetNextAlarmLogCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLogCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAlarmLogCount(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_AttemptAutofixAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_AttemptAutofixAlarm() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_AttemptAutofixAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AttemptAutofixAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAttemptAutofixAlarm(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAutofixAlarmStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAutofixAlarmStatus() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_GetNextAutofixAlarmStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* /*request*/, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAutofixAlarmStatus(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAlarmList() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAlarmList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlarmList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAlarmCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAlarmCount() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAlarmCount(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAlarmCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmCount* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlarmCount(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextNewAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextNewAlarmList() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextNewAlarmList(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextNewAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextNewAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextNewAlarmList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_AcknowledgeAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_AcknowledgeAlarm() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->AcknowledgeAlarm(context, request, response); }));
    }
    ~WithRawCallbackMethod_AcknowledgeAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AcknowledgeAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AcknowledgeRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AcknowledgeAlarm(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ResetAlarms : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ResetAlarms() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ResetAlarms(context, request, response); }));
    }
    ~WithRawCallbackMethod_ResetAlarms() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetAlarms(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ResetAlarms(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAlarmLog : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAlarmLog() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAlarmLog(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAlarmLog() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLog(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlarmLog(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAlarmLogCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAlarmLogCount() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAlarmLogCount(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAlarmLogCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAlarmLogCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAlarmLogCount(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_AttemptAutofixAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_AttemptAutofixAlarm() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->AttemptAutofixAlarm(context, request, response); }));
    }
    ~WithRawCallbackMethod_AttemptAutofixAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AttemptAutofixAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AttemptAutofixAlarm(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAutofixAlarmStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAutofixAlarmStatus() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAutofixAlarmStatus(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAutofixAlarmStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* /*request*/, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAutofixAlarmStatus(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAlarmList() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>* streamer) {
                       return this->StreamedGetNextAlarmList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAlarmList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::alarm::AlarmTable>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAlarmCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAlarmCount() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmCount>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmCount>* streamer) {
                       return this->StreamedGetNextAlarmCount(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAlarmCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAlarmCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmCount* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAlarmCount(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::alarm::AlarmCount>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextNewAlarmList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextNewAlarmList() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable>* streamer) {
                       return this->StreamedGetNextNewAlarmList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextNewAlarmList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextNewAlarmList(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::alarm::AlarmTable* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextNewAlarmList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::alarm::AlarmTable>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_AcknowledgeAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_AcknowledgeAlarm() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::alarm::AcknowledgeRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::alarm::AcknowledgeRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedAcknowledgeAlarm(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_AcknowledgeAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status AcknowledgeAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AcknowledgeRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedAcknowledgeAlarm(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::alarm::AcknowledgeRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ResetAlarms : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ResetAlarms() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedResetAlarms(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ResetAlarms() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ResetAlarms(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedResetAlarms(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAlarmLog : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAlarmLog() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::carbon::frontend::alarm::GetNextAlarmLogResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::carbon::frontend::alarm::GetNextAlarmLogResponse>* streamer) {
                       return this->StreamedGetNextAlarmLog(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAlarmLog() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAlarmLog(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAlarmLog(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::alarm::GetNextAlarmLogRequest,::carbon::frontend::alarm::GetNextAlarmLogResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAlarmLogCount : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAlarmLogCount() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* streamer) {
                       return this->StreamedGetNextAlarmLogCount(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAlarmLogCount() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAlarmLogCount(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* /*request*/, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAlarmLogCount(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::alarm::GetNextAlarmLogCountRequest,::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_AttemptAutofixAlarm : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_AttemptAutofixAlarm() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedAttemptAutofixAlarm(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_AttemptAutofixAlarm() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status AttemptAutofixAlarm(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedAttemptAutofixAlarm(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::alarm::AttemptAutofixAlarmRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAutofixAlarmStatus : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAutofixAlarmStatus() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* streamer) {
                       return this->StreamedGetNextAutofixAlarmStatus(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAutofixAlarmStatus() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAutofixAlarmStatus(::grpc::ServerContext* /*context*/, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* /*request*/, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAutofixAlarmStatus(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest,::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextAlarmList<WithStreamedUnaryMethod_GetNextAlarmCount<WithStreamedUnaryMethod_GetNextNewAlarmList<WithStreamedUnaryMethod_AcknowledgeAlarm<WithStreamedUnaryMethod_ResetAlarms<WithStreamedUnaryMethod_GetNextAlarmLog<WithStreamedUnaryMethod_GetNextAlarmLogCount<WithStreamedUnaryMethod_AttemptAutofixAlarm<WithStreamedUnaryMethod_GetNextAutofixAlarmStatus<Service > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextAlarmList<WithStreamedUnaryMethod_GetNextAlarmCount<WithStreamedUnaryMethod_GetNextNewAlarmList<WithStreamedUnaryMethod_AcknowledgeAlarm<WithStreamedUnaryMethod_ResetAlarms<WithStreamedUnaryMethod_GetNextAlarmLog<WithStreamedUnaryMethod_GetNextAlarmLogCount<WithStreamedUnaryMethod_AttemptAutofixAlarm<WithStreamedUnaryMethod_GetNextAutofixAlarmStatus<Service > > > > > > > > > StreamedService;
};

}  // namespace alarm
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2falarm_2eproto__INCLUDED

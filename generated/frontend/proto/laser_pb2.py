# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/laser.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import camera_pb2 as frontend_dot_proto_dot_camera__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/laser.proto',
  package='carbon.frontend.laser',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1a\x66rontend/proto/laser.proto\x12\x15\x63\x61rbon.frontend.laser\x1a\x1b\x66rontend/proto/camera.proto\x1a\x19\x66rontend/proto/util.proto\"Z\n\x0fLaserDescriptor\x12\x12\n\nrow_number\x18\x01 \x01(\r\x12\x10\n\x08laser_id\x18\x02 \x01(\r\x12\x11\n\tcamera_id\x18\x03 \x01(\t\x12\x0e\n\x06serial\x18\x04 \x01(\t\"\xb8\x02\n\nLaserState\x12@\n\x10laser_descriptor\x18\x01 \x01(\x0b\x32&.carbon.frontend.laser.LaserDescriptor\x12\x0e\n\x06\x66iring\x18\x02 \x01(\x08\x12\x0f\n\x07\x65nabled\x18\x03 \x01(\x08\x12\r\n\x05\x65rror\x18\x04 \x01(\x08\x12\x18\n\x10total_fire_count\x18\x05 \x01(\x03\x12\x1a\n\x12total_fire_time_ms\x18\x06 \x01(\x03\x12\x12\n\ndelta_temp\x18\x07 \x01(\x02\x12\x0f\n\x07\x63urrent\x18\t \x01(\x02\x12\x1c\n\x14target_trajectory_id\x18\n \x01(\r\x12\x14\n\x0clifetime_sec\x18\x0b \x01(\x04\x12\x13\n\x0bpower_level\x18\x0c \x01(\x02\x12\x14\n\x0cinstalled_at\x18\r \x01(\x03\"p\n\x0eLaserStateList\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x31\n\x06lasers\x18\x02 \x03(\x0b\x32!.carbon.frontend.laser.LaserState\" \n\nRowRequest\x12\x12\n\nrow_number\x18\x01 \x01(\r\"m\n\x14SetLaserPowerRequest\x12@\n\x10laser_descriptor\x18\x01 \x01(\x0b\x32&.carbon.frontend.laser.LaserDescriptor\x12\x13\n\x0bpower_level\x18\x02 \x01(\x02\"\xa6\x01\n\x16\x46ixLaserMetricsRequest\x12@\n\x10laser_descriptor\x18\x01 \x01(\x0b\x32&.carbon.frontend.laser.LaserDescriptor\x12\x18\n\x10total_fire_count\x18\x02 \x01(\x03\x12\x1a\n\x12total_fire_time_ms\x18\x03 \x01(\x03\x12\x14\n\x0clifetime_sec\x18\x04 \x01(\x04\x32\xc8\x05\n\x0cLaserService\x12Q\n\tFireLaser\x12%.carbon.frontend.camera.CameraRequest\x1a\x1b.carbon.frontend.util.Empty(\x01\x12[\n\x11GetNextLaserState\x12\x1f.carbon.frontend.util.Timestamp\x1a%.carbon.frontend.laser.LaserStateList\x12Y\n\x12ToggleLaserEnabled\x12&.carbon.frontend.laser.LaserDescriptor\x1a\x1b.carbon.frontend.util.Empty\x12K\n\tEnableRow\x12!.carbon.frontend.laser.RowRequest\x1a\x1b.carbon.frontend.util.Empty\x12L\n\nDisableRow\x12!.carbon.frontend.laser.RowRequest\x1a\x1b.carbon.frontend.util.Empty\x12X\n\x11ResetLaserMetrics\x12&.carbon.frontend.laser.LaserDescriptor\x1a\x1b.carbon.frontend.util.Empty\x12]\n\x0f\x46ixLaserMetrics\x12-.carbon.frontend.laser.FixLaserMetricsRequest\x1a\x1b.carbon.frontend.util.Empty\x12Y\n\rSetLaserPower\x12+.carbon.frontend.laser.SetLaserPowerRequest\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_camera__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_LASERDESCRIPTOR = _descriptor.Descriptor(
  name='LaserDescriptor',
  full_name='carbon.frontend.laser.LaserDescriptor',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_number', full_name='carbon.frontend.laser.LaserDescriptor.row_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_id', full_name='carbon.frontend.laser.LaserDescriptor.laser_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='camera_id', full_name='carbon.frontend.laser.LaserDescriptor.camera_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.frontend.laser.LaserDescriptor.serial', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=109,
  serialized_end=199,
)


_LASERSTATE = _descriptor.Descriptor(
  name='LaserState',
  full_name='carbon.frontend.laser.LaserState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='laser_descriptor', full_name='carbon.frontend.laser.LaserState.laser_descriptor', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firing', full_name='carbon.frontend.laser.LaserState.firing', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.laser.LaserState.enabled', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='carbon.frontend.laser.LaserState.error', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='total_fire_count', full_name='carbon.frontend.laser.LaserState.total_fire_count', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='total_fire_time_ms', full_name='carbon.frontend.laser.LaserState.total_fire_time_ms', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='delta_temp', full_name='carbon.frontend.laser.LaserState.delta_temp', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current', full_name='carbon.frontend.laser.LaserState.current', index=7,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_trajectory_id', full_name='carbon.frontend.laser.LaserState.target_trajectory_id', index=8,
      number=10, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifetime_sec', full_name='carbon.frontend.laser.LaserState.lifetime_sec', index=9,
      number=11, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_level', full_name='carbon.frontend.laser.LaserState.power_level', index=10,
      number=12, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='installed_at', full_name='carbon.frontend.laser.LaserState.installed_at', index=11,
      number=13, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=202,
  serialized_end=514,
)


_LASERSTATELIST = _descriptor.Descriptor(
  name='LaserStateList',
  full_name='carbon.frontend.laser.LaserStateList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.laser.LaserStateList.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lasers', full_name='carbon.frontend.laser.LaserStateList.lasers', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=516,
  serialized_end=628,
)


_ROWREQUEST = _descriptor.Descriptor(
  name='RowRequest',
  full_name='carbon.frontend.laser.RowRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_number', full_name='carbon.frontend.laser.RowRequest.row_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=630,
  serialized_end=662,
)


_SETLASERPOWERREQUEST = _descriptor.Descriptor(
  name='SetLaserPowerRequest',
  full_name='carbon.frontend.laser.SetLaserPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='laser_descriptor', full_name='carbon.frontend.laser.SetLaserPowerRequest.laser_descriptor', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_level', full_name='carbon.frontend.laser.SetLaserPowerRequest.power_level', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=664,
  serialized_end=773,
)


_FIXLASERMETRICSREQUEST = _descriptor.Descriptor(
  name='FixLaserMetricsRequest',
  full_name='carbon.frontend.laser.FixLaserMetricsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='laser_descriptor', full_name='carbon.frontend.laser.FixLaserMetricsRequest.laser_descriptor', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='total_fire_count', full_name='carbon.frontend.laser.FixLaserMetricsRequest.total_fire_count', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='total_fire_time_ms', full_name='carbon.frontend.laser.FixLaserMetricsRequest.total_fire_time_ms', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifetime_sec', full_name='carbon.frontend.laser.FixLaserMetricsRequest.lifetime_sec', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=776,
  serialized_end=942,
)

_LASERSTATE.fields_by_name['laser_descriptor'].message_type = _LASERDESCRIPTOR
_LASERSTATELIST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_LASERSTATELIST.fields_by_name['lasers'].message_type = _LASERSTATE
_SETLASERPOWERREQUEST.fields_by_name['laser_descriptor'].message_type = _LASERDESCRIPTOR
_FIXLASERMETRICSREQUEST.fields_by_name['laser_descriptor'].message_type = _LASERDESCRIPTOR
DESCRIPTOR.message_types_by_name['LaserDescriptor'] = _LASERDESCRIPTOR
DESCRIPTOR.message_types_by_name['LaserState'] = _LASERSTATE
DESCRIPTOR.message_types_by_name['LaserStateList'] = _LASERSTATELIST
DESCRIPTOR.message_types_by_name['RowRequest'] = _ROWREQUEST
DESCRIPTOR.message_types_by_name['SetLaserPowerRequest'] = _SETLASERPOWERREQUEST
DESCRIPTOR.message_types_by_name['FixLaserMetricsRequest'] = _FIXLASERMETRICSREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

LaserDescriptor = _reflection.GeneratedProtocolMessageType('LaserDescriptor', (_message.Message,), {
  'DESCRIPTOR' : _LASERDESCRIPTOR,
  '__module__' : 'frontend.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.laser.LaserDescriptor)
  })
_sym_db.RegisterMessage(LaserDescriptor)

LaserState = _reflection.GeneratedProtocolMessageType('LaserState', (_message.Message,), {
  'DESCRIPTOR' : _LASERSTATE,
  '__module__' : 'frontend.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.laser.LaserState)
  })
_sym_db.RegisterMessage(LaserState)

LaserStateList = _reflection.GeneratedProtocolMessageType('LaserStateList', (_message.Message,), {
  'DESCRIPTOR' : _LASERSTATELIST,
  '__module__' : 'frontend.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.laser.LaserStateList)
  })
_sym_db.RegisterMessage(LaserStateList)

RowRequest = _reflection.GeneratedProtocolMessageType('RowRequest', (_message.Message,), {
  'DESCRIPTOR' : _ROWREQUEST,
  '__module__' : 'frontend.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.laser.RowRequest)
  })
_sym_db.RegisterMessage(RowRequest)

SetLaserPowerRequest = _reflection.GeneratedProtocolMessageType('SetLaserPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETLASERPOWERREQUEST,
  '__module__' : 'frontend.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.laser.SetLaserPowerRequest)
  })
_sym_db.RegisterMessage(SetLaserPowerRequest)

FixLaserMetricsRequest = _reflection.GeneratedProtocolMessageType('FixLaserMetricsRequest', (_message.Message,), {
  'DESCRIPTOR' : _FIXLASERMETRICSREQUEST,
  '__module__' : 'frontend.proto.laser_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.laser.FixLaserMetricsRequest)
  })
_sym_db.RegisterMessage(FixLaserMetricsRequest)


DESCRIPTOR._options = None

_LASERSERVICE = _descriptor.ServiceDescriptor(
  name='LaserService',
  full_name='carbon.frontend.laser.LaserService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=945,
  serialized_end=1657,
  methods=[
  _descriptor.MethodDescriptor(
    name='FireLaser',
    full_name='carbon.frontend.laser.LaserService.FireLaser',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_camera__pb2._CAMERAREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextLaserState',
    full_name='carbon.frontend.laser.LaserService.GetNextLaserState',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_LASERSTATELIST,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ToggleLaserEnabled',
    full_name='carbon.frontend.laser.LaserService.ToggleLaserEnabled',
    index=2,
    containing_service=None,
    input_type=_LASERDESCRIPTOR,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='EnableRow',
    full_name='carbon.frontend.laser.LaserService.EnableRow',
    index=3,
    containing_service=None,
    input_type=_ROWREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DisableRow',
    full_name='carbon.frontend.laser.LaserService.DisableRow',
    index=4,
    containing_service=None,
    input_type=_ROWREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ResetLaserMetrics',
    full_name='carbon.frontend.laser.LaserService.ResetLaserMetrics',
    index=5,
    containing_service=None,
    input_type=_LASERDESCRIPTOR,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FixLaserMetrics',
    full_name='carbon.frontend.laser.LaserService.FixLaserMetrics',
    index=6,
    containing_service=None,
    input_type=_FIXLASERMETRICSREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetLaserPower',
    full_name='carbon.frontend.laser.LaserService.SetLaserPower',
    index=7,
    containing_service=None,
    input_type=_SETLASERPOWERREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_LASERSERVICE)

DESCRIPTOR.services_by_name['LaserService'] = _LASERSERVICE

# @@protoc_insertion_point(module_scope)

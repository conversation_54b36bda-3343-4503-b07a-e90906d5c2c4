// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/features.proto
#ifndef GRPC_frontend_2fproto_2ffeatures_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2ffeatures_2eproto__INCLUDED

#include "frontend/proto/features.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace features {

class FeatureService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.features.FeatureService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::util::FeatureFlags* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::FeatureFlags>> AsyncGetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::FeatureFlags>>(AsyncGetNextFeatureFlagsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::FeatureFlags>> PrepareAsyncGetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::FeatureFlags>>(PrepareAsyncGetNextFeatureFlagsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::features::RobotConfiguration* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::features::RobotConfiguration>> AsyncGetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::features::RobotConfiguration>>(AsyncGetRobotConfigurationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::features::RobotConfiguration>> PrepareAsyncGetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::features::RobotConfiguration>>(PrepareAsyncGetRobotConfigurationRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::FeatureFlags>* AsyncGetNextFeatureFlagsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::FeatureFlags>* PrepareAsyncGetNextFeatureFlagsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::features::RobotConfiguration>* AsyncGetRobotConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::features::RobotConfiguration>* PrepareAsyncGetRobotConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::util::FeatureFlags* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::FeatureFlags>> AsyncGetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::FeatureFlags>>(AsyncGetNextFeatureFlagsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::FeatureFlags>> PrepareAsyncGetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::FeatureFlags>>(PrepareAsyncGetNextFeatureFlagsRaw(context, request, cq));
    }
    ::grpc::Status GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::features::RobotConfiguration* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::RobotConfiguration>> AsyncGetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::RobotConfiguration>>(AsyncGetRobotConfigurationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::RobotConfiguration>> PrepareAsyncGetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::RobotConfiguration>>(PrepareAsyncGetRobotConfigurationRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response, std::function<void(::grpc::Status)>) override;
      void GetNextFeatureFlags(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response, std::function<void(::grpc::Status)>) override;
      void GetRobotConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::FeatureFlags>* AsyncGetNextFeatureFlagsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::FeatureFlags>* PrepareAsyncGetNextFeatureFlagsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::RobotConfiguration>* AsyncGetRobotConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::RobotConfiguration>* PrepareAsyncGetRobotConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextFeatureFlags_;
    const ::grpc::internal::RpcMethod rpcmethod_GetRobotConfiguration_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextFeatureFlags(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response);
    virtual ::grpc::Status GetRobotConfiguration(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextFeatureFlags : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextFeatureFlags() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextFeatureFlags() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFeatureFlags(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::util::FeatureFlags* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextFeatureFlags(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::FeatureFlags>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetRobotConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetRobotConfiguration() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetRobotConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobotConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::features::RobotConfiguration* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRobotConfiguration(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::features::RobotConfiguration>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextFeatureFlags<WithAsyncMethod_GetRobotConfiguration<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextFeatureFlags : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextFeatureFlags() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::util::FeatureFlags>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::util::FeatureFlags* response) { return this->GetNextFeatureFlags(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextFeatureFlags(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::util::FeatureFlags>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::util::FeatureFlags>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextFeatureFlags() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFeatureFlags(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::util::FeatureFlags* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextFeatureFlags(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::util::FeatureFlags* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetRobotConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetRobotConfiguration() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::features::RobotConfiguration>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::features::RobotConfiguration* response) { return this->GetRobotConfiguration(context, request, response); }));}
    void SetMessageAllocatorFor_GetRobotConfiguration(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::features::RobotConfiguration>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::features::RobotConfiguration>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetRobotConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobotConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::features::RobotConfiguration* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRobotConfiguration(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::features::RobotConfiguration* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextFeatureFlags<WithCallbackMethod_GetRobotConfiguration<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextFeatureFlags : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextFeatureFlags() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextFeatureFlags() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFeatureFlags(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::util::FeatureFlags* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetRobotConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetRobotConfiguration() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetRobotConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobotConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::features::RobotConfiguration* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextFeatureFlags : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextFeatureFlags() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextFeatureFlags() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFeatureFlags(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::util::FeatureFlags* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextFeatureFlags(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetRobotConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetRobotConfiguration() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetRobotConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobotConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::features::RobotConfiguration* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetRobotConfiguration(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextFeatureFlags : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextFeatureFlags() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextFeatureFlags(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextFeatureFlags() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextFeatureFlags(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::util::FeatureFlags* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextFeatureFlags(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetRobotConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetRobotConfiguration() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetRobotConfiguration(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetRobotConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetRobotConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::features::RobotConfiguration* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetRobotConfiguration(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextFeatureFlags : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextFeatureFlags() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::util::FeatureFlags>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::util::FeatureFlags>* streamer) {
                       return this->StreamedGetNextFeatureFlags(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextFeatureFlags() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextFeatureFlags(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::util::FeatureFlags* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextFeatureFlags(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::util::FeatureFlags>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetRobotConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetRobotConfiguration() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::features::RobotConfiguration>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::features::RobotConfiguration>* streamer) {
                       return this->StreamedGetRobotConfiguration(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetRobotConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetRobotConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::features::RobotConfiguration* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetRobotConfiguration(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::features::RobotConfiguration>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextFeatureFlags<WithStreamedUnaryMethod_GetRobotConfiguration<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextFeatureFlags<WithStreamedUnaryMethod_GetRobotConfiguration<Service > > StreamedService;
};

}  // namespace features
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2ffeatures_2eproto__INCLUDED

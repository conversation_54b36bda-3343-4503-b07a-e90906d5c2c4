// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/actuation_tasks.proto

#include "frontend/proto/actuation_tasks.pb.h"
#include "frontend/proto/actuation_tasks.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace actuation_tasks {

static const char* ActuationTasksService_method_names[] = {
  "/carbon.frontend.actuation_tasks.ActuationTasksService/GetNextGlobalActuationTaskState",
  "/carbon.frontend.actuation_tasks.ActuationTasksService/StartGlobalAimbotActuationTask",
  "/carbon.frontend.actuation_tasks.ActuationTasksService/CancelGlobalAimbotActuationTask",
};

std::unique_ptr< ActuationTasksService::Stub> ActuationTasksService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ActuationTasksService::Stub> stub(new ActuationTasksService::Stub(channel, options));
  return stub;
}

ActuationTasksService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextGlobalActuationTaskState_(ActuationTasksService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartGlobalAimbotActuationTask_(ActuationTasksService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CancelGlobalAimbotActuationTask_(ActuationTasksService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ActuationTasksService::Stub::GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextGlobalActuationTaskState_, context, request, response);
}

void ActuationTasksService::Stub::async::GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextGlobalActuationTaskState_, context, request, response, std::move(f));
}

void ActuationTasksService::Stub::async::GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextGlobalActuationTaskState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* ActuationTasksService::Stub::PrepareAsyncGetNextGlobalActuationTaskStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextGlobalActuationTaskState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* ActuationTasksService::Stub::AsyncGetNextGlobalActuationTaskStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextGlobalActuationTaskStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ActuationTasksService::Stub::StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartGlobalAimbotActuationTask_, context, request, response);
}

void ActuationTasksService::Stub::async::StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartGlobalAimbotActuationTask_, context, request, response, std::move(f));
}

void ActuationTasksService::Stub::async::StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartGlobalAimbotActuationTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ActuationTasksService::Stub::PrepareAsyncStartGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartGlobalAimbotActuationTask_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ActuationTasksService::Stub::AsyncStartGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartGlobalAimbotActuationTaskRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ActuationTasksService::Stub::CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CancelGlobalAimbotActuationTask_, context, request, response);
}

void ActuationTasksService::Stub::async::CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelGlobalAimbotActuationTask_, context, request, response, std::move(f));
}

void ActuationTasksService::Stub::async::CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelGlobalAimbotActuationTask_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ActuationTasksService::Stub::PrepareAsyncCancelGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CancelGlobalAimbotActuationTask_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* ActuationTasksService::Stub::AsyncCancelGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCancelGlobalAimbotActuationTaskRaw(context, request, cq);
  result->StartCall();
  return result;
}

ActuationTasksService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ActuationTasksService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ActuationTasksService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ActuationTasksService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* resp) {
               return service->GetNextGlobalActuationTaskState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ActuationTasksService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ActuationTasksService::Service, ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ActuationTasksService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartGlobalAimbotActuationTask(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ActuationTasksService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ActuationTasksService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ActuationTasksService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->CancelGlobalAimbotActuationTask(ctx, req, resp);
             }, this)));
}

ActuationTasksService::Service::~Service() {
}

::grpc::Status ActuationTasksService::Service::GetNextGlobalActuationTaskState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ActuationTasksService::Service::StartGlobalAimbotActuationTask(::grpc::ServerContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ActuationTasksService::Service::CancelGlobalAimbotActuationTask(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace actuation_tasks


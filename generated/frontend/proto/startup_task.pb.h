// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/startup_task.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fstartup_5ftask_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fstartup_5ftask_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "proto/startup_task/startup_task.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fstartup_5ftask_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fstartup_5ftask_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fstartup_5ftask_2eproto;
namespace carbon {
namespace frontend {
namespace startup_task {
class GetNextTasksResponse;
struct GetNextTasksResponseDefaultTypeInternal;
extern GetNextTasksResponseDefaultTypeInternal _GetNextTasksResponse_default_instance_;
class MarkTaskCompleteRequest;
struct MarkTaskCompleteRequestDefaultTypeInternal;
extern MarkTaskCompleteRequestDefaultTypeInternal _MarkTaskCompleteRequest_default_instance_;
class MarkTaskCompleteResponse;
struct MarkTaskCompleteResponseDefaultTypeInternal;
extern MarkTaskCompleteResponseDefaultTypeInternal _MarkTaskCompleteResponse_default_instance_;
}  // namespace startup_task
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::startup_task::GetNextTasksResponse* Arena::CreateMaybeMessage<::carbon::frontend::startup_task::GetNextTasksResponse>(Arena*);
template<> ::carbon::frontend::startup_task::MarkTaskCompleteRequest* Arena::CreateMaybeMessage<::carbon::frontend::startup_task::MarkTaskCompleteRequest>(Arena*);
template<> ::carbon::frontend::startup_task::MarkTaskCompleteResponse* Arena::CreateMaybeMessage<::carbon::frontend::startup_task::MarkTaskCompleteResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace startup_task {

// ===================================================================

class GetNextTasksResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.startup_task.GetNextTasksResponse) */ {
 public:
  inline GetNextTasksResponse() : GetNextTasksResponse(nullptr) {}
  ~GetNextTasksResponse() override;
  explicit constexpr GetNextTasksResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextTasksResponse(const GetNextTasksResponse& from);
  GetNextTasksResponse(GetNextTasksResponse&& from) noexcept
    : GetNextTasksResponse() {
    *this = ::std::move(from);
  }

  inline GetNextTasksResponse& operator=(const GetNextTasksResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextTasksResponse& operator=(GetNextTasksResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextTasksResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextTasksResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextTasksResponse*>(
               &_GetNextTasksResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetNextTasksResponse& a, GetNextTasksResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextTasksResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextTasksResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextTasksResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextTasksResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextTasksResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextTasksResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextTasksResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.startup_task.GetNextTasksResponse";
  }
  protected:
  explicit GetNextTasksResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTasksFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.startup_task.Task tasks = 2;
  int tasks_size() const;
  private:
  int _internal_tasks_size() const;
  public:
  void clear_tasks();
  ::carbon::startup_task::Task* mutable_tasks(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::startup_task::Task >*
      mutable_tasks();
  private:
  const ::carbon::startup_task::Task& _internal_tasks(int index) const;
  ::carbon::startup_task::Task* _internal_add_tasks();
  public:
  const ::carbon::startup_task::Task& tasks(int index) const;
  ::carbon::startup_task::Task* add_tasks();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::startup_task::Task >&
      tasks() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.startup_task.GetNextTasksResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::startup_task::Task > tasks_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstartup_5ftask_2eproto;
};
// -------------------------------------------------------------------

class MarkTaskCompleteRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.startup_task.MarkTaskCompleteRequest) */ {
 public:
  inline MarkTaskCompleteRequest() : MarkTaskCompleteRequest(nullptr) {}
  ~MarkTaskCompleteRequest() override;
  explicit constexpr MarkTaskCompleteRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MarkTaskCompleteRequest(const MarkTaskCompleteRequest& from);
  MarkTaskCompleteRequest(MarkTaskCompleteRequest&& from) noexcept
    : MarkTaskCompleteRequest() {
    *this = ::std::move(from);
  }

  inline MarkTaskCompleteRequest& operator=(const MarkTaskCompleteRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarkTaskCompleteRequest& operator=(MarkTaskCompleteRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MarkTaskCompleteRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MarkTaskCompleteRequest* internal_default_instance() {
    return reinterpret_cast<const MarkTaskCompleteRequest*>(
               &_MarkTaskCompleteRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MarkTaskCompleteRequest& a, MarkTaskCompleteRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MarkTaskCompleteRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarkTaskCompleteRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MarkTaskCompleteRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MarkTaskCompleteRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MarkTaskCompleteRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MarkTaskCompleteRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MarkTaskCompleteRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.startup_task.MarkTaskCompleteRequest";
  }
  protected:
  explicit MarkTaskCompleteRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTaskIdFieldNumber = 1,
  };
  // string task_id = 1;
  void clear_task_id();
  const std::string& task_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_task_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_task_id();
  PROTOBUF_NODISCARD std::string* release_task_id();
  void set_allocated_task_id(std::string* task_id);
  private:
  const std::string& _internal_task_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_task_id(const std::string& value);
  std::string* _internal_mutable_task_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.startup_task.MarkTaskCompleteRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr task_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstartup_5ftask_2eproto;
};
// -------------------------------------------------------------------

class MarkTaskCompleteResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.startup_task.MarkTaskCompleteResponse) */ {
 public:
  inline MarkTaskCompleteResponse() : MarkTaskCompleteResponse(nullptr) {}
  explicit constexpr MarkTaskCompleteResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MarkTaskCompleteResponse(const MarkTaskCompleteResponse& from);
  MarkTaskCompleteResponse(MarkTaskCompleteResponse&& from) noexcept
    : MarkTaskCompleteResponse() {
    *this = ::std::move(from);
  }

  inline MarkTaskCompleteResponse& operator=(const MarkTaskCompleteResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MarkTaskCompleteResponse& operator=(MarkTaskCompleteResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MarkTaskCompleteResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const MarkTaskCompleteResponse* internal_default_instance() {
    return reinterpret_cast<const MarkTaskCompleteResponse*>(
               &_MarkTaskCompleteResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(MarkTaskCompleteResponse& a, MarkTaskCompleteResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MarkTaskCompleteResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MarkTaskCompleteResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MarkTaskCompleteResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MarkTaskCompleteResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const MarkTaskCompleteResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const MarkTaskCompleteResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.startup_task.MarkTaskCompleteResponse";
  }
  protected:
  explicit MarkTaskCompleteResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.startup_task.MarkTaskCompleteResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstartup_5ftask_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetNextTasksResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextTasksResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextTasksResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextTasksResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextTasksResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.startup_task.GetNextTasksResponse.ts)
  return _internal_ts();
}
inline void GetNextTasksResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.startup_task.GetNextTasksResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextTasksResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextTasksResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.startup_task.GetNextTasksResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextTasksResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextTasksResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.startup_task.GetNextTasksResponse.ts)
  return _msg;
}
inline void GetNextTasksResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.startup_task.GetNextTasksResponse.ts)
}

// repeated .carbon.startup_task.Task tasks = 2;
inline int GetNextTasksResponse::_internal_tasks_size() const {
  return tasks_.size();
}
inline int GetNextTasksResponse::tasks_size() const {
  return _internal_tasks_size();
}
inline ::carbon::startup_task::Task* GetNextTasksResponse::mutable_tasks(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.startup_task.GetNextTasksResponse.tasks)
  return tasks_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::startup_task::Task >*
GetNextTasksResponse::mutable_tasks() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.startup_task.GetNextTasksResponse.tasks)
  return &tasks_;
}
inline const ::carbon::startup_task::Task& GetNextTasksResponse::_internal_tasks(int index) const {
  return tasks_.Get(index);
}
inline const ::carbon::startup_task::Task& GetNextTasksResponse::tasks(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.startup_task.GetNextTasksResponse.tasks)
  return _internal_tasks(index);
}
inline ::carbon::startup_task::Task* GetNextTasksResponse::_internal_add_tasks() {
  return tasks_.Add();
}
inline ::carbon::startup_task::Task* GetNextTasksResponse::add_tasks() {
  ::carbon::startup_task::Task* _add = _internal_add_tasks();
  // @@protoc_insertion_point(field_add:carbon.frontend.startup_task.GetNextTasksResponse.tasks)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::startup_task::Task >&
GetNextTasksResponse::tasks() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.startup_task.GetNextTasksResponse.tasks)
  return tasks_;
}

// -------------------------------------------------------------------

// MarkTaskCompleteRequest

// string task_id = 1;
inline void MarkTaskCompleteRequest::clear_task_id() {
  task_id_.ClearToEmpty();
}
inline const std::string& MarkTaskCompleteRequest::task_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.startup_task.MarkTaskCompleteRequest.task_id)
  return _internal_task_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MarkTaskCompleteRequest::set_task_id(ArgT0&& arg0, ArgT... args) {
 
 task_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.startup_task.MarkTaskCompleteRequest.task_id)
}
inline std::string* MarkTaskCompleteRequest::mutable_task_id() {
  std::string* _s = _internal_mutable_task_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.startup_task.MarkTaskCompleteRequest.task_id)
  return _s;
}
inline const std::string& MarkTaskCompleteRequest::_internal_task_id() const {
  return task_id_.Get();
}
inline void MarkTaskCompleteRequest::_internal_set_task_id(const std::string& value) {
  
  task_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MarkTaskCompleteRequest::_internal_mutable_task_id() {
  
  return task_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MarkTaskCompleteRequest::release_task_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.startup_task.MarkTaskCompleteRequest.task_id)
  return task_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MarkTaskCompleteRequest::set_allocated_task_id(std::string* task_id) {
  if (task_id != nullptr) {
    
  } else {
    
  }
  task_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), task_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (task_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    task_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.startup_task.MarkTaskCompleteRequest.task_id)
}

// -------------------------------------------------------------------

// MarkTaskCompleteResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace startup_task
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fstartup_5ftask_2eproto

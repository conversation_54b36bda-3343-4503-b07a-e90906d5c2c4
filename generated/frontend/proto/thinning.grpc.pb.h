// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/thinning.proto
#ifndef GRPC_frontend_2fproto_2fthinning_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fthinning_2eproto__INCLUDED

#include "frontend/proto/thinning.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace thinning {

class ThinningService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.thinning.ThinningService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextConfigurationsResponse>> AsyncGetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextConfigurationsResponse>>(AsyncGetNextConfigurationsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextConfigurationsResponse>> PrepareAsyncGetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextConfigurationsResponse>>(PrepareAsyncGetNextConfigurationsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextActiveConfResponse>> AsyncGetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextActiveConfResponse>>(AsyncGetNextActiveConfRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextActiveConfResponse>> PrepareAsyncGetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextActiveConfResponse>>(PrepareAsyncGetNextActiveConfRaw(context, request, cq));
    }
    virtual ::grpc::Status DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::carbon::frontend::thinning::DefineConfigurationResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DefineConfigurationResponse>> AsyncDefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DefineConfigurationResponse>>(AsyncDefineConfigurationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DefineConfigurationResponse>> PrepareAsyncDefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DefineConfigurationResponse>>(PrepareAsyncDefineConfigurationRaw(context, request, cq));
    }
    virtual ::grpc::Status SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::carbon::frontend::thinning::SetActiveConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::SetActiveConfigResponse>> AsyncSetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::SetActiveConfigResponse>>(AsyncSetActiveConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::SetActiveConfigResponse>> PrepareAsyncSetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::SetActiveConfigResponse>>(PrepareAsyncSetActiveConfigRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::carbon::frontend::thinning::DeleteConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DeleteConfigResponse>> AsyncDeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DeleteConfigResponse>>(AsyncDeleteConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DeleteConfigResponse>> PrepareAsyncDeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DeleteConfigResponse>>(PrepareAsyncDeleteConfigRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextConfigurationsResponse>* AsyncGetNextConfigurationsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextConfigurationsResponse>* PrepareAsyncGetNextConfigurationsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextActiveConfResponse>* AsyncGetNextActiveConfRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::GetNextActiveConfResponse>* PrepareAsyncGetNextActiveConfRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DefineConfigurationResponse>* AsyncDefineConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DefineConfigurationResponse>* PrepareAsyncDefineConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::SetActiveConfigResponse>* AsyncSetActiveConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::SetActiveConfigResponse>* PrepareAsyncSetActiveConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DeleteConfigResponse>* AsyncDeleteConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::thinning::DeleteConfigResponse>* PrepareAsyncDeleteConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextConfigurationsResponse>> AsyncGetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextConfigurationsResponse>>(AsyncGetNextConfigurationsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextConfigurationsResponse>> PrepareAsyncGetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextConfigurationsResponse>>(PrepareAsyncGetNextConfigurationsRaw(context, request, cq));
    }
    ::grpc::Status GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextActiveConfResponse>> AsyncGetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextActiveConfResponse>>(AsyncGetNextActiveConfRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextActiveConfResponse>> PrepareAsyncGetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextActiveConfResponse>>(PrepareAsyncGetNextActiveConfRaw(context, request, cq));
    }
    ::grpc::Status DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::carbon::frontend::thinning::DefineConfigurationResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DefineConfigurationResponse>> AsyncDefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DefineConfigurationResponse>>(AsyncDefineConfigurationRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DefineConfigurationResponse>> PrepareAsyncDefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DefineConfigurationResponse>>(PrepareAsyncDefineConfigurationRaw(context, request, cq));
    }
    ::grpc::Status SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::carbon::frontend::thinning::SetActiveConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::SetActiveConfigResponse>> AsyncSetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::SetActiveConfigResponse>>(AsyncSetActiveConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::SetActiveConfigResponse>> PrepareAsyncSetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::SetActiveConfigResponse>>(PrepareAsyncSetActiveConfigRaw(context, request, cq));
    }
    ::grpc::Status DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::carbon::frontend::thinning::DeleteConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DeleteConfigResponse>> AsyncDeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DeleteConfigResponse>>(AsyncDeleteConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DeleteConfigResponse>> PrepareAsyncDeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DeleteConfigResponse>>(PrepareAsyncDeleteConfigRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response, std::function<void(::grpc::Status)>) override;
      void DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextConfigurationsResponse>* AsyncGetNextConfigurationsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextConfigurationsResponse>* PrepareAsyncGetNextConfigurationsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextActiveConfResponse>* AsyncGetNextActiveConfRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextActiveConfResponse>* PrepareAsyncGetNextActiveConfRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DefineConfigurationResponse>* AsyncDefineConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DefineConfigurationResponse>* PrepareAsyncDefineConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::SetActiveConfigResponse>* AsyncSetActiveConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::SetActiveConfigResponse>* PrepareAsyncSetActiveConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DeleteConfigResponse>* AsyncDeleteConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DeleteConfigResponse>* PrepareAsyncDeleteConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextConfigurations_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextActiveConf_;
    const ::grpc::internal::RpcMethod rpcmethod_DefineConfiguration_;
    const ::grpc::internal::RpcMethod rpcmethod_SetActiveConfig_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteConfig_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextConfigurations(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response);
    virtual ::grpc::Status GetNextActiveConf(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response);
    virtual ::grpc::Status DefineConfiguration(::grpc::ServerContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response);
    virtual ::grpc::Status SetActiveConfig(::grpc::ServerContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response);
    virtual ::grpc::Status DeleteConfig(::grpc::ServerContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextConfigurations : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextConfigurations() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextConfigurations() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigurations(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextConfigurationsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextConfigurations(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::thinning::GetNextConfigurationsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextActiveConf : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextActiveConf() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextActiveConf() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveConf(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextActiveConfResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveConf(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::thinning::GetNextActiveConfResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DefineConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DefineConfiguration() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_DefineConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DefineConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DefineConfigurationRequest* /*request*/, ::carbon::frontend::thinning::DefineConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDefineConfiguration(::grpc::ServerContext* context, ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::thinning::DefineConfigurationResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetActiveConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetActiveConfig() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SetActiveConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::SetActiveConfigRequest* /*request*/, ::carbon::frontend::thinning::SetActiveConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveConfig(::grpc::ServerContext* context, ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::thinning::SetActiveConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteConfig() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_DeleteConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DeleteConfigRequest* /*request*/, ::carbon::frontend::thinning::DeleteConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteConfig(::grpc::ServerContext* context, ::carbon::frontend::thinning::DeleteConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::thinning::DeleteConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextConfigurations<WithAsyncMethod_GetNextActiveConf<WithAsyncMethod_DefineConfiguration<WithAsyncMethod_SetActiveConfig<WithAsyncMethod_DeleteConfig<Service > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextConfigurations : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextConfigurations() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextConfigurationsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response) { return this->GetNextConfigurations(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextConfigurations(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextConfigurationsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextConfigurationsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextConfigurations() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigurations(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextConfigurationsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextConfigurations(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextConfigurationsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextActiveConf : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextActiveConf() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextActiveConfResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response) { return this->GetNextActiveConf(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextActiveConf(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextActiveConfResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextActiveConfResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextActiveConf() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveConf(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextActiveConfResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveConf(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextActiveConfResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DefineConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DefineConfiguration() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::thinning::DefineConfigurationRequest, ::carbon::frontend::thinning::DefineConfigurationResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response) { return this->DefineConfiguration(context, request, response); }));}
    void SetMessageAllocatorFor_DefineConfiguration(
        ::grpc::MessageAllocator< ::carbon::frontend::thinning::DefineConfigurationRequest, ::carbon::frontend::thinning::DefineConfigurationResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::thinning::DefineConfigurationRequest, ::carbon::frontend::thinning::DefineConfigurationResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DefineConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DefineConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DefineConfigurationRequest* /*request*/, ::carbon::frontend::thinning::DefineConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DefineConfiguration(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::thinning::DefineConfigurationRequest* /*request*/, ::carbon::frontend::thinning::DefineConfigurationResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetActiveConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetActiveConfig() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::thinning::SetActiveConfigRequest, ::carbon::frontend::thinning::SetActiveConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response) { return this->SetActiveConfig(context, request, response); }));}
    void SetMessageAllocatorFor_SetActiveConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::thinning::SetActiveConfigRequest, ::carbon::frontend::thinning::SetActiveConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::thinning::SetActiveConfigRequest, ::carbon::frontend::thinning::SetActiveConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetActiveConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::SetActiveConfigRequest* /*request*/, ::carbon::frontend::thinning::SetActiveConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::thinning::SetActiveConfigRequest* /*request*/, ::carbon::frontend::thinning::SetActiveConfigResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteConfig() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::thinning::DeleteConfigRequest, ::carbon::frontend::thinning::DeleteConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response) { return this->DeleteConfig(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteConfig(
        ::grpc::MessageAllocator< ::carbon::frontend::thinning::DeleteConfigRequest, ::carbon::frontend::thinning::DeleteConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::thinning::DeleteConfigRequest, ::carbon::frontend::thinning::DeleteConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DeleteConfigRequest* /*request*/, ::carbon::frontend::thinning::DeleteConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::thinning::DeleteConfigRequest* /*request*/, ::carbon::frontend::thinning::DeleteConfigResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextConfigurations<WithCallbackMethod_GetNextActiveConf<WithCallbackMethod_DefineConfiguration<WithCallbackMethod_SetActiveConfig<WithCallbackMethod_DeleteConfig<Service > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextConfigurations : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextConfigurations() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextConfigurations() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigurations(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextConfigurationsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextActiveConf : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextActiveConf() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextActiveConf() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveConf(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextActiveConfResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DefineConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DefineConfiguration() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_DefineConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DefineConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DefineConfigurationRequest* /*request*/, ::carbon::frontend::thinning::DefineConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetActiveConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetActiveConfig() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SetActiveConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::SetActiveConfigRequest* /*request*/, ::carbon::frontend::thinning::SetActiveConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteConfig() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_DeleteConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DeleteConfigRequest* /*request*/, ::carbon::frontend::thinning::DeleteConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextConfigurations : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextConfigurations() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextConfigurations() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigurations(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextConfigurationsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextConfigurations(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextActiveConf : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextActiveConf() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextActiveConf() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveConf(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextActiveConfResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveConf(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DefineConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DefineConfiguration() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_DefineConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DefineConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DefineConfigurationRequest* /*request*/, ::carbon::frontend::thinning::DefineConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDefineConfiguration(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetActiveConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetActiveConfig() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SetActiveConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::SetActiveConfigRequest* /*request*/, ::carbon::frontend::thinning::SetActiveConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteConfig() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_DeleteConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DeleteConfigRequest* /*request*/, ::carbon::frontend::thinning::DeleteConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextConfigurations : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextConfigurations() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextConfigurations(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextConfigurations() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextConfigurations(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextConfigurationsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextConfigurations(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextActiveConf : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextActiveConf() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextActiveConf(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextActiveConf() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveConf(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextActiveConfResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveConf(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DefineConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DefineConfiguration() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DefineConfiguration(context, request, response); }));
    }
    ~WithRawCallbackMethod_DefineConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DefineConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DefineConfigurationRequest* /*request*/, ::carbon::frontend::thinning::DefineConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DefineConfiguration(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetActiveConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetActiveConfig() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetActiveConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetActiveConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::SetActiveConfigRequest* /*request*/, ::carbon::frontend::thinning::SetActiveConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteConfig() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DeleteConfigRequest* /*request*/, ::carbon::frontend::thinning::DeleteConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextConfigurations : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextConfigurations() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextConfigurationsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextConfigurationsResponse>* streamer) {
                       return this->StreamedGetNextConfigurations(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextConfigurations() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextConfigurations(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextConfigurationsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextConfigurations(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::thinning::GetNextConfigurationsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextActiveConf : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextActiveConf() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextActiveConfResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextActiveConfResponse>* streamer) {
                       return this->StreamedGetNextActiveConf(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextActiveConf() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextActiveConf(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::thinning::GetNextActiveConfResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextActiveConf(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::thinning::GetNextActiveConfResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DefineConfiguration : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DefineConfiguration() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::thinning::DefineConfigurationRequest, ::carbon::frontend::thinning::DefineConfigurationResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::thinning::DefineConfigurationRequest, ::carbon::frontend::thinning::DefineConfigurationResponse>* streamer) {
                       return this->StreamedDefineConfiguration(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DefineConfiguration() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DefineConfiguration(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DefineConfigurationRequest* /*request*/, ::carbon::frontend::thinning::DefineConfigurationResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDefineConfiguration(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::thinning::DefineConfigurationRequest,::carbon::frontend::thinning::DefineConfigurationResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetActiveConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetActiveConfig() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::thinning::SetActiveConfigRequest, ::carbon::frontend::thinning::SetActiveConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::thinning::SetActiveConfigRequest, ::carbon::frontend::thinning::SetActiveConfigResponse>* streamer) {
                       return this->StreamedSetActiveConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetActiveConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetActiveConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::SetActiveConfigRequest* /*request*/, ::carbon::frontend::thinning::SetActiveConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetActiveConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::thinning::SetActiveConfigRequest,::carbon::frontend::thinning::SetActiveConfigResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteConfig() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::thinning::DeleteConfigRequest, ::carbon::frontend::thinning::DeleteConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::thinning::DeleteConfigRequest, ::carbon::frontend::thinning::DeleteConfigResponse>* streamer) {
                       return this->StreamedDeleteConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteConfig(::grpc::ServerContext* /*context*/, const ::carbon::frontend::thinning::DeleteConfigRequest* /*request*/, ::carbon::frontend::thinning::DeleteConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::thinning::DeleteConfigRequest,::carbon::frontend::thinning::DeleteConfigResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextConfigurations<WithStreamedUnaryMethod_GetNextActiveConf<WithStreamedUnaryMethod_DefineConfiguration<WithStreamedUnaryMethod_SetActiveConfig<WithStreamedUnaryMethod_DeleteConfig<Service > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextConfigurations<WithStreamedUnaryMethod_GetNextActiveConf<WithStreamedUnaryMethod_DefineConfiguration<WithStreamedUnaryMethod_SetActiveConfig<WithStreamedUnaryMethod_DeleteConfig<Service > > > > > StreamedService;
};

}  // namespace thinning
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fthinning_2eproto__INCLUDED

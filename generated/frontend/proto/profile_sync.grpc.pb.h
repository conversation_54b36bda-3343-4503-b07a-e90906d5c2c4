// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/profile_sync.proto
#ifndef GRPC_frontend_2fproto_2fprofile_5fsync_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fprofile_5fsync_2eproto__INCLUDED

#include "frontend/proto/profile_sync.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace profile_sync {

}  // namespace profile_sync
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fprofile_5fsync_2eproto__INCLUDED

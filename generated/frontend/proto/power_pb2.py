# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/power.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/power.proto',
  package='carbon.frontend.power',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1a\x66rontend/proto/power.proto\x12\x15\x63\x61rbon.frontend.power\x1a\x19\x66rontend/proto/util.proto\"\xdd\x02\n\x0c\x44\x65viceStatus\x12-\n\x06\x64\x65vice\x18\x01 \x01(\x0e\x32\x1d.carbon.frontend.power.Device\x12\r\n\x05label\x18\x02 \x01(\t\x12\x45\n\nrelay_type\x18\x06 \x01(\x0b\x32/.carbon.frontend.power.DeviceStatus.RelayStatusH\x00\x12G\n\x0bsensor_type\x18\x07 \x01(\x0b\x32\x30.carbon.frontend.power.DeviceStatus.SensorStatusH\x00\x1a\x1f\n\x0bRelayStatus\x12\x10\n\x08\x64isabled\x18\x03 \x01(\x08\x1aV\n\x0cSensorStatus\x12\x0e\n\x06status\x18\x04 \x01(\t\x12\x36\n\x05\x63olor\x18\x05 \x01(\x0e\x32\'.carbon.frontend.power.DeviceValueColorB\x06\n\x04type\"A\n\x12PowerStatusRequest\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"x\n\x13PowerStatusResponse\x12\x34\n\x07\x64\x65vices\x18\x01 \x03(\x0b\x32#.carbon.frontend.power.DeviceStatus\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\".\n\x0eValueWithRange\x12\r\n\x05value\x18\x01 \x01(\x01\x12\r\n\x05is_ok\x18\x02 \x01(\x08\"\xd0\x01\n\x17\x45nvironmentalSensorData\x12<\n\rtemperature_c\x18\x01 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12:\n\x0bhumidity_rh\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0cpressure_hpa\x18\x03 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\"\x8e\x01\n\x11\x43oolantSensorData\x12<\n\rtemperature_c\x18\x01 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0cpressure_kpa\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\"\xad\x01\n\x10NetworkPortState\x12\x0f\n\x07link_up\x18\x01 \x01(\x08\x12\x42\n\x11\x61\x63tual_link_speed\x18\x02 \x01(\x0e\x32\'.carbon.frontend.power.NetworkLinkSpeed\x12\x44\n\x13\x65xpected_link_speed\x18\x03 \x01(\x0e\x32\'.carbon.frontend.power.NetworkLinkSpeed\"\x97\t\n\x12ReaperPcSensorData\x12\x45\n\x16temperature_cpu_core_c\x18\x01 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x43\n\x14temperature_system_c\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12G\n\x13temperature_gpu_1_c\x18\x03 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRangeH\x00\x88\x01\x01\x12G\n\x13temperature_gpu_2_c\x18\x04 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRangeH\x01\x88\x01\x01\x12\x36\n\x07psu_12v\x18\x05 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x35\n\x06psu_5v\x18\x06 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x36\n\x07psu_3v3\x18\x07 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x33\n\x04load\x18\x08 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x0e\n\x06uptime\x18\t \x01(\r\x12@\n\x11ram_usage_percent\x18\n \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x41\n\x12\x64isk_usage_percent\x18\x0b \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12?\n\x0escanner_a_link\x18\x0c \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12?\n\x0escanner_b_link\x18\r \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12\x42\n\x11target_cam_a_link\x18\x0e \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12\x42\n\x11target_cam_b_link\x18\x0f \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12\x41\n\x10predict_cam_link\x18\x10 \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12:\n\tipmi_link\x18\x11 \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortState\x12\x39\n\x08\x65xt_link\x18\x12 \x01(\x0b\x32\'.carbon.frontend.power.NetworkPortStateB\x16\n\x14_temperature_gpu_1_cB\x16\n\x14_temperature_gpu_2_c\"\x92\x02\n\x18ReaperScannerLaserStatus\x12\r\n\x05model\x18\x01 \x01(\t\x12\n\n\x02sn\x18\x02 \x01(\t\x12\x13\n\x0brated_power\x18\x03 \x01(\r\x12<\n\rtemperature_c\x18\x04 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x37\n\x08humidity\x18\x05 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12?\n\x10laser_current_ma\x18\x06 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x0e\n\x06\x66\x61ults\x18\x07 \x03(\t\"\x8d\x02\n\x16ReaperScannerMotorData\x12\x15\n\rcontroller_sn\x18\x01 \x01(\t\x12\x43\n\x14temperature_output_c\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0emotor_supply_v\x18\x03 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12>\n\x0fmotor_current_a\x18\x04 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x18\n\x10\x65ncoder_position\x18\x05 \x01(\x03\"\xce\x06\n\x17ReaperScannerSensorData\x12\x12\n\nscanner_sn\x18\x01 \x01(\t\x12\x38\n\tcurrent_a\x18\x02 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x14\n\x0c\x66use_tripped\x18\x03 \x01(\x08\x12G\n\x18temperature_collimator_c\x18\x04 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x42\n\x13temperature_fiber_c\x18\x05 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12<\n\rlaser_power_w\x18\x06 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x17\n\x0flaser_connected\x18\x07 \x01(\x08\x12J\n\x0claser_status\x18\x08 \x01(\x0b\x32/.carbon.frontend.power.ReaperScannerLaserStatusH\x00\x88\x01\x01\x12\x18\n\x10target_connected\x18\t \x01(\x08\x12\x16\n\ttarget_sn\x18\n \x01(\tH\x01\x88\x01\x01\x12H\n\x14temperature_target_c\x18\x0b \x01(\x0b\x32%.carbon.frontend.power.ValueWithRangeH\x02\x88\x01\x01\x12\x45\n\tmotor_pan\x18\x0c \x01(\x0b\x32-.carbon.frontend.power.ReaperScannerMotorDataH\x03\x88\x01\x01\x12\x46\n\nmotor_tilt\x18\r \x01(\x0b\x32-.carbon.frontend.power.ReaperScannerMotorDataH\x04\x88\x01\x01\x12\x1d\n\x15scanner_power_enabled\x18\x0e \x01(\x08\x12 \n\x18target_cam_power_enabled\x18\x0f \x01(\x08\x42\x0f\n\r_laser_statusB\x0c\n\n_target_snB\x17\n\x15_temperature_target_cB\x0c\n\n_motor_panB\r\n\x0b_motor_tilt\"E\n\rReaperGpsData\x12\x0f\n\x07has_fix\x18\x01 \x01(\x08\x12\x10\n\x08latitude\x18\x02 \x01(\x02\x12\x11\n\tlongitude\x18\x03 \x01(\x02\"A\n\x16ReaperWheelEncoderData\x12\x12\n\nfront_left\x18\x01 \x01(\x03\x12\x13\n\x0b\x66ront_right\x18\x02 \x01(\x03\"\x84\x0e\n\x19ReaperCenterEnclosureData\x12\x1c\n\x14water_protect_status\x18\x01 \x01(\x08\x12 \n\x18main_contactor_status_fb\x18\x02 \x01(\x08\x12:\n\x0cpower_status\x18\x03 \x01(\x0e\x32$.carbon.frontend.power.AcPowerStatus\x12\x15\n\rlifted_status\x18\x04 \x01(\x08\x12 \n\x14temp_humidity_status\x18\x05 \x01(\x08\x42\x02\x18\x01\x12\x15\n\rtractor_power\x18\x06 \x01(\x08\x12;\n\x0c\x61\x63_frequency\x18\x07 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0e\x61\x63_voltage_a_b\x18\x08 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0e\x61\x63_voltage_b_c\x18\t \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0e\x61\x63_voltage_a_c\x18\n \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0c\x61\x63_voltage_a\x18\x0b \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0c\x61\x63_voltage_b\x18\x0c \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12;\n\x0c\x61\x63_voltage_c\x18\r \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x17\n\x0fphase_power_w_3\x18\x0e \x01(\x03\x12\x18\n\x10phase_power_va_3\x18\x0f \x01(\x03\x12;\n\x0cpower_factor\x18\x10 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x42\n\x13server_cabinet_temp\x18\x11 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x46\n\x17server_cabinet_humidity\x18\x12 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x42\n\x13\x62\x61ttery_voltage_12v\x18\x13 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x1e\n\x16wheel_encoder_disabled\x18\x14 \x01(\x08\x12\x1b\n\x0fstrobe_disabled\x18\x15 \x01(\x08\x42\x02\x18\x01\x12\x14\n\x0cgps_disabled\x18\x16 \x01(\x08\x12\x1f\n\x17main_contactor_disabled\x18\x17 \x01(\x08\x12 \n\x18\x61ir_conditioner_disabled\x18\x18 \x01(\x08\x12\x18\n\x10\x63hiller_disabled\x18\x19 \x01(\x08\x12\x16\n\x0e\x63hiller_alarms\x18\x1a \x03(\t\x12=\n\x0e\x63hiller_temp_c\x18\x1b \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x41\n\x12\x63hiller_flow_l_min\x18\x1c \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x43\n\x14\x63hiller_pressure_psi\x18\x1d \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12I\n\x1a\x63hiller_conductivity_us_cm\x18\x1e \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x41\n\x12\x63hiller_set_temp_c\x18\x1f \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12I\n\x1a\x63hiller_heat_transfer_kbtu\x18  \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12I\n\x1a\x63hiller_fluid_delta_temp_c\x18! \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x31\n\x03gps\x18\" \x01(\x0b\x32$.carbon.frontend.power.ReaperGpsData\x12\x44\n\rwheel_encoder\x18# \x01(\x0b\x32-.carbon.frontend.power.ReaperWheelEncoderData\"\xca\x07\n\x16ReaperModuleSensorData\x12\x11\n\tmodule_id\x18\x01 \x01(\x05\x12\x11\n\tmodule_sn\x18\x02 \x01(\t\x12H\n\x10\x65nviro_enclosure\x18\x03 \x01(\x0b\x32..carbon.frontend.power.EnvironmentalSensorData\x12\x41\n\tenviro_pc\x18\x04 \x01(\x0b\x32..carbon.frontend.power.EnvironmentalSensorData\x12?\n\rcoolant_inlet\x18\x05 \x01(\x0b\x32(.carbon.frontend.power.CoolantSensorData\x12@\n\x0e\x63oolant_outlet\x18\x06 \x01(\x0b\x32(.carbon.frontend.power.CoolantSensorData\x12\x43\n\x14strobe_temperature_c\x18\x07 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12\x41\n\x12strobe_cap_voltage\x18\x08 \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12=\n\x0estrobe_current\x18\t \x01(\x0b\x32%.carbon.frontend.power.ValueWithRange\x12:\n\x02pc\x18\n \x01(\x0b\x32).carbon.frontend.power.ReaperPcSensorDataH\x00\x88\x01\x01\x12\x46\n\tscanner_a\x18\x0b \x01(\x0b\x32..carbon.frontend.power.ReaperScannerSensorDataH\x01\x88\x01\x01\x12\x46\n\tscanner_b\x18\x0c \x01(\x0b\x32..carbon.frontend.power.ReaperScannerSensorDataH\x02\x88\x01\x01\x12\x18\n\x10pc_power_enabled\x18\r \x01(\x08\x12\x1c\n\x14lasers_power_enabled\x18\x0e \x01(\x08\x12!\n\x19predict_cam_power_enabled\x18\x0f \x01(\x08\x12\x1c\n\x14strobe_power_enabled\x18\x10 \x01(\x08\x12\x16\n\x0estrobe_enabled\x18\x11 \x01(\x08\x12\x33\n\x06status\x18\x12 \x01(\x0e\x32#.carbon.frontend.power.ModuleStatusB\x05\n\x03_pcB\x0c\n\n_scanner_aB\x0c\n\n_scanner_b\"\x1e\n\x1c\x43\x65nterEnclosureStatusRequest\"0\n\x1bModuleHardwareStatusRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\x05\"\x81\x02\n\"GetNextReaperHardwareStatusRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12V\n\x17\x63\x65nter_enclosure_status\x18\x02 \x01(\x0b\x32\x33.carbon.frontend.power.CenterEnclosureStatusRequestH\x00\x12K\n\rmodule_status\x18\x03 \x01(\x0b\x32\x32.carbon.frontend.power.ModuleHardwareStatusRequestH\x00\x42\t\n\x07request\"\xfb\x01\n#GetNextReaperHardwareStatusResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12S\n\x17\x63\x65nter_enclosure_status\x18\x02 \x01(\x0b\x32\x30.carbon.frontend.power.ReaperCenterEnclosureDataH\x00\x12\x46\n\rmodule_status\x18\x03 \x01(\x0b\x32-.carbon.frontend.power.ReaperModuleSensorDataH\x00\x42\n\n\x08response\"T\n%GetNextReaperAllHardwareStatusRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\xee\x01\n&GetNextReaperAllHardwareStatusResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12Q\n\x17\x63\x65nter_enclosure_status\x18\x02 \x01(\x0b\x32\x30.carbon.frontend.power.ReaperCenterEnclosureData\x12\x44\n\rmodule_status\x18\x03 \x03(\x0b\x32-.carbon.frontend.power.ReaperModuleSensorData\"=\n\x0cRelayRequest\x12-\n\x06\x64\x65vice\x18\x01 \x01(\x0e\x32\x1d.carbon.frontend.power.Device\" \n\rRelayResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x95\x01\n\x1cSetReaperScannerPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x1c\n\x0fscanner_a_power\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1c\n\x0fscanner_b_power\x18\x03 \x01(\x08H\x01\x88\x01\x01\x42\x12\n\x10_scanner_a_powerB\x12\n\x10_scanner_b_power\"0\n\x1dSetReaperScannerPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"\x90\x01\n\x1bSetReaperTargetPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x1b\n\x0etarget_a_power\x18\x02 \x01(\x08H\x00\x88\x01\x01\x12\x1b\n\x0etarget_b_power\x18\x03 \x01(\x08H\x01\x88\x01\x01\x42\x11\n\x0f_target_a_powerB\x11\n\x0f_target_b_power\"/\n\x1cSetReaperTargetPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"E\n\x1fSetReaperPredictCamPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"3\n SetReaperPredictCamPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"B\n\x1cSetReaperStrobeEnableRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"0\n\x1dSetReaperStrobeEnableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"3\n SetReaperAllStrobesEnableRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"4\n!SetReaperAllStrobesEnableResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"C\n\x1dSetReaperModulePcPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"1\n\x1eSetReaperModulePcPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08\"F\n SetReaperModuleLaserPowerRequest\x12\x11\n\tmodule_id\x18\x01 \x01(\r\x12\x0f\n\x07\x65nabled\x18\x02 \x01(\x08\"4\n!SetReaperModuleLaserPowerResponse\x12\x0f\n\x07success\x18\x01 \x01(\x08*\xd1\x07\n\x06\x44\x65vice\x12\x11\n\rSENSOR_LIFTED\x10\x00\x12\x1d\n\x19SENSOR_SERVER_TEMPERATURE\x10\x01\x12\x1a\n\x16SENSOR_SERVER_HUMIDITY\x10\x02\x12\x10\n\x0cSENSOR_WATER\x10\x03\x12\x16\n\x12SENSOR_12V_BATTERY\x10\x04\x12\x18\n\x14SENSOR_POWER_QUALITY\x10\x05\x12\x12\n\x0eSENSOR_TRACTOR\x10\x06\x12\x17\n\x13SENSOR_AC_FREQUENCY\x10\x07\x12\x15\n\x11SENSOR_AB_VOLTAGE\x10\x08\x12\x15\n\x11SENSOR_BC_VOLTAGE\x10\t\x12\x15\n\x11SENSOR_AC_VOLTAGE\x10\n\x12\x14\n\x10SENSOR_A_CURRENT\x10\x0b\x12\x14\n\x10SENSOR_B_CURRENT\x10\x0c\x12\x14\n\x10SENSOR_C_CURRENT\x10\r\x12\x11\n\rRELAY_SUICIDE\x10\x0e\x12\x10\n\x0cRELAY_REBOOT\x10\x0f\x12\x0e\n\nRELAY_MAIN\x10\x10\x12\x0f\n\x0bRELAY_ROW_1\x10\x11\x12\x0f\n\x0bRELAY_ROW_2\x10\x12\x12\x0f\n\x0bRELAY_ROW_3\x10\x13\x12\x12\n\x0eRELAY_LIGHTS_1\x10\x14\x12\x12\n\x0eRELAY_LIGHTS_2\x10\x15\x12\x12\n\x0eRELAY_LIGHTS_3\x10\x16\x12\x13\n\x0fRELAY_SCANNER_1\x10\x17\x12\x13\n\x0fRELAY_SCANNER_2\x10\x18\x12\x13\n\x0fRELAY_SCANNER_3\x10\x19\x12\x0c\n\x08RELAY_AC\x10\x1a\x12\x11\n\rRELAY_CHILLER\x10\x1b\x12\x10\n\x0cRELAY_STROBE\x10\x1c\x12\x1c\n\x18RELAY_ENCODER_FRONT_LEFT\x10\x1d\x12\x1d\n\x19RELAY_ENCODER_FRONT_RIGHT\x10\x1e\x12\x1b\n\x17RELAY_ENCODER_BACK_LEFT\x10\x1f\x12\x1c\n\x18RELAY_ENCODER_BACK_RIGHT\x10 \x12\x1d\n\x19SENSOR_ENCODER_FRONT_LEFT\x10!\x12\x1e\n\x1aSENSOR_ENCODER_FRONT_RIGHT\x10\"\x12\x1c\n\x18SENSOR_ENCODER_BACK_LEFT\x10#\x12\x1d\n\x19SENSOR_ENCODER_BACK_RIGHT\x10$\x12\r\n\tRELAY_GPS\x10%\x12\x13\n\x0fSENSOR_LATITUDE\x10&\x12\x14\n\x10SENSOR_LONGITUDE\x10\'\x12\x0e\n\nSENSOR_KEY\x10(\x12\x14\n\x10SENSOR_INTERLOCK\x10)\x12\x17\n\x13RELAY_ENCODER_BOARD\x10**T\n\x10\x44\x65viceValueColor\x12\x0e\n\nCOLOR_GRAY\x10\x00\x12\x0f\n\x0b\x43OLOR_GREEN\x10\x01\x12\x10\n\x0c\x43OLOR_ORANGE\x10\x02\x12\r\n\tCOLOR_RED\x10\x03*\xbf\x01\n\x10NetworkLinkSpeed\x12\x0b\n\x07UNKNOWN\x10\x00\x12\x12\n\x0eSPEED_10M_HALF\x10\x01\x12\x12\n\x0eSPEED_10M_FULL\x10\x02\x12\x13\n\x0fSPEED_100M_HALF\x10\x03\x12\x13\n\x0fSPEED_100M_FULL\x10\x04\x12\x11\n\rSPEED_1G_FULL\x10\x05\x12\x12\n\x0eSPEED_2G5_FULL\x10\x06\x12\x11\n\rSPEED_5G_FULL\x10\x07\x12\x12\n\x0eSPEED_10G_FULL\x10\x08*U\n\rAcPowerStatus\x12\x11\n\rPOWER_UNKNOWN\x10\x00\x12\x0e\n\nPOWER_GOOD\x10\x01\x12\r\n\tPOWER_BAD\x10\x02\x12\x12\n\x0ePOWER_VERY_BAD\x10\x03*!\n\x0cModuleStatus\x12\x06\n\x02OK\x10\x00\x12\t\n\x05\x45rror\x10\x01\x32\xbb\x0c\n\x0cPowerService\x12k\n\x12GetNextPowerStatus\x12).carbon.frontend.power.PowerStatusRequest\x1a*.carbon.frontend.power.PowerStatusResponse\x12Z\n\rTurnOffDevice\x12#.carbon.frontend.power.RelayRequest\x1a$.carbon.frontend.power.RelayResponse\x12Y\n\x0cTurnOnDevice\x12#.carbon.frontend.power.RelayRequest\x1a$.carbon.frontend.power.RelayResponse\x12\x9d\x01\n\x1eGetNextReaperAllHardwareStatus\x12<.carbon.frontend.power.GetNextReaperAllHardwareStatusRequest\x1a=.carbon.frontend.power.GetNextReaperAllHardwareStatusResponse\x12\x94\x01\n\x1bGetNextReaperHardwareStatus\x12\x39.carbon.frontend.power.GetNextReaperHardwareStatusRequest\x1a:.carbon.frontend.power.GetNextReaperHardwareStatusResponse\x12\x84\x01\n\x15SetReaperScannerPower\x12\x33.carbon.frontend.power.SetReaperScannerPowerRequest\x1a\x34.carbon.frontend.power.SetReaperScannerPowerResponse\"\x00\x12\x81\x01\n\x14SetReaperTargetPower\x12\x32.carbon.frontend.power.SetReaperTargetPowerRequest\x1a\x33.carbon.frontend.power.SetReaperTargetPowerResponse\"\x00\x12\x8d\x01\n\x18SetReaperPredictCamPower\x12\x36.carbon.frontend.power.SetReaperPredictCamPowerRequest\x1a\x37.carbon.frontend.power.SetReaperPredictCamPowerResponse\"\x00\x12\x84\x01\n\x15SetReaperStrobeEnable\x12\x33.carbon.frontend.power.SetReaperStrobeEnableRequest\x1a\x34.carbon.frontend.power.SetReaperStrobeEnableResponse\"\x00\x12\x90\x01\n\x19SetReaperAllStrobesEnable\x12\x37.carbon.frontend.power.SetReaperAllStrobesEnableRequest\x1a\x38.carbon.frontend.power.SetReaperAllStrobesEnableResponse\"\x00\x12\x87\x01\n\x16SetReaperModulePcPower\x12\x34.carbon.frontend.power.SetReaperModulePcPowerRequest\x1a\x35.carbon.frontend.power.SetReaperModulePcPowerResponse\"\x00\x12\x90\x01\n\x19SetReaperModuleLaserPower\x12\x37.carbon.frontend.power.SetReaperModuleLaserPowerRequest\x1a\x38.carbon.frontend.power.SetReaperModuleLaserPowerResponse\"\x00\x42\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_DEVICE = _descriptor.EnumDescriptor(
  name='Device',
  full_name='carbon.frontend.power.Device',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SENSOR_LIFTED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_SERVER_TEMPERATURE', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_SERVER_HUMIDITY', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_WATER', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_12V_BATTERY', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_POWER_QUALITY', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_TRACTOR', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_AC_FREQUENCY', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_AB_VOLTAGE', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_BC_VOLTAGE', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_AC_VOLTAGE', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_A_CURRENT', index=11, number=11,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_B_CURRENT', index=12, number=12,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_C_CURRENT', index=13, number=13,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_SUICIDE', index=14, number=14,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_REBOOT', index=15, number=15,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_MAIN', index=16, number=16,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_ROW_1', index=17, number=17,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_ROW_2', index=18, number=18,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_ROW_3', index=19, number=19,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_LIGHTS_1', index=20, number=20,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_LIGHTS_2', index=21, number=21,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_LIGHTS_3', index=22, number=22,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_SCANNER_1', index=23, number=23,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_SCANNER_2', index=24, number=24,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_SCANNER_3', index=25, number=25,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_AC', index=26, number=26,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_CHILLER', index=27, number=27,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_STROBE', index=28, number=28,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_ENCODER_FRONT_LEFT', index=29, number=29,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_ENCODER_FRONT_RIGHT', index=30, number=30,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_ENCODER_BACK_LEFT', index=31, number=31,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_ENCODER_BACK_RIGHT', index=32, number=32,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_ENCODER_FRONT_LEFT', index=33, number=33,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_ENCODER_FRONT_RIGHT', index=34, number=34,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_ENCODER_BACK_LEFT', index=35, number=35,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_ENCODER_BACK_RIGHT', index=36, number=36,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_GPS', index=37, number=37,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_LATITUDE', index=38, number=38,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_LONGITUDE', index=39, number=39,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_KEY', index=40, number=40,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SENSOR_INTERLOCK', index=41, number=41,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='RELAY_ENCODER_BOARD', index=42, number=42,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=8701,
  serialized_end=9678,
)
_sym_db.RegisterEnumDescriptor(_DEVICE)

Device = enum_type_wrapper.EnumTypeWrapper(_DEVICE)
_DEVICEVALUECOLOR = _descriptor.EnumDescriptor(
  name='DeviceValueColor',
  full_name='carbon.frontend.power.DeviceValueColor',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='COLOR_GRAY', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='COLOR_GREEN', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='COLOR_ORANGE', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='COLOR_RED', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=9680,
  serialized_end=9764,
)
_sym_db.RegisterEnumDescriptor(_DEVICEVALUECOLOR)

DeviceValueColor = enum_type_wrapper.EnumTypeWrapper(_DEVICEVALUECOLOR)
_NETWORKLINKSPEED = _descriptor.EnumDescriptor(
  name='NetworkLinkSpeed',
  full_name='carbon.frontend.power.NetworkLinkSpeed',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_10M_HALF', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_10M_FULL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_100M_HALF', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_100M_FULL', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_1G_FULL', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_2G5_FULL', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_5G_FULL', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPEED_10G_FULL', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=9767,
  serialized_end=9958,
)
_sym_db.RegisterEnumDescriptor(_NETWORKLINKSPEED)

NetworkLinkSpeed = enum_type_wrapper.EnumTypeWrapper(_NETWORKLINKSPEED)
_ACPOWERSTATUS = _descriptor.EnumDescriptor(
  name='AcPowerStatus',
  full_name='carbon.frontend.power.AcPowerStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='POWER_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='POWER_GOOD', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='POWER_BAD', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='POWER_VERY_BAD', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=9960,
  serialized_end=10045,
)
_sym_db.RegisterEnumDescriptor(_ACPOWERSTATUS)

AcPowerStatus = enum_type_wrapper.EnumTypeWrapper(_ACPOWERSTATUS)
_MODULESTATUS = _descriptor.EnumDescriptor(
  name='ModuleStatus',
  full_name='carbon.frontend.power.ModuleStatus',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='OK', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='Error', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=10047,
  serialized_end=10080,
)
_sym_db.RegisterEnumDescriptor(_MODULESTATUS)

ModuleStatus = enum_type_wrapper.EnumTypeWrapper(_MODULESTATUS)
SENSOR_LIFTED = 0
SENSOR_SERVER_TEMPERATURE = 1
SENSOR_SERVER_HUMIDITY = 2
SENSOR_WATER = 3
SENSOR_12V_BATTERY = 4
SENSOR_POWER_QUALITY = 5
SENSOR_TRACTOR = 6
SENSOR_AC_FREQUENCY = 7
SENSOR_AB_VOLTAGE = 8
SENSOR_BC_VOLTAGE = 9
SENSOR_AC_VOLTAGE = 10
SENSOR_A_CURRENT = 11
SENSOR_B_CURRENT = 12
SENSOR_C_CURRENT = 13
RELAY_SUICIDE = 14
RELAY_REBOOT = 15
RELAY_MAIN = 16
RELAY_ROW_1 = 17
RELAY_ROW_2 = 18
RELAY_ROW_3 = 19
RELAY_LIGHTS_1 = 20
RELAY_LIGHTS_2 = 21
RELAY_LIGHTS_3 = 22
RELAY_SCANNER_1 = 23
RELAY_SCANNER_2 = 24
RELAY_SCANNER_3 = 25
RELAY_AC = 26
RELAY_CHILLER = 27
RELAY_STROBE = 28
RELAY_ENCODER_FRONT_LEFT = 29
RELAY_ENCODER_FRONT_RIGHT = 30
RELAY_ENCODER_BACK_LEFT = 31
RELAY_ENCODER_BACK_RIGHT = 32
SENSOR_ENCODER_FRONT_LEFT = 33
SENSOR_ENCODER_FRONT_RIGHT = 34
SENSOR_ENCODER_BACK_LEFT = 35
SENSOR_ENCODER_BACK_RIGHT = 36
RELAY_GPS = 37
SENSOR_LATITUDE = 38
SENSOR_LONGITUDE = 39
SENSOR_KEY = 40
SENSOR_INTERLOCK = 41
RELAY_ENCODER_BOARD = 42
COLOR_GRAY = 0
COLOR_GREEN = 1
COLOR_ORANGE = 2
COLOR_RED = 3
UNKNOWN = 0
SPEED_10M_HALF = 1
SPEED_10M_FULL = 2
SPEED_100M_HALF = 3
SPEED_100M_FULL = 4
SPEED_1G_FULL = 5
SPEED_2G5_FULL = 6
SPEED_5G_FULL = 7
SPEED_10G_FULL = 8
POWER_UNKNOWN = 0
POWER_GOOD = 1
POWER_BAD = 2
POWER_VERY_BAD = 3
OK = 0
Error = 1



_DEVICESTATUS_RELAYSTATUS = _descriptor.Descriptor(
  name='RelayStatus',
  full_name='carbon.frontend.power.DeviceStatus.RelayStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='disabled', full_name='carbon.frontend.power.DeviceStatus.RelayStatus.disabled', index=0,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=303,
  serialized_end=334,
)

_DEVICESTATUS_SENSORSTATUS = _descriptor.Descriptor(
  name='SensorStatus',
  full_name='carbon.frontend.power.DeviceStatus.SensorStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status', full_name='carbon.frontend.power.DeviceStatus.SensorStatus.status', index=0,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='color', full_name='carbon.frontend.power.DeviceStatus.SensorStatus.color', index=1,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=336,
  serialized_end=422,
)

_DEVICESTATUS = _descriptor.Descriptor(
  name='DeviceStatus',
  full_name='carbon.frontend.power.DeviceStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='device', full_name='carbon.frontend.power.DeviceStatus.device', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='label', full_name='carbon.frontend.power.DeviceStatus.label', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='relay_type', full_name='carbon.frontend.power.DeviceStatus.relay_type', index=2,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sensor_type', full_name='carbon.frontend.power.DeviceStatus.sensor_type', index=3,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_DEVICESTATUS_RELAYSTATUS, _DEVICESTATUS_SENSORSTATUS, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='type', full_name='carbon.frontend.power.DeviceStatus.type',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=81,
  serialized_end=430,
)


_POWERSTATUSREQUEST = _descriptor.Descriptor(
  name='PowerStatusRequest',
  full_name='carbon.frontend.power.PowerStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.power.PowerStatusRequest.ts', index=0,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=432,
  serialized_end=497,
)


_POWERSTATUSRESPONSE = _descriptor.Descriptor(
  name='PowerStatusResponse',
  full_name='carbon.frontend.power.PowerStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='devices', full_name='carbon.frontend.power.PowerStatusResponse.devices', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.power.PowerStatusResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=499,
  serialized_end=619,
)


_VALUEWITHRANGE = _descriptor.Descriptor(
  name='ValueWithRange',
  full_name='carbon.frontend.power.ValueWithRange',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.power.ValueWithRange.value', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_ok', full_name='carbon.frontend.power.ValueWithRange.is_ok', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=621,
  serialized_end=667,
)


_ENVIRONMENTALSENSORDATA = _descriptor.Descriptor(
  name='EnvironmentalSensorData',
  full_name='carbon.frontend.power.EnvironmentalSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='temperature_c', full_name='carbon.frontend.power.EnvironmentalSensorData.temperature_c', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity_rh', full_name='carbon.frontend.power.EnvironmentalSensorData.humidity_rh', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pressure_hpa', full_name='carbon.frontend.power.EnvironmentalSensorData.pressure_hpa', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=670,
  serialized_end=878,
)


_COOLANTSENSORDATA = _descriptor.Descriptor(
  name='CoolantSensorData',
  full_name='carbon.frontend.power.CoolantSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='temperature_c', full_name='carbon.frontend.power.CoolantSensorData.temperature_c', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pressure_kpa', full_name='carbon.frontend.power.CoolantSensorData.pressure_kpa', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=881,
  serialized_end=1023,
)


_NETWORKPORTSTATE = _descriptor.Descriptor(
  name='NetworkPortState',
  full_name='carbon.frontend.power.NetworkPortState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='link_up', full_name='carbon.frontend.power.NetworkPortState.link_up', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actual_link_speed', full_name='carbon.frontend.power.NetworkPortState.actual_link_speed', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expected_link_speed', full_name='carbon.frontend.power.NetworkPortState.expected_link_speed', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1026,
  serialized_end=1199,
)


_REAPERPCSENSORDATA = _descriptor.Descriptor(
  name='ReaperPcSensorData',
  full_name='carbon.frontend.power.ReaperPcSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='temperature_cpu_core_c', full_name='carbon.frontend.power.ReaperPcSensorData.temperature_cpu_core_c', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_system_c', full_name='carbon.frontend.power.ReaperPcSensorData.temperature_system_c', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_gpu_1_c', full_name='carbon.frontend.power.ReaperPcSensorData.temperature_gpu_1_c', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_gpu_2_c', full_name='carbon.frontend.power.ReaperPcSensorData.temperature_gpu_2_c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='psu_12v', full_name='carbon.frontend.power.ReaperPcSensorData.psu_12v', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='psu_5v', full_name='carbon.frontend.power.ReaperPcSensorData.psu_5v', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='psu_3v3', full_name='carbon.frontend.power.ReaperPcSensorData.psu_3v3', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='load', full_name='carbon.frontend.power.ReaperPcSensorData.load', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uptime', full_name='carbon.frontend.power.ReaperPcSensorData.uptime', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ram_usage_percent', full_name='carbon.frontend.power.ReaperPcSensorData.ram_usage_percent', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='disk_usage_percent', full_name='carbon.frontend.power.ReaperPcSensorData.disk_usage_percent', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_a_link', full_name='carbon.frontend.power.ReaperPcSensorData.scanner_a_link', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_b_link', full_name='carbon.frontend.power.ReaperPcSensorData.scanner_b_link', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_cam_a_link', full_name='carbon.frontend.power.ReaperPcSensorData.target_cam_a_link', index=13,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_cam_b_link', full_name='carbon.frontend.power.ReaperPcSensorData.target_cam_b_link', index=14,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_cam_link', full_name='carbon.frontend.power.ReaperPcSensorData.predict_cam_link', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ipmi_link', full_name='carbon.frontend.power.ReaperPcSensorData.ipmi_link', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ext_link', full_name='carbon.frontend.power.ReaperPcSensorData.ext_link', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_temperature_gpu_1_c', full_name='carbon.frontend.power.ReaperPcSensorData._temperature_gpu_1_c',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_temperature_gpu_2_c', full_name='carbon.frontend.power.ReaperPcSensorData._temperature_gpu_2_c',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1202,
  serialized_end=2377,
)


_REAPERSCANNERLASERSTATUS = _descriptor.Descriptor(
  name='ReaperScannerLaserStatus',
  full_name='carbon.frontend.power.ReaperScannerLaserStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model', full_name='carbon.frontend.power.ReaperScannerLaserStatus.model', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='sn', full_name='carbon.frontend.power.ReaperScannerLaserStatus.sn', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rated_power', full_name='carbon.frontend.power.ReaperScannerLaserStatus.rated_power', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_c', full_name='carbon.frontend.power.ReaperScannerLaserStatus.temperature_c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity', full_name='carbon.frontend.power.ReaperScannerLaserStatus.humidity', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_current_ma', full_name='carbon.frontend.power.ReaperScannerLaserStatus.laser_current_ma', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='faults', full_name='carbon.frontend.power.ReaperScannerLaserStatus.faults', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2380,
  serialized_end=2654,
)


_REAPERSCANNERMOTORDATA = _descriptor.Descriptor(
  name='ReaperScannerMotorData',
  full_name='carbon.frontend.power.ReaperScannerMotorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='controller_sn', full_name='carbon.frontend.power.ReaperScannerMotorData.controller_sn', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_output_c', full_name='carbon.frontend.power.ReaperScannerMotorData.temperature_output_c', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_supply_v', full_name='carbon.frontend.power.ReaperScannerMotorData.motor_supply_v', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_current_a', full_name='carbon.frontend.power.ReaperScannerMotorData.motor_current_a', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='encoder_position', full_name='carbon.frontend.power.ReaperScannerMotorData.encoder_position', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2657,
  serialized_end=2926,
)


_REAPERSCANNERSENSORDATA = _descriptor.Descriptor(
  name='ReaperScannerSensorData',
  full_name='carbon.frontend.power.ReaperScannerSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='scanner_sn', full_name='carbon.frontend.power.ReaperScannerSensorData.scanner_sn', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_a', full_name='carbon.frontend.power.ReaperScannerSensorData.current_a', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fuse_tripped', full_name='carbon.frontend.power.ReaperScannerSensorData.fuse_tripped', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_collimator_c', full_name='carbon.frontend.power.ReaperScannerSensorData.temperature_collimator_c', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_fiber_c', full_name='carbon.frontend.power.ReaperScannerSensorData.temperature_fiber_c', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_power_w', full_name='carbon.frontend.power.ReaperScannerSensorData.laser_power_w', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_connected', full_name='carbon.frontend.power.ReaperScannerSensorData.laser_connected', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_status', full_name='carbon.frontend.power.ReaperScannerSensorData.laser_status', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_connected', full_name='carbon.frontend.power.ReaperScannerSensorData.target_connected', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_sn', full_name='carbon.frontend.power.ReaperScannerSensorData.target_sn', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_target_c', full_name='carbon.frontend.power.ReaperScannerSensorData.temperature_target_c', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_pan', full_name='carbon.frontend.power.ReaperScannerSensorData.motor_pan', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='motor_tilt', full_name='carbon.frontend.power.ReaperScannerSensorData.motor_tilt', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_power_enabled', full_name='carbon.frontend.power.ReaperScannerSensorData.scanner_power_enabled', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_cam_power_enabled', full_name='carbon.frontend.power.ReaperScannerSensorData.target_cam_power_enabled', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_laser_status', full_name='carbon.frontend.power.ReaperScannerSensorData._laser_status',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_target_sn', full_name='carbon.frontend.power.ReaperScannerSensorData._target_sn',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_temperature_target_c', full_name='carbon.frontend.power.ReaperScannerSensorData._temperature_target_c',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_motor_pan', full_name='carbon.frontend.power.ReaperScannerSensorData._motor_pan',
      index=3, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_motor_tilt', full_name='carbon.frontend.power.ReaperScannerSensorData._motor_tilt',
      index=4, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=2929,
  serialized_end=3775,
)


_REAPERGPSDATA = _descriptor.Descriptor(
  name='ReaperGpsData',
  full_name='carbon.frontend.power.ReaperGpsData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='has_fix', full_name='carbon.frontend.power.ReaperGpsData.has_fix', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='latitude', full_name='carbon.frontend.power.ReaperGpsData.latitude', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='longitude', full_name='carbon.frontend.power.ReaperGpsData.longitude', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3777,
  serialized_end=3846,
)


_REAPERWHEELENCODERDATA = _descriptor.Descriptor(
  name='ReaperWheelEncoderData',
  full_name='carbon.frontend.power.ReaperWheelEncoderData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='front_left', full_name='carbon.frontend.power.ReaperWheelEncoderData.front_left', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='front_right', full_name='carbon.frontend.power.ReaperWheelEncoderData.front_right', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3848,
  serialized_end=3913,
)


_REAPERCENTERENCLOSUREDATA = _descriptor.Descriptor(
  name='ReaperCenterEnclosureData',
  full_name='carbon.frontend.power.ReaperCenterEnclosureData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='water_protect_status', full_name='carbon.frontend.power.ReaperCenterEnclosureData.water_protect_status', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_status_fb', full_name='carbon.frontend.power.ReaperCenterEnclosureData.main_contactor_status_fb', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_status', full_name='carbon.frontend.power.ReaperCenterEnclosureData.power_status', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifted_status', full_name='carbon.frontend.power.ReaperCenterEnclosureData.lifted_status', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp_humidity_status', full_name='carbon.frontend.power.ReaperCenterEnclosureData.temp_humidity_status', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tractor_power', full_name='carbon.frontend.power.ReaperCenterEnclosureData.tractor_power', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_frequency', full_name='carbon.frontend.power.ReaperCenterEnclosureData.ac_frequency', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a_b', full_name='carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_a_b', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_b_c', full_name='carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_b_c', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a_c', full_name='carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_a_c', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a', full_name='carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_a', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_b', full_name='carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_b', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_c', full_name='carbon.frontend.power.ReaperCenterEnclosureData.ac_voltage_c', index=12,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_power_w_3', full_name='carbon.frontend.power.ReaperCenterEnclosureData.phase_power_w_3', index=13,
      number=14, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_power_va_3', full_name='carbon.frontend.power.ReaperCenterEnclosureData.phase_power_va_3', index=14,
      number=15, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_factor', full_name='carbon.frontend.power.ReaperCenterEnclosureData.power_factor', index=15,
      number=16, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_cabinet_temp', full_name='carbon.frontend.power.ReaperCenterEnclosureData.server_cabinet_temp', index=16,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_cabinet_humidity', full_name='carbon.frontend.power.ReaperCenterEnclosureData.server_cabinet_humidity', index=17,
      number=18, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='battery_voltage_12v', full_name='carbon.frontend.power.ReaperCenterEnclosureData.battery_voltage_12v', index=18,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_encoder_disabled', full_name='carbon.frontend.power.ReaperCenterEnclosureData.wheel_encoder_disabled', index=19,
      number=20, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_disabled', full_name='carbon.frontend.power.ReaperCenterEnclosureData.strobe_disabled', index=20,
      number=21, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps_disabled', full_name='carbon.frontend.power.ReaperCenterEnclosureData.gps_disabled', index=21,
      number=22, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_disabled', full_name='carbon.frontend.power.ReaperCenterEnclosureData.main_contactor_disabled', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='air_conditioner_disabled', full_name='carbon.frontend.power.ReaperCenterEnclosureData.air_conditioner_disabled', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_disabled', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_disabled', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_alarms', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_alarms', index=25,
      number=26, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_temp_c', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_temp_c', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_flow_l_min', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_flow_l_min', index=27,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_pressure_psi', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_pressure_psi', index=28,
      number=29, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_conductivity_us_cm', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_conductivity_us_cm', index=29,
      number=30, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_set_temp_c', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_set_temp_c', index=30,
      number=31, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_heat_transfer_kbtu', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_heat_transfer_kbtu', index=31,
      number=32, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_fluid_delta_temp_c', full_name='carbon.frontend.power.ReaperCenterEnclosureData.chiller_fluid_delta_temp_c', index=32,
      number=33, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps', full_name='carbon.frontend.power.ReaperCenterEnclosureData.gps', index=33,
      number=34, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_encoder', full_name='carbon.frontend.power.ReaperCenterEnclosureData.wheel_encoder', index=34,
      number=35, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3916,
  serialized_end=5712,
)


_REAPERMODULESENSORDATA = _descriptor.Descriptor(
  name='ReaperModuleSensorData',
  full_name='carbon.frontend.power.ReaperModuleSensorData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.power.ReaperModuleSensorData.module_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='module_sn', full_name='carbon.frontend.power.ReaperModuleSensorData.module_sn', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enviro_enclosure', full_name='carbon.frontend.power.ReaperModuleSensorData.enviro_enclosure', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enviro_pc', full_name='carbon.frontend.power.ReaperModuleSensorData.enviro_pc', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='coolant_inlet', full_name='carbon.frontend.power.ReaperModuleSensorData.coolant_inlet', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='coolant_outlet', full_name='carbon.frontend.power.ReaperModuleSensorData.coolant_outlet', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_temperature_c', full_name='carbon.frontend.power.ReaperModuleSensorData.strobe_temperature_c', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_cap_voltage', full_name='carbon.frontend.power.ReaperModuleSensorData.strobe_cap_voltage', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_current', full_name='carbon.frontend.power.ReaperModuleSensorData.strobe_current', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pc', full_name='carbon.frontend.power.ReaperModuleSensorData.pc', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_a', full_name='carbon.frontend.power.ReaperModuleSensorData.scanner_a', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_b', full_name='carbon.frontend.power.ReaperModuleSensorData.scanner_b', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pc_power_enabled', full_name='carbon.frontend.power.ReaperModuleSensorData.pc_power_enabled', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lasers_power_enabled', full_name='carbon.frontend.power.ReaperModuleSensorData.lasers_power_enabled', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predict_cam_power_enabled', full_name='carbon.frontend.power.ReaperModuleSensorData.predict_cam_power_enabled', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_power_enabled', full_name='carbon.frontend.power.ReaperModuleSensorData.strobe_power_enabled', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_enabled', full_name='carbon.frontend.power.ReaperModuleSensorData.strobe_enabled', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status', full_name='carbon.frontend.power.ReaperModuleSensorData.status', index=17,
      number=18, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_pc', full_name='carbon.frontend.power.ReaperModuleSensorData._pc',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_scanner_a', full_name='carbon.frontend.power.ReaperModuleSensorData._scanner_a',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_scanner_b', full_name='carbon.frontend.power.ReaperModuleSensorData._scanner_b',
      index=2, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=5715,
  serialized_end=6685,
)


_CENTERENCLOSURESTATUSREQUEST = _descriptor.Descriptor(
  name='CenterEnclosureStatusRequest',
  full_name='carbon.frontend.power.CenterEnclosureStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6687,
  serialized_end=6717,
)


_MODULEHARDWARESTATUSREQUEST = _descriptor.Descriptor(
  name='ModuleHardwareStatusRequest',
  full_name='carbon.frontend.power.ModuleHardwareStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.power.ModuleHardwareStatusRequest.module_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6719,
  serialized_end=6767,
)


_GETNEXTREAPERHARDWARESTATUSREQUEST = _descriptor.Descriptor(
  name='GetNextReaperHardwareStatusRequest',
  full_name='carbon.frontend.power.GetNextReaperHardwareStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.power.GetNextReaperHardwareStatusRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_enclosure_status', full_name='carbon.frontend.power.GetNextReaperHardwareStatusRequest.center_enclosure_status', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='module_status', full_name='carbon.frontend.power.GetNextReaperHardwareStatusRequest.module_status', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='request', full_name='carbon.frontend.power.GetNextReaperHardwareStatusRequest.request',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=6770,
  serialized_end=7027,
)


_GETNEXTREAPERHARDWARESTATUSRESPONSE = _descriptor.Descriptor(
  name='GetNextReaperHardwareStatusResponse',
  full_name='carbon.frontend.power.GetNextReaperHardwareStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.power.GetNextReaperHardwareStatusResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_enclosure_status', full_name='carbon.frontend.power.GetNextReaperHardwareStatusResponse.center_enclosure_status', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='module_status', full_name='carbon.frontend.power.GetNextReaperHardwareStatusResponse.module_status', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='response', full_name='carbon.frontend.power.GetNextReaperHardwareStatusResponse.response',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=7030,
  serialized_end=7281,
)


_GETNEXTREAPERALLHARDWARESTATUSREQUEST = _descriptor.Descriptor(
  name='GetNextReaperAllHardwareStatusRequest',
  full_name='carbon.frontend.power.GetNextReaperAllHardwareStatusRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.power.GetNextReaperAllHardwareStatusRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7283,
  serialized_end=7367,
)


_GETNEXTREAPERALLHARDWARESTATUSRESPONSE = _descriptor.Descriptor(
  name='GetNextReaperAllHardwareStatusResponse',
  full_name='carbon.frontend.power.GetNextReaperAllHardwareStatusResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.power.GetNextReaperAllHardwareStatusResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_enclosure_status', full_name='carbon.frontend.power.GetNextReaperAllHardwareStatusResponse.center_enclosure_status', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='module_status', full_name='carbon.frontend.power.GetNextReaperAllHardwareStatusResponse.module_status', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7370,
  serialized_end=7608,
)


_RELAYREQUEST = _descriptor.Descriptor(
  name='RelayRequest',
  full_name='carbon.frontend.power.RelayRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='device', full_name='carbon.frontend.power.RelayRequest.device', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7610,
  serialized_end=7671,
)


_RELAYRESPONSE = _descriptor.Descriptor(
  name='RelayResponse',
  full_name='carbon.frontend.power.RelayResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='carbon.frontend.power.RelayResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7673,
  serialized_end=7705,
)


_SETREAPERSCANNERPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperScannerPowerRequest',
  full_name='carbon.frontend.power.SetReaperScannerPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.power.SetReaperScannerPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_a_power', full_name='carbon.frontend.power.SetReaperScannerPowerRequest.scanner_a_power', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanner_b_power', full_name='carbon.frontend.power.SetReaperScannerPowerRequest.scanner_b_power', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_scanner_a_power', full_name='carbon.frontend.power.SetReaperScannerPowerRequest._scanner_a_power',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_scanner_b_power', full_name='carbon.frontend.power.SetReaperScannerPowerRequest._scanner_b_power',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=7708,
  serialized_end=7857,
)


_SETREAPERSCANNERPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperScannerPowerResponse',
  full_name='carbon.frontend.power.SetReaperScannerPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='carbon.frontend.power.SetReaperScannerPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7859,
  serialized_end=7907,
)


_SETREAPERTARGETPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperTargetPowerRequest',
  full_name='carbon.frontend.power.SetReaperTargetPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.power.SetReaperTargetPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_a_power', full_name='carbon.frontend.power.SetReaperTargetPowerRequest.target_a_power', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_b_power', full_name='carbon.frontend.power.SetReaperTargetPowerRequest.target_b_power', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='_target_a_power', full_name='carbon.frontend.power.SetReaperTargetPowerRequest._target_a_power',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
    _descriptor.OneofDescriptor(
      name='_target_b_power', full_name='carbon.frontend.power.SetReaperTargetPowerRequest._target_b_power',
      index=1, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=7910,
  serialized_end=8054,
)


_SETREAPERTARGETPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperTargetPowerResponse',
  full_name='carbon.frontend.power.SetReaperTargetPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='carbon.frontend.power.SetReaperTargetPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8056,
  serialized_end=8103,
)


_SETREAPERPREDICTCAMPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperPredictCamPowerRequest',
  full_name='carbon.frontend.power.SetReaperPredictCamPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.power.SetReaperPredictCamPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.power.SetReaperPredictCamPowerRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8105,
  serialized_end=8174,
)


_SETREAPERPREDICTCAMPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperPredictCamPowerResponse',
  full_name='carbon.frontend.power.SetReaperPredictCamPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='carbon.frontend.power.SetReaperPredictCamPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8176,
  serialized_end=8227,
)


_SETREAPERSTROBEENABLEREQUEST = _descriptor.Descriptor(
  name='SetReaperStrobeEnableRequest',
  full_name='carbon.frontend.power.SetReaperStrobeEnableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.power.SetReaperStrobeEnableRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.power.SetReaperStrobeEnableRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8229,
  serialized_end=8295,
)


_SETREAPERSTROBEENABLERESPONSE = _descriptor.Descriptor(
  name='SetReaperStrobeEnableResponse',
  full_name='carbon.frontend.power.SetReaperStrobeEnableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='carbon.frontend.power.SetReaperStrobeEnableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8297,
  serialized_end=8345,
)


_SETREAPERALLSTROBESENABLEREQUEST = _descriptor.Descriptor(
  name='SetReaperAllStrobesEnableRequest',
  full_name='carbon.frontend.power.SetReaperAllStrobesEnableRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.power.SetReaperAllStrobesEnableRequest.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8347,
  serialized_end=8398,
)


_SETREAPERALLSTROBESENABLERESPONSE = _descriptor.Descriptor(
  name='SetReaperAllStrobesEnableResponse',
  full_name='carbon.frontend.power.SetReaperAllStrobesEnableResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='carbon.frontend.power.SetReaperAllStrobesEnableResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8400,
  serialized_end=8452,
)


_SETREAPERMODULEPCPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperModulePcPowerRequest',
  full_name='carbon.frontend.power.SetReaperModulePcPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.power.SetReaperModulePcPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.power.SetReaperModulePcPowerRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8454,
  serialized_end=8521,
)


_SETREAPERMODULEPCPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperModulePcPowerResponse',
  full_name='carbon.frontend.power.SetReaperModulePcPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='carbon.frontend.power.SetReaperModulePcPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8523,
  serialized_end=8572,
)


_SETREAPERMODULELASERPOWERREQUEST = _descriptor.Descriptor(
  name='SetReaperModuleLaserPowerRequest',
  full_name='carbon.frontend.power.SetReaperModuleLaserPowerRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='module_id', full_name='carbon.frontend.power.SetReaperModuleLaserPowerRequest.module_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.power.SetReaperModuleLaserPowerRequest.enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8574,
  serialized_end=8644,
)


_SETREAPERMODULELASERPOWERRESPONSE = _descriptor.Descriptor(
  name='SetReaperModuleLaserPowerResponse',
  full_name='carbon.frontend.power.SetReaperModuleLaserPowerResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='success', full_name='carbon.frontend.power.SetReaperModuleLaserPowerResponse.success', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8646,
  serialized_end=8698,
)

_DEVICESTATUS_RELAYSTATUS.containing_type = _DEVICESTATUS
_DEVICESTATUS_SENSORSTATUS.fields_by_name['color'].enum_type = _DEVICEVALUECOLOR
_DEVICESTATUS_SENSORSTATUS.containing_type = _DEVICESTATUS
_DEVICESTATUS.fields_by_name['device'].enum_type = _DEVICE
_DEVICESTATUS.fields_by_name['relay_type'].message_type = _DEVICESTATUS_RELAYSTATUS
_DEVICESTATUS.fields_by_name['sensor_type'].message_type = _DEVICESTATUS_SENSORSTATUS
_DEVICESTATUS.oneofs_by_name['type'].fields.append(
  _DEVICESTATUS.fields_by_name['relay_type'])
_DEVICESTATUS.fields_by_name['relay_type'].containing_oneof = _DEVICESTATUS.oneofs_by_name['type']
_DEVICESTATUS.oneofs_by_name['type'].fields.append(
  _DEVICESTATUS.fields_by_name['sensor_type'])
_DEVICESTATUS.fields_by_name['sensor_type'].containing_oneof = _DEVICESTATUS.oneofs_by_name['type']
_POWERSTATUSREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_POWERSTATUSRESPONSE.fields_by_name['devices'].message_type = _DEVICESTATUS
_POWERSTATUSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_ENVIRONMENTALSENSORDATA.fields_by_name['temperature_c'].message_type = _VALUEWITHRANGE
_ENVIRONMENTALSENSORDATA.fields_by_name['humidity_rh'].message_type = _VALUEWITHRANGE
_ENVIRONMENTALSENSORDATA.fields_by_name['pressure_hpa'].message_type = _VALUEWITHRANGE
_COOLANTSENSORDATA.fields_by_name['temperature_c'].message_type = _VALUEWITHRANGE
_COOLANTSENSORDATA.fields_by_name['pressure_kpa'].message_type = _VALUEWITHRANGE
_NETWORKPORTSTATE.fields_by_name['actual_link_speed'].enum_type = _NETWORKLINKSPEED
_NETWORKPORTSTATE.fields_by_name['expected_link_speed'].enum_type = _NETWORKLINKSPEED
_REAPERPCSENSORDATA.fields_by_name['temperature_cpu_core_c'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['temperature_system_c'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['temperature_gpu_1_c'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['temperature_gpu_2_c'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['psu_12v'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['psu_5v'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['psu_3v3'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['load'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['ram_usage_percent'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['disk_usage_percent'].message_type = _VALUEWITHRANGE
_REAPERPCSENSORDATA.fields_by_name['scanner_a_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['scanner_b_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['target_cam_a_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['target_cam_b_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['predict_cam_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['ipmi_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.fields_by_name['ext_link'].message_type = _NETWORKPORTSTATE
_REAPERPCSENSORDATA.oneofs_by_name['_temperature_gpu_1_c'].fields.append(
  _REAPERPCSENSORDATA.fields_by_name['temperature_gpu_1_c'])
_REAPERPCSENSORDATA.fields_by_name['temperature_gpu_1_c'].containing_oneof = _REAPERPCSENSORDATA.oneofs_by_name['_temperature_gpu_1_c']
_REAPERPCSENSORDATA.oneofs_by_name['_temperature_gpu_2_c'].fields.append(
  _REAPERPCSENSORDATA.fields_by_name['temperature_gpu_2_c'])
_REAPERPCSENSORDATA.fields_by_name['temperature_gpu_2_c'].containing_oneof = _REAPERPCSENSORDATA.oneofs_by_name['_temperature_gpu_2_c']
_REAPERSCANNERLASERSTATUS.fields_by_name['temperature_c'].message_type = _VALUEWITHRANGE
_REAPERSCANNERLASERSTATUS.fields_by_name['humidity'].message_type = _VALUEWITHRANGE
_REAPERSCANNERLASERSTATUS.fields_by_name['laser_current_ma'].message_type = _VALUEWITHRANGE
_REAPERSCANNERMOTORDATA.fields_by_name['temperature_output_c'].message_type = _VALUEWITHRANGE
_REAPERSCANNERMOTORDATA.fields_by_name['motor_supply_v'].message_type = _VALUEWITHRANGE
_REAPERSCANNERMOTORDATA.fields_by_name['motor_current_a'].message_type = _VALUEWITHRANGE
_REAPERSCANNERSENSORDATA.fields_by_name['current_a'].message_type = _VALUEWITHRANGE
_REAPERSCANNERSENSORDATA.fields_by_name['temperature_collimator_c'].message_type = _VALUEWITHRANGE
_REAPERSCANNERSENSORDATA.fields_by_name['temperature_fiber_c'].message_type = _VALUEWITHRANGE
_REAPERSCANNERSENSORDATA.fields_by_name['laser_power_w'].message_type = _VALUEWITHRANGE
_REAPERSCANNERSENSORDATA.fields_by_name['laser_status'].message_type = _REAPERSCANNERLASERSTATUS
_REAPERSCANNERSENSORDATA.fields_by_name['temperature_target_c'].message_type = _VALUEWITHRANGE
_REAPERSCANNERSENSORDATA.fields_by_name['motor_pan'].message_type = _REAPERSCANNERMOTORDATA
_REAPERSCANNERSENSORDATA.fields_by_name['motor_tilt'].message_type = _REAPERSCANNERMOTORDATA
_REAPERSCANNERSENSORDATA.oneofs_by_name['_laser_status'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['laser_status'])
_REAPERSCANNERSENSORDATA.fields_by_name['laser_status'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_laser_status']
_REAPERSCANNERSENSORDATA.oneofs_by_name['_target_sn'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['target_sn'])
_REAPERSCANNERSENSORDATA.fields_by_name['target_sn'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_target_sn']
_REAPERSCANNERSENSORDATA.oneofs_by_name['_temperature_target_c'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['temperature_target_c'])
_REAPERSCANNERSENSORDATA.fields_by_name['temperature_target_c'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_temperature_target_c']
_REAPERSCANNERSENSORDATA.oneofs_by_name['_motor_pan'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['motor_pan'])
_REAPERSCANNERSENSORDATA.fields_by_name['motor_pan'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_motor_pan']
_REAPERSCANNERSENSORDATA.oneofs_by_name['_motor_tilt'].fields.append(
  _REAPERSCANNERSENSORDATA.fields_by_name['motor_tilt'])
_REAPERSCANNERSENSORDATA.fields_by_name['motor_tilt'].containing_oneof = _REAPERSCANNERSENSORDATA.oneofs_by_name['_motor_tilt']
_REAPERCENTERENCLOSUREDATA.fields_by_name['power_status'].enum_type = _ACPOWERSTATUS
_REAPERCENTERENCLOSUREDATA.fields_by_name['ac_frequency'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['ac_voltage_a_b'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['ac_voltage_b_c'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['ac_voltage_a_c'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['ac_voltage_a'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['ac_voltage_b'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['ac_voltage_c'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['power_factor'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['server_cabinet_temp'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['server_cabinet_humidity'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['battery_voltage_12v'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['chiller_temp_c'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['chiller_flow_l_min'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['chiller_pressure_psi'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['chiller_conductivity_us_cm'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['chiller_set_temp_c'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['chiller_heat_transfer_kbtu'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['chiller_fluid_delta_temp_c'].message_type = _VALUEWITHRANGE
_REAPERCENTERENCLOSUREDATA.fields_by_name['gps'].message_type = _REAPERGPSDATA
_REAPERCENTERENCLOSUREDATA.fields_by_name['wheel_encoder'].message_type = _REAPERWHEELENCODERDATA
_REAPERMODULESENSORDATA.fields_by_name['enviro_enclosure'].message_type = _ENVIRONMENTALSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['enviro_pc'].message_type = _ENVIRONMENTALSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['coolant_inlet'].message_type = _COOLANTSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['coolant_outlet'].message_type = _COOLANTSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['strobe_temperature_c'].message_type = _VALUEWITHRANGE
_REAPERMODULESENSORDATA.fields_by_name['strobe_cap_voltage'].message_type = _VALUEWITHRANGE
_REAPERMODULESENSORDATA.fields_by_name['strobe_current'].message_type = _VALUEWITHRANGE
_REAPERMODULESENSORDATA.fields_by_name['pc'].message_type = _REAPERPCSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['scanner_a'].message_type = _REAPERSCANNERSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['scanner_b'].message_type = _REAPERSCANNERSENSORDATA
_REAPERMODULESENSORDATA.fields_by_name['status'].enum_type = _MODULESTATUS
_REAPERMODULESENSORDATA.oneofs_by_name['_pc'].fields.append(
  _REAPERMODULESENSORDATA.fields_by_name['pc'])
_REAPERMODULESENSORDATA.fields_by_name['pc'].containing_oneof = _REAPERMODULESENSORDATA.oneofs_by_name['_pc']
_REAPERMODULESENSORDATA.oneofs_by_name['_scanner_a'].fields.append(
  _REAPERMODULESENSORDATA.fields_by_name['scanner_a'])
_REAPERMODULESENSORDATA.fields_by_name['scanner_a'].containing_oneof = _REAPERMODULESENSORDATA.oneofs_by_name['_scanner_a']
_REAPERMODULESENSORDATA.oneofs_by_name['_scanner_b'].fields.append(
  _REAPERMODULESENSORDATA.fields_by_name['scanner_b'])
_REAPERMODULESENSORDATA.fields_by_name['scanner_b'].containing_oneof = _REAPERMODULESENSORDATA.oneofs_by_name['_scanner_b']
_GETNEXTREAPERHARDWARESTATUSREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTREAPERHARDWARESTATUSREQUEST.fields_by_name['center_enclosure_status'].message_type = _CENTERENCLOSURESTATUSREQUEST
_GETNEXTREAPERHARDWARESTATUSREQUEST.fields_by_name['module_status'].message_type = _MODULEHARDWARESTATUSREQUEST
_GETNEXTREAPERHARDWARESTATUSREQUEST.oneofs_by_name['request'].fields.append(
  _GETNEXTREAPERHARDWARESTATUSREQUEST.fields_by_name['center_enclosure_status'])
_GETNEXTREAPERHARDWARESTATUSREQUEST.fields_by_name['center_enclosure_status'].containing_oneof = _GETNEXTREAPERHARDWARESTATUSREQUEST.oneofs_by_name['request']
_GETNEXTREAPERHARDWARESTATUSREQUEST.oneofs_by_name['request'].fields.append(
  _GETNEXTREAPERHARDWARESTATUSREQUEST.fields_by_name['module_status'])
_GETNEXTREAPERHARDWARESTATUSREQUEST.fields_by_name['module_status'].containing_oneof = _GETNEXTREAPERHARDWARESTATUSREQUEST.oneofs_by_name['request']
_GETNEXTREAPERHARDWARESTATUSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTREAPERHARDWARESTATUSRESPONSE.fields_by_name['center_enclosure_status'].message_type = _REAPERCENTERENCLOSUREDATA
_GETNEXTREAPERHARDWARESTATUSRESPONSE.fields_by_name['module_status'].message_type = _REAPERMODULESENSORDATA
_GETNEXTREAPERHARDWARESTATUSRESPONSE.oneofs_by_name['response'].fields.append(
  _GETNEXTREAPERHARDWARESTATUSRESPONSE.fields_by_name['center_enclosure_status'])
_GETNEXTREAPERHARDWARESTATUSRESPONSE.fields_by_name['center_enclosure_status'].containing_oneof = _GETNEXTREAPERHARDWARESTATUSRESPONSE.oneofs_by_name['response']
_GETNEXTREAPERHARDWARESTATUSRESPONSE.oneofs_by_name['response'].fields.append(
  _GETNEXTREAPERHARDWARESTATUSRESPONSE.fields_by_name['module_status'])
_GETNEXTREAPERHARDWARESTATUSRESPONSE.fields_by_name['module_status'].containing_oneof = _GETNEXTREAPERHARDWARESTATUSRESPONSE.oneofs_by_name['response']
_GETNEXTREAPERALLHARDWARESTATUSREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTREAPERALLHARDWARESTATUSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTREAPERALLHARDWARESTATUSRESPONSE.fields_by_name['center_enclosure_status'].message_type = _REAPERCENTERENCLOSUREDATA
_GETNEXTREAPERALLHARDWARESTATUSRESPONSE.fields_by_name['module_status'].message_type = _REAPERMODULESENSORDATA
_RELAYREQUEST.fields_by_name['device'].enum_type = _DEVICE
_SETREAPERSCANNERPOWERREQUEST.oneofs_by_name['_scanner_a_power'].fields.append(
  _SETREAPERSCANNERPOWERREQUEST.fields_by_name['scanner_a_power'])
_SETREAPERSCANNERPOWERREQUEST.fields_by_name['scanner_a_power'].containing_oneof = _SETREAPERSCANNERPOWERREQUEST.oneofs_by_name['_scanner_a_power']
_SETREAPERSCANNERPOWERREQUEST.oneofs_by_name['_scanner_b_power'].fields.append(
  _SETREAPERSCANNERPOWERREQUEST.fields_by_name['scanner_b_power'])
_SETREAPERSCANNERPOWERREQUEST.fields_by_name['scanner_b_power'].containing_oneof = _SETREAPERSCANNERPOWERREQUEST.oneofs_by_name['_scanner_b_power']
_SETREAPERTARGETPOWERREQUEST.oneofs_by_name['_target_a_power'].fields.append(
  _SETREAPERTARGETPOWERREQUEST.fields_by_name['target_a_power'])
_SETREAPERTARGETPOWERREQUEST.fields_by_name['target_a_power'].containing_oneof = _SETREAPERTARGETPOWERREQUEST.oneofs_by_name['_target_a_power']
_SETREAPERTARGETPOWERREQUEST.oneofs_by_name['_target_b_power'].fields.append(
  _SETREAPERTARGETPOWERREQUEST.fields_by_name['target_b_power'])
_SETREAPERTARGETPOWERREQUEST.fields_by_name['target_b_power'].containing_oneof = _SETREAPERTARGETPOWERREQUEST.oneofs_by_name['_target_b_power']
DESCRIPTOR.message_types_by_name['DeviceStatus'] = _DEVICESTATUS
DESCRIPTOR.message_types_by_name['PowerStatusRequest'] = _POWERSTATUSREQUEST
DESCRIPTOR.message_types_by_name['PowerStatusResponse'] = _POWERSTATUSRESPONSE
DESCRIPTOR.message_types_by_name['ValueWithRange'] = _VALUEWITHRANGE
DESCRIPTOR.message_types_by_name['EnvironmentalSensorData'] = _ENVIRONMENTALSENSORDATA
DESCRIPTOR.message_types_by_name['CoolantSensorData'] = _COOLANTSENSORDATA
DESCRIPTOR.message_types_by_name['NetworkPortState'] = _NETWORKPORTSTATE
DESCRIPTOR.message_types_by_name['ReaperPcSensorData'] = _REAPERPCSENSORDATA
DESCRIPTOR.message_types_by_name['ReaperScannerLaserStatus'] = _REAPERSCANNERLASERSTATUS
DESCRIPTOR.message_types_by_name['ReaperScannerMotorData'] = _REAPERSCANNERMOTORDATA
DESCRIPTOR.message_types_by_name['ReaperScannerSensorData'] = _REAPERSCANNERSENSORDATA
DESCRIPTOR.message_types_by_name['ReaperGpsData'] = _REAPERGPSDATA
DESCRIPTOR.message_types_by_name['ReaperWheelEncoderData'] = _REAPERWHEELENCODERDATA
DESCRIPTOR.message_types_by_name['ReaperCenterEnclosureData'] = _REAPERCENTERENCLOSUREDATA
DESCRIPTOR.message_types_by_name['ReaperModuleSensorData'] = _REAPERMODULESENSORDATA
DESCRIPTOR.message_types_by_name['CenterEnclosureStatusRequest'] = _CENTERENCLOSURESTATUSREQUEST
DESCRIPTOR.message_types_by_name['ModuleHardwareStatusRequest'] = _MODULEHARDWARESTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetNextReaperHardwareStatusRequest'] = _GETNEXTREAPERHARDWARESTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetNextReaperHardwareStatusResponse'] = _GETNEXTREAPERHARDWARESTATUSRESPONSE
DESCRIPTOR.message_types_by_name['GetNextReaperAllHardwareStatusRequest'] = _GETNEXTREAPERALLHARDWARESTATUSREQUEST
DESCRIPTOR.message_types_by_name['GetNextReaperAllHardwareStatusResponse'] = _GETNEXTREAPERALLHARDWARESTATUSRESPONSE
DESCRIPTOR.message_types_by_name['RelayRequest'] = _RELAYREQUEST
DESCRIPTOR.message_types_by_name['RelayResponse'] = _RELAYRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperScannerPowerRequest'] = _SETREAPERSCANNERPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperScannerPowerResponse'] = _SETREAPERSCANNERPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperTargetPowerRequest'] = _SETREAPERTARGETPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperTargetPowerResponse'] = _SETREAPERTARGETPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperPredictCamPowerRequest'] = _SETREAPERPREDICTCAMPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperPredictCamPowerResponse'] = _SETREAPERPREDICTCAMPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperStrobeEnableRequest'] = _SETREAPERSTROBEENABLEREQUEST
DESCRIPTOR.message_types_by_name['SetReaperStrobeEnableResponse'] = _SETREAPERSTROBEENABLERESPONSE
DESCRIPTOR.message_types_by_name['SetReaperAllStrobesEnableRequest'] = _SETREAPERALLSTROBESENABLEREQUEST
DESCRIPTOR.message_types_by_name['SetReaperAllStrobesEnableResponse'] = _SETREAPERALLSTROBESENABLERESPONSE
DESCRIPTOR.message_types_by_name['SetReaperModulePcPowerRequest'] = _SETREAPERMODULEPCPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperModulePcPowerResponse'] = _SETREAPERMODULEPCPOWERRESPONSE
DESCRIPTOR.message_types_by_name['SetReaperModuleLaserPowerRequest'] = _SETREAPERMODULELASERPOWERREQUEST
DESCRIPTOR.message_types_by_name['SetReaperModuleLaserPowerResponse'] = _SETREAPERMODULELASERPOWERRESPONSE
DESCRIPTOR.enum_types_by_name['Device'] = _DEVICE
DESCRIPTOR.enum_types_by_name['DeviceValueColor'] = _DEVICEVALUECOLOR
DESCRIPTOR.enum_types_by_name['NetworkLinkSpeed'] = _NETWORKLINKSPEED
DESCRIPTOR.enum_types_by_name['AcPowerStatus'] = _ACPOWERSTATUS
DESCRIPTOR.enum_types_by_name['ModuleStatus'] = _MODULESTATUS
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

DeviceStatus = _reflection.GeneratedProtocolMessageType('DeviceStatus', (_message.Message,), {

  'RelayStatus' : _reflection.GeneratedProtocolMessageType('RelayStatus', (_message.Message,), {
    'DESCRIPTOR' : _DEVICESTATUS_RELAYSTATUS,
    '__module__' : 'frontend.proto.power_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.power.DeviceStatus.RelayStatus)
    })
  ,

  'SensorStatus' : _reflection.GeneratedProtocolMessageType('SensorStatus', (_message.Message,), {
    'DESCRIPTOR' : _DEVICESTATUS_SENSORSTATUS,
    '__module__' : 'frontend.proto.power_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.power.DeviceStatus.SensorStatus)
    })
  ,
  'DESCRIPTOR' : _DEVICESTATUS,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.DeviceStatus)
  })
_sym_db.RegisterMessage(DeviceStatus)
_sym_db.RegisterMessage(DeviceStatus.RelayStatus)
_sym_db.RegisterMessage(DeviceStatus.SensorStatus)

PowerStatusRequest = _reflection.GeneratedProtocolMessageType('PowerStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _POWERSTATUSREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.PowerStatusRequest)
  })
_sym_db.RegisterMessage(PowerStatusRequest)

PowerStatusResponse = _reflection.GeneratedProtocolMessageType('PowerStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _POWERSTATUSRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.PowerStatusResponse)
  })
_sym_db.RegisterMessage(PowerStatusResponse)

ValueWithRange = _reflection.GeneratedProtocolMessageType('ValueWithRange', (_message.Message,), {
  'DESCRIPTOR' : _VALUEWITHRANGE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ValueWithRange)
  })
_sym_db.RegisterMessage(ValueWithRange)

EnvironmentalSensorData = _reflection.GeneratedProtocolMessageType('EnvironmentalSensorData', (_message.Message,), {
  'DESCRIPTOR' : _ENVIRONMENTALSENSORDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.EnvironmentalSensorData)
  })
_sym_db.RegisterMessage(EnvironmentalSensorData)

CoolantSensorData = _reflection.GeneratedProtocolMessageType('CoolantSensorData', (_message.Message,), {
  'DESCRIPTOR' : _COOLANTSENSORDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.CoolantSensorData)
  })
_sym_db.RegisterMessage(CoolantSensorData)

NetworkPortState = _reflection.GeneratedProtocolMessageType('NetworkPortState', (_message.Message,), {
  'DESCRIPTOR' : _NETWORKPORTSTATE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.NetworkPortState)
  })
_sym_db.RegisterMessage(NetworkPortState)

ReaperPcSensorData = _reflection.GeneratedProtocolMessageType('ReaperPcSensorData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERPCSENSORDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ReaperPcSensorData)
  })
_sym_db.RegisterMessage(ReaperPcSensorData)

ReaperScannerLaserStatus = _reflection.GeneratedProtocolMessageType('ReaperScannerLaserStatus', (_message.Message,), {
  'DESCRIPTOR' : _REAPERSCANNERLASERSTATUS,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ReaperScannerLaserStatus)
  })
_sym_db.RegisterMessage(ReaperScannerLaserStatus)

ReaperScannerMotorData = _reflection.GeneratedProtocolMessageType('ReaperScannerMotorData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERSCANNERMOTORDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ReaperScannerMotorData)
  })
_sym_db.RegisterMessage(ReaperScannerMotorData)

ReaperScannerSensorData = _reflection.GeneratedProtocolMessageType('ReaperScannerSensorData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERSCANNERSENSORDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ReaperScannerSensorData)
  })
_sym_db.RegisterMessage(ReaperScannerSensorData)

ReaperGpsData = _reflection.GeneratedProtocolMessageType('ReaperGpsData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERGPSDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ReaperGpsData)
  })
_sym_db.RegisterMessage(ReaperGpsData)

ReaperWheelEncoderData = _reflection.GeneratedProtocolMessageType('ReaperWheelEncoderData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERWHEELENCODERDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ReaperWheelEncoderData)
  })
_sym_db.RegisterMessage(ReaperWheelEncoderData)

ReaperCenterEnclosureData = _reflection.GeneratedProtocolMessageType('ReaperCenterEnclosureData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERCENTERENCLOSUREDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ReaperCenterEnclosureData)
  })
_sym_db.RegisterMessage(ReaperCenterEnclosureData)

ReaperModuleSensorData = _reflection.GeneratedProtocolMessageType('ReaperModuleSensorData', (_message.Message,), {
  'DESCRIPTOR' : _REAPERMODULESENSORDATA,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ReaperModuleSensorData)
  })
_sym_db.RegisterMessage(ReaperModuleSensorData)

CenterEnclosureStatusRequest = _reflection.GeneratedProtocolMessageType('CenterEnclosureStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _CENTERENCLOSURESTATUSREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.CenterEnclosureStatusRequest)
  })
_sym_db.RegisterMessage(CenterEnclosureStatusRequest)

ModuleHardwareStatusRequest = _reflection.GeneratedProtocolMessageType('ModuleHardwareStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _MODULEHARDWARESTATUSREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.ModuleHardwareStatusRequest)
  })
_sym_db.RegisterMessage(ModuleHardwareStatusRequest)

GetNextReaperHardwareStatusRequest = _reflection.GeneratedProtocolMessageType('GetNextReaperHardwareStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTREAPERHARDWARESTATUSREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.GetNextReaperHardwareStatusRequest)
  })
_sym_db.RegisterMessage(GetNextReaperHardwareStatusRequest)

GetNextReaperHardwareStatusResponse = _reflection.GeneratedProtocolMessageType('GetNextReaperHardwareStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTREAPERHARDWARESTATUSRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.GetNextReaperHardwareStatusResponse)
  })
_sym_db.RegisterMessage(GetNextReaperHardwareStatusResponse)

GetNextReaperAllHardwareStatusRequest = _reflection.GeneratedProtocolMessageType('GetNextReaperAllHardwareStatusRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTREAPERALLHARDWARESTATUSREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.GetNextReaperAllHardwareStatusRequest)
  })
_sym_db.RegisterMessage(GetNextReaperAllHardwareStatusRequest)

GetNextReaperAllHardwareStatusResponse = _reflection.GeneratedProtocolMessageType('GetNextReaperAllHardwareStatusResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTREAPERALLHARDWARESTATUSRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.GetNextReaperAllHardwareStatusResponse)
  })
_sym_db.RegisterMessage(GetNextReaperAllHardwareStatusResponse)

RelayRequest = _reflection.GeneratedProtocolMessageType('RelayRequest', (_message.Message,), {
  'DESCRIPTOR' : _RELAYREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.RelayRequest)
  })
_sym_db.RegisterMessage(RelayRequest)

RelayResponse = _reflection.GeneratedProtocolMessageType('RelayResponse', (_message.Message,), {
  'DESCRIPTOR' : _RELAYRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.RelayResponse)
  })
_sym_db.RegisterMessage(RelayResponse)

SetReaperScannerPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperScannerPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSCANNERPOWERREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperScannerPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperScannerPowerRequest)

SetReaperScannerPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperScannerPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSCANNERPOWERRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperScannerPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperScannerPowerResponse)

SetReaperTargetPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperTargetPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERTARGETPOWERREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperTargetPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperTargetPowerRequest)

SetReaperTargetPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperTargetPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERTARGETPOWERRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperTargetPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperTargetPowerResponse)

SetReaperPredictCamPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperPredictCamPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERPREDICTCAMPOWERREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperPredictCamPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperPredictCamPowerRequest)

SetReaperPredictCamPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperPredictCamPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERPREDICTCAMPOWERRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperPredictCamPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperPredictCamPowerResponse)

SetReaperStrobeEnableRequest = _reflection.GeneratedProtocolMessageType('SetReaperStrobeEnableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSTROBEENABLEREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperStrobeEnableRequest)
  })
_sym_db.RegisterMessage(SetReaperStrobeEnableRequest)

SetReaperStrobeEnableResponse = _reflection.GeneratedProtocolMessageType('SetReaperStrobeEnableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERSTROBEENABLERESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperStrobeEnableResponse)
  })
_sym_db.RegisterMessage(SetReaperStrobeEnableResponse)

SetReaperAllStrobesEnableRequest = _reflection.GeneratedProtocolMessageType('SetReaperAllStrobesEnableRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERALLSTROBESENABLEREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperAllStrobesEnableRequest)
  })
_sym_db.RegisterMessage(SetReaperAllStrobesEnableRequest)

SetReaperAllStrobesEnableResponse = _reflection.GeneratedProtocolMessageType('SetReaperAllStrobesEnableResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERALLSTROBESENABLERESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperAllStrobesEnableResponse)
  })
_sym_db.RegisterMessage(SetReaperAllStrobesEnableResponse)

SetReaperModulePcPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperModulePcPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULEPCPOWERREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperModulePcPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperModulePcPowerRequest)

SetReaperModulePcPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperModulePcPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULEPCPOWERRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperModulePcPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperModulePcPowerResponse)

SetReaperModuleLaserPowerRequest = _reflection.GeneratedProtocolMessageType('SetReaperModuleLaserPowerRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULELASERPOWERREQUEST,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperModuleLaserPowerRequest)
  })
_sym_db.RegisterMessage(SetReaperModuleLaserPowerRequest)

SetReaperModuleLaserPowerResponse = _reflection.GeneratedProtocolMessageType('SetReaperModuleLaserPowerResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETREAPERMODULELASERPOWERRESPONSE,
  '__module__' : 'frontend.proto.power_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.power.SetReaperModuleLaserPowerResponse)
  })
_sym_db.RegisterMessage(SetReaperModuleLaserPowerResponse)


DESCRIPTOR._options = None
_REAPERCENTERENCLOSUREDATA.fields_by_name['temp_humidity_status']._options = None
_REAPERCENTERENCLOSUREDATA.fields_by_name['strobe_disabled']._options = None

_POWERSERVICE = _descriptor.ServiceDescriptor(
  name='PowerService',
  full_name='carbon.frontend.power.PowerService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=10083,
  serialized_end=11678,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextPowerStatus',
    full_name='carbon.frontend.power.PowerService.GetNextPowerStatus',
    index=0,
    containing_service=None,
    input_type=_POWERSTATUSREQUEST,
    output_type=_POWERSTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='TurnOffDevice',
    full_name='carbon.frontend.power.PowerService.TurnOffDevice',
    index=1,
    containing_service=None,
    input_type=_RELAYREQUEST,
    output_type=_RELAYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='TurnOnDevice',
    full_name='carbon.frontend.power.PowerService.TurnOnDevice',
    index=2,
    containing_service=None,
    input_type=_RELAYREQUEST,
    output_type=_RELAYRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextReaperAllHardwareStatus',
    full_name='carbon.frontend.power.PowerService.GetNextReaperAllHardwareStatus',
    index=3,
    containing_service=None,
    input_type=_GETNEXTREAPERALLHARDWARESTATUSREQUEST,
    output_type=_GETNEXTREAPERALLHARDWARESTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextReaperHardwareStatus',
    full_name='carbon.frontend.power.PowerService.GetNextReaperHardwareStatus',
    index=4,
    containing_service=None,
    input_type=_GETNEXTREAPERHARDWARESTATUSREQUEST,
    output_type=_GETNEXTREAPERHARDWARESTATUSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperScannerPower',
    full_name='carbon.frontend.power.PowerService.SetReaperScannerPower',
    index=5,
    containing_service=None,
    input_type=_SETREAPERSCANNERPOWERREQUEST,
    output_type=_SETREAPERSCANNERPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperTargetPower',
    full_name='carbon.frontend.power.PowerService.SetReaperTargetPower',
    index=6,
    containing_service=None,
    input_type=_SETREAPERTARGETPOWERREQUEST,
    output_type=_SETREAPERTARGETPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperPredictCamPower',
    full_name='carbon.frontend.power.PowerService.SetReaperPredictCamPower',
    index=7,
    containing_service=None,
    input_type=_SETREAPERPREDICTCAMPOWERREQUEST,
    output_type=_SETREAPERPREDICTCAMPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperStrobeEnable',
    full_name='carbon.frontend.power.PowerService.SetReaperStrobeEnable',
    index=8,
    containing_service=None,
    input_type=_SETREAPERSTROBEENABLEREQUEST,
    output_type=_SETREAPERSTROBEENABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperAllStrobesEnable',
    full_name='carbon.frontend.power.PowerService.SetReaperAllStrobesEnable',
    index=9,
    containing_service=None,
    input_type=_SETREAPERALLSTROBESENABLEREQUEST,
    output_type=_SETREAPERALLSTROBESENABLERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperModulePcPower',
    full_name='carbon.frontend.power.PowerService.SetReaperModulePcPower',
    index=10,
    containing_service=None,
    input_type=_SETREAPERMODULEPCPOWERREQUEST,
    output_type=_SETREAPERMODULEPCPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetReaperModuleLaserPower',
    full_name='carbon.frontend.power.PowerService.SetReaperModuleLaserPower',
    index=11,
    containing_service=None,
    input_type=_SETREAPERMODULELASERPOWERREQUEST,
    output_type=_SETREAPERMODULELASERPOWERRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_POWERSERVICE)

DESCRIPTOR.services_by_name['PowerService'] = _POWERSERVICE

# @@protoc_insertion_point(module_scope)

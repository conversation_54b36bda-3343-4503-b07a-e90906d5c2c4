// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/data_capture.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdata_5fcapture_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdata_5fcapture_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fdata_5fcapture_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto;
namespace carbon {
namespace frontend {
namespace data_capture {
class AvailableSessionResponse;
struct AvailableSessionResponseDefaultTypeInternal;
extern AvailableSessionResponseDefaultTypeInternal _AvailableSessionResponse_default_instance_;
class DataCaptureRate;
struct DataCaptureRateDefaultTypeInternal;
extern DataCaptureRateDefaultTypeInternal _DataCaptureRate_default_instance_;
class DataCaptureSession;
struct DataCaptureSessionDefaultTypeInternal;
extern DataCaptureSessionDefaultTypeInternal _DataCaptureSession_default_instance_;
class DataCaptureState;
struct DataCaptureStateDefaultTypeInternal;
extern DataCaptureStateDefaultTypeInternal _DataCaptureState_default_instance_;
class RegularCaptureStatus;
struct RegularCaptureStatusDefaultTypeInternal;
extern RegularCaptureStatusDefaultTypeInternal _RegularCaptureStatus_default_instance_;
class Session;
struct SessionDefaultTypeInternal;
extern SessionDefaultTypeInternal _Session_default_instance_;
class SessionName;
struct SessionNameDefaultTypeInternal;
extern SessionNameDefaultTypeInternal _SessionName_default_instance_;
class SnapImagesRequest;
struct SnapImagesRequestDefaultTypeInternal;
extern SnapImagesRequestDefaultTypeInternal _SnapImagesRequest_default_instance_;
class StartDataCaptureRequest;
struct StartDataCaptureRequestDefaultTypeInternal;
extern StartDataCaptureRequestDefaultTypeInternal _StartDataCaptureRequest_default_instance_;
}  // namespace data_capture
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::data_capture::AvailableSessionResponse* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::AvailableSessionResponse>(Arena*);
template<> ::carbon::frontend::data_capture::DataCaptureRate* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::DataCaptureRate>(Arena*);
template<> ::carbon::frontend::data_capture::DataCaptureSession* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::DataCaptureSession>(Arena*);
template<> ::carbon::frontend::data_capture::DataCaptureState* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::DataCaptureState>(Arena*);
template<> ::carbon::frontend::data_capture::RegularCaptureStatus* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::RegularCaptureStatus>(Arena*);
template<> ::carbon::frontend::data_capture::Session* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::Session>(Arena*);
template<> ::carbon::frontend::data_capture::SessionName* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::SessionName>(Arena*);
template<> ::carbon::frontend::data_capture::SnapImagesRequest* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::SnapImagesRequest>(Arena*);
template<> ::carbon::frontend::data_capture::StartDataCaptureRequest* Arena::CreateMaybeMessage<::carbon::frontend::data_capture::StartDataCaptureRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace data_capture {

enum UploadMethod : int {
  WIRELESS = 0,
  USB = 1,
  UploadMethod_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  UploadMethod_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool UploadMethod_IsValid(int value);
constexpr UploadMethod UploadMethod_MIN = WIRELESS;
constexpr UploadMethod UploadMethod_MAX = USB;
constexpr int UploadMethod_ARRAYSIZE = UploadMethod_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* UploadMethod_descriptor();
template<typename T>
inline const std::string& UploadMethod_Name(T enum_t_value) {
  static_assert(::std::is_same<T, UploadMethod>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function UploadMethod_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    UploadMethod_descriptor(), enum_t_value);
}
inline bool UploadMethod_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, UploadMethod* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<UploadMethod>(
    UploadMethod_descriptor(), name, value);
}
enum ProcedureStep : int {
  NEW = 0,
  CAPTURING = 1,
  CAPTURE_PAUSED = 2,
  CAPTURE_COMPLETE = 3,
  UPLOADING_WIRELESS = 4,
  UPLOADING_WIRELESS_PAUSED = 5,
  UPLOADING_USB = 6,
  UPLOADING_USB_PAUSED = 7,
  UPLOADING_COMPLETE = 8,
  ProcedureStep_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ProcedureStep_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ProcedureStep_IsValid(int value);
constexpr ProcedureStep ProcedureStep_MIN = NEW;
constexpr ProcedureStep ProcedureStep_MAX = UPLOADING_COMPLETE;
constexpr int ProcedureStep_ARRAYSIZE = ProcedureStep_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ProcedureStep_descriptor();
template<typename T>
inline const std::string& ProcedureStep_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ProcedureStep>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ProcedureStep_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ProcedureStep_descriptor(), enum_t_value);
}
inline bool ProcedureStep_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ProcedureStep* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ProcedureStep>(
    ProcedureStep_descriptor(), name, value);
}
// ===================================================================

class DataCaptureRate final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.DataCaptureRate) */ {
 public:
  inline DataCaptureRate() : DataCaptureRate(nullptr) {}
  ~DataCaptureRate() override;
  explicit constexpr DataCaptureRate(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataCaptureRate(const DataCaptureRate& from);
  DataCaptureRate(DataCaptureRate&& from) noexcept
    : DataCaptureRate() {
    *this = ::std::move(from);
  }

  inline DataCaptureRate& operator=(const DataCaptureRate& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataCaptureRate& operator=(DataCaptureRate&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataCaptureRate& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataCaptureRate* internal_default_instance() {
    return reinterpret_cast<const DataCaptureRate*>(
               &_DataCaptureRate_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(DataCaptureRate& a, DataCaptureRate& b) {
    a.Swap(&b);
  }
  inline void Swap(DataCaptureRate* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataCaptureRate* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataCaptureRate* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataCaptureRate>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataCaptureRate& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DataCaptureRate& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataCaptureRate* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.DataCaptureRate";
  }
  protected:
  explicit DataCaptureRate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRateFieldNumber = 1,
  };
  // double rate = 1;
  void clear_rate();
  double rate() const;
  void set_rate(double value);
  private:
  double _internal_rate() const;
  void _internal_set_rate(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.DataCaptureRate)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double rate_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// -------------------------------------------------------------------

class DataCaptureState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.DataCaptureState) */ {
 public:
  inline DataCaptureState() : DataCaptureState(nullptr) {}
  ~DataCaptureState() override;
  explicit constexpr DataCaptureState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataCaptureState(const DataCaptureState& from);
  DataCaptureState(DataCaptureState&& from) noexcept
    : DataCaptureState() {
    *this = ::std::move(from);
  }

  inline DataCaptureState& operator=(const DataCaptureState& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataCaptureState& operator=(DataCaptureState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataCaptureState& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataCaptureState* internal_default_instance() {
    return reinterpret_cast<const DataCaptureState*>(
               &_DataCaptureState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(DataCaptureState& a, DataCaptureState& b) {
    a.Swap(&b);
  }
  inline void Swap(DataCaptureState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataCaptureState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataCaptureState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataCaptureState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataCaptureState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DataCaptureState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataCaptureState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.DataCaptureState";
  }
  protected:
  explicit DataCaptureState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCaptureStatusFieldNumber = 11,
    kUploadStatusFieldNumber = 12,
    kSessionNameFieldNumber = 13,
    kCropFieldNumber = 15,
    kErrorMessageFieldNumber = 16,
    kCropIdFieldNumber = 17,
    kTsFieldNumber = 1,
    kRateFieldNumber = 8,
    kImagesTakenFieldNumber = 2,
    kTargetImagesTakenFieldNumber = 3,
    kEstimatedCaptureRemainingTimeMsFieldNumber = 4,
    kImagesUploadedFieldNumber = 5,
    kTargetImagesUploadedFieldNumber = 6,
    kEstimatedUploadRemainingTimeMsFieldNumber = 7,
    kWirelessUploadAvailableFieldNumber = 9,
    kUsbStorageConnectedFieldNumber = 10,
    kStepFieldNumber = 14,
  };
  // string capture_status = 11;
  void clear_capture_status();
  const std::string& capture_status() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_capture_status(ArgT0&& arg0, ArgT... args);
  std::string* mutable_capture_status();
  PROTOBUF_NODISCARD std::string* release_capture_status();
  void set_allocated_capture_status(std::string* capture_status);
  private:
  const std::string& _internal_capture_status() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_capture_status(const std::string& value);
  std::string* _internal_mutable_capture_status();
  public:

  // string upload_status = 12;
  void clear_upload_status();
  const std::string& upload_status() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_upload_status(ArgT0&& arg0, ArgT... args);
  std::string* mutable_upload_status();
  PROTOBUF_NODISCARD std::string* release_upload_status();
  void set_allocated_upload_status(std::string* upload_status);
  private:
  const std::string& _internal_upload_status() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_upload_status(const std::string& value);
  std::string* _internal_mutable_upload_status();
  public:

  // string session_name = 13;
  void clear_session_name();
  const std::string& session_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_name();
  PROTOBUF_NODISCARD std::string* release_session_name();
  void set_allocated_session_name(std::string* session_name);
  private:
  const std::string& _internal_session_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_name(const std::string& value);
  std::string* _internal_mutable_session_name();
  public:

  // string crop = 15;
  void clear_crop();
  const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop();
  PROTOBUF_NODISCARD std::string* release_crop();
  void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string error_message = 16;
  void clear_error_message();
  const std::string& error_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_error_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_error_message();
  PROTOBUF_NODISCARD std::string* release_error_message();
  void set_allocated_error_message(std::string* error_message);
  private:
  const std::string& _internal_error_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_error_message(const std::string& value);
  std::string* _internal_mutable_error_message();
  public:

  // string crop_id = 17;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.data_capture.DataCaptureRate rate = 8;
  bool has_rate() const;
  private:
  bool _internal_has_rate() const;
  public:
  void clear_rate();
  const ::carbon::frontend::data_capture::DataCaptureRate& rate() const;
  PROTOBUF_NODISCARD ::carbon::frontend::data_capture::DataCaptureRate* release_rate();
  ::carbon::frontend::data_capture::DataCaptureRate* mutable_rate();
  void set_allocated_rate(::carbon::frontend::data_capture::DataCaptureRate* rate);
  private:
  const ::carbon::frontend::data_capture::DataCaptureRate& _internal_rate() const;
  ::carbon::frontend::data_capture::DataCaptureRate* _internal_mutable_rate();
  public:
  void unsafe_arena_set_allocated_rate(
      ::carbon::frontend::data_capture::DataCaptureRate* rate);
  ::carbon::frontend::data_capture::DataCaptureRate* unsafe_arena_release_rate();

  // uint32 images_taken = 2;
  void clear_images_taken();
  uint32_t images_taken() const;
  void set_images_taken(uint32_t value);
  private:
  uint32_t _internal_images_taken() const;
  void _internal_set_images_taken(uint32_t value);
  public:

  // uint32 target_images_taken = 3;
  void clear_target_images_taken();
  uint32_t target_images_taken() const;
  void set_target_images_taken(uint32_t value);
  private:
  uint32_t _internal_target_images_taken() const;
  void _internal_set_target_images_taken(uint32_t value);
  public:

  // uint64 estimated_capture_remaining_time_ms = 4;
  void clear_estimated_capture_remaining_time_ms();
  uint64_t estimated_capture_remaining_time_ms() const;
  void set_estimated_capture_remaining_time_ms(uint64_t value);
  private:
  uint64_t _internal_estimated_capture_remaining_time_ms() const;
  void _internal_set_estimated_capture_remaining_time_ms(uint64_t value);
  public:

  // uint32 images_uploaded = 5;
  void clear_images_uploaded();
  uint32_t images_uploaded() const;
  void set_images_uploaded(uint32_t value);
  private:
  uint32_t _internal_images_uploaded() const;
  void _internal_set_images_uploaded(uint32_t value);
  public:

  // uint32 target_images_uploaded = 6;
  void clear_target_images_uploaded();
  uint32_t target_images_uploaded() const;
  void set_target_images_uploaded(uint32_t value);
  private:
  uint32_t _internal_target_images_uploaded() const;
  void _internal_set_target_images_uploaded(uint32_t value);
  public:

  // uint64 estimated_upload_remaining_time_ms = 7;
  void clear_estimated_upload_remaining_time_ms();
  uint64_t estimated_upload_remaining_time_ms() const;
  void set_estimated_upload_remaining_time_ms(uint64_t value);
  private:
  uint64_t _internal_estimated_upload_remaining_time_ms() const;
  void _internal_set_estimated_upload_remaining_time_ms(uint64_t value);
  public:

  // bool wireless_upload_available = 9;
  void clear_wireless_upload_available();
  bool wireless_upload_available() const;
  void set_wireless_upload_available(bool value);
  private:
  bool _internal_wireless_upload_available() const;
  void _internal_set_wireless_upload_available(bool value);
  public:

  // bool usb_storage_connected = 10;
  void clear_usb_storage_connected();
  bool usb_storage_connected() const;
  void set_usb_storage_connected(bool value);
  private:
  bool _internal_usb_storage_connected() const;
  void _internal_set_usb_storage_connected(bool value);
  public:

  // .carbon.frontend.data_capture.ProcedureStep step = 14;
  void clear_step();
  ::carbon::frontend::data_capture::ProcedureStep step() const;
  void set_step(::carbon::frontend::data_capture::ProcedureStep value);
  private:
  ::carbon::frontend::data_capture::ProcedureStep _internal_step() const;
  void _internal_set_step(::carbon::frontend::data_capture::ProcedureStep value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.DataCaptureState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr capture_status_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr upload_status_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr error_message_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::data_capture::DataCaptureRate* rate_;
  uint32_t images_taken_;
  uint32_t target_images_taken_;
  uint64_t estimated_capture_remaining_time_ms_;
  uint32_t images_uploaded_;
  uint32_t target_images_uploaded_;
  uint64_t estimated_upload_remaining_time_ms_;
  bool wireless_upload_available_;
  bool usb_storage_connected_;
  int step_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// -------------------------------------------------------------------

class DataCaptureSession final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.DataCaptureSession) */ {
 public:
  inline DataCaptureSession() : DataCaptureSession(nullptr) {}
  ~DataCaptureSession() override;
  explicit constexpr DataCaptureSession(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DataCaptureSession(const DataCaptureSession& from);
  DataCaptureSession(DataCaptureSession&& from) noexcept
    : DataCaptureSession() {
    *this = ::std::move(from);
  }

  inline DataCaptureSession& operator=(const DataCaptureSession& from) {
    CopyFrom(from);
    return *this;
  }
  inline DataCaptureSession& operator=(DataCaptureSession&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DataCaptureSession& default_instance() {
    return *internal_default_instance();
  }
  static inline const DataCaptureSession* internal_default_instance() {
    return reinterpret_cast<const DataCaptureSession*>(
               &_DataCaptureSession_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DataCaptureSession& a, DataCaptureSession& b) {
    a.Swap(&b);
  }
  inline void Swap(DataCaptureSession* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DataCaptureSession* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DataCaptureSession* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DataCaptureSession>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DataCaptureSession& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DataCaptureSession& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DataCaptureSession* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.DataCaptureSession";
  }
  protected:
  explicit DataCaptureSession(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.DataCaptureSession)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// -------------------------------------------------------------------

class StartDataCaptureRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.StartDataCaptureRequest) */ {
 public:
  inline StartDataCaptureRequest() : StartDataCaptureRequest(nullptr) {}
  ~StartDataCaptureRequest() override;
  explicit constexpr StartDataCaptureRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StartDataCaptureRequest(const StartDataCaptureRequest& from);
  StartDataCaptureRequest(StartDataCaptureRequest&& from) noexcept
    : StartDataCaptureRequest() {
    *this = ::std::move(from);
  }

  inline StartDataCaptureRequest& operator=(const StartDataCaptureRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline StartDataCaptureRequest& operator=(StartDataCaptureRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StartDataCaptureRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const StartDataCaptureRequest* internal_default_instance() {
    return reinterpret_cast<const StartDataCaptureRequest*>(
               &_StartDataCaptureRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(StartDataCaptureRequest& a, StartDataCaptureRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(StartDataCaptureRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StartDataCaptureRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StartDataCaptureRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StartDataCaptureRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StartDataCaptureRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StartDataCaptureRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StartDataCaptureRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.StartDataCaptureRequest";
  }
  protected:
  explicit StartDataCaptureRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kCropFieldNumber = 3,
    kCropIdFieldNumber = 4,
    kRateFieldNumber = 2,
    kSnapCaptureFieldNumber = 5,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string crop = 3;
  void clear_crop();
  const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop();
  PROTOBUF_NODISCARD std::string* release_crop();
  void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string crop_id = 4;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // double rate = 2;
  void clear_rate();
  double rate() const;
  void set_rate(double value);
  private:
  double _internal_rate() const;
  void _internal_set_rate(double value);
  public:

  // bool snap_capture = 5;
  void clear_snap_capture();
  bool snap_capture() const;
  void set_snap_capture(bool value);
  private:
  bool _internal_snap_capture() const;
  void _internal_set_snap_capture(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.StartDataCaptureRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  double rate_;
  bool snap_capture_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// -------------------------------------------------------------------

class SnapImagesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.SnapImagesRequest) */ {
 public:
  inline SnapImagesRequest() : SnapImagesRequest(nullptr) {}
  ~SnapImagesRequest() override;
  explicit constexpr SnapImagesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SnapImagesRequest(const SnapImagesRequest& from);
  SnapImagesRequest(SnapImagesRequest&& from) noexcept
    : SnapImagesRequest() {
    *this = ::std::move(from);
  }

  inline SnapImagesRequest& operator=(const SnapImagesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SnapImagesRequest& operator=(SnapImagesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SnapImagesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SnapImagesRequest* internal_default_instance() {
    return reinterpret_cast<const SnapImagesRequest*>(
               &_SnapImagesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SnapImagesRequest& a, SnapImagesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SnapImagesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SnapImagesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SnapImagesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SnapImagesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SnapImagesRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SnapImagesRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SnapImagesRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.SnapImagesRequest";
  }
  protected:
  explicit SnapImagesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCropFieldNumber = 1,
    kCropIdFieldNumber = 2,
    kCamIdFieldNumber = 3,
    kSessionNameFieldNumber = 5,
    kTimestampMsFieldNumber = 4,
  };
  // string crop = 1;
  void clear_crop();
  const std::string& crop() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop();
  PROTOBUF_NODISCARD std::string* release_crop();
  void set_allocated_crop(std::string* crop);
  private:
  const std::string& _internal_crop() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop(const std::string& value);
  std::string* _internal_mutable_crop();
  public:

  // string crop_id = 2;
  void clear_crop_id();
  const std::string& crop_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_crop_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_crop_id();
  PROTOBUF_NODISCARD std::string* release_crop_id();
  void set_allocated_crop_id(std::string* crop_id);
  private:
  const std::string& _internal_crop_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_crop_id(const std::string& value);
  std::string* _internal_mutable_crop_id();
  public:

  // optional string cam_id = 3;
  bool has_cam_id() const;
  private:
  bool _internal_has_cam_id() const;
  public:
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // string session_name = 5;
  void clear_session_name();
  const std::string& session_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_session_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_session_name();
  PROTOBUF_NODISCARD std::string* release_session_name();
  void set_allocated_session_name(std::string* session_name);
  private:
  const std::string& _internal_session_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_session_name(const std::string& value);
  std::string* _internal_mutable_session_name();
  public:

  // optional int64 timestamp_ms = 4;
  bool has_timestamp_ms() const;
  private:
  bool _internal_has_timestamp_ms() const;
  public:
  void clear_timestamp_ms();
  int64_t timestamp_ms() const;
  void set_timestamp_ms(int64_t value);
  private:
  int64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.SnapImagesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr crop_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr session_name_;
  int64_t timestamp_ms_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// -------------------------------------------------------------------

class Session final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.Session) */ {
 public:
  inline Session() : Session(nullptr) {}
  ~Session() override;
  explicit constexpr Session(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Session(const Session& from);
  Session(Session&& from) noexcept
    : Session() {
    *this = ::std::move(from);
  }

  inline Session& operator=(const Session& from) {
    CopyFrom(from);
    return *this;
  }
  inline Session& operator=(Session&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Session& default_instance() {
    return *internal_default_instance();
  }
  static inline const Session* internal_default_instance() {
    return reinterpret_cast<const Session*>(
               &_Session_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(Session& a, Session& b) {
    a.Swap(&b);
  }
  inline void Swap(Session* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Session* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Session* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Session>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Session& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Session& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Session* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.Session";
  }
  protected:
  explicit Session(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kImagesRemainingFieldNumber = 2,
    kIsUploadingFieldNumber = 3,
    kHasCompletedFieldNumber = 4,
    kIsCapturingFieldNumber = 5,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // uint32 images_remaining = 2;
  void clear_images_remaining();
  uint32_t images_remaining() const;
  void set_images_remaining(uint32_t value);
  private:
  uint32_t _internal_images_remaining() const;
  void _internal_set_images_remaining(uint32_t value);
  public:

  // bool is_uploading = 3;
  void clear_is_uploading();
  bool is_uploading() const;
  void set_is_uploading(bool value);
  private:
  bool _internal_is_uploading() const;
  void _internal_set_is_uploading(bool value);
  public:

  // bool has_completed = 4;
  void clear_has_completed();
  bool has_completed() const;
  void set_has_completed(bool value);
  private:
  bool _internal_has_completed() const;
  void _internal_set_has_completed(bool value);
  public:

  // bool is_capturing = 5;
  void clear_is_capturing();
  bool is_capturing() const;
  void set_is_capturing(bool value);
  private:
  bool _internal_is_capturing() const;
  void _internal_set_is_capturing(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.Session)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  uint32_t images_remaining_;
  bool is_uploading_;
  bool has_completed_;
  bool is_capturing_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// -------------------------------------------------------------------

class AvailableSessionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.AvailableSessionResponse) */ {
 public:
  inline AvailableSessionResponse() : AvailableSessionResponse(nullptr) {}
  ~AvailableSessionResponse() override;
  explicit constexpr AvailableSessionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AvailableSessionResponse(const AvailableSessionResponse& from);
  AvailableSessionResponse(AvailableSessionResponse&& from) noexcept
    : AvailableSessionResponse() {
    *this = ::std::move(from);
  }

  inline AvailableSessionResponse& operator=(const AvailableSessionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline AvailableSessionResponse& operator=(AvailableSessionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AvailableSessionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const AvailableSessionResponse* internal_default_instance() {
    return reinterpret_cast<const AvailableSessionResponse*>(
               &_AvailableSessionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(AvailableSessionResponse& a, AvailableSessionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(AvailableSessionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AvailableSessionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AvailableSessionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AvailableSessionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AvailableSessionResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AvailableSessionResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AvailableSessionResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.AvailableSessionResponse";
  }
  protected:
  explicit AvailableSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSessionsFieldNumber = 1,
  };
  // repeated .carbon.frontend.data_capture.Session sessions = 1;
  int sessions_size() const;
  private:
  int _internal_sessions_size() const;
  public:
  void clear_sessions();
  ::carbon::frontend::data_capture::Session* mutable_sessions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::data_capture::Session >*
      mutable_sessions();
  private:
  const ::carbon::frontend::data_capture::Session& _internal_sessions(int index) const;
  ::carbon::frontend::data_capture::Session* _internal_add_sessions();
  public:
  const ::carbon::frontend::data_capture::Session& sessions(int index) const;
  ::carbon::frontend::data_capture::Session* add_sessions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::data_capture::Session >&
      sessions() const;

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.AvailableSessionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::data_capture::Session > sessions_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// -------------------------------------------------------------------

class SessionName final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.SessionName) */ {
 public:
  inline SessionName() : SessionName(nullptr) {}
  ~SessionName() override;
  explicit constexpr SessionName(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SessionName(const SessionName& from);
  SessionName(SessionName&& from) noexcept
    : SessionName() {
    *this = ::std::move(from);
  }

  inline SessionName& operator=(const SessionName& from) {
    CopyFrom(from);
    return *this;
  }
  inline SessionName& operator=(SessionName&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SessionName& default_instance() {
    return *internal_default_instance();
  }
  static inline const SessionName* internal_default_instance() {
    return reinterpret_cast<const SessionName*>(
               &_SessionName_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(SessionName& a, SessionName& b) {
    a.Swap(&b);
  }
  inline void Swap(SessionName* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SessionName* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SessionName* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SessionName>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SessionName& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SessionName& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SessionName* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.SessionName";
  }
  protected:
  explicit SessionName(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.SessionName)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// -------------------------------------------------------------------

class RegularCaptureStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.data_capture.RegularCaptureStatus) */ {
 public:
  inline RegularCaptureStatus() : RegularCaptureStatus(nullptr) {}
  ~RegularCaptureStatus() override;
  explicit constexpr RegularCaptureStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RegularCaptureStatus(const RegularCaptureStatus& from);
  RegularCaptureStatus(RegularCaptureStatus&& from) noexcept
    : RegularCaptureStatus() {
    *this = ::std::move(from);
  }

  inline RegularCaptureStatus& operator=(const RegularCaptureStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline RegularCaptureStatus& operator=(RegularCaptureStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RegularCaptureStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const RegularCaptureStatus* internal_default_instance() {
    return reinterpret_cast<const RegularCaptureStatus*>(
               &_RegularCaptureStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(RegularCaptureStatus& a, RegularCaptureStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(RegularCaptureStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RegularCaptureStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RegularCaptureStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RegularCaptureStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RegularCaptureStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RegularCaptureStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RegularCaptureStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.data_capture.RegularCaptureStatus";
  }
  protected:
  explicit RegularCaptureStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUploadedFieldNumber = 1,
    kBudgetFieldNumber = 2,
    kLastUploadTimestampFieldNumber = 3,
  };
  // uint32 uploaded = 1;
  void clear_uploaded();
  uint32_t uploaded() const;
  void set_uploaded(uint32_t value);
  private:
  uint32_t _internal_uploaded() const;
  void _internal_set_uploaded(uint32_t value);
  public:

  // uint32 budget = 2;
  void clear_budget();
  uint32_t budget() const;
  void set_budget(uint32_t value);
  private:
  uint32_t _internal_budget() const;
  void _internal_set_budget(uint32_t value);
  public:

  // int64 last_upload_timestamp = 3;
  void clear_last_upload_timestamp();
  int64_t last_upload_timestamp() const;
  void set_last_upload_timestamp(int64_t value);
  private:
  int64_t _internal_last_upload_timestamp() const;
  void _internal_set_last_upload_timestamp(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.data_capture.RegularCaptureStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t uploaded_;
  uint32_t budget_;
  int64_t last_upload_timestamp_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// DataCaptureRate

// double rate = 1;
inline void DataCaptureRate::clear_rate() {
  rate_ = 0;
}
inline double DataCaptureRate::_internal_rate() const {
  return rate_;
}
inline double DataCaptureRate::rate() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureRate.rate)
  return _internal_rate();
}
inline void DataCaptureRate::_internal_set_rate(double value) {
  
  rate_ = value;
}
inline void DataCaptureRate::set_rate(double value) {
  _internal_set_rate(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureRate.rate)
}

// -------------------------------------------------------------------

// DataCaptureState

// .carbon.frontend.util.Timestamp ts = 1;
inline bool DataCaptureState::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool DataCaptureState::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& DataCaptureState::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& DataCaptureState::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.ts)
  return _internal_ts();
}
inline void DataCaptureState::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.data_capture.DataCaptureState.ts)
}
inline ::carbon::frontend::util::Timestamp* DataCaptureState::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* DataCaptureState::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureState.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* DataCaptureState::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* DataCaptureState::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureState.ts)
  return _msg;
}
inline void DataCaptureState::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureState.ts)
}

// uint32 images_taken = 2;
inline void DataCaptureState::clear_images_taken() {
  images_taken_ = 0u;
}
inline uint32_t DataCaptureState::_internal_images_taken() const {
  return images_taken_;
}
inline uint32_t DataCaptureState::images_taken() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.images_taken)
  return _internal_images_taken();
}
inline void DataCaptureState::_internal_set_images_taken(uint32_t value) {
  
  images_taken_ = value;
}
inline void DataCaptureState::set_images_taken(uint32_t value) {
  _internal_set_images_taken(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.images_taken)
}

// uint32 target_images_taken = 3;
inline void DataCaptureState::clear_target_images_taken() {
  target_images_taken_ = 0u;
}
inline uint32_t DataCaptureState::_internal_target_images_taken() const {
  return target_images_taken_;
}
inline uint32_t DataCaptureState::target_images_taken() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.target_images_taken)
  return _internal_target_images_taken();
}
inline void DataCaptureState::_internal_set_target_images_taken(uint32_t value) {
  
  target_images_taken_ = value;
}
inline void DataCaptureState::set_target_images_taken(uint32_t value) {
  _internal_set_target_images_taken(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.target_images_taken)
}

// uint64 estimated_capture_remaining_time_ms = 4;
inline void DataCaptureState::clear_estimated_capture_remaining_time_ms() {
  estimated_capture_remaining_time_ms_ = uint64_t{0u};
}
inline uint64_t DataCaptureState::_internal_estimated_capture_remaining_time_ms() const {
  return estimated_capture_remaining_time_ms_;
}
inline uint64_t DataCaptureState::estimated_capture_remaining_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.estimated_capture_remaining_time_ms)
  return _internal_estimated_capture_remaining_time_ms();
}
inline void DataCaptureState::_internal_set_estimated_capture_remaining_time_ms(uint64_t value) {
  
  estimated_capture_remaining_time_ms_ = value;
}
inline void DataCaptureState::set_estimated_capture_remaining_time_ms(uint64_t value) {
  _internal_set_estimated_capture_remaining_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.estimated_capture_remaining_time_ms)
}

// uint32 images_uploaded = 5;
inline void DataCaptureState::clear_images_uploaded() {
  images_uploaded_ = 0u;
}
inline uint32_t DataCaptureState::_internal_images_uploaded() const {
  return images_uploaded_;
}
inline uint32_t DataCaptureState::images_uploaded() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.images_uploaded)
  return _internal_images_uploaded();
}
inline void DataCaptureState::_internal_set_images_uploaded(uint32_t value) {
  
  images_uploaded_ = value;
}
inline void DataCaptureState::set_images_uploaded(uint32_t value) {
  _internal_set_images_uploaded(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.images_uploaded)
}

// uint32 target_images_uploaded = 6;
inline void DataCaptureState::clear_target_images_uploaded() {
  target_images_uploaded_ = 0u;
}
inline uint32_t DataCaptureState::_internal_target_images_uploaded() const {
  return target_images_uploaded_;
}
inline uint32_t DataCaptureState::target_images_uploaded() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.target_images_uploaded)
  return _internal_target_images_uploaded();
}
inline void DataCaptureState::_internal_set_target_images_uploaded(uint32_t value) {
  
  target_images_uploaded_ = value;
}
inline void DataCaptureState::set_target_images_uploaded(uint32_t value) {
  _internal_set_target_images_uploaded(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.target_images_uploaded)
}

// uint64 estimated_upload_remaining_time_ms = 7;
inline void DataCaptureState::clear_estimated_upload_remaining_time_ms() {
  estimated_upload_remaining_time_ms_ = uint64_t{0u};
}
inline uint64_t DataCaptureState::_internal_estimated_upload_remaining_time_ms() const {
  return estimated_upload_remaining_time_ms_;
}
inline uint64_t DataCaptureState::estimated_upload_remaining_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.estimated_upload_remaining_time_ms)
  return _internal_estimated_upload_remaining_time_ms();
}
inline void DataCaptureState::_internal_set_estimated_upload_remaining_time_ms(uint64_t value) {
  
  estimated_upload_remaining_time_ms_ = value;
}
inline void DataCaptureState::set_estimated_upload_remaining_time_ms(uint64_t value) {
  _internal_set_estimated_upload_remaining_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.estimated_upload_remaining_time_ms)
}

// .carbon.frontend.data_capture.DataCaptureRate rate = 8;
inline bool DataCaptureState::_internal_has_rate() const {
  return this != internal_default_instance() && rate_ != nullptr;
}
inline bool DataCaptureState::has_rate() const {
  return _internal_has_rate();
}
inline void DataCaptureState::clear_rate() {
  if (GetArenaForAllocation() == nullptr && rate_ != nullptr) {
    delete rate_;
  }
  rate_ = nullptr;
}
inline const ::carbon::frontend::data_capture::DataCaptureRate& DataCaptureState::_internal_rate() const {
  const ::carbon::frontend::data_capture::DataCaptureRate* p = rate_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::data_capture::DataCaptureRate&>(
      ::carbon::frontend::data_capture::_DataCaptureRate_default_instance_);
}
inline const ::carbon::frontend::data_capture::DataCaptureRate& DataCaptureState::rate() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.rate)
  return _internal_rate();
}
inline void DataCaptureState::unsafe_arena_set_allocated_rate(
    ::carbon::frontend::data_capture::DataCaptureRate* rate) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(rate_);
  }
  rate_ = rate;
  if (rate) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.data_capture.DataCaptureState.rate)
}
inline ::carbon::frontend::data_capture::DataCaptureRate* DataCaptureState::release_rate() {
  
  ::carbon::frontend::data_capture::DataCaptureRate* temp = rate_;
  rate_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::data_capture::DataCaptureRate* DataCaptureState::unsafe_arena_release_rate() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureState.rate)
  
  ::carbon::frontend::data_capture::DataCaptureRate* temp = rate_;
  rate_ = nullptr;
  return temp;
}
inline ::carbon::frontend::data_capture::DataCaptureRate* DataCaptureState::_internal_mutable_rate() {
  
  if (rate_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::data_capture::DataCaptureRate>(GetArenaForAllocation());
    rate_ = p;
  }
  return rate_;
}
inline ::carbon::frontend::data_capture::DataCaptureRate* DataCaptureState::mutable_rate() {
  ::carbon::frontend::data_capture::DataCaptureRate* _msg = _internal_mutable_rate();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureState.rate)
  return _msg;
}
inline void DataCaptureState::set_allocated_rate(::carbon::frontend::data_capture::DataCaptureRate* rate) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete rate_;
  }
  if (rate) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::data_capture::DataCaptureRate>::GetOwningArena(rate);
    if (message_arena != submessage_arena) {
      rate = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, rate, submessage_arena);
    }
    
  } else {
    
  }
  rate_ = rate;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureState.rate)
}

// bool wireless_upload_available = 9;
inline void DataCaptureState::clear_wireless_upload_available() {
  wireless_upload_available_ = false;
}
inline bool DataCaptureState::_internal_wireless_upload_available() const {
  return wireless_upload_available_;
}
inline bool DataCaptureState::wireless_upload_available() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.wireless_upload_available)
  return _internal_wireless_upload_available();
}
inline void DataCaptureState::_internal_set_wireless_upload_available(bool value) {
  
  wireless_upload_available_ = value;
}
inline void DataCaptureState::set_wireless_upload_available(bool value) {
  _internal_set_wireless_upload_available(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.wireless_upload_available)
}

// bool usb_storage_connected = 10;
inline void DataCaptureState::clear_usb_storage_connected() {
  usb_storage_connected_ = false;
}
inline bool DataCaptureState::_internal_usb_storage_connected() const {
  return usb_storage_connected_;
}
inline bool DataCaptureState::usb_storage_connected() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.usb_storage_connected)
  return _internal_usb_storage_connected();
}
inline void DataCaptureState::_internal_set_usb_storage_connected(bool value) {
  
  usb_storage_connected_ = value;
}
inline void DataCaptureState::set_usb_storage_connected(bool value) {
  _internal_set_usb_storage_connected(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.usb_storage_connected)
}

// string capture_status = 11;
inline void DataCaptureState::clear_capture_status() {
  capture_status_.ClearToEmpty();
}
inline const std::string& DataCaptureState::capture_status() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.capture_status)
  return _internal_capture_status();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataCaptureState::set_capture_status(ArgT0&& arg0, ArgT... args) {
 
 capture_status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.capture_status)
}
inline std::string* DataCaptureState::mutable_capture_status() {
  std::string* _s = _internal_mutable_capture_status();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureState.capture_status)
  return _s;
}
inline const std::string& DataCaptureState::_internal_capture_status() const {
  return capture_status_.Get();
}
inline void DataCaptureState::_internal_set_capture_status(const std::string& value) {
  
  capture_status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DataCaptureState::_internal_mutable_capture_status() {
  
  return capture_status_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DataCaptureState::release_capture_status() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureState.capture_status)
  return capture_status_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DataCaptureState::set_allocated_capture_status(std::string* capture_status) {
  if (capture_status != nullptr) {
    
  } else {
    
  }
  capture_status_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), capture_status,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (capture_status_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    capture_status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureState.capture_status)
}

// string upload_status = 12;
inline void DataCaptureState::clear_upload_status() {
  upload_status_.ClearToEmpty();
}
inline const std::string& DataCaptureState::upload_status() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.upload_status)
  return _internal_upload_status();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataCaptureState::set_upload_status(ArgT0&& arg0, ArgT... args) {
 
 upload_status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.upload_status)
}
inline std::string* DataCaptureState::mutable_upload_status() {
  std::string* _s = _internal_mutable_upload_status();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureState.upload_status)
  return _s;
}
inline const std::string& DataCaptureState::_internal_upload_status() const {
  return upload_status_.Get();
}
inline void DataCaptureState::_internal_set_upload_status(const std::string& value) {
  
  upload_status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DataCaptureState::_internal_mutable_upload_status() {
  
  return upload_status_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DataCaptureState::release_upload_status() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureState.upload_status)
  return upload_status_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DataCaptureState::set_allocated_upload_status(std::string* upload_status) {
  if (upload_status != nullptr) {
    
  } else {
    
  }
  upload_status_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), upload_status,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (upload_status_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    upload_status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureState.upload_status)
}

// string session_name = 13;
inline void DataCaptureState::clear_session_name() {
  session_name_.ClearToEmpty();
}
inline const std::string& DataCaptureState::session_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.session_name)
  return _internal_session_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataCaptureState::set_session_name(ArgT0&& arg0, ArgT... args) {
 
 session_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.session_name)
}
inline std::string* DataCaptureState::mutable_session_name() {
  std::string* _s = _internal_mutable_session_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureState.session_name)
  return _s;
}
inline const std::string& DataCaptureState::_internal_session_name() const {
  return session_name_.Get();
}
inline void DataCaptureState::_internal_set_session_name(const std::string& value) {
  
  session_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DataCaptureState::_internal_mutable_session_name() {
  
  return session_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DataCaptureState::release_session_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureState.session_name)
  return session_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DataCaptureState::set_allocated_session_name(std::string* session_name) {
  if (session_name != nullptr) {
    
  } else {
    
  }
  session_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (session_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    session_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureState.session_name)
}

// .carbon.frontend.data_capture.ProcedureStep step = 14;
inline void DataCaptureState::clear_step() {
  step_ = 0;
}
inline ::carbon::frontend::data_capture::ProcedureStep DataCaptureState::_internal_step() const {
  return static_cast< ::carbon::frontend::data_capture::ProcedureStep >(step_);
}
inline ::carbon::frontend::data_capture::ProcedureStep DataCaptureState::step() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.step)
  return _internal_step();
}
inline void DataCaptureState::_internal_set_step(::carbon::frontend::data_capture::ProcedureStep value) {
  
  step_ = value;
}
inline void DataCaptureState::set_step(::carbon::frontend::data_capture::ProcedureStep value) {
  _internal_set_step(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.step)
}

// string crop = 15;
inline void DataCaptureState::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& DataCaptureState::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataCaptureState::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.crop)
}
inline std::string* DataCaptureState::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureState.crop)
  return _s;
}
inline const std::string& DataCaptureState::_internal_crop() const {
  return crop_.Get();
}
inline void DataCaptureState::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DataCaptureState::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DataCaptureState::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureState.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DataCaptureState::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureState.crop)
}

// string error_message = 16;
inline void DataCaptureState::clear_error_message() {
  error_message_.ClearToEmpty();
}
inline const std::string& DataCaptureState::error_message() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.error_message)
  return _internal_error_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataCaptureState::set_error_message(ArgT0&& arg0, ArgT... args) {
 
 error_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.error_message)
}
inline std::string* DataCaptureState::mutable_error_message() {
  std::string* _s = _internal_mutable_error_message();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureState.error_message)
  return _s;
}
inline const std::string& DataCaptureState::_internal_error_message() const {
  return error_message_.Get();
}
inline void DataCaptureState::_internal_set_error_message(const std::string& value) {
  
  error_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DataCaptureState::_internal_mutable_error_message() {
  
  return error_message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DataCaptureState::release_error_message() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureState.error_message)
  return error_message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DataCaptureState::set_allocated_error_message(std::string* error_message) {
  if (error_message != nullptr) {
    
  } else {
    
  }
  error_message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), error_message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (error_message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureState.error_message)
}

// string crop_id = 17;
inline void DataCaptureState::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& DataCaptureState::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureState.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataCaptureState::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureState.crop_id)
}
inline std::string* DataCaptureState::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureState.crop_id)
  return _s;
}
inline const std::string& DataCaptureState::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void DataCaptureState::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DataCaptureState::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DataCaptureState::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureState.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DataCaptureState::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureState.crop_id)
}

// -------------------------------------------------------------------

// DataCaptureSession

// string name = 1;
inline void DataCaptureSession::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& DataCaptureSession::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.DataCaptureSession.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DataCaptureSession::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.DataCaptureSession.name)
}
inline std::string* DataCaptureSession::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.DataCaptureSession.name)
  return _s;
}
inline const std::string& DataCaptureSession::_internal_name() const {
  return name_.Get();
}
inline void DataCaptureSession::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DataCaptureSession::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DataCaptureSession::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.DataCaptureSession.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DataCaptureSession::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.DataCaptureSession.name)
}

// -------------------------------------------------------------------

// StartDataCaptureRequest

// string name = 1;
inline void StartDataCaptureRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& StartDataCaptureRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.StartDataCaptureRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StartDataCaptureRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.StartDataCaptureRequest.name)
}
inline std::string* StartDataCaptureRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.StartDataCaptureRequest.name)
  return _s;
}
inline const std::string& StartDataCaptureRequest::_internal_name() const {
  return name_.Get();
}
inline void StartDataCaptureRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StartDataCaptureRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StartDataCaptureRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.StartDataCaptureRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StartDataCaptureRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.StartDataCaptureRequest.name)
}

// double rate = 2;
inline void StartDataCaptureRequest::clear_rate() {
  rate_ = 0;
}
inline double StartDataCaptureRequest::_internal_rate() const {
  return rate_;
}
inline double StartDataCaptureRequest::rate() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.StartDataCaptureRequest.rate)
  return _internal_rate();
}
inline void StartDataCaptureRequest::_internal_set_rate(double value) {
  
  rate_ = value;
}
inline void StartDataCaptureRequest::set_rate(double value) {
  _internal_set_rate(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.StartDataCaptureRequest.rate)
}

// string crop = 3;
inline void StartDataCaptureRequest::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& StartDataCaptureRequest::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.StartDataCaptureRequest.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StartDataCaptureRequest::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.StartDataCaptureRequest.crop)
}
inline std::string* StartDataCaptureRequest::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.StartDataCaptureRequest.crop)
  return _s;
}
inline const std::string& StartDataCaptureRequest::_internal_crop() const {
  return crop_.Get();
}
inline void StartDataCaptureRequest::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StartDataCaptureRequest::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StartDataCaptureRequest::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.StartDataCaptureRequest.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StartDataCaptureRequest::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.StartDataCaptureRequest.crop)
}

// string crop_id = 4;
inline void StartDataCaptureRequest::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& StartDataCaptureRequest::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.StartDataCaptureRequest.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StartDataCaptureRequest::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.StartDataCaptureRequest.crop_id)
}
inline std::string* StartDataCaptureRequest::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.StartDataCaptureRequest.crop_id)
  return _s;
}
inline const std::string& StartDataCaptureRequest::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void StartDataCaptureRequest::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StartDataCaptureRequest::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StartDataCaptureRequest::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.StartDataCaptureRequest.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StartDataCaptureRequest::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.StartDataCaptureRequest.crop_id)
}

// bool snap_capture = 5;
inline void StartDataCaptureRequest::clear_snap_capture() {
  snap_capture_ = false;
}
inline bool StartDataCaptureRequest::_internal_snap_capture() const {
  return snap_capture_;
}
inline bool StartDataCaptureRequest::snap_capture() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.StartDataCaptureRequest.snap_capture)
  return _internal_snap_capture();
}
inline void StartDataCaptureRequest::_internal_set_snap_capture(bool value) {
  
  snap_capture_ = value;
}
inline void StartDataCaptureRequest::set_snap_capture(bool value) {
  _internal_set_snap_capture(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.StartDataCaptureRequest.snap_capture)
}

// -------------------------------------------------------------------

// SnapImagesRequest

// string crop = 1;
inline void SnapImagesRequest::clear_crop() {
  crop_.ClearToEmpty();
}
inline const std::string& SnapImagesRequest::crop() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.SnapImagesRequest.crop)
  return _internal_crop();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SnapImagesRequest::set_crop(ArgT0&& arg0, ArgT... args) {
 
 crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.SnapImagesRequest.crop)
}
inline std::string* SnapImagesRequest::mutable_crop() {
  std::string* _s = _internal_mutable_crop();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.SnapImagesRequest.crop)
  return _s;
}
inline const std::string& SnapImagesRequest::_internal_crop() const {
  return crop_.Get();
}
inline void SnapImagesRequest::_internal_set_crop(const std::string& value) {
  
  crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SnapImagesRequest::_internal_mutable_crop() {
  
  return crop_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SnapImagesRequest::release_crop() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.SnapImagesRequest.crop)
  return crop_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SnapImagesRequest::set_allocated_crop(std::string* crop) {
  if (crop != nullptr) {
    
  } else {
    
  }
  crop_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.SnapImagesRequest.crop)
}

// string crop_id = 2;
inline void SnapImagesRequest::clear_crop_id() {
  crop_id_.ClearToEmpty();
}
inline const std::string& SnapImagesRequest::crop_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.SnapImagesRequest.crop_id)
  return _internal_crop_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SnapImagesRequest::set_crop_id(ArgT0&& arg0, ArgT... args) {
 
 crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.SnapImagesRequest.crop_id)
}
inline std::string* SnapImagesRequest::mutable_crop_id() {
  std::string* _s = _internal_mutable_crop_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.SnapImagesRequest.crop_id)
  return _s;
}
inline const std::string& SnapImagesRequest::_internal_crop_id() const {
  return crop_id_.Get();
}
inline void SnapImagesRequest::_internal_set_crop_id(const std::string& value) {
  
  crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SnapImagesRequest::_internal_mutable_crop_id() {
  
  return crop_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SnapImagesRequest::release_crop_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.SnapImagesRequest.crop_id)
  return crop_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SnapImagesRequest::set_allocated_crop_id(std::string* crop_id) {
  if (crop_id != nullptr) {
    
  } else {
    
  }
  crop_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), crop_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (crop_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.SnapImagesRequest.crop_id)
}

// optional string cam_id = 3;
inline bool SnapImagesRequest::_internal_has_cam_id() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  return value;
}
inline bool SnapImagesRequest::has_cam_id() const {
  return _internal_has_cam_id();
}
inline void SnapImagesRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
  _has_bits_[0] &= ~0x00000001u;
}
inline const std::string& SnapImagesRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.SnapImagesRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SnapImagesRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 _has_bits_[0] |= 0x00000001u;
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.SnapImagesRequest.cam_id)
}
inline std::string* SnapImagesRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.SnapImagesRequest.cam_id)
  return _s;
}
inline const std::string& SnapImagesRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void SnapImagesRequest::_internal_set_cam_id(const std::string& value) {
  _has_bits_[0] |= 0x00000001u;
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SnapImagesRequest::_internal_mutable_cam_id() {
  _has_bits_[0] |= 0x00000001u;
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SnapImagesRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.SnapImagesRequest.cam_id)
  if (!_internal_has_cam_id()) {
    return nullptr;
  }
  _has_bits_[0] &= ~0x00000001u;
  auto* p = cam_id_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  return p;
}
inline void SnapImagesRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.SnapImagesRequest.cam_id)
}

// optional int64 timestamp_ms = 4;
inline bool SnapImagesRequest::_internal_has_timestamp_ms() const {
  bool value = (_has_bits_[0] & 0x00000002u) != 0;
  return value;
}
inline bool SnapImagesRequest::has_timestamp_ms() const {
  return _internal_has_timestamp_ms();
}
inline void SnapImagesRequest::clear_timestamp_ms() {
  timestamp_ms_ = int64_t{0};
  _has_bits_[0] &= ~0x00000002u;
}
inline int64_t SnapImagesRequest::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline int64_t SnapImagesRequest::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.SnapImagesRequest.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void SnapImagesRequest::_internal_set_timestamp_ms(int64_t value) {
  _has_bits_[0] |= 0x00000002u;
  timestamp_ms_ = value;
}
inline void SnapImagesRequest::set_timestamp_ms(int64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.SnapImagesRequest.timestamp_ms)
}

// string session_name = 5;
inline void SnapImagesRequest::clear_session_name() {
  session_name_.ClearToEmpty();
}
inline const std::string& SnapImagesRequest::session_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.SnapImagesRequest.session_name)
  return _internal_session_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SnapImagesRequest::set_session_name(ArgT0&& arg0, ArgT... args) {
 
 session_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.SnapImagesRequest.session_name)
}
inline std::string* SnapImagesRequest::mutable_session_name() {
  std::string* _s = _internal_mutable_session_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.SnapImagesRequest.session_name)
  return _s;
}
inline const std::string& SnapImagesRequest::_internal_session_name() const {
  return session_name_.Get();
}
inline void SnapImagesRequest::_internal_set_session_name(const std::string& value) {
  
  session_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SnapImagesRequest::_internal_mutable_session_name() {
  
  return session_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SnapImagesRequest::release_session_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.SnapImagesRequest.session_name)
  return session_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SnapImagesRequest::set_allocated_session_name(std::string* session_name) {
  if (session_name != nullptr) {
    
  } else {
    
  }
  session_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), session_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (session_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    session_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.SnapImagesRequest.session_name)
}

// -------------------------------------------------------------------

// Session

// string name = 1;
inline void Session::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& Session::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.Session.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Session::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.Session.name)
}
inline std::string* Session::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.Session.name)
  return _s;
}
inline const std::string& Session::_internal_name() const {
  return name_.Get();
}
inline void Session::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Session::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Session::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.Session.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Session::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.Session.name)
}

// uint32 images_remaining = 2;
inline void Session::clear_images_remaining() {
  images_remaining_ = 0u;
}
inline uint32_t Session::_internal_images_remaining() const {
  return images_remaining_;
}
inline uint32_t Session::images_remaining() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.Session.images_remaining)
  return _internal_images_remaining();
}
inline void Session::_internal_set_images_remaining(uint32_t value) {
  
  images_remaining_ = value;
}
inline void Session::set_images_remaining(uint32_t value) {
  _internal_set_images_remaining(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.Session.images_remaining)
}

// bool is_uploading = 3;
inline void Session::clear_is_uploading() {
  is_uploading_ = false;
}
inline bool Session::_internal_is_uploading() const {
  return is_uploading_;
}
inline bool Session::is_uploading() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.Session.is_uploading)
  return _internal_is_uploading();
}
inline void Session::_internal_set_is_uploading(bool value) {
  
  is_uploading_ = value;
}
inline void Session::set_is_uploading(bool value) {
  _internal_set_is_uploading(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.Session.is_uploading)
}

// bool has_completed = 4;
inline void Session::clear_has_completed() {
  has_completed_ = false;
}
inline bool Session::_internal_has_completed() const {
  return has_completed_;
}
inline bool Session::has_completed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.Session.has_completed)
  return _internal_has_completed();
}
inline void Session::_internal_set_has_completed(bool value) {
  
  has_completed_ = value;
}
inline void Session::set_has_completed(bool value) {
  _internal_set_has_completed(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.Session.has_completed)
}

// bool is_capturing = 5;
inline void Session::clear_is_capturing() {
  is_capturing_ = false;
}
inline bool Session::_internal_is_capturing() const {
  return is_capturing_;
}
inline bool Session::is_capturing() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.Session.is_capturing)
  return _internal_is_capturing();
}
inline void Session::_internal_set_is_capturing(bool value) {
  
  is_capturing_ = value;
}
inline void Session::set_is_capturing(bool value) {
  _internal_set_is_capturing(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.Session.is_capturing)
}

// -------------------------------------------------------------------

// AvailableSessionResponse

// repeated .carbon.frontend.data_capture.Session sessions = 1;
inline int AvailableSessionResponse::_internal_sessions_size() const {
  return sessions_.size();
}
inline int AvailableSessionResponse::sessions_size() const {
  return _internal_sessions_size();
}
inline void AvailableSessionResponse::clear_sessions() {
  sessions_.Clear();
}
inline ::carbon::frontend::data_capture::Session* AvailableSessionResponse::mutable_sessions(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.AvailableSessionResponse.sessions)
  return sessions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::data_capture::Session >*
AvailableSessionResponse::mutable_sessions() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.data_capture.AvailableSessionResponse.sessions)
  return &sessions_;
}
inline const ::carbon::frontend::data_capture::Session& AvailableSessionResponse::_internal_sessions(int index) const {
  return sessions_.Get(index);
}
inline const ::carbon::frontend::data_capture::Session& AvailableSessionResponse::sessions(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.AvailableSessionResponse.sessions)
  return _internal_sessions(index);
}
inline ::carbon::frontend::data_capture::Session* AvailableSessionResponse::_internal_add_sessions() {
  return sessions_.Add();
}
inline ::carbon::frontend::data_capture::Session* AvailableSessionResponse::add_sessions() {
  ::carbon::frontend::data_capture::Session* _add = _internal_add_sessions();
  // @@protoc_insertion_point(field_add:carbon.frontend.data_capture.AvailableSessionResponse.sessions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::data_capture::Session >&
AvailableSessionResponse::sessions() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.data_capture.AvailableSessionResponse.sessions)
  return sessions_;
}

// -------------------------------------------------------------------

// SessionName

// string name = 1;
inline void SessionName::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& SessionName::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.SessionName.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SessionName::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.SessionName.name)
}
inline std::string* SessionName::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.data_capture.SessionName.name)
  return _s;
}
inline const std::string& SessionName::_internal_name() const {
  return name_.Get();
}
inline void SessionName::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SessionName::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SessionName::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.data_capture.SessionName.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SessionName::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.data_capture.SessionName.name)
}

// -------------------------------------------------------------------

// RegularCaptureStatus

// uint32 uploaded = 1;
inline void RegularCaptureStatus::clear_uploaded() {
  uploaded_ = 0u;
}
inline uint32_t RegularCaptureStatus::_internal_uploaded() const {
  return uploaded_;
}
inline uint32_t RegularCaptureStatus::uploaded() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.RegularCaptureStatus.uploaded)
  return _internal_uploaded();
}
inline void RegularCaptureStatus::_internal_set_uploaded(uint32_t value) {
  
  uploaded_ = value;
}
inline void RegularCaptureStatus::set_uploaded(uint32_t value) {
  _internal_set_uploaded(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.RegularCaptureStatus.uploaded)
}

// uint32 budget = 2;
inline void RegularCaptureStatus::clear_budget() {
  budget_ = 0u;
}
inline uint32_t RegularCaptureStatus::_internal_budget() const {
  return budget_;
}
inline uint32_t RegularCaptureStatus::budget() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.RegularCaptureStatus.budget)
  return _internal_budget();
}
inline void RegularCaptureStatus::_internal_set_budget(uint32_t value) {
  
  budget_ = value;
}
inline void RegularCaptureStatus::set_budget(uint32_t value) {
  _internal_set_budget(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.RegularCaptureStatus.budget)
}

// int64 last_upload_timestamp = 3;
inline void RegularCaptureStatus::clear_last_upload_timestamp() {
  last_upload_timestamp_ = int64_t{0};
}
inline int64_t RegularCaptureStatus::_internal_last_upload_timestamp() const {
  return last_upload_timestamp_;
}
inline int64_t RegularCaptureStatus::last_upload_timestamp() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.data_capture.RegularCaptureStatus.last_upload_timestamp)
  return _internal_last_upload_timestamp();
}
inline void RegularCaptureStatus::_internal_set_last_upload_timestamp(int64_t value) {
  
  last_upload_timestamp_ = value;
}
inline void RegularCaptureStatus::set_last_upload_timestamp(int64_t value) {
  _internal_set_last_upload_timestamp(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.data_capture.RegularCaptureStatus.last_upload_timestamp)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace data_capture
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::data_capture::UploadMethod> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::data_capture::UploadMethod>() {
  return ::carbon::frontend::data_capture::UploadMethod_descriptor();
}
template <> struct is_proto_enum< ::carbon::frontend::data_capture::ProcedureStep> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::data_capture::ProcedureStep>() {
  return ::carbon::frontend::data_capture::ProcedureStep_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdata_5fcapture_2eproto

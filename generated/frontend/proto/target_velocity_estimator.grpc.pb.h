// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/target_velocity_estimator.proto
#ifndef GRPC_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto__INCLUDED

#include "frontend/proto/target_velocity_estimator.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace target_velocity_estimator {

class TargetVelocityEstimatorService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.target_velocity_estimator.TargetVelocityEstimatorService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>> AsyncGetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>>(AsyncGetNextAvailableProfilesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>> PrepareAsyncGetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>>(PrepareAsyncGetNextAvailableProfilesRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>> AsyncGetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>>(AsyncGetNextActiveProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>> PrepareAsyncGetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>>(PrepareAsyncGetNextActiveProfileRaw(context, request, cq));
    }
    virtual ::grpc::Status LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>> AsyncLoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>>(AsyncLoadProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>> PrepareAsyncLoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>>(PrepareAsyncLoadProfileRaw(context, request, cq));
    }
    virtual ::grpc::Status SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>> AsyncSaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>>(AsyncSaveProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>> PrepareAsyncSaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>>(PrepareAsyncSaveProfileRaw(context, request, cq));
    }
    virtual ::grpc::Status SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>> AsyncSetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>>(AsyncSetActiveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>> PrepareAsyncSetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>>(PrepareAsyncSetActiveRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>> AsyncDeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>>(AsyncDeleteProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>> PrepareAsyncDeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>>(PrepareAsyncDeleteProfileRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* AsyncGetNextAvailableProfilesRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* PrepareAsyncGetNextAvailableProfilesRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* AsyncGetNextActiveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* PrepareAsyncGetNextActiveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* AsyncLoadProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* PrepareAsyncLoadProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* AsyncSaveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* PrepareAsyncSaveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* AsyncSetActiveRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* PrepareAsyncSetActiveRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* AsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* PrepareAsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>> AsyncGetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>>(AsyncGetNextAvailableProfilesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>> PrepareAsyncGetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>>(PrepareAsyncGetNextAvailableProfilesRaw(context, request, cq));
    }
    ::grpc::Status GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>> AsyncGetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>>(AsyncGetNextActiveProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>> PrepareAsyncGetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>>(PrepareAsyncGetNextActiveProfileRaw(context, request, cq));
    }
    ::grpc::Status LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>> AsyncLoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>>(AsyncLoadProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>> PrepareAsyncLoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>>(PrepareAsyncLoadProfileRaw(context, request, cq));
    }
    ::grpc::Status SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>> AsyncSaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>>(AsyncSaveProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>> PrepareAsyncSaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>>(PrepareAsyncSaveProfileRaw(context, request, cq));
    }
    ::grpc::Status SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>> AsyncSetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>>(AsyncSetActiveRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>> PrepareAsyncSetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>>(PrepareAsyncSetActiveRaw(context, request, cq));
    }
    ::grpc::Status DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>> AsyncDeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>>(AsyncDeleteProfileRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>> PrepareAsyncDeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>>(PrepareAsyncDeleteProfileRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextAvailableProfiles(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextActiveProfile(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response, std::function<void(::grpc::Status)>) override;
      void LoadProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response, std::function<void(::grpc::Status)>) override;
      void SaveProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response, std::function<void(::grpc::Status)>) override;
      void SetActive(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response, std::function<void(::grpc::Status)>) override;
      void DeleteProfile(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* AsyncGetNextAvailableProfilesRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* PrepareAsyncGetNextAvailableProfilesRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* AsyncGetNextActiveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* PrepareAsyncGetNextActiveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* AsyncLoadProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* PrepareAsyncLoadProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* AsyncSaveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* PrepareAsyncSaveProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* AsyncSetActiveRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* PrepareAsyncSetActiveRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* AsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* PrepareAsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAvailableProfiles_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextActiveProfile_;
    const ::grpc::internal::RpcMethod rpcmethod_LoadProfile_;
    const ::grpc::internal::RpcMethod rpcmethod_SaveProfile_;
    const ::grpc::internal::RpcMethod rpcmethod_SetActive_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteProfile_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextAvailableProfiles(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response);
    virtual ::grpc::Status GetNextActiveProfile(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response);
    virtual ::grpc::Status LoadProfile(::grpc::ServerContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response);
    virtual ::grpc::Status SaveProfile(::grpc::ServerContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response);
    virtual ::grpc::Status SetActive(::grpc::ServerContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response);
    virtual ::grpc::Status DeleteProfile(::grpc::ServerContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAvailableProfiles : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAvailableProfiles() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextAvailableProfiles() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAvailableProfiles(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAvailableProfiles(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextActiveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextActiveProfile() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextActiveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveProfile(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_LoadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_LoadProfile() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_LoadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLoadProfile(::grpc::ServerContext* context, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SaveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SaveProfile() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SaveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveProfile(::grpc::ServerContext* context, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetActive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetActive() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_SetActive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActive(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActive(::grpc::ServerContext* context, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteProfile(::grpc::ServerContext* context, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextAvailableProfiles<WithAsyncMethod_GetNextActiveProfile<WithAsyncMethod_LoadProfile<WithAsyncMethod_SaveProfile<WithAsyncMethod_SetActive<WithAsyncMethod_DeleteProfile<Service > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextAvailableProfiles : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAvailableProfiles() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* response) { return this->GetNextAvailableProfiles(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAvailableProfiles(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAvailableProfiles() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAvailableProfiles(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAvailableProfiles(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextActiveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextActiveProfile() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* response) { return this->GetNextActiveProfile(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextActiveProfile(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextActiveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_LoadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_LoadProfile() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* response) { return this->LoadProfile(context, request, response); }));}
    void SetMessageAllocatorFor_LoadProfile(
        ::grpc::MessageAllocator< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_LoadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LoadProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SaveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SaveProfile() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* response) { return this->SaveProfile(context, request, response); }));}
    void SetMessageAllocatorFor_SaveProfile(
        ::grpc::MessageAllocator< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SaveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetActive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetActive() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* response) { return this->SetActive(context, request, response); }));}
    void SetMessageAllocatorFor_SetActive(
        ::grpc::MessageAllocator< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetActive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActive(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActive(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* request, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* response) { return this->DeleteProfile(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteProfile(
        ::grpc::MessageAllocator< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextAvailableProfiles<WithCallbackMethod_GetNextActiveProfile<WithCallbackMethod_LoadProfile<WithCallbackMethod_SaveProfile<WithCallbackMethod_SetActive<WithCallbackMethod_DeleteProfile<Service > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextAvailableProfiles : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAvailableProfiles() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextAvailableProfiles() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAvailableProfiles(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextActiveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextActiveProfile() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextActiveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_LoadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_LoadProfile() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_LoadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SaveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SaveProfile() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SaveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetActive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetActive() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_SetActive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActive(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAvailableProfiles : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAvailableProfiles() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextAvailableProfiles() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAvailableProfiles(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAvailableProfiles(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextActiveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextActiveProfile() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextActiveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_LoadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_LoadProfile() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_LoadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLoadProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SaveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SaveProfile() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SaveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetActive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetActive() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_SetActive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActive(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActive(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteProfile(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAvailableProfiles : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAvailableProfiles() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAvailableProfiles(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAvailableProfiles() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAvailableProfiles(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAvailableProfiles(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextActiveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextActiveProfile() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextActiveProfile(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextActiveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_LoadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_LoadProfile() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->LoadProfile(context, request, response); }));
    }
    ~WithRawCallbackMethod_LoadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LoadProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SaveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SaveProfile() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SaveProfile(context, request, response); }));
    }
    ~WithRawCallbackMethod_SaveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetActive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetActive() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetActive(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetActive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActive(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActive(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteProfile(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteProfile(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAvailableProfiles : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAvailableProfiles() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* streamer) {
                       return this->StreamedGetNextAvailableProfiles(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAvailableProfiles() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAvailableProfiles(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAvailableProfiles(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::target_velocity_estimator::GetNextAvailableTVEProfilesResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextActiveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextActiveProfile() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* streamer) {
                       return this->StreamedGetNextActiveProfile(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextActiveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextActiveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextActiveProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::target_velocity_estimator::GetNextActiveTVEProfileResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_LoadProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_LoadProfile() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* streamer) {
                       return this->StreamedLoadProfile(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_LoadProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status LoadProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedLoadProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::target_velocity_estimator::LoadTVEProfileRequest,::carbon::frontend::target_velocity_estimator::LoadTVEProfileResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SaveProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SaveProfile() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* streamer) {
                       return this->StreamedSaveProfile(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SaveProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SaveProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSaveProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::target_velocity_estimator::SaveTVEProfileRequest,::carbon::frontend::target_velocity_estimator::SaveTVEProfileResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetActive : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetActive() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* streamer) {
                       return this->StreamedSetActive(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetActive() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetActive(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetActive(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileRequest,::carbon::frontend::target_velocity_estimator::SetActiveTVEProfileResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteProfile : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteProfile() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* streamer) {
                       return this->StreamedDeleteProfile(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteProfile() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteProfile(::grpc::ServerContext* /*context*/, const ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest* /*request*/, ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteProfile(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::target_velocity_estimator::DeleteTVEProfileRequest,::carbon::frontend::target_velocity_estimator::DeleteTVEProfileResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextAvailableProfiles<WithStreamedUnaryMethod_GetNextActiveProfile<WithStreamedUnaryMethod_LoadProfile<WithStreamedUnaryMethod_SaveProfile<WithStreamedUnaryMethod_SetActive<WithStreamedUnaryMethod_DeleteProfile<Service > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextAvailableProfiles<WithStreamedUnaryMethod_GetNextActiveProfile<WithStreamedUnaryMethod_LoadProfile<WithStreamedUnaryMethod_SaveProfile<WithStreamedUnaryMethod_SetActive<WithStreamedUnaryMethod_DeleteProfile<Service > > > > > > StreamedService;
};

}  // namespace target_velocity_estimator
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2ftarget_5fvelocity_5festimator_2eproto__INCLUDED

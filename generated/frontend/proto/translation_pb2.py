# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/translation.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/translation.proto',
  package='carbon.frontend.translation',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n frontend/proto/translation.proto\x12\x1b\x63\x61rbon.frontend.translation\"\x1d\n\x0cIntegerValue\x12\r\n\x05value\x18\x01 \x01(\x03\"\x1c\n\x0b\x44oubleValue\x12\r\n\x05value\x18\x01 \x01(\x01\"\x1c\n\x0bStringValue\x12\r\n\x05value\x18\x01 \x01(\t\"D\n\x10TemperatureValue\x12\x11\n\x07\x63\x65lcius\x18\x01 \x01(\x01H\x00\x12\x14\n\nfahrenheit\x18\x02 \x01(\x01H\x00\x42\x07\n\x05value\"\x1f\n\x0cPercentValue\x12\x0f\n\x07percent\x18\x01 \x01(\r\"\x1d\n\x0cVoltageValue\x12\r\n\x05volts\x18\x01 \x01(\x01\"\x1f\n\x0e\x46requencyValue\x12\r\n\x05hertz\x18\x01 \x01(\x01\"i\n\tAreaValue\x12\x0f\n\x05\x61\x63res\x18\x01 \x01(\x01H\x00\x12\x12\n\x08hectares\x18\x02 \x01(\x01H\x00\x12\x15\n\x0bsquare_feet\x18\x03 \x01(\x01H\x00\x12\x17\n\rsquare_meters\x18\x04 \x01(\x01H\x00\x42\x07\n\x05value\"g\n\rDurationValue\x12\x16\n\x0cmilliseconds\x18\x01 \x01(\x04H\x00\x12\x11\n\x07seconds\x18\x02 \x01(\x04H\x00\x12\x11\n\x07minutes\x18\x03 \x01(\x04H\x00\x12\x0f\n\x05hours\x18\x04 \x01(\x04H\x00\x42\x07\n\x05value\"\xa1\x01\n\rDistanceValue\x12\x15\n\x0bmillimeters\x18\x01 \x01(\x01H\x00\x12\x10\n\x06meters\x18\x02 \x01(\x01H\x00\x12\x14\n\nkilometers\x18\x03 \x01(\x01H\x00\x12\x10\n\x06inches\x18\x04 \x01(\x01H\x00\x12\x0e\n\x04\x66\x65\x65t\x18\x05 \x01(\x01H\x00\x12\x0f\n\x05miles\x18\x06 \x01(\x01H\x00\x12\x15\n\x0b\x63\x65ntimeters\x18\x07 \x01(\x01H\x00\x42\x07\n\x05value\"N\n\nSpeedValue\x12\x1d\n\x13kilometers_per_hour\x18\x01 \x01(\x01H\x00\x12\x18\n\x0emiles_per_hour\x18\x02 \x01(\x01H\x00\x42\x07\n\x05value\"\x97\x06\n\x14TranslationParameter\x12\x0c\n\x04name\x18\x01 \x01(\t\x12>\n\tint_value\x18\x02 \x01(\x0b\x32).carbon.frontend.translation.IntegerValueH\x00\x12@\n\x0c\x64ouble_value\x18\x03 \x01(\x0b\x32(.carbon.frontend.translation.DoubleValueH\x00\x12@\n\x0cstring_value\x18\x04 \x01(\x0b\x32(.carbon.frontend.translation.StringValueH\x00\x12J\n\x11temperature_value\x18\x05 \x01(\x0b\x32-.carbon.frontend.translation.TemperatureValueH\x00\x12\x42\n\rpercent_value\x18\x06 \x01(\x0b\x32).carbon.frontend.translation.PercentValueH\x00\x12\x42\n\rvoltage_value\x18\x07 \x01(\x0b\x32).carbon.frontend.translation.VoltageValueH\x00\x12\x46\n\x0f\x66requency_value\x18\x08 \x01(\x0b\x32+.carbon.frontend.translation.FrequencyValueH\x00\x12<\n\narea_value\x18\t \x01(\x0b\x32&.carbon.frontend.translation.AreaValueH\x00\x12\x44\n\x0e\x64uration_value\x18\n \x01(\x0b\x32*.carbon.frontend.translation.DurationValueH\x00\x12\x44\n\x0e\x64istance_value\x18\x0b \x01(\x0b\x32*.carbon.frontend.translation.DistanceValueH\x00\x12>\n\x0bspeed_value\x18\x0c \x01(\x0b\x32\'.carbon.frontend.translation.SpeedValueH\x00\x42\x07\n\x05valueB\x10Z\x0eproto/frontendb\x06proto3'
)




_INTEGERVALUE = _descriptor.Descriptor(
  name='IntegerValue',
  full_name='carbon.frontend.translation.IntegerValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.translation.IntegerValue.value', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=65,
  serialized_end=94,
)


_DOUBLEVALUE = _descriptor.Descriptor(
  name='DoubleValue',
  full_name='carbon.frontend.translation.DoubleValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.translation.DoubleValue.value', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=96,
  serialized_end=124,
)


_STRINGVALUE = _descriptor.Descriptor(
  name='StringValue',
  full_name='carbon.frontend.translation.StringValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.translation.StringValue.value', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=126,
  serialized_end=154,
)


_TEMPERATUREVALUE = _descriptor.Descriptor(
  name='TemperatureValue',
  full_name='carbon.frontend.translation.TemperatureValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='celcius', full_name='carbon.frontend.translation.TemperatureValue.celcius', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fahrenheit', full_name='carbon.frontend.translation.TemperatureValue.fahrenheit', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='carbon.frontend.translation.TemperatureValue.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=156,
  serialized_end=224,
)


_PERCENTVALUE = _descriptor.Descriptor(
  name='PercentValue',
  full_name='carbon.frontend.translation.PercentValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='percent', full_name='carbon.frontend.translation.PercentValue.percent', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=226,
  serialized_end=257,
)


_VOLTAGEVALUE = _descriptor.Descriptor(
  name='VoltageValue',
  full_name='carbon.frontend.translation.VoltageValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='volts', full_name='carbon.frontend.translation.VoltageValue.volts', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=259,
  serialized_end=288,
)


_FREQUENCYVALUE = _descriptor.Descriptor(
  name='FrequencyValue',
  full_name='carbon.frontend.translation.FrequencyValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='hertz', full_name='carbon.frontend.translation.FrequencyValue.hertz', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=290,
  serialized_end=321,
)


_AREAVALUE = _descriptor.Descriptor(
  name='AreaValue',
  full_name='carbon.frontend.translation.AreaValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='acres', full_name='carbon.frontend.translation.AreaValue.acres', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hectares', full_name='carbon.frontend.translation.AreaValue.hectares', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='square_feet', full_name='carbon.frontend.translation.AreaValue.square_feet', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='square_meters', full_name='carbon.frontend.translation.AreaValue.square_meters', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='carbon.frontend.translation.AreaValue.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=323,
  serialized_end=428,
)


_DURATIONVALUE = _descriptor.Descriptor(
  name='DurationValue',
  full_name='carbon.frontend.translation.DurationValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='milliseconds', full_name='carbon.frontend.translation.DurationValue.milliseconds', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='seconds', full_name='carbon.frontend.translation.DurationValue.seconds', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='minutes', full_name='carbon.frontend.translation.DurationValue.minutes', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hours', full_name='carbon.frontend.translation.DurationValue.hours', index=3,
      number=4, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='carbon.frontend.translation.DurationValue.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=430,
  serialized_end=533,
)


_DISTANCEVALUE = _descriptor.Descriptor(
  name='DistanceValue',
  full_name='carbon.frontend.translation.DistanceValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='millimeters', full_name='carbon.frontend.translation.DistanceValue.millimeters', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='meters', full_name='carbon.frontend.translation.DistanceValue.meters', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='kilometers', full_name='carbon.frontend.translation.DistanceValue.kilometers', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='inches', full_name='carbon.frontend.translation.DistanceValue.inches', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='feet', full_name='carbon.frontend.translation.DistanceValue.feet', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='miles', full_name='carbon.frontend.translation.DistanceValue.miles', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='centimeters', full_name='carbon.frontend.translation.DistanceValue.centimeters', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='carbon.frontend.translation.DistanceValue.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=536,
  serialized_end=697,
)


_SPEEDVALUE = _descriptor.Descriptor(
  name='SpeedValue',
  full_name='carbon.frontend.translation.SpeedValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='kilometers_per_hour', full_name='carbon.frontend.translation.SpeedValue.kilometers_per_hour', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='miles_per_hour', full_name='carbon.frontend.translation.SpeedValue.miles_per_hour', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='carbon.frontend.translation.SpeedValue.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=699,
  serialized_end=777,
)


_TRANSLATIONPARAMETER = _descriptor.Descriptor(
  name='TranslationParameter',
  full_name='carbon.frontend.translation.TranslationParameter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.translation.TranslationParameter.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='int_value', full_name='carbon.frontend.translation.TranslationParameter.int_value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='double_value', full_name='carbon.frontend.translation.TranslationParameter.double_value', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='string_value', full_name='carbon.frontend.translation.TranslationParameter.string_value', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_value', full_name='carbon.frontend.translation.TranslationParameter.temperature_value', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='percent_value', full_name='carbon.frontend.translation.TranslationParameter.percent_value', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='voltage_value', full_name='carbon.frontend.translation.TranslationParameter.voltage_value', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='frequency_value', full_name='carbon.frontend.translation.TranslationParameter.frequency_value', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='area_value', full_name='carbon.frontend.translation.TranslationParameter.area_value', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='duration_value', full_name='carbon.frontend.translation.TranslationParameter.duration_value', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='distance_value', full_name='carbon.frontend.translation.TranslationParameter.distance_value', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='speed_value', full_name='carbon.frontend.translation.TranslationParameter.speed_value', index=11,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='carbon.frontend.translation.TranslationParameter.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=780,
  serialized_end=1571,
)

_TEMPERATUREVALUE.oneofs_by_name['value'].fields.append(
  _TEMPERATUREVALUE.fields_by_name['celcius'])
_TEMPERATUREVALUE.fields_by_name['celcius'].containing_oneof = _TEMPERATUREVALUE.oneofs_by_name['value']
_TEMPERATUREVALUE.oneofs_by_name['value'].fields.append(
  _TEMPERATUREVALUE.fields_by_name['fahrenheit'])
_TEMPERATUREVALUE.fields_by_name['fahrenheit'].containing_oneof = _TEMPERATUREVALUE.oneofs_by_name['value']
_AREAVALUE.oneofs_by_name['value'].fields.append(
  _AREAVALUE.fields_by_name['acres'])
_AREAVALUE.fields_by_name['acres'].containing_oneof = _AREAVALUE.oneofs_by_name['value']
_AREAVALUE.oneofs_by_name['value'].fields.append(
  _AREAVALUE.fields_by_name['hectares'])
_AREAVALUE.fields_by_name['hectares'].containing_oneof = _AREAVALUE.oneofs_by_name['value']
_AREAVALUE.oneofs_by_name['value'].fields.append(
  _AREAVALUE.fields_by_name['square_feet'])
_AREAVALUE.fields_by_name['square_feet'].containing_oneof = _AREAVALUE.oneofs_by_name['value']
_AREAVALUE.oneofs_by_name['value'].fields.append(
  _AREAVALUE.fields_by_name['square_meters'])
_AREAVALUE.fields_by_name['square_meters'].containing_oneof = _AREAVALUE.oneofs_by_name['value']
_DURATIONVALUE.oneofs_by_name['value'].fields.append(
  _DURATIONVALUE.fields_by_name['milliseconds'])
_DURATIONVALUE.fields_by_name['milliseconds'].containing_oneof = _DURATIONVALUE.oneofs_by_name['value']
_DURATIONVALUE.oneofs_by_name['value'].fields.append(
  _DURATIONVALUE.fields_by_name['seconds'])
_DURATIONVALUE.fields_by_name['seconds'].containing_oneof = _DURATIONVALUE.oneofs_by_name['value']
_DURATIONVALUE.oneofs_by_name['value'].fields.append(
  _DURATIONVALUE.fields_by_name['minutes'])
_DURATIONVALUE.fields_by_name['minutes'].containing_oneof = _DURATIONVALUE.oneofs_by_name['value']
_DURATIONVALUE.oneofs_by_name['value'].fields.append(
  _DURATIONVALUE.fields_by_name['hours'])
_DURATIONVALUE.fields_by_name['hours'].containing_oneof = _DURATIONVALUE.oneofs_by_name['value']
_DISTANCEVALUE.oneofs_by_name['value'].fields.append(
  _DISTANCEVALUE.fields_by_name['millimeters'])
_DISTANCEVALUE.fields_by_name['millimeters'].containing_oneof = _DISTANCEVALUE.oneofs_by_name['value']
_DISTANCEVALUE.oneofs_by_name['value'].fields.append(
  _DISTANCEVALUE.fields_by_name['meters'])
_DISTANCEVALUE.fields_by_name['meters'].containing_oneof = _DISTANCEVALUE.oneofs_by_name['value']
_DISTANCEVALUE.oneofs_by_name['value'].fields.append(
  _DISTANCEVALUE.fields_by_name['kilometers'])
_DISTANCEVALUE.fields_by_name['kilometers'].containing_oneof = _DISTANCEVALUE.oneofs_by_name['value']
_DISTANCEVALUE.oneofs_by_name['value'].fields.append(
  _DISTANCEVALUE.fields_by_name['inches'])
_DISTANCEVALUE.fields_by_name['inches'].containing_oneof = _DISTANCEVALUE.oneofs_by_name['value']
_DISTANCEVALUE.oneofs_by_name['value'].fields.append(
  _DISTANCEVALUE.fields_by_name['feet'])
_DISTANCEVALUE.fields_by_name['feet'].containing_oneof = _DISTANCEVALUE.oneofs_by_name['value']
_DISTANCEVALUE.oneofs_by_name['value'].fields.append(
  _DISTANCEVALUE.fields_by_name['miles'])
_DISTANCEVALUE.fields_by_name['miles'].containing_oneof = _DISTANCEVALUE.oneofs_by_name['value']
_DISTANCEVALUE.oneofs_by_name['value'].fields.append(
  _DISTANCEVALUE.fields_by_name['centimeters'])
_DISTANCEVALUE.fields_by_name['centimeters'].containing_oneof = _DISTANCEVALUE.oneofs_by_name['value']
_SPEEDVALUE.oneofs_by_name['value'].fields.append(
  _SPEEDVALUE.fields_by_name['kilometers_per_hour'])
_SPEEDVALUE.fields_by_name['kilometers_per_hour'].containing_oneof = _SPEEDVALUE.oneofs_by_name['value']
_SPEEDVALUE.oneofs_by_name['value'].fields.append(
  _SPEEDVALUE.fields_by_name['miles_per_hour'])
_SPEEDVALUE.fields_by_name['miles_per_hour'].containing_oneof = _SPEEDVALUE.oneofs_by_name['value']
_TRANSLATIONPARAMETER.fields_by_name['int_value'].message_type = _INTEGERVALUE
_TRANSLATIONPARAMETER.fields_by_name['double_value'].message_type = _DOUBLEVALUE
_TRANSLATIONPARAMETER.fields_by_name['string_value'].message_type = _STRINGVALUE
_TRANSLATIONPARAMETER.fields_by_name['temperature_value'].message_type = _TEMPERATUREVALUE
_TRANSLATIONPARAMETER.fields_by_name['percent_value'].message_type = _PERCENTVALUE
_TRANSLATIONPARAMETER.fields_by_name['voltage_value'].message_type = _VOLTAGEVALUE
_TRANSLATIONPARAMETER.fields_by_name['frequency_value'].message_type = _FREQUENCYVALUE
_TRANSLATIONPARAMETER.fields_by_name['area_value'].message_type = _AREAVALUE
_TRANSLATIONPARAMETER.fields_by_name['duration_value'].message_type = _DURATIONVALUE
_TRANSLATIONPARAMETER.fields_by_name['distance_value'].message_type = _DISTANCEVALUE
_TRANSLATIONPARAMETER.fields_by_name['speed_value'].message_type = _SPEEDVALUE
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['int_value'])
_TRANSLATIONPARAMETER.fields_by_name['int_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['double_value'])
_TRANSLATIONPARAMETER.fields_by_name['double_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['string_value'])
_TRANSLATIONPARAMETER.fields_by_name['string_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['temperature_value'])
_TRANSLATIONPARAMETER.fields_by_name['temperature_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['percent_value'])
_TRANSLATIONPARAMETER.fields_by_name['percent_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['voltage_value'])
_TRANSLATIONPARAMETER.fields_by_name['voltage_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['frequency_value'])
_TRANSLATIONPARAMETER.fields_by_name['frequency_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['area_value'])
_TRANSLATIONPARAMETER.fields_by_name['area_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['duration_value'])
_TRANSLATIONPARAMETER.fields_by_name['duration_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['distance_value'])
_TRANSLATIONPARAMETER.fields_by_name['distance_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
_TRANSLATIONPARAMETER.oneofs_by_name['value'].fields.append(
  _TRANSLATIONPARAMETER.fields_by_name['speed_value'])
_TRANSLATIONPARAMETER.fields_by_name['speed_value'].containing_oneof = _TRANSLATIONPARAMETER.oneofs_by_name['value']
DESCRIPTOR.message_types_by_name['IntegerValue'] = _INTEGERVALUE
DESCRIPTOR.message_types_by_name['DoubleValue'] = _DOUBLEVALUE
DESCRIPTOR.message_types_by_name['StringValue'] = _STRINGVALUE
DESCRIPTOR.message_types_by_name['TemperatureValue'] = _TEMPERATUREVALUE
DESCRIPTOR.message_types_by_name['PercentValue'] = _PERCENTVALUE
DESCRIPTOR.message_types_by_name['VoltageValue'] = _VOLTAGEVALUE
DESCRIPTOR.message_types_by_name['FrequencyValue'] = _FREQUENCYVALUE
DESCRIPTOR.message_types_by_name['AreaValue'] = _AREAVALUE
DESCRIPTOR.message_types_by_name['DurationValue'] = _DURATIONVALUE
DESCRIPTOR.message_types_by_name['DistanceValue'] = _DISTANCEVALUE
DESCRIPTOR.message_types_by_name['SpeedValue'] = _SPEEDVALUE
DESCRIPTOR.message_types_by_name['TranslationParameter'] = _TRANSLATIONPARAMETER
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

IntegerValue = _reflection.GeneratedProtocolMessageType('IntegerValue', (_message.Message,), {
  'DESCRIPTOR' : _INTEGERVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.IntegerValue)
  })
_sym_db.RegisterMessage(IntegerValue)

DoubleValue = _reflection.GeneratedProtocolMessageType('DoubleValue', (_message.Message,), {
  'DESCRIPTOR' : _DOUBLEVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.DoubleValue)
  })
_sym_db.RegisterMessage(DoubleValue)

StringValue = _reflection.GeneratedProtocolMessageType('StringValue', (_message.Message,), {
  'DESCRIPTOR' : _STRINGVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.StringValue)
  })
_sym_db.RegisterMessage(StringValue)

TemperatureValue = _reflection.GeneratedProtocolMessageType('TemperatureValue', (_message.Message,), {
  'DESCRIPTOR' : _TEMPERATUREVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.TemperatureValue)
  })
_sym_db.RegisterMessage(TemperatureValue)

PercentValue = _reflection.GeneratedProtocolMessageType('PercentValue', (_message.Message,), {
  'DESCRIPTOR' : _PERCENTVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.PercentValue)
  })
_sym_db.RegisterMessage(PercentValue)

VoltageValue = _reflection.GeneratedProtocolMessageType('VoltageValue', (_message.Message,), {
  'DESCRIPTOR' : _VOLTAGEVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.VoltageValue)
  })
_sym_db.RegisterMessage(VoltageValue)

FrequencyValue = _reflection.GeneratedProtocolMessageType('FrequencyValue', (_message.Message,), {
  'DESCRIPTOR' : _FREQUENCYVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.FrequencyValue)
  })
_sym_db.RegisterMessage(FrequencyValue)

AreaValue = _reflection.GeneratedProtocolMessageType('AreaValue', (_message.Message,), {
  'DESCRIPTOR' : _AREAVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.AreaValue)
  })
_sym_db.RegisterMessage(AreaValue)

DurationValue = _reflection.GeneratedProtocolMessageType('DurationValue', (_message.Message,), {
  'DESCRIPTOR' : _DURATIONVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.DurationValue)
  })
_sym_db.RegisterMessage(DurationValue)

DistanceValue = _reflection.GeneratedProtocolMessageType('DistanceValue', (_message.Message,), {
  'DESCRIPTOR' : _DISTANCEVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.DistanceValue)
  })
_sym_db.RegisterMessage(DistanceValue)

SpeedValue = _reflection.GeneratedProtocolMessageType('SpeedValue', (_message.Message,), {
  'DESCRIPTOR' : _SPEEDVALUE,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.SpeedValue)
  })
_sym_db.RegisterMessage(SpeedValue)

TranslationParameter = _reflection.GeneratedProtocolMessageType('TranslationParameter', (_message.Message,), {
  'DESCRIPTOR' : _TRANSLATIONPARAMETER,
  '__module__' : 'frontend.proto.translation_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.translation.TranslationParameter)
  })
_sym_db.RegisterMessage(TranslationParameter)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import plant_captcha_pb2 as frontend_dot_proto_dot_plant__captcha__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class PlantCaptchaServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StartPlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptcha',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaResponse.FromString,
                )
        self.GetNextPlantCaptchaStatus = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaStatus',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusResponse.FromString,
                )
        self.GetNextPlantCaptchasList = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchasList',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchasListRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchasListResponse.FromString,
                )
        self.DeletePlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/DeletePlantCaptcha',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.DeletePlantCaptchaRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetPlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptcha',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaResponse.FromString,
                )
        self.CancelPlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptcha',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.StartPlantCaptchaUpload = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptchaUpload',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaUploadRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextPlantCaptchaUploadState = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaUploadState',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateResponse.FromString,
                )
        self.SubmitPlantCaptchaResults = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/SubmitPlantCaptchaResults',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.SubmitPlantCaptchaResultsRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetPlantCaptchaItemResults = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptchaItemResults',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsResponse.FromString,
                )
        self.CalculatePlantCaptcha = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/CalculatePlantCaptcha',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.CalculatePlantCaptchaRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.CalculatePlantCaptchaResponse.FromString,
                )
        self.GetOriginalModelinatorConfig = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetOriginalModelinatorConfig',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetOriginalModelinatorConfigRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetOriginalModelinatorConfigResponse.FromString,
                )
        self.GetCaptchaRowStatus = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/GetCaptchaRowStatus',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetCaptchaRowStatusResponse.FromString,
                )
        self.CancelPlantCaptchaOnRow = channel.unary_unary(
                '/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptchaOnRow',
                request_serializer=frontend_dot_proto_dot_plant__captcha__pb2.CancelPlantCaptchaOnRowRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )


class PlantCaptchaServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def StartPlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextPlantCaptchaStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextPlantCaptchasList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeletePlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelPlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartPlantCaptchaUpload(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextPlantCaptchaUploadState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SubmitPlantCaptchaResults(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetPlantCaptchaItemResults(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CalculatePlantCaptcha(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetOriginalModelinatorConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetCaptchaRowStatus(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelPlantCaptchaOnRow(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_PlantCaptchaServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StartPlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.StartPlantCaptcha,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaResponse.SerializeToString,
            ),
            'GetNextPlantCaptchaStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPlantCaptchaStatus,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusResponse.SerializeToString,
            ),
            'GetNextPlantCaptchasList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPlantCaptchasList,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchasListRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchasListResponse.SerializeToString,
            ),
            'DeletePlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.DeletePlantCaptcha,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.DeletePlantCaptchaRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetPlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPlantCaptcha,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaResponse.SerializeToString,
            ),
            'CancelPlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelPlantCaptcha,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartPlantCaptchaUpload': grpc.unary_unary_rpc_method_handler(
                    servicer.StartPlantCaptchaUpload,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaUploadRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextPlantCaptchaUploadState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextPlantCaptchaUploadState,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateResponse.SerializeToString,
            ),
            'SubmitPlantCaptchaResults': grpc.unary_unary_rpc_method_handler(
                    servicer.SubmitPlantCaptchaResults,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.SubmitPlantCaptchaResultsRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetPlantCaptchaItemResults': grpc.unary_unary_rpc_method_handler(
                    servicer.GetPlantCaptchaItemResults,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsResponse.SerializeToString,
            ),
            'CalculatePlantCaptcha': grpc.unary_unary_rpc_method_handler(
                    servicer.CalculatePlantCaptcha,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.CalculatePlantCaptchaRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.CalculatePlantCaptchaResponse.SerializeToString,
            ),
            'GetOriginalModelinatorConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.GetOriginalModelinatorConfig,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.GetOriginalModelinatorConfigRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetOriginalModelinatorConfigResponse.SerializeToString,
            ),
            'GetCaptchaRowStatus': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCaptchaRowStatus,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_plant__captcha__pb2.GetCaptchaRowStatusResponse.SerializeToString,
            ),
            'CancelPlantCaptchaOnRow': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelPlantCaptchaOnRow,
                    request_deserializer=frontend_dot_proto_dot_plant__captcha__pb2.CancelPlantCaptchaOnRowRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.plant_captcha.PlantCaptchaService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class PlantCaptchaService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def StartPlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptcha',
            frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaRequest.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextPlantCaptchaStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaStatus',
            frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusRequest.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextPlantCaptchasList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchasList',
            frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchasListRequest.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchasListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeletePlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/DeletePlantCaptcha',
            frontend_dot_proto_dot_plant__captcha__pb2.DeletePlantCaptchaRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptcha',
            frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaRequest.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CancelPlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptcha',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartPlantCaptchaUpload(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptchaUpload',
            frontend_dot_proto_dot_plant__captcha__pb2.StartPlantCaptchaUploadRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextPlantCaptchaUploadState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaUploadState',
            frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateRequest.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.GetNextPlantCaptchaUploadStateResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SubmitPlantCaptchaResults(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/SubmitPlantCaptchaResults',
            frontend_dot_proto_dot_plant__captcha__pb2.SubmitPlantCaptchaResultsRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetPlantCaptchaItemResults(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptchaItemResults',
            frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsRequest.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.GetPlantCaptchaItemResultsResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CalculatePlantCaptcha(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/CalculatePlantCaptcha',
            frontend_dot_proto_dot_plant__captcha__pb2.CalculatePlantCaptchaRequest.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.CalculatePlantCaptchaResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetOriginalModelinatorConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/GetOriginalModelinatorConfig',
            frontend_dot_proto_dot_plant__captcha__pb2.GetOriginalModelinatorConfigRequest.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.GetOriginalModelinatorConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetCaptchaRowStatus(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/GetCaptchaRowStatus',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_plant__captcha__pb2.GetCaptchaRowStatusResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CancelPlantCaptchaOnRow(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptchaOnRow',
            frontend_dot_proto_dot_plant__captcha__pb2.CancelPlantCaptchaOnRowRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

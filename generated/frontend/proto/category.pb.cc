// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/category.proto

#include "frontend/proto/category.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace category {
constexpr GetNextCategoryDataRequest::GetNextCategoryDataRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct GetNextCategoryDataRequestDefaultTypeInternal {
  constexpr GetNextCategoryDataRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextCategoryDataRequestDefaultTypeInternal() {}
  union {
    GetNextCategoryDataRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextCategoryDataRequestDefaultTypeInternal _GetNextCategoryDataRequest_default_instance_;
constexpr GetNextCategoryDataResponse::GetNextCategoryDataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : categories_()
  , ts_(nullptr){}
struct GetNextCategoryDataResponseDefaultTypeInternal {
  constexpr GetNextCategoryDataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextCategoryDataResponseDefaultTypeInternal() {}
  union {
    GetNextCategoryDataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextCategoryDataResponseDefaultTypeInternal _GetNextCategoryDataResponse_default_instance_;
}  // namespace category
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fcategory_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fcategory_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fcategory_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fcategory_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category::GetNextCategoryDataRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category::GetNextCategoryDataRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category::GetNextCategoryDataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category::GetNextCategoryDataResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::category::GetNextCategoryDataResponse, categories_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::category::GetNextCategoryDataRequest)},
  { 7, -1, -1, sizeof(::carbon::frontend::category::GetNextCategoryDataResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::category::_GetNextCategoryDataRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::category::_GetNextCategoryDataResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fcategory_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035frontend/proto/category.proto\022\030carbon."
  "frontend.category\032\031frontend/proto/util.p"
  "roto\032\035category/proto/category.proto\"I\n\032G"
  "etNextCategoryDataRequest\022+\n\002ts\030\001 \001(\0132\037."
  "carbon.frontend.util.Timestamp\"y\n\033GetNex"
  "tCategoryDataResponse\022+\n\002ts\030\001 \001(\0132\037.carb"
  "on.frontend.util.Timestamp\022-\n\ncategories"
  "\030\002 \003(\0132\031.carbon.category.Category2\226\001\n\017Ca"
  "tegoryService\022\202\001\n\023GetNextCategoryData\0224."
  "carbon.frontend.category.GetNextCategory"
  "DataRequest\0325.carbon.frontend.category.G"
  "etNextCategoryDataResponseB\020Z\016proto/fron"
  "tendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fcategory_2eproto_deps[2] = {
  &::descriptor_table_category_2fproto_2fcategory_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fcategory_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcategory_2eproto = {
  false, false, 492, descriptor_table_protodef_frontend_2fproto_2fcategory_2eproto, "frontend/proto/category.proto", 
  &descriptor_table_frontend_2fproto_2fcategory_2eproto_once, descriptor_table_frontend_2fproto_2fcategory_2eproto_deps, 2, 2,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fcategory_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fcategory_2eproto, file_level_enum_descriptors_frontend_2fproto_2fcategory_2eproto, file_level_service_descriptors_frontend_2fproto_2fcategory_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fcategory_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fcategory_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fcategory_2eproto(&descriptor_table_frontend_2fproto_2fcategory_2eproto);
namespace carbon {
namespace frontend {
namespace category {

// ===================================================================

class GetNextCategoryDataRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextCategoryDataRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextCategoryDataRequest::_Internal::ts(const GetNextCategoryDataRequest* msg) {
  return *msg->ts_;
}
void GetNextCategoryDataRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextCategoryDataRequest::GetNextCategoryDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.category.GetNextCategoryDataRequest)
}
GetNextCategoryDataRequest::GetNextCategoryDataRequest(const GetNextCategoryDataRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.category.GetNextCategoryDataRequest)
}

inline void GetNextCategoryDataRequest::SharedCtor() {
ts_ = nullptr;
}

GetNextCategoryDataRequest::~GetNextCategoryDataRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.category.GetNextCategoryDataRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextCategoryDataRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextCategoryDataRequest::ArenaDtor(void* object) {
  GetNextCategoryDataRequest* _this = reinterpret_cast< GetNextCategoryDataRequest* >(object);
  (void)_this;
}
void GetNextCategoryDataRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextCategoryDataRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextCategoryDataRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.category.GetNextCategoryDataRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextCategoryDataRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextCategoryDataRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.category.GetNextCategoryDataRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.category.GetNextCategoryDataRequest)
  return target;
}

size_t GetNextCategoryDataRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.category.GetNextCategoryDataRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextCategoryDataRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextCategoryDataRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextCategoryDataRequest::GetClassData() const { return &_class_data_; }

void GetNextCategoryDataRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextCategoryDataRequest *>(to)->MergeFrom(
      static_cast<const GetNextCategoryDataRequest &>(from));
}


void GetNextCategoryDataRequest::MergeFrom(const GetNextCategoryDataRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.category.GetNextCategoryDataRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextCategoryDataRequest::CopyFrom(const GetNextCategoryDataRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.category.GetNextCategoryDataRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextCategoryDataRequest::IsInitialized() const {
  return true;
}

void GetNextCategoryDataRequest::InternalSwap(GetNextCategoryDataRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextCategoryDataRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcategory_2eproto_getter, &descriptor_table_frontend_2fproto_2fcategory_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcategory_2eproto[0]);
}

// ===================================================================

class GetNextCategoryDataResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextCategoryDataResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextCategoryDataResponse::_Internal::ts(const GetNextCategoryDataResponse* msg) {
  return *msg->ts_;
}
void GetNextCategoryDataResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void GetNextCategoryDataResponse::clear_categories() {
  categories_.Clear();
}
GetNextCategoryDataResponse::GetNextCategoryDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  categories_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.category.GetNextCategoryDataResponse)
}
GetNextCategoryDataResponse::GetNextCategoryDataResponse(const GetNextCategoryDataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      categories_(from.categories_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.category.GetNextCategoryDataResponse)
}

inline void GetNextCategoryDataResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextCategoryDataResponse::~GetNextCategoryDataResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.category.GetNextCategoryDataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextCategoryDataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextCategoryDataResponse::ArenaDtor(void* object) {
  GetNextCategoryDataResponse* _this = reinterpret_cast< GetNextCategoryDataResponse* >(object);
  (void)_this;
}
void GetNextCategoryDataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextCategoryDataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextCategoryDataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.category.GetNextCategoryDataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  categories_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextCategoryDataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.category.Category categories = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_categories(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextCategoryDataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.category.GetNextCategoryDataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.category.Category categories = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_categories_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_categories(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.category.GetNextCategoryDataResponse)
  return target;
}

size_t GetNextCategoryDataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.category.GetNextCategoryDataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.category.Category categories = 2;
  total_size += 1UL * this->_internal_categories_size();
  for (const auto& msg : this->categories_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextCategoryDataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextCategoryDataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextCategoryDataResponse::GetClassData() const { return &_class_data_; }

void GetNextCategoryDataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextCategoryDataResponse *>(to)->MergeFrom(
      static_cast<const GetNextCategoryDataResponse &>(from));
}


void GetNextCategoryDataResponse::MergeFrom(const GetNextCategoryDataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.category.GetNextCategoryDataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  categories_.MergeFrom(from.categories_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextCategoryDataResponse::CopyFrom(const GetNextCategoryDataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.category.GetNextCategoryDataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextCategoryDataResponse::IsInitialized() const {
  return true;
}

void GetNextCategoryDataResponse::InternalSwap(GetNextCategoryDataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  categories_.InternalSwap(&other->categories_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextCategoryDataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcategory_2eproto_getter, &descriptor_table_frontend_2fproto_2fcategory_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcategory_2eproto[1]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace category
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::category::GetNextCategoryDataRequest* Arena::CreateMaybeMessage< ::carbon::frontend::category::GetNextCategoryDataRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::category::GetNextCategoryDataRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::category::GetNextCategoryDataResponse* Arena::CreateMaybeMessage< ::carbon::frontend::category::GetNextCategoryDataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::category::GetNextCategoryDataResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

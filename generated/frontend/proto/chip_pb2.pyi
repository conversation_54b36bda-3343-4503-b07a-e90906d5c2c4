"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class ChipData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    url: typing___Text = ...
    geohash: typing___Text = ...
    checksum: typing___Text = ...
    content_length: builtin___int = ...

    @property
    def downloaded_ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def last_used_ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        url : typing___Optional[typing___Text] = None,
        geohash : typing___Optional[typing___Text] = None,
        checksum : typing___Optional[typing___Text] = None,
        content_length : typing___Optional[builtin___int] = None,
        downloaded_ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        last_used_ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"downloaded_ts",b"downloaded_ts",u"last_used_ts",b"last_used_ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"checksum",b"checksum",u"content_length",b"content_length",u"downloaded_ts",b"downloaded_ts",u"geohash",b"geohash",u"id",b"id",u"last_used_ts",b"last_used_ts",u"url",b"url"]) -> None: ...
type___ChipData = ChipData

class GetChipMetadataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def chips(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ChipData]: ...

    def __init__(self,
        *,
        chips : typing___Optional[typing___Iterable[type___ChipData]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"chips",b"chips"]) -> None: ...
type___GetChipMetadataResponse = GetChipMetadataResponse

class ChipIdsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    chip_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        chip_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"chip_ids",b"chip_ids"]) -> None: ...
type___ChipIdsResponse = ChipIdsResponse

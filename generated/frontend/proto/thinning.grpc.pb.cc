// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/thinning.proto

#include "frontend/proto/thinning.pb.h"
#include "frontend/proto/thinning.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace thinning {

static const char* ThinningService_method_names[] = {
  "/carbon.frontend.thinning.ThinningService/GetNextConfigurations",
  "/carbon.frontend.thinning.ThinningService/GetNextActiveConf",
  "/carbon.frontend.thinning.ThinningService/DefineConfiguration",
  "/carbon.frontend.thinning.ThinningService/SetActiveConfig",
  "/carbon.frontend.thinning.ThinningService/DeleteConfig",
};

std::unique_ptr< ThinningService::Stub> ThinningService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ThinningService::Stub> stub(new ThinningService::Stub(channel, options));
  return stub;
}

ThinningService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextConfigurations_(ThinningService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextActiveConf_(ThinningService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DefineConfiguration_(ThinningService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetActiveConfig_(ThinningService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteConfig_(ThinningService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ThinningService::Stub::GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextConfigurationsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextConfigurations_, context, request, response);
}

void ThinningService::Stub::async::GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextConfigurationsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextConfigurations_, context, request, response, std::move(f));
}

void ThinningService::Stub::async::GetNextConfigurations(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextConfigurations_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextConfigurationsResponse>* ThinningService::Stub::PrepareAsyncGetNextConfigurationsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::thinning::GetNextConfigurationsResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextConfigurations_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextConfigurationsResponse>* ThinningService::Stub::AsyncGetNextConfigurationsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextConfigurationsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ThinningService::Stub::GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextActiveConfResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextActiveConf_, context, request, response);
}

void ThinningService::Stub::async::GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextActiveConfResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveConf_, context, request, response, std::move(f));
}

void ThinningService::Stub::async::GetNextActiveConf(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveConf_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextActiveConfResponse>* ThinningService::Stub::PrepareAsyncGetNextActiveConfRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::thinning::GetNextActiveConfResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextActiveConf_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::GetNextActiveConfResponse>* ThinningService::Stub::AsyncGetNextActiveConfRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextActiveConfRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ThinningService::Stub::DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::carbon::frontend::thinning::DefineConfigurationResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::thinning::DefineConfigurationRequest, ::carbon::frontend::thinning::DefineConfigurationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DefineConfiguration_, context, request, response);
}

void ThinningService::Stub::async::DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::thinning::DefineConfigurationRequest, ::carbon::frontend::thinning::DefineConfigurationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DefineConfiguration_, context, request, response, std::move(f));
}

void ThinningService::Stub::async::DefineConfiguration(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DefineConfiguration_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DefineConfigurationResponse>* ThinningService::Stub::PrepareAsyncDefineConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::thinning::DefineConfigurationResponse, ::carbon::frontend::thinning::DefineConfigurationRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DefineConfiguration_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DefineConfigurationResponse>* ThinningService::Stub::AsyncDefineConfigurationRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDefineConfigurationRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ThinningService::Stub::SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::carbon::frontend::thinning::SetActiveConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::thinning::SetActiveConfigRequest, ::carbon::frontend::thinning::SetActiveConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetActiveConfig_, context, request, response);
}

void ThinningService::Stub::async::SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::thinning::SetActiveConfigRequest, ::carbon::frontend::thinning::SetActiveConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveConfig_, context, request, response, std::move(f));
}

void ThinningService::Stub::async::SetActiveConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetActiveConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::SetActiveConfigResponse>* ThinningService::Stub::PrepareAsyncSetActiveConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::thinning::SetActiveConfigResponse, ::carbon::frontend::thinning::SetActiveConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetActiveConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::SetActiveConfigResponse>* ThinningService::Stub::AsyncSetActiveConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetActiveConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ThinningService::Stub::DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::carbon::frontend::thinning::DeleteConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::thinning::DeleteConfigRequest, ::carbon::frontend::thinning::DeleteConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteConfig_, context, request, response);
}

void ThinningService::Stub::async::DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::thinning::DeleteConfigRequest, ::carbon::frontend::thinning::DeleteConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteConfig_, context, request, response, std::move(f));
}

void ThinningService::Stub::async::DeleteConfig(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DeleteConfigResponse>* ThinningService::Stub::PrepareAsyncDeleteConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::thinning::DeleteConfigResponse, ::carbon::frontend::thinning::DeleteConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::thinning::DeleteConfigResponse>* ThinningService::Stub::AsyncDeleteConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

ThinningService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ThinningService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ThinningService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextConfigurationsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ThinningService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::thinning::GetNextConfigurationsResponse* resp) {
               return service->GetNextConfigurations(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ThinningService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ThinningService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::thinning::GetNextActiveConfResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ThinningService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::thinning::GetNextActiveConfResponse* resp) {
               return service->GetNextActiveConf(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ThinningService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ThinningService::Service, ::carbon::frontend::thinning::DefineConfigurationRequest, ::carbon::frontend::thinning::DefineConfigurationResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ThinningService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::thinning::DefineConfigurationRequest* req,
             ::carbon::frontend::thinning::DefineConfigurationResponse* resp) {
               return service->DefineConfiguration(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ThinningService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ThinningService::Service, ::carbon::frontend::thinning::SetActiveConfigRequest, ::carbon::frontend::thinning::SetActiveConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ThinningService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::thinning::SetActiveConfigRequest* req,
             ::carbon::frontend::thinning::SetActiveConfigResponse* resp) {
               return service->SetActiveConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ThinningService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ThinningService::Service, ::carbon::frontend::thinning::DeleteConfigRequest, ::carbon::frontend::thinning::DeleteConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ThinningService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::thinning::DeleteConfigRequest* req,
             ::carbon::frontend::thinning::DeleteConfigResponse* resp) {
               return service->DeleteConfig(ctx, req, resp);
             }, this)));
}

ThinningService::Service::~Service() {
}

::grpc::Status ThinningService::Service::GetNextConfigurations(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextConfigurationsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ThinningService::Service::GetNextActiveConf(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::thinning::GetNextActiveConfResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ThinningService::Service::DefineConfiguration(::grpc::ServerContext* context, const ::carbon::frontend::thinning::DefineConfigurationRequest* request, ::carbon::frontend::thinning::DefineConfigurationResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ThinningService::Service::SetActiveConfig(::grpc::ServerContext* context, const ::carbon::frontend::thinning::SetActiveConfigRequest* request, ::carbon::frontend::thinning::SetActiveConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ThinningService::Service::DeleteConfig(::grpc::ServerContext* context, const ::carbon::frontend::thinning::DeleteConfigRequest* request, ::carbon::frontend::thinning::DeleteConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace thinning


# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/software.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/software.proto',
  package='carbon.frontend.software',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1d\x66rontend/proto/software.proto\x12\x18\x63\x61rbon.frontend.software\x1a\x19\x66rontend/proto/util.proto\"@\n\x0fSoftwareVersion\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x11\n\tavailable\x18\x02 \x01(\x08\x12\r\n\x05ready\x18\x03 \x01(\x08\"\x94\x02\n\x18HostSoftwareVersionState\x12\x11\n\thost_name\x18\x01 \x01(\t\x12\x0f\n\x07host_id\x18\x02 \x01(\r\x12\x0e\n\x06\x61\x63tive\x18\x03 \x01(\x08\x12:\n\x07\x63urrent\x18\x04 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12\x39\n\x06target\x18\x05 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12;\n\x08previous\x18\x06 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12\x10\n\x08updating\x18\x07 \x01(\x08\"\x92\x03\n\x14SoftwareVersionState\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12:\n\x07\x63urrent\x18\x02 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12\x39\n\x06target\x18\x03 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12;\n\x08previous\x18\x04 \x01(\x0b\x32).carbon.frontend.software.SoftwareVersion\x12\x10\n\x08updating\x18\x05 \x01(\x08\x12$\n\x1cshow_software_update_to_user\x18\x06 \x01(\x08\x12G\n\x0bhost_states\x18\x07 \x03(\x0b\x32\x32.carbon.frontend.software.HostSoftwareVersionState\x12\x18\n\x10version_mismatch\x18\x08 \x01(\x08\"c\n\x1bSoftwareVersionStateRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x17\n\x0fget_host_states\x18\x02 \x01(\x08\"$\n\x11UpdateHostRequest\x12\x0f\n\x07host_id\x18\x01 \x01(\r2\xc8\x03\n\x0fSoftwareService\x12\x84\x01\n\x1bGetNextSoftwareVersionState\x12\x35.carbon.frontend.software.SoftwareVersionStateRequest\x1a..carbon.frontend.software.SoftwareVersionState\x12V\n\nUpdateHost\x12+.carbon.frontend.software.UpdateHostRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x42\n\x06Update\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12\x42\n\x06Revert\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12N\n\x12\x46ixVersionMismatch\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_SOFTWAREVERSION = _descriptor.Descriptor(
  name='SoftwareVersion',
  full_name='carbon.frontend.software.SoftwareVersion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='carbon.frontend.software.SoftwareVersion.tag', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='available', full_name='carbon.frontend.software.SoftwareVersion.available', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ready', full_name='carbon.frontend.software.SoftwareVersion.ready', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=86,
  serialized_end=150,
)


_HOSTSOFTWAREVERSIONSTATE = _descriptor.Descriptor(
  name='HostSoftwareVersionState',
  full_name='carbon.frontend.software.HostSoftwareVersionState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='host_name', full_name='carbon.frontend.software.HostSoftwareVersionState.host_name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='host_id', full_name='carbon.frontend.software.HostSoftwareVersionState.host_id', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active', full_name='carbon.frontend.software.HostSoftwareVersionState.active', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current', full_name='carbon.frontend.software.HostSoftwareVersionState.current', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target', full_name='carbon.frontend.software.HostSoftwareVersionState.target', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='previous', full_name='carbon.frontend.software.HostSoftwareVersionState.previous', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='updating', full_name='carbon.frontend.software.HostSoftwareVersionState.updating', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=153,
  serialized_end=429,
)


_SOFTWAREVERSIONSTATE = _descriptor.Descriptor(
  name='SoftwareVersionState',
  full_name='carbon.frontend.software.SoftwareVersionState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.software.SoftwareVersionState.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current', full_name='carbon.frontend.software.SoftwareVersionState.current', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target', full_name='carbon.frontend.software.SoftwareVersionState.target', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='previous', full_name='carbon.frontend.software.SoftwareVersionState.previous', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='updating', full_name='carbon.frontend.software.SoftwareVersionState.updating', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='show_software_update_to_user', full_name='carbon.frontend.software.SoftwareVersionState.show_software_update_to_user', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='host_states', full_name='carbon.frontend.software.SoftwareVersionState.host_states', index=6,
      number=7, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version_mismatch', full_name='carbon.frontend.software.SoftwareVersionState.version_mismatch', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=432,
  serialized_end=834,
)


_SOFTWAREVERSIONSTATEREQUEST = _descriptor.Descriptor(
  name='SoftwareVersionStateRequest',
  full_name='carbon.frontend.software.SoftwareVersionStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.software.SoftwareVersionStateRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='get_host_states', full_name='carbon.frontend.software.SoftwareVersionStateRequest.get_host_states', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=836,
  serialized_end=935,
)


_UPDATEHOSTREQUEST = _descriptor.Descriptor(
  name='UpdateHostRequest',
  full_name='carbon.frontend.software.UpdateHostRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='host_id', full_name='carbon.frontend.software.UpdateHostRequest.host_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=937,
  serialized_end=973,
)

_HOSTSOFTWAREVERSIONSTATE.fields_by_name['current'].message_type = _SOFTWAREVERSION
_HOSTSOFTWAREVERSIONSTATE.fields_by_name['target'].message_type = _SOFTWAREVERSION
_HOSTSOFTWAREVERSIONSTATE.fields_by_name['previous'].message_type = _SOFTWAREVERSION
_SOFTWAREVERSIONSTATE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_SOFTWAREVERSIONSTATE.fields_by_name['current'].message_type = _SOFTWAREVERSION
_SOFTWAREVERSIONSTATE.fields_by_name['target'].message_type = _SOFTWAREVERSION
_SOFTWAREVERSIONSTATE.fields_by_name['previous'].message_type = _SOFTWAREVERSION
_SOFTWAREVERSIONSTATE.fields_by_name['host_states'].message_type = _HOSTSOFTWAREVERSIONSTATE
_SOFTWAREVERSIONSTATEREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['SoftwareVersion'] = _SOFTWAREVERSION
DESCRIPTOR.message_types_by_name['HostSoftwareVersionState'] = _HOSTSOFTWAREVERSIONSTATE
DESCRIPTOR.message_types_by_name['SoftwareVersionState'] = _SOFTWAREVERSIONSTATE
DESCRIPTOR.message_types_by_name['SoftwareVersionStateRequest'] = _SOFTWAREVERSIONSTATEREQUEST
DESCRIPTOR.message_types_by_name['UpdateHostRequest'] = _UPDATEHOSTREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SoftwareVersion = _reflection.GeneratedProtocolMessageType('SoftwareVersion', (_message.Message,), {
  'DESCRIPTOR' : _SOFTWAREVERSION,
  '__module__' : 'frontend.proto.software_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.software.SoftwareVersion)
  })
_sym_db.RegisterMessage(SoftwareVersion)

HostSoftwareVersionState = _reflection.GeneratedProtocolMessageType('HostSoftwareVersionState', (_message.Message,), {
  'DESCRIPTOR' : _HOSTSOFTWAREVERSIONSTATE,
  '__module__' : 'frontend.proto.software_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.software.HostSoftwareVersionState)
  })
_sym_db.RegisterMessage(HostSoftwareVersionState)

SoftwareVersionState = _reflection.GeneratedProtocolMessageType('SoftwareVersionState', (_message.Message,), {
  'DESCRIPTOR' : _SOFTWAREVERSIONSTATE,
  '__module__' : 'frontend.proto.software_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.software.SoftwareVersionState)
  })
_sym_db.RegisterMessage(SoftwareVersionState)

SoftwareVersionStateRequest = _reflection.GeneratedProtocolMessageType('SoftwareVersionStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _SOFTWAREVERSIONSTATEREQUEST,
  '__module__' : 'frontend.proto.software_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.software.SoftwareVersionStateRequest)
  })
_sym_db.RegisterMessage(SoftwareVersionStateRequest)

UpdateHostRequest = _reflection.GeneratedProtocolMessageType('UpdateHostRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEHOSTREQUEST,
  '__module__' : 'frontend.proto.software_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.software.UpdateHostRequest)
  })
_sym_db.RegisterMessage(UpdateHostRequest)


DESCRIPTOR._options = None

_SOFTWARESERVICE = _descriptor.ServiceDescriptor(
  name='SoftwareService',
  full_name='carbon.frontend.software.SoftwareService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=976,
  serialized_end=1432,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextSoftwareVersionState',
    full_name='carbon.frontend.software.SoftwareService.GetNextSoftwareVersionState',
    index=0,
    containing_service=None,
    input_type=_SOFTWAREVERSIONSTATEREQUEST,
    output_type=_SOFTWAREVERSIONSTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UpdateHost',
    full_name='carbon.frontend.software.SoftwareService.UpdateHost',
    index=1,
    containing_service=None,
    input_type=_UPDATEHOSTREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Update',
    full_name='carbon.frontend.software.SoftwareService.Update',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='Revert',
    full_name='carbon.frontend.software.SoftwareService.Revert',
    index=3,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='FixVersionMismatch',
    full_name='carbon.frontend.software.SoftwareService.FixVersionMismatch',
    index=4,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_SOFTWARESERVICE)

DESCRIPTOR.services_by_name['SoftwareService'] = _SOFTWARESERVICE

# @@protoc_insertion_point(module_scope)

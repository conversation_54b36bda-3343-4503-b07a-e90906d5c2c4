// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/crosshair.proto
#ifndef GRPC_frontend_2fproto_2fcrosshair_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fcrosshair_2eproto__INCLUDED

#include "frontend/proto/crosshair.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace crosshair {

class CrosshairService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.crosshair.CrosshairService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncStartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncStartAutoCalibrateCrosshairRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncStartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncStartAutoCalibrateCrosshairRaw(context, request, cq));
    }
    virtual ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncStartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncStartAutoCalibrateAllCrosshairsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncStartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncStartAutoCalibrateAllCrosshairsRaw(context, request, cq));
    }
    virtual ::grpc::Status StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncStopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncStopAutoCalibrateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncStopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncStopAutoCalibrateRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::carbon::frontend::crosshair::CrosshairPositionState* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::CrosshairPositionState>> AsyncGetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::CrosshairPositionState>>(AsyncGetNextCrosshairStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::CrosshairPositionState>> PrepareAsyncGetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::CrosshairPositionState>>(PrepareAsyncGetNextCrosshairStateRaw(context, request, cq));
    }
    virtual ::grpc::Status SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetCrosshairPositionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetCrosshairPositionRaw(context, request, cq));
    }
    virtual ::grpc::Status MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncMoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncMoveScannerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncMoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncMoveScannerRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>> AsyncGetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>>(AsyncGetNextAutoCrossHairCalStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>> PrepareAsyncGetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>>(PrepareAsyncGetNextAutoCrossHairCalStateRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncStartAutoCalibrateCrosshairRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncStartAutoCalibrateCrosshairRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncStartAutoCalibrateAllCrosshairsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncStartAutoCalibrateAllCrosshairsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncStopAutoCalibrateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncStopAutoCalibrateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::CrosshairPositionState>* AsyncGetNextCrosshairStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::CrosshairPositionState>* PrepareAsyncGetNextCrosshairStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetCrosshairPositionRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetCrosshairPositionRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncMoveScannerRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncMoveScannerRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* AsyncGetNextAutoCrossHairCalStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* PrepareAsyncGetNextAutoCrossHairCalStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncStartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncStartAutoCalibrateCrosshairRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncStartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncStartAutoCalibrateCrosshairRaw(context, request, cq));
    }
    ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncStartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncStartAutoCalibrateAllCrosshairsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncStartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncStartAutoCalibrateAllCrosshairsRaw(context, request, cq));
    }
    ::grpc::Status StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncStopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncStopAutoCalibrateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncStopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncStopAutoCalibrateRaw(context, request, cq));
    }
    ::grpc::Status GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::carbon::frontend::crosshair::CrosshairPositionState* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::CrosshairPositionState>> AsyncGetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::CrosshairPositionState>>(AsyncGetNextCrosshairStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::CrosshairPositionState>> PrepareAsyncGetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::CrosshairPositionState>>(PrepareAsyncGetNextCrosshairStateRaw(context, request, cq));
    }
    ::grpc::Status SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetCrosshairPositionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetCrosshairPositionRaw(context, request, cq));
    }
    ::grpc::Status MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncMoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncMoveScannerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncMoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncMoveScannerRaw(context, request, cq));
    }
    ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>> AsyncGetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>>(AsyncGetNextAutoCrossHairCalStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>> PrepareAsyncGetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>>(PrepareAsyncGetNextAutoCrossHairCalStateRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response, std::function<void(::grpc::Status)>) override;
      void GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncStartAutoCalibrateCrosshairRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncStartAutoCalibrateCrosshairRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncStartAutoCalibrateAllCrosshairsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncStartAutoCalibrateAllCrosshairsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncStopAutoCalibrateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncStopAutoCalibrateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::CrosshairPositionState>* AsyncGetNextCrosshairStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::CrosshairPositionState>* PrepareAsyncGetNextCrosshairStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetCrosshairPositionRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetCrosshairPositionRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncMoveScannerRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncMoveScannerRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* AsyncGetNextAutoCrossHairCalStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* PrepareAsyncGetNextAutoCrossHairCalStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_StartAutoCalibrateCrosshair_;
    const ::grpc::internal::RpcMethod rpcmethod_StartAutoCalibrateAllCrosshairs_;
    const ::grpc::internal::RpcMethod rpcmethod_StopAutoCalibrate_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextCrosshairState_;
    const ::grpc::internal::RpcMethod rpcmethod_SetCrosshairPosition_;
    const ::grpc::internal::RpcMethod rpcmethod_MoveScanner_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAutoCrossHairCalState_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ServerContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status StopAutoCalibrate(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextCrosshairState(::grpc::ServerContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response);
    virtual ::grpc::Status SetCrosshairPosition(::grpc::ServerContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status MoveScanner(::grpc::ServerContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ServerContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_StartAutoCalibrateCrosshair : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartAutoCalibrateCrosshair() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_StartAutoCalibrateCrosshair() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartAutoCalibrateCrosshair(::grpc::ServerContext* context, ::carbon::frontend::camera::CameraRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartAutoCalibrateAllCrosshairs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartAutoCalibrateAllCrosshairs() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_StartAutoCalibrateAllCrosshairs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartAutoCalibrateAllCrosshairs(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StopAutoCalibrate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StopAutoCalibrate() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_StopAutoCalibrate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopAutoCalibrate(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStopAutoCalibrate(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextCrosshairState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextCrosshairState() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GetNextCrosshairState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCrosshairState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::CrosshairPositionRequest* /*request*/, ::carbon::frontend::crosshair::CrosshairPositionState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCrosshairState(::grpc::ServerContext* context, ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::crosshair::CrosshairPositionState>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetCrosshairPosition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetCrosshairPosition() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_SetCrosshairPosition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCrosshairPosition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetCrosshairPosition(::grpc::ServerContext* context, ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_MoveScanner : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_MoveScanner() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_MoveScanner() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MoveScanner(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::MoveScannerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMoveScanner(::grpc::ServerContext* context, ::carbon::frontend::crosshair::MoveScannerRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAutoCrossHairCalState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAutoCrossHairCalState() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetNextAutoCrossHairCalState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* /*request*/, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAutoCrossHairCalState(::grpc::ServerContext* context, ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_StartAutoCalibrateCrosshair<WithAsyncMethod_StartAutoCalibrateAllCrosshairs<WithAsyncMethod_StopAutoCalibrate<WithAsyncMethod_GetNextCrosshairState<WithAsyncMethod_SetCrosshairPosition<WithAsyncMethod_MoveScanner<WithAsyncMethod_GetNextAutoCrossHairCalState<Service > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_StartAutoCalibrateCrosshair : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartAutoCalibrateCrosshair() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response) { return this->StartAutoCalibrateCrosshair(context, request, response); }));}
    void SetMessageAllocatorFor_StartAutoCalibrateCrosshair(
        ::grpc::MessageAllocator< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartAutoCalibrateCrosshair() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartAutoCalibrateCrosshair(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartAutoCalibrateAllCrosshairs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartAutoCalibrateAllCrosshairs() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->StartAutoCalibrateAllCrosshairs(context, request, response); }));}
    void SetMessageAllocatorFor_StartAutoCalibrateAllCrosshairs(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartAutoCalibrateAllCrosshairs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartAutoCalibrateAllCrosshairs(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StopAutoCalibrate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StopAutoCalibrate() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->StopAutoCalibrate(context, request, response); }));}
    void SetMessageAllocatorFor_StopAutoCalibrate(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StopAutoCalibrate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopAutoCalibrate(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StopAutoCalibrate(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextCrosshairState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextCrosshairState() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::crosshair::CrosshairPositionRequest, ::carbon::frontend::crosshair::CrosshairPositionState>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response) { return this->GetNextCrosshairState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextCrosshairState(
        ::grpc::MessageAllocator< ::carbon::frontend::crosshair::CrosshairPositionRequest, ::carbon::frontend::crosshair::CrosshairPositionState>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::crosshair::CrosshairPositionRequest, ::carbon::frontend::crosshair::CrosshairPositionState>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextCrosshairState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCrosshairState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::CrosshairPositionRequest* /*request*/, ::carbon::frontend::crosshair::CrosshairPositionState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCrosshairState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::crosshair::CrosshairPositionRequest* /*request*/, ::carbon::frontend::crosshair::CrosshairPositionState* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetCrosshairPosition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetCrosshairPosition() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetCrosshairPosition(context, request, response); }));}
    void SetMessageAllocatorFor_SetCrosshairPosition(
        ::grpc::MessageAllocator< ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetCrosshairPosition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCrosshairPosition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetCrosshairPosition(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_MoveScanner : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_MoveScanner() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::crosshair::MoveScannerRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response) { return this->MoveScanner(context, request, response); }));}
    void SetMessageAllocatorFor_MoveScanner(
        ::grpc::MessageAllocator< ::carbon::frontend::crosshair::MoveScannerRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::crosshair::MoveScannerRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_MoveScanner() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MoveScanner(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::MoveScannerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* MoveScanner(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::crosshair::MoveScannerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextAutoCrossHairCalState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAutoCrossHairCalState() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response) { return this->GetNextAutoCrossHairCalState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAutoCrossHairCalState(
        ::grpc::MessageAllocator< ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAutoCrossHairCalState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* /*request*/, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAutoCrossHairCalState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* /*request*/, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_StartAutoCalibrateCrosshair<WithCallbackMethod_StartAutoCalibrateAllCrosshairs<WithCallbackMethod_StopAutoCalibrate<WithCallbackMethod_GetNextCrosshairState<WithCallbackMethod_SetCrosshairPosition<WithCallbackMethod_MoveScanner<WithCallbackMethod_GetNextAutoCrossHairCalState<Service > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_StartAutoCalibrateCrosshair : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartAutoCalibrateCrosshair() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_StartAutoCalibrateCrosshair() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartAutoCalibrateAllCrosshairs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartAutoCalibrateAllCrosshairs() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_StartAutoCalibrateAllCrosshairs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StopAutoCalibrate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StopAutoCalibrate() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_StopAutoCalibrate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopAutoCalibrate(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextCrosshairState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextCrosshairState() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GetNextCrosshairState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCrosshairState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::CrosshairPositionRequest* /*request*/, ::carbon::frontend::crosshair::CrosshairPositionState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetCrosshairPosition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetCrosshairPosition() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_SetCrosshairPosition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCrosshairPosition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_MoveScanner : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_MoveScanner() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_MoveScanner() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MoveScanner(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::MoveScannerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextAutoCrossHairCalState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAutoCrossHairCalState() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetNextAutoCrossHairCalState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* /*request*/, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartAutoCalibrateCrosshair : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartAutoCalibrateCrosshair() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_StartAutoCalibrateCrosshair() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartAutoCalibrateCrosshair(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartAutoCalibrateAllCrosshairs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartAutoCalibrateAllCrosshairs() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_StartAutoCalibrateAllCrosshairs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartAutoCalibrateAllCrosshairs(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StopAutoCalibrate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StopAutoCalibrate() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_StopAutoCalibrate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopAutoCalibrate(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStopAutoCalibrate(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextCrosshairState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextCrosshairState() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GetNextCrosshairState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCrosshairState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::CrosshairPositionRequest* /*request*/, ::carbon::frontend::crosshair::CrosshairPositionState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCrosshairState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetCrosshairPosition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetCrosshairPosition() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_SetCrosshairPosition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCrosshairPosition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetCrosshairPosition(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_MoveScanner : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_MoveScanner() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_MoveScanner() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MoveScanner(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::MoveScannerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMoveScanner(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAutoCrossHairCalState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAutoCrossHairCalState() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetNextAutoCrossHairCalState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* /*request*/, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAutoCrossHairCalState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartAutoCalibrateCrosshair : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartAutoCalibrateCrosshair() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartAutoCalibrateCrosshair(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartAutoCalibrateCrosshair() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartAutoCalibrateCrosshair(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartAutoCalibrateAllCrosshairs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartAutoCalibrateAllCrosshairs() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartAutoCalibrateAllCrosshairs(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartAutoCalibrateAllCrosshairs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartAutoCalibrateAllCrosshairs(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StopAutoCalibrate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StopAutoCalibrate() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StopAutoCalibrate(context, request, response); }));
    }
    ~WithRawCallbackMethod_StopAutoCalibrate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StopAutoCalibrate(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StopAutoCalibrate(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextCrosshairState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextCrosshairState() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextCrosshairState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextCrosshairState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCrosshairState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::CrosshairPositionRequest* /*request*/, ::carbon::frontend::crosshair::CrosshairPositionState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCrosshairState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetCrosshairPosition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetCrosshairPosition() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetCrosshairPosition(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetCrosshairPosition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCrosshairPosition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetCrosshairPosition(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_MoveScanner : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_MoveScanner() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->MoveScanner(context, request, response); }));
    }
    ~WithRawCallbackMethod_MoveScanner() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MoveScanner(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::MoveScannerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* MoveScanner(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAutoCrossHairCalState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAutoCrossHairCalState() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAutoCrossHairCalState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAutoCrossHairCalState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* /*request*/, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAutoCrossHairCalState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartAutoCalibrateCrosshair : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartAutoCalibrateCrosshair() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedStartAutoCalibrateCrosshair(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartAutoCalibrateCrosshair() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartAutoCalibrateCrosshair(::grpc::ServerContext* /*context*/, const ::carbon::frontend::camera::CameraRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartAutoCalibrateCrosshair(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::camera::CameraRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartAutoCalibrateAllCrosshairs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartAutoCalibrateAllCrosshairs() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedStartAutoCalibrateAllCrosshairs(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartAutoCalibrateAllCrosshairs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartAutoCalibrateAllCrosshairs(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StopAutoCalibrate : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StopAutoCalibrate() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedStopAutoCalibrate(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StopAutoCalibrate() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StopAutoCalibrate(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStopAutoCalibrate(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextCrosshairState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextCrosshairState() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::crosshair::CrosshairPositionRequest, ::carbon::frontend::crosshair::CrosshairPositionState>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::crosshair::CrosshairPositionRequest, ::carbon::frontend::crosshair::CrosshairPositionState>* streamer) {
                       return this->StreamedGetNextCrosshairState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextCrosshairState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextCrosshairState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::CrosshairPositionRequest* /*request*/, ::carbon::frontend::crosshair::CrosshairPositionState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextCrosshairState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::crosshair::CrosshairPositionRequest,::carbon::frontend::crosshair::CrosshairPositionState>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetCrosshairPosition : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetCrosshairPosition() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetCrosshairPosition(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetCrosshairPosition() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetCrosshairPosition(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetCrosshairPosition(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::crosshair::SetCrosshairPositionRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_MoveScanner : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_MoveScanner() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::crosshair::MoveScannerRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::crosshair::MoveScannerRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedMoveScanner(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_MoveScanner() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status MoveScanner(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::MoveScannerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedMoveScanner(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::crosshair::MoveScannerRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAutoCrossHairCalState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAutoCrossHairCalState() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* streamer) {
                       return this->StreamedGetNextAutoCrossHairCalState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAutoCrossHairCalState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAutoCrossHairCalState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* /*request*/, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAutoCrossHairCalState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest,::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_StartAutoCalibrateCrosshair<WithStreamedUnaryMethod_StartAutoCalibrateAllCrosshairs<WithStreamedUnaryMethod_StopAutoCalibrate<WithStreamedUnaryMethod_GetNextCrosshairState<WithStreamedUnaryMethod_SetCrosshairPosition<WithStreamedUnaryMethod_MoveScanner<WithStreamedUnaryMethod_GetNextAutoCrossHairCalState<Service > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_StartAutoCalibrateCrosshair<WithStreamedUnaryMethod_StartAutoCalibrateAllCrosshairs<WithStreamedUnaryMethod_StopAutoCalibrate<WithStreamedUnaryMethod_GetNextCrosshairState<WithStreamedUnaryMethod_SetCrosshairPosition<WithStreamedUnaryMethod_MoveScanner<WithStreamedUnaryMethod_GetNextAutoCrossHairCalState<Service > > > > > > > StreamedService;
};

}  // namespace crosshair
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fcrosshair_2eproto__INCLUDED

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/banding.proto
#ifndef GRPC_frontend_2fproto_2fbanding_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fbanding_2eproto__INCLUDED

#include "frontend/proto/banding.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace banding {

class BandingService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.banding.BandingService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::LoadBandingDefsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::LoadBandingDefsResponse>> AsyncLoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::LoadBandingDefsResponse>>(AsyncLoadBandingDefsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::LoadBandingDefsResponse>> PrepareAsyncLoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::LoadBandingDefsResponse>>(PrepareAsyncLoadBandingDefsRaw(context, request, cq));
    }
    virtual ::grpc::Status SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSaveBandingDefRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSaveBandingDefRaw(context, request, cq));
    }
    virtual ::grpc::Status DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDeleteBandingDefRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteBandingDefRaw(context, request, cq));
    }
    virtual ::grpc::Status SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetActiveBandingDefRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetActiveBandingDefRaw(context, request, cq));
    }
    virtual ::grpc::Status GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetActiveBandingDefResponse>> AsyncGetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetActiveBandingDefResponse>>(AsyncGetActiveBandingDefRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetActiveBandingDefResponse>> PrepareAsyncGetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetActiveBandingDefResponse>>(PrepareAsyncGetActiveBandingDefRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataResponse>> AsyncGetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataResponse>>(AsyncGetNextVisualizationDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataResponse>> PrepareAsyncGetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataResponse>>(PrepareAsyncGetNextVisualizationDataRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationData2Response>> AsyncGetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationData2Response>>(AsyncGetNextVisualizationData2Raw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationData2Response>> PrepareAsyncGetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationData2Response>>(PrepareAsyncGetNextVisualizationData2Raw(context, request, cq));
    }
    virtual ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>> AsyncGetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>>(AsyncGetNextVisualizationDataForAllRowsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>> PrepareAsyncGetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>>(PrepareAsyncGetNextVisualizationDataForAllRowsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::aimbot::GetDimensionsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::aimbot::GetDimensionsResponse>> AsyncGetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::aimbot::GetDimensionsResponse>>(AsyncGetDimensionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::aimbot::GetDimensionsResponse>> PrepareAsyncGetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::aimbot::GetDimensionsResponse>>(PrepareAsyncGetDimensionsRaw(context, request, cq));
    }
    virtual ::grpc::Status SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>> AsyncSetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>>(AsyncSetBandingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>> PrepareAsyncSetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>>(PrepareAsyncSetBandingEnabledRaw(context, request, cq));
    }
    virtual ::grpc::Status IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>> AsyncIsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>>(AsyncIsBandingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>> PrepareAsyncIsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>>(PrepareAsyncIsBandingEnabledRaw(context, request, cq));
    }
    virtual ::grpc::Status SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>> AsyncSetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>>(AsyncSetDynamicBandingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>> PrepareAsyncSetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>>(PrepareAsyncSetDynamicBandingEnabledRaw(context, request, cq));
    }
    virtual ::grpc::Status IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>> AsyncIsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>>(AsyncIsDynamicBandingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>> PrepareAsyncIsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>>(PrepareAsyncIsDynamicBandingEnabledRaw(context, request, cq));
    }
    virtual ::grpc::Status GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetVisualizationMetadataResponse>> AsyncGetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetVisualizationMetadataResponse>>(AsyncGetVisualizationMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetVisualizationMetadataResponse>> PrepareAsyncGetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetVisualizationMetadataResponse>>(PrepareAsyncGetVisualizationMetadataRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::banding::GetNextBandingStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextBandingStateResponse>> AsyncGetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextBandingStateResponse>>(AsyncGetNextBandingStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextBandingStateResponse>> PrepareAsyncGetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextBandingStateResponse>>(PrepareAsyncGetNextBandingStateRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::LoadBandingDefsResponse>* AsyncLoadBandingDefsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::LoadBandingDefsResponse>* PrepareAsyncLoadBandingDefsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSaveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSaveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDeleteBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetActiveBandingDefResponse>* AsyncGetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetActiveBandingDefResponse>* PrepareAsyncGetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataResponse>* AsyncGetNextVisualizationDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataResponse>* PrepareAsyncGetNextVisualizationDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationData2Response>* AsyncGetNextVisualizationData2Raw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationData2Response>* PrepareAsyncGetNextVisualizationData2Raw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* AsyncGetNextVisualizationDataForAllRowsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* PrepareAsyncGetNextVisualizationDataForAllRowsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::aimbot::GetDimensionsResponse>* AsyncGetDimensionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::aimbot::GetDimensionsResponse>* PrepareAsyncGetDimensionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>* AsyncSetBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>* PrepareAsyncSetBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>* AsyncIsBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>* PrepareAsyncIsBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>* AsyncSetDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::SetBandingEnabledResponse>* PrepareAsyncSetDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>* AsyncIsDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::IsBandingEnabledResponse>* PrepareAsyncIsDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetVisualizationMetadataResponse>* AsyncGetVisualizationMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetVisualizationMetadataResponse>* PrepareAsyncGetVisualizationMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextBandingStateResponse>* AsyncGetNextBandingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::banding::GetNextBandingStateResponse>* PrepareAsyncGetNextBandingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::LoadBandingDefsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::LoadBandingDefsResponse>> AsyncLoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::LoadBandingDefsResponse>>(AsyncLoadBandingDefsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::LoadBandingDefsResponse>> PrepareAsyncLoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::LoadBandingDefsResponse>>(PrepareAsyncLoadBandingDefsRaw(context, request, cq));
    }
    ::grpc::Status SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSaveBandingDefRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSaveBandingDefRaw(context, request, cq));
    }
    ::grpc::Status DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDeleteBandingDefRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDeleteBandingDefRaw(context, request, cq));
    }
    ::grpc::Status SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetActiveBandingDefRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetActiveBandingDefRaw(context, request, cq));
    }
    ::grpc::Status GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetActiveBandingDefResponse>> AsyncGetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetActiveBandingDefResponse>>(AsyncGetActiveBandingDefRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetActiveBandingDefResponse>> PrepareAsyncGetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetActiveBandingDefResponse>>(PrepareAsyncGetActiveBandingDefRaw(context, request, cq));
    }
    ::grpc::Status GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataResponse>> AsyncGetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataResponse>>(AsyncGetNextVisualizationDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataResponse>> PrepareAsyncGetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataResponse>>(PrepareAsyncGetNextVisualizationDataRaw(context, request, cq));
    }
    ::grpc::Status GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationData2Response>> AsyncGetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationData2Response>>(AsyncGetNextVisualizationData2Raw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationData2Response>> PrepareAsyncGetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationData2Response>>(PrepareAsyncGetNextVisualizationData2Raw(context, request, cq));
    }
    ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>> AsyncGetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>>(AsyncGetNextVisualizationDataForAllRowsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>> PrepareAsyncGetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>>(PrepareAsyncGetNextVisualizationDataForAllRowsRaw(context, request, cq));
    }
    ::grpc::Status GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::aimbot::GetDimensionsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>> AsyncGetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>>(AsyncGetDimensionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>> PrepareAsyncGetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>>(PrepareAsyncGetDimensionsRaw(context, request, cq));
    }
    ::grpc::Status SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>> AsyncSetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>>(AsyncSetBandingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>> PrepareAsyncSetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>>(PrepareAsyncSetBandingEnabledRaw(context, request, cq));
    }
    ::grpc::Status IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>> AsyncIsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>>(AsyncIsBandingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>> PrepareAsyncIsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>>(PrepareAsyncIsBandingEnabledRaw(context, request, cq));
    }
    ::grpc::Status SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>> AsyncSetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>>(AsyncSetDynamicBandingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>> PrepareAsyncSetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>>(PrepareAsyncSetDynamicBandingEnabledRaw(context, request, cq));
    }
    ::grpc::Status IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>> AsyncIsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>>(AsyncIsDynamicBandingEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>> PrepareAsyncIsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>>(PrepareAsyncIsDynamicBandingEnabledRaw(context, request, cq));
    }
    ::grpc::Status GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetVisualizationMetadataResponse>> AsyncGetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetVisualizationMetadataResponse>>(AsyncGetVisualizationMetadataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetVisualizationMetadataResponse>> PrepareAsyncGetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetVisualizationMetadataResponse>>(PrepareAsyncGetVisualizationMetadataRaw(context, request, cq));
    }
    ::grpc::Status GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::banding::GetNextBandingStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextBandingStateResponse>> AsyncGetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextBandingStateResponse>>(AsyncGetNextBandingStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextBandingStateResponse>> PrepareAsyncGetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextBandingStateResponse>>(PrepareAsyncGetNextBandingStateRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response, std::function<void(::grpc::Status)>) override;
      void LoadBandingDefs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SaveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DeleteBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response, std::function<void(::grpc::Status)>) override;
      void GetActiveBandingDef(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextVisualizationData(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response, std::function<void(::grpc::Status)>) override;
      void GetNextVisualizationData2(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextVisualizationDataForAllRows(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetDimensions(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, std::function<void(::grpc::Status)>) override;
      void SetBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, std::function<void(::grpc::Status)>) override;
      void IsBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, std::function<void(::grpc::Status)>) override;
      void SetDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, std::function<void(::grpc::Status)>) override;
      void IsDynamicBandingEnabled(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetVisualizationMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextBandingState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::LoadBandingDefsResponse>* AsyncLoadBandingDefsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::LoadBandingDefsResponse>* PrepareAsyncLoadBandingDefsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSaveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSaveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDeleteBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDeleteBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetActiveBandingDefResponse>* AsyncGetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetActiveBandingDefResponse>* PrepareAsyncGetActiveBandingDefRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataResponse>* AsyncGetNextVisualizationDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataResponse>* PrepareAsyncGetNextVisualizationDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationData2Response>* AsyncGetNextVisualizationData2Raw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationData2Response>* PrepareAsyncGetNextVisualizationData2Raw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* AsyncGetNextVisualizationDataForAllRowsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* PrepareAsyncGetNextVisualizationDataForAllRowsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>* AsyncGetDimensionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::aimbot::GetDimensionsResponse>* PrepareAsyncGetDimensionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::GetDimensionsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>* AsyncSetBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>* PrepareAsyncSetBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>* AsyncIsBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>* PrepareAsyncIsBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>* AsyncSetDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::SetBandingEnabledResponse>* PrepareAsyncSetDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>* AsyncIsDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::IsBandingEnabledResponse>* PrepareAsyncIsDynamicBandingEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetVisualizationMetadataResponse>* AsyncGetVisualizationMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetVisualizationMetadataResponse>* PrepareAsyncGetVisualizationMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextBandingStateResponse>* AsyncGetNextBandingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::banding::GetNextBandingStateResponse>* PrepareAsyncGetNextBandingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_LoadBandingDefs_;
    const ::grpc::internal::RpcMethod rpcmethod_SaveBandingDef_;
    const ::grpc::internal::RpcMethod rpcmethod_DeleteBandingDef_;
    const ::grpc::internal::RpcMethod rpcmethod_SetActiveBandingDef_;
    const ::grpc::internal::RpcMethod rpcmethod_GetActiveBandingDef_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextVisualizationData_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextVisualizationData2_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextVisualizationDataForAllRows_;
    const ::grpc::internal::RpcMethod rpcmethod_GetDimensions_;
    const ::grpc::internal::RpcMethod rpcmethod_SetBandingEnabled_;
    const ::grpc::internal::RpcMethod rpcmethod_IsBandingEnabled_;
    const ::grpc::internal::RpcMethod rpcmethod_SetDynamicBandingEnabled_;
    const ::grpc::internal::RpcMethod rpcmethod_IsDynamicBandingEnabled_;
    const ::grpc::internal::RpcMethod rpcmethod_GetVisualizationMetadata_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextBandingState_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status LoadBandingDefs(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response);
    virtual ::grpc::Status SaveBandingDef(::grpc::ServerContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status DeleteBandingDef(::grpc::ServerContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status SetActiveBandingDef(::grpc::ServerContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetActiveBandingDef(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response);
    virtual ::grpc::Status GetNextVisualizationData(::grpc::ServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response);
    virtual ::grpc::Status GetNextVisualizationData2(::grpc::ServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response);
    virtual ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response);
    virtual ::grpc::Status GetDimensions(::grpc::ServerContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response);
    virtual ::grpc::Status SetBandingEnabled(::grpc::ServerContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response);
    virtual ::grpc::Status IsBandingEnabled(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response);
    virtual ::grpc::Status SetDynamicBandingEnabled(::grpc::ServerContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response);
    virtual ::grpc::Status IsDynamicBandingEnabled(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response);
    virtual ::grpc::Status GetVisualizationMetadata(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response);
    virtual ::grpc::Status GetNextBandingState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_LoadBandingDefs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_LoadBandingDefs() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_LoadBandingDefs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadBandingDefs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::LoadBandingDefsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLoadBandingDefs(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::LoadBandingDefsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SaveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SaveBandingDef() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SaveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SaveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveBandingDef(::grpc::ServerContext* context, ::carbon::frontend::banding::SaveBandingDefRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DeleteBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DeleteBandingDef() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_DeleteBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::DeleteBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteBandingDef(::grpc::ServerContext* context, ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetActiveBandingDef() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetActiveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveBandingDef(::grpc::ServerContext* context, ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetActiveBandingDef() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetActiveBandingDefResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetActiveBandingDef(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::GetActiveBandingDefResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextVisualizationData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextVisualizationData() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_GetNextVisualizationData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextVisualizationData(::grpc::ServerContext* context, ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::GetNextVisualizationDataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextVisualizationData2 : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextVisualizationData2() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetNextVisualizationData2() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData2(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationData2Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextVisualizationData2(::grpc::ServerContext* context, ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::GetNextVisualizationData2Response>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextVisualizationDataForAllRows : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextVisualizationDataForAllRows() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_GetNextVisualizationDataForAllRows() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextVisualizationDataForAllRows(::grpc::ServerContext* context, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetDimensions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetDimensions() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_GetDimensions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDimensions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetDimensionsRequest* /*request*/, ::aimbot::GetDimensionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDimensions(::grpc::ServerContext* context, ::carbon::frontend::banding::GetDimensionsRequest* request, ::grpc::ServerAsyncResponseWriter< ::aimbot::GetDimensionsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetBandingEnabled() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_SetBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetBandingEnabled(::grpc::ServerContext* context, ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::SetBandingEnabledResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_IsBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_IsBandingEnabled() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_IsBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestIsBandingEnabled(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::IsBandingEnabledResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_SetDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetDynamicBandingEnabled(::grpc::ServerContext* context, ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::SetBandingEnabledResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_IsDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_IsDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodAsync(12);
    }
    ~WithAsyncMethod_IsDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestIsDynamicBandingEnabled(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::IsBandingEnabledResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetVisualizationMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetVisualizationMetadata() {
      ::grpc::Service::MarkMethodAsync(13);
    }
    ~WithAsyncMethod_GetVisualizationMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVisualizationMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetVisualizationMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetVisualizationMetadata(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::GetVisualizationMetadataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextBandingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextBandingState() {
      ::grpc::Service::MarkMethodAsync(14);
    }
    ~WithAsyncMethod_GetNextBandingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextBandingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::banding::GetNextBandingStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextBandingState(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::banding::GetNextBandingStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_LoadBandingDefs<WithAsyncMethod_SaveBandingDef<WithAsyncMethod_DeleteBandingDef<WithAsyncMethod_SetActiveBandingDef<WithAsyncMethod_GetActiveBandingDef<WithAsyncMethod_GetNextVisualizationData<WithAsyncMethod_GetNextVisualizationData2<WithAsyncMethod_GetNextVisualizationDataForAllRows<WithAsyncMethod_GetDimensions<WithAsyncMethod_SetBandingEnabled<WithAsyncMethod_IsBandingEnabled<WithAsyncMethod_SetDynamicBandingEnabled<WithAsyncMethod_IsDynamicBandingEnabled<WithAsyncMethod_GetVisualizationMetadata<WithAsyncMethod_GetNextBandingState<Service > > > > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_LoadBandingDefs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_LoadBandingDefs() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::LoadBandingDefsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::LoadBandingDefsResponse* response) { return this->LoadBandingDefs(context, request, response); }));}
    void SetMessageAllocatorFor_LoadBandingDefs(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::LoadBandingDefsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::LoadBandingDefsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_LoadBandingDefs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadBandingDefs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::LoadBandingDefsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LoadBandingDefs(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::LoadBandingDefsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SaveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SaveBandingDef() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::SaveBandingDefRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::SaveBandingDefRequest* request, ::carbon::frontend::util::Empty* response) { return this->SaveBandingDef(context, request, response); }));}
    void SetMessageAllocatorFor_SaveBandingDef(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::SaveBandingDefRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::SaveBandingDefRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SaveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SaveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveBandingDef(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::SaveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DeleteBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DeleteBandingDef() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::DeleteBandingDefRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::DeleteBandingDefRequest* request, ::carbon::frontend::util::Empty* response) { return this->DeleteBandingDef(context, request, response); }));}
    void SetMessageAllocatorFor_DeleteBandingDef(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::DeleteBandingDefRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::DeleteBandingDefRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DeleteBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::DeleteBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteBandingDef(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::DeleteBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetActiveBandingDef() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::SetActiveBandingDefRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::SetActiveBandingDefRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetActiveBandingDef(context, request, response); }));}
    void SetMessageAllocatorFor_SetActiveBandingDef(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::SetActiveBandingDefRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::SetActiveBandingDefRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetActiveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveBandingDef(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::SetActiveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetActiveBandingDef() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetActiveBandingDefResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetActiveBandingDefResponse* response) { return this->GetActiveBandingDef(context, request, response); }));}
    void SetMessageAllocatorFor_GetActiveBandingDef(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetActiveBandingDefResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetActiveBandingDefResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetActiveBandingDefResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetActiveBandingDef(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetActiveBandingDefResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextVisualizationData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextVisualizationData() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationDataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataResponse* response) { return this->GetNextVisualizationData(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextVisualizationData(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationDataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationDataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextVisualizationData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextVisualizationData(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextVisualizationData2 : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextVisualizationData2() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationData2Response>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* request, ::carbon::frontend::banding::GetNextVisualizationData2Response* response) { return this->GetNextVisualizationData2(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextVisualizationData2(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationData2Response>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationData2Response>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextVisualizationData2() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData2(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationData2Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextVisualizationData2(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationData2Response* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextVisualizationDataForAllRows : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextVisualizationDataForAllRows() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* request, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* response) { return this->GetNextVisualizationDataForAllRows(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextVisualizationDataForAllRows(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextVisualizationDataForAllRows() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextVisualizationDataForAllRows(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetDimensions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetDimensions() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::GetDimensionsRequest, ::aimbot::GetDimensionsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::GetDimensionsRequest* request, ::aimbot::GetDimensionsResponse* response) { return this->GetDimensions(context, request, response); }));}
    void SetMessageAllocatorFor_GetDimensions(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::GetDimensionsRequest, ::aimbot::GetDimensionsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::GetDimensionsRequest, ::aimbot::GetDimensionsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetDimensions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDimensions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetDimensionsRequest* /*request*/, ::aimbot::GetDimensionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDimensions(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::GetDimensionsRequest* /*request*/, ::aimbot::GetDimensionsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetBandingEnabled() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) { return this->SetBandingEnabled(context, request, response); }));}
    void SetMessageAllocatorFor_SetBandingEnabled(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetBandingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_IsBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_IsBandingEnabled() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) { return this->IsBandingEnabled(context, request, response); }));}
    void SetMessageAllocatorFor_IsBandingEnabled(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_IsBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* IsBandingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::banding::SetBandingEnabledRequest* request, ::carbon::frontend::banding::SetBandingEnabledResponse* response) { return this->SetDynamicBandingEnabled(context, request, response); }));}
    void SetMessageAllocatorFor_SetDynamicBandingEnabled(
        ::grpc::MessageAllocator< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetDynamicBandingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_IsDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_IsDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::IsBandingEnabledResponse* response) { return this->IsDynamicBandingEnabled(context, request, response); }));}
    void SetMessageAllocatorFor_IsDynamicBandingEnabled(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(12);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_IsDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* IsDynamicBandingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetVisualizationMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetVisualizationMetadata() {
      ::grpc::Service::MarkMethodCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetVisualizationMetadataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::banding::GetVisualizationMetadataResponse* response) { return this->GetVisualizationMetadata(context, request, response); }));}
    void SetMessageAllocatorFor_GetVisualizationMetadata(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetVisualizationMetadataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(13);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetVisualizationMetadataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetVisualizationMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVisualizationMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetVisualizationMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetVisualizationMetadata(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetVisualizationMetadataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextBandingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextBandingState() {
      ::grpc::Service::MarkMethodCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::banding::GetNextBandingStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::banding::GetNextBandingStateResponse* response) { return this->GetNextBandingState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextBandingState(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::banding::GetNextBandingStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(14);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::banding::GetNextBandingStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextBandingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextBandingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::banding::GetNextBandingStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextBandingState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::banding::GetNextBandingStateResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_LoadBandingDefs<WithCallbackMethod_SaveBandingDef<WithCallbackMethod_DeleteBandingDef<WithCallbackMethod_SetActiveBandingDef<WithCallbackMethod_GetActiveBandingDef<WithCallbackMethod_GetNextVisualizationData<WithCallbackMethod_GetNextVisualizationData2<WithCallbackMethod_GetNextVisualizationDataForAllRows<WithCallbackMethod_GetDimensions<WithCallbackMethod_SetBandingEnabled<WithCallbackMethod_IsBandingEnabled<WithCallbackMethod_SetDynamicBandingEnabled<WithCallbackMethod_IsDynamicBandingEnabled<WithCallbackMethod_GetVisualizationMetadata<WithCallbackMethod_GetNextBandingState<Service > > > > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_LoadBandingDefs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_LoadBandingDefs() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_LoadBandingDefs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadBandingDefs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::LoadBandingDefsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SaveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SaveBandingDef() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SaveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SaveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DeleteBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DeleteBandingDef() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_DeleteBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::DeleteBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetActiveBandingDef() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetActiveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetActiveBandingDef() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetActiveBandingDefResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextVisualizationData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextVisualizationData() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_GetNextVisualizationData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextVisualizationData2 : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextVisualizationData2() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetNextVisualizationData2() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData2(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationData2Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextVisualizationDataForAllRows : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextVisualizationDataForAllRows() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_GetNextVisualizationDataForAllRows() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetDimensions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetDimensions() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_GetDimensions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDimensions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetDimensionsRequest* /*request*/, ::aimbot::GetDimensionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetBandingEnabled() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_SetBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_IsBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_IsBandingEnabled() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_IsBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_SetDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_IsDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_IsDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodGeneric(12);
    }
    ~WithGenericMethod_IsDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetVisualizationMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetVisualizationMetadata() {
      ::grpc::Service::MarkMethodGeneric(13);
    }
    ~WithGenericMethod_GetVisualizationMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVisualizationMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetVisualizationMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextBandingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextBandingState() {
      ::grpc::Service::MarkMethodGeneric(14);
    }
    ~WithGenericMethod_GetNextBandingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextBandingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::banding::GetNextBandingStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_LoadBandingDefs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_LoadBandingDefs() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_LoadBandingDefs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadBandingDefs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::LoadBandingDefsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestLoadBandingDefs(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SaveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SaveBandingDef() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SaveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SaveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSaveBandingDef(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DeleteBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DeleteBandingDef() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_DeleteBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::DeleteBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDeleteBandingDef(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetActiveBandingDef() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetActiveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveBandingDef(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetActiveBandingDef() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetActiveBandingDefResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetActiveBandingDef(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextVisualizationData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextVisualizationData() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_GetNextVisualizationData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextVisualizationData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextVisualizationData2 : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextVisualizationData2() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetNextVisualizationData2() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData2(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationData2Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextVisualizationData2(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextVisualizationDataForAllRows : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextVisualizationDataForAllRows() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_GetNextVisualizationDataForAllRows() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextVisualizationDataForAllRows(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetDimensions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetDimensions() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_GetDimensions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDimensions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetDimensionsRequest* /*request*/, ::aimbot::GetDimensionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetDimensions(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetBandingEnabled() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_SetBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetBandingEnabled(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_IsBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_IsBandingEnabled() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_IsBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestIsBandingEnabled(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_SetDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetDynamicBandingEnabled(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_IsDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_IsDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodRaw(12);
    }
    ~WithRawMethod_IsDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestIsDynamicBandingEnabled(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetVisualizationMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetVisualizationMetadata() {
      ::grpc::Service::MarkMethodRaw(13);
    }
    ~WithRawMethod_GetVisualizationMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVisualizationMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetVisualizationMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetVisualizationMetadata(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextBandingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextBandingState() {
      ::grpc::Service::MarkMethodRaw(14);
    }
    ~WithRawMethod_GetNextBandingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextBandingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::banding::GetNextBandingStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextBandingState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_LoadBandingDefs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_LoadBandingDefs() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->LoadBandingDefs(context, request, response); }));
    }
    ~WithRawCallbackMethod_LoadBandingDefs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status LoadBandingDefs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::LoadBandingDefsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* LoadBandingDefs(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SaveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SaveBandingDef() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SaveBandingDef(context, request, response); }));
    }
    ~WithRawCallbackMethod_SaveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SaveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SaveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SaveBandingDef(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DeleteBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DeleteBandingDef() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DeleteBandingDef(context, request, response); }));
    }
    ~WithRawCallbackMethod_DeleteBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DeleteBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::DeleteBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DeleteBandingDef(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetActiveBandingDef() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetActiveBandingDef(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetActiveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveBandingDef(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetActiveBandingDef() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetActiveBandingDef(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetActiveBandingDefResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetActiveBandingDef(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextVisualizationData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextVisualizationData() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextVisualizationData(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextVisualizationData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextVisualizationData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextVisualizationData2 : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextVisualizationData2() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextVisualizationData2(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextVisualizationData2() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationData2(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationData2Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextVisualizationData2(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextVisualizationDataForAllRows : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextVisualizationDataForAllRows() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextVisualizationDataForAllRows(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextVisualizationDataForAllRows() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextVisualizationDataForAllRows(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetDimensions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetDimensions() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetDimensions(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetDimensions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetDimensions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetDimensionsRequest* /*request*/, ::aimbot::GetDimensionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetDimensions(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetBandingEnabled() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetBandingEnabled(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetBandingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_IsBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_IsBandingEnabled() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->IsBandingEnabled(context, request, response); }));
    }
    ~WithRawCallbackMethod_IsBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* IsBandingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetDynamicBandingEnabled(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetDynamicBandingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_IsDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_IsDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodRawCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->IsDynamicBandingEnabled(context, request, response); }));
    }
    ~WithRawCallbackMethod_IsDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status IsDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* IsDynamicBandingEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetVisualizationMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetVisualizationMetadata() {
      ::grpc::Service::MarkMethodRawCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetVisualizationMetadata(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetVisualizationMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetVisualizationMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetVisualizationMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetVisualizationMetadata(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextBandingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextBandingState() {
      ::grpc::Service::MarkMethodRawCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextBandingState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextBandingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextBandingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::banding::GetNextBandingStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextBandingState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_LoadBandingDefs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_LoadBandingDefs() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::banding::LoadBandingDefsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::banding::LoadBandingDefsResponse>* streamer) {
                       return this->StreamedLoadBandingDefs(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_LoadBandingDefs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status LoadBandingDefs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::LoadBandingDefsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedLoadBandingDefs(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::banding::LoadBandingDefsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SaveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SaveBandingDef() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::SaveBandingDefRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::SaveBandingDefRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSaveBandingDef(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SaveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SaveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SaveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSaveBandingDef(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::SaveBandingDefRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DeleteBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DeleteBandingDef() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::DeleteBandingDefRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::DeleteBandingDefRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDeleteBandingDef(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DeleteBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DeleteBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::DeleteBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDeleteBandingDef(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::DeleteBandingDefRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetActiveBandingDef() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::SetActiveBandingDefRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::SetActiveBandingDefRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetActiveBandingDef(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetActiveBandingDefRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetActiveBandingDef(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::SetActiveBandingDefRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetActiveBandingDef : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetActiveBandingDef() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetActiveBandingDefResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetActiveBandingDefResponse>* streamer) {
                       return this->StreamedGetActiveBandingDef(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetActiveBandingDef() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetActiveBandingDef(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetActiveBandingDefResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetActiveBandingDef(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::banding::GetActiveBandingDefResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextVisualizationData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextVisualizationData() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationDataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationDataResponse>* streamer) {
                       return this->StreamedGetNextVisualizationData(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextVisualizationData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextVisualizationData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextVisualizationData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::GetNextVisualizationDataRequest,::carbon::frontend::banding::GetNextVisualizationDataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextVisualizationData2 : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextVisualizationData2() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationData2Response>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::GetNextVisualizationDataRequest, ::carbon::frontend::banding::GetNextVisualizationData2Response>* streamer) {
                       return this->StreamedGetNextVisualizationData2(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextVisualizationData2() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextVisualizationData2(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationData2Response* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextVisualizationData2(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::GetNextVisualizationDataRequest,::carbon::frontend::banding::GetNextVisualizationData2Response>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextVisualizationDataForAllRows : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextVisualizationDataForAllRows() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* streamer) {
                       return this->StreamedGetNextVisualizationDataForAllRows(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextVisualizationDataForAllRows() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextVisualizationDataForAllRows(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest* /*request*/, ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextVisualizationDataForAllRows(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::GetNextVisualizationDataForAllRowsRequest,::carbon::frontend::banding::GetNextVisualizationDataForAllRowsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetDimensions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetDimensions() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::GetDimensionsRequest, ::aimbot::GetDimensionsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::GetDimensionsRequest, ::aimbot::GetDimensionsResponse>* streamer) {
                       return this->StreamedGetDimensions(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetDimensions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetDimensions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::GetDimensionsRequest* /*request*/, ::aimbot::GetDimensionsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetDimensions(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::GetDimensionsRequest,::aimbot::GetDimensionsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetBandingEnabled() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>* streamer) {
                       return this->StreamedSetBandingEnabled(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetBandingEnabled(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::SetBandingEnabledRequest,::carbon::frontend::banding::SetBandingEnabledResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_IsBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_IsBandingEnabled() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>* streamer) {
                       return this->StreamedIsBandingEnabled(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_IsBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status IsBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedIsBandingEnabled(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::banding::IsBandingEnabledResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::banding::SetBandingEnabledRequest, ::carbon::frontend::banding::SetBandingEnabledResponse>* streamer) {
                       return this->StreamedSetDynamicBandingEnabled(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::banding::SetBandingEnabledRequest* /*request*/, ::carbon::frontend::banding::SetBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetDynamicBandingEnabled(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::banding::SetBandingEnabledRequest,::carbon::frontend::banding::SetBandingEnabledResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_IsDynamicBandingEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_IsDynamicBandingEnabled() {
      ::grpc::Service::MarkMethodStreamed(12,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::banding::IsBandingEnabledResponse>* streamer) {
                       return this->StreamedIsDynamicBandingEnabled(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_IsDynamicBandingEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status IsDynamicBandingEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::IsBandingEnabledResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedIsDynamicBandingEnabled(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::banding::IsBandingEnabledResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetVisualizationMetadata : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetVisualizationMetadata() {
      ::grpc::Service::MarkMethodStreamed(13,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetVisualizationMetadataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::banding::GetVisualizationMetadataResponse>* streamer) {
                       return this->StreamedGetVisualizationMetadata(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetVisualizationMetadata() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetVisualizationMetadata(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::banding::GetVisualizationMetadataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetVisualizationMetadata(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::banding::GetVisualizationMetadataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextBandingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextBandingState() {
      ::grpc::Service::MarkMethodStreamed(14,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::banding::GetNextBandingStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::banding::GetNextBandingStateResponse>* streamer) {
                       return this->StreamedGetNextBandingState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextBandingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextBandingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::banding::GetNextBandingStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextBandingState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::banding::GetNextBandingStateResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_LoadBandingDefs<WithStreamedUnaryMethod_SaveBandingDef<WithStreamedUnaryMethod_DeleteBandingDef<WithStreamedUnaryMethod_SetActiveBandingDef<WithStreamedUnaryMethod_GetActiveBandingDef<WithStreamedUnaryMethod_GetNextVisualizationData<WithStreamedUnaryMethod_GetNextVisualizationData2<WithStreamedUnaryMethod_GetNextVisualizationDataForAllRows<WithStreamedUnaryMethod_GetDimensions<WithStreamedUnaryMethod_SetBandingEnabled<WithStreamedUnaryMethod_IsBandingEnabled<WithStreamedUnaryMethod_SetDynamicBandingEnabled<WithStreamedUnaryMethod_IsDynamicBandingEnabled<WithStreamedUnaryMethod_GetVisualizationMetadata<WithStreamedUnaryMethod_GetNextBandingState<Service > > > > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_LoadBandingDefs<WithStreamedUnaryMethod_SaveBandingDef<WithStreamedUnaryMethod_DeleteBandingDef<WithStreamedUnaryMethod_SetActiveBandingDef<WithStreamedUnaryMethod_GetActiveBandingDef<WithStreamedUnaryMethod_GetNextVisualizationData<WithStreamedUnaryMethod_GetNextVisualizationData2<WithStreamedUnaryMethod_GetNextVisualizationDataForAllRows<WithStreamedUnaryMethod_GetDimensions<WithStreamedUnaryMethod_SetBandingEnabled<WithStreamedUnaryMethod_IsBandingEnabled<WithStreamedUnaryMethod_SetDynamicBandingEnabled<WithStreamedUnaryMethod_IsDynamicBandingEnabled<WithStreamedUnaryMethod_GetVisualizationMetadata<WithStreamedUnaryMethod_GetNextBandingState<Service > > > > > > > > > > > > > > > StreamedService;
};

}  // namespace banding
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fbanding_2eproto__INCLUDED

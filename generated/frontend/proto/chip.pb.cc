// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/chip.proto

#include "frontend/proto/chip.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace chip {
constexpr ChipData::ChipData(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , url_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , geohash_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , checksum_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , downloaded_ts_(nullptr)
  , last_used_ts_(nullptr)
  , content_length_(0u){}
struct ChipDataDefaultTypeInternal {
  constexpr ChipDataDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ChipDataDefaultTypeInternal() {}
  union {
    ChipData _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ChipDataDefaultTypeInternal _ChipData_default_instance_;
constexpr GetChipMetadataResponse::GetChipMetadataResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : chips_(){}
struct GetChipMetadataResponseDefaultTypeInternal {
  constexpr GetChipMetadataResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetChipMetadataResponseDefaultTypeInternal() {}
  union {
    GetChipMetadataResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetChipMetadataResponseDefaultTypeInternal _GetChipMetadataResponse_default_instance_;
constexpr ChipIdsResponse::ChipIdsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : chip_ids_(){}
struct ChipIdsResponseDefaultTypeInternal {
  constexpr ChipIdsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ChipIdsResponseDefaultTypeInternal() {}
  union {
    ChipIdsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ChipIdsResponseDefaultTypeInternal _ChipIdsResponse_default_instance_;
}  // namespace chip
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fchip_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fchip_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fchip_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fchip_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipData, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipData, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipData, url_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipData, geohash_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipData, checksum_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipData, content_length_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipData, downloaded_ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipData, last_used_ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::GetChipMetadataResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::GetChipMetadataResponse, chips_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipIdsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::chip::ChipIdsResponse, chip_ids_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::chip::ChipData)},
  { 13, -1, -1, sizeof(::carbon::frontend::chip::GetChipMetadataResponse)},
  { 20, -1, -1, sizeof(::carbon::frontend::chip::ChipIdsResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::chip::_ChipData_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::chip::_GetChipMetadataResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::chip::_ChipIdsResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fchip_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\031frontend/proto/chip.proto\022\024carbon.fron"
  "tend.chip\032\031frontend/proto/util.proto\"\315\001\n"
  "\010ChipData\022\n\n\002id\030\001 \001(\t\022\013\n\003url\030\002 \001(\t\022\017\n\007ge"
  "ohash\030\003 \001(\t\022\020\n\010checksum\030\004 \001(\t\022\026\n\016content"
  "_length\030\005 \001(\r\0226\n\rdownloaded_ts\030\006 \001(\0132\037.c"
  "arbon.frontend.util.Timestamp\0225\n\014last_us"
  "ed_ts\030\007 \001(\0132\037.carbon.frontend.util.Times"
  "tamp\"H\n\027GetChipMetadataResponse\022-\n\005chips"
  "\030\001 \003(\0132\036.carbon.frontend.chip.ChipData\"#"
  "\n\017ChipIdsResponse\022\020\n\010chip_ids\030\001 \003(\t2\240\002\n\013"
  "ChipService\022]\n\017GetChipMetadata\022\033.carbon."
  "frontend.util.Empty\032-.carbon.frontend.ch"
  "ip.GetChipMetadataResponse\022Z\n\024GetDownloa"
  "dedChipIds\022\033.carbon.frontend.util.Empty\032"
  "%.carbon.frontend.chip.ChipIdsResponse\022V"
  "\n\020GetSyncedChipIds\022\033.carbon.frontend.uti"
  "l.Empty\032%.carbon.frontend.chip.ChipIdsRe"
  "sponseB\020Z\016proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fchip_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fchip_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fchip_2eproto = {
  false, false, 712, descriptor_table_protodef_frontend_2fproto_2fchip_2eproto, "frontend/proto/chip.proto", 
  &descriptor_table_frontend_2fproto_2fchip_2eproto_once, descriptor_table_frontend_2fproto_2fchip_2eproto_deps, 1, 3,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fchip_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fchip_2eproto, file_level_enum_descriptors_frontend_2fproto_2fchip_2eproto, file_level_service_descriptors_frontend_2fproto_2fchip_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fchip_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fchip_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fchip_2eproto(&descriptor_table_frontend_2fproto_2fchip_2eproto);
namespace carbon {
namespace frontend {
namespace chip {

// ===================================================================

class ChipData::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& downloaded_ts(const ChipData* msg);
  static const ::carbon::frontend::util::Timestamp& last_used_ts(const ChipData* msg);
};

const ::carbon::frontend::util::Timestamp&
ChipData::_Internal::downloaded_ts(const ChipData* msg) {
  return *msg->downloaded_ts_;
}
const ::carbon::frontend::util::Timestamp&
ChipData::_Internal::last_used_ts(const ChipData* msg) {
  return *msg->last_used_ts_;
}
void ChipData::clear_downloaded_ts() {
  if (GetArenaForAllocation() == nullptr && downloaded_ts_ != nullptr) {
    delete downloaded_ts_;
  }
  downloaded_ts_ = nullptr;
}
void ChipData::clear_last_used_ts() {
  if (GetArenaForAllocation() == nullptr && last_used_ts_ != nullptr) {
    delete last_used_ts_;
  }
  last_used_ts_ = nullptr;
}
ChipData::ChipData(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.chip.ChipData)
}
ChipData::ChipData(const ChipData& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  url_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_url().empty()) {
    url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_url(), 
      GetArenaForAllocation());
  }
  geohash_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    geohash_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_geohash().empty()) {
    geohash_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_geohash(), 
      GetArenaForAllocation());
  }
  checksum_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    checksum_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_checksum().empty()) {
    checksum_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_checksum(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_downloaded_ts()) {
    downloaded_ts_ = new ::carbon::frontend::util::Timestamp(*from.downloaded_ts_);
  } else {
    downloaded_ts_ = nullptr;
  }
  if (from._internal_has_last_used_ts()) {
    last_used_ts_ = new ::carbon::frontend::util::Timestamp(*from.last_used_ts_);
  } else {
    last_used_ts_ = nullptr;
  }
  content_length_ = from.content_length_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.chip.ChipData)
}

inline void ChipData::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
url_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
geohash_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  geohash_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
checksum_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  checksum_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&downloaded_ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&content_length_) -
    reinterpret_cast<char*>(&downloaded_ts_)) + sizeof(content_length_));
}

ChipData::~ChipData() {
  // @@protoc_insertion_point(destructor:carbon.frontend.chip.ChipData)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ChipData::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  url_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  geohash_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  checksum_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete downloaded_ts_;
  if (this != internal_default_instance()) delete last_used_ts_;
}

void ChipData::ArenaDtor(void* object) {
  ChipData* _this = reinterpret_cast< ChipData* >(object);
  (void)_this;
}
void ChipData::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ChipData::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ChipData::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.chip.ChipData)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  url_.ClearToEmpty();
  geohash_.ClearToEmpty();
  checksum_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && downloaded_ts_ != nullptr) {
    delete downloaded_ts_;
  }
  downloaded_ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && last_used_ts_ != nullptr) {
    delete last_used_ts_;
  }
  last_used_ts_ = nullptr;
  content_length_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ChipData::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.chip.ChipData.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string url = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_url();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.chip.ChipData.url"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string geohash = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_geohash();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.chip.ChipData.geohash"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string checksum = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_checksum();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.chip.ChipData.checksum"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 content_length = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          content_length_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp downloaded_ts = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_downloaded_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp last_used_ts = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ctx->ParseMessage(_internal_mutable_last_used_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ChipData::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.chip.ChipData)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.chip.ChipData.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string url = 2;
  if (!this->_internal_url().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_url().data(), static_cast<int>(this->_internal_url().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.chip.ChipData.url");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_url(), target);
  }

  // string geohash = 3;
  if (!this->_internal_geohash().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_geohash().data(), static_cast<int>(this->_internal_geohash().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.chip.ChipData.geohash");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_geohash(), target);
  }

  // string checksum = 4;
  if (!this->_internal_checksum().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_checksum().data(), static_cast<int>(this->_internal_checksum().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.chip.ChipData.checksum");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_checksum(), target);
  }

  // uint32 content_length = 5;
  if (this->_internal_content_length() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_content_length(), target);
  }

  // .carbon.frontend.util.Timestamp downloaded_ts = 6;
  if (this->_internal_has_downloaded_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::downloaded_ts(this), target, stream);
  }

  // .carbon.frontend.util.Timestamp last_used_ts = 7;
  if (this->_internal_has_last_used_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        7, _Internal::last_used_ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.chip.ChipData)
  return target;
}

size_t ChipData::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.chip.ChipData)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string url = 2;
  if (!this->_internal_url().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_url());
  }

  // string geohash = 3;
  if (!this->_internal_geohash().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_geohash());
  }

  // string checksum = 4;
  if (!this->_internal_checksum().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_checksum());
  }

  // .carbon.frontend.util.Timestamp downloaded_ts = 6;
  if (this->_internal_has_downloaded_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *downloaded_ts_);
  }

  // .carbon.frontend.util.Timestamp last_used_ts = 7;
  if (this->_internal_has_last_used_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *last_used_ts_);
  }

  // uint32 content_length = 5;
  if (this->_internal_content_length() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_content_length());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ChipData::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ChipData::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ChipData::GetClassData() const { return &_class_data_; }

void ChipData::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ChipData *>(to)->MergeFrom(
      static_cast<const ChipData &>(from));
}


void ChipData::MergeFrom(const ChipData& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.chip.ChipData)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_url().empty()) {
    _internal_set_url(from._internal_url());
  }
  if (!from._internal_geohash().empty()) {
    _internal_set_geohash(from._internal_geohash());
  }
  if (!from._internal_checksum().empty()) {
    _internal_set_checksum(from._internal_checksum());
  }
  if (from._internal_has_downloaded_ts()) {
    _internal_mutable_downloaded_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_downloaded_ts());
  }
  if (from._internal_has_last_used_ts()) {
    _internal_mutable_last_used_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_last_used_ts());
  }
  if (from._internal_content_length() != 0) {
    _internal_set_content_length(from._internal_content_length());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ChipData::CopyFrom(const ChipData& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.chip.ChipData)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ChipData::IsInitialized() const {
  return true;
}

void ChipData::InternalSwap(ChipData* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &url_, lhs_arena,
      &other->url_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &geohash_, lhs_arena,
      &other->geohash_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &checksum_, lhs_arena,
      &other->checksum_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ChipData, content_length_)
      + sizeof(ChipData::content_length_)
      - PROTOBUF_FIELD_OFFSET(ChipData, downloaded_ts_)>(
          reinterpret_cast<char*>(&downloaded_ts_),
          reinterpret_cast<char*>(&other->downloaded_ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ChipData::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fchip_2eproto_getter, &descriptor_table_frontend_2fproto_2fchip_2eproto_once,
      file_level_metadata_frontend_2fproto_2fchip_2eproto[0]);
}

// ===================================================================

class GetChipMetadataResponse::_Internal {
 public:
};

GetChipMetadataResponse::GetChipMetadataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  chips_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.chip.GetChipMetadataResponse)
}
GetChipMetadataResponse::GetChipMetadataResponse(const GetChipMetadataResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      chips_(from.chips_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.chip.GetChipMetadataResponse)
}

inline void GetChipMetadataResponse::SharedCtor() {
}

GetChipMetadataResponse::~GetChipMetadataResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.chip.GetChipMetadataResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetChipMetadataResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetChipMetadataResponse::ArenaDtor(void* object) {
  GetChipMetadataResponse* _this = reinterpret_cast< GetChipMetadataResponse* >(object);
  (void)_this;
}
void GetChipMetadataResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetChipMetadataResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetChipMetadataResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.chip.GetChipMetadataResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  chips_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetChipMetadataResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.chip.ChipData chips = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_chips(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetChipMetadataResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.chip.GetChipMetadataResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.chip.ChipData chips = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_chips_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_chips(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.chip.GetChipMetadataResponse)
  return target;
}

size_t GetChipMetadataResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.chip.GetChipMetadataResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.chip.ChipData chips = 1;
  total_size += 1UL * this->_internal_chips_size();
  for (const auto& msg : this->chips_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetChipMetadataResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetChipMetadataResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetChipMetadataResponse::GetClassData() const { return &_class_data_; }

void GetChipMetadataResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetChipMetadataResponse *>(to)->MergeFrom(
      static_cast<const GetChipMetadataResponse &>(from));
}


void GetChipMetadataResponse::MergeFrom(const GetChipMetadataResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.chip.GetChipMetadataResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  chips_.MergeFrom(from.chips_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetChipMetadataResponse::CopyFrom(const GetChipMetadataResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.chip.GetChipMetadataResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetChipMetadataResponse::IsInitialized() const {
  return true;
}

void GetChipMetadataResponse::InternalSwap(GetChipMetadataResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  chips_.InternalSwap(&other->chips_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetChipMetadataResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fchip_2eproto_getter, &descriptor_table_frontend_2fproto_2fchip_2eproto_once,
      file_level_metadata_frontend_2fproto_2fchip_2eproto[1]);
}

// ===================================================================

class ChipIdsResponse::_Internal {
 public:
};

ChipIdsResponse::ChipIdsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  chip_ids_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.chip.ChipIdsResponse)
}
ChipIdsResponse::ChipIdsResponse(const ChipIdsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      chip_ids_(from.chip_ids_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.chip.ChipIdsResponse)
}

inline void ChipIdsResponse::SharedCtor() {
}

ChipIdsResponse::~ChipIdsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.chip.ChipIdsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ChipIdsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ChipIdsResponse::ArenaDtor(void* object) {
  ChipIdsResponse* _this = reinterpret_cast< ChipIdsResponse* >(object);
  (void)_this;
}
void ChipIdsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ChipIdsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ChipIdsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.chip.ChipIdsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  chip_ids_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ChipIdsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string chip_ids = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_chip_ids();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.chip.ChipIdsResponse.chip_ids"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ChipIdsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.chip.ChipIdsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string chip_ids = 1;
  for (int i = 0, n = this->_internal_chip_ids_size(); i < n; i++) {
    const auto& s = this->_internal_chip_ids(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.chip.ChipIdsResponse.chip_ids");
    target = stream->WriteString(1, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.chip.ChipIdsResponse)
  return target;
}

size_t ChipIdsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.chip.ChipIdsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string chip_ids = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(chip_ids_.size());
  for (int i = 0, n = chip_ids_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      chip_ids_.Get(i));
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ChipIdsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ChipIdsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ChipIdsResponse::GetClassData() const { return &_class_data_; }

void ChipIdsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ChipIdsResponse *>(to)->MergeFrom(
      static_cast<const ChipIdsResponse &>(from));
}


void ChipIdsResponse::MergeFrom(const ChipIdsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.chip.ChipIdsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  chip_ids_.MergeFrom(from.chip_ids_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ChipIdsResponse::CopyFrom(const ChipIdsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.chip.ChipIdsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ChipIdsResponse::IsInitialized() const {
  return true;
}

void ChipIdsResponse::InternalSwap(ChipIdsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  chip_ids_.InternalSwap(&other->chip_ids_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ChipIdsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fchip_2eproto_getter, &descriptor_table_frontend_2fproto_2fchip_2eproto_once,
      file_level_metadata_frontend_2fproto_2fchip_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace chip
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::chip::ChipData* Arena::CreateMaybeMessage< ::carbon::frontend::chip::ChipData >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::chip::ChipData >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::chip::GetChipMetadataResponse* Arena::CreateMaybeMessage< ::carbon::frontend::chip::GetChipMetadataResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::chip::GetChipMetadataResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::chip::ChipIdsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::chip::ChipIdsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::chip::ChipIdsResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

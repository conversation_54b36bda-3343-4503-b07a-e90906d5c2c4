# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import camera_pb2 as frontend_dot_proto_dot_camera__pb2


class CameraServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetCameraList = channel.unary_unary(
                '/carbon.frontend.camera.CameraService/GetCameraList',
                request_serializer=frontend_dot_proto_dot_camera__pb2.CameraListRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_camera__pb2.CameraList.FromString,
                )
        self.GetNextCameraList = channel.unary_unary(
                '/carbon.frontend.camera.CameraService/GetNextCameraList',
                request_serializer=frontend_dot_proto_dot_camera__pb2.NextCameraListRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_camera__pb2.CameraList.FromString,
                )


class CameraServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetCameraList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextCameraList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CameraServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetCameraList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetCameraList,
                    request_deserializer=frontend_dot_proto_dot_camera__pb2.CameraListRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_camera__pb2.CameraList.SerializeToString,
            ),
            'GetNextCameraList': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextCameraList,
                    request_deserializer=frontend_dot_proto_dot_camera__pb2.NextCameraListRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_camera__pb2.CameraList.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.camera.CameraService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CameraService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetCameraList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.camera.CameraService/GetCameraList',
            frontend_dot_proto_dot_camera__pb2.CameraListRequest.SerializeToString,
            frontend_dot_proto_dot_camera__pb2.CameraList.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextCameraList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.camera.CameraService/GetNextCameraList',
            frontend_dot_proto_dot_camera__pb2.NextCameraListRequest.SerializeToString,
            frontend_dot_proto_dot_camera__pb2.CameraList.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

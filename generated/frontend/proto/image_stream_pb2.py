# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/image_stream.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/image_stream.proto',
  package='carbon.frontend.image_stream',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n!frontend/proto/image_stream.proto\x12\x1c\x63\x61rbon.frontend.image_stream\x1a\x19\x66rontend/proto/util.proto\x1a\'weed_tracking/proto/weed_tracking.proto\"\x8b\x01\n\x0b\x41nnotations\x12-\n\ndetections\x18\x01 \x01(\x0b\x32\x19.weed_tracking.Detections\x12#\n\x05\x62\x61nds\x18\x02 \x01(\x0b\x32\x14.weed_tracking.Bands\x12\x13\n\x0b\x63rosshair_x\x18\x03 \x01(\x05\x12\x13\n\x0b\x63rosshair_y\x18\x04 \x01(\x05\"\xb0\x01\n\x05Image\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\r\n\x05width\x18\x02 \x01(\r\x12\x0e\n\x06height\x18\x03 \x01(\r\x12\r\n\x05\x66ocus\x18\x04 \x01(\x01\x12\x0c\n\x04\x64\x61ta\x18\x05 \x01(\x0c\x12>\n\x0b\x61nnotations\x18\x06 \x01(\x0b\x32).carbon.frontend.image_stream.Annotations\"\xd1\x01\n\x12\x43\x61meraImageRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x11\n\tannotated\x18\x03 \x01(\x08\x12$\n\x1cinclude_annotations_metadata\x18\x04 \x01(\x08\x12\x17\n\x0f\x64ont_downsample\x18\x05 \x01(\x08\x12\x15\n\rencode_as_png\x18\x06 \x01(\x08\x12\x15\n\rencode_as_raw\x18\x07 \x01(\x08\"\x8e\x01\n!GetPredictImageByTimestampRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x15\n\rcrop_around_x\x18\x03 \x01(\x05\x12\x15\n\rcrop_around_y\x18\x04 \x01(\x05\"V\n\"GetPredictImageByTimestampResponse\x12\x0c\n\x04\x64\x61ta\x18\x01 \x01(\x0c\x12\x10\n\x08\x63\x65nter_x\x18\x02 \x01(\x05\x12\x10\n\x08\x63\x65nter_y\x18\x03 \x01(\x05\"p\n\x13PossiblePerspective\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x15\n\rcrop_around_x\x18\x02 \x01(\x05\x12\x15\n\rcrop_around_y\x18\x03 \x01(\x05\"\x9d\x01\n\"GetMultiPredictPerspectivesRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\x12G\n\x0cperspectives\x18\x02 \x03(\x0b\x32\x31.carbon.frontend.image_stream.PossiblePerspective\x12\x1e\n\x16requested_perspectives\x18\x03 \x01(\x05\"t\n\x13\x43\x65ntroidPerspective\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x10\n\x08\x63\x65nter_x\x18\x02 \x01(\x05\x12\x10\n\x08\x63\x65nter_y\x18\x03 \x01(\x05\x12\x0c\n\x04\x64\x61ta\x18\x04 \x01(\x0c\"n\n#GetMultiPredictPerspectivesResponse\x12G\n\x0cperspectives\x18\x01 \x03(\x0b\x32\x31.carbon.frontend.image_stream.CentroidPerspective2\xc8\x03\n\x12ImageStreamService\x12k\n\x12GetNextCameraImage\x12\x30.carbon.frontend.image_stream.CameraImageRequest\x1a#.carbon.frontend.image_stream.Image\x12\x9f\x01\n\x1aGetPredictImageByTimestamp\x12?.carbon.frontend.image_stream.GetPredictImageByTimestampRequest\<EMAIL>.image_stream.GetPredictImageByTimestampResponse\x12\xa2\x01\n\x1bGetMultiPredictPerspectives\<EMAIL>.image_stream.GetMultiPredictPerspectivesRequest\x1a\x41.carbon.frontend.image_stream.GetMultiPredictPerspectivesResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,weed__tracking_dot_proto_dot_weed__tracking__pb2.DESCRIPTOR,])




_ANNOTATIONS = _descriptor.Descriptor(
  name='Annotations',
  full_name='carbon.frontend.image_stream.Annotations',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='detections', full_name='carbon.frontend.image_stream.Annotations.detections', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bands', full_name='carbon.frontend.image_stream.Annotations.bands', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crosshair_x', full_name='carbon.frontend.image_stream.Annotations.crosshair_x', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crosshair_y', full_name='carbon.frontend.image_stream.Annotations.crosshair_y', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=136,
  serialized_end=275,
)


_IMAGE = _descriptor.Descriptor(
  name='Image',
  full_name='carbon.frontend.image_stream.Image',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.image_stream.Image.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width', full_name='carbon.frontend.image_stream.Image.width', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='carbon.frontend.image_stream.Image.height', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='focus', full_name='carbon.frontend.image_stream.Image.focus', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='carbon.frontend.image_stream.Image.data', index=4,
      number=5, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='annotations', full_name='carbon.frontend.image_stream.Image.annotations', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=278,
  serialized_end=454,
)


_CAMERAIMAGEREQUEST = _descriptor.Descriptor(
  name='CameraImageRequest',
  full_name='carbon.frontend.image_stream.CameraImageRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.image_stream.CameraImageRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.image_stream.CameraImageRequest.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='annotated', full_name='carbon.frontend.image_stream.CameraImageRequest.annotated', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='include_annotations_metadata', full_name='carbon.frontend.image_stream.CameraImageRequest.include_annotations_metadata', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dont_downsample', full_name='carbon.frontend.image_stream.CameraImageRequest.dont_downsample', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='encode_as_png', full_name='carbon.frontend.image_stream.CameraImageRequest.encode_as_png', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='encode_as_raw', full_name='carbon.frontend.image_stream.CameraImageRequest.encode_as_raw', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=457,
  serialized_end=666,
)


_GETPREDICTIMAGEBYTIMESTAMPREQUEST = _descriptor.Descriptor(
  name='GetPredictImageByTimestampRequest',
  full_name='carbon.frontend.image_stream.GetPredictImageByTimestampRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.image_stream.GetPredictImageByTimestampRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.image_stream.GetPredictImageByTimestampRequest.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_around_x', full_name='carbon.frontend.image_stream.GetPredictImageByTimestampRequest.crop_around_x', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_around_y', full_name='carbon.frontend.image_stream.GetPredictImageByTimestampRequest.crop_around_y', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=669,
  serialized_end=811,
)


_GETPREDICTIMAGEBYTIMESTAMPRESPONSE = _descriptor.Descriptor(
  name='GetPredictImageByTimestampResponse',
  full_name='carbon.frontend.image_stream.GetPredictImageByTimestampResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='carbon.frontend.image_stream.GetPredictImageByTimestampResponse.data', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_x', full_name='carbon.frontend.image_stream.GetPredictImageByTimestampResponse.center_x', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_y', full_name='carbon.frontend.image_stream.GetPredictImageByTimestampResponse.center_y', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=813,
  serialized_end=899,
)


_POSSIBLEPERSPECTIVE = _descriptor.Descriptor(
  name='PossiblePerspective',
  full_name='carbon.frontend.image_stream.PossiblePerspective',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.image_stream.PossiblePerspective.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_around_x', full_name='carbon.frontend.image_stream.PossiblePerspective.crop_around_x', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_around_y', full_name='carbon.frontend.image_stream.PossiblePerspective.crop_around_y', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=901,
  serialized_end=1013,
)


_GETMULTIPREDICTPERSPECTIVESREQUEST = _descriptor.Descriptor(
  name='GetMultiPredictPerspectivesRequest',
  full_name='carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='perspectives', full_name='carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.perspectives', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='requested_perspectives', full_name='carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest.requested_perspectives', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1016,
  serialized_end=1173,
)


_CENTROIDPERSPECTIVE = _descriptor.Descriptor(
  name='CentroidPerspective',
  full_name='carbon.frontend.image_stream.CentroidPerspective',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.image_stream.CentroidPerspective.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_x', full_name='carbon.frontend.image_stream.CentroidPerspective.center_x', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='center_y', full_name='carbon.frontend.image_stream.CentroidPerspective.center_y', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='carbon.frontend.image_stream.CentroidPerspective.data', index=3,
      number=4, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1175,
  serialized_end=1291,
)


_GETMULTIPREDICTPERSPECTIVESRESPONSE = _descriptor.Descriptor(
  name='GetMultiPredictPerspectivesResponse',
  full_name='carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='perspectives', full_name='carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse.perspectives', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1293,
  serialized_end=1403,
)

_ANNOTATIONS.fields_by_name['detections'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._DETECTIONS
_ANNOTATIONS.fields_by_name['bands'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._BANDS
_IMAGE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_IMAGE.fields_by_name['annotations'].message_type = _ANNOTATIONS
_CAMERAIMAGEREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETPREDICTIMAGEBYTIMESTAMPREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_POSSIBLEPERSPECTIVE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETMULTIPREDICTPERSPECTIVESREQUEST.fields_by_name['perspectives'].message_type = _POSSIBLEPERSPECTIVE
_CENTROIDPERSPECTIVE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETMULTIPREDICTPERSPECTIVESRESPONSE.fields_by_name['perspectives'].message_type = _CENTROIDPERSPECTIVE
DESCRIPTOR.message_types_by_name['Annotations'] = _ANNOTATIONS
DESCRIPTOR.message_types_by_name['Image'] = _IMAGE
DESCRIPTOR.message_types_by_name['CameraImageRequest'] = _CAMERAIMAGEREQUEST
DESCRIPTOR.message_types_by_name['GetPredictImageByTimestampRequest'] = _GETPREDICTIMAGEBYTIMESTAMPREQUEST
DESCRIPTOR.message_types_by_name['GetPredictImageByTimestampResponse'] = _GETPREDICTIMAGEBYTIMESTAMPRESPONSE
DESCRIPTOR.message_types_by_name['PossiblePerspective'] = _POSSIBLEPERSPECTIVE
DESCRIPTOR.message_types_by_name['GetMultiPredictPerspectivesRequest'] = _GETMULTIPREDICTPERSPECTIVESREQUEST
DESCRIPTOR.message_types_by_name['CentroidPerspective'] = _CENTROIDPERSPECTIVE
DESCRIPTOR.message_types_by_name['GetMultiPredictPerspectivesResponse'] = _GETMULTIPREDICTPERSPECTIVESRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

Annotations = _reflection.GeneratedProtocolMessageType('Annotations', (_message.Message,), {
  'DESCRIPTOR' : _ANNOTATIONS,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.Annotations)
  })
_sym_db.RegisterMessage(Annotations)

Image = _reflection.GeneratedProtocolMessageType('Image', (_message.Message,), {
  'DESCRIPTOR' : _IMAGE,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.Image)
  })
_sym_db.RegisterMessage(Image)

CameraImageRequest = _reflection.GeneratedProtocolMessageType('CameraImageRequest', (_message.Message,), {
  'DESCRIPTOR' : _CAMERAIMAGEREQUEST,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.CameraImageRequest)
  })
_sym_db.RegisterMessage(CameraImageRequest)

GetPredictImageByTimestampRequest = _reflection.GeneratedProtocolMessageType('GetPredictImageByTimestampRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPREDICTIMAGEBYTIMESTAMPREQUEST,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.GetPredictImageByTimestampRequest)
  })
_sym_db.RegisterMessage(GetPredictImageByTimestampRequest)

GetPredictImageByTimestampResponse = _reflection.GeneratedProtocolMessageType('GetPredictImageByTimestampResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETPREDICTIMAGEBYTIMESTAMPRESPONSE,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.GetPredictImageByTimestampResponse)
  })
_sym_db.RegisterMessage(GetPredictImageByTimestampResponse)

PossiblePerspective = _reflection.GeneratedProtocolMessageType('PossiblePerspective', (_message.Message,), {
  'DESCRIPTOR' : _POSSIBLEPERSPECTIVE,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.PossiblePerspective)
  })
_sym_db.RegisterMessage(PossiblePerspective)

GetMultiPredictPerspectivesRequest = _reflection.GeneratedProtocolMessageType('GetMultiPredictPerspectivesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETMULTIPREDICTPERSPECTIVESREQUEST,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.GetMultiPredictPerspectivesRequest)
  })
_sym_db.RegisterMessage(GetMultiPredictPerspectivesRequest)

CentroidPerspective = _reflection.GeneratedProtocolMessageType('CentroidPerspective', (_message.Message,), {
  'DESCRIPTOR' : _CENTROIDPERSPECTIVE,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.CentroidPerspective)
  })
_sym_db.RegisterMessage(CentroidPerspective)

GetMultiPredictPerspectivesResponse = _reflection.GeneratedProtocolMessageType('GetMultiPredictPerspectivesResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETMULTIPREDICTPERSPECTIVESRESPONSE,
  '__module__' : 'frontend.proto.image_stream_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.image_stream.GetMultiPredictPerspectivesResponse)
  })
_sym_db.RegisterMessage(GetMultiPredictPerspectivesResponse)


DESCRIPTOR._options = None

_IMAGESTREAMSERVICE = _descriptor.ServiceDescriptor(
  name='ImageStreamService',
  full_name='carbon.frontend.image_stream.ImageStreamService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1406,
  serialized_end=1862,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextCameraImage',
    full_name='carbon.frontend.image_stream.ImageStreamService.GetNextCameraImage',
    index=0,
    containing_service=None,
    input_type=_CAMERAIMAGEREQUEST,
    output_type=_IMAGE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetPredictImageByTimestamp',
    full_name='carbon.frontend.image_stream.ImageStreamService.GetPredictImageByTimestamp',
    index=1,
    containing_service=None,
    input_type=_GETPREDICTIMAGEBYTIMESTAMPREQUEST,
    output_type=_GETPREDICTIMAGEBYTIMESTAMPRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetMultiPredictPerspectives',
    full_name='carbon.frontend.image_stream.ImageStreamService.GetMultiPredictPerspectives',
    index=2,
    containing_service=None,
    input_type=_GETMULTIPREDICTPERSPECTIVESREQUEST,
    output_type=_GETMULTIPREDICTPERSPECTIVESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_IMAGESTREAMSERVICE)

DESCRIPTOR.services_by_name['ImageStreamService'] = _IMAGESTREAMSERVICE

# @@protoc_insertion_point(module_scope)

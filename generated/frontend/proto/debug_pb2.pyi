"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.alarm_pb2 import (
    AlarmRow as frontend___proto___alarm_pb2___AlarmRow,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.logging.logging_pb2 import (
    LogLevelValue as proto___logging___logging_pb2___LogLevelValue,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class RobotMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    serial: typing___Text = ...

    @property
    def alarms(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[frontend___proto___alarm_pb2___AlarmRow]: ...

    def __init__(self,
        *,
        serial : typing___Optional[typing___Text] = None,
        alarms : typing___Optional[typing___Iterable[frontend___proto___alarm_pb2___AlarmRow]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alarms",b"alarms",u"serial",b"serial"]) -> None: ...
type___RobotMessage = RobotMessage

class SetLogLevelRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    level: proto___logging___logging_pb2___LogLevelValue = ...
    component: typing___Text = ...
    row_num: builtin___int = ...

    def __init__(self,
        *,
        level : typing___Optional[proto___logging___logging_pb2___LogLevelValue] = None,
        component : typing___Optional[typing___Text] = None,
        row_num : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"component",b"component",u"level",b"level",u"row_num",b"row_num"]) -> None: ...
type___SetLogLevelRequest = SetLogLevelRequest

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/reporting.proto

#include "frontend/proto/reporting.pb.h"
#include "frontend/proto/reporting.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace features {

static const char* ReportingService_method_names[] = {
  "/carbon.frontend.features.ReportingService/GetNextLocationHistory",
};

std::unique_ptr< ReportingService::Stub> ReportingService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ReportingService::Stub> stub(new ReportingService::Stub(channel, options));
  return stub;
}

ReportingService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextLocationHistory_(ReportingService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ReportingService::Stub::GetNextLocationHistory(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::features::LocationHistory* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::features::LocationHistory, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextLocationHistory_, context, request, response);
}

void ReportingService::Stub::async::GetNextLocationHistory(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::features::LocationHistory* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::features::LocationHistory, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextLocationHistory_, context, request, response, std::move(f));
}

void ReportingService::Stub::async::GetNextLocationHistory(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::features::LocationHistory* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextLocationHistory_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::LocationHistory>* ReportingService::Stub::PrepareAsyncGetNextLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::features::LocationHistory, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextLocationHistory_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::features::LocationHistory>* ReportingService::Stub::AsyncGetNextLocationHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextLocationHistoryRaw(context, request, cq);
  result->StartCall();
  return result;
}

ReportingService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ReportingService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ReportingService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::features::LocationHistory, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ReportingService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::features::LocationHistory* resp) {
               return service->GetNextLocationHistory(ctx, req, resp);
             }, this)));
}

ReportingService::Service::~Service() {
}

::grpc::Status ReportingService::Service::GetNextLocationHistory(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::features::LocationHistory* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace features


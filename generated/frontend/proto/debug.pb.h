// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/debug.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdebug_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdebug_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/message_lite.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include "frontend/proto/alarm.pb.h"
#include "frontend/proto/util.pb.h"
#include "proto/logging/logging.pb.h"
#include "weed_tracking/proto/weed_tracking.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fdebug_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fdebug_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[2]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
namespace carbon {
namespace frontend {
namespace debug {
class RobotMessage;
struct RobotMessageDefaultTypeInternal;
extern RobotMessageDefaultTypeInternal _RobotMessage_default_instance_;
class SetLogLevelRequest;
struct SetLogLevelRequestDefaultTypeInternal;
extern SetLogLevelRequestDefaultTypeInternal _SetLogLevelRequest_default_instance_;
}  // namespace debug
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::debug::RobotMessage* Arena::CreateMaybeMessage<::carbon::frontend::debug::RobotMessage>(Arena*);
template<> ::carbon::frontend::debug::SetLogLevelRequest* Arena::CreateMaybeMessage<::carbon::frontend::debug::SetLogLevelRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace debug {

// ===================================================================

class RobotMessage final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:carbon.frontend.debug.RobotMessage) */ {
 public:
  inline RobotMessage() : RobotMessage(nullptr) {}
  ~RobotMessage() override;
  explicit constexpr RobotMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RobotMessage(const RobotMessage& from);
  RobotMessage(RobotMessage&& from) noexcept
    : RobotMessage() {
    *this = ::std::move(from);
  }

  inline RobotMessage& operator=(const RobotMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline RobotMessage& operator=(RobotMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const RobotMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const RobotMessage* internal_default_instance() {
    return reinterpret_cast<const RobotMessage*>(
               &_RobotMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RobotMessage& a, RobotMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(RobotMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RobotMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RobotMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RobotMessage>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const RobotMessage& from);
  void MergeFrom(const RobotMessage& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(RobotMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.debug.RobotMessage";
  }
  protected:
  explicit RobotMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlarmsFieldNumber = 2,
    kSerialFieldNumber = 1,
  };
  // repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
  int alarms_size() const;
  private:
  int _internal_alarms_size() const;
  public:
  void clear_alarms();
  ::carbon::frontend::alarm::AlarmRow* mutable_alarms(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
      mutable_alarms();
  private:
  const ::carbon::frontend::alarm::AlarmRow& _internal_alarms(int index) const;
  ::carbon::frontend::alarm::AlarmRow* _internal_add_alarms();
  public:
  const ::carbon::frontend::alarm::AlarmRow& alarms(int index) const;
  ::carbon::frontend::alarm::AlarmRow* add_alarms();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
      alarms() const;

  // string serial = 1;
  void clear_serial();
  const std::string& serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial();
  PROTOBUF_NODISCARD std::string* release_serial();
  void set_allocated_serial(std::string* serial);
  private:
  const std::string& _internal_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial(const std::string& value);
  std::string* _internal_mutable_serial();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.debug.RobotMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow > alarms_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdebug_2eproto;
};
// -------------------------------------------------------------------

class SetLogLevelRequest final :
    public ::PROTOBUF_NAMESPACE_ID::MessageLite /* @@protoc_insertion_point(class_definition:carbon.frontend.debug.SetLogLevelRequest) */ {
 public:
  inline SetLogLevelRequest() : SetLogLevelRequest(nullptr) {}
  ~SetLogLevelRequest() override;
  explicit constexpr SetLogLevelRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetLogLevelRequest(const SetLogLevelRequest& from);
  SetLogLevelRequest(SetLogLevelRequest&& from) noexcept
    : SetLogLevelRequest() {
    *this = ::std::move(from);
  }

  inline SetLogLevelRequest& operator=(const SetLogLevelRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetLogLevelRequest& operator=(SetLogLevelRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const SetLogLevelRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetLogLevelRequest* internal_default_instance() {
    return reinterpret_cast<const SetLogLevelRequest*>(
               &_SetLogLevelRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(SetLogLevelRequest& a, SetLogLevelRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetLogLevelRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetLogLevelRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetLogLevelRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetLogLevelRequest>(arena);
  }
  void CheckTypeAndMergeFrom(const ::PROTOBUF_NAMESPACE_ID::MessageLite& from)  final;
  void CopyFrom(const SetLogLevelRequest& from);
  void MergeFrom(const SetLogLevelRequest& from);
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const;
  void InternalSwap(SetLogLevelRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.debug.SetLogLevelRequest";
  }
  protected:
  explicit SetLogLevelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  std::string GetTypeName() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kComponentFieldNumber = 2,
    kLevelFieldNumber = 1,
    kRowNumFieldNumber = 3,
  };
  // string component = 2;
  void clear_component();
  const std::string& component() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_component(ArgT0&& arg0, ArgT... args);
  std::string* mutable_component();
  PROTOBUF_NODISCARD std::string* release_component();
  void set_allocated_component(std::string* component);
  private:
  const std::string& _internal_component() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_component(const std::string& value);
  std::string* _internal_mutable_component();
  public:

  // .carbon.logging.LogLevel level = 1;
  void clear_level();
  ::carbon::logging::LogLevel level() const;
  void set_level(::carbon::logging::LogLevel value);
  private:
  ::carbon::logging::LogLevel _internal_level() const;
  void _internal_set_level(::carbon::logging::LogLevel value);
  public:

  // int32 row_num = 3;
  void clear_row_num();
  int32_t row_num() const;
  void set_row_num(int32_t value);
  private:
  int32_t _internal_row_num() const;
  void _internal_set_row_num(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.debug.SetLogLevelRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr component_;
  int level_;
  int32_t row_num_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fdebug_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RobotMessage

// string serial = 1;
inline void RobotMessage::clear_serial() {
  serial_.ClearToEmpty();
}
inline const std::string& RobotMessage::serial() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.RobotMessage.serial)
  return _internal_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RobotMessage::set_serial(ArgT0&& arg0, ArgT... args) {
 
 serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.RobotMessage.serial)
}
inline std::string* RobotMessage::mutable_serial() {
  std::string* _s = _internal_mutable_serial();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.RobotMessage.serial)
  return _s;
}
inline const std::string& RobotMessage::_internal_serial() const {
  return serial_.Get();
}
inline void RobotMessage::_internal_set_serial(const std::string& value) {
  
  serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RobotMessage::_internal_mutable_serial() {
  
  return serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RobotMessage::release_serial() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.RobotMessage.serial)
  return serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RobotMessage::set_allocated_serial(std::string* serial) {
  if (serial != nullptr) {
    
  } else {
    
  }
  serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.RobotMessage.serial)
}

// repeated .carbon.frontend.alarm.AlarmRow alarms = 2;
inline int RobotMessage::_internal_alarms_size() const {
  return alarms_.size();
}
inline int RobotMessage::alarms_size() const {
  return _internal_alarms_size();
}
inline ::carbon::frontend::alarm::AlarmRow* RobotMessage::mutable_alarms(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.RobotMessage.alarms)
  return alarms_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >*
RobotMessage::mutable_alarms() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.debug.RobotMessage.alarms)
  return &alarms_;
}
inline const ::carbon::frontend::alarm::AlarmRow& RobotMessage::_internal_alarms(int index) const {
  return alarms_.Get(index);
}
inline const ::carbon::frontend::alarm::AlarmRow& RobotMessage::alarms(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.RobotMessage.alarms)
  return _internal_alarms(index);
}
inline ::carbon::frontend::alarm::AlarmRow* RobotMessage::_internal_add_alarms() {
  return alarms_.Add();
}
inline ::carbon::frontend::alarm::AlarmRow* RobotMessage::add_alarms() {
  ::carbon::frontend::alarm::AlarmRow* _add = _internal_add_alarms();
  // @@protoc_insertion_point(field_add:carbon.frontend.debug.RobotMessage.alarms)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::alarm::AlarmRow >&
RobotMessage::alarms() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.debug.RobotMessage.alarms)
  return alarms_;
}

// -------------------------------------------------------------------

// SetLogLevelRequest

// .carbon.logging.LogLevel level = 1;
inline void SetLogLevelRequest::clear_level() {
  level_ = 0;
}
inline ::carbon::logging::LogLevel SetLogLevelRequest::_internal_level() const {
  return static_cast< ::carbon::logging::LogLevel >(level_);
}
inline ::carbon::logging::LogLevel SetLogLevelRequest::level() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.SetLogLevelRequest.level)
  return _internal_level();
}
inline void SetLogLevelRequest::_internal_set_level(::carbon::logging::LogLevel value) {
  
  level_ = value;
}
inline void SetLogLevelRequest::set_level(::carbon::logging::LogLevel value) {
  _internal_set_level(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.SetLogLevelRequest.level)
}

// string component = 2;
inline void SetLogLevelRequest::clear_component() {
  component_.ClearToEmpty();
}
inline const std::string& SetLogLevelRequest::component() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.SetLogLevelRequest.component)
  return _internal_component();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetLogLevelRequest::set_component(ArgT0&& arg0, ArgT... args) {
 
 component_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.SetLogLevelRequest.component)
}
inline std::string* SetLogLevelRequest::mutable_component() {
  std::string* _s = _internal_mutable_component();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.SetLogLevelRequest.component)
  return _s;
}
inline const std::string& SetLogLevelRequest::_internal_component() const {
  return component_.Get();
}
inline void SetLogLevelRequest::_internal_set_component(const std::string& value) {
  
  component_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetLogLevelRequest::_internal_mutable_component() {
  
  return component_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetLogLevelRequest::release_component() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.SetLogLevelRequest.component)
  return component_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetLogLevelRequest::set_allocated_component(std::string* component) {
  if (component != nullptr) {
    
  } else {
    
  }
  component_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), component,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (component_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    component_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.SetLogLevelRequest.component)
}

// int32 row_num = 3;
inline void SetLogLevelRequest::clear_row_num() {
  row_num_ = 0;
}
inline int32_t SetLogLevelRequest::_internal_row_num() const {
  return row_num_;
}
inline int32_t SetLogLevelRequest::row_num() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.SetLogLevelRequest.row_num)
  return _internal_row_num();
}
inline void SetLogLevelRequest::_internal_set_row_num(int32_t value) {
  
  row_num_ = value;
}
inline void SetLogLevelRequest::set_row_num(int32_t value) {
  _internal_set_row_num(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.SetLogLevelRequest.row_num)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace debug
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fdebug_2eproto

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/plant_captcha.proto

#include "frontend/proto/plant_captcha.pb.h"
#include "frontend/proto/plant_captcha.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace plant_captcha {

static const char* PlantCaptchaService_method_names[] = {
  "/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptcha",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaStatus",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchasList",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/DeletePlantCaptcha",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptcha",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptcha",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/StartPlantCaptchaUpload",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/GetNextPlantCaptchaUploadState",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/SubmitPlantCaptchaResults",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/GetPlantCaptchaItemResults",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/CalculatePlantCaptcha",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/GetOriginalModelinatorConfig",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/GetCaptchaRowStatus",
  "/carbon.frontend.plant_captcha.PlantCaptchaService/CancelPlantCaptchaOnRow",
};

std::unique_ptr< PlantCaptchaService::Stub> PlantCaptchaService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< PlantCaptchaService::Stub> stub(new PlantCaptchaService::Stub(channel, options));
  return stub;
}

PlantCaptchaService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_StartPlantCaptcha_(PlantCaptchaService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextPlantCaptchaStatus_(PlantCaptchaService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextPlantCaptchasList_(PlantCaptchaService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeletePlantCaptcha_(PlantCaptchaService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetPlantCaptcha_(PlantCaptchaService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CancelPlantCaptcha_(PlantCaptchaService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartPlantCaptchaUpload_(PlantCaptchaService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextPlantCaptchaUploadState_(PlantCaptchaService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SubmitPlantCaptchaResults_(PlantCaptchaService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetPlantCaptchaItemResults_(PlantCaptchaService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CalculatePlantCaptcha_(PlantCaptchaService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetOriginalModelinatorConfig_(PlantCaptchaService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetCaptchaRowStatus_(PlantCaptchaService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CancelPlantCaptchaOnRow_(PlantCaptchaService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status PlantCaptchaService::Stub::StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartPlantCaptcha_, context, request, response);
}

void PlantCaptchaService::Stub::async::StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartPlantCaptcha_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::StartPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartPlantCaptcha_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* PlantCaptchaService::Stub::PrepareAsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse, ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartPlantCaptcha_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse>* PlantCaptchaService::Stub::AsyncStartPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartPlantCaptchaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextPlantCaptchaStatus_, context, request, response);
}

void PlantCaptchaService::Stub::async::GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPlantCaptchaStatus_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::GetNextPlantCaptchaStatus(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPlantCaptchaStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* PlantCaptchaService::Stub::PrepareAsyncGetNextPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextPlantCaptchaStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse>* PlantCaptchaService::Stub::AsyncGetNextPlantCaptchaStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextPlantCaptchaStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextPlantCaptchasList_, context, request, response);
}

void PlantCaptchaService::Stub::async::GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPlantCaptchasList_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::GetNextPlantCaptchasList(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPlantCaptchasList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* PlantCaptchaService::Stub::PrepareAsyncGetNextPlantCaptchasListRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextPlantCaptchasList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse>* PlantCaptchaService::Stub::AsyncGetNextPlantCaptchasListRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextPlantCaptchasListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeletePlantCaptcha_, context, request, response);
}

void PlantCaptchaService::Stub::async::DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeletePlantCaptcha_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::DeletePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeletePlantCaptcha_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::PrepareAsyncDeletePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeletePlantCaptcha_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::AsyncDeletePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeletePlantCaptchaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetPlantCaptcha_, context, request, response);
}

void PlantCaptchaService::Stub::async::GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPlantCaptcha_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::GetPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPlantCaptcha_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* PlantCaptchaService::Stub::PrepareAsyncGetPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse, ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetPlantCaptcha_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse>* PlantCaptchaService::Stub::AsyncGetPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetPlantCaptchaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CancelPlantCaptcha_, context, request, response);
}

void PlantCaptchaService::Stub::async::CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelPlantCaptcha_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::CancelPlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelPlantCaptcha_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::PrepareAsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CancelPlantCaptcha_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::AsyncCancelPlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCancelPlantCaptchaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartPlantCaptchaUpload_, context, request, response);
}

void PlantCaptchaService::Stub::async::StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartPlantCaptchaUpload_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::StartPlantCaptchaUpload(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartPlantCaptchaUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::PrepareAsyncStartPlantCaptchaUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartPlantCaptchaUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::AsyncStartPlantCaptchaUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartPlantCaptchaUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextPlantCaptchaUploadState_, context, request, response);
}

void PlantCaptchaService::Stub::async::GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPlantCaptchaUploadState_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::GetNextPlantCaptchaUploadState(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextPlantCaptchaUploadState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* PlantCaptchaService::Stub::PrepareAsyncGetNextPlantCaptchaUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextPlantCaptchaUploadState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse>* PlantCaptchaService::Stub::AsyncGetNextPlantCaptchaUploadStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextPlantCaptchaUploadStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SubmitPlantCaptchaResults_, context, request, response);
}

void PlantCaptchaService::Stub::async::SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SubmitPlantCaptchaResults_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::SubmitPlantCaptchaResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SubmitPlantCaptchaResults_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::PrepareAsyncSubmitPlantCaptchaResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SubmitPlantCaptchaResults_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::AsyncSubmitPlantCaptchaResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSubmitPlantCaptchaResultsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetPlantCaptchaItemResults_, context, request, response);
}

void PlantCaptchaService::Stub::async::GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPlantCaptchaItemResults_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::GetPlantCaptchaItemResults(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetPlantCaptchaItemResults_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* PlantCaptchaService::Stub::PrepareAsyncGetPlantCaptchaItemResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetPlantCaptchaItemResults_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse>* PlantCaptchaService::Stub::AsyncGetPlantCaptchaItemResultsRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetPlantCaptchaItemResultsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CalculatePlantCaptcha_, context, request, response);
}

void PlantCaptchaService::Stub::async::CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CalculatePlantCaptcha_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::CalculatePlantCaptcha(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CalculatePlantCaptcha_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* PlantCaptchaService::Stub::PrepareAsyncCalculatePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CalculatePlantCaptcha_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse>* PlantCaptchaService::Stub::AsyncCalculatePlantCaptchaRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCalculatePlantCaptchaRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetOriginalModelinatorConfig_, context, request, response);
}

void PlantCaptchaService::Stub::async::GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetOriginalModelinatorConfig_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::GetOriginalModelinatorConfig(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetOriginalModelinatorConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* PlantCaptchaService::Stub::PrepareAsyncGetOriginalModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetOriginalModelinatorConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse>* PlantCaptchaService::Stub::AsyncGetOriginalModelinatorConfigRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetOriginalModelinatorConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetCaptchaRowStatus_, context, request, response);
}

void PlantCaptchaService::Stub::async::GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCaptchaRowStatus_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::GetCaptchaRowStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetCaptchaRowStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* PlantCaptchaService::Stub::PrepareAsyncGetCaptchaRowStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetCaptchaRowStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse>* PlantCaptchaService::Stub::AsyncGetCaptchaRowStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetCaptchaRowStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status PlantCaptchaService::Stub::CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CancelPlantCaptchaOnRow_, context, request, response);
}

void PlantCaptchaService::Stub::async::CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelPlantCaptchaOnRow_, context, request, response, std::move(f));
}

void PlantCaptchaService::Stub::async::CancelPlantCaptchaOnRow(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CancelPlantCaptchaOnRow_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::PrepareAsyncCancelPlantCaptchaOnRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CancelPlantCaptchaOnRow_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PlantCaptchaService::Stub::AsyncCancelPlantCaptchaOnRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCancelPlantCaptchaOnRowRaw(context, request, cq);
  result->StartCall();
  return result;
}

PlantCaptchaService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* req,
             ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* resp) {
               return service->StartPlantCaptcha(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* req,
             ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* resp) {
               return service->GetNextPlantCaptchaStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* req,
             ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* resp) {
               return service->GetNextPlantCaptchasList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DeletePlantCaptcha(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* req,
             ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* resp) {
               return service->GetPlantCaptcha(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->CancelPlantCaptcha(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartPlantCaptchaUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* req,
             ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* resp) {
               return service->GetNextPlantCaptchaUploadState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SubmitPlantCaptchaResults(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* req,
             ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* resp) {
               return service->GetPlantCaptchaItemResults(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* req,
             ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* resp) {
               return service->CalculatePlantCaptcha(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* req,
             ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* resp) {
               return service->GetOriginalModelinatorConfig(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* resp) {
               return service->GetCaptchaRowStatus(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      PlantCaptchaService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< PlantCaptchaService::Service, ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](PlantCaptchaService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->CancelPlantCaptchaOnRow(ctx, req, resp);
             }, this)));
}

PlantCaptchaService::Service::~Service() {
}

::grpc::Status PlantCaptchaService::Service::StartPlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::StartPlantCaptchaResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::GetNextPlantCaptchaStatus(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::GetNextPlantCaptchasList(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchasListResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::DeletePlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::DeletePlantCaptchaRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::GetPlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::CancelPlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::StartPlantCaptchaUpload(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::StartPlantCaptchaUploadRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::GetNextPlantCaptchaUploadState(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateRequest* request, ::carbon::frontend::plant_captcha::GetNextPlantCaptchaUploadStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::SubmitPlantCaptchaResults(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::SubmitPlantCaptchaResultsRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::GetPlantCaptchaItemResults(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsRequest* request, ::carbon::frontend::plant_captcha::GetPlantCaptchaItemResultsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::CalculatePlantCaptcha(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::CalculatePlantCaptchaRequest* request, ::carbon::frontend::plant_captcha::CalculatePlantCaptchaResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::GetOriginalModelinatorConfig(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigRequest* request, ::carbon::frontend::plant_captcha::GetOriginalModelinatorConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::GetCaptchaRowStatus(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::plant_captcha::GetCaptchaRowStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status PlantCaptchaService::Service::CancelPlantCaptchaOnRow(::grpc::ServerContext* context, const ::carbon::frontend::plant_captcha::CancelPlantCaptchaOnRowRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace plant_captcha


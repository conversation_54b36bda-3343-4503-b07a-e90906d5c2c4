# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import features_pb2 as frontend_dot_proto_dot_features__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class FeatureServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextFeatureFlags = channel.unary_unary(
                '/carbon.frontend.features.FeatureService/GetNextFeatureFlags',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.FeatureFlags.FromString,
                )
        self.GetRobotConfiguration = channel.unary_unary(
                '/carbon.frontend.features.FeatureService/GetRobotConfiguration',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_features__pb2.RobotConfiguration.FromString,
                )


class FeatureServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextFeatureFlags(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetRobotConfiguration(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_FeatureServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextFeatureFlags': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextFeatureFlags,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.FeatureFlags.SerializeToString,
            ),
            'GetRobotConfiguration': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRobotConfiguration,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_features__pb2.RobotConfiguration.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.features.FeatureService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class FeatureService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextFeatureFlags(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.features.FeatureService/GetNextFeatureFlags',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_util__pb2.FeatureFlags.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetRobotConfiguration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.features.FeatureService/GetRobotConfiguration',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_features__pb2.RobotConfiguration.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

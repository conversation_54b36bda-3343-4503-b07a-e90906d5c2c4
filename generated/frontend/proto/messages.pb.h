// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/messages.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmessages_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmessages_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/alarm.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fmessages_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fmessages_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fmessages_2eproto;
namespace carbon {
namespace frontend {
namespace debug {
class Message;
struct MessageDefaultTypeInternal;
extern MessageDefaultTypeInternal _Message_default_instance_;
class MessageRequest;
struct MessageRequestDefaultTypeInternal;
extern MessageRequestDefaultTypeInternal _MessageRequest_default_instance_;
class MessagesRequest;
struct MessagesRequestDefaultTypeInternal;
extern MessagesRequestDefaultTypeInternal _MessagesRequest_default_instance_;
class MessagesResponse;
struct MessagesResponseDefaultTypeInternal;
extern MessagesResponseDefaultTypeInternal _MessagesResponse_default_instance_;
class ReadRequest;
struct ReadRequestDefaultTypeInternal;
extern ReadRequestDefaultTypeInternal _ReadRequest_default_instance_;
}  // namespace debug
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::debug::Message* Arena::CreateMaybeMessage<::carbon::frontend::debug::Message>(Arena*);
template<> ::carbon::frontend::debug::MessageRequest* Arena::CreateMaybeMessage<::carbon::frontend::debug::MessageRequest>(Arena*);
template<> ::carbon::frontend::debug::MessagesRequest* Arena::CreateMaybeMessage<::carbon::frontend::debug::MessagesRequest>(Arena*);
template<> ::carbon::frontend::debug::MessagesResponse* Arena::CreateMaybeMessage<::carbon::frontend::debug::MessagesResponse>(Arena*);
template<> ::carbon::frontend::debug::ReadRequest* Arena::CreateMaybeMessage<::carbon::frontend::debug::ReadRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace debug {

// ===================================================================

class Message final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.debug.Message) */ {
 public:
  inline Message() : Message(nullptr) {}
  ~Message() override;
  explicit constexpr Message(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Message(const Message& from);
  Message(Message&& from) noexcept
    : Message() {
    *this = ::std::move(from);
  }

  inline Message& operator=(const Message& from) {
    CopyFrom(from);
    return *this;
  }
  inline Message& operator=(Message&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Message& default_instance() {
    return *internal_default_instance();
  }
  static inline const Message* internal_default_instance() {
    return reinterpret_cast<const Message*>(
               &_Message_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(Message& a, Message& b) {
    a.Swap(&b);
  }
  inline void Swap(Message* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Message* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Message* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Message>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Message& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Message& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Message* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.debug.Message";
  }
  protected:
  explicit Message(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 3,
    kAuthorUserIdFieldNumber = 6,
    kRecipientUserIdFieldNumber = 8,
    kTsFieldNumber = 2,
    kIdFieldNumber = 1,
    kAuthorRobotIdFieldNumber = 7,
    kRecipientCustomerIdFieldNumber = 9,
    kRecipientRobotIdFieldNumber = 10,
    kFromSupportFieldNumber = 4,
    kReadFieldNumber = 5,
  };
  // string message = 3;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // string author_user_id = 6;
  void clear_author_user_id();
  const std::string& author_user_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_author_user_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_author_user_id();
  PROTOBUF_NODISCARD std::string* release_author_user_id();
  void set_allocated_author_user_id(std::string* author_user_id);
  private:
  const std::string& _internal_author_user_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_author_user_id(const std::string& value);
  std::string* _internal_mutable_author_user_id();
  public:

  // string recipient_user_id = 8;
  void clear_recipient_user_id();
  const std::string& recipient_user_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_recipient_user_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_recipient_user_id();
  PROTOBUF_NODISCARD std::string* release_recipient_user_id();
  void set_allocated_recipient_user_id(std::string* recipient_user_id);
  private:
  const std::string& _internal_recipient_user_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_recipient_user_id(const std::string& value);
  std::string* _internal_mutable_recipient_user_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // int64 author_robot_id = 7;
  void clear_author_robot_id();
  int64_t author_robot_id() const;
  void set_author_robot_id(int64_t value);
  private:
  int64_t _internal_author_robot_id() const;
  void _internal_set_author_robot_id(int64_t value);
  public:

  // int64 recipient_customer_id = 9;
  void clear_recipient_customer_id();
  int64_t recipient_customer_id() const;
  void set_recipient_customer_id(int64_t value);
  private:
  int64_t _internal_recipient_customer_id() const;
  void _internal_set_recipient_customer_id(int64_t value);
  public:

  // int64 recipient_robot_id = 10;
  void clear_recipient_robot_id();
  int64_t recipient_robot_id() const;
  void set_recipient_robot_id(int64_t value);
  private:
  int64_t _internal_recipient_robot_id() const;
  void _internal_set_recipient_robot_id(int64_t value);
  public:

  // bool from_support = 4;
  void clear_from_support();
  bool from_support() const;
  void set_from_support(bool value);
  private:
  bool _internal_from_support() const;
  void _internal_set_from_support(bool value);
  public:

  // bool read = 5;
  void clear_read();
  bool read() const;
  void set_read(bool value);
  private:
  bool _internal_read() const;
  void _internal_set_read(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.debug.Message)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr author_user_id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr recipient_user_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  int64_t id_;
  int64_t author_robot_id_;
  int64_t recipient_customer_id_;
  int64_t recipient_robot_id_;
  bool from_support_;
  bool read_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmessages_2eproto;
};
// -------------------------------------------------------------------

class MessageRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.debug.MessageRequest) */ {
 public:
  inline MessageRequest() : MessageRequest(nullptr) {}
  ~MessageRequest() override;
  explicit constexpr MessageRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MessageRequest(const MessageRequest& from);
  MessageRequest(MessageRequest&& from) noexcept
    : MessageRequest() {
    *this = ::std::move(from);
  }

  inline MessageRequest& operator=(const MessageRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MessageRequest& operator=(MessageRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MessageRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MessageRequest* internal_default_instance() {
    return reinterpret_cast<const MessageRequest*>(
               &_MessageRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(MessageRequest& a, MessageRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MessageRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MessageRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MessageRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MessageRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MessageRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MessageRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MessageRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.debug.MessageRequest";
  }
  protected:
  explicit MessageRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessageFieldNumber = 1,
  };
  // string message = 1;
  void clear_message();
  const std::string& message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_message();
  PROTOBUF_NODISCARD std::string* release_message();
  void set_allocated_message(std::string* message);
  private:
  const std::string& _internal_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_message(const std::string& value);
  std::string* _internal_mutable_message();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.debug.MessageRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr message_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmessages_2eproto;
};
// -------------------------------------------------------------------

class MessagesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.debug.MessagesRequest) */ {
 public:
  inline MessagesRequest() : MessagesRequest(nullptr) {}
  ~MessagesRequest() override;
  explicit constexpr MessagesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MessagesRequest(const MessagesRequest& from);
  MessagesRequest(MessagesRequest&& from) noexcept
    : MessagesRequest() {
    *this = ::std::move(from);
  }

  inline MessagesRequest& operator=(const MessagesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MessagesRequest& operator=(MessagesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MessagesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MessagesRequest* internal_default_instance() {
    return reinterpret_cast<const MessagesRequest*>(
               &_MessagesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(MessagesRequest& a, MessagesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MessagesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MessagesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MessagesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MessagesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MessagesRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MessagesRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MessagesRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.debug.MessagesRequest";
  }
  protected:
  explicit MessagesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.debug.MessagesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmessages_2eproto;
};
// -------------------------------------------------------------------

class MessagesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.debug.MessagesResponse) */ {
 public:
  inline MessagesResponse() : MessagesResponse(nullptr) {}
  ~MessagesResponse() override;
  explicit constexpr MessagesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MessagesResponse(const MessagesResponse& from);
  MessagesResponse(MessagesResponse&& from) noexcept
    : MessagesResponse() {
    *this = ::std::move(from);
  }

  inline MessagesResponse& operator=(const MessagesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline MessagesResponse& operator=(MessagesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MessagesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const MessagesResponse* internal_default_instance() {
    return reinterpret_cast<const MessagesResponse*>(
               &_MessagesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(MessagesResponse& a, MessagesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(MessagesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MessagesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MessagesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MessagesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MessagesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MessagesResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MessagesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.debug.MessagesResponse";
  }
  protected:
  explicit MessagesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMessagesFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.frontend.debug.Message messages = 2;
  int messages_size() const;
  private:
  int _internal_messages_size() const;
  public:
  void clear_messages();
  ::carbon::frontend::debug::Message* mutable_messages(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::debug::Message >*
      mutable_messages();
  private:
  const ::carbon::frontend::debug::Message& _internal_messages(int index) const;
  ::carbon::frontend::debug::Message* _internal_add_messages();
  public:
  const ::carbon::frontend::debug::Message& messages(int index) const;
  ::carbon::frontend::debug::Message* add_messages();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::debug::Message >&
      messages() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.debug.MessagesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::debug::Message > messages_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmessages_2eproto;
};
// -------------------------------------------------------------------

class ReadRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.debug.ReadRequest) */ {
 public:
  inline ReadRequest() : ReadRequest(nullptr) {}
  ~ReadRequest() override;
  explicit constexpr ReadRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReadRequest(const ReadRequest& from);
  ReadRequest(ReadRequest&& from) noexcept
    : ReadRequest() {
    *this = ::std::move(from);
  }

  inline ReadRequest& operator=(const ReadRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReadRequest& operator=(ReadRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReadRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReadRequest* internal_default_instance() {
    return reinterpret_cast<const ReadRequest*>(
               &_ReadRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(ReadRequest& a, ReadRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ReadRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReadRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReadRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReadRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ReadRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ReadRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReadRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.debug.ReadRequest";
  }
  protected:
  explicit ReadRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // int64 id = 1;
  void clear_id();
  int64_t id() const;
  void set_id(int64_t value);
  private:
  int64_t _internal_id() const;
  void _internal_set_id(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.debug.ReadRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int64_t id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmessages_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// Message

// int64 id = 1;
inline void Message::clear_id() {
  id_ = int64_t{0};
}
inline int64_t Message::_internal_id() const {
  return id_;
}
inline int64_t Message::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.id)
  return _internal_id();
}
inline void Message::_internal_set_id(int64_t value) {
  
  id_ = value;
}
inline void Message::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.id)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool Message::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool Message::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& Message::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& Message::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.ts)
  return _internal_ts();
}
inline void Message::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.debug.Message.ts)
}
inline ::carbon::frontend::util::Timestamp* Message::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Message::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.Message.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* Message::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* Message::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.Message.ts)
  return _msg;
}
inline void Message::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.Message.ts)
}

// string message = 3;
inline void Message::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& Message::message() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Message::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.message)
}
inline std::string* Message::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.Message.message)
  return _s;
}
inline const std::string& Message::_internal_message() const {
  return message_.Get();
}
inline void Message::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Message::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Message::release_message() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.Message.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Message::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.Message.message)
}

// bool from_support = 4;
inline void Message::clear_from_support() {
  from_support_ = false;
}
inline bool Message::_internal_from_support() const {
  return from_support_;
}
inline bool Message::from_support() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.from_support)
  return _internal_from_support();
}
inline void Message::_internal_set_from_support(bool value) {
  
  from_support_ = value;
}
inline void Message::set_from_support(bool value) {
  _internal_set_from_support(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.from_support)
}

// bool read = 5;
inline void Message::clear_read() {
  read_ = false;
}
inline bool Message::_internal_read() const {
  return read_;
}
inline bool Message::read() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.read)
  return _internal_read();
}
inline void Message::_internal_set_read(bool value) {
  
  read_ = value;
}
inline void Message::set_read(bool value) {
  _internal_set_read(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.read)
}

// string author_user_id = 6;
inline void Message::clear_author_user_id() {
  author_user_id_.ClearToEmpty();
}
inline const std::string& Message::author_user_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.author_user_id)
  return _internal_author_user_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Message::set_author_user_id(ArgT0&& arg0, ArgT... args) {
 
 author_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.author_user_id)
}
inline std::string* Message::mutable_author_user_id() {
  std::string* _s = _internal_mutable_author_user_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.Message.author_user_id)
  return _s;
}
inline const std::string& Message::_internal_author_user_id() const {
  return author_user_id_.Get();
}
inline void Message::_internal_set_author_user_id(const std::string& value) {
  
  author_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Message::_internal_mutable_author_user_id() {
  
  return author_user_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Message::release_author_user_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.Message.author_user_id)
  return author_user_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Message::set_allocated_author_user_id(std::string* author_user_id) {
  if (author_user_id != nullptr) {
    
  } else {
    
  }
  author_user_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), author_user_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (author_user_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    author_user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.Message.author_user_id)
}

// int64 author_robot_id = 7;
inline void Message::clear_author_robot_id() {
  author_robot_id_ = int64_t{0};
}
inline int64_t Message::_internal_author_robot_id() const {
  return author_robot_id_;
}
inline int64_t Message::author_robot_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.author_robot_id)
  return _internal_author_robot_id();
}
inline void Message::_internal_set_author_robot_id(int64_t value) {
  
  author_robot_id_ = value;
}
inline void Message::set_author_robot_id(int64_t value) {
  _internal_set_author_robot_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.author_robot_id)
}

// string recipient_user_id = 8;
inline void Message::clear_recipient_user_id() {
  recipient_user_id_.ClearToEmpty();
}
inline const std::string& Message::recipient_user_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.recipient_user_id)
  return _internal_recipient_user_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Message::set_recipient_user_id(ArgT0&& arg0, ArgT... args) {
 
 recipient_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.recipient_user_id)
}
inline std::string* Message::mutable_recipient_user_id() {
  std::string* _s = _internal_mutable_recipient_user_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.Message.recipient_user_id)
  return _s;
}
inline const std::string& Message::_internal_recipient_user_id() const {
  return recipient_user_id_.Get();
}
inline void Message::_internal_set_recipient_user_id(const std::string& value) {
  
  recipient_user_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Message::_internal_mutable_recipient_user_id() {
  
  return recipient_user_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Message::release_recipient_user_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.Message.recipient_user_id)
  return recipient_user_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Message::set_allocated_recipient_user_id(std::string* recipient_user_id) {
  if (recipient_user_id != nullptr) {
    
  } else {
    
  }
  recipient_user_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), recipient_user_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (recipient_user_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    recipient_user_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.Message.recipient_user_id)
}

// int64 recipient_customer_id = 9;
inline void Message::clear_recipient_customer_id() {
  recipient_customer_id_ = int64_t{0};
}
inline int64_t Message::_internal_recipient_customer_id() const {
  return recipient_customer_id_;
}
inline int64_t Message::recipient_customer_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.recipient_customer_id)
  return _internal_recipient_customer_id();
}
inline void Message::_internal_set_recipient_customer_id(int64_t value) {
  
  recipient_customer_id_ = value;
}
inline void Message::set_recipient_customer_id(int64_t value) {
  _internal_set_recipient_customer_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.recipient_customer_id)
}

// int64 recipient_robot_id = 10;
inline void Message::clear_recipient_robot_id() {
  recipient_robot_id_ = int64_t{0};
}
inline int64_t Message::_internal_recipient_robot_id() const {
  return recipient_robot_id_;
}
inline int64_t Message::recipient_robot_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.Message.recipient_robot_id)
  return _internal_recipient_robot_id();
}
inline void Message::_internal_set_recipient_robot_id(int64_t value) {
  
  recipient_robot_id_ = value;
}
inline void Message::set_recipient_robot_id(int64_t value) {
  _internal_set_recipient_robot_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.Message.recipient_robot_id)
}

// -------------------------------------------------------------------

// MessageRequest

// string message = 1;
inline void MessageRequest::clear_message() {
  message_.ClearToEmpty();
}
inline const std::string& MessageRequest::message() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.MessageRequest.message)
  return _internal_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MessageRequest::set_message(ArgT0&& arg0, ArgT... args) {
 
 message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.MessageRequest.message)
}
inline std::string* MessageRequest::mutable_message() {
  std::string* _s = _internal_mutable_message();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.MessageRequest.message)
  return _s;
}
inline const std::string& MessageRequest::_internal_message() const {
  return message_.Get();
}
inline void MessageRequest::_internal_set_message(const std::string& value) {
  
  message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MessageRequest::_internal_mutable_message() {
  
  return message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MessageRequest::release_message() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.MessageRequest.message)
  return message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MessageRequest::set_allocated_message(std::string* message) {
  if (message != nullptr) {
    
  } else {
    
  }
  message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.MessageRequest.message)
}

// -------------------------------------------------------------------

// MessagesRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool MessagesRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool MessagesRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& MessagesRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& MessagesRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.MessagesRequest.ts)
  return _internal_ts();
}
inline void MessagesRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.debug.MessagesRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* MessagesRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* MessagesRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.MessagesRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* MessagesRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* MessagesRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.MessagesRequest.ts)
  return _msg;
}
inline void MessagesRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.MessagesRequest.ts)
}

// -------------------------------------------------------------------

// MessagesResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool MessagesResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool MessagesResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& MessagesResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& MessagesResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.MessagesResponse.ts)
  return _internal_ts();
}
inline void MessagesResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.debug.MessagesResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* MessagesResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* MessagesResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.debug.MessagesResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* MessagesResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* MessagesResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.MessagesResponse.ts)
  return _msg;
}
inline void MessagesResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.debug.MessagesResponse.ts)
}

// repeated .carbon.frontend.debug.Message messages = 2;
inline int MessagesResponse::_internal_messages_size() const {
  return messages_.size();
}
inline int MessagesResponse::messages_size() const {
  return _internal_messages_size();
}
inline void MessagesResponse::clear_messages() {
  messages_.Clear();
}
inline ::carbon::frontend::debug::Message* MessagesResponse::mutable_messages(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.debug.MessagesResponse.messages)
  return messages_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::debug::Message >*
MessagesResponse::mutable_messages() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.debug.MessagesResponse.messages)
  return &messages_;
}
inline const ::carbon::frontend::debug::Message& MessagesResponse::_internal_messages(int index) const {
  return messages_.Get(index);
}
inline const ::carbon::frontend::debug::Message& MessagesResponse::messages(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.MessagesResponse.messages)
  return _internal_messages(index);
}
inline ::carbon::frontend::debug::Message* MessagesResponse::_internal_add_messages() {
  return messages_.Add();
}
inline ::carbon::frontend::debug::Message* MessagesResponse::add_messages() {
  ::carbon::frontend::debug::Message* _add = _internal_add_messages();
  // @@protoc_insertion_point(field_add:carbon.frontend.debug.MessagesResponse.messages)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::debug::Message >&
MessagesResponse::messages() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.debug.MessagesResponse.messages)
  return messages_;
}

// -------------------------------------------------------------------

// ReadRequest

// int64 id = 1;
inline void ReadRequest::clear_id() {
  id_ = int64_t{0};
}
inline int64_t ReadRequest::_internal_id() const {
  return id_;
}
inline int64_t ReadRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.debug.ReadRequest.id)
  return _internal_id();
}
inline void ReadRequest::_internal_set_id(int64_t value) {
  
  id_ = value;
}
inline void ReadRequest::set_id(int64_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.debug.ReadRequest.id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace debug
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmessages_2eproto

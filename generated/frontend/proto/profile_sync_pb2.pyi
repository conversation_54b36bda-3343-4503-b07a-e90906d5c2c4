"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    Optional as typing___Optional,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

ProfileTypeValue = typing___NewType('ProfileTypeValue', builtin___int)
type___ProfileTypeValue = ProfileTypeValue
ProfileType: _ProfileType
class _ProfileType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ProfileTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    ALMANAC = typing___cast(ProfileTypeValue, 0)
    DISCRIMINATOR = typing___cast(ProfileTypeValue, 1)
    MODELINATOR = typing___cast(ProfileTypeValue, 3)
    BANDING = typing___cast(ProfileTypeValue, 4)
    THINNING = typing___cast(ProfileTypeValue, 5)
    TARGET_VELOCITY_ESTIMATOR = typing___cast(ProfileTypeValue, 6)
    CATEGORY_COLLECTION = typing___cast(ProfileTypeValue, 7)
    CATEGORY = typing___cast(ProfileTypeValue, 8)
ALMANAC = typing___cast(ProfileTypeValue, 0)
DISCRIMINATOR = typing___cast(ProfileTypeValue, 1)
MODELINATOR = typing___cast(ProfileTypeValue, 3)
BANDING = typing___cast(ProfileTypeValue, 4)
THINNING = typing___cast(ProfileTypeValue, 5)
TARGET_VELOCITY_ESTIMATOR = typing___cast(ProfileTypeValue, 6)
CATEGORY_COLLECTION = typing___cast(ProfileTypeValue, 7)
CATEGORY = typing___cast(ProfileTypeValue, 8)

class ProfileSyncData(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    profile_type: type___ProfileTypeValue = ...
    last_updated_ts_ms: builtin___int = ...
    deleted: builtin___bool = ...
    protected: builtin___bool = ...

    def __init__(self,
        *,
        profile_type : typing___Optional[type___ProfileTypeValue] = None,
        last_updated_ts_ms : typing___Optional[builtin___int] = None,
        deleted : typing___Optional[builtin___bool] = None,
        protected : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"deleted",b"deleted",u"last_updated_ts_ms",b"last_updated_ts_ms",u"profile_type",b"profile_type",u"protected",b"protected"]) -> None: ...
type___ProfileSyncData = ProfileSyncData

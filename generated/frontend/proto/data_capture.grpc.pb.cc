// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/data_capture.proto

#include "frontend/proto/data_capture.pb.h"
#include "frontend/proto/data_capture.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace data_capture {

static const char* DataCaptureService_method_names[] = {
  "/carbon.frontend.data_capture.DataCaptureService/StartDataCapture",
  "/carbon.frontend.data_capture.DataCaptureService/PauseDataCapture",
  "/carbon.frontend.data_capture.DataCaptureService/StopDataCapture",
  "/carbon.frontend.data_capture.DataCaptureService/ResumeDataCapture",
  "/carbon.frontend.data_capture.DataCaptureService/CompleteDataCapture",
  "/carbon.frontend.data_capture.DataCaptureService/StartDataCaptureWirelessUpload",
  "/carbon.frontend.data_capture.DataCaptureService/StartDataCaptureUSBUpload",
  "/carbon.frontend.data_capture.DataCaptureService/StopDataCaptureUpload",
  "/carbon.frontend.data_capture.DataCaptureService/PauseDataCaptureUpload",
  "/carbon.frontend.data_capture.DataCaptureService/ResumeDataCaptureUpload",
  "/carbon.frontend.data_capture.DataCaptureService/StartBackgroundDataCaptureWirelessUpload",
  "/carbon.frontend.data_capture.DataCaptureService/StartBackgroundDataCaptureUSBUpload",
  "/carbon.frontend.data_capture.DataCaptureService/StopBackgroundDataCaptureUpload",
  "/carbon.frontend.data_capture.DataCaptureService/PauseBackgroundDataCaptureUpload",
  "/carbon.frontend.data_capture.DataCaptureService/ResumeBackgroundDataCaptureUpload",
  "/carbon.frontend.data_capture.DataCaptureService/GetNextDataCaptureState",
  "/carbon.frontend.data_capture.DataCaptureService/SnapImages",
  "/carbon.frontend.data_capture.DataCaptureService/GetSessions",
  "/carbon.frontend.data_capture.DataCaptureService/GetRegularCaptureStatus",
};

std::unique_ptr< DataCaptureService::Stub> DataCaptureService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< DataCaptureService::Stub> stub(new DataCaptureService::Stub(channel, options));
  return stub;
}

DataCaptureService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_StartDataCapture_(DataCaptureService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_PauseDataCapture_(DataCaptureService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopDataCapture_(DataCaptureService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResumeDataCapture_(DataCaptureService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CompleteDataCapture_(DataCaptureService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartDataCaptureWirelessUpload_(DataCaptureService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartDataCaptureUSBUpload_(DataCaptureService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopDataCaptureUpload_(DataCaptureService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_PauseDataCaptureUpload_(DataCaptureService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResumeDataCaptureUpload_(DataCaptureService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartBackgroundDataCaptureWirelessUpload_(DataCaptureService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartBackgroundDataCaptureUSBUpload_(DataCaptureService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopBackgroundDataCaptureUpload_(DataCaptureService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_PauseBackgroundDataCaptureUpload_(DataCaptureService_method_names[13], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResumeBackgroundDataCaptureUpload_(DataCaptureService_method_names[14], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextDataCaptureState_(DataCaptureService_method_names[15], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SnapImages_(DataCaptureService_method_names[16], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSessions_(DataCaptureService_method_names[17], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetRegularCaptureStatus_(DataCaptureService_method_names[18], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status DataCaptureService::Stub::StartDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::StartDataCaptureRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::data_capture::StartDataCaptureRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartDataCapture_, context, request, response);
}

void DataCaptureService::Stub::async::StartDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::StartDataCaptureRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::data_capture::StartDataCaptureRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartDataCapture_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::StartDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::StartDataCaptureRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartDataCapture_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncStartDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::StartDataCaptureRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::StartDataCaptureRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartDataCapture_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncStartDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::StartDataCaptureRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartDataCaptureRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::PauseDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_PauseDataCapture_, context, request, response);
}

void DataCaptureService::Stub::async::PauseDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PauseDataCapture_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::PauseDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PauseDataCapture_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncPauseDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_PauseDataCapture_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncPauseDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPauseDataCaptureRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::StopDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopDataCapture_, context, request, response);
}

void DataCaptureService::Stub::async::StopDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopDataCapture_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::StopDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopDataCapture_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncStopDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopDataCapture_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncStopDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopDataCaptureRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::ResumeDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResumeDataCapture_, context, request, response);
}

void DataCaptureService::Stub::async::ResumeDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResumeDataCapture_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::ResumeDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResumeDataCapture_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncResumeDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResumeDataCapture_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncResumeDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResumeDataCaptureRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::CompleteDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CompleteDataCapture_, context, request, response);
}

void DataCaptureService::Stub::async::CompleteDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CompleteDataCapture_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::CompleteDataCapture(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CompleteDataCapture_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncCompleteDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CompleteDataCapture_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncCompleteDataCaptureRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCompleteDataCaptureRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::StartDataCaptureWirelessUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartDataCaptureWirelessUpload_, context, request, response);
}

void DataCaptureService::Stub::async::StartDataCaptureWirelessUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartDataCaptureWirelessUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::StartDataCaptureWirelessUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartDataCaptureWirelessUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncStartDataCaptureWirelessUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartDataCaptureWirelessUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncStartDataCaptureWirelessUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartDataCaptureWirelessUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::StartDataCaptureUSBUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartDataCaptureUSBUpload_, context, request, response);
}

void DataCaptureService::Stub::async::StartDataCaptureUSBUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartDataCaptureUSBUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::StartDataCaptureUSBUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartDataCaptureUSBUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncStartDataCaptureUSBUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartDataCaptureUSBUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncStartDataCaptureUSBUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartDataCaptureUSBUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::StopDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopDataCaptureUpload_, context, request, response);
}

void DataCaptureService::Stub::async::StopDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopDataCaptureUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::StopDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopDataCaptureUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncStopDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopDataCaptureUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncStopDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopDataCaptureUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::PauseDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_PauseDataCaptureUpload_, context, request, response);
}

void DataCaptureService::Stub::async::PauseDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PauseDataCaptureUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::PauseDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PauseDataCaptureUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncPauseDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_PauseDataCaptureUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncPauseDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPauseDataCaptureUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::ResumeDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResumeDataCaptureUpload_, context, request, response);
}

void DataCaptureService::Stub::async::ResumeDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResumeDataCaptureUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::ResumeDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResumeDataCaptureUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncResumeDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResumeDataCaptureUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncResumeDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResumeDataCaptureUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::StartBackgroundDataCaptureWirelessUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartBackgroundDataCaptureWirelessUpload_, context, request, response);
}

void DataCaptureService::Stub::async::StartBackgroundDataCaptureWirelessUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartBackgroundDataCaptureWirelessUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::StartBackgroundDataCaptureWirelessUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartBackgroundDataCaptureWirelessUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncStartBackgroundDataCaptureWirelessUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::SessionName, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartBackgroundDataCaptureWirelessUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncStartBackgroundDataCaptureWirelessUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartBackgroundDataCaptureWirelessUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::StartBackgroundDataCaptureUSBUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartBackgroundDataCaptureUSBUpload_, context, request, response);
}

void DataCaptureService::Stub::async::StartBackgroundDataCaptureUSBUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartBackgroundDataCaptureUSBUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::StartBackgroundDataCaptureUSBUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartBackgroundDataCaptureUSBUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncStartBackgroundDataCaptureUSBUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::SessionName, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartBackgroundDataCaptureUSBUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncStartBackgroundDataCaptureUSBUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartBackgroundDataCaptureUSBUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::StopBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopBackgroundDataCaptureUpload_, context, request, response);
}

void DataCaptureService::Stub::async::StopBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopBackgroundDataCaptureUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::StopBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopBackgroundDataCaptureUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncStopBackgroundDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::SessionName, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopBackgroundDataCaptureUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncStopBackgroundDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopBackgroundDataCaptureUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::PauseBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_PauseBackgroundDataCaptureUpload_, context, request, response);
}

void DataCaptureService::Stub::async::PauseBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PauseBackgroundDataCaptureUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::PauseBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PauseBackgroundDataCaptureUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncPauseBackgroundDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::SessionName, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_PauseBackgroundDataCaptureUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncPauseBackgroundDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPauseBackgroundDataCaptureUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::ResumeBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResumeBackgroundDataCaptureUpload_, context, request, response);
}

void DataCaptureService::Stub::async::ResumeBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResumeBackgroundDataCaptureUpload_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::ResumeBackgroundDataCaptureUpload(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResumeBackgroundDataCaptureUpload_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncResumeBackgroundDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::SessionName, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResumeBackgroundDataCaptureUpload_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncResumeBackgroundDataCaptureUploadRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SessionName& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResumeBackgroundDataCaptureUploadRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::GetNextDataCaptureState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::data_capture::DataCaptureState* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::data_capture::DataCaptureState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextDataCaptureState_, context, request, response);
}

void DataCaptureService::Stub::async::GetNextDataCaptureState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::data_capture::DataCaptureState* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::data_capture::DataCaptureState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDataCaptureState_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::GetNextDataCaptureState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::data_capture::DataCaptureState* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextDataCaptureState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::data_capture::DataCaptureState>* DataCaptureService::Stub::PrepareAsyncGetNextDataCaptureStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::data_capture::DataCaptureState, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextDataCaptureState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::data_capture::DataCaptureState>* DataCaptureService::Stub::AsyncGetNextDataCaptureStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextDataCaptureStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::SnapImages(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SnapImagesRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::data_capture::SnapImagesRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SnapImages_, context, request, response);
}

void DataCaptureService::Stub::async::SnapImages(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SnapImagesRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::data_capture::SnapImagesRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SnapImages_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::SnapImages(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SnapImagesRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SnapImages_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::PrepareAsyncSnapImagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SnapImagesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::SnapImagesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SnapImages_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* DataCaptureService::Stub::AsyncSnapImagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::data_capture::SnapImagesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSnapImagesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::GetSessions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::data_capture::AvailableSessionResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::AvailableSessionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSessions_, context, request, response);
}

void DataCaptureService::Stub::async::GetSessions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::data_capture::AvailableSessionResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::AvailableSessionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSessions_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::GetSessions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::data_capture::AvailableSessionResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSessions_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::data_capture::AvailableSessionResponse>* DataCaptureService::Stub::PrepareAsyncGetSessionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::data_capture::AvailableSessionResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSessions_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::data_capture::AvailableSessionResponse>* DataCaptureService::Stub::AsyncGetSessionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSessionsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status DataCaptureService::Stub::GetRegularCaptureStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::data_capture::RegularCaptureStatus* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::RegularCaptureStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetRegularCaptureStatus_, context, request, response);
}

void DataCaptureService::Stub::async::GetRegularCaptureStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::data_capture::RegularCaptureStatus* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::RegularCaptureStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRegularCaptureStatus_, context, request, response, std::move(f));
}

void DataCaptureService::Stub::async::GetRegularCaptureStatus(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::data_capture::RegularCaptureStatus* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetRegularCaptureStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::data_capture::RegularCaptureStatus>* DataCaptureService::Stub::PrepareAsyncGetRegularCaptureStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::data_capture::RegularCaptureStatus, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetRegularCaptureStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::data_capture::RegularCaptureStatus>* DataCaptureService::Stub::AsyncGetRegularCaptureStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetRegularCaptureStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

DataCaptureService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::data_capture::StartDataCaptureRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::data_capture::StartDataCaptureRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartDataCapture(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->PauseDataCapture(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StopDataCapture(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ResumeDataCapture(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->CompleteDataCapture(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartDataCaptureWirelessUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartDataCaptureUSBUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StopDataCaptureUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->PauseDataCaptureUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ResumeDataCaptureUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::data_capture::SessionName* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartBackgroundDataCaptureWirelessUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::data_capture::SessionName* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartBackgroundDataCaptureUSBUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::data_capture::SessionName* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StopBackgroundDataCaptureUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[13],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::data_capture::SessionName* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->PauseBackgroundDataCaptureUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[14],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::data_capture::SessionName, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::data_capture::SessionName* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ResumeBackgroundDataCaptureUpload(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[15],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::data_capture::DataCaptureState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::data_capture::DataCaptureState* resp) {
               return service->GetNextDataCaptureState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[16],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::data_capture::SnapImagesRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::data_capture::SnapImagesRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SnapImages(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[17],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::AvailableSessionResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::data_capture::AvailableSessionResponse* resp) {
               return service->GetSessions(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      DataCaptureService_method_names[18],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< DataCaptureService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::data_capture::RegularCaptureStatus, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](DataCaptureService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::data_capture::RegularCaptureStatus* resp) {
               return service->GetRegularCaptureStatus(ctx, req, resp);
             }, this)));
}

DataCaptureService::Service::~Service() {
}

::grpc::Status DataCaptureService::Service::StartDataCapture(::grpc::ServerContext* context, const ::carbon::frontend::data_capture::StartDataCaptureRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::PauseDataCapture(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::StopDataCapture(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::ResumeDataCapture(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::CompleteDataCapture(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::StartDataCaptureWirelessUpload(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::StartDataCaptureUSBUpload(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::StopDataCaptureUpload(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::PauseDataCaptureUpload(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::ResumeDataCaptureUpload(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::StartBackgroundDataCaptureWirelessUpload(::grpc::ServerContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::StartBackgroundDataCaptureUSBUpload(::grpc::ServerContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::StopBackgroundDataCaptureUpload(::grpc::ServerContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::PauseBackgroundDataCaptureUpload(::grpc::ServerContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::ResumeBackgroundDataCaptureUpload(::grpc::ServerContext* context, const ::carbon::frontend::data_capture::SessionName* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::GetNextDataCaptureState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::data_capture::DataCaptureState* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::SnapImages(::grpc::ServerContext* context, const ::carbon::frontend::data_capture::SnapImagesRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::GetSessions(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::data_capture::AvailableSessionResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status DataCaptureService::Service::GetRegularCaptureStatus(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::data_capture::RegularCaptureStatus* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace data_capture


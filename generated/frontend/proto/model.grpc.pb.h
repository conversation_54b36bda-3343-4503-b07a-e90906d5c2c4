// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/model.proto
#ifndef GRPC_frontend_2fproto_2fmodel_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fmodel_2eproto__INCLUDED

#include "frontend/proto/model.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace model {

class ModelService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.model.ModelService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncPinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncPinModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncPinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncPinModelRaw(context, request, cq));
    }
    virtual ::grpc::Status UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncUnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncUnpinModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncUnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncUnpinModelRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::carbon::frontend::model::GetNextModelStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>> AsyncGetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>>(AsyncGetNextModelStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>> PrepareAsyncGetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>>(PrepareAsyncGetNextModelStateRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::carbon::frontend::model::GetNextModelStateResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>> AsyncGetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>>(AsyncGetNextAllModelStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>> PrepareAsyncGetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>>(PrepareAsyncGetNextAllModelStateRaw(context, request, cq));
    }
    virtual ::grpc::Status UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncUpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncUpdateModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncUpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncUpdateModelRaw(context, request, cq));
    }
    virtual ::grpc::Status ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::carbon::frontend::model::EnabledCropList* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>> AsyncListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>>(AsyncListEnabledCropsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>> PrepareAsyncListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>>(PrepareAsyncListEnabledCropsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextEnabledCropsResponse>> AsyncGetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextEnabledCropsResponse>>(AsyncGetNextEnabledCropsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextEnabledCropsResponse>> PrepareAsyncGetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextEnabledCropsResponse>>(PrepareAsyncGetNextEnabledCropsRaw(context, request, cq));
    }
    virtual ::grpc::Status ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::carbon::frontend::model::EnabledCropList* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>> AsyncListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>>(AsyncListCaptureCropsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>> PrepareAsyncListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>>(PrepareAsyncListCaptureCropsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextCaptureCropsResponse>> AsyncGetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextCaptureCropsResponse>>(AsyncGetNextCaptureCropsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextCaptureCropsResponse>> PrepareAsyncGetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextCaptureCropsResponse>>(PrepareAsyncGetNextCaptureCropsRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextSelectedCropIDResponse>> AsyncGetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextSelectedCropIDResponse>>(AsyncGetNextSelectedCropIDRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextSelectedCropIDResponse>> PrepareAsyncGetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextSelectedCropIDResponse>>(PrepareAsyncGetNextSelectedCropIDRaw(context, request, cq));
    }
    virtual ::grpc::Status SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSelectCropRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSelectCropRaw(context, request, cq));
    }
    virtual ::grpc::Status DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDownloadModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDownloadModelRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::carbon::frontend::model::ModelHistoryResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>> AsyncGetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>>(AsyncGetNextModelHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>> PrepareAsyncGetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>>(PrepareAsyncGetNextModelHistoryRaw(context, request, cq));
    }
    virtual ::grpc::Status GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::carbon::frontend::model::ModelHistoryResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>> AsyncGetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>>(AsyncGetModelHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>> PrepareAsyncGetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>>(PrepareAsyncGetModelHistoryRaw(context, request, cq));
    }
    virtual ::grpc::Status GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::carbon::frontend::model::GetModelNicknamesResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>> AsyncGetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>>(AsyncGetModelNicknamesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>> PrepareAsyncGetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>>(PrepareAsyncGetModelNicknamesRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::carbon::frontend::model::GetModelNicknamesResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>> AsyncGetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>>(AsyncGetNextModelNicknamesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>> PrepareAsyncGetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>>(PrepareAsyncGetNextModelNicknamesRaw(context, request, cq));
    }
    virtual ::grpc::Status SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetModelNicknameRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetModelNicknameRaw(context, request, cq));
    }
    virtual ::grpc::Status RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncRefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncRefreshDefaultModelParametersRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncRefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncRefreshDefaultModelParametersRaw(context, request, cq));
    }
    virtual ::grpc::Status SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSyncCropIDsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSyncCropIDsRaw(context, request, cq));
    }
    virtual ::grpc::Status TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncTriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncTriggerDownloadRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncTriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncTriggerDownloadRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncPinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncPinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncUnpinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncUnpinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>* AsyncGetNextModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>* PrepareAsyncGetNextModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>* AsyncGetNextAllModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextModelStateResponse>* PrepareAsyncGetNextAllModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncUpdateModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncUpdateModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>* AsyncListEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>* PrepareAsyncListEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextEnabledCropsResponse>* AsyncGetNextEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextEnabledCropsResponse>* PrepareAsyncGetNextEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>* AsyncListCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::EnabledCropList>* PrepareAsyncListCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextCaptureCropsResponse>* AsyncGetNextCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextCaptureCropsResponse>* PrepareAsyncGetNextCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextSelectedCropIDResponse>* AsyncGetNextSelectedCropIDRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetNextSelectedCropIDResponse>* PrepareAsyncGetNextSelectedCropIDRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSelectCropRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSelectCropRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDownloadModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDownloadModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>* AsyncGetNextModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>* PrepareAsyncGetNextModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>* AsyncGetModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::ModelHistoryResponse>* PrepareAsyncGetModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>* AsyncGetModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>* PrepareAsyncGetModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>* AsyncGetNextModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::model::GetModelNicknamesResponse>* PrepareAsyncGetNextModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetModelNicknameRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetModelNicknameRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncRefreshDefaultModelParametersRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncRefreshDefaultModelParametersRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSyncCropIDsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSyncCropIDsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncTriggerDownloadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncTriggerDownloadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncPinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncPinModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncPinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncPinModelRaw(context, request, cq));
    }
    ::grpc::Status UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncUnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncUnpinModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncUnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncUnpinModelRaw(context, request, cq));
    }
    ::grpc::Status GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::carbon::frontend::model::GetNextModelStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>> AsyncGetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>>(AsyncGetNextModelStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>> PrepareAsyncGetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>>(PrepareAsyncGetNextModelStateRaw(context, request, cq));
    }
    ::grpc::Status GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::carbon::frontend::model::GetNextModelStateResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>> AsyncGetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>>(AsyncGetNextAllModelStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>> PrepareAsyncGetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>>(PrepareAsyncGetNextAllModelStateRaw(context, request, cq));
    }
    ::grpc::Status UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncUpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncUpdateModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncUpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncUpdateModelRaw(context, request, cq));
    }
    ::grpc::Status ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::carbon::frontend::model::EnabledCropList* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>> AsyncListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>>(AsyncListEnabledCropsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>> PrepareAsyncListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>>(PrepareAsyncListEnabledCropsRaw(context, request, cq));
    }
    ::grpc::Status GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextEnabledCropsResponse>> AsyncGetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextEnabledCropsResponse>>(AsyncGetNextEnabledCropsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextEnabledCropsResponse>> PrepareAsyncGetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextEnabledCropsResponse>>(PrepareAsyncGetNextEnabledCropsRaw(context, request, cq));
    }
    ::grpc::Status ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::carbon::frontend::model::EnabledCropList* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>> AsyncListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>>(AsyncListCaptureCropsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>> PrepareAsyncListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>>(PrepareAsyncListCaptureCropsRaw(context, request, cq));
    }
    ::grpc::Status GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextCaptureCropsResponse>> AsyncGetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextCaptureCropsResponse>>(AsyncGetNextCaptureCropsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextCaptureCropsResponse>> PrepareAsyncGetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextCaptureCropsResponse>>(PrepareAsyncGetNextCaptureCropsRaw(context, request, cq));
    }
    ::grpc::Status GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextSelectedCropIDResponse>> AsyncGetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextSelectedCropIDResponse>>(AsyncGetNextSelectedCropIDRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextSelectedCropIDResponse>> PrepareAsyncGetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextSelectedCropIDResponse>>(PrepareAsyncGetNextSelectedCropIDRaw(context, request, cq));
    }
    ::grpc::Status SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSelectCropRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSelectCropRaw(context, request, cq));
    }
    ::grpc::Status DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDownloadModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDownloadModelRaw(context, request, cq));
    }
    ::grpc::Status GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::carbon::frontend::model::ModelHistoryResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>> AsyncGetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>>(AsyncGetNextModelHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>> PrepareAsyncGetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>>(PrepareAsyncGetNextModelHistoryRaw(context, request, cq));
    }
    ::grpc::Status GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::carbon::frontend::model::ModelHistoryResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>> AsyncGetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>>(AsyncGetModelHistoryRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>> PrepareAsyncGetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>>(PrepareAsyncGetModelHistoryRaw(context, request, cq));
    }
    ::grpc::Status GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::carbon::frontend::model::GetModelNicknamesResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>> AsyncGetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>>(AsyncGetModelNicknamesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>> PrepareAsyncGetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>>(PrepareAsyncGetModelNicknamesRaw(context, request, cq));
    }
    ::grpc::Status GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::carbon::frontend::model::GetModelNicknamesResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>> AsyncGetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>>(AsyncGetNextModelNicknamesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>> PrepareAsyncGetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>>(PrepareAsyncGetNextModelNicknamesRaw(context, request, cq));
    }
    ::grpc::Status SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetModelNicknameRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetModelNicknameRaw(context, request, cq));
    }
    ::grpc::Status RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncRefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncRefreshDefaultModelParametersRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncRefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncRefreshDefaultModelParametersRaw(context, request, cq));
    }
    ::grpc::Status SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSyncCropIDsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSyncCropIDsRaw(context, request, cq));
    }
    ::grpc::Status TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncTriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncTriggerDownloadRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncTriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncTriggerDownloadRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void PinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UnpinModel(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextAllModelState(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void UpdateModel(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, std::function<void(::grpc::Status)>) override;
      void ListEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextEnabledCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, std::function<void(::grpc::Status)>) override;
      void ListCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextCaptureCrops(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextSelectedCropID(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SelectCrop(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DownloadModel(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, std::function<void(::grpc::Status)>) override;
      void GetModelHistory(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, std::function<void(::grpc::Status)>) override;
      void GetModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextModelNicknames(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetModelNickname(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void RefreshDefaultModelParameters(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SyncCropIDs(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void TriggerDownload(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncPinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncPinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::PinModelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncUnpinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncUnpinModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::UnpinModelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>* AsyncGetNextModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>* PrepareAsyncGetNextModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>* AsyncGetNextAllModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextModelStateResponse>* PrepareAsyncGetNextAllModelStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextModelStateRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncUpdateModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncUpdateModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>* AsyncListEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>* PrepareAsyncListEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextEnabledCropsResponse>* AsyncGetNextEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextEnabledCropsResponse>* PrepareAsyncGetNextEnabledCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>* AsyncListCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::EnabledCropList>* PrepareAsyncListCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ListCropParameters& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextCaptureCropsResponse>* AsyncGetNextCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextCaptureCropsResponse>* PrepareAsyncGetNextCaptureCropsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextSelectedCropIDResponse>* AsyncGetNextSelectedCropIDRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetNextSelectedCropIDResponse>* PrepareAsyncGetNextSelectedCropIDRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSelectCropRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSelectCropRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SelectCropRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDownloadModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDownloadModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::DownloadModelRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>* AsyncGetNextModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>* PrepareAsyncGetNextModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>* AsyncGetModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::ModelHistoryResponse>* PrepareAsyncGetModelHistoryRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::ModelHistoryRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>* AsyncGetModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>* PrepareAsyncGetModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>* AsyncGetNextModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::model::GetModelNicknamesResponse>* PrepareAsyncGetNextModelNicknamesRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetModelNicknameRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetModelNicknameRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SetModelNicknameRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncRefreshDefaultModelParametersRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncRefreshDefaultModelParametersRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSyncCropIDsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSyncCropIDsRaw(::grpc::ClientContext* context, const ::carbon::frontend::model::SyncCropIDsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncTriggerDownloadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncTriggerDownloadRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_PinModel_;
    const ::grpc::internal::RpcMethod rpcmethod_UnpinModel_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextModelState_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextAllModelState_;
    const ::grpc::internal::RpcMethod rpcmethod_UpdateModel_;
    const ::grpc::internal::RpcMethod rpcmethod_ListEnabledCrops_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextEnabledCrops_;
    const ::grpc::internal::RpcMethod rpcmethod_ListCaptureCrops_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextCaptureCrops_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextSelectedCropID_;
    const ::grpc::internal::RpcMethod rpcmethod_SelectCrop_;
    const ::grpc::internal::RpcMethod rpcmethod_DownloadModel_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextModelHistory_;
    const ::grpc::internal::RpcMethod rpcmethod_GetModelHistory_;
    const ::grpc::internal::RpcMethod rpcmethod_GetModelNicknames_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextModelNicknames_;
    const ::grpc::internal::RpcMethod rpcmethod_SetModelNickname_;
    const ::grpc::internal::RpcMethod rpcmethod_RefreshDefaultModelParameters_;
    const ::grpc::internal::RpcMethod rpcmethod_SyncCropIDs_;
    const ::grpc::internal::RpcMethod rpcmethod_TriggerDownload_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status PinModel(::grpc::ServerContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status UnpinModel(::grpc::ServerContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextModelState(::grpc::ServerContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response);
    virtual ::grpc::Status GetNextAllModelState(::grpc::ServerContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response);
    virtual ::grpc::Status UpdateModel(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status ListEnabledCrops(::grpc::ServerContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response);
    virtual ::grpc::Status GetNextEnabledCrops(::grpc::ServerContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response);
    virtual ::grpc::Status ListCaptureCrops(::grpc::ServerContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response);
    virtual ::grpc::Status GetNextCaptureCrops(::grpc::ServerContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response);
    virtual ::grpc::Status GetNextSelectedCropID(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response);
    virtual ::grpc::Status SelectCrop(::grpc::ServerContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status DownloadModel(::grpc::ServerContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextModelHistory(::grpc::ServerContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response);
    virtual ::grpc::Status GetModelHistory(::grpc::ServerContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response);
    virtual ::grpc::Status GetModelNicknames(::grpc::ServerContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response);
    virtual ::grpc::Status GetNextModelNicknames(::grpc::ServerContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response);
    virtual ::grpc::Status SetModelNickname(::grpc::ServerContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status RefreshDefaultModelParameters(::grpc::ServerContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status SyncCropIDs(::grpc::ServerContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status TriggerDownload(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_PinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_PinModel() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_PinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::PinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPinModel(::grpc::ServerContext* context, ::carbon::frontend::model::PinModelRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UnpinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UnpinModel() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_UnpinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnpinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::UnpinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnpinModel(::grpc::ServerContext* context, ::carbon::frontend::model::UnpinModelRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextModelState() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetNextModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModelState(::grpc::ServerContext* context, ::carbon::frontend::model::GetNextModelStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::GetNextModelStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextAllModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextAllModelState() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GetNextAllModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAllModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAllModelState(::grpc::ServerContext* context, ::carbon::frontend::model::GetNextModelStateRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::GetNextModelStateResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UpdateModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UpdateModel() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_UpdateModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateModel(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ListEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ListEnabledCrops() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_ListEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestListEnabledCrops(::grpc::ServerContext* context, ::carbon::frontend::model::ListCropParameters* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::EnabledCropList>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextEnabledCrops() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_GetNextEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextEnabledCropsRequest* /*request*/, ::carbon::frontend::model::GetNextEnabledCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextEnabledCrops(::grpc::ServerContext* context, ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::GetNextEnabledCropsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ListCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ListCaptureCrops() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_ListCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestListCaptureCrops(::grpc::ServerContext* context, ::carbon::frontend::model::ListCropParameters* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::EnabledCropList>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextCaptureCrops() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_GetNextCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextCaptureCropsRequest* /*request*/, ::carbon::frontend::model::GetNextCaptureCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCaptureCrops(::grpc::ServerContext* context, ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::GetNextCaptureCropsResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextSelectedCropID : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextSelectedCropID() {
      ::grpc::Service::MarkMethodAsync(9);
    }
    ~WithAsyncMethod_GetNextSelectedCropID() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSelectedCropID(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::model::GetNextSelectedCropIDResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextSelectedCropID(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::GetNextSelectedCropIDResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SelectCrop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SelectCrop() {
      ::grpc::Service::MarkMethodAsync(10);
    }
    ~WithAsyncMethod_SelectCrop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SelectCrop(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SelectCropRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSelectCrop(::grpc::ServerContext* context, ::carbon::frontend::model::SelectCropRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DownloadModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DownloadModel() {
      ::grpc::Service::MarkMethodAsync(11);
    }
    ~WithAsyncMethod_DownloadModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DownloadModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::DownloadModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDownloadModel(::grpc::ServerContext* context, ::carbon::frontend::model::DownloadModelRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextModelHistory() {
      ::grpc::Service::MarkMethodAsync(12);
    }
    ~WithAsyncMethod_GetNextModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModelHistory(::grpc::ServerContext* context, ::carbon::frontend::model::ModelHistoryRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::ModelHistoryResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetModelHistory() {
      ::grpc::Service::MarkMethodAsync(13);
    }
    ~WithAsyncMethod_GetModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetModelHistory(::grpc::ServerContext* context, ::carbon::frontend::model::ModelHistoryRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::ModelHistoryResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetModelNicknames() {
      ::grpc::Service::MarkMethodAsync(14);
    }
    ~WithAsyncMethod_GetModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetModelNicknames(::grpc::ServerContext* context, ::carbon::frontend::model::GetModelNicknamesRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::GetModelNicknamesResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextModelNicknames() {
      ::grpc::Service::MarkMethodAsync(15);
    }
    ~WithAsyncMethod_GetNextModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModelNicknames(::grpc::ServerContext* context, ::carbon::frontend::model::GetModelNicknamesRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::model::GetModelNicknamesResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetModelNickname : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetModelNickname() {
      ::grpc::Service::MarkMethodAsync(16);
    }
    ~WithAsyncMethod_SetModelNickname() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModelNickname(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SetModelNicknameRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetModelNickname(::grpc::ServerContext* context, ::carbon::frontend::model::SetModelNicknameRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(16, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_RefreshDefaultModelParameters : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RefreshDefaultModelParameters() {
      ::grpc::Service::MarkMethodAsync(17);
    }
    ~WithAsyncMethod_RefreshDefaultModelParameters() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RefreshDefaultModelParameters(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRefreshDefaultModelParameters(::grpc::ServerContext* context, ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(17, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SyncCropIDs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SyncCropIDs() {
      ::grpc::Service::MarkMethodAsync(18);
    }
    ~WithAsyncMethod_SyncCropIDs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SyncCropIDs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SyncCropIDsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSyncCropIDs(::grpc::ServerContext* context, ::carbon::frontend::model::SyncCropIDsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(18, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_TriggerDownload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_TriggerDownload() {
      ::grpc::Service::MarkMethodAsync(19);
    }
    ~WithAsyncMethod_TriggerDownload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TriggerDownload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTriggerDownload(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(19, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_PinModel<WithAsyncMethod_UnpinModel<WithAsyncMethod_GetNextModelState<WithAsyncMethod_GetNextAllModelState<WithAsyncMethod_UpdateModel<WithAsyncMethod_ListEnabledCrops<WithAsyncMethod_GetNextEnabledCrops<WithAsyncMethod_ListCaptureCrops<WithAsyncMethod_GetNextCaptureCrops<WithAsyncMethod_GetNextSelectedCropID<WithAsyncMethod_SelectCrop<WithAsyncMethod_DownloadModel<WithAsyncMethod_GetNextModelHistory<WithAsyncMethod_GetModelHistory<WithAsyncMethod_GetModelNicknames<WithAsyncMethod_GetNextModelNicknames<WithAsyncMethod_SetModelNickname<WithAsyncMethod_RefreshDefaultModelParameters<WithAsyncMethod_SyncCropIDs<WithAsyncMethod_TriggerDownload<Service > > > > > > > > > > > > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_PinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_PinModel() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::PinModelRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::PinModelRequest* request, ::carbon::frontend::util::Empty* response) { return this->PinModel(context, request, response); }));}
    void SetMessageAllocatorFor_PinModel(
        ::grpc::MessageAllocator< ::carbon::frontend::model::PinModelRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::PinModelRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_PinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::PinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* PinModel(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::PinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UnpinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UnpinModel() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::UnpinModelRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::UnpinModelRequest* request, ::carbon::frontend::util::Empty* response) { return this->UnpinModel(context, request, response); }));}
    void SetMessageAllocatorFor_UnpinModel(
        ::grpc::MessageAllocator< ::carbon::frontend::model::UnpinModelRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::UnpinModelRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UnpinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnpinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::UnpinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UnpinModel(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::UnpinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextModelState() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response) { return this->GetNextModelState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextModelState(
        ::grpc::MessageAllocator< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModelState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextAllModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextAllModelState() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::GetNextModelStateRequest* request, ::carbon::frontend::model::GetNextModelStateResponse* response) { return this->GetNextAllModelState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextAllModelState(
        ::grpc::MessageAllocator< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextAllModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAllModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAllModelState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UpdateModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UpdateModel() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->UpdateModel(context, request, response); }));}
    void SetMessageAllocatorFor_UpdateModel(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UpdateModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateModel(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ListEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ListEnabledCrops() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response) { return this->ListEnabledCrops(context, request, response); }));}
    void SetMessageAllocatorFor_ListEnabledCrops(
        ::grpc::MessageAllocator< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ListEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ListEnabledCrops(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextEnabledCrops() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetNextEnabledCropsRequest, ::carbon::frontend::model::GetNextEnabledCropsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::GetNextEnabledCropsRequest* request, ::carbon::frontend::model::GetNextEnabledCropsResponse* response) { return this->GetNextEnabledCrops(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextEnabledCrops(
        ::grpc::MessageAllocator< ::carbon::frontend::model::GetNextEnabledCropsRequest, ::carbon::frontend::model::GetNextEnabledCropsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetNextEnabledCropsRequest, ::carbon::frontend::model::GetNextEnabledCropsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextEnabledCropsRequest* /*request*/, ::carbon::frontend::model::GetNextEnabledCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextEnabledCrops(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::GetNextEnabledCropsRequest* /*request*/, ::carbon::frontend::model::GetNextEnabledCropsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ListCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ListCaptureCrops() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::ListCropParameters* request, ::carbon::frontend::model::EnabledCropList* response) { return this->ListCaptureCrops(context, request, response); }));}
    void SetMessageAllocatorFor_ListCaptureCrops(
        ::grpc::MessageAllocator< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ListCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ListCaptureCrops(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextCaptureCrops() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetNextCaptureCropsRequest, ::carbon::frontend::model::GetNextCaptureCropsResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::GetNextCaptureCropsRequest* request, ::carbon::frontend::model::GetNextCaptureCropsResponse* response) { return this->GetNextCaptureCrops(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextCaptureCrops(
        ::grpc::MessageAllocator< ::carbon::frontend::model::GetNextCaptureCropsRequest, ::carbon::frontend::model::GetNextCaptureCropsResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetNextCaptureCropsRequest, ::carbon::frontend::model::GetNextCaptureCropsResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextCaptureCropsRequest* /*request*/, ::carbon::frontend::model::GetNextCaptureCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCaptureCrops(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::GetNextCaptureCropsRequest* /*request*/, ::carbon::frontend::model::GetNextCaptureCropsResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextSelectedCropID : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextSelectedCropID() {
      ::grpc::Service::MarkMethodCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::model::GetNextSelectedCropIDResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::model::GetNextSelectedCropIDResponse* response) { return this->GetNextSelectedCropID(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextSelectedCropID(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::model::GetNextSelectedCropIDResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(9);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::model::GetNextSelectedCropIDResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextSelectedCropID() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSelectedCropID(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::model::GetNextSelectedCropIDResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextSelectedCropID(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::model::GetNextSelectedCropIDResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SelectCrop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SelectCrop() {
      ::grpc::Service::MarkMethodCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::SelectCropRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::SelectCropRequest* request, ::carbon::frontend::util::Empty* response) { return this->SelectCrop(context, request, response); }));}
    void SetMessageAllocatorFor_SelectCrop(
        ::grpc::MessageAllocator< ::carbon::frontend::model::SelectCropRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(10);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::SelectCropRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SelectCrop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SelectCrop(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SelectCropRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SelectCrop(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::SelectCropRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DownloadModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DownloadModel() {
      ::grpc::Service::MarkMethodCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::DownloadModelRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::DownloadModelRequest* request, ::carbon::frontend::util::Empty* response) { return this->DownloadModel(context, request, response); }));}
    void SetMessageAllocatorFor_DownloadModel(
        ::grpc::MessageAllocator< ::carbon::frontend::model::DownloadModelRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(11);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::DownloadModelRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DownloadModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DownloadModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::DownloadModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DownloadModel(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::DownloadModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextModelHistory() {
      ::grpc::Service::MarkMethodCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response) { return this->GetNextModelHistory(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextModelHistory(
        ::grpc::MessageAllocator< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(12);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModelHistory(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetModelHistory() {
      ::grpc::Service::MarkMethodCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::ModelHistoryRequest* request, ::carbon::frontend::model::ModelHistoryResponse* response) { return this->GetModelHistory(context, request, response); }));}
    void SetMessageAllocatorFor_GetModelHistory(
        ::grpc::MessageAllocator< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(13);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetModelHistory(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetModelNicknames() {
      ::grpc::Service::MarkMethodCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response) { return this->GetModelNicknames(context, request, response); }));}
    void SetMessageAllocatorFor_GetModelNicknames(
        ::grpc::MessageAllocator< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(14);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetModelNicknames(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextModelNicknames() {
      ::grpc::Service::MarkMethodCallback(15,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::GetModelNicknamesRequest* request, ::carbon::frontend::model::GetModelNicknamesResponse* response) { return this->GetNextModelNicknames(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextModelNicknames(
        ::grpc::MessageAllocator< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(15);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModelNicknames(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetModelNickname : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetModelNickname() {
      ::grpc::Service::MarkMethodCallback(16,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::SetModelNicknameRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::SetModelNicknameRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetModelNickname(context, request, response); }));}
    void SetMessageAllocatorFor_SetModelNickname(
        ::grpc::MessageAllocator< ::carbon::frontend::model::SetModelNicknameRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(16);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::SetModelNicknameRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetModelNickname() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModelNickname(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SetModelNicknameRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetModelNickname(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::SetModelNicknameRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_RefreshDefaultModelParameters : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_RefreshDefaultModelParameters() {
      ::grpc::Service::MarkMethodCallback(17,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* request, ::carbon::frontend::util::Empty* response) { return this->RefreshDefaultModelParameters(context, request, response); }));}
    void SetMessageAllocatorFor_RefreshDefaultModelParameters(
        ::grpc::MessageAllocator< ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(17);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_RefreshDefaultModelParameters() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RefreshDefaultModelParameters(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RefreshDefaultModelParameters(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SyncCropIDs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SyncCropIDs() {
      ::grpc::Service::MarkMethodCallback(18,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::SyncCropIDsRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::model::SyncCropIDsRequest* request, ::carbon::frontend::util::Empty* response) { return this->SyncCropIDs(context, request, response); }));}
    void SetMessageAllocatorFor_SyncCropIDs(
        ::grpc::MessageAllocator< ::carbon::frontend::model::SyncCropIDsRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(18);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::model::SyncCropIDsRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SyncCropIDs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SyncCropIDs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SyncCropIDsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SyncCropIDs(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::model::SyncCropIDsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_TriggerDownload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_TriggerDownload() {
      ::grpc::Service::MarkMethodCallback(19,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->TriggerDownload(context, request, response); }));}
    void SetMessageAllocatorFor_TriggerDownload(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(19);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_TriggerDownload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TriggerDownload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* TriggerDownload(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_PinModel<WithCallbackMethod_UnpinModel<WithCallbackMethod_GetNextModelState<WithCallbackMethod_GetNextAllModelState<WithCallbackMethod_UpdateModel<WithCallbackMethod_ListEnabledCrops<WithCallbackMethod_GetNextEnabledCrops<WithCallbackMethod_ListCaptureCrops<WithCallbackMethod_GetNextCaptureCrops<WithCallbackMethod_GetNextSelectedCropID<WithCallbackMethod_SelectCrop<WithCallbackMethod_DownloadModel<WithCallbackMethod_GetNextModelHistory<WithCallbackMethod_GetModelHistory<WithCallbackMethod_GetModelNicknames<WithCallbackMethod_GetNextModelNicknames<WithCallbackMethod_SetModelNickname<WithCallbackMethod_RefreshDefaultModelParameters<WithCallbackMethod_SyncCropIDs<WithCallbackMethod_TriggerDownload<Service > > > > > > > > > > > > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_PinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_PinModel() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_PinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::PinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UnpinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UnpinModel() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_UnpinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnpinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::UnpinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextModelState() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetNextModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextAllModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextAllModelState() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GetNextAllModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAllModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UpdateModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UpdateModel() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_UpdateModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ListEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ListEnabledCrops() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_ListEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextEnabledCrops() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_GetNextEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextEnabledCropsRequest* /*request*/, ::carbon::frontend::model::GetNextEnabledCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ListCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ListCaptureCrops() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_ListCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextCaptureCrops() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_GetNextCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextCaptureCropsRequest* /*request*/, ::carbon::frontend::model::GetNextCaptureCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextSelectedCropID : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextSelectedCropID() {
      ::grpc::Service::MarkMethodGeneric(9);
    }
    ~WithGenericMethod_GetNextSelectedCropID() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSelectedCropID(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::model::GetNextSelectedCropIDResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SelectCrop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SelectCrop() {
      ::grpc::Service::MarkMethodGeneric(10);
    }
    ~WithGenericMethod_SelectCrop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SelectCrop(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SelectCropRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DownloadModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DownloadModel() {
      ::grpc::Service::MarkMethodGeneric(11);
    }
    ~WithGenericMethod_DownloadModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DownloadModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::DownloadModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextModelHistory() {
      ::grpc::Service::MarkMethodGeneric(12);
    }
    ~WithGenericMethod_GetNextModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetModelHistory() {
      ::grpc::Service::MarkMethodGeneric(13);
    }
    ~WithGenericMethod_GetModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetModelNicknames() {
      ::grpc::Service::MarkMethodGeneric(14);
    }
    ~WithGenericMethod_GetModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextModelNicknames() {
      ::grpc::Service::MarkMethodGeneric(15);
    }
    ~WithGenericMethod_GetNextModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetModelNickname : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetModelNickname() {
      ::grpc::Service::MarkMethodGeneric(16);
    }
    ~WithGenericMethod_SetModelNickname() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModelNickname(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SetModelNicknameRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_RefreshDefaultModelParameters : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RefreshDefaultModelParameters() {
      ::grpc::Service::MarkMethodGeneric(17);
    }
    ~WithGenericMethod_RefreshDefaultModelParameters() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RefreshDefaultModelParameters(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SyncCropIDs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SyncCropIDs() {
      ::grpc::Service::MarkMethodGeneric(18);
    }
    ~WithGenericMethod_SyncCropIDs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SyncCropIDs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SyncCropIDsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_TriggerDownload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_TriggerDownload() {
      ::grpc::Service::MarkMethodGeneric(19);
    }
    ~WithGenericMethod_TriggerDownload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TriggerDownload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_PinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_PinModel() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_PinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::PinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPinModel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UnpinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UnpinModel() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_UnpinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnpinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::UnpinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUnpinModel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextModelState() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetNextModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModelState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextAllModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextAllModelState() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GetNextAllModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAllModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextAllModelState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UpdateModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UpdateModel() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_UpdateModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpdateModel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ListEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ListEnabledCrops() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_ListEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestListEnabledCrops(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextEnabledCrops() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_GetNextEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextEnabledCropsRequest* /*request*/, ::carbon::frontend::model::GetNextEnabledCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextEnabledCrops(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ListCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ListCaptureCrops() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_ListCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestListCaptureCrops(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextCaptureCrops() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_GetNextCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextCaptureCropsRequest* /*request*/, ::carbon::frontend::model::GetNextCaptureCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCaptureCrops(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextSelectedCropID : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextSelectedCropID() {
      ::grpc::Service::MarkMethodRaw(9);
    }
    ~WithRawMethod_GetNextSelectedCropID() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSelectedCropID(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::model::GetNextSelectedCropIDResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextSelectedCropID(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(9, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SelectCrop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SelectCrop() {
      ::grpc::Service::MarkMethodRaw(10);
    }
    ~WithRawMethod_SelectCrop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SelectCrop(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SelectCropRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSelectCrop(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(10, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DownloadModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DownloadModel() {
      ::grpc::Service::MarkMethodRaw(11);
    }
    ~WithRawMethod_DownloadModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DownloadModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::DownloadModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDownloadModel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(11, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextModelHistory() {
      ::grpc::Service::MarkMethodRaw(12);
    }
    ~WithRawMethod_GetNextModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModelHistory(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(12, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetModelHistory() {
      ::grpc::Service::MarkMethodRaw(13);
    }
    ~WithRawMethod_GetModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetModelHistory(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(13, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetModelNicknames() {
      ::grpc::Service::MarkMethodRaw(14);
    }
    ~WithRawMethod_GetModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetModelNicknames(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(14, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextModelNicknames() {
      ::grpc::Service::MarkMethodRaw(15);
    }
    ~WithRawMethod_GetNextModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextModelNicknames(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(15, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetModelNickname : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetModelNickname() {
      ::grpc::Service::MarkMethodRaw(16);
    }
    ~WithRawMethod_SetModelNickname() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModelNickname(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SetModelNicknameRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetModelNickname(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(16, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_RefreshDefaultModelParameters : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RefreshDefaultModelParameters() {
      ::grpc::Service::MarkMethodRaw(17);
    }
    ~WithRawMethod_RefreshDefaultModelParameters() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RefreshDefaultModelParameters(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRefreshDefaultModelParameters(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(17, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SyncCropIDs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SyncCropIDs() {
      ::grpc::Service::MarkMethodRaw(18);
    }
    ~WithRawMethod_SyncCropIDs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SyncCropIDs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SyncCropIDsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSyncCropIDs(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(18, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_TriggerDownload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_TriggerDownload() {
      ::grpc::Service::MarkMethodRaw(19);
    }
    ~WithRawMethod_TriggerDownload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TriggerDownload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestTriggerDownload(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(19, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_PinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_PinModel() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->PinModel(context, request, response); }));
    }
    ~WithRawCallbackMethod_PinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status PinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::PinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* PinModel(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UnpinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UnpinModel() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UnpinModel(context, request, response); }));
    }
    ~WithRawCallbackMethod_UnpinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UnpinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::UnpinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UnpinModel(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextModelState() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextModelState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModelState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextAllModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextAllModelState() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextAllModelState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextAllModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextAllModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextAllModelState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UpdateModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UpdateModel() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UpdateModel(context, request, response); }));
    }
    ~WithRawCallbackMethod_UpdateModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpdateModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpdateModel(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ListEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ListEnabledCrops() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ListEnabledCrops(context, request, response); }));
    }
    ~WithRawCallbackMethod_ListEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ListEnabledCrops(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextEnabledCrops() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextEnabledCrops(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextEnabledCropsRequest* /*request*/, ::carbon::frontend::model::GetNextEnabledCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextEnabledCrops(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ListCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ListCaptureCrops() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ListCaptureCrops(context, request, response); }));
    }
    ~WithRawCallbackMethod_ListCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ListCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ListCaptureCrops(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextCaptureCrops() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextCaptureCrops(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextCaptureCropsRequest* /*request*/, ::carbon::frontend::model::GetNextCaptureCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCaptureCrops(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextSelectedCropID : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextSelectedCropID() {
      ::grpc::Service::MarkMethodRawCallback(9,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextSelectedCropID(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextSelectedCropID() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextSelectedCropID(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::model::GetNextSelectedCropIDResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextSelectedCropID(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SelectCrop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SelectCrop() {
      ::grpc::Service::MarkMethodRawCallback(10,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SelectCrop(context, request, response); }));
    }
    ~WithRawCallbackMethod_SelectCrop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SelectCrop(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SelectCropRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SelectCrop(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DownloadModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DownloadModel() {
      ::grpc::Service::MarkMethodRawCallback(11,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DownloadModel(context, request, response); }));
    }
    ~WithRawCallbackMethod_DownloadModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DownloadModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::DownloadModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DownloadModel(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextModelHistory() {
      ::grpc::Service::MarkMethodRawCallback(12,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextModelHistory(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModelHistory(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetModelHistory() {
      ::grpc::Service::MarkMethodRawCallback(13,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetModelHistory(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetModelHistory(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetModelNicknames() {
      ::grpc::Service::MarkMethodRawCallback(14,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetModelNicknames(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetModelNicknames(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextModelNicknames() {
      ::grpc::Service::MarkMethodRawCallback(15,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextModelNicknames(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextModelNicknames(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetModelNickname : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetModelNickname() {
      ::grpc::Service::MarkMethodRawCallback(16,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetModelNickname(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetModelNickname() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetModelNickname(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SetModelNicknameRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetModelNickname(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_RefreshDefaultModelParameters : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_RefreshDefaultModelParameters() {
      ::grpc::Service::MarkMethodRawCallback(17,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->RefreshDefaultModelParameters(context, request, response); }));
    }
    ~WithRawCallbackMethod_RefreshDefaultModelParameters() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RefreshDefaultModelParameters(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RefreshDefaultModelParameters(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SyncCropIDs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SyncCropIDs() {
      ::grpc::Service::MarkMethodRawCallback(18,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SyncCropIDs(context, request, response); }));
    }
    ~WithRawCallbackMethod_SyncCropIDs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SyncCropIDs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SyncCropIDsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SyncCropIDs(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_TriggerDownload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_TriggerDownload() {
      ::grpc::Service::MarkMethodRawCallback(19,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->TriggerDownload(context, request, response); }));
    }
    ~WithRawCallbackMethod_TriggerDownload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status TriggerDownload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* TriggerDownload(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_PinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_PinModel() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::PinModelRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::PinModelRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedPinModel(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_PinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status PinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::PinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPinModel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::PinModelRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UnpinModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UnpinModel() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::UnpinModelRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::UnpinModelRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedUnpinModel(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UnpinModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UnpinModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::UnpinModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUnpinModel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::UnpinModelRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextModelState() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>* streamer) {
                       return this->StreamedGetNextModelState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextModelState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::GetNextModelStateRequest,::carbon::frontend::model::GetNextModelStateResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextAllModelState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextAllModelState() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::GetNextModelStateRequest, ::carbon::frontend::model::GetNextModelStateResponse>* streamer) {
                       return this->StreamedGetNextAllModelState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextAllModelState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextAllModelState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextModelStateRequest* /*request*/, ::carbon::frontend::model::GetNextModelStateResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextAllModelState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::GetNextModelStateRequest,::carbon::frontend::model::GetNextModelStateResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UpdateModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UpdateModel() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedUpdateModel(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UpdateModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UpdateModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUpdateModel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ListEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ListEnabledCrops() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>* streamer) {
                       return this->StreamedListEnabledCrops(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ListEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ListEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedListEnabledCrops(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::ListCropParameters,::carbon::frontend::model::EnabledCropList>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextEnabledCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextEnabledCrops() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::GetNextEnabledCropsRequest, ::carbon::frontend::model::GetNextEnabledCropsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::GetNextEnabledCropsRequest, ::carbon::frontend::model::GetNextEnabledCropsResponse>* streamer) {
                       return this->StreamedGetNextEnabledCrops(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextEnabledCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextEnabledCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextEnabledCropsRequest* /*request*/, ::carbon::frontend::model::GetNextEnabledCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextEnabledCrops(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::GetNextEnabledCropsRequest,::carbon::frontend::model::GetNextEnabledCropsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ListCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ListCaptureCrops() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::ListCropParameters, ::carbon::frontend::model::EnabledCropList>* streamer) {
                       return this->StreamedListCaptureCrops(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ListCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ListCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ListCropParameters* /*request*/, ::carbon::frontend::model::EnabledCropList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedListCaptureCrops(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::ListCropParameters,::carbon::frontend::model::EnabledCropList>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextCaptureCrops : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextCaptureCrops() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::GetNextCaptureCropsRequest, ::carbon::frontend::model::GetNextCaptureCropsResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::GetNextCaptureCropsRequest, ::carbon::frontend::model::GetNextCaptureCropsResponse>* streamer) {
                       return this->StreamedGetNextCaptureCrops(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextCaptureCrops() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextCaptureCrops(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetNextCaptureCropsRequest* /*request*/, ::carbon::frontend::model::GetNextCaptureCropsResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextCaptureCrops(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::GetNextCaptureCropsRequest,::carbon::frontend::model::GetNextCaptureCropsResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextSelectedCropID : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextSelectedCropID() {
      ::grpc::Service::MarkMethodStreamed(9,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::model::GetNextSelectedCropIDResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::model::GetNextSelectedCropIDResponse>* streamer) {
                       return this->StreamedGetNextSelectedCropID(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextSelectedCropID() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextSelectedCropID(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::model::GetNextSelectedCropIDResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextSelectedCropID(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::model::GetNextSelectedCropIDResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SelectCrop : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SelectCrop() {
      ::grpc::Service::MarkMethodStreamed(10,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::SelectCropRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::SelectCropRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSelectCrop(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SelectCrop() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SelectCrop(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SelectCropRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSelectCrop(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::SelectCropRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DownloadModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DownloadModel() {
      ::grpc::Service::MarkMethodStreamed(11,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::DownloadModelRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::DownloadModelRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDownloadModel(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DownloadModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DownloadModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::DownloadModelRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDownloadModel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::DownloadModelRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextModelHistory() {
      ::grpc::Service::MarkMethodStreamed(12,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>* streamer) {
                       return this->StreamedGetNextModelHistory(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextModelHistory(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::ModelHistoryRequest,::carbon::frontend::model::ModelHistoryResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetModelHistory : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetModelHistory() {
      ::grpc::Service::MarkMethodStreamed(13,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::ModelHistoryRequest, ::carbon::frontend::model::ModelHistoryResponse>* streamer) {
                       return this->StreamedGetModelHistory(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetModelHistory() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetModelHistory(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::ModelHistoryRequest* /*request*/, ::carbon::frontend::model::ModelHistoryResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetModelHistory(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::ModelHistoryRequest,::carbon::frontend::model::ModelHistoryResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetModelNicknames() {
      ::grpc::Service::MarkMethodStreamed(14,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>* streamer) {
                       return this->StreamedGetModelNicknames(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetModelNicknames(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::GetModelNicknamesRequest,::carbon::frontend::model::GetModelNicknamesResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextModelNicknames : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextModelNicknames() {
      ::grpc::Service::MarkMethodStreamed(15,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::GetModelNicknamesRequest, ::carbon::frontend::model::GetModelNicknamesResponse>* streamer) {
                       return this->StreamedGetNextModelNicknames(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextModelNicknames() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextModelNicknames(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::GetModelNicknamesRequest* /*request*/, ::carbon::frontend::model::GetModelNicknamesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextModelNicknames(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::GetModelNicknamesRequest,::carbon::frontend::model::GetModelNicknamesResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetModelNickname : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetModelNickname() {
      ::grpc::Service::MarkMethodStreamed(16,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::SetModelNicknameRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::SetModelNicknameRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetModelNickname(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetModelNickname() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetModelNickname(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SetModelNicknameRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetModelNickname(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::SetModelNicknameRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_RefreshDefaultModelParameters : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_RefreshDefaultModelParameters() {
      ::grpc::Service::MarkMethodStreamed(17,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::RefreshDefaultModelParametersRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedRefreshDefaultModelParameters(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_RefreshDefaultModelParameters() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status RefreshDefaultModelParameters(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::RefreshDefaultModelParametersRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedRefreshDefaultModelParameters(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::RefreshDefaultModelParametersRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SyncCropIDs : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SyncCropIDs() {
      ::grpc::Service::MarkMethodStreamed(18,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::model::SyncCropIDsRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::model::SyncCropIDsRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSyncCropIDs(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SyncCropIDs() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SyncCropIDs(::grpc::ServerContext* /*context*/, const ::carbon::frontend::model::SyncCropIDsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSyncCropIDs(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::model::SyncCropIDsRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_TriggerDownload : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_TriggerDownload() {
      ::grpc::Service::MarkMethodStreamed(19,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedTriggerDownload(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_TriggerDownload() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status TriggerDownload(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedTriggerDownload(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_PinModel<WithStreamedUnaryMethod_UnpinModel<WithStreamedUnaryMethod_GetNextModelState<WithStreamedUnaryMethod_GetNextAllModelState<WithStreamedUnaryMethod_UpdateModel<WithStreamedUnaryMethod_ListEnabledCrops<WithStreamedUnaryMethod_GetNextEnabledCrops<WithStreamedUnaryMethod_ListCaptureCrops<WithStreamedUnaryMethod_GetNextCaptureCrops<WithStreamedUnaryMethod_GetNextSelectedCropID<WithStreamedUnaryMethod_SelectCrop<WithStreamedUnaryMethod_DownloadModel<WithStreamedUnaryMethod_GetNextModelHistory<WithStreamedUnaryMethod_GetModelHistory<WithStreamedUnaryMethod_GetModelNicknames<WithStreamedUnaryMethod_GetNextModelNicknames<WithStreamedUnaryMethod_SetModelNickname<WithStreamedUnaryMethod_RefreshDefaultModelParameters<WithStreamedUnaryMethod_SyncCropIDs<WithStreamedUnaryMethod_TriggerDownload<Service > > > > > > > > > > > > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_PinModel<WithStreamedUnaryMethod_UnpinModel<WithStreamedUnaryMethod_GetNextModelState<WithStreamedUnaryMethod_GetNextAllModelState<WithStreamedUnaryMethod_UpdateModel<WithStreamedUnaryMethod_ListEnabledCrops<WithStreamedUnaryMethod_GetNextEnabledCrops<WithStreamedUnaryMethod_ListCaptureCrops<WithStreamedUnaryMethod_GetNextCaptureCrops<WithStreamedUnaryMethod_GetNextSelectedCropID<WithStreamedUnaryMethod_SelectCrop<WithStreamedUnaryMethod_DownloadModel<WithStreamedUnaryMethod_GetNextModelHistory<WithStreamedUnaryMethod_GetModelHistory<WithStreamedUnaryMethod_GetModelNicknames<WithStreamedUnaryMethod_GetNextModelNicknames<WithStreamedUnaryMethod_SetModelNickname<WithStreamedUnaryMethod_RefreshDefaultModelParameters<WithStreamedUnaryMethod_SyncCropIDs<WithStreamedUnaryMethod_TriggerDownload<Service > > > > > > > > > > > > > > > > > > > > StreamedService;
};

}  // namespace model
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fmodel_2eproto__INCLUDED

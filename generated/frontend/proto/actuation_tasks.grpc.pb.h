// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/actuation_tasks.proto
#ifndef GRPC_frontend_2fproto_2factuation_5ftasks_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2factuation_5ftasks_2eproto__INCLUDED

#include "frontend/proto/actuation_tasks.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace actuation_tasks {

class ActuationTasksService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.actuation_tasks.ActuationTasksService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>> AsyncGetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>>(AsyncGetNextGlobalActuationTaskStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>> PrepareAsyncGetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>>(PrepareAsyncGetNextGlobalActuationTaskStateRaw(context, request, cq));
    }
    virtual ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncStartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncStartGlobalAimbotActuationTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncStartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncStartGlobalAimbotActuationTaskRaw(context, request, cq));
    }
    virtual ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncCancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncCancelGlobalAimbotActuationTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncCancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncCancelGlobalAimbotActuationTaskRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* AsyncGetNextGlobalActuationTaskStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* PrepareAsyncGetNextGlobalActuationTaskStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncStartGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncStartGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncCancelGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncCancelGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>> AsyncGetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>>(AsyncGetNextGlobalActuationTaskStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>> PrepareAsyncGetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>>(PrepareAsyncGetNextGlobalActuationTaskStateRaw(context, request, cq));
    }
    ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncStartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncStartGlobalAimbotActuationTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncStartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncStartGlobalAimbotActuationTaskRaw(context, request, cq));
    }
    ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncCancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncCancelGlobalAimbotActuationTaskRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncCancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncCancelGlobalAimbotActuationTaskRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response, std::function<void(::grpc::Status)>) override;
      void GetNextGlobalActuationTaskState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response, ::grpc::ClientUnaryReactor* reactor) override;
      void StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void StartGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void CancelGlobalAimbotActuationTask(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* AsyncGetNextGlobalActuationTaskStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* PrepareAsyncGetNextGlobalActuationTaskStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncStartGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncStartGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncCancelGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncCancelGlobalAimbotActuationTaskRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextGlobalActuationTaskState_;
    const ::grpc::internal::RpcMethod rpcmethod_StartGlobalAimbotActuationTask_;
    const ::grpc::internal::RpcMethod rpcmethod_CancelGlobalAimbotActuationTask_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response);
    virtual ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ServerContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextGlobalActuationTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextGlobalActuationTaskState() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextGlobalActuationTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextGlobalActuationTaskState(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_StartGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_StartGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_StartGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartGlobalAimbotActuationTask(::grpc::ServerContext* context, ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_CancelGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_CancelGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_CancelGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelGlobalAimbotActuationTask(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextGlobalActuationTaskState<WithAsyncMethod_StartGlobalAimbotActuationTask<WithAsyncMethod_CancelGlobalAimbotActuationTask<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextGlobalActuationTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextGlobalActuationTaskState() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* response) { return this->GetNextGlobalActuationTaskState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextGlobalActuationTaskState(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextGlobalActuationTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextGlobalActuationTaskState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_StartGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_StartGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* request, ::carbon::frontend::util::Empty* response) { return this->StartGlobalAimbotActuationTask(context, request, response); }));}
    void SetMessageAllocatorFor_StartGlobalAimbotActuationTask(
        ::grpc::MessageAllocator< ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_StartGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartGlobalAimbotActuationTask(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_CancelGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_CancelGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->CancelGlobalAimbotActuationTask(context, request, response); }));}
    void SetMessageAllocatorFor_CancelGlobalAimbotActuationTask(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_CancelGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CancelGlobalAimbotActuationTask(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextGlobalActuationTaskState<WithCallbackMethod_StartGlobalAimbotActuationTask<WithCallbackMethod_CancelGlobalAimbotActuationTask<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextGlobalActuationTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextGlobalActuationTaskState() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextGlobalActuationTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_StartGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_StartGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_StartGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_CancelGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_CancelGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_CancelGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextGlobalActuationTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextGlobalActuationTaskState() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextGlobalActuationTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextGlobalActuationTaskState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_StartGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_StartGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_StartGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestStartGlobalAimbotActuationTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_CancelGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_CancelGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_CancelGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestCancelGlobalAimbotActuationTask(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextGlobalActuationTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextGlobalActuationTaskState() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextGlobalActuationTaskState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextGlobalActuationTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextGlobalActuationTaskState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_StartGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_StartGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->StartGlobalAimbotActuationTask(context, request, response); }));
    }
    ~WithRawCallbackMethod_StartGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* StartGlobalAimbotActuationTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_CancelGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_CancelGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->CancelGlobalAimbotActuationTask(context, request, response); }));
    }
    ~WithRawCallbackMethod_CancelGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* CancelGlobalAimbotActuationTask(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextGlobalActuationTaskState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextGlobalActuationTaskState() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* streamer) {
                       return this->StreamedGetNextGlobalActuationTaskState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextGlobalActuationTaskState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextGlobalActuationTaskState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::actuation_tasks::GlobalActuationTaskState* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextGlobalActuationTaskState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::actuation_tasks::GlobalActuationTaskState>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_StartGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_StartGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedStartGlobalAimbotActuationTask(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_StartGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status StartGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedStartGlobalAimbotActuationTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::actuation_tasks::GlobalAimbotActuationTaskRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_CancelGlobalAimbotActuationTask : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_CancelGlobalAimbotActuationTask() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedCancelGlobalAimbotActuationTask(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_CancelGlobalAimbotActuationTask() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status CancelGlobalAimbotActuationTask(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedCancelGlobalAimbotActuationTask(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextGlobalActuationTaskState<WithStreamedUnaryMethod_StartGlobalAimbotActuationTask<WithStreamedUnaryMethod_CancelGlobalAimbotActuationTask<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextGlobalActuationTaskState<WithStreamedUnaryMethod_StartGlobalAimbotActuationTask<WithStreamedUnaryMethod_CancelGlobalAimbotActuationTask<Service > > > StreamedService;
};

}  // namespace actuation_tasks
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2factuation_5ftasks_2eproto__INCLUDED

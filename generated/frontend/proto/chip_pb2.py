# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/chip.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/chip.proto',
  package='carbon.frontend.chip',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x19\x66rontend/proto/chip.proto\x12\x14\x63\x61rbon.frontend.chip\x1a\x19\x66rontend/proto/util.proto\"\xcd\x01\n\x08\x43hipData\x12\n\n\x02id\x18\x01 \x01(\t\x12\x0b\n\x03url\x18\x02 \x01(\t\x12\x0f\n\x07geohash\x18\x03 \x01(\t\x12\x10\n\x08\x63hecksum\x18\x04 \x01(\t\x12\x16\n\x0e\x63ontent_length\x18\x05 \x01(\r\x12\x36\n\rdownloaded_ts\x18\x06 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x35\n\x0clast_used_ts\x18\x07 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"H\n\x17GetChipMetadataResponse\x12-\n\x05\x63hips\x18\x01 \x03(\x0b\x32\x1e.carbon.frontend.chip.ChipData\"#\n\x0f\x43hipIdsResponse\x12\x10\n\x08\x63hip_ids\x18\x01 \x03(\t2\xa0\x02\n\x0b\x43hipService\x12]\n\x0fGetChipMetadata\x12\x1b.carbon.frontend.util.Empty\x1a-.carbon.frontend.chip.GetChipMetadataResponse\x12Z\n\x14GetDownloadedChipIds\x12\x1b.carbon.frontend.util.Empty\x1a%.carbon.frontend.chip.ChipIdsResponse\x12V\n\x10GetSyncedChipIds\x12\x1b.carbon.frontend.util.Empty\x1a%.carbon.frontend.chip.ChipIdsResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_CHIPDATA = _descriptor.Descriptor(
  name='ChipData',
  full_name='carbon.frontend.chip.ChipData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.chip.ChipData.id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='url', full_name='carbon.frontend.chip.ChipData.url', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='geohash', full_name='carbon.frontend.chip.ChipData.geohash', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='checksum', full_name='carbon.frontend.chip.ChipData.checksum', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='content_length', full_name='carbon.frontend.chip.ChipData.content_length', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='downloaded_ts', full_name='carbon.frontend.chip.ChipData.downloaded_ts', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_used_ts', full_name='carbon.frontend.chip.ChipData.last_used_ts', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=79,
  serialized_end=284,
)


_GETCHIPMETADATARESPONSE = _descriptor.Descriptor(
  name='GetChipMetadataResponse',
  full_name='carbon.frontend.chip.GetChipMetadataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='chips', full_name='carbon.frontend.chip.GetChipMetadataResponse.chips', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=286,
  serialized_end=358,
)


_CHIPIDSRESPONSE = _descriptor.Descriptor(
  name='ChipIdsResponse',
  full_name='carbon.frontend.chip.ChipIdsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='chip_ids', full_name='carbon.frontend.chip.ChipIdsResponse.chip_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=360,
  serialized_end=395,
)

_CHIPDATA.fields_by_name['downloaded_ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_CHIPDATA.fields_by_name['last_used_ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETCHIPMETADATARESPONSE.fields_by_name['chips'].message_type = _CHIPDATA
DESCRIPTOR.message_types_by_name['ChipData'] = _CHIPDATA
DESCRIPTOR.message_types_by_name['GetChipMetadataResponse'] = _GETCHIPMETADATARESPONSE
DESCRIPTOR.message_types_by_name['ChipIdsResponse'] = _CHIPIDSRESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ChipData = _reflection.GeneratedProtocolMessageType('ChipData', (_message.Message,), {
  'DESCRIPTOR' : _CHIPDATA,
  '__module__' : 'frontend.proto.chip_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.chip.ChipData)
  })
_sym_db.RegisterMessage(ChipData)

GetChipMetadataResponse = _reflection.GeneratedProtocolMessageType('GetChipMetadataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETCHIPMETADATARESPONSE,
  '__module__' : 'frontend.proto.chip_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.chip.GetChipMetadataResponse)
  })
_sym_db.RegisterMessage(GetChipMetadataResponse)

ChipIdsResponse = _reflection.GeneratedProtocolMessageType('ChipIdsResponse', (_message.Message,), {
  'DESCRIPTOR' : _CHIPIDSRESPONSE,
  '__module__' : 'frontend.proto.chip_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.chip.ChipIdsResponse)
  })
_sym_db.RegisterMessage(ChipIdsResponse)


DESCRIPTOR._options = None

_CHIPSERVICE = _descriptor.ServiceDescriptor(
  name='ChipService',
  full_name='carbon.frontend.chip.ChipService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=398,
  serialized_end=686,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetChipMetadata',
    full_name='carbon.frontend.chip.ChipService.GetChipMetadata',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_GETCHIPMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDownloadedChipIds',
    full_name='carbon.frontend.chip.ChipService.GetDownloadedChipIds',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_CHIPIDSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSyncedChipIds',
    full_name='carbon.frontend.chip.ChipService.GetSyncedChipIds',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_CHIPIDSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CHIPSERVICE)

DESCRIPTOR.services_by_name['ChipService'] = _CHIPSERVICE

# @@protoc_insertion_point(module_scope)

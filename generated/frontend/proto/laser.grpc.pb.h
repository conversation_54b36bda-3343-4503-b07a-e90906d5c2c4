// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/laser.proto
#ifndef GRPC_frontend_2fproto_2flaser_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2flaser_2eproto__INCLUDED

#include "frontend/proto/laser.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace laser {

class LaserService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.laser.LaserService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    std::unique_ptr< ::grpc::ClientWriterInterface< ::carbon::frontend::camera::CameraRequest>> FireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response) {
      return std::unique_ptr< ::grpc::ClientWriterInterface< ::carbon::frontend::camera::CameraRequest>>(FireLaserRaw(context, response));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::carbon::frontend::camera::CameraRequest>> AsyncFireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::carbon::frontend::camera::CameraRequest>>(AsyncFireLaserRaw(context, response, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::carbon::frontend::camera::CameraRequest>> PrepareAsyncFireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncWriterInterface< ::carbon::frontend::camera::CameraRequest>>(PrepareAsyncFireLaserRaw(context, response, cq));
    }
    virtual ::grpc::Status GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::laser::LaserStateList* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::laser::LaserStateList>> AsyncGetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::laser::LaserStateList>>(AsyncGetNextLaserStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::laser::LaserStateList>> PrepareAsyncGetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::laser::LaserStateList>>(PrepareAsyncGetNextLaserStateRaw(context, request, cq));
    }
    virtual ::grpc::Status ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncToggleLaserEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncToggleLaserEnabledRaw(context, request, cq));
    }
    virtual ::grpc::Status EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncEnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncEnableRowRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncEnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncEnableRowRaw(context, request, cq));
    }
    virtual ::grpc::Status DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncDisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncDisableRowRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncDisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncDisableRowRaw(context, request, cq));
    }
    virtual ::grpc::Status ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncResetLaserMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncResetLaserMetricsRaw(context, request, cq));
    }
    virtual ::grpc::Status FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncFixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncFixLaserMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncFixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncFixLaserMetricsRaw(context, request, cq));
    }
    virtual ::grpc::Status SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetLaserPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetLaserPowerRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void FireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::ClientWriteReactor< ::carbon::frontend::camera::CameraRequest>* reactor) = 0;
      virtual void GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientWriterInterface< ::carbon::frontend::camera::CameraRequest>* FireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response) = 0;
    virtual ::grpc::ClientAsyncWriterInterface< ::carbon::frontend::camera::CameraRequest>* AsyncFireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncWriterInterface< ::carbon::frontend::camera::CameraRequest>* PrepareAsyncFireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::laser::LaserStateList>* AsyncGetNextLaserStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::laser::LaserStateList>* PrepareAsyncGetNextLaserStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncToggleLaserEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncToggleLaserEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncEnableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncEnableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncDisableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncDisableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncResetLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncResetLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncFixLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncFixLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    std::unique_ptr< ::grpc::ClientWriter< ::carbon::frontend::camera::CameraRequest>> FireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response) {
      return std::unique_ptr< ::grpc::ClientWriter< ::carbon::frontend::camera::CameraRequest>>(FireLaserRaw(context, response));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriter< ::carbon::frontend::camera::CameraRequest>> AsyncFireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncWriter< ::carbon::frontend::camera::CameraRequest>>(AsyncFireLaserRaw(context, response, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncWriter< ::carbon::frontend::camera::CameraRequest>> PrepareAsyncFireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncWriter< ::carbon::frontend::camera::CameraRequest>>(PrepareAsyncFireLaserRaw(context, response, cq));
    }
    ::grpc::Status GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::laser::LaserStateList* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::laser::LaserStateList>> AsyncGetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::laser::LaserStateList>>(AsyncGetNextLaserStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::laser::LaserStateList>> PrepareAsyncGetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::laser::LaserStateList>>(PrepareAsyncGetNextLaserStateRaw(context, request, cq));
    }
    ::grpc::Status ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncToggleLaserEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncToggleLaserEnabledRaw(context, request, cq));
    }
    ::grpc::Status EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncEnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncEnableRowRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncEnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncEnableRowRaw(context, request, cq));
    }
    ::grpc::Status DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncDisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncDisableRowRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncDisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncDisableRowRaw(context, request, cq));
    }
    ::grpc::Status ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncResetLaserMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncResetLaserMetricsRaw(context, request, cq));
    }
    ::grpc::Status FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncFixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncFixLaserMetricsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncFixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncFixLaserMetricsRaw(context, request, cq));
    }
    ::grpc::Status SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetLaserPowerRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetLaserPowerRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void FireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::ClientWriteReactor< ::carbon::frontend::camera::CameraRequest>* reactor) override;
      void GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response, std::function<void(::grpc::Status)>) override;
      void GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientWriter< ::carbon::frontend::camera::CameraRequest>* FireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response) override;
    ::grpc::ClientAsyncWriter< ::carbon::frontend::camera::CameraRequest>* AsyncFireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncWriter< ::carbon::frontend::camera::CameraRequest>* PrepareAsyncFireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::laser::LaserStateList>* AsyncGetNextLaserStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::laser::LaserStateList>* PrepareAsyncGetNextLaserStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncToggleLaserEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncToggleLaserEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncEnableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncEnableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncDisableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncDisableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncResetLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncResetLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncFixLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncFixLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_FireLaser_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextLaserState_;
    const ::grpc::internal::RpcMethod rpcmethod_ToggleLaserEnabled_;
    const ::grpc::internal::RpcMethod rpcmethod_EnableRow_;
    const ::grpc::internal::RpcMethod rpcmethod_DisableRow_;
    const ::grpc::internal::RpcMethod rpcmethod_ResetLaserMetrics_;
    const ::grpc::internal::RpcMethod rpcmethod_FixLaserMetrics_;
    const ::grpc::internal::RpcMethod rpcmethod_SetLaserPower_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status FireLaser(::grpc::ServerContext* context, ::grpc::ServerReader< ::carbon::frontend::camera::CameraRequest>* reader, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextLaserState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response);
    virtual ::grpc::Status ToggleLaserEnabled(::grpc::ServerContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status EnableRow(::grpc::ServerContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status DisableRow(::grpc::ServerContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status ResetLaserMetrics(::grpc::ServerContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status FixLaserMetrics(::grpc::ServerContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status SetLaserPower(::grpc::ServerContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_FireLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_FireLaser() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_FireLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FireLaser(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::frontend::camera::CameraRequest>* /*reader*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFireLaser(::grpc::ServerContext* context, ::grpc::ServerAsyncReader< ::carbon::frontend::util::Empty, ::carbon::frontend::camera::CameraRequest>* reader, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncClientStreaming(0, context, reader, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextLaserState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextLaserState() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextLaserState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextLaserState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::laser::LaserStateList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextLaserState(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::laser::LaserStateList>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ToggleLaserEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ToggleLaserEnabled() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_ToggleLaserEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLaserEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestToggleLaserEnabled(::grpc::ServerContext* context, ::carbon::frontend::laser::LaserDescriptor* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_EnableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_EnableRow() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_EnableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEnableRow(::grpc::ServerContext* context, ::carbon::frontend::laser::RowRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_DisableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_DisableRow() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_DisableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DisableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDisableRow(::grpc::ServerContext* context, ::carbon::frontend::laser::RowRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ResetLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ResetLaserMetrics() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_ResetLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResetLaserMetrics(::grpc::ServerContext* context, ::carbon::frontend::laser::LaserDescriptor* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_FixLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_FixLaserMetrics() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_FixLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FixLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::FixLaserMetricsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFixLaserMetrics(::grpc::ServerContext* context, ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetLaserPower() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_SetLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::SetLaserPowerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLaserPower(::grpc::ServerContext* context, ::carbon::frontend::laser::SetLaserPowerRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_FireLaser<WithAsyncMethod_GetNextLaserState<WithAsyncMethod_ToggleLaserEnabled<WithAsyncMethod_EnableRow<WithAsyncMethod_DisableRow<WithAsyncMethod_ResetLaserMetrics<WithAsyncMethod_FixLaserMetrics<WithAsyncMethod_SetLaserPower<Service > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_FireLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_FireLaser() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackClientStreamingHandler< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, ::carbon::frontend::util::Empty* response) { return this->FireLaser(context, response); }));
    }
    ~WithCallbackMethod_FireLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FireLaser(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::frontend::camera::CameraRequest>* /*reader*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerReadReactor< ::carbon::frontend::camera::CameraRequest>* FireLaser(
      ::grpc::CallbackServerContext* /*context*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextLaserState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextLaserState() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::laser::LaserStateList>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response) { return this->GetNextLaserState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextLaserState(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::laser::LaserStateList>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::laser::LaserStateList>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextLaserState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextLaserState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::laser::LaserStateList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextLaserState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::laser::LaserStateList* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ToggleLaserEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ToggleLaserEnabled() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response) { return this->ToggleLaserEnabled(context, request, response); }));}
    void SetMessageAllocatorFor_ToggleLaserEnabled(
        ::grpc::MessageAllocator< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ToggleLaserEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLaserEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ToggleLaserEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_EnableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_EnableRow() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response) { return this->EnableRow(context, request, response); }));}
    void SetMessageAllocatorFor_EnableRow(
        ::grpc::MessageAllocator< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_EnableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* EnableRow(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_DisableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_DisableRow() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response) { return this->DisableRow(context, request, response); }));}
    void SetMessageAllocatorFor_DisableRow(
        ::grpc::MessageAllocator< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_DisableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DisableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DisableRow(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ResetLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ResetLaserMetrics() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response) { return this->ResetLaserMetrics(context, request, response); }));}
    void SetMessageAllocatorFor_ResetLaserMetrics(
        ::grpc::MessageAllocator< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ResetLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ResetLaserMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_FixLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_FixLaserMetrics() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::FixLaserMetricsRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response) { return this->FixLaserMetrics(context, request, response); }));}
    void SetMessageAllocatorFor_FixLaserMetrics(
        ::grpc::MessageAllocator< ::carbon::frontend::laser::FixLaserMetricsRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::FixLaserMetricsRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_FixLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FixLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::FixLaserMetricsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FixLaserMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::laser::FixLaserMetricsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetLaserPower() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::SetLaserPowerRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetLaserPower(context, request, response); }));}
    void SetMessageAllocatorFor_SetLaserPower(
        ::grpc::MessageAllocator< ::carbon::frontend::laser::SetLaserPowerRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::laser::SetLaserPowerRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::SetLaserPowerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLaserPower(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::laser::SetLaserPowerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_FireLaser<WithCallbackMethod_GetNextLaserState<WithCallbackMethod_ToggleLaserEnabled<WithCallbackMethod_EnableRow<WithCallbackMethod_DisableRow<WithCallbackMethod_ResetLaserMetrics<WithCallbackMethod_FixLaserMetrics<WithCallbackMethod_SetLaserPower<Service > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_FireLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_FireLaser() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_FireLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FireLaser(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::frontend::camera::CameraRequest>* /*reader*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextLaserState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextLaserState() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextLaserState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextLaserState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::laser::LaserStateList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ToggleLaserEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ToggleLaserEnabled() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_ToggleLaserEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLaserEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_EnableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_EnableRow() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_EnableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_DisableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_DisableRow() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_DisableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DisableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ResetLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ResetLaserMetrics() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_ResetLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_FixLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_FixLaserMetrics() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_FixLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FixLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::FixLaserMetricsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetLaserPower() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_SetLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::SetLaserPowerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_FireLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_FireLaser() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_FireLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FireLaser(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::frontend::camera::CameraRequest>* /*reader*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFireLaser(::grpc::ServerContext* context, ::grpc::ServerAsyncReader< ::grpc::ByteBuffer, ::grpc::ByteBuffer>* reader, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncClientStreaming(0, context, reader, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextLaserState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextLaserState() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextLaserState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextLaserState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::laser::LaserStateList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextLaserState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ToggleLaserEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ToggleLaserEnabled() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_ToggleLaserEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLaserEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestToggleLaserEnabled(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_EnableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_EnableRow() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_EnableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestEnableRow(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_DisableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_DisableRow() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_DisableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DisableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestDisableRow(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ResetLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ResetLaserMetrics() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_ResetLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestResetLaserMetrics(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_FixLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_FixLaserMetrics() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_FixLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FixLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::FixLaserMetricsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestFixLaserMetrics(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetLaserPower() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_SetLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::SetLaserPowerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetLaserPower(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_FireLaser : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_FireLaser() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackClientStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, ::grpc::ByteBuffer* response) { return this->FireLaser(context, response); }));
    }
    ~WithRawCallbackMethod_FireLaser() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FireLaser(::grpc::ServerContext* /*context*/, ::grpc::ServerReader< ::carbon::frontend::camera::CameraRequest>* /*reader*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerReadReactor< ::grpc::ByteBuffer>* FireLaser(
      ::grpc::CallbackServerContext* /*context*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextLaserState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextLaserState() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextLaserState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextLaserState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextLaserState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::laser::LaserStateList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextLaserState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ToggleLaserEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ToggleLaserEnabled() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ToggleLaserEnabled(context, request, response); }));
    }
    ~WithRawCallbackMethod_ToggleLaserEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLaserEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ToggleLaserEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_EnableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_EnableRow() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->EnableRow(context, request, response); }));
    }
    ~WithRawCallbackMethod_EnableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status EnableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* EnableRow(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_DisableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_DisableRow() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->DisableRow(context, request, response); }));
    }
    ~WithRawCallbackMethod_DisableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status DisableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* DisableRow(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ResetLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ResetLaserMetrics() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ResetLaserMetrics(context, request, response); }));
    }
    ~WithRawCallbackMethod_ResetLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ResetLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ResetLaserMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_FixLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_FixLaserMetrics() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->FixLaserMetrics(context, request, response); }));
    }
    ~WithRawCallbackMethod_FixLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status FixLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::FixLaserMetricsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* FixLaserMetrics(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetLaserPower() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetLaserPower(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::SetLaserPowerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetLaserPower(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextLaserState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextLaserState() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::laser::LaserStateList>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::laser::LaserStateList>* streamer) {
                       return this->StreamedGetNextLaserState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextLaserState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextLaserState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::laser::LaserStateList* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextLaserState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::laser::LaserStateList>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ToggleLaserEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ToggleLaserEnabled() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedToggleLaserEnabled(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ToggleLaserEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ToggleLaserEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedToggleLaserEnabled(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::laser::LaserDescriptor,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_EnableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_EnableRow() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedEnableRow(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_EnableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status EnableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedEnableRow(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::laser::RowRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_DisableRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_DisableRow() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedDisableRow(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_DisableRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status DisableRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::RowRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedDisableRow(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::laser::RowRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ResetLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ResetLaserMetrics() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedResetLaserMetrics(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ResetLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ResetLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::LaserDescriptor* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedResetLaserMetrics(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::laser::LaserDescriptor,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_FixLaserMetrics : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_FixLaserMetrics() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::laser::FixLaserMetricsRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::laser::FixLaserMetricsRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedFixLaserMetrics(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_FixLaserMetrics() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status FixLaserMetrics(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::FixLaserMetricsRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedFixLaserMetrics(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::laser::FixLaserMetricsRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetLaserPower : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetLaserPower() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::laser::SetLaserPowerRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::laser::SetLaserPowerRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetLaserPower(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetLaserPower() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetLaserPower(::grpc::ServerContext* /*context*/, const ::carbon::frontend::laser::SetLaserPowerRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetLaserPower(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::laser::SetLaserPowerRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextLaserState<WithStreamedUnaryMethod_ToggleLaserEnabled<WithStreamedUnaryMethod_EnableRow<WithStreamedUnaryMethod_DisableRow<WithStreamedUnaryMethod_ResetLaserMetrics<WithStreamedUnaryMethod_FixLaserMetrics<WithStreamedUnaryMethod_SetLaserPower<Service > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextLaserState<WithStreamedUnaryMethod_ToggleLaserEnabled<WithStreamedUnaryMethod_EnableRow<WithStreamedUnaryMethod_DisableRow<WithStreamedUnaryMethod_ResetLaserMetrics<WithStreamedUnaryMethod_FixLaserMetrics<WithStreamedUnaryMethod_SetLaserPower<Service > > > > > > > StreamedService;
};

}  // namespace laser
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2flaser_2eproto__INCLUDED

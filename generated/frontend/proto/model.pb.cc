// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/model.proto

#include "frontend/proto/model.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace model {
constexpr Model::Model(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : synced_to_rows_()
  , viable_crop_ids_()
  , id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , nickname_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , last_used_timestamp_(nullptr)
  , downloaded_timestamp_(nullptr)
  , custom_(false)
  , pinned_(false)
  , active_(false)
  , synced_(false)
  , downloading_progress_(0)
  , estimated_downloading_remaining_time_ms_(uint64_t{0u})
  , downloading_(false)
  , recommended_(false)
  , maintained_(false){}
struct ModelDefaultTypeInternal {
  constexpr ModelDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelDefaultTypeInternal() {}
  union {
    Model _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelDefaultTypeInternal _Model_default_instance_;
constexpr SelectCropRequest::SelectCropRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SelectCropRequestDefaultTypeInternal {
  constexpr SelectCropRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SelectCropRequestDefaultTypeInternal() {}
  union {
    SelectCropRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SelectCropRequestDefaultTypeInternal _SelectCropRequest_default_instance_;
constexpr ListCropParameters::ListCropParameters(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lang_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct ListCropParametersDefaultTypeInternal {
  constexpr ListCropParametersDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ListCropParametersDefaultTypeInternal() {}
  union {
    ListCropParameters _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ListCropParametersDefaultTypeInternal _ListCropParameters_default_instance_;
constexpr EnabledCrop::EnabledCrop(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , carbon_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , common_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , description_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , notes_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , pinned_model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , recommended_model_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , created_(int64_t{0}){}
struct EnabledCropDefaultTypeInternal {
  constexpr EnabledCropDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EnabledCropDefaultTypeInternal() {}
  union {
    EnabledCrop _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EnabledCropDefaultTypeInternal _EnabledCrop_default_instance_;
constexpr EnabledCropList::EnabledCropList(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabledcrops_(){}
struct EnabledCropListDefaultTypeInternal {
  constexpr EnabledCropListDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~EnabledCropListDefaultTypeInternal() {}
  union {
    EnabledCropList _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT EnabledCropListDefaultTypeInternal _EnabledCropList_default_instance_;
constexpr GetNextSelectedCropIDResponse::GetNextSelectedCropIDResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextSelectedCropIDResponseDefaultTypeInternal {
  constexpr GetNextSelectedCropIDResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextSelectedCropIDResponseDefaultTypeInternal() {}
  union {
    GetNextSelectedCropIDResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextSelectedCropIDResponseDefaultTypeInternal _GetNextSelectedCropIDResponse_default_instance_;
constexpr PinModelRequest::PinModelRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , allow_pinned_crop_override_(false)
  , p2p_(false){}
struct PinModelRequestDefaultTypeInternal {
  constexpr PinModelRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PinModelRequestDefaultTypeInternal() {}
  union {
    PinModelRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PinModelRequestDefaultTypeInternal _PinModelRequest_default_instance_;
constexpr UnpinModelRequest::UnpinModelRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , p2p_(false){}
struct UnpinModelRequestDefaultTypeInternal {
  constexpr UnpinModelRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UnpinModelRequestDefaultTypeInternal() {}
  union {
    UnpinModelRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UnpinModelRequestDefaultTypeInternal _UnpinModelRequest_default_instance_;
constexpr GetNextModelStateRequest::GetNextModelStateRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextModelStateRequestDefaultTypeInternal {
  constexpr GetNextModelStateRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextModelStateRequestDefaultTypeInternal() {}
  union {
    GetNextModelStateRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextModelStateRequestDefaultTypeInternal _GetNextModelStateRequest_default_instance_;
constexpr GetNextModelStateResponse::GetNextModelStateResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : models_()
  , current_p2p_model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , current_deepweed_model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextModelStateResponseDefaultTypeInternal {
  constexpr GetNextModelStateResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextModelStateResponseDefaultTypeInternal() {}
  union {
    GetNextModelStateResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextModelStateResponseDefaultTypeInternal _GetNextModelStateResponse_default_instance_;
constexpr DownloadModelRequest::DownloadModelRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DownloadModelRequestDefaultTypeInternal {
  constexpr DownloadModelRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DownloadModelRequestDefaultTypeInternal() {}
  union {
    DownloadModelRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DownloadModelRequestDefaultTypeInternal _DownloadModelRequest_default_instance_;
constexpr ModelHistoryRequest::ModelHistoryRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : match_filter_(nullptr)
  , ts_(nullptr)
  , event_type_matcher_(nullptr)
  , start_timestamp_(int64_t{0})
  , count_(int64_t{0})
  , reverse_(false){}
struct ModelHistoryRequestDefaultTypeInternal {
  constexpr ModelHistoryRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelHistoryRequestDefaultTypeInternal() {}
  union {
    ModelHistoryRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelHistoryRequestDefaultTypeInternal _ModelHistoryRequest_default_instance_;
constexpr ModelEvent::ModelEvent(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , job_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_nickname_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_parameters_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , time_(int64_t{0}){}
struct ModelEventDefaultTypeInternal {
  constexpr ModelEventDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelEventDefaultTypeInternal() {}
  union {
    ModelEvent _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelEventDefaultTypeInternal _ModelEvent_default_instance_;
constexpr ModelEventTypeMatcher::ModelEventTypeMatcher(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_start_(false)
  , pinned_(false)
  , unpinned_(false)
  , recommended_(false)
  , activated_(false)
  , nickname_change_(false)
  , nickname_delete_(false)
  , default_parameter_change_(false)
  , parameter_change_(false){}
struct ModelEventTypeMatcherDefaultTypeInternal {
  constexpr ModelEventTypeMatcherDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelEventTypeMatcherDefaultTypeInternal() {}
  union {
    ModelEventTypeMatcher _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelEventTypeMatcherDefaultTypeInternal _ModelEventTypeMatcher_default_instance_;
constexpr ModelHistoryResponse::ModelHistoryResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : events_()
  , ts_(nullptr){}
struct ModelHistoryResponseDefaultTypeInternal {
  constexpr ModelHistoryResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelHistoryResponseDefaultTypeInternal() {}
  union {
    ModelHistoryResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelHistoryResponseDefaultTypeInternal _ModelHistoryResponse_default_instance_;
constexpr GetModelNicknamesRequest::GetModelNicknamesRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : model_ids_()
  , ts_(nullptr){}
struct GetModelNicknamesRequestDefaultTypeInternal {
  constexpr GetModelNicknamesRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetModelNicknamesRequestDefaultTypeInternal() {}
  union {
    GetModelNicknamesRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetModelNicknamesRequestDefaultTypeInternal _GetModelNicknamesRequest_default_instance_;
constexpr GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUseDefaultTypeInternal {
  constexpr GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUseDefaultTypeInternal() {}
  union {
    GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUseDefaultTypeInternal _GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse_default_instance_;
constexpr GetModelNicknamesResponse::GetModelNicknamesResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : model_nicknames_(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{})
  , ts_(nullptr){}
struct GetModelNicknamesResponseDefaultTypeInternal {
  constexpr GetModelNicknamesResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetModelNicknamesResponseDefaultTypeInternal() {}
  union {
    GetModelNicknamesResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetModelNicknamesResponseDefaultTypeInternal _GetModelNicknamesResponse_default_instance_;
constexpr SetModelNicknameRequest::SetModelNicknameRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_nickname_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SetModelNicknameRequestDefaultTypeInternal {
  constexpr SetModelNicknameRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetModelNicknameRequestDefaultTypeInternal() {}
  union {
    SetModelNicknameRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetModelNicknameRequestDefaultTypeInternal _SetModelNicknameRequest_default_instance_;
constexpr CropModelPair::CropModelPair(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct CropModelPairDefaultTypeInternal {
  constexpr CropModelPairDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CropModelPairDefaultTypeInternal() {}
  union {
    CropModelPair _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CropModelPairDefaultTypeInternal _CropModelPair_default_instance_;
constexpr RefreshDefaultModelParametersRequest::RefreshDefaultModelParametersRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cropmodelpairs_(){}
struct RefreshDefaultModelParametersRequestDefaultTypeInternal {
  constexpr RefreshDefaultModelParametersRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RefreshDefaultModelParametersRequestDefaultTypeInternal() {}
  union {
    RefreshDefaultModelParametersRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RefreshDefaultModelParametersRequestDefaultTypeInternal _RefreshDefaultModelParametersRequest_default_instance_;
constexpr SyncCropIDsRequest::SyncCropIDsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : force_cache_refresh_(false){}
struct SyncCropIDsRequestDefaultTypeInternal {
  constexpr SyncCropIDsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SyncCropIDsRequestDefaultTypeInternal() {}
  union {
    SyncCropIDsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SyncCropIDsRequestDefaultTypeInternal _SyncCropIDsRequest_default_instance_;
constexpr GetNextEnabledCropsRequest::GetNextEnabledCropsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lang_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextEnabledCropsRequestDefaultTypeInternal {
  constexpr GetNextEnabledCropsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextEnabledCropsRequestDefaultTypeInternal() {}
  union {
    GetNextEnabledCropsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextEnabledCropsRequestDefaultTypeInternal _GetNextEnabledCropsRequest_default_instance_;
constexpr GetNextEnabledCropsResponse::GetNextEnabledCropsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabledcrops_()
  , ts_(nullptr){}
struct GetNextEnabledCropsResponseDefaultTypeInternal {
  constexpr GetNextEnabledCropsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextEnabledCropsResponseDefaultTypeInternal() {}
  union {
    GetNextEnabledCropsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextEnabledCropsResponseDefaultTypeInternal _GetNextEnabledCropsResponse_default_instance_;
constexpr GetNextCaptureCropsRequest::GetNextCaptureCropsRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : lang_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextCaptureCropsRequestDefaultTypeInternal {
  constexpr GetNextCaptureCropsRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextCaptureCropsRequestDefaultTypeInternal() {}
  union {
    GetNextCaptureCropsRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextCaptureCropsRequestDefaultTypeInternal _GetNextCaptureCropsRequest_default_instance_;
constexpr GetNextCaptureCropsResponse::GetNextCaptureCropsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enabledcrops_()
  , ts_(nullptr){}
struct GetNextCaptureCropsResponseDefaultTypeInternal {
  constexpr GetNextCaptureCropsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextCaptureCropsResponseDefaultTypeInternal() {}
  union {
    GetNextCaptureCropsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextCaptureCropsResponseDefaultTypeInternal _GetNextCaptureCropsResponse_default_instance_;
}  // namespace model
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fmodel_2eproto[26];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fmodel_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fmodel_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fmodel_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, custom_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, pinned_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, active_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, synced_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, synced_to_rows_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, downloading_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, last_used_timestamp_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, downloading_progress_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, estimated_downloading_remaining_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, downloaded_timestamp_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, recommended_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, viable_crop_ids_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, maintained_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::Model, nickname_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::SelectCropRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::SelectCropRequest, crop_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ListCropParameters, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ListCropParameters, lang_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, created_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, carbon_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, common_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, description_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, notes_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, pinned_model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCrop, recommended_model_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCropList, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::EnabledCropList, enabledcrops_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextSelectedCropIDResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextSelectedCropIDResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextSelectedCropIDResponse, crop_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::PinModelRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::PinModelRequest, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::PinModelRequest, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::PinModelRequest, allow_pinned_crop_override_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::PinModelRequest, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::PinModelRequest, p2p_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::UnpinModelRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::UnpinModelRequest, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::UnpinModelRequest, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::UnpinModelRequest, p2p_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateRequest, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateRequest, crop_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateResponse, models_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateResponse, current_p2p_model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextModelStateResponse, current_deepweed_model_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::DownloadModelRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::DownloadModelRequest, model_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryRequest, start_timestamp_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryRequest, count_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryRequest, reverse_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryRequest, match_filter_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryRequest, event_type_matcher_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, model_type_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, job_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, time_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, model_nickname_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEvent, model_parameters_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, robot_start_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, pinned_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, unpinned_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, recommended_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, activated_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, nickname_change_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, nickname_delete_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, default_parameter_change_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelEventTypeMatcher, parameter_change_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryResponse, events_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::ModelHistoryResponse, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesRequest, model_ids_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse, value_),
  0,
  1,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesResponse, model_nicknames_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetModelNicknamesResponse, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::SetModelNicknameRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::SetModelNicknameRequest, model_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::SetModelNicknameRequest, model_nickname_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::CropModelPair, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::CropModelPair, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::CropModelPair, model_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::RefreshDefaultModelParametersRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::RefreshDefaultModelParametersRequest, cropmodelpairs_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::SyncCropIDsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::SyncCropIDsRequest, force_cache_refresh_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextEnabledCropsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextEnabledCropsRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextEnabledCropsRequest, lang_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextEnabledCropsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextEnabledCropsResponse, enabledcrops_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextEnabledCropsResponse, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextCaptureCropsRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextCaptureCropsRequest, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextCaptureCropsRequest, lang_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextCaptureCropsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextCaptureCropsResponse, enabledcrops_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::model::GetNextCaptureCropsResponse, ts_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::model::Model)},
  { 24, -1, -1, sizeof(::carbon::frontend::model::SelectCropRequest)},
  { 31, -1, -1, sizeof(::carbon::frontend::model::ListCropParameters)},
  { 38, -1, -1, sizeof(::carbon::frontend::model::EnabledCrop)},
  { 52, -1, -1, sizeof(::carbon::frontend::model::EnabledCropList)},
  { 59, -1, -1, sizeof(::carbon::frontend::model::GetNextSelectedCropIDResponse)},
  { 67, -1, -1, sizeof(::carbon::frontend::model::PinModelRequest)},
  { 78, -1, -1, sizeof(::carbon::frontend::model::UnpinModelRequest)},
  { 87, -1, -1, sizeof(::carbon::frontend::model::GetNextModelStateRequest)},
  { 96, -1, -1, sizeof(::carbon::frontend::model::GetNextModelStateResponse)},
  { 106, -1, -1, sizeof(::carbon::frontend::model::DownloadModelRequest)},
  { 113, -1, -1, sizeof(::carbon::frontend::model::ModelHistoryRequest)},
  { 125, -1, -1, sizeof(::carbon::frontend::model::ModelEvent)},
  { 139, -1, -1, sizeof(::carbon::frontend::model::ModelEventTypeMatcher)},
  { 154, -1, -1, sizeof(::carbon::frontend::model::ModelHistoryResponse)},
  { 162, -1, -1, sizeof(::carbon::frontend::model::GetModelNicknamesRequest)},
  { 170, 178, -1, sizeof(::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse)},
  { 180, -1, -1, sizeof(::carbon::frontend::model::GetModelNicknamesResponse)},
  { 188, -1, -1, sizeof(::carbon::frontend::model::SetModelNicknameRequest)},
  { 196, -1, -1, sizeof(::carbon::frontend::model::CropModelPair)},
  { 204, -1, -1, sizeof(::carbon::frontend::model::RefreshDefaultModelParametersRequest)},
  { 211, -1, -1, sizeof(::carbon::frontend::model::SyncCropIDsRequest)},
  { 218, -1, -1, sizeof(::carbon::frontend::model::GetNextEnabledCropsRequest)},
  { 226, -1, -1, sizeof(::carbon::frontend::model::GetNextEnabledCropsResponse)},
  { 234, -1, -1, sizeof(::carbon::frontend::model::GetNextCaptureCropsRequest)},
  { 242, -1, -1, sizeof(::carbon::frontend::model::GetNextCaptureCropsResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_Model_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_SelectCropRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_ListCropParameters_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_EnabledCrop_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_EnabledCropList_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetNextSelectedCropIDResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_PinModelRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_UnpinModelRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetNextModelStateRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetNextModelStateResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_DownloadModelRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_ModelHistoryRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_ModelEvent_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_ModelEventTypeMatcher_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_ModelHistoryResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetModelNicknamesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetModelNicknamesResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_SetModelNicknameRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_CropModelPair_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_RefreshDefaultModelParametersRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_SyncCropIDsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetNextEnabledCropsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetNextEnabledCropsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetNextCaptureCropsRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::model::_GetNextCaptureCropsResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fmodel_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\032frontend/proto/model.proto\022\025carbon.fro"
  "ntend.model\032\031frontend/proto/util.proto\"\351"
  "\003\n\005Model\022\n\n\002id\030\001 \001(\t\022\014\n\004crop\030\002 \001(\t\022+\n\002ts"
  "\030\003 \001(\0132\037.carbon.frontend.util.Timestamp\022"
  "\016\n\006custom\030\004 \001(\010\022\016\n\006pinned\030\005 \001(\010\022\016\n\006activ"
  "e\030\006 \001(\010\022\016\n\006synced\030\007 \001(\010\022\026\n\016synced_to_row"
  "s\030\010 \003(\010\022\023\n\013downloading\030\t \001(\010\022\014\n\004type\030\n \001"
  "(\t\022<\n\023last_used_timestamp\030\013 \001(\0132\037.carbon"
  ".frontend.util.Timestamp\022\034\n\024downloading_"
  "progress\030\014 \001(\002\022/\n\'estimated_downloading_"
  "remaining_time_ms\030\r \001(\004\022=\n\024downloaded_ti"
  "mestamp\030\016 \001(\0132\037.carbon.frontend.util.Tim"
  "estamp\022\023\n\013recommended\030\017 \001(\010\022\027\n\017viable_cr"
  "op_ids\030\020 \003(\t\022\022\n\nmaintained\030\021 \001(\010\022\020\n\010nick"
  "name\030\022 \001(\t\"$\n\021SelectCropRequest\022\017\n\007crop_"
  "id\030\001 \001(\t\"\"\n\022ListCropParameters\022\014\n\004lang\030\001"
  " \001(\t\"\260\001\n\013EnabledCrop\022\n\n\002id\030\001 \001(\t\022\017\n\007crea"
  "ted\030\002 \001(\003\022\027\n\013carbon_name\030\003 \001(\tB\002\030\001\022\023\n\013co"
  "mmon_name\030\004 \001(\t\022\023\n\013description\030\005 \001(\t\022\r\n\005"
  "notes\030\006 \001(\t\022\027\n\017pinned_model_id\030\007 \001(\t\022\031\n\021"
  "recommended_model\030\010 \001(\t\"K\n\017EnabledCropLi"
  "st\0228\n\014enabledCrops\030\001 \003(\0132\".carbon.fronte"
  "nd.model.EnabledCrop\"]\n\035GetNextSelectedC"
  "ropIDResponse\022+\n\002ts\030\001 \001(\0132\037.carbon.front"
  "end.util.Timestamp\022\017\n\007crop_id\030\002 \001(\t\"q\n\017P"
  "inModelRequest\022\n\n\002id\030\001 \001(\t\022\020\n\004crop\030\002 \001(\t"
  "B\002\030\001\022\"\n\032allow_pinned_crop_override\030\003 \001(\010"
  "\022\017\n\007crop_id\030\004 \001(\t\022\013\n\003p2p\030\005 \001(\010\"C\n\021UnpinM"
  "odelRequest\022\020\n\004crop\030\001 \001(\tB\002\030\001\022\017\n\007crop_id"
  "\030\004 \001(\t\022\013\n\003p2p\030\005 \001(\010\"j\n\030GetNextModelState"
  "Request\022\020\n\004crop\030\001 \001(\tB\002\030\001\022+\n\002ts\030\002 \001(\0132\037."
  "carbon.frontend.util.Timestamp\022\017\n\007crop_i"
  "d\030\003 \001(\t\"\267\001\n\031GetNextModelStateResponse\022,\n"
  "\006models\030\001 \003(\0132\034.carbon.frontend.model.Mo"
  "del\022+\n\002ts\030\002 \001(\0132\037.carbon.frontend.util.T"
  "imestamp\022\034\n\024current_p2p_model_id\030\003 \001(\t\022!"
  "\n\031current_deepweed_model_id\030\004 \001(\t\"(\n\024Dow"
  "nloadModelRequest\022\020\n\010model_id\030\001 \001(\t\"\376\001\n\023"
  "ModelHistoryRequest\022\027\n\017start_timestamp\030\001"
  " \001(\003\022\r\n\005count\030\002 \001(\003\022\017\n\007reverse\030\003 \001(\010\0227\n\014"
  "match_filter\030\004 \001(\0132!.carbon.frontend.mod"
  "el.ModelEvent\022+\n\002ts\030\005 \001(\0132\037.carbon.front"
  "end.util.Timestamp\022H\n\022event_type_matcher"
  "\030\006 \001(\0132,.carbon.frontend.model.ModelEven"
  "tTypeMatcher\"\243\001\n\nModelEvent\022\014\n\004type\030\001 \001("
  "\t\022\020\n\010model_id\030\002 \001(\t\022\022\n\nmodel_type\030\003 \001(\t\022"
  "\017\n\007crop_id\030\004 \001(\t\022\020\n\010job_name\030\005 \001(\t\022\014\n\004ti"
  "me\030\006 \001(\003\022\026\n\016model_nickname\030\007 \001(\t\022\030\n\020mode"
  "l_parameters\030\010 \001(\t\"\344\001\n\025ModelEventTypeMat"
  "cher\022\023\n\013robot_start\030\001 \001(\010\022\016\n\006pinned\030\002 \001("
  "\010\022\020\n\010unpinned\030\003 \001(\010\022\023\n\013recommended\030\004 \001(\010"
  "\022\021\n\tactivated\030\005 \001(\010\022\027\n\017nickname_change\030\006"
  " \001(\010\022\027\n\017nickname_delete\030\007 \001(\010\022 \n\030default"
  "_parameter_change\030\010 \001(\010\022\030\n\020parameter_cha"
  "nge\030\t \001(\010\"v\n\024ModelHistoryResponse\0221\n\006eve"
  "nts\030\001 \003(\0132!.carbon.frontend.model.ModelE"
  "vent\022+\n\002ts\030\005 \001(\0132\037.carbon.frontend.util."
  "Timestamp\"Z\n\030GetModelNicknamesRequest\022\021\n"
  "\tmodel_ids\030\001 \003(\t\022+\n\002ts\030\005 \001(\0132\037.carbon.fr"
  "ontend.util.Timestamp\"\336\001\n\031GetModelNickna"
  "mesResponse\022]\n\017model_nicknames\030\001 \003(\0132D.c"
  "arbon.frontend.model.GetModelNicknamesRe"
  "sponse.ModelNicknamesEntry\022+\n\002ts\030\005 \001(\0132\037"
  ".carbon.frontend.util.Timestamp\0325\n\023Model"
  "NicknamesEntry\022\013\n\003key\030\001 \001(\t\022\r\n\005value\030\002 \001"
  "(\t:\0028\001\"C\n\027SetModelNicknameRequest\022\020\n\010mod"
  "el_id\030\001 \001(\t\022\026\n\016model_nickname\030\002 \001(\t\"2\n\rC"
  "ropModelPair\022\017\n\007crop_id\030\001 \001(\t\022\020\n\010model_i"
  "d\030\002 \001(\t\"d\n$RefreshDefaultModelParameters"
  "Request\022<\n\016cropModelPairs\030\001 \003(\0132$.carbon"
  ".frontend.model.CropModelPair\"1\n\022SyncCro"
  "pIDsRequest\022\033\n\023force_cache_refresh\030\001 \001(\010"
  "\"W\n\032GetNextEnabledCropsRequest\022+\n\002ts\030\001 \001"
  "(\0132\037.carbon.frontend.util.Timestamp\022\014\n\004l"
  "ang\030\002 \001(\t\"\204\001\n\033GetNextEnabledCropsRespons"
  "e\0228\n\014enabledCrops\030\001 \003(\0132\".carbon.fronten"
  "d.model.EnabledCrop\022+\n\002ts\030\002 \001(\0132\037.carbon"
  ".frontend.util.Timestamp\"W\n\032GetNextCaptu"
  "reCropsRequest\022+\n\002ts\030\001 \001(\0132\037.carbon.fron"
  "tend.util.Timestamp\022\014\n\004lang\030\002 \001(\t\"\204\001\n\033Ge"
  "tNextCaptureCropsResponse\0228\n\014enabledCrop"
  "s\030\001 \003(\0132\".carbon.frontend.model.EnabledC"
  "rop\022+\n\002ts\030\002 \001(\0132\037.carbon.frontend.util.T"
  "imestamp2\252\020\n\014ModelService\022O\n\010PinModel\022&."
  "carbon.frontend.model.PinModelRequest\032\033."
  "carbon.frontend.util.Empty\022S\n\nUnpinModel"
  "\022(.carbon.frontend.model.UnpinModelReque"
  "st\032\033.carbon.frontend.util.Empty\022v\n\021GetNe"
  "xtModelState\022/.carbon.frontend.model.Get"
  "NextModelStateRequest\0320.carbon.frontend."
  "model.GetNextModelStateResponse\022y\n\024GetNe"
  "xtAllModelState\022/.carbon.frontend.model."
  "GetNextModelStateRequest\0320.carbon.fronte"
  "nd.model.GetNextModelStateResponse\022G\n\013Up"
  "dateModel\022\033.carbon.frontend.util.Empty\032\033"
  ".carbon.frontend.util.Empty\022e\n\020ListEnabl"
  "edCrops\022).carbon.frontend.model.ListCrop"
  "Parameters\032&.carbon.frontend.model.Enabl"
  "edCropList\022|\n\023GetNextEnabledCrops\0221.carb"
  "on.frontend.model.GetNextEnabledCropsReq"
  "uest\0322.carbon.frontend.model.GetNextEnab"
  "ledCropsResponse\022e\n\020ListCaptureCrops\022).c"
  "arbon.frontend.model.ListCropParameters\032"
  "&.carbon.frontend.model.EnabledCropList\022"
  "|\n\023GetNextCaptureCrops\0221.carbon.frontend"
  ".model.GetNextCaptureCropsRequest\0322.carb"
  "on.frontend.model.GetNextCaptureCropsRes"
  "ponse\022n\n\025GetNextSelectedCropID\022\037.carbon."
  "frontend.util.Timestamp\0324.carbon.fronten"
  "d.model.GetNextSelectedCropIDResponse\022S\n"
  "\nSelectCrop\022(.carbon.frontend.model.Sele"
  "ctCropRequest\032\033.carbon.frontend.util.Emp"
  "ty\022Y\n\rDownloadModel\022+.carbon.frontend.mo"
  "del.DownloadModelRequest\032\033.carbon.fronte"
  "nd.util.Empty\022n\n\023GetNextModelHistory\022*.c"
  "arbon.frontend.model.ModelHistoryRequest"
  "\032+.carbon.frontend.model.ModelHistoryRes"
  "ponse\022j\n\017GetModelHistory\022*.carbon.fronte"
  "nd.model.ModelHistoryRequest\032+.carbon.fr"
  "ontend.model.ModelHistoryResponse\022v\n\021Get"
  "ModelNicknames\022/.carbon.frontend.model.G"
  "etModelNicknamesRequest\0320.carbon.fronten"
  "d.model.GetModelNicknamesResponse\022z\n\025Get"
  "NextModelNicknames\022/.carbon.frontend.mod"
  "el.GetModelNicknamesRequest\0320.carbon.fro"
  "ntend.model.GetModelNicknamesResponse\022_\n"
  "\020SetModelNickname\022..carbon.frontend.mode"
  "l.SetModelNicknameRequest\032\033.carbon.front"
  "end.util.Empty\022y\n\035RefreshDefaultModelPar"
  "ameters\022;.carbon.frontend.model.RefreshD"
  "efaultModelParametersRequest\032\033.carbon.fr"
  "ontend.util.Empty\022U\n\013SyncCropIDs\022).carbo"
  "n.frontend.model.SyncCropIDsRequest\032\033.ca"
  "rbon.frontend.util.Empty\022K\n\017TriggerDownl"
  "oad\022\033.carbon.frontend.util.Empty\032\033.carbo"
  "n.frontend.util.EmptyB\020Z\016proto/frontendb"
  "\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fmodel_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fmodel_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fmodel_2eproto = {
  false, false, 5447, descriptor_table_protodef_frontend_2fproto_2fmodel_2eproto, "frontend/proto/model.proto", 
  &descriptor_table_frontend_2fproto_2fmodel_2eproto_once, descriptor_table_frontend_2fproto_2fmodel_2eproto_deps, 1, 26,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fmodel_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fmodel_2eproto, file_level_enum_descriptors_frontend_2fproto_2fmodel_2eproto, file_level_service_descriptors_frontend_2fproto_2fmodel_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fmodel_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fmodel_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fmodel_2eproto(&descriptor_table_frontend_2fproto_2fmodel_2eproto);
namespace carbon {
namespace frontend {
namespace model {

// ===================================================================

class Model::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const Model* msg);
  static const ::carbon::frontend::util::Timestamp& last_used_timestamp(const Model* msg);
  static const ::carbon::frontend::util::Timestamp& downloaded_timestamp(const Model* msg);
};

const ::carbon::frontend::util::Timestamp&
Model::_Internal::ts(const Model* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::util::Timestamp&
Model::_Internal::last_used_timestamp(const Model* msg) {
  return *msg->last_used_timestamp_;
}
const ::carbon::frontend::util::Timestamp&
Model::_Internal::downloaded_timestamp(const Model* msg) {
  return *msg->downloaded_timestamp_;
}
void Model::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void Model::clear_last_used_timestamp() {
  if (GetArenaForAllocation() == nullptr && last_used_timestamp_ != nullptr) {
    delete last_used_timestamp_;
  }
  last_used_timestamp_ = nullptr;
}
void Model::clear_downloaded_timestamp() {
  if (GetArenaForAllocation() == nullptr && downloaded_timestamp_ != nullptr) {
    delete downloaded_timestamp_;
  }
  downloaded_timestamp_ = nullptr;
}
Model::Model(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  synced_to_rows_(arena),
  viable_crop_ids_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.Model)
}
Model::Model(const Model& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      synced_to_rows_(from.synced_to_rows_),
      viable_crop_ids_(from.viable_crop_ids_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_type().empty()) {
    type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_type(), 
      GetArenaForAllocation());
  }
  nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_nickname().empty()) {
    nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_nickname(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_last_used_timestamp()) {
    last_used_timestamp_ = new ::carbon::frontend::util::Timestamp(*from.last_used_timestamp_);
  } else {
    last_used_timestamp_ = nullptr;
  }
  if (from._internal_has_downloaded_timestamp()) {
    downloaded_timestamp_ = new ::carbon::frontend::util::Timestamp(*from.downloaded_timestamp_);
  } else {
    downloaded_timestamp_ = nullptr;
  }
  ::memcpy(&custom_, &from.custom_,
    static_cast<size_t>(reinterpret_cast<char*>(&maintained_) -
    reinterpret_cast<char*>(&custom_)) + sizeof(maintained_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.Model)
}

inline void Model::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&maintained_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(maintained_));
}

Model::~Model() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.Model)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Model::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  nickname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete last_used_timestamp_;
  if (this != internal_default_instance()) delete downloaded_timestamp_;
}

void Model::ArenaDtor(void* object) {
  Model* _this = reinterpret_cast< Model* >(object);
  (void)_this;
}
void Model::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Model::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Model::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.Model)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  synced_to_rows_.Clear();
  viable_crop_ids_.Clear();
  id_.ClearToEmpty();
  crop_.ClearToEmpty();
  type_.ClearToEmpty();
  nickname_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && last_used_timestamp_ != nullptr) {
    delete last_used_timestamp_;
  }
  last_used_timestamp_ = nullptr;
  if (GetArenaForAllocation() == nullptr && downloaded_timestamp_ != nullptr) {
    delete downloaded_timestamp_;
  }
  downloaded_timestamp_ = nullptr;
  ::memset(&custom_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&maintained_) -
      reinterpret_cast<char*>(&custom_)) + sizeof(maintained_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Model::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.Model.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.Model.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool custom = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          custom_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool pinned = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          pinned_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool active = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          active_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool synced = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          synced_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated bool synced_to_rows = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedBoolParser(_internal_mutable_synced_to_rows(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 64) {
          _internal_add_synced_to_rows(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool downloading = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          downloading_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string type = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          auto str = _internal_mutable_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.Model.type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp last_used_timestamp = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          ptr = ctx->ParseMessage(_internal_mutable_last_used_timestamp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float downloading_progress = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 101)) {
          downloading_progress_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // uint64 estimated_downloading_remaining_time_ms = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 104)) {
          estimated_downloading_remaining_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp downloaded_timestamp = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 114)) {
          ptr = ctx->ParseMessage(_internal_mutable_downloaded_timestamp(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool recommended = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 120)) {
          recommended_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string viable_crop_ids = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 130)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_viable_crop_ids();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.Model.viable_crop_ids"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<130>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool maintained = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 136)) {
          maintained_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string nickname = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          auto str = _internal_mutable_nickname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.Model.nickname"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Model::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.Model)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.Model.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string crop = 2;
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.Model.crop");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_crop(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 3;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::ts(this), target, stream);
  }

  // bool custom = 4;
  if (this->_internal_custom() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_custom(), target);
  }

  // bool pinned = 5;
  if (this->_internal_pinned() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_pinned(), target);
  }

  // bool active = 6;
  if (this->_internal_active() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_active(), target);
  }

  // bool synced = 7;
  if (this->_internal_synced() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_synced(), target);
  }

  // repeated bool synced_to_rows = 8;
  if (this->_internal_synced_to_rows_size() > 0) {
    target = stream->WriteFixedPacked(8, _internal_synced_to_rows(), target);
  }

  // bool downloading = 9;
  if (this->_internal_downloading() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(9, this->_internal_downloading(), target);
  }

  // string type = 10;
  if (!this->_internal_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_type().data(), static_cast<int>(this->_internal_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.Model.type");
    target = stream->WriteStringMaybeAliased(
        10, this->_internal_type(), target);
  }

  // .carbon.frontend.util.Timestamp last_used_timestamp = 11;
  if (this->_internal_has_last_used_timestamp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        11, _Internal::last_used_timestamp(this), target, stream);
  }

  // float downloading_progress = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_downloading_progress = this->_internal_downloading_progress();
  uint32_t raw_downloading_progress;
  memcpy(&raw_downloading_progress, &tmp_downloading_progress, sizeof(tmp_downloading_progress));
  if (raw_downloading_progress != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(12, this->_internal_downloading_progress(), target);
  }

  // uint64 estimated_downloading_remaining_time_ms = 13;
  if (this->_internal_estimated_downloading_remaining_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(13, this->_internal_estimated_downloading_remaining_time_ms(), target);
  }

  // .carbon.frontend.util.Timestamp downloaded_timestamp = 14;
  if (this->_internal_has_downloaded_timestamp()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        14, _Internal::downloaded_timestamp(this), target, stream);
  }

  // bool recommended = 15;
  if (this->_internal_recommended() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(15, this->_internal_recommended(), target);
  }

  // repeated string viable_crop_ids = 16;
  for (int i = 0, n = this->_internal_viable_crop_ids_size(); i < n; i++) {
    const auto& s = this->_internal_viable_crop_ids(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.Model.viable_crop_ids");
    target = stream->WriteString(16, s, target);
  }

  // bool maintained = 17;
  if (this->_internal_maintained() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(17, this->_internal_maintained(), target);
  }

  // string nickname = 18;
  if (!this->_internal_nickname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_nickname().data(), static_cast<int>(this->_internal_nickname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.Model.nickname");
    target = stream->WriteStringMaybeAliased(
        18, this->_internal_nickname(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.Model)
  return target;
}

size_t Model::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.Model)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated bool synced_to_rows = 8;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_synced_to_rows_size());
    size_t data_size = 1UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated string viable_crop_ids = 16;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(viable_crop_ids_.size());
  for (int i = 0, n = viable_crop_ids_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      viable_crop_ids_.Get(i));
  }

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string crop = 2;
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string type = 10;
  if (!this->_internal_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_type());
  }

  // string nickname = 18;
  if (!this->_internal_nickname().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_nickname());
  }

  // .carbon.frontend.util.Timestamp ts = 3;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.util.Timestamp last_used_timestamp = 11;
  if (this->_internal_has_last_used_timestamp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *last_used_timestamp_);
  }

  // .carbon.frontend.util.Timestamp downloaded_timestamp = 14;
  if (this->_internal_has_downloaded_timestamp()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *downloaded_timestamp_);
  }

  // bool custom = 4;
  if (this->_internal_custom() != 0) {
    total_size += 1 + 1;
  }

  // bool pinned = 5;
  if (this->_internal_pinned() != 0) {
    total_size += 1 + 1;
  }

  // bool active = 6;
  if (this->_internal_active() != 0) {
    total_size += 1 + 1;
  }

  // bool synced = 7;
  if (this->_internal_synced() != 0) {
    total_size += 1 + 1;
  }

  // float downloading_progress = 12;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_downloading_progress = this->_internal_downloading_progress();
  uint32_t raw_downloading_progress;
  memcpy(&raw_downloading_progress, &tmp_downloading_progress, sizeof(tmp_downloading_progress));
  if (raw_downloading_progress != 0) {
    total_size += 1 + 4;
  }

  // uint64 estimated_downloading_remaining_time_ms = 13;
  if (this->_internal_estimated_downloading_remaining_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_estimated_downloading_remaining_time_ms());
  }

  // bool downloading = 9;
  if (this->_internal_downloading() != 0) {
    total_size += 1 + 1;
  }

  // bool recommended = 15;
  if (this->_internal_recommended() != 0) {
    total_size += 1 + 1;
  }

  // bool maintained = 17;
  if (this->_internal_maintained() != 0) {
    total_size += 2 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Model::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Model::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Model::GetClassData() const { return &_class_data_; }

void Model::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Model *>(to)->MergeFrom(
      static_cast<const Model &>(from));
}


void Model::MergeFrom(const Model& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.Model)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  synced_to_rows_.MergeFrom(from.synced_to_rows_);
  viable_crop_ids_.MergeFrom(from.viable_crop_ids_);
  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_type().empty()) {
    _internal_set_type(from._internal_type());
  }
  if (!from._internal_nickname().empty()) {
    _internal_set_nickname(from._internal_nickname());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_last_used_timestamp()) {
    _internal_mutable_last_used_timestamp()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_last_used_timestamp());
  }
  if (from._internal_has_downloaded_timestamp()) {
    _internal_mutable_downloaded_timestamp()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_downloaded_timestamp());
  }
  if (from._internal_custom() != 0) {
    _internal_set_custom(from._internal_custom());
  }
  if (from._internal_pinned() != 0) {
    _internal_set_pinned(from._internal_pinned());
  }
  if (from._internal_active() != 0) {
    _internal_set_active(from._internal_active());
  }
  if (from._internal_synced() != 0) {
    _internal_set_synced(from._internal_synced());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_downloading_progress = from._internal_downloading_progress();
  uint32_t raw_downloading_progress;
  memcpy(&raw_downloading_progress, &tmp_downloading_progress, sizeof(tmp_downloading_progress));
  if (raw_downloading_progress != 0) {
    _internal_set_downloading_progress(from._internal_downloading_progress());
  }
  if (from._internal_estimated_downloading_remaining_time_ms() != 0) {
    _internal_set_estimated_downloading_remaining_time_ms(from._internal_estimated_downloading_remaining_time_ms());
  }
  if (from._internal_downloading() != 0) {
    _internal_set_downloading(from._internal_downloading());
  }
  if (from._internal_recommended() != 0) {
    _internal_set_recommended(from._internal_recommended());
  }
  if (from._internal_maintained() != 0) {
    _internal_set_maintained(from._internal_maintained());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Model::CopyFrom(const Model& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.Model)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Model::IsInitialized() const {
  return true;
}

void Model::InternalSwap(Model* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  synced_to_rows_.InternalSwap(&other->synced_to_rows_);
  viable_crop_ids_.InternalSwap(&other->viable_crop_ids_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &type_, lhs_arena,
      &other->type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &nickname_, lhs_arena,
      &other->nickname_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Model, maintained_)
      + sizeof(Model::maintained_)
      - PROTOBUF_FIELD_OFFSET(Model, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Model::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[0]);
}

// ===================================================================

class SelectCropRequest::_Internal {
 public:
};

SelectCropRequest::SelectCropRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.SelectCropRequest)
}
SelectCropRequest::SelectCropRequest(const SelectCropRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.SelectCropRequest)
}

inline void SelectCropRequest::SharedCtor() {
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SelectCropRequest::~SelectCropRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.SelectCropRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SelectCropRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SelectCropRequest::ArenaDtor(void* object) {
  SelectCropRequest* _this = reinterpret_cast< SelectCropRequest* >(object);
  (void)_this;
}
void SelectCropRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SelectCropRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SelectCropRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.SelectCropRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SelectCropRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string crop_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.SelectCropRequest.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SelectCropRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.SelectCropRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string crop_id = 1;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.SelectCropRequest.crop_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_crop_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.SelectCropRequest)
  return target;
}

size_t SelectCropRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.SelectCropRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string crop_id = 1;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SelectCropRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SelectCropRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SelectCropRequest::GetClassData() const { return &_class_data_; }

void SelectCropRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SelectCropRequest *>(to)->MergeFrom(
      static_cast<const SelectCropRequest &>(from));
}


void SelectCropRequest::MergeFrom(const SelectCropRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.SelectCropRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SelectCropRequest::CopyFrom(const SelectCropRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.SelectCropRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SelectCropRequest::IsInitialized() const {
  return true;
}

void SelectCropRequest::InternalSwap(SelectCropRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SelectCropRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[1]);
}

// ===================================================================

class ListCropParameters::_Internal {
 public:
};

ListCropParameters::ListCropParameters(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.ListCropParameters)
}
ListCropParameters::ListCropParameters(const ListCropParameters& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  lang_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_lang().empty()) {
    lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_lang(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.ListCropParameters)
}

inline void ListCropParameters::SharedCtor() {
lang_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

ListCropParameters::~ListCropParameters() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.ListCropParameters)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ListCropParameters::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  lang_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ListCropParameters::ArenaDtor(void* object) {
  ListCropParameters* _this = reinterpret_cast< ListCropParameters* >(object);
  (void)_this;
}
void ListCropParameters::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ListCropParameters::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ListCropParameters::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.ListCropParameters)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lang_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ListCropParameters::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string lang = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_lang();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.ListCropParameters.lang"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ListCropParameters::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.ListCropParameters)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string lang = 1;
  if (!this->_internal_lang().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_lang().data(), static_cast<int>(this->_internal_lang().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.ListCropParameters.lang");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_lang(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.ListCropParameters)
  return target;
}

size_t ListCropParameters::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.ListCropParameters)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string lang = 1;
  if (!this->_internal_lang().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_lang());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ListCropParameters::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ListCropParameters::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ListCropParameters::GetClassData() const { return &_class_data_; }

void ListCropParameters::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ListCropParameters *>(to)->MergeFrom(
      static_cast<const ListCropParameters &>(from));
}


void ListCropParameters::MergeFrom(const ListCropParameters& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.ListCropParameters)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_lang().empty()) {
    _internal_set_lang(from._internal_lang());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ListCropParameters::CopyFrom(const ListCropParameters& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.ListCropParameters)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ListCropParameters::IsInitialized() const {
  return true;
}

void ListCropParameters::InternalSwap(ListCropParameters* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &lang_, lhs_arena,
      &other->lang_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata ListCropParameters::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[2]);
}

// ===================================================================

class EnabledCrop::_Internal {
 public:
};

EnabledCrop::EnabledCrop(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.EnabledCrop)
}
EnabledCrop::EnabledCrop(const EnabledCrop& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  carbon_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    carbon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_carbon_name().empty()) {
    carbon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_carbon_name(), 
      GetArenaForAllocation());
  }
  common_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    common_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_common_name().empty()) {
    common_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_common_name(), 
      GetArenaForAllocation());
  }
  description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_description().empty()) {
    description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_description(), 
      GetArenaForAllocation());
  }
  notes_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    notes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_notes().empty()) {
    notes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_notes(), 
      GetArenaForAllocation());
  }
  pinned_model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    pinned_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_pinned_model_id().empty()) {
    pinned_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_pinned_model_id(), 
      GetArenaForAllocation());
  }
  recommended_model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    recommended_model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_recommended_model().empty()) {
    recommended_model_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_recommended_model(), 
      GetArenaForAllocation());
  }
  created_ = from.created_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.EnabledCrop)
}

inline void EnabledCrop::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
carbon_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  carbon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
common_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  common_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
description_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
notes_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  notes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
pinned_model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  pinned_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
recommended_model_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  recommended_model_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
created_ = int64_t{0};
}

EnabledCrop::~EnabledCrop() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.EnabledCrop)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EnabledCrop::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  carbon_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  common_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  description_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  notes_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  pinned_model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  recommended_model_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void EnabledCrop::ArenaDtor(void* object) {
  EnabledCrop* _this = reinterpret_cast< EnabledCrop* >(object);
  (void)_this;
}
void EnabledCrop::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EnabledCrop::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EnabledCrop::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.EnabledCrop)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  carbon_name_.ClearToEmpty();
  common_name_.ClearToEmpty();
  description_.ClearToEmpty();
  notes_.ClearToEmpty();
  pinned_model_id_.ClearToEmpty();
  recommended_model_.ClearToEmpty();
  created_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EnabledCrop::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.EnabledCrop.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 created = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          created_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string carbon_name = 3 [deprecated = true];
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_carbon_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.EnabledCrop.carbon_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string common_name = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_common_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.EnabledCrop.common_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string description = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_description();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.EnabledCrop.description"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string notes = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_notes();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.EnabledCrop.notes"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string pinned_model_id = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_pinned_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.EnabledCrop.pinned_model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string recommended_model = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_recommended_model();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.EnabledCrop.recommended_model"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EnabledCrop::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.EnabledCrop)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.EnabledCrop.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // int64 created = 2;
  if (this->_internal_created() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_created(), target);
  }

  // string carbon_name = 3 [deprecated = true];
  if (!this->_internal_carbon_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_carbon_name().data(), static_cast<int>(this->_internal_carbon_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.EnabledCrop.carbon_name");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_carbon_name(), target);
  }

  // string common_name = 4;
  if (!this->_internal_common_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_common_name().data(), static_cast<int>(this->_internal_common_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.EnabledCrop.common_name");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_common_name(), target);
  }

  // string description = 5;
  if (!this->_internal_description().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_description().data(), static_cast<int>(this->_internal_description().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.EnabledCrop.description");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_description(), target);
  }

  // string notes = 6;
  if (!this->_internal_notes().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_notes().data(), static_cast<int>(this->_internal_notes().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.EnabledCrop.notes");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_notes(), target);
  }

  // string pinned_model_id = 7;
  if (!this->_internal_pinned_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_pinned_model_id().data(), static_cast<int>(this->_internal_pinned_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.EnabledCrop.pinned_model_id");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_pinned_model_id(), target);
  }

  // string recommended_model = 8;
  if (!this->_internal_recommended_model().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_recommended_model().data(), static_cast<int>(this->_internal_recommended_model().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.EnabledCrop.recommended_model");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_recommended_model(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.EnabledCrop)
  return target;
}

size_t EnabledCrop::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.EnabledCrop)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string carbon_name = 3 [deprecated = true];
  if (!this->_internal_carbon_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_carbon_name());
  }

  // string common_name = 4;
  if (!this->_internal_common_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_common_name());
  }

  // string description = 5;
  if (!this->_internal_description().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_description());
  }

  // string notes = 6;
  if (!this->_internal_notes().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_notes());
  }

  // string pinned_model_id = 7;
  if (!this->_internal_pinned_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_pinned_model_id());
  }

  // string recommended_model = 8;
  if (!this->_internal_recommended_model().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_recommended_model());
  }

  // int64 created = 2;
  if (this->_internal_created() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_created());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EnabledCrop::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EnabledCrop::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EnabledCrop::GetClassData() const { return &_class_data_; }

void EnabledCrop::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<EnabledCrop *>(to)->MergeFrom(
      static_cast<const EnabledCrop &>(from));
}


void EnabledCrop::MergeFrom(const EnabledCrop& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.EnabledCrop)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_carbon_name().empty()) {
    _internal_set_carbon_name(from._internal_carbon_name());
  }
  if (!from._internal_common_name().empty()) {
    _internal_set_common_name(from._internal_common_name());
  }
  if (!from._internal_description().empty()) {
    _internal_set_description(from._internal_description());
  }
  if (!from._internal_notes().empty()) {
    _internal_set_notes(from._internal_notes());
  }
  if (!from._internal_pinned_model_id().empty()) {
    _internal_set_pinned_model_id(from._internal_pinned_model_id());
  }
  if (!from._internal_recommended_model().empty()) {
    _internal_set_recommended_model(from._internal_recommended_model());
  }
  if (from._internal_created() != 0) {
    _internal_set_created(from._internal_created());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EnabledCrop::CopyFrom(const EnabledCrop& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.EnabledCrop)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EnabledCrop::IsInitialized() const {
  return true;
}

void EnabledCrop::InternalSwap(EnabledCrop* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &carbon_name_, lhs_arena,
      &other->carbon_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &common_name_, lhs_arena,
      &other->common_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &description_, lhs_arena,
      &other->description_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &notes_, lhs_arena,
      &other->notes_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &pinned_model_id_, lhs_arena,
      &other->pinned_model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &recommended_model_, lhs_arena,
      &other->recommended_model_, rhs_arena
  );
  swap(created_, other->created_);
}

::PROTOBUF_NAMESPACE_ID::Metadata EnabledCrop::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[3]);
}

// ===================================================================

class EnabledCropList::_Internal {
 public:
};

EnabledCropList::EnabledCropList(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  enabledcrops_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.EnabledCropList)
}
EnabledCropList::EnabledCropList(const EnabledCropList& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      enabledcrops_(from.enabledcrops_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.EnabledCropList)
}

inline void EnabledCropList::SharedCtor() {
}

EnabledCropList::~EnabledCropList() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.EnabledCropList)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void EnabledCropList::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void EnabledCropList::ArenaDtor(void* object) {
  EnabledCropList* _this = reinterpret_cast< EnabledCropList* >(object);
  (void)_this;
}
void EnabledCropList::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void EnabledCropList::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void EnabledCropList::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.EnabledCropList)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabledcrops_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* EnabledCropList::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_enabledcrops(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* EnabledCropList::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.EnabledCropList)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_enabledcrops_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_enabledcrops(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.EnabledCropList)
  return target;
}

size_t EnabledCropList::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.EnabledCropList)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  total_size += 1UL * this->_internal_enabledcrops_size();
  for (const auto& msg : this->enabledcrops_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData EnabledCropList::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    EnabledCropList::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*EnabledCropList::GetClassData() const { return &_class_data_; }

void EnabledCropList::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<EnabledCropList *>(to)->MergeFrom(
      static_cast<const EnabledCropList &>(from));
}


void EnabledCropList::MergeFrom(const EnabledCropList& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.EnabledCropList)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  enabledcrops_.MergeFrom(from.enabledcrops_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void EnabledCropList::CopyFrom(const EnabledCropList& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.EnabledCropList)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool EnabledCropList::IsInitialized() const {
  return true;
}

void EnabledCropList::InternalSwap(EnabledCropList* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  enabledcrops_.InternalSwap(&other->enabledcrops_);
}

::PROTOBUF_NAMESPACE_ID::Metadata EnabledCropList::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[4]);
}

// ===================================================================

class GetNextSelectedCropIDResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextSelectedCropIDResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextSelectedCropIDResponse::_Internal::ts(const GetNextSelectedCropIDResponse* msg) {
  return *msg->ts_;
}
void GetNextSelectedCropIDResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextSelectedCropIDResponse::GetNextSelectedCropIDResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetNextSelectedCropIDResponse)
}
GetNextSelectedCropIDResponse::GetNextSelectedCropIDResponse(const GetNextSelectedCropIDResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetNextSelectedCropIDResponse)
}

inline void GetNextSelectedCropIDResponse::SharedCtor() {
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextSelectedCropIDResponse::~GetNextSelectedCropIDResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetNextSelectedCropIDResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextSelectedCropIDResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextSelectedCropIDResponse::ArenaDtor(void* object) {
  GetNextSelectedCropIDResponse* _this = reinterpret_cast< GetNextSelectedCropIDResponse* >(object);
  (void)_this;
}
void GetNextSelectedCropIDResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextSelectedCropIDResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextSelectedCropIDResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetNextSelectedCropIDResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextSelectedCropIDResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.GetNextSelectedCropIDResponse.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextSelectedCropIDResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetNextSelectedCropIDResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string crop_id = 2;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.GetNextSelectedCropIDResponse.crop_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_crop_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetNextSelectedCropIDResponse)
  return target;
}

size_t GetNextSelectedCropIDResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetNextSelectedCropIDResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string crop_id = 2;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextSelectedCropIDResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextSelectedCropIDResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextSelectedCropIDResponse::GetClassData() const { return &_class_data_; }

void GetNextSelectedCropIDResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextSelectedCropIDResponse *>(to)->MergeFrom(
      static_cast<const GetNextSelectedCropIDResponse &>(from));
}


void GetNextSelectedCropIDResponse::MergeFrom(const GetNextSelectedCropIDResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetNextSelectedCropIDResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextSelectedCropIDResponse::CopyFrom(const GetNextSelectedCropIDResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetNextSelectedCropIDResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextSelectedCropIDResponse::IsInitialized() const {
  return true;
}

void GetNextSelectedCropIDResponse::InternalSwap(GetNextSelectedCropIDResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextSelectedCropIDResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[5]);
}

// ===================================================================

class PinModelRequest::_Internal {
 public:
};

PinModelRequest::PinModelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.PinModelRequest)
}
PinModelRequest::PinModelRequest(const PinModelRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&allow_pinned_crop_override_, &from.allow_pinned_crop_override_,
    static_cast<size_t>(reinterpret_cast<char*>(&p2p_) -
    reinterpret_cast<char*>(&allow_pinned_crop_override_)) + sizeof(p2p_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.PinModelRequest)
}

inline void PinModelRequest::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&allow_pinned_crop_override_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&p2p_) -
    reinterpret_cast<char*>(&allow_pinned_crop_override_)) + sizeof(p2p_));
}

PinModelRequest::~PinModelRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.PinModelRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PinModelRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void PinModelRequest::ArenaDtor(void* object) {
  PinModelRequest* _this = reinterpret_cast< PinModelRequest* >(object);
  (void)_this;
}
void PinModelRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PinModelRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PinModelRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.PinModelRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  crop_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  ::memset(&allow_pinned_crop_override_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&p2p_) -
      reinterpret_cast<char*>(&allow_pinned_crop_override_)) + sizeof(p2p_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PinModelRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.PinModelRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.PinModelRequest.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool allow_pinned_crop_override = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          allow_pinned_crop_override_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.PinModelRequest.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool p2p = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          p2p_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PinModelRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.PinModelRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.PinModelRequest.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  // string crop = 2 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.PinModelRequest.crop");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_crop(), target);
  }

  // bool allow_pinned_crop_override = 3;
  if (this->_internal_allow_pinned_crop_override() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_allow_pinned_crop_override(), target);
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.PinModelRequest.crop_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_crop_id(), target);
  }

  // bool p2p = 5;
  if (this->_internal_p2p() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_p2p(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.PinModelRequest)
  return target;
}

size_t PinModelRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.PinModelRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string crop = 2 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // bool allow_pinned_crop_override = 3;
  if (this->_internal_allow_pinned_crop_override() != 0) {
    total_size += 1 + 1;
  }

  // bool p2p = 5;
  if (this->_internal_p2p() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PinModelRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PinModelRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PinModelRequest::GetClassData() const { return &_class_data_; }

void PinModelRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PinModelRequest *>(to)->MergeFrom(
      static_cast<const PinModelRequest &>(from));
}


void PinModelRequest::MergeFrom(const PinModelRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.PinModelRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_allow_pinned_crop_override() != 0) {
    _internal_set_allow_pinned_crop_override(from._internal_allow_pinned_crop_override());
  }
  if (from._internal_p2p() != 0) {
    _internal_set_p2p(from._internal_p2p());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PinModelRequest::CopyFrom(const PinModelRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.PinModelRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PinModelRequest::IsInitialized() const {
  return true;
}

void PinModelRequest::InternalSwap(PinModelRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(PinModelRequest, p2p_)
      + sizeof(PinModelRequest::p2p_)
      - PROTOBUF_FIELD_OFFSET(PinModelRequest, allow_pinned_crop_override_)>(
          reinterpret_cast<char*>(&allow_pinned_crop_override_),
          reinterpret_cast<char*>(&other->allow_pinned_crop_override_));
}

::PROTOBUF_NAMESPACE_ID::Metadata PinModelRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[6]);
}

// ===================================================================

class UnpinModelRequest::_Internal {
 public:
};

UnpinModelRequest::UnpinModelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.UnpinModelRequest)
}
UnpinModelRequest::UnpinModelRequest(const UnpinModelRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  p2p_ = from.p2p_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.UnpinModelRequest)
}

inline void UnpinModelRequest::SharedCtor() {
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
p2p_ = false;
}

UnpinModelRequest::~UnpinModelRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.UnpinModelRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UnpinModelRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void UnpinModelRequest::ArenaDtor(void* object) {
  UnpinModelRequest* _this = reinterpret_cast< UnpinModelRequest* >(object);
  (void)_this;
}
void UnpinModelRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UnpinModelRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UnpinModelRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.UnpinModelRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  p2p_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UnpinModelRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string crop = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.UnpinModelRequest.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.UnpinModelRequest.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool p2p = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          p2p_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UnpinModelRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.UnpinModelRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string crop = 1 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.UnpinModelRequest.crop");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_crop(), target);
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.UnpinModelRequest.crop_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_crop_id(), target);
  }

  // bool p2p = 5;
  if (this->_internal_p2p() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_p2p(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.UnpinModelRequest)
  return target;
}

size_t UnpinModelRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.UnpinModelRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string crop = 1 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // bool p2p = 5;
  if (this->_internal_p2p() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UnpinModelRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UnpinModelRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UnpinModelRequest::GetClassData() const { return &_class_data_; }

void UnpinModelRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UnpinModelRequest *>(to)->MergeFrom(
      static_cast<const UnpinModelRequest &>(from));
}


void UnpinModelRequest::MergeFrom(const UnpinModelRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.UnpinModelRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_p2p() != 0) {
    _internal_set_p2p(from._internal_p2p());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UnpinModelRequest::CopyFrom(const UnpinModelRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.UnpinModelRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UnpinModelRequest::IsInitialized() const {
  return true;
}

void UnpinModelRequest::InternalSwap(UnpinModelRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  swap(p2p_, other->p2p_);
}

::PROTOBUF_NAMESPACE_ID::Metadata UnpinModelRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[7]);
}

// ===================================================================

class GetNextModelStateRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextModelStateRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextModelStateRequest::_Internal::ts(const GetNextModelStateRequest* msg) {
  return *msg->ts_;
}
void GetNextModelStateRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextModelStateRequest::GetNextModelStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetNextModelStateRequest)
}
GetNextModelStateRequest::GetNextModelStateRequest(const GetNextModelStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetNextModelStateRequest)
}

inline void GetNextModelStateRequest::SharedCtor() {
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextModelStateRequest::~GetNextModelStateRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetNextModelStateRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextModelStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextModelStateRequest::ArenaDtor(void* object) {
  GetNextModelStateRequest* _this = reinterpret_cast< GetNextModelStateRequest* >(object);
  (void)_this;
}
void GetNextModelStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextModelStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextModelStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetNextModelStateRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextModelStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string crop = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.GetNextModelStateRequest.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.GetNextModelStateRequest.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextModelStateRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetNextModelStateRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string crop = 1 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.GetNextModelStateRequest.crop");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_crop(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  // string crop_id = 3;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.GetNextModelStateRequest.crop_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_crop_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetNextModelStateRequest)
  return target;
}

size_t GetNextModelStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetNextModelStateRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string crop = 1 [deprecated = true];
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string crop_id = 3;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextModelStateRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextModelStateRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextModelStateRequest::GetClassData() const { return &_class_data_; }

void GetNextModelStateRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextModelStateRequest *>(to)->MergeFrom(
      static_cast<const GetNextModelStateRequest &>(from));
}


void GetNextModelStateRequest::MergeFrom(const GetNextModelStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetNextModelStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextModelStateRequest::CopyFrom(const GetNextModelStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetNextModelStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextModelStateRequest::IsInitialized() const {
  return true;
}

void GetNextModelStateRequest::InternalSwap(GetNextModelStateRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextModelStateRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[8]);
}

// ===================================================================

class GetNextModelStateResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextModelStateResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextModelStateResponse::_Internal::ts(const GetNextModelStateResponse* msg) {
  return *msg->ts_;
}
void GetNextModelStateResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextModelStateResponse::GetNextModelStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  models_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetNextModelStateResponse)
}
GetNextModelStateResponse::GetNextModelStateResponse(const GetNextModelStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      models_(from.models_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  current_p2p_model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    current_p2p_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_current_p2p_model_id().empty()) {
    current_p2p_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_current_p2p_model_id(), 
      GetArenaForAllocation());
  }
  current_deepweed_model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    current_deepweed_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_current_deepweed_model_id().empty()) {
    current_deepweed_model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_current_deepweed_model_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetNextModelStateResponse)
}

inline void GetNextModelStateResponse::SharedCtor() {
current_p2p_model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  current_p2p_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
current_deepweed_model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  current_deepweed_model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextModelStateResponse::~GetNextModelStateResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetNextModelStateResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextModelStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  current_p2p_model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  current_deepweed_model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextModelStateResponse::ArenaDtor(void* object) {
  GetNextModelStateResponse* _this = reinterpret_cast< GetNextModelStateResponse* >(object);
  (void)_this;
}
void GetNextModelStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextModelStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextModelStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetNextModelStateResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  models_.Clear();
  current_p2p_model_id_.ClearToEmpty();
  current_deepweed_model_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextModelStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.model.Model models = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_models(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string current_p2p_model_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_current_p2p_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.GetNextModelStateResponse.current_p2p_model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string current_deepweed_model_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_current_deepweed_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.GetNextModelStateResponse.current_deepweed_model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextModelStateResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetNextModelStateResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.Model models = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_models_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_models(i), target, stream);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  // string current_p2p_model_id = 3;
  if (!this->_internal_current_p2p_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_current_p2p_model_id().data(), static_cast<int>(this->_internal_current_p2p_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.GetNextModelStateResponse.current_p2p_model_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_current_p2p_model_id(), target);
  }

  // string current_deepweed_model_id = 4;
  if (!this->_internal_current_deepweed_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_current_deepweed_model_id().data(), static_cast<int>(this->_internal_current_deepweed_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.GetNextModelStateResponse.current_deepweed_model_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_current_deepweed_model_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetNextModelStateResponse)
  return target;
}

size_t GetNextModelStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetNextModelStateResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.Model models = 1;
  total_size += 1UL * this->_internal_models_size();
  for (const auto& msg : this->models_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string current_p2p_model_id = 3;
  if (!this->_internal_current_p2p_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_current_p2p_model_id());
  }

  // string current_deepweed_model_id = 4;
  if (!this->_internal_current_deepweed_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_current_deepweed_model_id());
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextModelStateResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextModelStateResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextModelStateResponse::GetClassData() const { return &_class_data_; }

void GetNextModelStateResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextModelStateResponse *>(to)->MergeFrom(
      static_cast<const GetNextModelStateResponse &>(from));
}


void GetNextModelStateResponse::MergeFrom(const GetNextModelStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetNextModelStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  models_.MergeFrom(from.models_);
  if (!from._internal_current_p2p_model_id().empty()) {
    _internal_set_current_p2p_model_id(from._internal_current_p2p_model_id());
  }
  if (!from._internal_current_deepweed_model_id().empty()) {
    _internal_set_current_deepweed_model_id(from._internal_current_deepweed_model_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextModelStateResponse::CopyFrom(const GetNextModelStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetNextModelStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextModelStateResponse::IsInitialized() const {
  return true;
}

void GetNextModelStateResponse::InternalSwap(GetNextModelStateResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  models_.InternalSwap(&other->models_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &current_p2p_model_id_, lhs_arena,
      &other->current_p2p_model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &current_deepweed_model_id_, lhs_arena,
      &other->current_deepweed_model_id_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextModelStateResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[9]);
}

// ===================================================================

class DownloadModelRequest::_Internal {
 public:
};

DownloadModelRequest::DownloadModelRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.DownloadModelRequest)
}
DownloadModelRequest::DownloadModelRequest(const DownloadModelRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.DownloadModelRequest)
}

inline void DownloadModelRequest::SharedCtor() {
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DownloadModelRequest::~DownloadModelRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.DownloadModelRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DownloadModelRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DownloadModelRequest::ArenaDtor(void* object) {
  DownloadModelRequest* _this = reinterpret_cast< DownloadModelRequest* >(object);
  (void)_this;
}
void DownloadModelRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DownloadModelRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DownloadModelRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.DownloadModelRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DownloadModelRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.DownloadModelRequest.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DownloadModelRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.DownloadModelRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.DownloadModelRequest.model_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_model_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.DownloadModelRequest)
  return target;
}

size_t DownloadModelRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.DownloadModelRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DownloadModelRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DownloadModelRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DownloadModelRequest::GetClassData() const { return &_class_data_; }

void DownloadModelRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DownloadModelRequest *>(to)->MergeFrom(
      static_cast<const DownloadModelRequest &>(from));
}


void DownloadModelRequest::MergeFrom(const DownloadModelRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.DownloadModelRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DownloadModelRequest::CopyFrom(const DownloadModelRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.DownloadModelRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DownloadModelRequest::IsInitialized() const {
  return true;
}

void DownloadModelRequest::InternalSwap(DownloadModelRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DownloadModelRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[10]);
}

// ===================================================================

class ModelHistoryRequest::_Internal {
 public:
  static const ::carbon::frontend::model::ModelEvent& match_filter(const ModelHistoryRequest* msg);
  static const ::carbon::frontend::util::Timestamp& ts(const ModelHistoryRequest* msg);
  static const ::carbon::frontend::model::ModelEventTypeMatcher& event_type_matcher(const ModelHistoryRequest* msg);
};

const ::carbon::frontend::model::ModelEvent&
ModelHistoryRequest::_Internal::match_filter(const ModelHistoryRequest* msg) {
  return *msg->match_filter_;
}
const ::carbon::frontend::util::Timestamp&
ModelHistoryRequest::_Internal::ts(const ModelHistoryRequest* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::model::ModelEventTypeMatcher&
ModelHistoryRequest::_Internal::event_type_matcher(const ModelHistoryRequest* msg) {
  return *msg->event_type_matcher_;
}
void ModelHistoryRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
ModelHistoryRequest::ModelHistoryRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.ModelHistoryRequest)
}
ModelHistoryRequest::ModelHistoryRequest(const ModelHistoryRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_match_filter()) {
    match_filter_ = new ::carbon::frontend::model::ModelEvent(*from.match_filter_);
  } else {
    match_filter_ = nullptr;
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_event_type_matcher()) {
    event_type_matcher_ = new ::carbon::frontend::model::ModelEventTypeMatcher(*from.event_type_matcher_);
  } else {
    event_type_matcher_ = nullptr;
  }
  ::memcpy(&start_timestamp_, &from.start_timestamp_,
    static_cast<size_t>(reinterpret_cast<char*>(&reverse_) -
    reinterpret_cast<char*>(&start_timestamp_)) + sizeof(reverse_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.ModelHistoryRequest)
}

inline void ModelHistoryRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&match_filter_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&reverse_) -
    reinterpret_cast<char*>(&match_filter_)) + sizeof(reverse_));
}

ModelHistoryRequest::~ModelHistoryRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.ModelHistoryRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelHistoryRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete match_filter_;
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete event_type_matcher_;
}

void ModelHistoryRequest::ArenaDtor(void* object) {
  ModelHistoryRequest* _this = reinterpret_cast< ModelHistoryRequest* >(object);
  (void)_this;
}
void ModelHistoryRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelHistoryRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelHistoryRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.ModelHistoryRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && match_filter_ != nullptr) {
    delete match_filter_;
  }
  match_filter_ = nullptr;
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && event_type_matcher_ != nullptr) {
    delete event_type_matcher_;
  }
  event_type_matcher_ = nullptr;
  ::memset(&start_timestamp_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&reverse_) -
      reinterpret_cast<char*>(&start_timestamp_)) + sizeof(reverse_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelHistoryRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 start_timestamp = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          start_timestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 count = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          count_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool reverse = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          reverse_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.model.ModelEvent match_filter = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_match_filter(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.model.ModelEventTypeMatcher event_type_matcher = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_event_type_matcher(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelHistoryRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.ModelHistoryRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 start_timestamp = 1;
  if (this->_internal_start_timestamp() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_start_timestamp(), target);
  }

  // int64 count = 2;
  if (this->_internal_count() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_count(), target);
  }

  // bool reverse = 3;
  if (this->_internal_reverse() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_reverse(), target);
  }

  // .carbon.frontend.model.ModelEvent match_filter = 4;
  if (this->_internal_has_match_filter()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::match_filter(this), target, stream);
  }

  // .carbon.frontend.util.Timestamp ts = 5;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.model.ModelEventTypeMatcher event_type_matcher = 6;
  if (this->_internal_has_event_type_matcher()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::event_type_matcher(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.ModelHistoryRequest)
  return target;
}

size_t ModelHistoryRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.ModelHistoryRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.model.ModelEvent match_filter = 4;
  if (this->_internal_has_match_filter()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *match_filter_);
  }

  // .carbon.frontend.util.Timestamp ts = 5;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.model.ModelEventTypeMatcher event_type_matcher = 6;
  if (this->_internal_has_event_type_matcher()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *event_type_matcher_);
  }

  // int64 start_timestamp = 1;
  if (this->_internal_start_timestamp() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_start_timestamp());
  }

  // int64 count = 2;
  if (this->_internal_count() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_count());
  }

  // bool reverse = 3;
  if (this->_internal_reverse() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelHistoryRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelHistoryRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelHistoryRequest::GetClassData() const { return &_class_data_; }

void ModelHistoryRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelHistoryRequest *>(to)->MergeFrom(
      static_cast<const ModelHistoryRequest &>(from));
}


void ModelHistoryRequest::MergeFrom(const ModelHistoryRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.ModelHistoryRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_match_filter()) {
    _internal_mutable_match_filter()->::carbon::frontend::model::ModelEvent::MergeFrom(from._internal_match_filter());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_event_type_matcher()) {
    _internal_mutable_event_type_matcher()->::carbon::frontend::model::ModelEventTypeMatcher::MergeFrom(from._internal_event_type_matcher());
  }
  if (from._internal_start_timestamp() != 0) {
    _internal_set_start_timestamp(from._internal_start_timestamp());
  }
  if (from._internal_count() != 0) {
    _internal_set_count(from._internal_count());
  }
  if (from._internal_reverse() != 0) {
    _internal_set_reverse(from._internal_reverse());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelHistoryRequest::CopyFrom(const ModelHistoryRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.ModelHistoryRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelHistoryRequest::IsInitialized() const {
  return true;
}

void ModelHistoryRequest::InternalSwap(ModelHistoryRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ModelHistoryRequest, reverse_)
      + sizeof(ModelHistoryRequest::reverse_)
      - PROTOBUF_FIELD_OFFSET(ModelHistoryRequest, match_filter_)>(
          reinterpret_cast<char*>(&match_filter_),
          reinterpret_cast<char*>(&other->match_filter_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelHistoryRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[11]);
}

// ===================================================================

class ModelEvent::_Internal {
 public:
};

ModelEvent::ModelEvent(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.ModelEvent)
}
ModelEvent::ModelEvent(const ModelEvent& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_type().empty()) {
    type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_type(), 
      GetArenaForAllocation());
  }
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  model_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_type().empty()) {
    model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_type(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  job_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_job_name().empty()) {
    job_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_job_name(), 
      GetArenaForAllocation());
  }
  model_nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_nickname().empty()) {
    model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_nickname(), 
      GetArenaForAllocation());
  }
  model_parameters_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_parameters_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_parameters().empty()) {
    model_parameters_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_parameters(), 
      GetArenaForAllocation());
  }
  time_ = from.time_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.ModelEvent)
}

inline void ModelEvent::SharedCtor() {
type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
job_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  job_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_parameters_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_parameters_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
time_ = int64_t{0};
}

ModelEvent::~ModelEvent() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.ModelEvent)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelEvent::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  job_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_nickname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_parameters_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ModelEvent::ArenaDtor(void* object) {
  ModelEvent* _this = reinterpret_cast< ModelEvent* >(object);
  (void)_this;
}
void ModelEvent::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelEvent::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelEvent::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.ModelEvent)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  type_.ClearToEmpty();
  model_id_.ClearToEmpty();
  model_type_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  job_name_.ClearToEmpty();
  model_nickname_.ClearToEmpty();
  model_parameters_.ClearToEmpty();
  time_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelEvent::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.ModelEvent.type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.ModelEvent.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_type = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_model_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.ModelEvent.model_type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.ModelEvent.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string job_name = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_job_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.ModelEvent.job_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 time = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          time_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_nickname = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_model_nickname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.ModelEvent.model_nickname"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_parameters = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          auto str = _internal_mutable_model_parameters();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.ModelEvent.model_parameters"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelEvent::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.ModelEvent)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string type = 1;
  if (!this->_internal_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_type().data(), static_cast<int>(this->_internal_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.ModelEvent.type");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_type(), target);
  }

  // string model_id = 2;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.ModelEvent.model_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_model_id(), target);
  }

  // string model_type = 3;
  if (!this->_internal_model_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_type().data(), static_cast<int>(this->_internal_model_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.ModelEvent.model_type");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_model_type(), target);
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.ModelEvent.crop_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_crop_id(), target);
  }

  // string job_name = 5;
  if (!this->_internal_job_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_job_name().data(), static_cast<int>(this->_internal_job_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.ModelEvent.job_name");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_job_name(), target);
  }

  // int64 time = 6;
  if (this->_internal_time() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(6, this->_internal_time(), target);
  }

  // string model_nickname = 7;
  if (!this->_internal_model_nickname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_nickname().data(), static_cast<int>(this->_internal_model_nickname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.ModelEvent.model_nickname");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_model_nickname(), target);
  }

  // string model_parameters = 8;
  if (!this->_internal_model_parameters().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_parameters().data(), static_cast<int>(this->_internal_model_parameters().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.ModelEvent.model_parameters");
    target = stream->WriteStringMaybeAliased(
        8, this->_internal_model_parameters(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.ModelEvent)
  return target;
}

size_t ModelEvent::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.ModelEvent)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string type = 1;
  if (!this->_internal_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_type());
  }

  // string model_id = 2;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string model_type = 3;
  if (!this->_internal_model_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_type());
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // string job_name = 5;
  if (!this->_internal_job_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_job_name());
  }

  // string model_nickname = 7;
  if (!this->_internal_model_nickname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_nickname());
  }

  // string model_parameters = 8;
  if (!this->_internal_model_parameters().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_parameters());
  }

  // int64 time = 6;
  if (this->_internal_time() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_time());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelEvent::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelEvent::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelEvent::GetClassData() const { return &_class_data_; }

void ModelEvent::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelEvent *>(to)->MergeFrom(
      static_cast<const ModelEvent &>(from));
}


void ModelEvent::MergeFrom(const ModelEvent& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.ModelEvent)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_type().empty()) {
    _internal_set_type(from._internal_type());
  }
  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_model_type().empty()) {
    _internal_set_model_type(from._internal_model_type());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (!from._internal_job_name().empty()) {
    _internal_set_job_name(from._internal_job_name());
  }
  if (!from._internal_model_nickname().empty()) {
    _internal_set_model_nickname(from._internal_model_nickname());
  }
  if (!from._internal_model_parameters().empty()) {
    _internal_set_model_parameters(from._internal_model_parameters());
  }
  if (from._internal_time() != 0) {
    _internal_set_time(from._internal_time());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelEvent::CopyFrom(const ModelEvent& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.ModelEvent)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelEvent::IsInitialized() const {
  return true;
}

void ModelEvent::InternalSwap(ModelEvent* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &type_, lhs_arena,
      &other->type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_type_, lhs_arena,
      &other->model_type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &job_name_, lhs_arena,
      &other->job_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_nickname_, lhs_arena,
      &other->model_nickname_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_parameters_, lhs_arena,
      &other->model_parameters_, rhs_arena
  );
  swap(time_, other->time_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelEvent::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[12]);
}

// ===================================================================

class ModelEventTypeMatcher::_Internal {
 public:
};

ModelEventTypeMatcher::ModelEventTypeMatcher(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.ModelEventTypeMatcher)
}
ModelEventTypeMatcher::ModelEventTypeMatcher(const ModelEventTypeMatcher& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&robot_start_, &from.robot_start_,
    static_cast<size_t>(reinterpret_cast<char*>(&parameter_change_) -
    reinterpret_cast<char*>(&robot_start_)) + sizeof(parameter_change_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.ModelEventTypeMatcher)
}

inline void ModelEventTypeMatcher::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&robot_start_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&parameter_change_) -
    reinterpret_cast<char*>(&robot_start_)) + sizeof(parameter_change_));
}

ModelEventTypeMatcher::~ModelEventTypeMatcher() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.ModelEventTypeMatcher)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelEventTypeMatcher::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ModelEventTypeMatcher::ArenaDtor(void* object) {
  ModelEventTypeMatcher* _this = reinterpret_cast< ModelEventTypeMatcher* >(object);
  (void)_this;
}
void ModelEventTypeMatcher::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelEventTypeMatcher::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelEventTypeMatcher::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.ModelEventTypeMatcher)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&robot_start_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&parameter_change_) -
      reinterpret_cast<char*>(&robot_start_)) + sizeof(parameter_change_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelEventTypeMatcher::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool robot_start = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          robot_start_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool pinned = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          pinned_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool unpinned = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          unpinned_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool recommended = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          recommended_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool activated = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          activated_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool nickname_change = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          nickname_change_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool nickname_delete = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          nickname_delete_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool default_parameter_change = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          default_parameter_change_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool parameter_change = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          parameter_change_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelEventTypeMatcher::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.ModelEventTypeMatcher)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool robot_start = 1;
  if (this->_internal_robot_start() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_robot_start(), target);
  }

  // bool pinned = 2;
  if (this->_internal_pinned() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_pinned(), target);
  }

  // bool unpinned = 3;
  if (this->_internal_unpinned() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_unpinned(), target);
  }

  // bool recommended = 4;
  if (this->_internal_recommended() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_recommended(), target);
  }

  // bool activated = 5;
  if (this->_internal_activated() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_activated(), target);
  }

  // bool nickname_change = 6;
  if (this->_internal_nickname_change() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_nickname_change(), target);
  }

  // bool nickname_delete = 7;
  if (this->_internal_nickname_delete() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_nickname_delete(), target);
  }

  // bool default_parameter_change = 8;
  if (this->_internal_default_parameter_change() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_default_parameter_change(), target);
  }

  // bool parameter_change = 9;
  if (this->_internal_parameter_change() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(9, this->_internal_parameter_change(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.ModelEventTypeMatcher)
  return target;
}

size_t ModelEventTypeMatcher::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.ModelEventTypeMatcher)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool robot_start = 1;
  if (this->_internal_robot_start() != 0) {
    total_size += 1 + 1;
  }

  // bool pinned = 2;
  if (this->_internal_pinned() != 0) {
    total_size += 1 + 1;
  }

  // bool unpinned = 3;
  if (this->_internal_unpinned() != 0) {
    total_size += 1 + 1;
  }

  // bool recommended = 4;
  if (this->_internal_recommended() != 0) {
    total_size += 1 + 1;
  }

  // bool activated = 5;
  if (this->_internal_activated() != 0) {
    total_size += 1 + 1;
  }

  // bool nickname_change = 6;
  if (this->_internal_nickname_change() != 0) {
    total_size += 1 + 1;
  }

  // bool nickname_delete = 7;
  if (this->_internal_nickname_delete() != 0) {
    total_size += 1 + 1;
  }

  // bool default_parameter_change = 8;
  if (this->_internal_default_parameter_change() != 0) {
    total_size += 1 + 1;
  }

  // bool parameter_change = 9;
  if (this->_internal_parameter_change() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelEventTypeMatcher::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelEventTypeMatcher::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelEventTypeMatcher::GetClassData() const { return &_class_data_; }

void ModelEventTypeMatcher::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelEventTypeMatcher *>(to)->MergeFrom(
      static_cast<const ModelEventTypeMatcher &>(from));
}


void ModelEventTypeMatcher::MergeFrom(const ModelEventTypeMatcher& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.ModelEventTypeMatcher)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_robot_start() != 0) {
    _internal_set_robot_start(from._internal_robot_start());
  }
  if (from._internal_pinned() != 0) {
    _internal_set_pinned(from._internal_pinned());
  }
  if (from._internal_unpinned() != 0) {
    _internal_set_unpinned(from._internal_unpinned());
  }
  if (from._internal_recommended() != 0) {
    _internal_set_recommended(from._internal_recommended());
  }
  if (from._internal_activated() != 0) {
    _internal_set_activated(from._internal_activated());
  }
  if (from._internal_nickname_change() != 0) {
    _internal_set_nickname_change(from._internal_nickname_change());
  }
  if (from._internal_nickname_delete() != 0) {
    _internal_set_nickname_delete(from._internal_nickname_delete());
  }
  if (from._internal_default_parameter_change() != 0) {
    _internal_set_default_parameter_change(from._internal_default_parameter_change());
  }
  if (from._internal_parameter_change() != 0) {
    _internal_set_parameter_change(from._internal_parameter_change());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelEventTypeMatcher::CopyFrom(const ModelEventTypeMatcher& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.ModelEventTypeMatcher)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelEventTypeMatcher::IsInitialized() const {
  return true;
}

void ModelEventTypeMatcher::InternalSwap(ModelEventTypeMatcher* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ModelEventTypeMatcher, parameter_change_)
      + sizeof(ModelEventTypeMatcher::parameter_change_)
      - PROTOBUF_FIELD_OFFSET(ModelEventTypeMatcher, robot_start_)>(
          reinterpret_cast<char*>(&robot_start_),
          reinterpret_cast<char*>(&other->robot_start_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelEventTypeMatcher::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[13]);
}

// ===================================================================

class ModelHistoryResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const ModelHistoryResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
ModelHistoryResponse::_Internal::ts(const ModelHistoryResponse* msg) {
  return *msg->ts_;
}
void ModelHistoryResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
ModelHistoryResponse::ModelHistoryResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  events_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.ModelHistoryResponse)
}
ModelHistoryResponse::ModelHistoryResponse(const ModelHistoryResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      events_(from.events_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.ModelHistoryResponse)
}

inline void ModelHistoryResponse::SharedCtor() {
ts_ = nullptr;
}

ModelHistoryResponse::~ModelHistoryResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.ModelHistoryResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelHistoryResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void ModelHistoryResponse::ArenaDtor(void* object) {
  ModelHistoryResponse* _this = reinterpret_cast< ModelHistoryResponse* >(object);
  (void)_this;
}
void ModelHistoryResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelHistoryResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelHistoryResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.ModelHistoryResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  events_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelHistoryResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.model.ModelEvent events = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_events(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelHistoryResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.ModelHistoryResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.ModelEvent events = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_events_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_events(i), target, stream);
  }

  // .carbon.frontend.util.Timestamp ts = 5;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.ModelHistoryResponse)
  return target;
}

size_t ModelHistoryResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.ModelHistoryResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.ModelEvent events = 1;
  total_size += 1UL * this->_internal_events_size();
  for (const auto& msg : this->events_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 5;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelHistoryResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelHistoryResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelHistoryResponse::GetClassData() const { return &_class_data_; }

void ModelHistoryResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelHistoryResponse *>(to)->MergeFrom(
      static_cast<const ModelHistoryResponse &>(from));
}


void ModelHistoryResponse::MergeFrom(const ModelHistoryResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.ModelHistoryResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  events_.MergeFrom(from.events_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelHistoryResponse::CopyFrom(const ModelHistoryResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.ModelHistoryResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelHistoryResponse::IsInitialized() const {
  return true;
}

void ModelHistoryResponse::InternalSwap(ModelHistoryResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  events_.InternalSwap(&other->events_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelHistoryResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[14]);
}

// ===================================================================

class GetModelNicknamesRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetModelNicknamesRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetModelNicknamesRequest::_Internal::ts(const GetModelNicknamesRequest* msg) {
  return *msg->ts_;
}
void GetModelNicknamesRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetModelNicknamesRequest::GetModelNicknamesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  model_ids_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetModelNicknamesRequest)
}
GetModelNicknamesRequest::GetModelNicknamesRequest(const GetModelNicknamesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      model_ids_(from.model_ids_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetModelNicknamesRequest)
}

inline void GetModelNicknamesRequest::SharedCtor() {
ts_ = nullptr;
}

GetModelNicknamesRequest::~GetModelNicknamesRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetModelNicknamesRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetModelNicknamesRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetModelNicknamesRequest::ArenaDtor(void* object) {
  GetModelNicknamesRequest* _this = reinterpret_cast< GetModelNicknamesRequest* >(object);
  (void)_this;
}
void GetModelNicknamesRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetModelNicknamesRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetModelNicknamesRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetModelNicknamesRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_ids_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetModelNicknamesRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string model_ids = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_model_ids();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.GetModelNicknamesRequest.model_ids"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetModelNicknamesRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetModelNicknamesRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string model_ids = 1;
  for (int i = 0, n = this->_internal_model_ids_size(); i < n; i++) {
    const auto& s = this->_internal_model_ids(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.GetModelNicknamesRequest.model_ids");
    target = stream->WriteString(1, s, target);
  }

  // .carbon.frontend.util.Timestamp ts = 5;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetModelNicknamesRequest)
  return target;
}

size_t GetModelNicknamesRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetModelNicknamesRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string model_ids = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(model_ids_.size());
  for (int i = 0, n = model_ids_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      model_ids_.Get(i));
  }

  // .carbon.frontend.util.Timestamp ts = 5;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetModelNicknamesRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetModelNicknamesRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetModelNicknamesRequest::GetClassData() const { return &_class_data_; }

void GetModelNicknamesRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetModelNicknamesRequest *>(to)->MergeFrom(
      static_cast<const GetModelNicknamesRequest &>(from));
}


void GetModelNicknamesRequest::MergeFrom(const GetModelNicknamesRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetModelNicknamesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  model_ids_.MergeFrom(from.model_ids_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetModelNicknamesRequest::CopyFrom(const GetModelNicknamesRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetModelNicknamesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetModelNicknamesRequest::IsInitialized() const {
  return true;
}

void GetModelNicknamesRequest::InternalSwap(GetModelNicknamesRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  model_ids_.InternalSwap(&other->model_ids_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetModelNicknamesRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[15]);
}

// ===================================================================

GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse() {}
GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena)
    : SuperType(arena) {}
void GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse::MergeFrom(const GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse& other) {
  MergeFromInternal(other);
}
::PROTOBUF_NAMESPACE_ID::Metadata GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[16]);
}

// ===================================================================

class GetModelNicknamesResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetModelNicknamesResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetModelNicknamesResponse::_Internal::ts(const GetModelNicknamesResponse* msg) {
  return *msg->ts_;
}
void GetModelNicknamesResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetModelNicknamesResponse::GetModelNicknamesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  model_nicknames_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetModelNicknamesResponse)
}
GetModelNicknamesResponse::GetModelNicknamesResponse(const GetModelNicknamesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_nicknames_.MergeFrom(from.model_nicknames_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetModelNicknamesResponse)
}

inline void GetModelNicknamesResponse::SharedCtor() {
ts_ = nullptr;
}

GetModelNicknamesResponse::~GetModelNicknamesResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetModelNicknamesResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetModelNicknamesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetModelNicknamesResponse::ArenaDtor(void* object) {
  GetModelNicknamesResponse* _this = reinterpret_cast< GetModelNicknamesResponse* >(object);
  (void)_this;
  _this->model_nicknames_. ~MapField();
}
inline void GetModelNicknamesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena) {
  if (arena != nullptr) {
    arena->OwnCustomDestructor(this, &GetModelNicknamesResponse::ArenaDtor);
  }
}
void GetModelNicknamesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetModelNicknamesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetModelNicknamesResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_nicknames_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetModelNicknamesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // map<string, string> model_nicknames = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(&model_nicknames_, ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetModelNicknamesResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetModelNicknamesResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // map<string, string> model_nicknames = 1;
  if (!this->_internal_model_nicknames().empty()) {
    typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_pointer
        ConstPtr;
    typedef ConstPtr SortItem;
    typedef ::PROTOBUF_NAMESPACE_ID::internal::CompareByDerefFirst<SortItem> Less;
    struct Utf8Check {
      static void Check(ConstPtr p) {
        (void)p;
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->first.data(), static_cast<int>(p->first.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry.key");
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
          p->second.data(), static_cast<int>(p->second.length()),
          ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
          "carbon.frontend.model.GetModelNicknamesResponse.ModelNicknamesEntry.value");
      }
    };

    if (stream->IsSerializationDeterministic() &&
        this->_internal_model_nicknames().size() > 1) {
      ::std::unique_ptr<SortItem[]> items(
          new SortItem[this->_internal_model_nicknames().size()]);
      typedef ::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::size_type size_type;
      size_type n = 0;
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_model_nicknames().begin();
          it != this->_internal_model_nicknames().end(); ++it, ++n) {
        items[static_cast<ptrdiff_t>(n)] = SortItem(&*it);
      }
      ::std::sort(&items[0], &items[static_cast<ptrdiff_t>(n)], Less());
      for (size_type i = 0; i < n; i++) {
        target = GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse::Funcs::InternalSerialize(1, items[static_cast<ptrdiff_t>(i)]->first, items[static_cast<ptrdiff_t>(i)]->second, target, stream);
        Utf8Check::Check(&(*items[static_cast<ptrdiff_t>(i)]));
      }
    } else {
      for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
          it = this->_internal_model_nicknames().begin();
          it != this->_internal_model_nicknames().end(); ++it) {
        target = GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse::Funcs::InternalSerialize(1, it->first, it->second, target, stream);
        Utf8Check::Check(&(*it));
      }
    }
  }

  // .carbon.frontend.util.Timestamp ts = 5;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetModelNicknamesResponse)
  return target;
}

size_t GetModelNicknamesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetModelNicknamesResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // map<string, string> model_nicknames = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(this->_internal_model_nicknames_size());
  for (::PROTOBUF_NAMESPACE_ID::Map< std::string, std::string >::const_iterator
      it = this->_internal_model_nicknames().begin();
      it != this->_internal_model_nicknames().end(); ++it) {
    total_size += GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse::Funcs::ByteSizeLong(it->first, it->second);
  }

  // .carbon.frontend.util.Timestamp ts = 5;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetModelNicknamesResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetModelNicknamesResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetModelNicknamesResponse::GetClassData() const { return &_class_data_; }

void GetModelNicknamesResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetModelNicknamesResponse *>(to)->MergeFrom(
      static_cast<const GetModelNicknamesResponse &>(from));
}


void GetModelNicknamesResponse::MergeFrom(const GetModelNicknamesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetModelNicknamesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  model_nicknames_.MergeFrom(from.model_nicknames_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetModelNicknamesResponse::CopyFrom(const GetModelNicknamesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetModelNicknamesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetModelNicknamesResponse::IsInitialized() const {
  return true;
}

void GetModelNicknamesResponse::InternalSwap(GetModelNicknamesResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  model_nicknames_.InternalSwap(&other->model_nicknames_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetModelNicknamesResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[17]);
}

// ===================================================================

class SetModelNicknameRequest::_Internal {
 public:
};

SetModelNicknameRequest::SetModelNicknameRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.SetModelNicknameRequest)
}
SetModelNicknameRequest::SetModelNicknameRequest(const SetModelNicknameRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  model_nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_nickname().empty()) {
    model_nickname_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_nickname(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.SetModelNicknameRequest)
}

inline void SetModelNicknameRequest::SharedCtor() {
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_nickname_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_nickname_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SetModelNicknameRequest::~SetModelNicknameRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.SetModelNicknameRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetModelNicknameRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_nickname_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetModelNicknameRequest::ArenaDtor(void* object) {
  SetModelNicknameRequest* _this = reinterpret_cast< SetModelNicknameRequest* >(object);
  (void)_this;
}
void SetModelNicknameRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetModelNicknameRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetModelNicknameRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.SetModelNicknameRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  model_id_.ClearToEmpty();
  model_nickname_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetModelNicknameRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string model_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.SetModelNicknameRequest.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_nickname = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_model_nickname();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.SetModelNicknameRequest.model_nickname"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetModelNicknameRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.SetModelNicknameRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.SetModelNicknameRequest.model_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_model_id(), target);
  }

  // string model_nickname = 2;
  if (!this->_internal_model_nickname().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_nickname().data(), static_cast<int>(this->_internal_model_nickname().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.SetModelNicknameRequest.model_nickname");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_model_nickname(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.SetModelNicknameRequest)
  return target;
}

size_t SetModelNicknameRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.SetModelNicknameRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string model_id = 1;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  // string model_nickname = 2;
  if (!this->_internal_model_nickname().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_nickname());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetModelNicknameRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetModelNicknameRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetModelNicknameRequest::GetClassData() const { return &_class_data_; }

void SetModelNicknameRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetModelNicknameRequest *>(to)->MergeFrom(
      static_cast<const SetModelNicknameRequest &>(from));
}


void SetModelNicknameRequest::MergeFrom(const SetModelNicknameRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.SetModelNicknameRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  if (!from._internal_model_nickname().empty()) {
    _internal_set_model_nickname(from._internal_model_nickname());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetModelNicknameRequest::CopyFrom(const SetModelNicknameRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.SetModelNicknameRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetModelNicknameRequest::IsInitialized() const {
  return true;
}

void SetModelNicknameRequest::InternalSwap(SetModelNicknameRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_nickname_, lhs_arena,
      &other->model_nickname_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SetModelNicknameRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[18]);
}

// ===================================================================

class CropModelPair::_Internal {
 public:
};

CropModelPair::CropModelPair(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.CropModelPair)
}
CropModelPair::CropModelPair(const CropModelPair& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_id().empty()) {
    model_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.CropModelPair)
}

inline void CropModelPair::SharedCtor() {
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

CropModelPair::~CropModelPair() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.CropModelPair)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CropModelPair::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void CropModelPair::ArenaDtor(void* object) {
  CropModelPair* _this = reinterpret_cast< CropModelPair* >(object);
  (void)_this;
}
void CropModelPair::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CropModelPair::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CropModelPair::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.CropModelPair)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_id_.ClearToEmpty();
  model_id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CropModelPair::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string crop_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.CropModelPair.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_model_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.CropModelPair.model_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CropModelPair::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.CropModelPair)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string crop_id = 1;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.CropModelPair.crop_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_crop_id(), target);
  }

  // string model_id = 2;
  if (!this->_internal_model_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_id().data(), static_cast<int>(this->_internal_model_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.CropModelPair.model_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_model_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.CropModelPair)
  return target;
}

size_t CropModelPair::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.CropModelPair)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string crop_id = 1;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // string model_id = 2;
  if (!this->_internal_model_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CropModelPair::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CropModelPair::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CropModelPair::GetClassData() const { return &_class_data_; }

void CropModelPair::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CropModelPair *>(to)->MergeFrom(
      static_cast<const CropModelPair &>(from));
}


void CropModelPair::MergeFrom(const CropModelPair& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.CropModelPair)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (!from._internal_model_id().empty()) {
    _internal_set_model_id(from._internal_model_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CropModelPair::CopyFrom(const CropModelPair& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.CropModelPair)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CropModelPair::IsInitialized() const {
  return true;
}

void CropModelPair::InternalSwap(CropModelPair* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_id_, lhs_arena,
      &other->model_id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata CropModelPair::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[19]);
}

// ===================================================================

class RefreshDefaultModelParametersRequest::_Internal {
 public:
};

RefreshDefaultModelParametersRequest::RefreshDefaultModelParametersRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  cropmodelpairs_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.RefreshDefaultModelParametersRequest)
}
RefreshDefaultModelParametersRequest::RefreshDefaultModelParametersRequest(const RefreshDefaultModelParametersRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      cropmodelpairs_(from.cropmodelpairs_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.RefreshDefaultModelParametersRequest)
}

inline void RefreshDefaultModelParametersRequest::SharedCtor() {
}

RefreshDefaultModelParametersRequest::~RefreshDefaultModelParametersRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.RefreshDefaultModelParametersRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RefreshDefaultModelParametersRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RefreshDefaultModelParametersRequest::ArenaDtor(void* object) {
  RefreshDefaultModelParametersRequest* _this = reinterpret_cast< RefreshDefaultModelParametersRequest* >(object);
  (void)_this;
}
void RefreshDefaultModelParametersRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RefreshDefaultModelParametersRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RefreshDefaultModelParametersRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.RefreshDefaultModelParametersRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cropmodelpairs_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RefreshDefaultModelParametersRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.model.CropModelPair cropModelPairs = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_cropmodelpairs(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RefreshDefaultModelParametersRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.RefreshDefaultModelParametersRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.CropModelPair cropModelPairs = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_cropmodelpairs_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_cropmodelpairs(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.RefreshDefaultModelParametersRequest)
  return target;
}

size_t RefreshDefaultModelParametersRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.RefreshDefaultModelParametersRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.CropModelPair cropModelPairs = 1;
  total_size += 1UL * this->_internal_cropmodelpairs_size();
  for (const auto& msg : this->cropmodelpairs_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RefreshDefaultModelParametersRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RefreshDefaultModelParametersRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RefreshDefaultModelParametersRequest::GetClassData() const { return &_class_data_; }

void RefreshDefaultModelParametersRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RefreshDefaultModelParametersRequest *>(to)->MergeFrom(
      static_cast<const RefreshDefaultModelParametersRequest &>(from));
}


void RefreshDefaultModelParametersRequest::MergeFrom(const RefreshDefaultModelParametersRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.RefreshDefaultModelParametersRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  cropmodelpairs_.MergeFrom(from.cropmodelpairs_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RefreshDefaultModelParametersRequest::CopyFrom(const RefreshDefaultModelParametersRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.RefreshDefaultModelParametersRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RefreshDefaultModelParametersRequest::IsInitialized() const {
  return true;
}

void RefreshDefaultModelParametersRequest::InternalSwap(RefreshDefaultModelParametersRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  cropmodelpairs_.InternalSwap(&other->cropmodelpairs_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RefreshDefaultModelParametersRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[20]);
}

// ===================================================================

class SyncCropIDsRequest::_Internal {
 public:
};

SyncCropIDsRequest::SyncCropIDsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.SyncCropIDsRequest)
}
SyncCropIDsRequest::SyncCropIDsRequest(const SyncCropIDsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  force_cache_refresh_ = from.force_cache_refresh_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.SyncCropIDsRequest)
}

inline void SyncCropIDsRequest::SharedCtor() {
force_cache_refresh_ = false;
}

SyncCropIDsRequest::~SyncCropIDsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.SyncCropIDsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SyncCropIDsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SyncCropIDsRequest::ArenaDtor(void* object) {
  SyncCropIDsRequest* _this = reinterpret_cast< SyncCropIDsRequest* >(object);
  (void)_this;
}
void SyncCropIDsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SyncCropIDsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SyncCropIDsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.SyncCropIDsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  force_cache_refresh_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SyncCropIDsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool force_cache_refresh = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          force_cache_refresh_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SyncCropIDsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.SyncCropIDsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool force_cache_refresh = 1;
  if (this->_internal_force_cache_refresh() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_force_cache_refresh(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.SyncCropIDsRequest)
  return target;
}

size_t SyncCropIDsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.SyncCropIDsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool force_cache_refresh = 1;
  if (this->_internal_force_cache_refresh() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SyncCropIDsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SyncCropIDsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SyncCropIDsRequest::GetClassData() const { return &_class_data_; }

void SyncCropIDsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SyncCropIDsRequest *>(to)->MergeFrom(
      static_cast<const SyncCropIDsRequest &>(from));
}


void SyncCropIDsRequest::MergeFrom(const SyncCropIDsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.SyncCropIDsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_force_cache_refresh() != 0) {
    _internal_set_force_cache_refresh(from._internal_force_cache_refresh());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SyncCropIDsRequest::CopyFrom(const SyncCropIDsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.SyncCropIDsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SyncCropIDsRequest::IsInitialized() const {
  return true;
}

void SyncCropIDsRequest::InternalSwap(SyncCropIDsRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(force_cache_refresh_, other->force_cache_refresh_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SyncCropIDsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[21]);
}

// ===================================================================

class GetNextEnabledCropsRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextEnabledCropsRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextEnabledCropsRequest::_Internal::ts(const GetNextEnabledCropsRequest* msg) {
  return *msg->ts_;
}
void GetNextEnabledCropsRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextEnabledCropsRequest::GetNextEnabledCropsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetNextEnabledCropsRequest)
}
GetNextEnabledCropsRequest::GetNextEnabledCropsRequest(const GetNextEnabledCropsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  lang_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_lang().empty()) {
    lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_lang(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetNextEnabledCropsRequest)
}

inline void GetNextEnabledCropsRequest::SharedCtor() {
lang_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextEnabledCropsRequest::~GetNextEnabledCropsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetNextEnabledCropsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextEnabledCropsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  lang_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextEnabledCropsRequest::ArenaDtor(void* object) {
  GetNextEnabledCropsRequest* _this = reinterpret_cast< GetNextEnabledCropsRequest* >(object);
  (void)_this;
}
void GetNextEnabledCropsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextEnabledCropsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextEnabledCropsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetNextEnabledCropsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lang_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextEnabledCropsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string lang = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_lang();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.GetNextEnabledCropsRequest.lang"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextEnabledCropsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetNextEnabledCropsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string lang = 2;
  if (!this->_internal_lang().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_lang().data(), static_cast<int>(this->_internal_lang().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.GetNextEnabledCropsRequest.lang");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_lang(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetNextEnabledCropsRequest)
  return target;
}

size_t GetNextEnabledCropsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetNextEnabledCropsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string lang = 2;
  if (!this->_internal_lang().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_lang());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextEnabledCropsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextEnabledCropsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextEnabledCropsRequest::GetClassData() const { return &_class_data_; }

void GetNextEnabledCropsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextEnabledCropsRequest *>(to)->MergeFrom(
      static_cast<const GetNextEnabledCropsRequest &>(from));
}


void GetNextEnabledCropsRequest::MergeFrom(const GetNextEnabledCropsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetNextEnabledCropsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_lang().empty()) {
    _internal_set_lang(from._internal_lang());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextEnabledCropsRequest::CopyFrom(const GetNextEnabledCropsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetNextEnabledCropsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextEnabledCropsRequest::IsInitialized() const {
  return true;
}

void GetNextEnabledCropsRequest::InternalSwap(GetNextEnabledCropsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &lang_, lhs_arena,
      &other->lang_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextEnabledCropsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[22]);
}

// ===================================================================

class GetNextEnabledCropsResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextEnabledCropsResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextEnabledCropsResponse::_Internal::ts(const GetNextEnabledCropsResponse* msg) {
  return *msg->ts_;
}
void GetNextEnabledCropsResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextEnabledCropsResponse::GetNextEnabledCropsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  enabledcrops_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetNextEnabledCropsResponse)
}
GetNextEnabledCropsResponse::GetNextEnabledCropsResponse(const GetNextEnabledCropsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      enabledcrops_(from.enabledcrops_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetNextEnabledCropsResponse)
}

inline void GetNextEnabledCropsResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextEnabledCropsResponse::~GetNextEnabledCropsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetNextEnabledCropsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextEnabledCropsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextEnabledCropsResponse::ArenaDtor(void* object) {
  GetNextEnabledCropsResponse* _this = reinterpret_cast< GetNextEnabledCropsResponse* >(object);
  (void)_this;
}
void GetNextEnabledCropsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextEnabledCropsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextEnabledCropsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetNextEnabledCropsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabledcrops_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextEnabledCropsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_enabledcrops(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextEnabledCropsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetNextEnabledCropsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_enabledcrops_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_enabledcrops(i), target, stream);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetNextEnabledCropsResponse)
  return target;
}

size_t GetNextEnabledCropsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetNextEnabledCropsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  total_size += 1UL * this->_internal_enabledcrops_size();
  for (const auto& msg : this->enabledcrops_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextEnabledCropsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextEnabledCropsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextEnabledCropsResponse::GetClassData() const { return &_class_data_; }

void GetNextEnabledCropsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextEnabledCropsResponse *>(to)->MergeFrom(
      static_cast<const GetNextEnabledCropsResponse &>(from));
}


void GetNextEnabledCropsResponse::MergeFrom(const GetNextEnabledCropsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetNextEnabledCropsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  enabledcrops_.MergeFrom(from.enabledcrops_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextEnabledCropsResponse::CopyFrom(const GetNextEnabledCropsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetNextEnabledCropsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextEnabledCropsResponse::IsInitialized() const {
  return true;
}

void GetNextEnabledCropsResponse::InternalSwap(GetNextEnabledCropsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  enabledcrops_.InternalSwap(&other->enabledcrops_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextEnabledCropsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[23]);
}

// ===================================================================

class GetNextCaptureCropsRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextCaptureCropsRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextCaptureCropsRequest::_Internal::ts(const GetNextCaptureCropsRequest* msg) {
  return *msg->ts_;
}
void GetNextCaptureCropsRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextCaptureCropsRequest::GetNextCaptureCropsRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetNextCaptureCropsRequest)
}
GetNextCaptureCropsRequest::GetNextCaptureCropsRequest(const GetNextCaptureCropsRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  lang_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_lang().empty()) {
    lang_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_lang(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetNextCaptureCropsRequest)
}

inline void GetNextCaptureCropsRequest::SharedCtor() {
lang_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  lang_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextCaptureCropsRequest::~GetNextCaptureCropsRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetNextCaptureCropsRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextCaptureCropsRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  lang_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextCaptureCropsRequest::ArenaDtor(void* object) {
  GetNextCaptureCropsRequest* _this = reinterpret_cast< GetNextCaptureCropsRequest* >(object);
  (void)_this;
}
void GetNextCaptureCropsRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextCaptureCropsRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextCaptureCropsRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetNextCaptureCropsRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  lang_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextCaptureCropsRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string lang = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_lang();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.model.GetNextCaptureCropsRequest.lang"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextCaptureCropsRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetNextCaptureCropsRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string lang = 2;
  if (!this->_internal_lang().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_lang().data(), static_cast<int>(this->_internal_lang().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.model.GetNextCaptureCropsRequest.lang");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_lang(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetNextCaptureCropsRequest)
  return target;
}

size_t GetNextCaptureCropsRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetNextCaptureCropsRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string lang = 2;
  if (!this->_internal_lang().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_lang());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextCaptureCropsRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextCaptureCropsRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextCaptureCropsRequest::GetClassData() const { return &_class_data_; }

void GetNextCaptureCropsRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextCaptureCropsRequest *>(to)->MergeFrom(
      static_cast<const GetNextCaptureCropsRequest &>(from));
}


void GetNextCaptureCropsRequest::MergeFrom(const GetNextCaptureCropsRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetNextCaptureCropsRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_lang().empty()) {
    _internal_set_lang(from._internal_lang());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextCaptureCropsRequest::CopyFrom(const GetNextCaptureCropsRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetNextCaptureCropsRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextCaptureCropsRequest::IsInitialized() const {
  return true;
}

void GetNextCaptureCropsRequest::InternalSwap(GetNextCaptureCropsRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &lang_, lhs_arena,
      &other->lang_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextCaptureCropsRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[24]);
}

// ===================================================================

class GetNextCaptureCropsResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextCaptureCropsResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextCaptureCropsResponse::_Internal::ts(const GetNextCaptureCropsResponse* msg) {
  return *msg->ts_;
}
void GetNextCaptureCropsResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextCaptureCropsResponse::GetNextCaptureCropsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  enabledcrops_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.model.GetNextCaptureCropsResponse)
}
GetNextCaptureCropsResponse::GetNextCaptureCropsResponse(const GetNextCaptureCropsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      enabledcrops_(from.enabledcrops_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.model.GetNextCaptureCropsResponse)
}

inline void GetNextCaptureCropsResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextCaptureCropsResponse::~GetNextCaptureCropsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.model.GetNextCaptureCropsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextCaptureCropsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextCaptureCropsResponse::ArenaDtor(void* object) {
  GetNextCaptureCropsResponse* _this = reinterpret_cast< GetNextCaptureCropsResponse* >(object);
  (void)_this;
}
void GetNextCaptureCropsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextCaptureCropsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextCaptureCropsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.model.GetNextCaptureCropsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enabledcrops_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextCaptureCropsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_enabledcrops(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextCaptureCropsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.model.GetNextCaptureCropsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_enabledcrops_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_enabledcrops(i), target, stream);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.model.GetNextCaptureCropsResponse)
  return target;
}

size_t GetNextCaptureCropsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.model.GetNextCaptureCropsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.model.EnabledCrop enabledCrops = 1;
  total_size += 1UL * this->_internal_enabledcrops_size();
  for (const auto& msg : this->enabledcrops_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextCaptureCropsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextCaptureCropsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextCaptureCropsResponse::GetClassData() const { return &_class_data_; }

void GetNextCaptureCropsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextCaptureCropsResponse *>(to)->MergeFrom(
      static_cast<const GetNextCaptureCropsResponse &>(from));
}


void GetNextCaptureCropsResponse::MergeFrom(const GetNextCaptureCropsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.model.GetNextCaptureCropsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  enabledcrops_.MergeFrom(from.enabledcrops_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextCaptureCropsResponse::CopyFrom(const GetNextCaptureCropsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.model.GetNextCaptureCropsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextCaptureCropsResponse::IsInitialized() const {
  return true;
}

void GetNextCaptureCropsResponse::InternalSwap(GetNextCaptureCropsResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  enabledcrops_.InternalSwap(&other->enabledcrops_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextCaptureCropsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodel_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodel_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodel_2eproto[25]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace model
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::Model* Arena::CreateMaybeMessage< ::carbon::frontend::model::Model >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::Model >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::SelectCropRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::SelectCropRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::SelectCropRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::ListCropParameters* Arena::CreateMaybeMessage< ::carbon::frontend::model::ListCropParameters >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::ListCropParameters >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::EnabledCrop* Arena::CreateMaybeMessage< ::carbon::frontend::model::EnabledCrop >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::EnabledCrop >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::EnabledCropList* Arena::CreateMaybeMessage< ::carbon::frontend::model::EnabledCropList >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::EnabledCropList >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetNextSelectedCropIDResponse* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetNextSelectedCropIDResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetNextSelectedCropIDResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::PinModelRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::PinModelRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::PinModelRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::UnpinModelRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::UnpinModelRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::UnpinModelRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetNextModelStateRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetNextModelStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetNextModelStateRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetNextModelStateResponse* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetNextModelStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetNextModelStateResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::DownloadModelRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::DownloadModelRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::DownloadModelRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::ModelHistoryRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::ModelHistoryRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::ModelHistoryRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::ModelEvent* Arena::CreateMaybeMessage< ::carbon::frontend::model::ModelEvent >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::ModelEvent >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::ModelEventTypeMatcher* Arena::CreateMaybeMessage< ::carbon::frontend::model::ModelEventTypeMatcher >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::ModelEventTypeMatcher >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::ModelHistoryResponse* Arena::CreateMaybeMessage< ::carbon::frontend::model::ModelHistoryResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::ModelHistoryResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetModelNicknamesRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetModelNicknamesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetModelNicknamesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetModelNicknamesResponse_ModelNicknamesEntry_DoNotUse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetModelNicknamesResponse* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetModelNicknamesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetModelNicknamesResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::SetModelNicknameRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::SetModelNicknameRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::SetModelNicknameRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::CropModelPair* Arena::CreateMaybeMessage< ::carbon::frontend::model::CropModelPair >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::CropModelPair >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::RefreshDefaultModelParametersRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::RefreshDefaultModelParametersRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::RefreshDefaultModelParametersRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::SyncCropIDsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::SyncCropIDsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::SyncCropIDsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetNextEnabledCropsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetNextEnabledCropsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetNextEnabledCropsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetNextEnabledCropsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetNextEnabledCropsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetNextEnabledCropsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetNextCaptureCropsRequest* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetNextCaptureCropsRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetNextCaptureCropsRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::model::GetNextCaptureCropsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::model::GetNextCaptureCropsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::model::GetNextCaptureCropsResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

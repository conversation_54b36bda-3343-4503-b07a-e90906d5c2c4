// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/robot.proto

#include "frontend/proto/robot.pb.h"
#include "frontend/proto/robot.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace robot {

static const char* RobotDiagnosticService_method_names[] = {
  "/carbon.frontend.robot.RobotDiagnosticService/GetNextRobotState",
};

std::unique_ptr< RobotDiagnosticService::Stub> RobotDiagnosticService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< RobotDiagnosticService::Stub> stub(new RobotDiagnosticService::Stub(channel, options));
  return stub;
}

RobotDiagnosticService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextRobotState_(RobotDiagnosticService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status RobotDiagnosticService::Stub::GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::carbon::frontend::robot::TimestampedRobotState* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::robot::RobotStateRequest, ::carbon::frontend::robot::TimestampedRobotState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextRobotState_, context, request, response);
}

void RobotDiagnosticService::Stub::async::GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::robot::RobotStateRequest, ::carbon::frontend::robot::TimestampedRobotState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextRobotState_, context, request, response, std::move(f));
}

void RobotDiagnosticService::Stub::async::GetNextRobotState(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextRobotState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::robot::TimestampedRobotState>* RobotDiagnosticService::Stub::PrepareAsyncGetNextRobotStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::robot::TimestampedRobotState, ::carbon::frontend::robot::RobotStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextRobotState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::robot::TimestampedRobotState>* RobotDiagnosticService::Stub::AsyncGetNextRobotStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::robot::RobotStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextRobotStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

RobotDiagnosticService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RobotDiagnosticService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RobotDiagnosticService::Service, ::carbon::frontend::robot::RobotStateRequest, ::carbon::frontend::robot::TimestampedRobotState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RobotDiagnosticService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::robot::RobotStateRequest* req,
             ::carbon::frontend::robot::TimestampedRobotState* resp) {
               return service->GetNextRobotState(ctx, req, resp);
             }, this)));
}

RobotDiagnosticService::Service::~Service() {
}

::grpc::Status RobotDiagnosticService::Service::GetNextRobotState(::grpc::ServerContext* context, const ::carbon::frontend::robot::RobotStateRequest* request, ::carbon::frontend::robot::TimestampedRobotState* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace robot


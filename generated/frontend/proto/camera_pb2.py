# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/camera.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/camera.proto',
  package='carbon.frontend.camera',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1b\x66rontend/proto/camera.proto\x12\x16\x63\x61rbon.frontend.camera\x1a\x19\x66rontend/proto/util.proto\"\x1f\n\rCameraRequest\x12\x0e\n\x06\x63\x61m_id\x18\x01 \x01(\t\"\x9b\x02\n\x06\x43\x61mera\x12\x12\n\nrow_number\x18\x01 \x01(\r\x12\x11\n\tcamera_id\x18\x02 \x01(\t\x12\x30\n\x04type\x18\x03 \x01(\x0e\x32\".carbon.frontend.camera.CameraType\x12\x16\n\x0e\x61uto_focusable\x18\x04 \x01(\x08\x12\x13\n\x0bstream_host\x18\x05 \x01(\t\x12\x13\n\x0bstream_port\x18\x06 \x01(\r\x12\r\n\x05width\x18\x07 \x01(\r\x12\x0e\n\x06height\x18\x08 \x01(\r\x12\x11\n\ttranspose\x18\t \x01(\x08\x12\x11\n\tconnected\x18\n \x01(\x08\x12\x31\n\x08rtc_info\x18\x0b \x01(\x0b\x32\x1f.carbon.frontend.camera.RTCInfo\"-\n\x07RTCInfo\x12\x0f\n\x07host_id\x18\x01 \x01(\t\x12\x11\n\tstream_id\x18\x02 \x01(\t\"j\n\nCameraList\x12/\n\x07\x63\x61meras\x18\x01 \x03(\x0b\x32\x1e.carbon.frontend.camera.Camera\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"c\n\x11\x43\x61meraListRequest\x12\x30\n\x04type\x18\x01 \x01(\x0e\x32\".carbon.frontend.camera.CameraType\x12\x1c\n\x14include_disconnected\x18\x02 \x01(\x08\"\x94\x01\n\x15NextCameraListRequest\x12\x30\n\x04type\x18\x01 \x01(\x0e\x32\".carbon.frontend.camera.CameraType\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x1c\n\x14include_disconnected\x18\x03 \x01(\x08*;\n\nCameraType\x12\x07\n\x03\x41NY\x10\x00\x12\x0b\n\x07PREDICT\x10\x01\x12\n\n\x06TARGET\x10\x02\x12\x0b\n\x07KILLCAM\x10\x03\x32\xd7\x01\n\rCameraService\x12^\n\rGetCameraList\x12).carbon.frontend.camera.CameraListRequest\x1a\".carbon.frontend.camera.CameraList\x12\x66\n\x11GetNextCameraList\x12-.carbon.frontend.camera.NextCameraListRequest\x1a\".carbon.frontend.camera.CameraListB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_CAMERATYPE = _descriptor.EnumDescriptor(
  name='CameraType',
  full_name='carbon.frontend.camera.CameraType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ANY', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PREDICT', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='KILLCAM', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=808,
  serialized_end=867,
)
_sym_db.RegisterEnumDescriptor(_CAMERATYPE)

CameraType = enum_type_wrapper.EnumTypeWrapper(_CAMERATYPE)
ANY = 0
PREDICT = 1
TARGET = 2
KILLCAM = 3



_CAMERAREQUEST = _descriptor.Descriptor(
  name='CameraRequest',
  full_name='carbon.frontend.camera.CameraRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.camera.CameraRequest.cam_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=82,
  serialized_end=113,
)


_CAMERA = _descriptor.Descriptor(
  name='Camera',
  full_name='carbon.frontend.camera.Camera',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_number', full_name='carbon.frontend.camera.Camera.row_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='camera_id', full_name='carbon.frontend.camera.Camera.camera_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.frontend.camera.Camera.type', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='auto_focusable', full_name='carbon.frontend.camera.Camera.auto_focusable', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stream_host', full_name='carbon.frontend.camera.Camera.stream_host', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stream_port', full_name='carbon.frontend.camera.Camera.stream_port', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width', full_name='carbon.frontend.camera.Camera.width', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='carbon.frontend.camera.Camera.height', index=7,
      number=8, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='transpose', full_name='carbon.frontend.camera.Camera.transpose', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connected', full_name='carbon.frontend.camera.Camera.connected', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rtc_info', full_name='carbon.frontend.camera.Camera.rtc_info', index=10,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=116,
  serialized_end=399,
)


_RTCINFO = _descriptor.Descriptor(
  name='RTCInfo',
  full_name='carbon.frontend.camera.RTCInfo',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='host_id', full_name='carbon.frontend.camera.RTCInfo.host_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stream_id', full_name='carbon.frontend.camera.RTCInfo.stream_id', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=401,
  serialized_end=446,
)


_CAMERALIST = _descriptor.Descriptor(
  name='CameraList',
  full_name='carbon.frontend.camera.CameraList',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='cameras', full_name='carbon.frontend.camera.CameraList.cameras', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.camera.CameraList.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=448,
  serialized_end=554,
)


_CAMERALISTREQUEST = _descriptor.Descriptor(
  name='CameraListRequest',
  full_name='carbon.frontend.camera.CameraListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.frontend.camera.CameraListRequest.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='include_disconnected', full_name='carbon.frontend.camera.CameraListRequest.include_disconnected', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=556,
  serialized_end=655,
)


_NEXTCAMERALISTREQUEST = _descriptor.Descriptor(
  name='NextCameraListRequest',
  full_name='carbon.frontend.camera.NextCameraListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.frontend.camera.NextCameraListRequest.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.camera.NextCameraListRequest.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='include_disconnected', full_name='carbon.frontend.camera.NextCameraListRequest.include_disconnected', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=658,
  serialized_end=806,
)

_CAMERA.fields_by_name['type'].enum_type = _CAMERATYPE
_CAMERA.fields_by_name['rtc_info'].message_type = _RTCINFO
_CAMERALIST.fields_by_name['cameras'].message_type = _CAMERA
_CAMERALIST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_CAMERALISTREQUEST.fields_by_name['type'].enum_type = _CAMERATYPE
_NEXTCAMERALISTREQUEST.fields_by_name['type'].enum_type = _CAMERATYPE
_NEXTCAMERALISTREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['CameraRequest'] = _CAMERAREQUEST
DESCRIPTOR.message_types_by_name['Camera'] = _CAMERA
DESCRIPTOR.message_types_by_name['RTCInfo'] = _RTCINFO
DESCRIPTOR.message_types_by_name['CameraList'] = _CAMERALIST
DESCRIPTOR.message_types_by_name['CameraListRequest'] = _CAMERALISTREQUEST
DESCRIPTOR.message_types_by_name['NextCameraListRequest'] = _NEXTCAMERALISTREQUEST
DESCRIPTOR.enum_types_by_name['CameraType'] = _CAMERATYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

CameraRequest = _reflection.GeneratedProtocolMessageType('CameraRequest', (_message.Message,), {
  'DESCRIPTOR' : _CAMERAREQUEST,
  '__module__' : 'frontend.proto.camera_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.camera.CameraRequest)
  })
_sym_db.RegisterMessage(CameraRequest)

Camera = _reflection.GeneratedProtocolMessageType('Camera', (_message.Message,), {
  'DESCRIPTOR' : _CAMERA,
  '__module__' : 'frontend.proto.camera_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.camera.Camera)
  })
_sym_db.RegisterMessage(Camera)

RTCInfo = _reflection.GeneratedProtocolMessageType('RTCInfo', (_message.Message,), {
  'DESCRIPTOR' : _RTCINFO,
  '__module__' : 'frontend.proto.camera_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.camera.RTCInfo)
  })
_sym_db.RegisterMessage(RTCInfo)

CameraList = _reflection.GeneratedProtocolMessageType('CameraList', (_message.Message,), {
  'DESCRIPTOR' : _CAMERALIST,
  '__module__' : 'frontend.proto.camera_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.camera.CameraList)
  })
_sym_db.RegisterMessage(CameraList)

CameraListRequest = _reflection.GeneratedProtocolMessageType('CameraListRequest', (_message.Message,), {
  'DESCRIPTOR' : _CAMERALISTREQUEST,
  '__module__' : 'frontend.proto.camera_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.camera.CameraListRequest)
  })
_sym_db.RegisterMessage(CameraListRequest)

NextCameraListRequest = _reflection.GeneratedProtocolMessageType('NextCameraListRequest', (_message.Message,), {
  'DESCRIPTOR' : _NEXTCAMERALISTREQUEST,
  '__module__' : 'frontend.proto.camera_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.camera.NextCameraListRequest)
  })
_sym_db.RegisterMessage(NextCameraListRequest)


DESCRIPTOR._options = None

_CAMERASERVICE = _descriptor.ServiceDescriptor(
  name='CameraService',
  full_name='carbon.frontend.camera.CameraService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=870,
  serialized_end=1085,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetCameraList',
    full_name='carbon.frontend.camera.CameraService.GetCameraList',
    index=0,
    containing_service=None,
    input_type=_CAMERALISTREQUEST,
    output_type=_CAMERALIST,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextCameraList',
    full_name='carbon.frontend.camera.CameraService.GetNextCameraList',
    index=1,
    containing_service=None,
    input_type=_NEXTCAMERALISTREQUEST,
    output_type=_CAMERALIST,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CAMERASERVICE)

DESCRIPTOR.services_by_name['CameraService'] = _CAMERASERVICE

# @@protoc_insertion_point(module_scope)

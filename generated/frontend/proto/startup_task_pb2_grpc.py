# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import startup_task_pb2 as frontend_dot_proto_dot_startup__task__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class StartupTaskServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextTasks = channel.unary_unary(
                '/carbon.frontend.startup_task.StartupTaskService/GetNextTasks',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_startup__task__pb2.GetNextTasksResponse.FromString,
                )
        self.MarkTaskComplete = channel.unary_unary(
                '/carbon.frontend.startup_task.StartupTaskService/MarkTaskComplete',
                request_serializer=frontend_dot_proto_dot_startup__task__pb2.MarkTaskCompleteRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_startup__task__pb2.MarkTaskCompleteResponse.FromString,
                )


class StartupTaskServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextTasks(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def MarkTaskComplete(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_StartupTaskServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextTasks': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextTasks,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_startup__task__pb2.GetNextTasksResponse.SerializeToString,
            ),
            'MarkTaskComplete': grpc.unary_unary_rpc_method_handler(
                    servicer.MarkTaskComplete,
                    request_deserializer=frontend_dot_proto_dot_startup__task__pb2.MarkTaskCompleteRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_startup__task__pb2.MarkTaskCompleteResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.startup_task.StartupTaskService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class StartupTaskService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextTasks(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.startup_task.StartupTaskService/GetNextTasks',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_startup__task__pb2.GetNextTasksResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def MarkTaskComplete(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.startup_task.StartupTaskService/MarkTaskComplete',
            frontend_dot_proto_dot_startup__task__pb2.MarkTaskCompleteRequest.SerializeToString,
            frontend_dot_proto_dot_startup__task__pb2.MarkTaskCompleteResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

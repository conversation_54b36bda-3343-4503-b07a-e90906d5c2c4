// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/tractor.proto

#include "frontend/proto/tractor.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace tractor {
constexpr TractorIfState::TractorIfState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : expected_(false)
  , connected_(false){}
struct TractorIfStateDefaultTypeInternal {
  constexpr TractorIfStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TractorIfStateDefaultTypeInternal() {}
  union {
    TractorIfState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TractorIfStateDefaultTypeInternal _TractorIfState_default_instance_;
constexpr TractorSafetyState::TractorSafetyState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : is_safe_(false)
  , enforced_(false){}
struct TractorSafetyStateDefaultTypeInternal {
  constexpr TractorSafetyStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TractorSafetyStateDefaultTypeInternal() {}
  union {
    TractorSafetyState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TractorSafetyStateDefaultTypeInternal _TractorSafetyState_default_instance_;
constexpr GetNextTractorIfStateResponse::GetNextTractorIfStateResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , state_(nullptr){}
struct GetNextTractorIfStateResponseDefaultTypeInternal {
  constexpr GetNextTractorIfStateResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextTractorIfStateResponseDefaultTypeInternal() {}
  union {
    GetNextTractorIfStateResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextTractorIfStateResponseDefaultTypeInternal _GetNextTractorIfStateResponse_default_instance_;
constexpr GetNextTractorSafetyStateResponse::GetNextTractorSafetyStateResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , state_(nullptr){}
struct GetNextTractorSafetyStateResponseDefaultTypeInternal {
  constexpr GetNextTractorSafetyStateResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextTractorSafetyStateResponseDefaultTypeInternal() {}
  union {
    GetNextTractorSafetyStateResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextTractorSafetyStateResponseDefaultTypeInternal _GetNextTractorSafetyStateResponse_default_instance_;
constexpr SetEnforcementPolicyRequest::SetEnforcementPolicyRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : enforced_(false){}
struct SetEnforcementPolicyRequestDefaultTypeInternal {
  constexpr SetEnforcementPolicyRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetEnforcementPolicyRequestDefaultTypeInternal() {}
  union {
    SetEnforcementPolicyRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetEnforcementPolicyRequestDefaultTypeInternal _SetEnforcementPolicyRequest_default_instance_;
constexpr SetEnforcementPolicyResponse::SetEnforcementPolicyResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetEnforcementPolicyResponseDefaultTypeInternal {
  constexpr SetEnforcementPolicyResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetEnforcementPolicyResponseDefaultTypeInternal() {}
  union {
    SetEnforcementPolicyResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetEnforcementPolicyResponseDefaultTypeInternal _SetEnforcementPolicyResponse_default_instance_;
}  // namespace tractor
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2ftractor_2eproto[6];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2ftractor_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2ftractor_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2ftractor_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::TractorIfState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::TractorIfState, expected_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::TractorIfState, connected_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::TractorSafetyState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::TractorSafetyState, is_safe_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::TractorSafetyState, enforced_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::GetNextTractorIfStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::GetNextTractorIfStateResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::GetNextTractorIfStateResponse, state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::GetNextTractorSafetyStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::GetNextTractorSafetyStateResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::GetNextTractorSafetyStateResponse, state_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::SetEnforcementPolicyRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::SetEnforcementPolicyRequest, enforced_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::tractor::SetEnforcementPolicyResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::tractor::TractorIfState)},
  { 8, -1, -1, sizeof(::carbon::frontend::tractor::TractorSafetyState)},
  { 16, -1, -1, sizeof(::carbon::frontend::tractor::GetNextTractorIfStateResponse)},
  { 24, -1, -1, sizeof(::carbon::frontend::tractor::GetNextTractorSafetyStateResponse)},
  { 32, -1, -1, sizeof(::carbon::frontend::tractor::SetEnforcementPolicyRequest)},
  { 39, -1, -1, sizeof(::carbon::frontend::tractor::SetEnforcementPolicyResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::tractor::_TractorIfState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::tractor::_TractorSafetyState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::tractor::_GetNextTractorIfStateResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::tractor::_GetNextTractorSafetyStateResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::tractor::_SetEnforcementPolicyRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::tractor::_SetEnforcementPolicyResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2ftractor_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\034frontend/proto/tractor.proto\022\027carbon.f"
  "rontend.tractor\032\031frontend/proto/util.pro"
  "to\"5\n\016TractorIfState\022\020\n\010expected\030\001 \001(\010\022\021"
  "\n\tconnected\030\002 \001(\010\"7\n\022TractorSafetyState\022"
  "\017\n\007is_safe\030\001 \001(\010\022\020\n\010enforced\030\002 \001(\010\"\204\001\n\035G"
  "etNextTractorIfStateResponse\022+\n\002ts\030\001 \001(\013"
  "2\037.carbon.frontend.util.Timestamp\0226\n\005sta"
  "te\030\002 \001(\0132\'.carbon.frontend.tractor.Tract"
  "orIfState\"\214\001\n!GetNextTractorSafetyStateR"
  "esponse\022+\n\002ts\030\001 \001(\0132\037.carbon.frontend.ut"
  "il.Timestamp\022:\n\005state\030\002 \001(\0132+.carbon.fro"
  "ntend.tractor.TractorSafetyState\"/\n\033SetE"
  "nforcementPolicyRequest\022\020\n\010enforced\030\001 \001("
  "\010\"\036\n\034SetEnforcementPolicyResponse2\202\003\n\016Tr"
  "actorService\022p\n\025GetNextTractorIfState\022\037."
  "carbon.frontend.util.Timestamp\0326.carbon."
  "frontend.tractor.GetNextTractorIfStateRe"
  "sponse\022x\n\031GetNextTractorSafetyState\022\037.ca"
  "rbon.frontend.util.Timestamp\032:.carbon.fr"
  "ontend.tractor.GetNextTractorSafetyState"
  "Response\022\203\001\n\024SetEnforcementPolicy\0224.carb"
  "on.frontend.tractor.SetEnforcementPolicy"
  "Request\0325.carbon.frontend.tractor.SetEnf"
  "orcementPolicyResponseB\020Z\016proto/frontend"
  "b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2ftractor_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2ftractor_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ftractor_2eproto = {
  false, false, 968, descriptor_table_protodef_frontend_2fproto_2ftractor_2eproto, "frontend/proto/tractor.proto", 
  &descriptor_table_frontend_2fproto_2ftractor_2eproto_once, descriptor_table_frontend_2fproto_2ftractor_2eproto_deps, 1, 6,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2ftractor_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2ftractor_2eproto, file_level_enum_descriptors_frontend_2fproto_2ftractor_2eproto, file_level_service_descriptors_frontend_2fproto_2ftractor_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2ftractor_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2ftractor_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2ftractor_2eproto(&descriptor_table_frontend_2fproto_2ftractor_2eproto);
namespace carbon {
namespace frontend {
namespace tractor {

// ===================================================================

class TractorIfState::_Internal {
 public:
};

TractorIfState::TractorIfState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.tractor.TractorIfState)
}
TractorIfState::TractorIfState(const TractorIfState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&expected_, &from.expected_,
    static_cast<size_t>(reinterpret_cast<char*>(&connected_) -
    reinterpret_cast<char*>(&expected_)) + sizeof(connected_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.tractor.TractorIfState)
}

inline void TractorIfState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&expected_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&connected_) -
    reinterpret_cast<char*>(&expected_)) + sizeof(connected_));
}

TractorIfState::~TractorIfState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.tractor.TractorIfState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TractorIfState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TractorIfState::ArenaDtor(void* object) {
  TractorIfState* _this = reinterpret_cast< TractorIfState* >(object);
  (void)_this;
}
void TractorIfState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TractorIfState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TractorIfState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.tractor.TractorIfState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&expected_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&connected_) -
      reinterpret_cast<char*>(&expected_)) + sizeof(connected_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TractorIfState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool expected = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          expected_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool connected = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          connected_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TractorIfState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.tractor.TractorIfState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool expected = 1;
  if (this->_internal_expected() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_expected(), target);
  }

  // bool connected = 2;
  if (this->_internal_connected() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_connected(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.tractor.TractorIfState)
  return target;
}

size_t TractorIfState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.tractor.TractorIfState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool expected = 1;
  if (this->_internal_expected() != 0) {
    total_size += 1 + 1;
  }

  // bool connected = 2;
  if (this->_internal_connected() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TractorIfState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TractorIfState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TractorIfState::GetClassData() const { return &_class_data_; }

void TractorIfState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TractorIfState *>(to)->MergeFrom(
      static_cast<const TractorIfState &>(from));
}


void TractorIfState::MergeFrom(const TractorIfState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.tractor.TractorIfState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_expected() != 0) {
    _internal_set_expected(from._internal_expected());
  }
  if (from._internal_connected() != 0) {
    _internal_set_connected(from._internal_connected());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TractorIfState::CopyFrom(const TractorIfState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.tractor.TractorIfState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TractorIfState::IsInitialized() const {
  return true;
}

void TractorIfState::InternalSwap(TractorIfState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TractorIfState, connected_)
      + sizeof(TractorIfState::connected_)
      - PROTOBUF_FIELD_OFFSET(TractorIfState, expected_)>(
          reinterpret_cast<char*>(&expected_),
          reinterpret_cast<char*>(&other->expected_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TractorIfState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftractor_2eproto_getter, &descriptor_table_frontend_2fproto_2ftractor_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftractor_2eproto[0]);
}

// ===================================================================

class TractorSafetyState::_Internal {
 public:
};

TractorSafetyState::TractorSafetyState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.tractor.TractorSafetyState)
}
TractorSafetyState::TractorSafetyState(const TractorSafetyState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&is_safe_, &from.is_safe_,
    static_cast<size_t>(reinterpret_cast<char*>(&enforced_) -
    reinterpret_cast<char*>(&is_safe_)) + sizeof(enforced_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.tractor.TractorSafetyState)
}

inline void TractorSafetyState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&is_safe_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&enforced_) -
    reinterpret_cast<char*>(&is_safe_)) + sizeof(enforced_));
}

TractorSafetyState::~TractorSafetyState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.tractor.TractorSafetyState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TractorSafetyState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void TractorSafetyState::ArenaDtor(void* object) {
  TractorSafetyState* _this = reinterpret_cast< TractorSafetyState* >(object);
  (void)_this;
}
void TractorSafetyState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TractorSafetyState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TractorSafetyState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.tractor.TractorSafetyState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&is_safe_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&enforced_) -
      reinterpret_cast<char*>(&is_safe_)) + sizeof(enforced_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TractorSafetyState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool is_safe = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          is_safe_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool enforced = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          enforced_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TractorSafetyState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.tractor.TractorSafetyState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool is_safe = 1;
  if (this->_internal_is_safe() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_is_safe(), target);
  }

  // bool enforced = 2;
  if (this->_internal_enforced() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_enforced(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.tractor.TractorSafetyState)
  return target;
}

size_t TractorSafetyState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.tractor.TractorSafetyState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool is_safe = 1;
  if (this->_internal_is_safe() != 0) {
    total_size += 1 + 1;
  }

  // bool enforced = 2;
  if (this->_internal_enforced() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TractorSafetyState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TractorSafetyState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TractorSafetyState::GetClassData() const { return &_class_data_; }

void TractorSafetyState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TractorSafetyState *>(to)->MergeFrom(
      static_cast<const TractorSafetyState &>(from));
}


void TractorSafetyState::MergeFrom(const TractorSafetyState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.tractor.TractorSafetyState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_is_safe() != 0) {
    _internal_set_is_safe(from._internal_is_safe());
  }
  if (from._internal_enforced() != 0) {
    _internal_set_enforced(from._internal_enforced());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TractorSafetyState::CopyFrom(const TractorSafetyState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.tractor.TractorSafetyState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TractorSafetyState::IsInitialized() const {
  return true;
}

void TractorSafetyState::InternalSwap(TractorSafetyState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TractorSafetyState, enforced_)
      + sizeof(TractorSafetyState::enforced_)
      - PROTOBUF_FIELD_OFFSET(TractorSafetyState, is_safe_)>(
          reinterpret_cast<char*>(&is_safe_),
          reinterpret_cast<char*>(&other->is_safe_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TractorSafetyState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftractor_2eproto_getter, &descriptor_table_frontend_2fproto_2ftractor_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftractor_2eproto[1]);
}

// ===================================================================

class GetNextTractorIfStateResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextTractorIfStateResponse* msg);
  static const ::carbon::frontend::tractor::TractorIfState& state(const GetNextTractorIfStateResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextTractorIfStateResponse::_Internal::ts(const GetNextTractorIfStateResponse* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::tractor::TractorIfState&
GetNextTractorIfStateResponse::_Internal::state(const GetNextTractorIfStateResponse* msg) {
  return *msg->state_;
}
void GetNextTractorIfStateResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextTractorIfStateResponse::GetNextTractorIfStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.tractor.GetNextTractorIfStateResponse)
}
GetNextTractorIfStateResponse::GetNextTractorIfStateResponse(const GetNextTractorIfStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_state()) {
    state_ = new ::carbon::frontend::tractor::TractorIfState(*from.state_);
  } else {
    state_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.tractor.GetNextTractorIfStateResponse)
}

inline void GetNextTractorIfStateResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(state_));
}

GetNextTractorIfStateResponse::~GetNextTractorIfStateResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.tractor.GetNextTractorIfStateResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextTractorIfStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete state_;
}

void GetNextTractorIfStateResponse::ArenaDtor(void* object) {
  GetNextTractorIfStateResponse* _this = reinterpret_cast< GetNextTractorIfStateResponse* >(object);
  (void)_this;
}
void GetNextTractorIfStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextTractorIfStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextTractorIfStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.tractor.GetNextTractorIfStateResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && state_ != nullptr) {
    delete state_;
  }
  state_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextTractorIfStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.tractor.TractorIfState state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextTractorIfStateResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.tractor.GetNextTractorIfStateResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.tractor.TractorIfState state = 2;
  if (this->_internal_has_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::state(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.tractor.GetNextTractorIfStateResponse)
  return target;
}

size_t GetNextTractorIfStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.tractor.GetNextTractorIfStateResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.tractor.TractorIfState state = 2;
  if (this->_internal_has_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *state_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextTractorIfStateResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextTractorIfStateResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextTractorIfStateResponse::GetClassData() const { return &_class_data_; }

void GetNextTractorIfStateResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextTractorIfStateResponse *>(to)->MergeFrom(
      static_cast<const GetNextTractorIfStateResponse &>(from));
}


void GetNextTractorIfStateResponse::MergeFrom(const GetNextTractorIfStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.tractor.GetNextTractorIfStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_state()) {
    _internal_mutable_state()->::carbon::frontend::tractor::TractorIfState::MergeFrom(from._internal_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextTractorIfStateResponse::CopyFrom(const GetNextTractorIfStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.tractor.GetNextTractorIfStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextTractorIfStateResponse::IsInitialized() const {
  return true;
}

void GetNextTractorIfStateResponse::InternalSwap(GetNextTractorIfStateResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextTractorIfStateResponse, state_)
      + sizeof(GetNextTractorIfStateResponse::state_)
      - PROTOBUF_FIELD_OFFSET(GetNextTractorIfStateResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextTractorIfStateResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftractor_2eproto_getter, &descriptor_table_frontend_2fproto_2ftractor_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftractor_2eproto[2]);
}

// ===================================================================

class GetNextTractorSafetyStateResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextTractorSafetyStateResponse* msg);
  static const ::carbon::frontend::tractor::TractorSafetyState& state(const GetNextTractorSafetyStateResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextTractorSafetyStateResponse::_Internal::ts(const GetNextTractorSafetyStateResponse* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::tractor::TractorSafetyState&
GetNextTractorSafetyStateResponse::_Internal::state(const GetNextTractorSafetyStateResponse* msg) {
  return *msg->state_;
}
void GetNextTractorSafetyStateResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextTractorSafetyStateResponse::GetNextTractorSafetyStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
}
GetNextTractorSafetyStateResponse::GetNextTractorSafetyStateResponse(const GetNextTractorSafetyStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_state()) {
    state_ = new ::carbon::frontend::tractor::TractorSafetyState(*from.state_);
  } else {
    state_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
}

inline void GetNextTractorSafetyStateResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&state_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(state_));
}

GetNextTractorSafetyStateResponse::~GetNextTractorSafetyStateResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextTractorSafetyStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete state_;
}

void GetNextTractorSafetyStateResponse::ArenaDtor(void* object) {
  GetNextTractorSafetyStateResponse* _this = reinterpret_cast< GetNextTractorSafetyStateResponse* >(object);
  (void)_this;
}
void GetNextTractorSafetyStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextTractorSafetyStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextTractorSafetyStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && state_ != nullptr) {
    delete state_;
  }
  state_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextTractorSafetyStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.tractor.TractorSafetyState state = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_state(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextTractorSafetyStateResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.tractor.TractorSafetyState state = 2;
  if (this->_internal_has_state()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::state(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
  return target;
}

size_t GetNextTractorSafetyStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.tractor.TractorSafetyState state = 2;
  if (this->_internal_has_state()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *state_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextTractorSafetyStateResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextTractorSafetyStateResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextTractorSafetyStateResponse::GetClassData() const { return &_class_data_; }

void GetNextTractorSafetyStateResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextTractorSafetyStateResponse *>(to)->MergeFrom(
      static_cast<const GetNextTractorSafetyStateResponse &>(from));
}


void GetNextTractorSafetyStateResponse::MergeFrom(const GetNextTractorSafetyStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_state()) {
    _internal_mutable_state()->::carbon::frontend::tractor::TractorSafetyState::MergeFrom(from._internal_state());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextTractorSafetyStateResponse::CopyFrom(const GetNextTractorSafetyStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.tractor.GetNextTractorSafetyStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextTractorSafetyStateResponse::IsInitialized() const {
  return true;
}

void GetNextTractorSafetyStateResponse::InternalSwap(GetNextTractorSafetyStateResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(GetNextTractorSafetyStateResponse, state_)
      + sizeof(GetNextTractorSafetyStateResponse::state_)
      - PROTOBUF_FIELD_OFFSET(GetNextTractorSafetyStateResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextTractorSafetyStateResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftractor_2eproto_getter, &descriptor_table_frontend_2fproto_2ftractor_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftractor_2eproto[3]);
}

// ===================================================================

class SetEnforcementPolicyRequest::_Internal {
 public:
};

SetEnforcementPolicyRequest::SetEnforcementPolicyRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.tractor.SetEnforcementPolicyRequest)
}
SetEnforcementPolicyRequest::SetEnforcementPolicyRequest(const SetEnforcementPolicyRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  enforced_ = from.enforced_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.tractor.SetEnforcementPolicyRequest)
}

inline void SetEnforcementPolicyRequest::SharedCtor() {
enforced_ = false;
}

SetEnforcementPolicyRequest::~SetEnforcementPolicyRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.tractor.SetEnforcementPolicyRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetEnforcementPolicyRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SetEnforcementPolicyRequest::ArenaDtor(void* object) {
  SetEnforcementPolicyRequest* _this = reinterpret_cast< SetEnforcementPolicyRequest* >(object);
  (void)_this;
}
void SetEnforcementPolicyRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetEnforcementPolicyRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetEnforcementPolicyRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.tractor.SetEnforcementPolicyRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  enforced_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetEnforcementPolicyRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bool enforced = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          enforced_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetEnforcementPolicyRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.tractor.SetEnforcementPolicyRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bool enforced = 1;
  if (this->_internal_enforced() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(1, this->_internal_enforced(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.tractor.SetEnforcementPolicyRequest)
  return target;
}

size_t SetEnforcementPolicyRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.tractor.SetEnforcementPolicyRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // bool enforced = 1;
  if (this->_internal_enforced() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetEnforcementPolicyRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetEnforcementPolicyRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetEnforcementPolicyRequest::GetClassData() const { return &_class_data_; }

void SetEnforcementPolicyRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetEnforcementPolicyRequest *>(to)->MergeFrom(
      static_cast<const SetEnforcementPolicyRequest &>(from));
}


void SetEnforcementPolicyRequest::MergeFrom(const SetEnforcementPolicyRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.tractor.SetEnforcementPolicyRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_enforced() != 0) {
    _internal_set_enforced(from._internal_enforced());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetEnforcementPolicyRequest::CopyFrom(const SetEnforcementPolicyRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.tractor.SetEnforcementPolicyRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetEnforcementPolicyRequest::IsInitialized() const {
  return true;
}

void SetEnforcementPolicyRequest::InternalSwap(SetEnforcementPolicyRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(enforced_, other->enforced_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetEnforcementPolicyRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftractor_2eproto_getter, &descriptor_table_frontend_2fproto_2ftractor_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftractor_2eproto[4]);
}

// ===================================================================

class SetEnforcementPolicyResponse::_Internal {
 public:
};

SetEnforcementPolicyResponse::SetEnforcementPolicyResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.tractor.SetEnforcementPolicyResponse)
}
SetEnforcementPolicyResponse::SetEnforcementPolicyResponse(const SetEnforcementPolicyResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.tractor.SetEnforcementPolicyResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetEnforcementPolicyResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetEnforcementPolicyResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetEnforcementPolicyResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2ftractor_2eproto_getter, &descriptor_table_frontend_2fproto_2ftractor_2eproto_once,
      file_level_metadata_frontend_2fproto_2ftractor_2eproto[5]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace tractor
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::tractor::TractorIfState* Arena::CreateMaybeMessage< ::carbon::frontend::tractor::TractorIfState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::tractor::TractorIfState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::tractor::TractorSafetyState* Arena::CreateMaybeMessage< ::carbon::frontend::tractor::TractorSafetyState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::tractor::TractorSafetyState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::tractor::GetNextTractorIfStateResponse* Arena::CreateMaybeMessage< ::carbon::frontend::tractor::GetNextTractorIfStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::tractor::GetNextTractorIfStateResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse* Arena::CreateMaybeMessage< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::tractor::GetNextTractorSafetyStateResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::tractor::SetEnforcementPolicyRequest* Arena::CreateMaybeMessage< ::carbon::frontend::tractor::SetEnforcementPolicyRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::tractor::SetEnforcementPolicyRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::tractor::SetEnforcementPolicyResponse* Arena::CreateMaybeMessage< ::carbon::frontend::tractor::SetEnforcementPolicyResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::tractor::SetEnforcementPolicyResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

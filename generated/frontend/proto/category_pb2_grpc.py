# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import category_pb2 as frontend_dot_proto_dot_category__pb2


class CategoryServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextCategoryData = channel.unary_unary(
                '/carbon.frontend.category.CategoryService/GetNextCategoryData',
                request_serializer=frontend_dot_proto_dot_category__pb2.GetNextCategoryDataRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_category__pb2.GetNextCategoryDataResponse.FromString,
                )


class CategoryServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextCategoryData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CategoryServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextCategoryData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextCategoryData,
                    request_deserializer=frontend_dot_proto_dot_category__pb2.GetNextCategoryDataRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_category__pb2.GetNextCategoryDataResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.category.CategoryService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CategoryService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextCategoryData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.category.CategoryService/GetNextCategoryData',
            frontend_dot_proto_dot_category__pb2.GetNextCategoryDataRequest.SerializeToString,
            frontend_dot_proto_dot_category__pb2.GetNextCategoryDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

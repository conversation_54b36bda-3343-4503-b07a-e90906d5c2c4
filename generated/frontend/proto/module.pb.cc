// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/module.proto

#include "frontend/proto/module.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace module {
constexpr ModuleIdentity::ModuleIdentity(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(0u){}
struct ModuleIdentityDefaultTypeInternal {
  constexpr ModuleIdentityDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModuleIdentityDefaultTypeInternal() {}
  union {
    ModuleIdentity _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModuleIdentityDefaultTypeInternal _ModuleIdentity_default_instance_;
constexpr GetNextModulesListRequest::GetNextModulesListRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct GetNextModulesListRequestDefaultTypeInternal {
  constexpr GetNextModulesListRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextModulesListRequestDefaultTypeInternal() {}
  union {
    GetNextModulesListRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextModulesListRequestDefaultTypeInternal _GetNextModulesListRequest_default_instance_;
constexpr GetNextModulesListResponse::GetNextModulesListResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : assigned_modules_()
  , unassigned_modules_()
  , unset_serial_modules_()
  , ts_(nullptr){}
struct GetNextModulesListResponseDefaultTypeInternal {
  constexpr GetNextModulesListResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextModulesListResponseDefaultTypeInternal() {}
  union {
    GetNextModulesListResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextModulesListResponseDefaultTypeInternal _GetNextModulesListResponse_default_instance_;
constexpr GetNextActiveModulesRequest::GetNextActiveModulesRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct GetNextActiveModulesRequestDefaultTypeInternal {
  constexpr GetNextActiveModulesRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveModulesRequestDefaultTypeInternal() {}
  union {
    GetNextActiveModulesRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveModulesRequestDefaultTypeInternal _GetNextActiveModulesRequest_default_instance_;
constexpr GetNextActiveModulesResponse::GetNextActiveModulesResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : active_modules_()
  , ts_(nullptr){}
struct GetNextActiveModulesResponseDefaultTypeInternal {
  constexpr GetNextActiveModulesResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveModulesResponseDefaultTypeInternal() {}
  union {
    GetNextActiveModulesResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveModulesResponseDefaultTypeInternal _GetNextActiveModulesResponse_default_instance_;
constexpr IdentifyModuleRequest::IdentifyModuleRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : module_identity_(nullptr){}
struct IdentifyModuleRequestDefaultTypeInternal {
  constexpr IdentifyModuleRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~IdentifyModuleRequestDefaultTypeInternal() {}
  union {
    IdentifyModuleRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT IdentifyModuleRequestDefaultTypeInternal _IdentifyModuleRequest_default_instance_;
constexpr AssignModuleRequest::AssignModuleRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : module_identity_(nullptr){}
struct AssignModuleRequestDefaultTypeInternal {
  constexpr AssignModuleRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AssignModuleRequestDefaultTypeInternal() {}
  union {
    AssignModuleRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AssignModuleRequestDefaultTypeInternal _AssignModuleRequest_default_instance_;
constexpr ClearModuleAssignmentRequest::ClearModuleAssignmentRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : module_identity_(nullptr){}
struct ClearModuleAssignmentRequestDefaultTypeInternal {
  constexpr ClearModuleAssignmentRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ClearModuleAssignmentRequestDefaultTypeInternal() {}
  union {
    ClearModuleAssignmentRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ClearModuleAssignmentRequestDefaultTypeInternal _ClearModuleAssignmentRequest_default_instance_;
constexpr SetModuleSerialRequest::SetModuleSerialRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : new_serial_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , module_identity_(nullptr){}
struct SetModuleSerialRequestDefaultTypeInternal {
  constexpr SetModuleSerialRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetModuleSerialRequestDefaultTypeInternal() {}
  union {
    SetModuleSerialRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetModuleSerialRequestDefaultTypeInternal _SetModuleSerialRequest_default_instance_;
constexpr ModuleDefinition::ModuleDefinition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : module_id_(0u)
  , module_spacing_mm_(0)
  , disabled_(false){}
struct ModuleDefinitionDefaultTypeInternal {
  constexpr ModuleDefinitionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModuleDefinitionDefaultTypeInternal() {}
  union {
    ModuleDefinition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModuleDefinitionDefaultTypeInternal _ModuleDefinition_default_instance_;
constexpr RowDefinition::RowDefinition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : modules_()
  , row_id_(0u)
  , row_spacing_mm_(0){}
struct RowDefinitionDefaultTypeInternal {
  constexpr RowDefinitionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RowDefinitionDefaultTypeInternal() {}
  union {
    RowDefinition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RowDefinitionDefaultTypeInternal _RowDefinition_default_instance_;
constexpr BarDefinition::BarDefinition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : bar_length_mm_(0u)
  , folding_(false){}
struct BarDefinitionDefaultTypeInternal {
  constexpr BarDefinitionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~BarDefinitionDefaultTypeInternal() {}
  union {
    BarDefinition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT BarDefinitionDefaultTypeInternal _BarDefinition_default_instance_;
constexpr RobotDefinition::RobotDefinition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : rows_()
  , bar_definition_(nullptr){}
struct RobotDefinitionDefaultTypeInternal {
  constexpr RobotDefinitionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RobotDefinitionDefaultTypeInternal() {}
  union {
    RobotDefinition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RobotDefinitionDefaultTypeInternal _RobotDefinition_default_instance_;
constexpr Preset::Preset(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uuid_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , display_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , definition_(nullptr){}
struct PresetDefaultTypeInternal {
  constexpr PresetDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PresetDefaultTypeInternal() {}
  union {
    Preset _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PresetDefaultTypeInternal _Preset_default_instance_;
constexpr GetPresetsListRequest::GetPresetsListRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : language_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetPresetsListRequestDefaultTypeInternal {
  constexpr GetPresetsListRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPresetsListRequestDefaultTypeInternal() {}
  union {
    GetPresetsListRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPresetsListRequestDefaultTypeInternal _GetPresetsListRequest_default_instance_;
constexpr GetPresetsListResponse::GetPresetsListResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : presets_(){}
struct GetPresetsListResponseDefaultTypeInternal {
  constexpr GetPresetsListResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetPresetsListResponseDefaultTypeInternal() {}
  union {
    GetPresetsListResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetPresetsListResponseDefaultTypeInternal _GetPresetsListResponse_default_instance_;
constexpr GetCurrentRobotDefinitionResponse::GetCurrentRobotDefinitionResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : current_definition_(nullptr){}
struct GetCurrentRobotDefinitionResponseDefaultTypeInternal {
  constexpr GetCurrentRobotDefinitionResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetCurrentRobotDefinitionResponseDefaultTypeInternal() {}
  union {
    GetCurrentRobotDefinitionResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetCurrentRobotDefinitionResponseDefaultTypeInternal _GetCurrentRobotDefinitionResponse_default_instance_;
constexpr SetCurrentRobotDefinitionRequest::SetCurrentRobotDefinitionRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : current_definition_(nullptr){}
struct SetCurrentRobotDefinitionRequestDefaultTypeInternal {
  constexpr SetCurrentRobotDefinitionRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetCurrentRobotDefinitionRequestDefaultTypeInternal() {}
  union {
    SetCurrentRobotDefinitionRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetCurrentRobotDefinitionRequestDefaultTypeInternal _SetCurrentRobotDefinitionRequest_default_instance_;
}  // namespace module
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fmodule_2eproto[18];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fmodule_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fmodule_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fmodule_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ModuleIdentity, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ModuleIdentity, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ModuleIdentity, serial_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextModulesListRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextModulesListRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextModulesListResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextModulesListResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextModulesListResponse, assigned_modules_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextModulesListResponse, unassigned_modules_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextModulesListResponse, unset_serial_modules_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextActiveModulesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextActiveModulesRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextActiveModulesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextActiveModulesResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetNextActiveModulesResponse, active_modules_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::IdentifyModuleRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::IdentifyModuleRequest, module_identity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::AssignModuleRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::AssignModuleRequest, module_identity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ClearModuleAssignmentRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ClearModuleAssignmentRequest, module_identity_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::SetModuleSerialRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::SetModuleSerialRequest, module_identity_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::SetModuleSerialRequest, new_serial_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ModuleDefinition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ModuleDefinition, module_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ModuleDefinition, module_spacing_mm_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::ModuleDefinition, disabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::RowDefinition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::RowDefinition, row_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::RowDefinition, modules_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::RowDefinition, row_spacing_mm_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::BarDefinition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::BarDefinition, bar_length_mm_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::BarDefinition, folding_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::RobotDefinition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::RobotDefinition, rows_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::RobotDefinition, bar_definition_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::Preset, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::Preset, uuid_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::Preset, display_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::Preset, definition_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetPresetsListRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetPresetsListRequest, language_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetPresetsListResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetPresetsListResponse, presets_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetCurrentRobotDefinitionResponse, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetCurrentRobotDefinitionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::GetCurrentRobotDefinitionResponse, current_definition_),
  0,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::SetCurrentRobotDefinitionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::module::SetCurrentRobotDefinitionRequest, current_definition_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::module::ModuleIdentity)},
  { 8, -1, -1, sizeof(::carbon::frontend::module::GetNextModulesListRequest)},
  { 15, -1, -1, sizeof(::carbon::frontend::module::GetNextModulesListResponse)},
  { 25, -1, -1, sizeof(::carbon::frontend::module::GetNextActiveModulesRequest)},
  { 32, -1, -1, sizeof(::carbon::frontend::module::GetNextActiveModulesResponse)},
  { 40, -1, -1, sizeof(::carbon::frontend::module::IdentifyModuleRequest)},
  { 47, -1, -1, sizeof(::carbon::frontend::module::AssignModuleRequest)},
  { 54, -1, -1, sizeof(::carbon::frontend::module::ClearModuleAssignmentRequest)},
  { 61, -1, -1, sizeof(::carbon::frontend::module::SetModuleSerialRequest)},
  { 69, -1, -1, sizeof(::carbon::frontend::module::ModuleDefinition)},
  { 78, -1, -1, sizeof(::carbon::frontend::module::RowDefinition)},
  { 87, -1, -1, sizeof(::carbon::frontend::module::BarDefinition)},
  { 95, -1, -1, sizeof(::carbon::frontend::module::RobotDefinition)},
  { 103, -1, -1, sizeof(::carbon::frontend::module::Preset)},
  { 112, -1, -1, sizeof(::carbon::frontend::module::GetPresetsListRequest)},
  { 119, -1, -1, sizeof(::carbon::frontend::module::GetPresetsListResponse)},
  { 126, 133, -1, sizeof(::carbon::frontend::module::GetCurrentRobotDefinitionResponse)},
  { 134, -1, -1, sizeof(::carbon::frontend::module::SetCurrentRobotDefinitionRequest)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_ModuleIdentity_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_GetNextModulesListRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_GetNextModulesListResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_GetNextActiveModulesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_GetNextActiveModulesResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_IdentifyModuleRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_AssignModuleRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_ClearModuleAssignmentRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_SetModuleSerialRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_ModuleDefinition_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_RowDefinition_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_BarDefinition_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_RobotDefinition_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_Preset_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_GetPresetsListRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_GetPresetsListResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_GetCurrentRobotDefinitionResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::module::_SetCurrentRobotDefinitionRequest_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fmodule_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\033frontend/proto/module.proto\022\026carbon.fr"
  "ontend.module\032\031frontend/proto/util.proto"
  "\",\n\016ModuleIdentity\022\n\n\002id\030\001 \001(\r\022\016\n\006serial"
  "\030\002 \001(\t\"H\n\031GetNextModulesListRequest\022+\n\002t"
  "s\030\001 \001(\0132\037.carbon.frontend.util.Timestamp"
  "\"\225\002\n\032GetNextModulesListResponse\022+\n\002ts\030\001 "
  "\001(\0132\037.carbon.frontend.util.Timestamp\022@\n\020"
  "assigned_modules\030\002 \003(\0132&.carbon.frontend"
  ".module.ModuleIdentity\022B\n\022unassigned_mod"
  "ules\030\003 \003(\0132&.carbon.frontend.module.Modu"
  "leIdentity\022D\n\024unset_serial_modules\030\004 \003(\013"
  "2&.carbon.frontend.module.ModuleIdentity"
  "\"J\n\033GetNextActiveModulesRequest\022+\n\002ts\030\001 "
  "\001(\0132\037.carbon.frontend.util.Timestamp\"\213\001\n"
  "\034GetNextActiveModulesResponse\022+\n\002ts\030\001 \001("
  "\0132\037.carbon.frontend.util.Timestamp\022>\n\016ac"
  "tive_modules\030\002 \003(\0132&.carbon.frontend.mod"
  "ule.ModuleIdentity\"X\n\025IdentifyModuleRequ"
  "est\022\?\n\017module_identity\030\001 \001(\0132&.carbon.fr"
  "ontend.module.ModuleIdentity\"V\n\023AssignMo"
  "duleRequest\022\?\n\017module_identity\030\001 \001(\0132&.c"
  "arbon.frontend.module.ModuleIdentity\"_\n\034"
  "ClearModuleAssignmentRequest\022\?\n\017module_i"
  "dentity\030\001 \001(\0132&.carbon.frontend.module.M"
  "oduleIdentity\"m\n\026SetModuleSerialRequest\022"
  "\?\n\017module_identity\030\001 \001(\0132&.carbon.fronte"
  "nd.module.ModuleIdentity\022\022\n\nnew_serial\030\002"
  " \001(\t\"R\n\020ModuleDefinition\022\021\n\tmodule_id\030\001 "
  "\001(\r\022\031\n\021module_spacing_mm\030\002 \001(\002\022\020\n\010disabl"
  "ed\030\003 \001(\010\"r\n\rRowDefinition\022\016\n\006row_id\030\001 \001("
  "\r\0229\n\007modules\030\002 \003(\0132(.carbon.frontend.mod"
  "ule.ModuleDefinition\022\026\n\016row_spacing_mm\030\003"
  " \001(\002\"7\n\rBarDefinition\022\025\n\rbar_length_mm\030\001"
  " \001(\r\022\017\n\007folding\030\002 \001(\010\"\205\001\n\017RobotDefinitio"
  "n\0223\n\004rows\030\001 \003(\0132%.carbon.frontend.module"
  ".RowDefinition\022=\n\016bar_definition\030\003 \001(\0132%"
  ".carbon.frontend.module.BarDefinition\"i\n"
  "\006Preset\022\014\n\004uuid\030\001 \001(\t\022\024\n\014display_name\030\002 "
  "\001(\t\022;\n\ndefinition\030\003 \001(\0132\'.carbon.fronten"
  "d.module.RobotDefinition\")\n\025GetPresetsLi"
  "stRequest\022\020\n\010language\030\001 \001(\t\"I\n\026GetPreset"
  "sListResponse\022/\n\007presets\030\001 \003(\0132\036.carbon."
  "frontend.module.Preset\"\204\001\n!GetCurrentRob"
  "otDefinitionResponse\022H\n\022current_definiti"
  "on\030\001 \001(\0132\'.carbon.frontend.module.RobotD"
  "efinitionH\000\210\001\001B\025\n\023_current_definition\"g\n"
  " SetCurrentRobotDefinitionRequest\022C\n\022cur"
  "rent_definition\030\001 \001(\0132\'.carbon.frontend."
  "module.RobotDefinition2\370\007\n\027ModuleAssignm"
  "entService\022{\n\022GetNextModulesList\0221.carbo"
  "n.frontend.module.GetNextModulesListRequ"
  "est\0322.carbon.frontend.module.GetNextModu"
  "lesListResponse\022\201\001\n\024GetNextActiveModules"
  "\0223.carbon.frontend.module.GetNextActiveM"
  "odulesRequest\0324.carbon.frontend.module.G"
  "etNextActiveModulesResponse\022\\\n\016IdentifyM"
  "odule\022-.carbon.frontend.module.IdentifyM"
  "oduleRequest\032\033.carbon.frontend.util.Empt"
  "y\022X\n\014AssignModule\022+.carbon.frontend.modu"
  "le.AssignModuleRequest\032\033.carbon.frontend"
  ".util.Empty\022j\n\025ClearModuleAssignment\0224.c"
  "arbon.frontend.module.ClearModuleAssignm"
  "entRequest\032\033.carbon.frontend.util.Empty\022"
  "^\n\017SetModuleSerial\022..carbon.frontend.mod"
  "ule.SetModuleSerialRequest\032\033.carbon.fron"
  "tend.util.Empty\022o\n\016GetPresetsList\022-.carb"
  "on.frontend.module.GetPresetsListRequest"
  "\032..carbon.frontend.module.GetPresetsList"
  "Response\022s\n\031GetCurrentRobotDefinition\022\033."
  "carbon.frontend.util.Empty\0329.carbon.fron"
  "tend.module.GetCurrentRobotDefinitionRes"
  "ponse\022r\n\031SetCurrentRobotDefinition\0228.car"
  "bon.frontend.module.SetCurrentRobotDefin"
  "itionRequest\032\033.carbon.frontend.util.Empt"
  "yB\020Z\016proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fmodule_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fmodule_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fmodule_2eproto = {
  false, false, 2987, descriptor_table_protodef_frontend_2fproto_2fmodule_2eproto, "frontend/proto/module.proto", 
  &descriptor_table_frontend_2fproto_2fmodule_2eproto_once, descriptor_table_frontend_2fproto_2fmodule_2eproto_deps, 1, 18,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fmodule_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fmodule_2eproto, file_level_enum_descriptors_frontend_2fproto_2fmodule_2eproto, file_level_service_descriptors_frontend_2fproto_2fmodule_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fmodule_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fmodule_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fmodule_2eproto(&descriptor_table_frontend_2fproto_2fmodule_2eproto);
namespace carbon {
namespace frontend {
namespace module {

// ===================================================================

class ModuleIdentity::_Internal {
 public:
};

ModuleIdentity::ModuleIdentity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.ModuleIdentity)
}
ModuleIdentity::ModuleIdentity(const ModuleIdentity& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_serial().empty()) {
    serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_serial(), 
      GetArenaForAllocation());
  }
  id_ = from.id_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.ModuleIdentity)
}

inline void ModuleIdentity::SharedCtor() {
serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
id_ = 0u;
}

ModuleIdentity::~ModuleIdentity() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.ModuleIdentity)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModuleIdentity::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void ModuleIdentity::ArenaDtor(void* object) {
  ModuleIdentity* _this = reinterpret_cast< ModuleIdentity* >(object);
  (void)_this;
}
void ModuleIdentity::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModuleIdentity::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModuleIdentity::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.ModuleIdentity)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  serial_.ClearToEmpty();
  id_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModuleIdentity::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string serial = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.module.ModuleIdentity.serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModuleIdentity::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.ModuleIdentity)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 id = 1;
  if (this->_internal_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_id(), target);
  }

  // string serial = 2;
  if (!this->_internal_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_serial().data(), static_cast<int>(this->_internal_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.module.ModuleIdentity.serial");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_serial(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.ModuleIdentity)
  return target;
}

size_t ModuleIdentity::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.ModuleIdentity)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string serial = 2;
  if (!this->_internal_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_serial());
  }

  // uint32 id = 1;
  if (this->_internal_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModuleIdentity::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModuleIdentity::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModuleIdentity::GetClassData() const { return &_class_data_; }

void ModuleIdentity::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModuleIdentity *>(to)->MergeFrom(
      static_cast<const ModuleIdentity &>(from));
}


void ModuleIdentity::MergeFrom(const ModuleIdentity& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.ModuleIdentity)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_serial().empty()) {
    _internal_set_serial(from._internal_serial());
  }
  if (from._internal_id() != 0) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModuleIdentity::CopyFrom(const ModuleIdentity& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.ModuleIdentity)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModuleIdentity::IsInitialized() const {
  return true;
}

void ModuleIdentity::InternalSwap(ModuleIdentity* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &serial_, lhs_arena,
      &other->serial_, rhs_arena
  );
  swap(id_, other->id_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ModuleIdentity::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[0]);
}

// ===================================================================

class GetNextModulesListRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextModulesListRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextModulesListRequest::_Internal::ts(const GetNextModulesListRequest* msg) {
  return *msg->ts_;
}
void GetNextModulesListRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextModulesListRequest::GetNextModulesListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.GetNextModulesListRequest)
}
GetNextModulesListRequest::GetNextModulesListRequest(const GetNextModulesListRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.GetNextModulesListRequest)
}

inline void GetNextModulesListRequest::SharedCtor() {
ts_ = nullptr;
}

GetNextModulesListRequest::~GetNextModulesListRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.GetNextModulesListRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextModulesListRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextModulesListRequest::ArenaDtor(void* object) {
  GetNextModulesListRequest* _this = reinterpret_cast< GetNextModulesListRequest* >(object);
  (void)_this;
}
void GetNextModulesListRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextModulesListRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextModulesListRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.GetNextModulesListRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextModulesListRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextModulesListRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.GetNextModulesListRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.GetNextModulesListRequest)
  return target;
}

size_t GetNextModulesListRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.GetNextModulesListRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextModulesListRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextModulesListRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextModulesListRequest::GetClassData() const { return &_class_data_; }

void GetNextModulesListRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextModulesListRequest *>(to)->MergeFrom(
      static_cast<const GetNextModulesListRequest &>(from));
}


void GetNextModulesListRequest::MergeFrom(const GetNextModulesListRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.GetNextModulesListRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextModulesListRequest::CopyFrom(const GetNextModulesListRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.GetNextModulesListRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextModulesListRequest::IsInitialized() const {
  return true;
}

void GetNextModulesListRequest::InternalSwap(GetNextModulesListRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextModulesListRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[1]);
}

// ===================================================================

class GetNextModulesListResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextModulesListResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextModulesListResponse::_Internal::ts(const GetNextModulesListResponse* msg) {
  return *msg->ts_;
}
void GetNextModulesListResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextModulesListResponse::GetNextModulesListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  assigned_modules_(arena),
  unassigned_modules_(arena),
  unset_serial_modules_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.GetNextModulesListResponse)
}
GetNextModulesListResponse::GetNextModulesListResponse(const GetNextModulesListResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      assigned_modules_(from.assigned_modules_),
      unassigned_modules_(from.unassigned_modules_),
      unset_serial_modules_(from.unset_serial_modules_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.GetNextModulesListResponse)
}

inline void GetNextModulesListResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextModulesListResponse::~GetNextModulesListResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.GetNextModulesListResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextModulesListResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextModulesListResponse::ArenaDtor(void* object) {
  GetNextModulesListResponse* _this = reinterpret_cast< GetNextModulesListResponse* >(object);
  (void)_this;
}
void GetNextModulesListResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextModulesListResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextModulesListResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.GetNextModulesListResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  assigned_modules_.Clear();
  unassigned_modules_.Clear();
  unset_serial_modules_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextModulesListResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_assigned_modules(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.module.ModuleIdentity unassigned_modules = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_unassigned_modules(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.module.ModuleIdentity unset_serial_modules = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_unset_serial_modules(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextModulesListResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.GetNextModulesListResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_assigned_modules_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_assigned_modules(i), target, stream);
  }

  // repeated .carbon.frontend.module.ModuleIdentity unassigned_modules = 3;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_unassigned_modules_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(3, this->_internal_unassigned_modules(i), target, stream);
  }

  // repeated .carbon.frontend.module.ModuleIdentity unset_serial_modules = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_unset_serial_modules_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_unset_serial_modules(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.GetNextModulesListResponse)
  return target;
}

size_t GetNextModulesListResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.GetNextModulesListResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 2;
  total_size += 1UL * this->_internal_assigned_modules_size();
  for (const auto& msg : this->assigned_modules_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .carbon.frontend.module.ModuleIdentity unassigned_modules = 3;
  total_size += 1UL * this->_internal_unassigned_modules_size();
  for (const auto& msg : this->unassigned_modules_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated .carbon.frontend.module.ModuleIdentity unset_serial_modules = 4;
  total_size += 1UL * this->_internal_unset_serial_modules_size();
  for (const auto& msg : this->unset_serial_modules_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextModulesListResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextModulesListResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextModulesListResponse::GetClassData() const { return &_class_data_; }

void GetNextModulesListResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextModulesListResponse *>(to)->MergeFrom(
      static_cast<const GetNextModulesListResponse &>(from));
}


void GetNextModulesListResponse::MergeFrom(const GetNextModulesListResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.GetNextModulesListResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  assigned_modules_.MergeFrom(from.assigned_modules_);
  unassigned_modules_.MergeFrom(from.unassigned_modules_);
  unset_serial_modules_.MergeFrom(from.unset_serial_modules_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextModulesListResponse::CopyFrom(const GetNextModulesListResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.GetNextModulesListResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextModulesListResponse::IsInitialized() const {
  return true;
}

void GetNextModulesListResponse::InternalSwap(GetNextModulesListResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  assigned_modules_.InternalSwap(&other->assigned_modules_);
  unassigned_modules_.InternalSwap(&other->unassigned_modules_);
  unset_serial_modules_.InternalSwap(&other->unset_serial_modules_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextModulesListResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[2]);
}

// ===================================================================

class GetNextActiveModulesRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextActiveModulesRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextActiveModulesRequest::_Internal::ts(const GetNextActiveModulesRequest* msg) {
  return *msg->ts_;
}
void GetNextActiveModulesRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextActiveModulesRequest::GetNextActiveModulesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.GetNextActiveModulesRequest)
}
GetNextActiveModulesRequest::GetNextActiveModulesRequest(const GetNextActiveModulesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.GetNextActiveModulesRequest)
}

inline void GetNextActiveModulesRequest::SharedCtor() {
ts_ = nullptr;
}

GetNextActiveModulesRequest::~GetNextActiveModulesRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.GetNextActiveModulesRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveModulesRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextActiveModulesRequest::ArenaDtor(void* object) {
  GetNextActiveModulesRequest* _this = reinterpret_cast< GetNextActiveModulesRequest* >(object);
  (void)_this;
}
void GetNextActiveModulesRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveModulesRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveModulesRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.GetNextActiveModulesRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveModulesRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveModulesRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.GetNextActiveModulesRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.GetNextActiveModulesRequest)
  return target;
}

size_t GetNextActiveModulesRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.GetNextActiveModulesRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveModulesRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveModulesRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveModulesRequest::GetClassData() const { return &_class_data_; }

void GetNextActiveModulesRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveModulesRequest *>(to)->MergeFrom(
      static_cast<const GetNextActiveModulesRequest &>(from));
}


void GetNextActiveModulesRequest::MergeFrom(const GetNextActiveModulesRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.GetNextActiveModulesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveModulesRequest::CopyFrom(const GetNextActiveModulesRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.GetNextActiveModulesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveModulesRequest::IsInitialized() const {
  return true;
}

void GetNextActiveModulesRequest::InternalSwap(GetNextActiveModulesRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveModulesRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[3]);
}

// ===================================================================

class GetNextActiveModulesResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextActiveModulesResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextActiveModulesResponse::_Internal::ts(const GetNextActiveModulesResponse* msg) {
  return *msg->ts_;
}
void GetNextActiveModulesResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextActiveModulesResponse::GetNextActiveModulesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  active_modules_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.GetNextActiveModulesResponse)
}
GetNextActiveModulesResponse::GetNextActiveModulesResponse(const GetNextActiveModulesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      active_modules_(from.active_modules_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.GetNextActiveModulesResponse)
}

inline void GetNextActiveModulesResponse::SharedCtor() {
ts_ = nullptr;
}

GetNextActiveModulesResponse::~GetNextActiveModulesResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.GetNextActiveModulesResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveModulesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void GetNextActiveModulesResponse::ArenaDtor(void* object) {
  GetNextActiveModulesResponse* _this = reinterpret_cast< GetNextActiveModulesResponse* >(object);
  (void)_this;
}
void GetNextActiveModulesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveModulesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveModulesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.GetNextActiveModulesResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  active_modules_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveModulesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.module.ModuleIdentity active_modules = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_active_modules(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveModulesResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.GetNextActiveModulesResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.frontend.module.ModuleIdentity active_modules = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_active_modules_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_active_modules(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.GetNextActiveModulesResponse)
  return target;
}

size_t GetNextActiveModulesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.GetNextActiveModulesResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.ModuleIdentity active_modules = 2;
  total_size += 1UL * this->_internal_active_modules_size();
  for (const auto& msg : this->active_modules_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveModulesResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveModulesResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveModulesResponse::GetClassData() const { return &_class_data_; }

void GetNextActiveModulesResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveModulesResponse *>(to)->MergeFrom(
      static_cast<const GetNextActiveModulesResponse &>(from));
}


void GetNextActiveModulesResponse::MergeFrom(const GetNextActiveModulesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.GetNextActiveModulesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  active_modules_.MergeFrom(from.active_modules_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveModulesResponse::CopyFrom(const GetNextActiveModulesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.GetNextActiveModulesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveModulesResponse::IsInitialized() const {
  return true;
}

void GetNextActiveModulesResponse::InternalSwap(GetNextActiveModulesResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  active_modules_.InternalSwap(&other->active_modules_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveModulesResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[4]);
}

// ===================================================================

class IdentifyModuleRequest::_Internal {
 public:
  static const ::carbon::frontend::module::ModuleIdentity& module_identity(const IdentifyModuleRequest* msg);
};

const ::carbon::frontend::module::ModuleIdentity&
IdentifyModuleRequest::_Internal::module_identity(const IdentifyModuleRequest* msg) {
  return *msg->module_identity_;
}
IdentifyModuleRequest::IdentifyModuleRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.IdentifyModuleRequest)
}
IdentifyModuleRequest::IdentifyModuleRequest(const IdentifyModuleRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_module_identity()) {
    module_identity_ = new ::carbon::frontend::module::ModuleIdentity(*from.module_identity_);
  } else {
    module_identity_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.IdentifyModuleRequest)
}

inline void IdentifyModuleRequest::SharedCtor() {
module_identity_ = nullptr;
}

IdentifyModuleRequest::~IdentifyModuleRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.IdentifyModuleRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void IdentifyModuleRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete module_identity_;
}

void IdentifyModuleRequest::ArenaDtor(void* object) {
  IdentifyModuleRequest* _this = reinterpret_cast< IdentifyModuleRequest* >(object);
  (void)_this;
}
void IdentifyModuleRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void IdentifyModuleRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IdentifyModuleRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.IdentifyModuleRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && module_identity_ != nullptr) {
    delete module_identity_;
  }
  module_identity_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IdentifyModuleRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.module.ModuleIdentity module_identity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_module_identity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IdentifyModuleRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.IdentifyModuleRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  if (this->_internal_has_module_identity()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::module_identity(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.IdentifyModuleRequest)
  return target;
}

size_t IdentifyModuleRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.IdentifyModuleRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  if (this->_internal_has_module_identity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *module_identity_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IdentifyModuleRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    IdentifyModuleRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IdentifyModuleRequest::GetClassData() const { return &_class_data_; }

void IdentifyModuleRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<IdentifyModuleRequest *>(to)->MergeFrom(
      static_cast<const IdentifyModuleRequest &>(from));
}


void IdentifyModuleRequest::MergeFrom(const IdentifyModuleRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.IdentifyModuleRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_module_identity()) {
    _internal_mutable_module_identity()->::carbon::frontend::module::ModuleIdentity::MergeFrom(from._internal_module_identity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IdentifyModuleRequest::CopyFrom(const IdentifyModuleRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.IdentifyModuleRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IdentifyModuleRequest::IsInitialized() const {
  return true;
}

void IdentifyModuleRequest::InternalSwap(IdentifyModuleRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(module_identity_, other->module_identity_);
}

::PROTOBUF_NAMESPACE_ID::Metadata IdentifyModuleRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[5]);
}

// ===================================================================

class AssignModuleRequest::_Internal {
 public:
  static const ::carbon::frontend::module::ModuleIdentity& module_identity(const AssignModuleRequest* msg);
};

const ::carbon::frontend::module::ModuleIdentity&
AssignModuleRequest::_Internal::module_identity(const AssignModuleRequest* msg) {
  return *msg->module_identity_;
}
AssignModuleRequest::AssignModuleRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.AssignModuleRequest)
}
AssignModuleRequest::AssignModuleRequest(const AssignModuleRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_module_identity()) {
    module_identity_ = new ::carbon::frontend::module::ModuleIdentity(*from.module_identity_);
  } else {
    module_identity_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.AssignModuleRequest)
}

inline void AssignModuleRequest::SharedCtor() {
module_identity_ = nullptr;
}

AssignModuleRequest::~AssignModuleRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.AssignModuleRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AssignModuleRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete module_identity_;
}

void AssignModuleRequest::ArenaDtor(void* object) {
  AssignModuleRequest* _this = reinterpret_cast< AssignModuleRequest* >(object);
  (void)_this;
}
void AssignModuleRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AssignModuleRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AssignModuleRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.AssignModuleRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && module_identity_ != nullptr) {
    delete module_identity_;
  }
  module_identity_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AssignModuleRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.module.ModuleIdentity module_identity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_module_identity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AssignModuleRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.AssignModuleRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  if (this->_internal_has_module_identity()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::module_identity(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.AssignModuleRequest)
  return target;
}

size_t AssignModuleRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.AssignModuleRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  if (this->_internal_has_module_identity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *module_identity_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AssignModuleRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AssignModuleRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AssignModuleRequest::GetClassData() const { return &_class_data_; }

void AssignModuleRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AssignModuleRequest *>(to)->MergeFrom(
      static_cast<const AssignModuleRequest &>(from));
}


void AssignModuleRequest::MergeFrom(const AssignModuleRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.AssignModuleRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_module_identity()) {
    _internal_mutable_module_identity()->::carbon::frontend::module::ModuleIdentity::MergeFrom(from._internal_module_identity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AssignModuleRequest::CopyFrom(const AssignModuleRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.AssignModuleRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AssignModuleRequest::IsInitialized() const {
  return true;
}

void AssignModuleRequest::InternalSwap(AssignModuleRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(module_identity_, other->module_identity_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AssignModuleRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[6]);
}

// ===================================================================

class ClearModuleAssignmentRequest::_Internal {
 public:
  static const ::carbon::frontend::module::ModuleIdentity& module_identity(const ClearModuleAssignmentRequest* msg);
};

const ::carbon::frontend::module::ModuleIdentity&
ClearModuleAssignmentRequest::_Internal::module_identity(const ClearModuleAssignmentRequest* msg) {
  return *msg->module_identity_;
}
ClearModuleAssignmentRequest::ClearModuleAssignmentRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.ClearModuleAssignmentRequest)
}
ClearModuleAssignmentRequest::ClearModuleAssignmentRequest(const ClearModuleAssignmentRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_module_identity()) {
    module_identity_ = new ::carbon::frontend::module::ModuleIdentity(*from.module_identity_);
  } else {
    module_identity_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.ClearModuleAssignmentRequest)
}

inline void ClearModuleAssignmentRequest::SharedCtor() {
module_identity_ = nullptr;
}

ClearModuleAssignmentRequest::~ClearModuleAssignmentRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.ClearModuleAssignmentRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ClearModuleAssignmentRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete module_identity_;
}

void ClearModuleAssignmentRequest::ArenaDtor(void* object) {
  ClearModuleAssignmentRequest* _this = reinterpret_cast< ClearModuleAssignmentRequest* >(object);
  (void)_this;
}
void ClearModuleAssignmentRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ClearModuleAssignmentRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ClearModuleAssignmentRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.ClearModuleAssignmentRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && module_identity_ != nullptr) {
    delete module_identity_;
  }
  module_identity_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ClearModuleAssignmentRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.module.ModuleIdentity module_identity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_module_identity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ClearModuleAssignmentRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.ClearModuleAssignmentRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  if (this->_internal_has_module_identity()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::module_identity(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.ClearModuleAssignmentRequest)
  return target;
}

size_t ClearModuleAssignmentRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.ClearModuleAssignmentRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  if (this->_internal_has_module_identity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *module_identity_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ClearModuleAssignmentRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ClearModuleAssignmentRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ClearModuleAssignmentRequest::GetClassData() const { return &_class_data_; }

void ClearModuleAssignmentRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ClearModuleAssignmentRequest *>(to)->MergeFrom(
      static_cast<const ClearModuleAssignmentRequest &>(from));
}


void ClearModuleAssignmentRequest::MergeFrom(const ClearModuleAssignmentRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.ClearModuleAssignmentRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_module_identity()) {
    _internal_mutable_module_identity()->::carbon::frontend::module::ModuleIdentity::MergeFrom(from._internal_module_identity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ClearModuleAssignmentRequest::CopyFrom(const ClearModuleAssignmentRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.ClearModuleAssignmentRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ClearModuleAssignmentRequest::IsInitialized() const {
  return true;
}

void ClearModuleAssignmentRequest::InternalSwap(ClearModuleAssignmentRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(module_identity_, other->module_identity_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ClearModuleAssignmentRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[7]);
}

// ===================================================================

class SetModuleSerialRequest::_Internal {
 public:
  static const ::carbon::frontend::module::ModuleIdentity& module_identity(const SetModuleSerialRequest* msg);
};

const ::carbon::frontend::module::ModuleIdentity&
SetModuleSerialRequest::_Internal::module_identity(const SetModuleSerialRequest* msg) {
  return *msg->module_identity_;
}
SetModuleSerialRequest::SetModuleSerialRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.SetModuleSerialRequest)
}
SetModuleSerialRequest::SetModuleSerialRequest(const SetModuleSerialRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  new_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    new_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_new_serial().empty()) {
    new_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_new_serial(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_module_identity()) {
    module_identity_ = new ::carbon::frontend::module::ModuleIdentity(*from.module_identity_);
  } else {
    module_identity_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.SetModuleSerialRequest)
}

inline void SetModuleSerialRequest::SharedCtor() {
new_serial_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  new_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
module_identity_ = nullptr;
}

SetModuleSerialRequest::~SetModuleSerialRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.SetModuleSerialRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetModuleSerialRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  new_serial_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete module_identity_;
}

void SetModuleSerialRequest::ArenaDtor(void* object) {
  SetModuleSerialRequest* _this = reinterpret_cast< SetModuleSerialRequest* >(object);
  (void)_this;
}
void SetModuleSerialRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetModuleSerialRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetModuleSerialRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.SetModuleSerialRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  new_serial_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && module_identity_ != nullptr) {
    delete module_identity_;
  }
  module_identity_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetModuleSerialRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.module.ModuleIdentity module_identity = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_module_identity(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string new_serial = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_new_serial();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.module.SetModuleSerialRequest.new_serial"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetModuleSerialRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.SetModuleSerialRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  if (this->_internal_has_module_identity()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::module_identity(this), target, stream);
  }

  // string new_serial = 2;
  if (!this->_internal_new_serial().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_new_serial().data(), static_cast<int>(this->_internal_new_serial().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.module.SetModuleSerialRequest.new_serial");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_new_serial(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.SetModuleSerialRequest)
  return target;
}

size_t SetModuleSerialRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.SetModuleSerialRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string new_serial = 2;
  if (!this->_internal_new_serial().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_new_serial());
  }

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  if (this->_internal_has_module_identity()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *module_identity_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetModuleSerialRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetModuleSerialRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetModuleSerialRequest::GetClassData() const { return &_class_data_; }

void SetModuleSerialRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetModuleSerialRequest *>(to)->MergeFrom(
      static_cast<const SetModuleSerialRequest &>(from));
}


void SetModuleSerialRequest::MergeFrom(const SetModuleSerialRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.SetModuleSerialRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_new_serial().empty()) {
    _internal_set_new_serial(from._internal_new_serial());
  }
  if (from._internal_has_module_identity()) {
    _internal_mutable_module_identity()->::carbon::frontend::module::ModuleIdentity::MergeFrom(from._internal_module_identity());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetModuleSerialRequest::CopyFrom(const SetModuleSerialRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.SetModuleSerialRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetModuleSerialRequest::IsInitialized() const {
  return true;
}

void SetModuleSerialRequest::InternalSwap(SetModuleSerialRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &new_serial_, lhs_arena,
      &other->new_serial_, rhs_arena
  );
  swap(module_identity_, other->module_identity_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetModuleSerialRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[8]);
}

// ===================================================================

class ModuleDefinition::_Internal {
 public:
};

ModuleDefinition::ModuleDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.ModuleDefinition)
}
ModuleDefinition::ModuleDefinition(const ModuleDefinition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&module_id_, &from.module_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&disabled_) -
    reinterpret_cast<char*>(&module_id_)) + sizeof(disabled_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.ModuleDefinition)
}

inline void ModuleDefinition::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&module_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&disabled_) -
    reinterpret_cast<char*>(&module_id_)) + sizeof(disabled_));
}

ModuleDefinition::~ModuleDefinition() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.ModuleDefinition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModuleDefinition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ModuleDefinition::ArenaDtor(void* object) {
  ModuleDefinition* _this = reinterpret_cast< ModuleDefinition* >(object);
  (void)_this;
}
void ModuleDefinition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModuleDefinition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModuleDefinition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.ModuleDefinition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&module_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&disabled_) -
      reinterpret_cast<char*>(&module_id_)) + sizeof(disabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModuleDefinition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 module_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          module_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float module_spacing_mm = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          module_spacing_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // bool disabled = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          disabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModuleDefinition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.ModuleDefinition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 module_id = 1;
  if (this->_internal_module_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_module_id(), target);
  }

  // float module_spacing_mm = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_module_spacing_mm = this->_internal_module_spacing_mm();
  uint32_t raw_module_spacing_mm;
  memcpy(&raw_module_spacing_mm, &tmp_module_spacing_mm, sizeof(tmp_module_spacing_mm));
  if (raw_module_spacing_mm != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_module_spacing_mm(), target);
  }

  // bool disabled = 3;
  if (this->_internal_disabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_disabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.ModuleDefinition)
  return target;
}

size_t ModuleDefinition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.ModuleDefinition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 module_id = 1;
  if (this->_internal_module_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_module_id());
  }

  // float module_spacing_mm = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_module_spacing_mm = this->_internal_module_spacing_mm();
  uint32_t raw_module_spacing_mm;
  memcpy(&raw_module_spacing_mm, &tmp_module_spacing_mm, sizeof(tmp_module_spacing_mm));
  if (raw_module_spacing_mm != 0) {
    total_size += 1 + 4;
  }

  // bool disabled = 3;
  if (this->_internal_disabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModuleDefinition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModuleDefinition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModuleDefinition::GetClassData() const { return &_class_data_; }

void ModuleDefinition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModuleDefinition *>(to)->MergeFrom(
      static_cast<const ModuleDefinition &>(from));
}


void ModuleDefinition::MergeFrom(const ModuleDefinition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.ModuleDefinition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_module_id() != 0) {
    _internal_set_module_id(from._internal_module_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_module_spacing_mm = from._internal_module_spacing_mm();
  uint32_t raw_module_spacing_mm;
  memcpy(&raw_module_spacing_mm, &tmp_module_spacing_mm, sizeof(tmp_module_spacing_mm));
  if (raw_module_spacing_mm != 0) {
    _internal_set_module_spacing_mm(from._internal_module_spacing_mm());
  }
  if (from._internal_disabled() != 0) {
    _internal_set_disabled(from._internal_disabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModuleDefinition::CopyFrom(const ModuleDefinition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.ModuleDefinition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModuleDefinition::IsInitialized() const {
  return true;
}

void ModuleDefinition::InternalSwap(ModuleDefinition* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ModuleDefinition, disabled_)
      + sizeof(ModuleDefinition::disabled_)
      - PROTOBUF_FIELD_OFFSET(ModuleDefinition, module_id_)>(
          reinterpret_cast<char*>(&module_id_),
          reinterpret_cast<char*>(&other->module_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ModuleDefinition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[9]);
}

// ===================================================================

class RowDefinition::_Internal {
 public:
};

RowDefinition::RowDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  modules_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.RowDefinition)
}
RowDefinition::RowDefinition(const RowDefinition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      modules_(from.modules_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&row_id_, &from.row_id_,
    static_cast<size_t>(reinterpret_cast<char*>(&row_spacing_mm_) -
    reinterpret_cast<char*>(&row_id_)) + sizeof(row_spacing_mm_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.RowDefinition)
}

inline void RowDefinition::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&row_id_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&row_spacing_mm_) -
    reinterpret_cast<char*>(&row_id_)) + sizeof(row_spacing_mm_));
}

RowDefinition::~RowDefinition() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.RowDefinition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RowDefinition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RowDefinition::ArenaDtor(void* object) {
  RowDefinition* _this = reinterpret_cast< RowDefinition* >(object);
  (void)_this;
}
void RowDefinition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RowDefinition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RowDefinition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.RowDefinition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  modules_.Clear();
  ::memset(&row_id_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&row_spacing_mm_) -
      reinterpret_cast<char*>(&row_id_)) + sizeof(row_spacing_mm_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RowDefinition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 row_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          row_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.frontend.module.ModuleDefinition modules = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_modules(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // float row_spacing_mm = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          row_spacing_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RowDefinition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.RowDefinition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_row_id(), target);
  }

  // repeated .carbon.frontend.module.ModuleDefinition modules = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_modules_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_modules(i), target, stream);
  }

  // float row_spacing_mm = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_row_spacing_mm = this->_internal_row_spacing_mm();
  uint32_t raw_row_spacing_mm;
  memcpy(&raw_row_spacing_mm, &tmp_row_spacing_mm, sizeof(tmp_row_spacing_mm));
  if (raw_row_spacing_mm != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_row_spacing_mm(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.RowDefinition)
  return target;
}

size_t RowDefinition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.RowDefinition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.ModuleDefinition modules = 2;
  total_size += 1UL * this->_internal_modules_size();
  for (const auto& msg : this->modules_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // uint32 row_id = 1;
  if (this->_internal_row_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_row_id());
  }

  // float row_spacing_mm = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_row_spacing_mm = this->_internal_row_spacing_mm();
  uint32_t raw_row_spacing_mm;
  memcpy(&raw_row_spacing_mm, &tmp_row_spacing_mm, sizeof(tmp_row_spacing_mm));
  if (raw_row_spacing_mm != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RowDefinition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RowDefinition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RowDefinition::GetClassData() const { return &_class_data_; }

void RowDefinition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RowDefinition *>(to)->MergeFrom(
      static_cast<const RowDefinition &>(from));
}


void RowDefinition::MergeFrom(const RowDefinition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.RowDefinition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  modules_.MergeFrom(from.modules_);
  if (from._internal_row_id() != 0) {
    _internal_set_row_id(from._internal_row_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_row_spacing_mm = from._internal_row_spacing_mm();
  uint32_t raw_row_spacing_mm;
  memcpy(&raw_row_spacing_mm, &tmp_row_spacing_mm, sizeof(tmp_row_spacing_mm));
  if (raw_row_spacing_mm != 0) {
    _internal_set_row_spacing_mm(from._internal_row_spacing_mm());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RowDefinition::CopyFrom(const RowDefinition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.RowDefinition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RowDefinition::IsInitialized() const {
  return true;
}

void RowDefinition::InternalSwap(RowDefinition* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  modules_.InternalSwap(&other->modules_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RowDefinition, row_spacing_mm_)
      + sizeof(RowDefinition::row_spacing_mm_)
      - PROTOBUF_FIELD_OFFSET(RowDefinition, row_id_)>(
          reinterpret_cast<char*>(&row_id_),
          reinterpret_cast<char*>(&other->row_id_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RowDefinition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[10]);
}

// ===================================================================

class BarDefinition::_Internal {
 public:
};

BarDefinition::BarDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.BarDefinition)
}
BarDefinition::BarDefinition(const BarDefinition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&bar_length_mm_, &from.bar_length_mm_,
    static_cast<size_t>(reinterpret_cast<char*>(&folding_) -
    reinterpret_cast<char*>(&bar_length_mm_)) + sizeof(folding_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.BarDefinition)
}

inline void BarDefinition::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&bar_length_mm_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&folding_) -
    reinterpret_cast<char*>(&bar_length_mm_)) + sizeof(folding_));
}

BarDefinition::~BarDefinition() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.BarDefinition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void BarDefinition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void BarDefinition::ArenaDtor(void* object) {
  BarDefinition* _this = reinterpret_cast< BarDefinition* >(object);
  (void)_this;
}
void BarDefinition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void BarDefinition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void BarDefinition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.BarDefinition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&bar_length_mm_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&folding_) -
      reinterpret_cast<char*>(&bar_length_mm_)) + sizeof(folding_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* BarDefinition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 bar_length_mm = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          bar_length_mm_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool folding = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          folding_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* BarDefinition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.BarDefinition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 bar_length_mm = 1;
  if (this->_internal_bar_length_mm() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_bar_length_mm(), target);
  }

  // bool folding = 2;
  if (this->_internal_folding() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_folding(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.BarDefinition)
  return target;
}

size_t BarDefinition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.BarDefinition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 bar_length_mm = 1;
  if (this->_internal_bar_length_mm() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_bar_length_mm());
  }

  // bool folding = 2;
  if (this->_internal_folding() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData BarDefinition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    BarDefinition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*BarDefinition::GetClassData() const { return &_class_data_; }

void BarDefinition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<BarDefinition *>(to)->MergeFrom(
      static_cast<const BarDefinition &>(from));
}


void BarDefinition::MergeFrom(const BarDefinition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.BarDefinition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_bar_length_mm() != 0) {
    _internal_set_bar_length_mm(from._internal_bar_length_mm());
  }
  if (from._internal_folding() != 0) {
    _internal_set_folding(from._internal_folding());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void BarDefinition::CopyFrom(const BarDefinition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.BarDefinition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool BarDefinition::IsInitialized() const {
  return true;
}

void BarDefinition::InternalSwap(BarDefinition* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(BarDefinition, folding_)
      + sizeof(BarDefinition::folding_)
      - PROTOBUF_FIELD_OFFSET(BarDefinition, bar_length_mm_)>(
          reinterpret_cast<char*>(&bar_length_mm_),
          reinterpret_cast<char*>(&other->bar_length_mm_));
}

::PROTOBUF_NAMESPACE_ID::Metadata BarDefinition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[11]);
}

// ===================================================================

class RobotDefinition::_Internal {
 public:
  static const ::carbon::frontend::module::BarDefinition& bar_definition(const RobotDefinition* msg);
};

const ::carbon::frontend::module::BarDefinition&
RobotDefinition::_Internal::bar_definition(const RobotDefinition* msg) {
  return *msg->bar_definition_;
}
RobotDefinition::RobotDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  rows_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.RobotDefinition)
}
RobotDefinition::RobotDefinition(const RobotDefinition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      rows_(from.rows_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_bar_definition()) {
    bar_definition_ = new ::carbon::frontend::module::BarDefinition(*from.bar_definition_);
  } else {
    bar_definition_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.RobotDefinition)
}

inline void RobotDefinition::SharedCtor() {
bar_definition_ = nullptr;
}

RobotDefinition::~RobotDefinition() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.RobotDefinition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RobotDefinition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete bar_definition_;
}

void RobotDefinition::ArenaDtor(void* object) {
  RobotDefinition* _this = reinterpret_cast< RobotDefinition* >(object);
  (void)_this;
}
void RobotDefinition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RobotDefinition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RobotDefinition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.RobotDefinition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rows_.Clear();
  if (GetArenaForAllocation() == nullptr && bar_definition_ != nullptr) {
    delete bar_definition_;
  }
  bar_definition_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RobotDefinition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.module.RowDefinition rows = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_rows(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.module.BarDefinition bar_definition = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_bar_definition(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RobotDefinition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.RobotDefinition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.RowDefinition rows = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_rows_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_rows(i), target, stream);
  }

  // .carbon.frontend.module.BarDefinition bar_definition = 3;
  if (this->_internal_has_bar_definition()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::bar_definition(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.RobotDefinition)
  return target;
}

size_t RobotDefinition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.RobotDefinition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.RowDefinition rows = 1;
  total_size += 1UL * this->_internal_rows_size();
  for (const auto& msg : this->rows_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // .carbon.frontend.module.BarDefinition bar_definition = 3;
  if (this->_internal_has_bar_definition()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *bar_definition_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RobotDefinition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RobotDefinition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RobotDefinition::GetClassData() const { return &_class_data_; }

void RobotDefinition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RobotDefinition *>(to)->MergeFrom(
      static_cast<const RobotDefinition &>(from));
}


void RobotDefinition::MergeFrom(const RobotDefinition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.RobotDefinition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  rows_.MergeFrom(from.rows_);
  if (from._internal_has_bar_definition()) {
    _internal_mutable_bar_definition()->::carbon::frontend::module::BarDefinition::MergeFrom(from._internal_bar_definition());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RobotDefinition::CopyFrom(const RobotDefinition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.RobotDefinition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RobotDefinition::IsInitialized() const {
  return true;
}

void RobotDefinition::InternalSwap(RobotDefinition* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  rows_.InternalSwap(&other->rows_);
  swap(bar_definition_, other->bar_definition_);
}

::PROTOBUF_NAMESPACE_ID::Metadata RobotDefinition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[12]);
}

// ===================================================================

class Preset::_Internal {
 public:
  static const ::carbon::frontend::module::RobotDefinition& definition(const Preset* msg);
};

const ::carbon::frontend::module::RobotDefinition&
Preset::_Internal::definition(const Preset* msg) {
  return *msg->definition_;
}
Preset::Preset(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.Preset)
}
Preset::Preset(const Preset& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_uuid().empty()) {
    uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_uuid(), 
      GetArenaForAllocation());
  }
  display_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_display_name().empty()) {
    display_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_display_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_definition()) {
    definition_ = new ::carbon::frontend::module::RobotDefinition(*from.definition_);
  } else {
    definition_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.Preset)
}

inline void Preset::SharedCtor() {
uuid_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
display_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
definition_ = nullptr;
}

Preset::~Preset() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.Preset)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Preset::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  uuid_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  display_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete definition_;
}

void Preset::ArenaDtor(void* object) {
  Preset* _this = reinterpret_cast< Preset* >(object);
  (void)_this;
}
void Preset::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Preset::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Preset::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.Preset)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  uuid_.ClearToEmpty();
  display_name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && definition_ != nullptr) {
    delete definition_;
  }
  definition_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Preset::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string uuid = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_uuid();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.module.Preset.uuid"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string display_name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_display_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.module.Preset.display_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.module.RobotDefinition definition = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_definition(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Preset::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.Preset)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_uuid().data(), static_cast<int>(this->_internal_uuid().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.module.Preset.uuid");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_uuid(), target);
  }

  // string display_name = 2;
  if (!this->_internal_display_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_display_name().data(), static_cast<int>(this->_internal_display_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.module.Preset.display_name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_display_name(), target);
  }

  // .carbon.frontend.module.RobotDefinition definition = 3;
  if (this->_internal_has_definition()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::definition(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.Preset)
  return target;
}

size_t Preset::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.Preset)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string uuid = 1;
  if (!this->_internal_uuid().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_uuid());
  }

  // string display_name = 2;
  if (!this->_internal_display_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_display_name());
  }

  // .carbon.frontend.module.RobotDefinition definition = 3;
  if (this->_internal_has_definition()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *definition_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Preset::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Preset::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Preset::GetClassData() const { return &_class_data_; }

void Preset::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Preset *>(to)->MergeFrom(
      static_cast<const Preset &>(from));
}


void Preset::MergeFrom(const Preset& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.Preset)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_uuid().empty()) {
    _internal_set_uuid(from._internal_uuid());
  }
  if (!from._internal_display_name().empty()) {
    _internal_set_display_name(from._internal_display_name());
  }
  if (from._internal_has_definition()) {
    _internal_mutable_definition()->::carbon::frontend::module::RobotDefinition::MergeFrom(from._internal_definition());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Preset::CopyFrom(const Preset& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.Preset)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Preset::IsInitialized() const {
  return true;
}

void Preset::InternalSwap(Preset* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &uuid_, lhs_arena,
      &other->uuid_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &display_name_, lhs_arena,
      &other->display_name_, rhs_arena
  );
  swap(definition_, other->definition_);
}

::PROTOBUF_NAMESPACE_ID::Metadata Preset::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[13]);
}

// ===================================================================

class GetPresetsListRequest::_Internal {
 public:
};

GetPresetsListRequest::GetPresetsListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.GetPresetsListRequest)
}
GetPresetsListRequest::GetPresetsListRequest(const GetPresetsListRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  language_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    language_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_language().empty()) {
    language_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_language(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.GetPresetsListRequest)
}

inline void GetPresetsListRequest::SharedCtor() {
language_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  language_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetPresetsListRequest::~GetPresetsListRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.GetPresetsListRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPresetsListRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  language_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetPresetsListRequest::ArenaDtor(void* object) {
  GetPresetsListRequest* _this = reinterpret_cast< GetPresetsListRequest* >(object);
  (void)_this;
}
void GetPresetsListRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPresetsListRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPresetsListRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.GetPresetsListRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  language_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPresetsListRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string language = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_language();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.module.GetPresetsListRequest.language"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPresetsListRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.GetPresetsListRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string language = 1;
  if (!this->_internal_language().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_language().data(), static_cast<int>(this->_internal_language().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.module.GetPresetsListRequest.language");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_language(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.GetPresetsListRequest)
  return target;
}

size_t GetPresetsListRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.GetPresetsListRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string language = 1;
  if (!this->_internal_language().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_language());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPresetsListRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPresetsListRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPresetsListRequest::GetClassData() const { return &_class_data_; }

void GetPresetsListRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPresetsListRequest *>(to)->MergeFrom(
      static_cast<const GetPresetsListRequest &>(from));
}


void GetPresetsListRequest::MergeFrom(const GetPresetsListRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.GetPresetsListRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_language().empty()) {
    _internal_set_language(from._internal_language());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPresetsListRequest::CopyFrom(const GetPresetsListRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.GetPresetsListRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPresetsListRequest::IsInitialized() const {
  return true;
}

void GetPresetsListRequest::InternalSwap(GetPresetsListRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &language_, lhs_arena,
      &other->language_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPresetsListRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[14]);
}

// ===================================================================

class GetPresetsListResponse::_Internal {
 public:
};

GetPresetsListResponse::GetPresetsListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  presets_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.GetPresetsListResponse)
}
GetPresetsListResponse::GetPresetsListResponse(const GetPresetsListResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      presets_(from.presets_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.GetPresetsListResponse)
}

inline void GetPresetsListResponse::SharedCtor() {
}

GetPresetsListResponse::~GetPresetsListResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.GetPresetsListResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetPresetsListResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetPresetsListResponse::ArenaDtor(void* object) {
  GetPresetsListResponse* _this = reinterpret_cast< GetPresetsListResponse* >(object);
  (void)_this;
}
void GetPresetsListResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetPresetsListResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetPresetsListResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.GetPresetsListResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  presets_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetPresetsListResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.module.Preset presets = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_presets(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetPresetsListResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.GetPresetsListResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.Preset presets = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_presets_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_presets(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.GetPresetsListResponse)
  return target;
}

size_t GetPresetsListResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.GetPresetsListResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.module.Preset presets = 1;
  total_size += 1UL * this->_internal_presets_size();
  for (const auto& msg : this->presets_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetPresetsListResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetPresetsListResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetPresetsListResponse::GetClassData() const { return &_class_data_; }

void GetPresetsListResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetPresetsListResponse *>(to)->MergeFrom(
      static_cast<const GetPresetsListResponse &>(from));
}


void GetPresetsListResponse::MergeFrom(const GetPresetsListResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.GetPresetsListResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  presets_.MergeFrom(from.presets_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetPresetsListResponse::CopyFrom(const GetPresetsListResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.GetPresetsListResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetPresetsListResponse::IsInitialized() const {
  return true;
}

void GetPresetsListResponse::InternalSwap(GetPresetsListResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  presets_.InternalSwap(&other->presets_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetPresetsListResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[15]);
}

// ===================================================================

class GetCurrentRobotDefinitionResponse::_Internal {
 public:
  using HasBits = decltype(std::declval<GetCurrentRobotDefinitionResponse>()._has_bits_);
  static const ::carbon::frontend::module::RobotDefinition& current_definition(const GetCurrentRobotDefinitionResponse* msg);
  static void set_has_current_definition(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
};

const ::carbon::frontend::module::RobotDefinition&
GetCurrentRobotDefinitionResponse::_Internal::current_definition(const GetCurrentRobotDefinitionResponse* msg) {
  return *msg->current_definition_;
}
GetCurrentRobotDefinitionResponse::GetCurrentRobotDefinitionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
}
GetCurrentRobotDefinitionResponse::GetCurrentRobotDefinitionResponse(const GetCurrentRobotDefinitionResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_current_definition()) {
    current_definition_ = new ::carbon::frontend::module::RobotDefinition(*from.current_definition_);
  } else {
    current_definition_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
}

inline void GetCurrentRobotDefinitionResponse::SharedCtor() {
current_definition_ = nullptr;
}

GetCurrentRobotDefinitionResponse::~GetCurrentRobotDefinitionResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetCurrentRobotDefinitionResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete current_definition_;
}

void GetCurrentRobotDefinitionResponse::ArenaDtor(void* object) {
  GetCurrentRobotDefinitionResponse* _this = reinterpret_cast< GetCurrentRobotDefinitionResponse* >(object);
  (void)_this;
}
void GetCurrentRobotDefinitionResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetCurrentRobotDefinitionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetCurrentRobotDefinitionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    GOOGLE_DCHECK(current_definition_ != nullptr);
    current_definition_->Clear();
  }
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetCurrentRobotDefinitionResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // optional .carbon.frontend.module.RobotDefinition current_definition = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_current_definition(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetCurrentRobotDefinitionResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // optional .carbon.frontend.module.RobotDefinition current_definition = 1;
  if (_internal_has_current_definition()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::current_definition(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
  return target;
}

size_t GetCurrentRobotDefinitionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // optional .carbon.frontend.module.RobotDefinition current_definition = 1;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *current_definition_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetCurrentRobotDefinitionResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetCurrentRobotDefinitionResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetCurrentRobotDefinitionResponse::GetClassData() const { return &_class_data_; }

void GetCurrentRobotDefinitionResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetCurrentRobotDefinitionResponse *>(to)->MergeFrom(
      static_cast<const GetCurrentRobotDefinitionResponse &>(from));
}


void GetCurrentRobotDefinitionResponse::MergeFrom(const GetCurrentRobotDefinitionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_current_definition()) {
    _internal_mutable_current_definition()->::carbon::frontend::module::RobotDefinition::MergeFrom(from._internal_current_definition());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetCurrentRobotDefinitionResponse::CopyFrom(const GetCurrentRobotDefinitionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetCurrentRobotDefinitionResponse::IsInitialized() const {
  return true;
}

void GetCurrentRobotDefinitionResponse::InternalSwap(GetCurrentRobotDefinitionResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  swap(current_definition_, other->current_definition_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetCurrentRobotDefinitionResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[16]);
}

// ===================================================================

class SetCurrentRobotDefinitionRequest::_Internal {
 public:
  static const ::carbon::frontend::module::RobotDefinition& current_definition(const SetCurrentRobotDefinitionRequest* msg);
};

const ::carbon::frontend::module::RobotDefinition&
SetCurrentRobotDefinitionRequest::_Internal::current_definition(const SetCurrentRobotDefinitionRequest* msg) {
  return *msg->current_definition_;
}
SetCurrentRobotDefinitionRequest::SetCurrentRobotDefinitionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
}
SetCurrentRobotDefinitionRequest::SetCurrentRobotDefinitionRequest(const SetCurrentRobotDefinitionRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_current_definition()) {
    current_definition_ = new ::carbon::frontend::module::RobotDefinition(*from.current_definition_);
  } else {
    current_definition_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
}

inline void SetCurrentRobotDefinitionRequest::SharedCtor() {
current_definition_ = nullptr;
}

SetCurrentRobotDefinitionRequest::~SetCurrentRobotDefinitionRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetCurrentRobotDefinitionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete current_definition_;
}

void SetCurrentRobotDefinitionRequest::ArenaDtor(void* object) {
  SetCurrentRobotDefinitionRequest* _this = reinterpret_cast< SetCurrentRobotDefinitionRequest* >(object);
  (void)_this;
}
void SetCurrentRobotDefinitionRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetCurrentRobotDefinitionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetCurrentRobotDefinitionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && current_definition_ != nullptr) {
    delete current_definition_;
  }
  current_definition_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetCurrentRobotDefinitionRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.module.RobotDefinition current_definition = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_current_definition(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetCurrentRobotDefinitionRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.module.RobotDefinition current_definition = 1;
  if (this->_internal_has_current_definition()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::current_definition(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
  return target;
}

size_t SetCurrentRobotDefinitionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.module.RobotDefinition current_definition = 1;
  if (this->_internal_has_current_definition()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *current_definition_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetCurrentRobotDefinitionRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetCurrentRobotDefinitionRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetCurrentRobotDefinitionRequest::GetClassData() const { return &_class_data_; }

void SetCurrentRobotDefinitionRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetCurrentRobotDefinitionRequest *>(to)->MergeFrom(
      static_cast<const SetCurrentRobotDefinitionRequest &>(from));
}


void SetCurrentRobotDefinitionRequest::MergeFrom(const SetCurrentRobotDefinitionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_current_definition()) {
    _internal_mutable_current_definition()->::carbon::frontend::module::RobotDefinition::MergeFrom(from._internal_current_definition());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetCurrentRobotDefinitionRequest::CopyFrom(const SetCurrentRobotDefinitionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetCurrentRobotDefinitionRequest::IsInitialized() const {
  return true;
}

void SetCurrentRobotDefinitionRequest::InternalSwap(SetCurrentRobotDefinitionRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(current_definition_, other->current_definition_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetCurrentRobotDefinitionRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fmodule_2eproto_getter, &descriptor_table_frontend_2fproto_2fmodule_2eproto_once,
      file_level_metadata_frontend_2fproto_2fmodule_2eproto[17]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace module
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::ModuleIdentity* Arena::CreateMaybeMessage< ::carbon::frontend::module::ModuleIdentity >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::ModuleIdentity >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::GetNextModulesListRequest* Arena::CreateMaybeMessage< ::carbon::frontend::module::GetNextModulesListRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::GetNextModulesListRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::GetNextModulesListResponse* Arena::CreateMaybeMessage< ::carbon::frontend::module::GetNextModulesListResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::GetNextModulesListResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::GetNextActiveModulesRequest* Arena::CreateMaybeMessage< ::carbon::frontend::module::GetNextActiveModulesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::GetNextActiveModulesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::GetNextActiveModulesResponse* Arena::CreateMaybeMessage< ::carbon::frontend::module::GetNextActiveModulesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::GetNextActiveModulesResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::IdentifyModuleRequest* Arena::CreateMaybeMessage< ::carbon::frontend::module::IdentifyModuleRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::IdentifyModuleRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::AssignModuleRequest* Arena::CreateMaybeMessage< ::carbon::frontend::module::AssignModuleRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::AssignModuleRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::ClearModuleAssignmentRequest* Arena::CreateMaybeMessage< ::carbon::frontend::module::ClearModuleAssignmentRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::ClearModuleAssignmentRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::SetModuleSerialRequest* Arena::CreateMaybeMessage< ::carbon::frontend::module::SetModuleSerialRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::SetModuleSerialRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::ModuleDefinition* Arena::CreateMaybeMessage< ::carbon::frontend::module::ModuleDefinition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::ModuleDefinition >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::RowDefinition* Arena::CreateMaybeMessage< ::carbon::frontend::module::RowDefinition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::RowDefinition >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::BarDefinition* Arena::CreateMaybeMessage< ::carbon::frontend::module::BarDefinition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::BarDefinition >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::RobotDefinition* Arena::CreateMaybeMessage< ::carbon::frontend::module::RobotDefinition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::RobotDefinition >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::Preset* Arena::CreateMaybeMessage< ::carbon::frontend::module::Preset >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::Preset >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::GetPresetsListRequest* Arena::CreateMaybeMessage< ::carbon::frontend::module::GetPresetsListRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::GetPresetsListRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::GetPresetsListResponse* Arena::CreateMaybeMessage< ::carbon::frontend::module::GetPresetsListResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::GetPresetsListResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* Arena::CreateMaybeMessage< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::GetCurrentRobotDefinitionResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* Arena::CreateMaybeMessage< ::carbon::frontend::module::SetCurrentRobotDefinitionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::module::SetCurrentRobotDefinitionRequest >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import actuation_tasks_pb2 as frontend_dot_proto_dot_actuation__tasks__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class ActuationTasksServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextGlobalActuationTaskState = channel.unary_unary(
                '/carbon.frontend.actuation_tasks.ActuationTasksService/GetNextGlobalActuationTaskState',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_actuation__tasks__pb2.GlobalActuationTaskState.FromString,
                )
        self.StartGlobalAimbotActuationTask = channel.unary_unary(
                '/carbon.frontend.actuation_tasks.ActuationTasksService/StartGlobalAimbotActuationTask',
                request_serializer=frontend_dot_proto_dot_actuation__tasks__pb2.GlobalAimbotActuationTaskRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.CancelGlobalAimbotActuationTask = channel.unary_unary(
                '/carbon.frontend.actuation_tasks.ActuationTasksService/CancelGlobalAimbotActuationTask',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )


class ActuationTasksServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextGlobalActuationTaskState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartGlobalAimbotActuationTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def CancelGlobalAimbotActuationTask(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ActuationTasksServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextGlobalActuationTaskState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextGlobalActuationTaskState,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_actuation__tasks__pb2.GlobalActuationTaskState.SerializeToString,
            ),
            'StartGlobalAimbotActuationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.StartGlobalAimbotActuationTask,
                    request_deserializer=frontend_dot_proto_dot_actuation__tasks__pb2.GlobalAimbotActuationTaskRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'CancelGlobalAimbotActuationTask': grpc.unary_unary_rpc_method_handler(
                    servicer.CancelGlobalAimbotActuationTask,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.actuation_tasks.ActuationTasksService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ActuationTasksService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextGlobalActuationTaskState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.actuation_tasks.ActuationTasksService/GetNextGlobalActuationTaskState',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_actuation__tasks__pb2.GlobalActuationTaskState.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartGlobalAimbotActuationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.actuation_tasks.ActuationTasksService/StartGlobalAimbotActuationTask',
            frontend_dot_proto_dot_actuation__tasks__pb2.GlobalAimbotActuationTaskRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def CancelGlobalAimbotActuationTask(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.actuation_tasks.ActuationTasksService/CancelGlobalAimbotActuationTask',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

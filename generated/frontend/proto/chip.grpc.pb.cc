// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/chip.proto

#include "frontend/proto/chip.pb.h"
#include "frontend/proto/chip.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace chip {

static const char* ChipService_method_names[] = {
  "/carbon.frontend.chip.ChipService/GetChipMetadata",
  "/carbon.frontend.chip.ChipService/GetDownloadedChipIds",
  "/carbon.frontend.chip.ChipService/GetSyncedChipIds",
};

std::unique_ptr< ChipService::Stub> ChipService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ChipService::Stub> stub(new ChipService::Stub(channel, options));
  return stub;
}

ChipService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetChipMetadata_(ChipService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetDownloadedChipIds_(ChipService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetSyncedChipIds_(ChipService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ChipService::Stub::GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::GetChipMetadataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::GetChipMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetChipMetadata_, context, request, response);
}

void ChipService::Stub::async::GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::GetChipMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipMetadata_, context, request, response, std::move(f));
}

void ChipService::Stub::async::GetChipMetadata(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetChipMetadata_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::GetChipMetadataResponse>* ChipService::Stub::PrepareAsyncGetChipMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::chip::GetChipMetadataResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetChipMetadata_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::GetChipMetadataResponse>* ChipService::Stub::AsyncGetChipMetadataRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetChipMetadataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ChipService::Stub::GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::ChipIdsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetDownloadedChipIds_, context, request, response);
}

void ChipService::Stub::async::GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDownloadedChipIds_, context, request, response, std::move(f));
}

void ChipService::Stub::async::GetDownloadedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetDownloadedChipIds_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>* ChipService::Stub::PrepareAsyncGetDownloadedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::chip::ChipIdsResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetDownloadedChipIds_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>* ChipService::Stub::AsyncGetDownloadedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetDownloadedChipIdsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ChipService::Stub::GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::chip::ChipIdsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetSyncedChipIds_, context, request, response);
}

void ChipService::Stub::async::GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSyncedChipIds_, context, request, response, std::move(f));
}

void ChipService::Stub::async::GetSyncedChipIds(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetSyncedChipIds_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>* ChipService::Stub::PrepareAsyncGetSyncedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::chip::ChipIdsResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetSyncedChipIds_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::chip::ChipIdsResponse>* ChipService::Stub::AsyncGetSyncedChipIdsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetSyncedChipIdsRaw(context, request, cq);
  result->StartCall();
  return result;
}

ChipService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ChipService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ChipService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::chip::GetChipMetadataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ChipService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::chip::GetChipMetadataResponse* resp) {
               return service->GetChipMetadata(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ChipService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ChipService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ChipService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::chip::ChipIdsResponse* resp) {
               return service->GetDownloadedChipIds(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ChipService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ChipService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::chip::ChipIdsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ChipService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::chip::ChipIdsResponse* resp) {
               return service->GetSyncedChipIds(ctx, req, resp);
             }, this)));
}

ChipService::Service::~Service() {
}

::grpc::Status ChipService::Service::GetChipMetadata(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::GetChipMetadataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ChipService::Service::GetDownloadedChipIds(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ChipService::Service::GetSyncedChipIds(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::chip::ChipIdsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace chip


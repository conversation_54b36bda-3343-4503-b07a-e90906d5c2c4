// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/features.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ffeatures_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ffeatures_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2ffeatures_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2ffeatures_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2ffeatures_2eproto;
namespace carbon {
namespace frontend {
namespace features {
class RobotConfiguration;
struct RobotConfigurationDefaultTypeInternal;
extern RobotConfigurationDefaultTypeInternal _RobotConfiguration_default_instance_;
class RobotConfiguration_RowConfigurationEntry_DoNotUse;
struct RobotConfiguration_RowConfigurationEntry_DoNotUseDefaultTypeInternal;
extern RobotConfiguration_RowConfigurationEntry_DoNotUseDefaultTypeInternal _RobotConfiguration_RowConfigurationEntry_DoNotUse_default_instance_;
class RowConfiguration;
struct RowConfigurationDefaultTypeInternal;
extern RowConfigurationDefaultTypeInternal _RowConfiguration_default_instance_;
}  // namespace features
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::features::RobotConfiguration* Arena::CreateMaybeMessage<::carbon::frontend::features::RobotConfiguration>(Arena*);
template<> ::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::features::RobotConfiguration_RowConfigurationEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::features::RowConfiguration* Arena::CreateMaybeMessage<::carbon::frontend::features::RowConfiguration>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace features {

enum Generation : int {
  Undefined = 0,
  Slayer = 1,
  Reaper = 2,
  Generation_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Generation_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Generation_IsValid(int value);
constexpr Generation Generation_MIN = Undefined;
constexpr Generation Generation_MAX = Reaper;
constexpr int Generation_ARRAYSIZE = Generation_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Generation_descriptor();
template<typename T>
inline const std::string& Generation_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Generation>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Generation_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Generation_descriptor(), enum_t_value);
}
inline bool Generation_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Generation* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Generation>(
    Generation_descriptor(), name, value);
}
// ===================================================================

class RowConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.features.RowConfiguration) */ {
 public:
  inline RowConfiguration() : RowConfiguration(nullptr) {}
  ~RowConfiguration() override;
  explicit constexpr RowConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RowConfiguration(const RowConfiguration& from);
  RowConfiguration(RowConfiguration&& from) noexcept
    : RowConfiguration() {
    *this = ::std::move(from);
  }

  inline RowConfiguration& operator=(const RowConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline RowConfiguration& operator=(RowConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RowConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const RowConfiguration* internal_default_instance() {
    return reinterpret_cast<const RowConfiguration*>(
               &_RowConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(RowConfiguration& a, RowConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(RowConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RowConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RowConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RowConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RowConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RowConfiguration& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RowConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.features.RowConfiguration";
  }
  protected:
  explicit RowConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNumPredictsFieldNumber = 1,
    kNumTargetsFieldNumber = 2,
  };
  // int32 num_predicts = 1;
  void clear_num_predicts();
  int32_t num_predicts() const;
  void set_num_predicts(int32_t value);
  private:
  int32_t _internal_num_predicts() const;
  void _internal_set_num_predicts(int32_t value);
  public:

  // int32 num_targets = 2;
  void clear_num_targets();
  int32_t num_targets() const;
  void set_num_targets(int32_t value);
  private:
  int32_t _internal_num_targets() const;
  void _internal_set_num_targets(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.features.RowConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t num_predicts_;
  int32_t num_targets_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ffeatures_2eproto;
};
// -------------------------------------------------------------------

class RobotConfiguration_RowConfigurationEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RobotConfiguration_RowConfigurationEntry_DoNotUse, 
    int32_t, ::carbon::frontend::features::RowConfiguration,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<RobotConfiguration_RowConfigurationEntry_DoNotUse, 
    int32_t, ::carbon::frontend::features::RowConfiguration,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  RobotConfiguration_RowConfigurationEntry_DoNotUse();
  explicit constexpr RobotConfiguration_RowConfigurationEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit RobotConfiguration_RowConfigurationEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const RobotConfiguration_RowConfigurationEntry_DoNotUse& other);
  static const RobotConfiguration_RowConfigurationEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const RobotConfiguration_RowConfigurationEntry_DoNotUse*>(&_RobotConfiguration_RowConfigurationEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class RobotConfiguration final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.features.RobotConfiguration) */ {
 public:
  inline RobotConfiguration() : RobotConfiguration(nullptr) {}
  ~RobotConfiguration() override;
  explicit constexpr RobotConfiguration(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RobotConfiguration(const RobotConfiguration& from);
  RobotConfiguration(RobotConfiguration&& from) noexcept
    : RobotConfiguration() {
    *this = ::std::move(from);
  }

  inline RobotConfiguration& operator=(const RobotConfiguration& from) {
    CopyFrom(from);
    return *this;
  }
  inline RobotConfiguration& operator=(RobotConfiguration&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RobotConfiguration& default_instance() {
    return *internal_default_instance();
  }
  static inline const RobotConfiguration* internal_default_instance() {
    return reinterpret_cast<const RobotConfiguration*>(
               &_RobotConfiguration_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(RobotConfiguration& a, RobotConfiguration& b) {
    a.Swap(&b);
  }
  inline void Swap(RobotConfiguration* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RobotConfiguration* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RobotConfiguration* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RobotConfiguration>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RobotConfiguration& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RobotConfiguration& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RobotConfiguration* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.features.RobotConfiguration";
  }
  protected:
  explicit RobotConfiguration(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kRowConfigurationFieldNumber = 2,
    kNumRowsFieldNumber = 1,
    kGenerationFieldNumber = 3,
  };
  // map<int32, .carbon.frontend.features.RowConfiguration> row_configuration = 2;
  int row_configuration_size() const;
  private:
  int _internal_row_configuration_size() const;
  public:
  void clear_row_configuration();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >&
      _internal_row_configuration() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >*
      _internal_mutable_row_configuration();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >&
      row_configuration() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >*
      mutable_row_configuration();

  // int32 num_rows = 1;
  void clear_num_rows();
  int32_t num_rows() const;
  void set_num_rows(int32_t value);
  private:
  int32_t _internal_num_rows() const;
  void _internal_set_num_rows(int32_t value);
  public:

  // .carbon.frontend.features.Generation generation = 3;
  void clear_generation();
  ::carbon::frontend::features::Generation generation() const;
  void set_generation(::carbon::frontend::features::Generation value);
  private:
  ::carbon::frontend::features::Generation _internal_generation() const;
  void _internal_set_generation(::carbon::frontend::features::Generation value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.features.RobotConfiguration)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      RobotConfiguration_RowConfigurationEntry_DoNotUse,
      int32_t, ::carbon::frontend::features::RowConfiguration,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> row_configuration_;
  int32_t num_rows_;
  int generation_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2ffeatures_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// RowConfiguration

// int32 num_predicts = 1;
inline void RowConfiguration::clear_num_predicts() {
  num_predicts_ = 0;
}
inline int32_t RowConfiguration::_internal_num_predicts() const {
  return num_predicts_;
}
inline int32_t RowConfiguration::num_predicts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.RowConfiguration.num_predicts)
  return _internal_num_predicts();
}
inline void RowConfiguration::_internal_set_num_predicts(int32_t value) {
  
  num_predicts_ = value;
}
inline void RowConfiguration::set_num_predicts(int32_t value) {
  _internal_set_num_predicts(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.features.RowConfiguration.num_predicts)
}

// int32 num_targets = 2;
inline void RowConfiguration::clear_num_targets() {
  num_targets_ = 0;
}
inline int32_t RowConfiguration::_internal_num_targets() const {
  return num_targets_;
}
inline int32_t RowConfiguration::num_targets() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.RowConfiguration.num_targets)
  return _internal_num_targets();
}
inline void RowConfiguration::_internal_set_num_targets(int32_t value) {
  
  num_targets_ = value;
}
inline void RowConfiguration::set_num_targets(int32_t value) {
  _internal_set_num_targets(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.features.RowConfiguration.num_targets)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// RobotConfiguration

// int32 num_rows = 1;
inline void RobotConfiguration::clear_num_rows() {
  num_rows_ = 0;
}
inline int32_t RobotConfiguration::_internal_num_rows() const {
  return num_rows_;
}
inline int32_t RobotConfiguration::num_rows() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.RobotConfiguration.num_rows)
  return _internal_num_rows();
}
inline void RobotConfiguration::_internal_set_num_rows(int32_t value) {
  
  num_rows_ = value;
}
inline void RobotConfiguration::set_num_rows(int32_t value) {
  _internal_set_num_rows(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.features.RobotConfiguration.num_rows)
}

// map<int32, .carbon.frontend.features.RowConfiguration> row_configuration = 2;
inline int RobotConfiguration::_internal_row_configuration_size() const {
  return row_configuration_.size();
}
inline int RobotConfiguration::row_configuration_size() const {
  return _internal_row_configuration_size();
}
inline void RobotConfiguration::clear_row_configuration() {
  row_configuration_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >&
RobotConfiguration::_internal_row_configuration() const {
  return row_configuration_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >&
RobotConfiguration::row_configuration() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.features.RobotConfiguration.row_configuration)
  return _internal_row_configuration();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >*
RobotConfiguration::_internal_mutable_row_configuration() {
  return row_configuration_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::features::RowConfiguration >*
RobotConfiguration::mutable_row_configuration() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.features.RobotConfiguration.row_configuration)
  return _internal_mutable_row_configuration();
}

// .carbon.frontend.features.Generation generation = 3;
inline void RobotConfiguration::clear_generation() {
  generation_ = 0;
}
inline ::carbon::frontend::features::Generation RobotConfiguration::_internal_generation() const {
  return static_cast< ::carbon::frontend::features::Generation >(generation_);
}
inline ::carbon::frontend::features::Generation RobotConfiguration::generation() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.features.RobotConfiguration.generation)
  return _internal_generation();
}
inline void RobotConfiguration::_internal_set_generation(::carbon::frontend::features::Generation value) {
  
  generation_ = value;
}
inline void RobotConfiguration::set_generation(::carbon::frontend::features::Generation value) {
  _internal_set_generation(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.features.RobotConfiguration.generation)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace features
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::features::Generation> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::features::Generation>() {
  return ::carbon::frontend::features::Generation_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2ffeatures_2eproto

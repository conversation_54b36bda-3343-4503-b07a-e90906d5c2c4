// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/laser.proto

#include "frontend/proto/laser.pb.h"
#include "frontend/proto/laser.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace laser {

static const char* LaserService_method_names[] = {
  "/carbon.frontend.laser.LaserService/FireLaser",
  "/carbon.frontend.laser.LaserService/GetNextLaserState",
  "/carbon.frontend.laser.LaserService/ToggleLaserEnabled",
  "/carbon.frontend.laser.LaserService/EnableRow",
  "/carbon.frontend.laser.LaserService/DisableRow",
  "/carbon.frontend.laser.LaserService/ResetLaserMetrics",
  "/carbon.frontend.laser.LaserService/FixLaserMetrics",
  "/carbon.frontend.laser.LaserService/SetLaserPower",
};

std::unique_ptr< LaserService::Stub> LaserService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< LaserService::Stub> stub(new LaserService::Stub(channel, options));
  return stub;
}

LaserService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_FireLaser_(LaserService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::CLIENT_STREAMING, channel)
  , rpcmethod_GetNextLaserState_(LaserService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ToggleLaserEnabled_(LaserService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_EnableRow_(LaserService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DisableRow_(LaserService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResetLaserMetrics_(LaserService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_FixLaserMetrics_(LaserService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetLaserPower_(LaserService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::ClientWriter< ::carbon::frontend::camera::CameraRequest>* LaserService::Stub::FireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::ClientWriterFactory< ::carbon::frontend::camera::CameraRequest>::Create(channel_.get(), rpcmethod_FireLaser_, context, response);
}

void LaserService::Stub::async::FireLaser(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::ClientWriteReactor< ::carbon::frontend::camera::CameraRequest>* reactor) {
  ::grpc::internal::ClientCallbackWriterFactory< ::carbon::frontend::camera::CameraRequest>::Create(stub_->channel_.get(), stub_->rpcmethod_FireLaser_, context, response, reactor);
}

::grpc::ClientAsyncWriter< ::carbon::frontend::camera::CameraRequest>* LaserService::Stub::AsyncFireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::carbon::frontend::camera::CameraRequest>::Create(channel_.get(), cq, rpcmethod_FireLaser_, context, response, true, tag);
}

::grpc::ClientAsyncWriter< ::carbon::frontend::camera::CameraRequest>* LaserService::Stub::PrepareAsyncFireLaserRaw(::grpc::ClientContext* context, ::carbon::frontend::util::Empty* response, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncWriterFactory< ::carbon::frontend::camera::CameraRequest>::Create(channel_.get(), cq, rpcmethod_FireLaser_, context, response, false, nullptr);
}

::grpc::Status LaserService::Stub::GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::laser::LaserStateList* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::laser::LaserStateList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextLaserState_, context, request, response);
}

void LaserService::Stub::async::GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::laser::LaserStateList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextLaserState_, context, request, response, std::move(f));
}

void LaserService::Stub::async::GetNextLaserState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextLaserState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::laser::LaserStateList>* LaserService::Stub::PrepareAsyncGetNextLaserStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::laser::LaserStateList, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextLaserState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::laser::LaserStateList>* LaserService::Stub::AsyncGetNextLaserStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextLaserStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status LaserService::Stub::ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ToggleLaserEnabled_, context, request, response);
}

void LaserService::Stub::async::ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ToggleLaserEnabled_, context, request, response, std::move(f));
}

void LaserService::Stub::async::ToggleLaserEnabled(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ToggleLaserEnabled_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::PrepareAsyncToggleLaserEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::laser::LaserDescriptor, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ToggleLaserEnabled_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::AsyncToggleLaserEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncToggleLaserEnabledRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status LaserService::Stub::EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_EnableRow_, context, request, response);
}

void LaserService::Stub::async::EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_EnableRow_, context, request, response, std::move(f));
}

void LaserService::Stub::async::EnableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_EnableRow_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::PrepareAsyncEnableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::laser::RowRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_EnableRow_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::AsyncEnableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncEnableRowRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status LaserService::Stub::DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DisableRow_, context, request, response);
}

void LaserService::Stub::async::DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DisableRow_, context, request, response, std::move(f));
}

void LaserService::Stub::async::DisableRow(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DisableRow_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::PrepareAsyncDisableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::laser::RowRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DisableRow_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::AsyncDisableRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::RowRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDisableRowRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status LaserService::Stub::ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResetLaserMetrics_, context, request, response);
}

void LaserService::Stub::async::ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetLaserMetrics_, context, request, response, std::move(f));
}

void LaserService::Stub::async::ResetLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetLaserMetrics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::PrepareAsyncResetLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::laser::LaserDescriptor, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResetLaserMetrics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::AsyncResetLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::LaserDescriptor& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResetLaserMetricsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status LaserService::Stub::FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::laser::FixLaserMetricsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_FixLaserMetrics_, context, request, response);
}

void LaserService::Stub::async::FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::laser::FixLaserMetricsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FixLaserMetrics_, context, request, response, std::move(f));
}

void LaserService::Stub::async::FixLaserMetrics(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_FixLaserMetrics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::PrepareAsyncFixLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::laser::FixLaserMetricsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_FixLaserMetrics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::AsyncFixLaserMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncFixLaserMetricsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status LaserService::Stub::SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::laser::SetLaserPowerRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetLaserPower_, context, request, response);
}

void LaserService::Stub::async::SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::laser::SetLaserPowerRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLaserPower_, context, request, response, std::move(f));
}

void LaserService::Stub::async::SetLaserPower(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetLaserPower_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::PrepareAsyncSetLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::laser::SetLaserPowerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetLaserPower_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* LaserService::Stub::AsyncSetLaserPowerRaw(::grpc::ClientContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetLaserPowerRaw(context, request, cq);
  result->StartCall();
  return result;
}

LaserService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LaserService_method_names[0],
      ::grpc::internal::RpcMethod::CLIENT_STREAMING,
      new ::grpc::internal::ClientStreamingHandler< LaserService::Service, ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty>(
          [](LaserService::Service* service,
             ::grpc::ServerContext* ctx,
             ::grpc::ServerReader<::carbon::frontend::camera::CameraRequest>* reader,
             ::carbon::frontend::util::Empty* resp) {
               return service->FireLaser(ctx, reader, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LaserService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LaserService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::laser::LaserStateList, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LaserService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::laser::LaserStateList* resp) {
               return service->GetNextLaserState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LaserService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LaserService::Service, ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LaserService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::laser::LaserDescriptor* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ToggleLaserEnabled(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LaserService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LaserService::Service, ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LaserService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::laser::RowRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->EnableRow(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LaserService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LaserService::Service, ::carbon::frontend::laser::RowRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LaserService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::laser::RowRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DisableRow(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LaserService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LaserService::Service, ::carbon::frontend::laser::LaserDescriptor, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LaserService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::laser::LaserDescriptor* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ResetLaserMetrics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LaserService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LaserService::Service, ::carbon::frontend::laser::FixLaserMetricsRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LaserService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::laser::FixLaserMetricsRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->FixLaserMetrics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      LaserService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< LaserService::Service, ::carbon::frontend::laser::SetLaserPowerRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](LaserService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::laser::SetLaserPowerRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetLaserPower(ctx, req, resp);
             }, this)));
}

LaserService::Service::~Service() {
}

::grpc::Status LaserService::Service::FireLaser(::grpc::ServerContext* context, ::grpc::ServerReader< ::carbon::frontend::camera::CameraRequest>* reader, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) reader;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LaserService::Service::GetNextLaserState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::laser::LaserStateList* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LaserService::Service::ToggleLaserEnabled(::grpc::ServerContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LaserService::Service::EnableRow(::grpc::ServerContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LaserService::Service::DisableRow(::grpc::ServerContext* context, const ::carbon::frontend::laser::RowRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LaserService::Service::ResetLaserMetrics(::grpc::ServerContext* context, const ::carbon::frontend::laser::LaserDescriptor* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LaserService::Service::FixLaserMetrics(::grpc::ServerContext* context, const ::carbon::frontend::laser::FixLaserMetricsRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status LaserService::Service::SetLaserPower(::grpc::ServerContext* context, const ::carbon::frontend::laser::SetLaserPowerRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace laser


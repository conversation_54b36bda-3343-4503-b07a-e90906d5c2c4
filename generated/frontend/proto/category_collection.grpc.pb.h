// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/category_collection.proto
#ifndef GRPC_frontend_2fproto_2fcategory_5fcollection_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fcategory_5fcollection_2eproto__INCLUDED

#include "frontend/proto/category_collection.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace category_collection {

class CategoryCollectionService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.category_collection.CategoryCollectionService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>> AsyncGetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>>(AsyncGetNextCategoryCollectionsDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>> PrepareAsyncGetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>>(PrepareAsyncGetNextCategoryCollectionsDataRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>> AsyncGetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>>(AsyncGetNextActiveCategoryCollectionIdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>> PrepareAsyncGetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>>(PrepareAsyncGetNextActiveCategoryCollectionIdRaw(context, request, cq));
    }
    virtual ::grpc::Status SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetActiveCategoryCollectionIdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetActiveCategoryCollectionIdRaw(context, request, cq));
    }
    virtual ::grpc::Status ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncReloadCategoryCollectionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncReloadCategoryCollectionRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* AsyncGetNextCategoryCollectionsDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* PrepareAsyncGetNextCategoryCollectionsDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* AsyncGetNextActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* PrepareAsyncGetNextActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncReloadCategoryCollectionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncReloadCategoryCollectionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>> AsyncGetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>>(AsyncGetNextCategoryCollectionsDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>> PrepareAsyncGetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>>(PrepareAsyncGetNextCategoryCollectionsDataRaw(context, request, cq));
    }
    ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>> AsyncGetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>>(AsyncGetNextActiveCategoryCollectionIdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>> PrepareAsyncGetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>>(PrepareAsyncGetNextActiveCategoryCollectionIdRaw(context, request, cq));
    }
    ::grpc::Status SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetActiveCategoryCollectionIdRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetActiveCategoryCollectionIdRaw(context, request, cq));
    }
    ::grpc::Status ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncReloadCategoryCollectionRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncReloadCategoryCollectionRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextCategoryCollectionsData(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetActiveCategoryCollectionId(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ReloadCategoryCollection(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* AsyncGetNextCategoryCollectionsDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* PrepareAsyncGetNextCategoryCollectionsDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* AsyncGetNextActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* PrepareAsyncGetNextActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetActiveCategoryCollectionIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncReloadCategoryCollectionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncReloadCategoryCollectionRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextCategoryCollectionsData_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextActiveCategoryCollectionId_;
    const ::grpc::internal::RpcMethod rpcmethod_SetActiveCategoryCollectionId_;
    const ::grpc::internal::RpcMethod rpcmethod_ReloadCategoryCollection_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextCategoryCollectionsData(::grpc::ServerContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response);
    virtual ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ServerContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response);
    virtual ::grpc::Status SetActiveCategoryCollectionId(::grpc::ServerContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status ReloadCategoryCollection(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextCategoryCollectionsData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextCategoryCollectionsData() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextCategoryCollectionsData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryCollectionsData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* /*request*/, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCategoryCollectionsData(::grpc::ServerContext* context, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetNextActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveCategoryCollectionId(::grpc::ServerContext* context, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_SetActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveCategoryCollectionId(::grpc::ServerContext* context, ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ReloadCategoryCollection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ReloadCategoryCollection() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_ReloadCategoryCollection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadCategoryCollection(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReloadCategoryCollection(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextCategoryCollectionsData<WithAsyncMethod_GetNextActiveCategoryCollectionId<WithAsyncMethod_SetActiveCategoryCollectionId<WithAsyncMethod_ReloadCategoryCollection<Service > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextCategoryCollectionsData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextCategoryCollectionsData() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* request, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* response) { return this->GetNextCategoryCollectionsData(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextCategoryCollectionsData(
        ::grpc::MessageAllocator< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextCategoryCollectionsData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryCollectionsData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* /*request*/, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCategoryCollectionsData(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* /*request*/, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* request, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* response) { return this->GetNextActiveCategoryCollectionId(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextActiveCategoryCollectionId(
        ::grpc::MessageAllocator< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveCategoryCollectionId(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* request, ::carbon::frontend::util::Empty* response) { return this->SetActiveCategoryCollectionId(context, request, response); }));}
    void SetMessageAllocatorFor_SetActiveCategoryCollectionId(
        ::grpc::MessageAllocator< ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveCategoryCollectionId(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ReloadCategoryCollection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ReloadCategoryCollection() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->ReloadCategoryCollection(context, request, response); }));}
    void SetMessageAllocatorFor_ReloadCategoryCollection(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ReloadCategoryCollection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadCategoryCollection(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ReloadCategoryCollection(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextCategoryCollectionsData<WithCallbackMethod_GetNextActiveCategoryCollectionId<WithCallbackMethod_SetActiveCategoryCollectionId<WithCallbackMethod_ReloadCategoryCollection<Service > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextCategoryCollectionsData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextCategoryCollectionsData() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextCategoryCollectionsData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryCollectionsData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* /*request*/, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetNextActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_SetActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ReloadCategoryCollection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ReloadCategoryCollection() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_ReloadCategoryCollection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadCategoryCollection(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextCategoryCollectionsData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextCategoryCollectionsData() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextCategoryCollectionsData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryCollectionsData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* /*request*/, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCategoryCollectionsData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetNextActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextActiveCategoryCollectionId(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_SetActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetActiveCategoryCollectionId(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ReloadCategoryCollection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ReloadCategoryCollection() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_ReloadCategoryCollection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadCategoryCollection(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestReloadCategoryCollection(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextCategoryCollectionsData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextCategoryCollectionsData() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextCategoryCollectionsData(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextCategoryCollectionsData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryCollectionsData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* /*request*/, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCategoryCollectionsData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextActiveCategoryCollectionId(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextActiveCategoryCollectionId(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetActiveCategoryCollectionId(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetActiveCategoryCollectionId(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ReloadCategoryCollection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ReloadCategoryCollection() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ReloadCategoryCollection(context, request, response); }));
    }
    ~WithRawCallbackMethod_ReloadCategoryCollection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ReloadCategoryCollection(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ReloadCategoryCollection(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextCategoryCollectionsData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextCategoryCollectionsData() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* streamer) {
                       return this->StreamedGetNextCategoryCollectionsData(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextCategoryCollectionsData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextCategoryCollectionsData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* /*request*/, ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextCategoryCollectionsData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest,::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* streamer) {
                       return this->StreamedGetNextActiveCategoryCollectionId(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextActiveCategoryCollectionId(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest,::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetActiveCategoryCollectionId : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetActiveCategoryCollectionId() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetActiveCategoryCollectionId(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetActiveCategoryCollectionId() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetActiveCategoryCollectionId(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetActiveCategoryCollectionId(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ReloadCategoryCollection : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ReloadCategoryCollection() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedReloadCategoryCollection(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ReloadCategoryCollection() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ReloadCategoryCollection(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedReloadCategoryCollection(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextCategoryCollectionsData<WithStreamedUnaryMethod_GetNextActiveCategoryCollectionId<WithStreamedUnaryMethod_SetActiveCategoryCollectionId<WithStreamedUnaryMethod_ReloadCategoryCollection<Service > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextCategoryCollectionsData<WithStreamedUnaryMethod_GetNextActiveCategoryCollectionId<WithStreamedUnaryMethod_SetActiveCategoryCollectionId<WithStreamedUnaryMethod_ReloadCategoryCollection<Service > > > > StreamedService;
};

}  // namespace category_collection
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fcategory_5fcollection_2eproto__INCLUDED

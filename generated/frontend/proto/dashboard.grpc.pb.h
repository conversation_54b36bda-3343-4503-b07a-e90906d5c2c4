// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/dashboard.proto
#ifndef GRPC_frontend_2fproto_2fdashboard_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fdashboard_2eproto__INCLUDED

#include "frontend/proto/dashboard.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace dashboard {

class DashboardService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.dashboard.DashboardService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncToggleRowRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncToggleRowRaw(context, request, cq));
    }
    virtual ::grpc::Status ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncToggleLasersRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncToggleLasersRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::dashboard::DashboardStateMessage* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::DashboardStateMessage>> AsyncGetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::DashboardStateMessage>>(AsyncGetNextDashboardStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::DashboardStateMessage>> PrepareAsyncGetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::DashboardStateMessage>>(PrepareAsyncGetNextDashboardStateRaw(context, request, cq));
    }
    virtual ::grpc::Status GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::dashboard::CropModelOptions* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::CropModelOptions>> AsyncGetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::CropModelOptions>>(AsyncGetCropModelOptionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::CropModelOptions>> PrepareAsyncGetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::CropModelOptions>>(PrepareAsyncGetCropModelOptionsRaw(context, request, cq));
    }
    virtual ::grpc::Status SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetCropModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetCropModelRaw(context, request, cq));
    }
    virtual ::grpc::Status GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::dashboard::WeedingVelocity* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::WeedingVelocity>> AsyncGetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::WeedingVelocity>>(AsyncGetNextWeedingVelocityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::WeedingVelocity>> PrepareAsyncGetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::WeedingVelocity>>(PrepareAsyncGetNextWeedingVelocityRaw(context, request, cq));
    }
    virtual ::grpc::Status SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetTargetingStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetTargetingStateRaw(context, request, cq));
    }
    virtual ::grpc::Status SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetRowSpacingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetRowSpacingRaw(context, request, cq));
    }
    virtual ::grpc::Status SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::carbon::frontend::util::Empty* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> AsyncSetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(AsyncSetCruiseEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>> PrepareAsyncSetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>>(PrepareAsyncSetCruiseEnabledRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncToggleRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncToggleRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncToggleLasersRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncToggleLasersRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::DashboardStateMessage>* AsyncGetNextDashboardStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::DashboardStateMessage>* PrepareAsyncGetNextDashboardStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::CropModelOptions>* AsyncGetCropModelOptionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::CropModelOptions>* PrepareAsyncGetCropModelOptionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetCropModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetCropModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::WeedingVelocity>* AsyncGetNextWeedingVelocityRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::dashboard::WeedingVelocity>* PrepareAsyncGetNextWeedingVelocityRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetRowSpacingRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetRowSpacingRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* AsyncSetCruiseEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::util::Empty>* PrepareAsyncSetCruiseEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncToggleRowRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncToggleRowRaw(context, request, cq));
    }
    ::grpc::Status ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncToggleLasersRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncToggleLasersRaw(context, request, cq));
    }
    ::grpc::Status GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::dashboard::DashboardStateMessage* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::DashboardStateMessage>> AsyncGetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::DashboardStateMessage>>(AsyncGetNextDashboardStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::DashboardStateMessage>> PrepareAsyncGetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::DashboardStateMessage>>(PrepareAsyncGetNextDashboardStateRaw(context, request, cq));
    }
    ::grpc::Status GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::dashboard::CropModelOptions* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::CropModelOptions>> AsyncGetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::CropModelOptions>>(AsyncGetCropModelOptionsRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::CropModelOptions>> PrepareAsyncGetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::CropModelOptions>>(PrepareAsyncGetCropModelOptionsRaw(context, request, cq));
    }
    ::grpc::Status SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetCropModelRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetCropModelRaw(context, request, cq));
    }
    ::grpc::Status GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::dashboard::WeedingVelocity* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::WeedingVelocity>> AsyncGetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::WeedingVelocity>>(AsyncGetNextWeedingVelocityRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::WeedingVelocity>> PrepareAsyncGetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::WeedingVelocity>>(PrepareAsyncGetNextWeedingVelocityRaw(context, request, cq));
    }
    ::grpc::Status SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetTargetingStateRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetTargetingStateRaw(context, request, cq));
    }
    ::grpc::Status SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetRowSpacingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetRowSpacingRaw(context, request, cq));
    }
    ::grpc::Status SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::carbon::frontend::util::Empty* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> AsyncSetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(AsyncSetCruiseEnabledRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>> PrepareAsyncSetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>>(PrepareAsyncSetCruiseEnabledRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ToggleRow(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void ToggleLasers(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response, std::function<void(::grpc::Status)>) override;
      void GetNextDashboardState(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response, std::function<void(::grpc::Status)>) override;
      void GetCropModelOptions(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetCropModel(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response, std::function<void(::grpc::Status)>) override;
      void GetNextWeedingVelocity(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetTargetingState(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetRowSpacing(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)>) override;
      void SetCruiseEnabled(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncToggleRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncToggleRowRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowId& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncToggleLasersRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncToggleLasersRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::DashboardStateMessage>* AsyncGetNextDashboardStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::DashboardStateMessage>* PrepareAsyncGetNextDashboardStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::CropModelOptions>* AsyncGetCropModelOptionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::CropModelOptions>* PrepareAsyncGetCropModelOptionsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetCropModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetCropModelRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CropModel& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::WeedingVelocity>* AsyncGetNextWeedingVelocityRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::dashboard::WeedingVelocity>* PrepareAsyncGetNextWeedingVelocityRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetTargetingStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::TargetingState& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetRowSpacingRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetRowSpacingRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::RowSpacing& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AsyncSetCruiseEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* PrepareAsyncSetCruiseEnabledRaw(::grpc::ClientContext* context, const ::carbon::frontend::dashboard::CruiseEnable& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_ToggleRow_;
    const ::grpc::internal::RpcMethod rpcmethod_ToggleLasers_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextDashboardState_;
    const ::grpc::internal::RpcMethod rpcmethod_GetCropModelOptions_;
    const ::grpc::internal::RpcMethod rpcmethod_SetCropModel_;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextWeedingVelocity_;
    const ::grpc::internal::RpcMethod rpcmethod_SetTargetingState_;
    const ::grpc::internal::RpcMethod rpcmethod_SetRowSpacing_;
    const ::grpc::internal::RpcMethod rpcmethod_SetCruiseEnabled_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status ToggleRow(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status ToggleLasers(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextDashboardState(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response);
    virtual ::grpc::Status GetCropModelOptions(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response);
    virtual ::grpc::Status SetCropModel(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status GetNextWeedingVelocity(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response);
    virtual ::grpc::Status SetTargetingState(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status SetRowSpacing(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response);
    virtual ::grpc::Status SetCruiseEnabled(::grpc::ServerContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_ToggleRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ToggleRow() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_ToggleRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowId* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestToggleRow(::grpc::ServerContext* context, ::carbon::frontend::dashboard::RowId* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_ToggleLasers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_ToggleLasers() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_ToggleLasers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLasers(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestToggleLasers(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextDashboardState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextDashboardState() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetNextDashboardState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDashboardState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::DashboardStateMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextDashboardState(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::dashboard::DashboardStateMessage>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetCropModelOptions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetCropModelOptions() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_GetCropModelOptions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCropModelOptions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::dashboard::CropModelOptions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCropModelOptions(::grpc::ServerContext* context, ::carbon::frontend::util::Empty* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::dashboard::CropModelOptions>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetCropModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetCropModel() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_SetCropModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCropModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CropModel* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetCropModel(::grpc::ServerContext* context, ::carbon::frontend::dashboard::CropModel* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextWeedingVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextWeedingVelocity() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_GetNextWeedingVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextWeedingVelocity(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::WeedingVelocity* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextWeedingVelocity(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::dashboard::WeedingVelocity>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetTargetingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetTargetingState() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_SetTargetingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTargetingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::TargetingState* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetTargetingState(::grpc::ServerContext* context, ::carbon::frontend::dashboard::TargetingState* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetRowSpacing : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetRowSpacing() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_SetRowSpacing() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRowSpacing(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowSpacing* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetRowSpacing(::grpc::ServerContext* context, ::carbon::frontend::dashboard::RowSpacing* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetCruiseEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetCruiseEnabled() {
      ::grpc::Service::MarkMethodAsync(8);
    }
    ~WithAsyncMethod_SetCruiseEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCruiseEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CruiseEnable* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetCruiseEnabled(::grpc::ServerContext* context, ::carbon::frontend::dashboard::CruiseEnable* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::util::Empty>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_ToggleRow<WithAsyncMethod_ToggleLasers<WithAsyncMethod_GetNextDashboardState<WithAsyncMethod_GetCropModelOptions<WithAsyncMethod_SetCropModel<WithAsyncMethod_GetNextWeedingVelocity<WithAsyncMethod_SetTargetingState<WithAsyncMethod_SetRowSpacing<WithAsyncMethod_SetCruiseEnabled<Service > > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_ToggleRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ToggleRow() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::RowId, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::dashboard::RowId* request, ::carbon::frontend::util::Empty* response) { return this->ToggleRow(context, request, response); }));}
    void SetMessageAllocatorFor_ToggleRow(
        ::grpc::MessageAllocator< ::carbon::frontend::dashboard::RowId, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::RowId, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ToggleRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowId* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ToggleRow(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::dashboard::RowId* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_ToggleLasers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_ToggleLasers() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) { return this->ToggleLasers(context, request, response); }));}
    void SetMessageAllocatorFor_ToggleLasers(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_ToggleLasers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLasers(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ToggleLasers(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextDashboardState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextDashboardState() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::DashboardStateMessage>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::DashboardStateMessage* response) { return this->GetNextDashboardState(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextDashboardState(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::DashboardStateMessage>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::DashboardStateMessage>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextDashboardState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDashboardState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::DashboardStateMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextDashboardState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::DashboardStateMessage* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetCropModelOptions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetCropModelOptions() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModelOptions>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::dashboard::CropModelOptions* response) { return this->GetCropModelOptions(context, request, response); }));}
    void SetMessageAllocatorFor_GetCropModelOptions(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModelOptions>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModelOptions>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetCropModelOptions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCropModelOptions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::dashboard::CropModelOptions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCropModelOptions(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::dashboard::CropModelOptions* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetCropModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetCropModel() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::CropModel, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::dashboard::CropModel* request, ::carbon::frontend::util::Empty* response) { return this->SetCropModel(context, request, response); }));}
    void SetMessageAllocatorFor_SetCropModel(
        ::grpc::MessageAllocator< ::carbon::frontend::dashboard::CropModel, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::CropModel, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetCropModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCropModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CropModel* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetCropModel(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::dashboard::CropModel* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetNextWeedingVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextWeedingVelocity() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::WeedingVelocity>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::dashboard::WeedingVelocity* response) { return this->GetNextWeedingVelocity(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextWeedingVelocity(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::WeedingVelocity>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::WeedingVelocity>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextWeedingVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextWeedingVelocity(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::WeedingVelocity* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextWeedingVelocity(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::WeedingVelocity* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetTargetingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetTargetingState() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::TargetingState, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::dashboard::TargetingState* request, ::carbon::frontend::util::Empty* response) { return this->SetTargetingState(context, request, response); }));}
    void SetMessageAllocatorFor_SetTargetingState(
        ::grpc::MessageAllocator< ::carbon::frontend::dashboard::TargetingState, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::TargetingState, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetTargetingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTargetingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::TargetingState* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetTargetingState(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::dashboard::TargetingState* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetRowSpacing : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetRowSpacing() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::RowSpacing, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::dashboard::RowSpacing* request, ::carbon::frontend::util::Empty* response) { return this->SetRowSpacing(context, request, response); }));}
    void SetMessageAllocatorFor_SetRowSpacing(
        ::grpc::MessageAllocator< ::carbon::frontend::dashboard::RowSpacing, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::RowSpacing, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetRowSpacing() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRowSpacing(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowSpacing* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetRowSpacing(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::dashboard::RowSpacing* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetCruiseEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetCruiseEnabled() {
      ::grpc::Service::MarkMethodCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::CruiseEnable, ::carbon::frontend::util::Empty>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::dashboard::CruiseEnable* request, ::carbon::frontend::util::Empty* response) { return this->SetCruiseEnabled(context, request, response); }));}
    void SetMessageAllocatorFor_SetCruiseEnabled(
        ::grpc::MessageAllocator< ::carbon::frontend::dashboard::CruiseEnable, ::carbon::frontend::util::Empty>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(8);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::dashboard::CruiseEnable, ::carbon::frontend::util::Empty>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetCruiseEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCruiseEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CruiseEnable* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetCruiseEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::dashboard::CruiseEnable* /*request*/, ::carbon::frontend::util::Empty* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_ToggleRow<WithCallbackMethod_ToggleLasers<WithCallbackMethod_GetNextDashboardState<WithCallbackMethod_GetCropModelOptions<WithCallbackMethod_SetCropModel<WithCallbackMethod_GetNextWeedingVelocity<WithCallbackMethod_SetTargetingState<WithCallbackMethod_SetRowSpacing<WithCallbackMethod_SetCruiseEnabled<Service > > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_ToggleRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ToggleRow() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_ToggleRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowId* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_ToggleLasers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_ToggleLasers() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_ToggleLasers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLasers(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextDashboardState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextDashboardState() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetNextDashboardState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDashboardState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::DashboardStateMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetCropModelOptions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetCropModelOptions() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_GetCropModelOptions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCropModelOptions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::dashboard::CropModelOptions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetCropModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetCropModel() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_SetCropModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCropModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CropModel* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetNextWeedingVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextWeedingVelocity() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_GetNextWeedingVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextWeedingVelocity(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::WeedingVelocity* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetTargetingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetTargetingState() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_SetTargetingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTargetingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::TargetingState* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetRowSpacing : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetRowSpacing() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_SetRowSpacing() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRowSpacing(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowSpacing* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetCruiseEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetCruiseEnabled() {
      ::grpc::Service::MarkMethodGeneric(8);
    }
    ~WithGenericMethod_SetCruiseEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCruiseEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CruiseEnable* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_ToggleRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ToggleRow() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_ToggleRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowId* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestToggleRow(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_ToggleLasers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_ToggleLasers() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_ToggleLasers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLasers(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestToggleLasers(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextDashboardState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextDashboardState() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetNextDashboardState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDashboardState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::DashboardStateMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextDashboardState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetCropModelOptions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetCropModelOptions() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_GetCropModelOptions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCropModelOptions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::dashboard::CropModelOptions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetCropModelOptions(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetCropModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetCropModel() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_SetCropModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCropModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CropModel* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetCropModel(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextWeedingVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextWeedingVelocity() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_GetNextWeedingVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextWeedingVelocity(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::WeedingVelocity* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextWeedingVelocity(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetTargetingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetTargetingState() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_SetTargetingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTargetingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::TargetingState* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetTargetingState(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetRowSpacing : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetRowSpacing() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_SetRowSpacing() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRowSpacing(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowSpacing* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetRowSpacing(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetCruiseEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetCruiseEnabled() {
      ::grpc::Service::MarkMethodRaw(8);
    }
    ~WithRawMethod_SetCruiseEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCruiseEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CruiseEnable* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetCruiseEnabled(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(8, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ToggleRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ToggleRow() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ToggleRow(context, request, response); }));
    }
    ~WithRawCallbackMethod_ToggleRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowId* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ToggleRow(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_ToggleLasers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_ToggleLasers() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->ToggleLasers(context, request, response); }));
    }
    ~WithRawCallbackMethod_ToggleLasers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status ToggleLasers(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* ToggleLasers(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextDashboardState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextDashboardState() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextDashboardState(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextDashboardState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextDashboardState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::DashboardStateMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextDashboardState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetCropModelOptions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetCropModelOptions() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetCropModelOptions(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetCropModelOptions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetCropModelOptions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::dashboard::CropModelOptions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetCropModelOptions(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetCropModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetCropModel() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetCropModel(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetCropModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCropModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CropModel* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetCropModel(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextWeedingVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextWeedingVelocity() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextWeedingVelocity(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextWeedingVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextWeedingVelocity(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::WeedingVelocity* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextWeedingVelocity(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetTargetingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetTargetingState() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetTargetingState(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetTargetingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTargetingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::TargetingState* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetTargetingState(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetRowSpacing : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetRowSpacing() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetRowSpacing(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetRowSpacing() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetRowSpacing(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowSpacing* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetRowSpacing(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetCruiseEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetCruiseEnabled() {
      ::grpc::Service::MarkMethodRawCallback(8,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetCruiseEnabled(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetCruiseEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetCruiseEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CruiseEnable* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetCruiseEnabled(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ToggleRow : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ToggleRow() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::dashboard::RowId, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::dashboard::RowId, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedToggleRow(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ToggleRow() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ToggleRow(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowId* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedToggleRow(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::dashboard::RowId,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_ToggleLasers : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_ToggleLasers() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedToggleLasers(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_ToggleLasers() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status ToggleLasers(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedToggleLasers(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextDashboardState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextDashboardState() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::DashboardStateMessage>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::DashboardStateMessage>* streamer) {
                       return this->StreamedGetNextDashboardState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextDashboardState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextDashboardState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::DashboardStateMessage* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextDashboardState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::dashboard::DashboardStateMessage>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetCropModelOptions : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetCropModelOptions() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModelOptions>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Empty, ::carbon::frontend::dashboard::CropModelOptions>* streamer) {
                       return this->StreamedGetCropModelOptions(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetCropModelOptions() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetCropModelOptions(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Empty* /*request*/, ::carbon::frontend::dashboard::CropModelOptions* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetCropModelOptions(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Empty,::carbon::frontend::dashboard::CropModelOptions>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetCropModel : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetCropModel() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::dashboard::CropModel, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::dashboard::CropModel, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetCropModel(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetCropModel() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetCropModel(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CropModel* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetCropModel(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::dashboard::CropModel,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextWeedingVelocity : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextWeedingVelocity() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::WeedingVelocity>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::dashboard::WeedingVelocity>* streamer) {
                       return this->StreamedGetNextWeedingVelocity(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextWeedingVelocity() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextWeedingVelocity(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::dashboard::WeedingVelocity* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextWeedingVelocity(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::dashboard::WeedingVelocity>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetTargetingState : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetTargetingState() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::dashboard::TargetingState, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::dashboard::TargetingState, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetTargetingState(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetTargetingState() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetTargetingState(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::TargetingState* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetTargetingState(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::dashboard::TargetingState,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetRowSpacing : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetRowSpacing() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::dashboard::RowSpacing, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::dashboard::RowSpacing, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetRowSpacing(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetRowSpacing() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetRowSpacing(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::RowSpacing* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetRowSpacing(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::dashboard::RowSpacing,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetCruiseEnabled : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetCruiseEnabled() {
      ::grpc::Service::MarkMethodStreamed(8,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::dashboard::CruiseEnable, ::carbon::frontend::util::Empty>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::dashboard::CruiseEnable, ::carbon::frontend::util::Empty>* streamer) {
                       return this->StreamedSetCruiseEnabled(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetCruiseEnabled() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetCruiseEnabled(::grpc::ServerContext* /*context*/, const ::carbon::frontend::dashboard::CruiseEnable* /*request*/, ::carbon::frontend::util::Empty* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetCruiseEnabled(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::dashboard::CruiseEnable,::carbon::frontend::util::Empty>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_ToggleRow<WithStreamedUnaryMethod_ToggleLasers<WithStreamedUnaryMethod_GetNextDashboardState<WithStreamedUnaryMethod_GetCropModelOptions<WithStreamedUnaryMethod_SetCropModel<WithStreamedUnaryMethod_GetNextWeedingVelocity<WithStreamedUnaryMethod_SetTargetingState<WithStreamedUnaryMethod_SetRowSpacing<WithStreamedUnaryMethod_SetCruiseEnabled<Service > > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_ToggleRow<WithStreamedUnaryMethod_ToggleLasers<WithStreamedUnaryMethod_GetNextDashboardState<WithStreamedUnaryMethod_GetCropModelOptions<WithStreamedUnaryMethod_SetCropModel<WithStreamedUnaryMethod_GetNextWeedingVelocity<WithStreamedUnaryMethod_SetTargetingState<WithStreamedUnaryMethod_SetRowSpacing<WithStreamedUnaryMethod_SetCruiseEnabled<Service > > > > > > > > > StreamedService;
};

}  // namespace dashboard
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fdashboard_2eproto__INCLUDED

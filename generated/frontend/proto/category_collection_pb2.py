# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/category_collection.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.category.proto import category_pb2 as category_dot_proto_dot_category__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/category_collection.proto',
  package='carbon.frontend.category_collection',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n(frontend/proto/category_collection.proto\x12#carbon.frontend.category_collection\x1a\x19\x66rontend/proto/util.proto\x1a\x1d\x63\x61tegory/proto/category.proto\"T\n%GetNextCategoryCollectionsDataRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\x98\x01\n&GetNextCategoryCollectionsDataResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x41\n\x14\x63\x61tegory_collections\x18\x03 \x03(\x0b\x32#.carbon.category.CategoryCollection\"W\n(GetNextActiveCategoryCollectionIdRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"\xa2\x01\n)GetNextActiveCategoryCollectionIdResponse\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x17\n\x0freload_required\x18\x03 \x01(\x08\x12!\n\x19last_updated_timestamp_ms\x18\x04 \x01(\x03\"a\n$SetActiveCategoryCollectionIdRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp2\xfc\x04\n\x19\x43\x61tegoryCollectionService\x12\xb9\x01\n\x1eGetNextCategoryCollectionsData\x12J.carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest\x1aK.carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse\x12\xc2\x01\n!GetNextActiveCategoryCollectionId\x12M.carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest\x1aN.carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse\x12\x87\x01\n\x1dSetActiveCategoryCollectionId\x12I.carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest\x1a\x1b.carbon.frontend.util.Empty\x12T\n\x18ReloadCategoryCollection\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,category_dot_proto_dot_category__pb2.DESCRIPTOR,])




_GETNEXTCATEGORYCOLLECTIONSDATAREQUEST = _descriptor.Descriptor(
  name='GetNextCategoryCollectionsDataRequest',
  full_name='carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=139,
  serialized_end=223,
)


_GETNEXTCATEGORYCOLLECTIONSDATARESPONSE = _descriptor.Descriptor(
  name='GetNextCategoryCollectionsDataResponse',
  full_name='carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='category_collections', full_name='carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.category_collections', index=1,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=226,
  serialized_end=378,
)


_GETNEXTACTIVECATEGORYCOLLECTIONIDREQUEST = _descriptor.Descriptor(
  name='GetNextActiveCategoryCollectionIdRequest',
  full_name='carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=380,
  serialized_end=467,
)


_GETNEXTACTIVECATEGORYCOLLECTIONIDRESPONSE = _descriptor.Descriptor(
  name='GetNextActiveCategoryCollectionIdResponse',
  full_name='carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='reload_required', full_name='carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.reload_required', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_updated_timestamp_ms', full_name='carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.last_updated_timestamp_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=470,
  serialized_end=632,
)


_SETACTIVECATEGORYCOLLECTIONIDREQUEST = _descriptor.Descriptor(
  name='SetActiveCategoryCollectionIdRequest',
  full_name='carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=634,
  serialized_end=731,
)

_GETNEXTCATEGORYCOLLECTIONSDATAREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCATEGORYCOLLECTIONSDATARESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCATEGORYCOLLECTIONSDATARESPONSE.fields_by_name['category_collections'].message_type = category_dot_proto_dot_category__pb2._CATEGORYCOLLECTION
_GETNEXTACTIVECATEGORYCOLLECTIONIDREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTACTIVECATEGORYCOLLECTIONIDRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_SETACTIVECATEGORYCOLLECTIONIDREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['GetNextCategoryCollectionsDataRequest'] = _GETNEXTCATEGORYCOLLECTIONSDATAREQUEST
DESCRIPTOR.message_types_by_name['GetNextCategoryCollectionsDataResponse'] = _GETNEXTCATEGORYCOLLECTIONSDATARESPONSE
DESCRIPTOR.message_types_by_name['GetNextActiveCategoryCollectionIdRequest'] = _GETNEXTACTIVECATEGORYCOLLECTIONIDREQUEST
DESCRIPTOR.message_types_by_name['GetNextActiveCategoryCollectionIdResponse'] = _GETNEXTACTIVECATEGORYCOLLECTIONIDRESPONSE
DESCRIPTOR.message_types_by_name['SetActiveCategoryCollectionIdRequest'] = _SETACTIVECATEGORYCOLLECTIONIDREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetNextCategoryCollectionsDataRequest = _reflection.GeneratedProtocolMessageType('GetNextCategoryCollectionsDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCATEGORYCOLLECTIONSDATAREQUEST,
  '__module__' : 'frontend.proto.category_collection_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
  })
_sym_db.RegisterMessage(GetNextCategoryCollectionsDataRequest)

GetNextCategoryCollectionsDataResponse = _reflection.GeneratedProtocolMessageType('GetNextCategoryCollectionsDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCATEGORYCOLLECTIONSDATARESPONSE,
  '__module__' : 'frontend.proto.category_collection_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
  })
_sym_db.RegisterMessage(GetNextCategoryCollectionsDataResponse)

GetNextActiveCategoryCollectionIdRequest = _reflection.GeneratedProtocolMessageType('GetNextActiveCategoryCollectionIdRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVECATEGORYCOLLECTIONIDREQUEST,
  '__module__' : 'frontend.proto.category_collection_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
  })
_sym_db.RegisterMessage(GetNextActiveCategoryCollectionIdRequest)

GetNextActiveCategoryCollectionIdResponse = _reflection.GeneratedProtocolMessageType('GetNextActiveCategoryCollectionIdResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTACTIVECATEGORYCOLLECTIONIDRESPONSE,
  '__module__' : 'frontend.proto.category_collection_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
  })
_sym_db.RegisterMessage(GetNextActiveCategoryCollectionIdResponse)

SetActiveCategoryCollectionIdRequest = _reflection.GeneratedProtocolMessageType('SetActiveCategoryCollectionIdRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVECATEGORYCOLLECTIONIDREQUEST,
  '__module__' : 'frontend.proto.category_collection_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
  })
_sym_db.RegisterMessage(SetActiveCategoryCollectionIdRequest)


DESCRIPTOR._options = None

_CATEGORYCOLLECTIONSERVICE = _descriptor.ServiceDescriptor(
  name='CategoryCollectionService',
  full_name='carbon.frontend.category_collection.CategoryCollectionService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=734,
  serialized_end=1370,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextCategoryCollectionsData',
    full_name='carbon.frontend.category_collection.CategoryCollectionService.GetNextCategoryCollectionsData',
    index=0,
    containing_service=None,
    input_type=_GETNEXTCATEGORYCOLLECTIONSDATAREQUEST,
    output_type=_GETNEXTCATEGORYCOLLECTIONSDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextActiveCategoryCollectionId',
    full_name='carbon.frontend.category_collection.CategoryCollectionService.GetNextActiveCategoryCollectionId',
    index=1,
    containing_service=None,
    input_type=_GETNEXTACTIVECATEGORYCOLLECTIONIDREQUEST,
    output_type=_GETNEXTACTIVECATEGORYCOLLECTIONIDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetActiveCategoryCollectionId',
    full_name='carbon.frontend.category_collection.CategoryCollectionService.SetActiveCategoryCollectionId',
    index=2,
    containing_service=None,
    input_type=_SETACTIVECATEGORYCOLLECTIONIDREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReloadCategoryCollection',
    full_name='carbon.frontend.category_collection.CategoryCollectionService.ReloadCategoryCollection',
    index=3,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CATEGORYCOLLECTIONSERVICE)

DESCRIPTOR.services_by_name['CategoryCollectionService'] = _CATEGORYCOLLECTIONSERVICE

# @@protoc_insertion_point(module_scope)

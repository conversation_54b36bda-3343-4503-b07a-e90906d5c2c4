# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/status_bar.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import translation_pb2 as frontend_dot_proto_dot_translation__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/status_bar.proto',
  package='carbon.frontend.status_bar',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1f\x66rontend/proto/status_bar.proto\x12\x1a\x63\x61rbon.frontend.status_bar\x1a frontend/proto/translation.proto\x1a\x19\x66rontend/proto/util.proto\"C\n\x0cGlobalStatus\x12\x0c\n\x04hint\x18\x01 \x01(\t\x12\x11\n\ticon_name\x18\x02 \x01(\t\x12\x12\n\nicon_color\x18\x03 \x01(\t\"\\\n\rServiceStatus\x12\x0c\n\x04name\x18\x01 \x01(\t\x12=\n\x0cstatus_level\x18\x02 \x01(\x0e\x32\'.carbon.frontend.status_bar.StatusLevel\"\x90\x01\n\x0cServerStatus\x12=\n\x0cstatus_level\x18\x01 \x01(\x0e\x32\'.carbon.frontend.status_bar.StatusLevel\x12\x41\n\x0eservice_status\x18\x02 \x03(\x0b\x32).carbon.frontend.status_bar.ServiceStatus\"\x86\x01\n\x1eTranslatedStatusMessageDetails\x12\x1c\n\x12\x64\x65tails_string_key\x18\x01 \x01(\tH\x00\x12;\n\x05timer\x18\x02 \x01(\x0b\x32*.carbon.frontend.translation.DurationValueH\x00\x42\t\n\x07\x64\x65tails\"v\n\x17TranslatedStatusMessage\x12\x0e\n\x06prefix\x18\x01 \x01(\t\x12K\n\x07\x64\x65tails\x18\x02 \x01(\x0b\x32:.carbon.frontend.status_bar.TranslatedStatusMessageDetails\"\xdc\x04\n\x10StatusBarMessage\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x16\n\x0elasers_enabled\x18\x03 \x01(\x08\x12\x17\n\x0fweeding_enabled\x18\x04 \x01(\x08\x12\x38\n\x0cstatus_level\x18\x05 \x01(\x0e\x32\".carbon.frontend.status_bar.Status\x12\x16\n\x0estatus_message\x18\x06 \x01(\t\x12\x0e\n\x06serial\x18\x07 \x01(\t\x12O\n\nrow_status\x18\x08 \x03(\x0b\x32;.carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry\x12@\n\x0e\x63ommand_status\x18\t \x01(\x0b\x32(.carbon.frontend.status_bar.ServerStatus\x12\x41\n\x0fglobal_statuses\x18\n \x03(\x0b\x32(.carbon.frontend.status_bar.GlobalStatus\x12V\n\x19translated_status_message\x18\x0b \x01(\x0b\x32\x33.carbon.frontend.status_bar.TranslatedStatusMessage\x1aZ\n\x0eRowStatusEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x37\n\x05value\x18\x02 \x01(\x0b\x32(.carbon.frontend.status_bar.ServerStatus:\x02\x38\x01\"?\n\x12ReportIssueRequest\x12\x13\n\x0b\x64\x65scription\x18\x01 \x01(\t\x12\x14\n\x0cphone_number\x18\x02 \x01(\t\",\n\x13SupportPhoneMessage\x12\x15\n\rsupport_phone\x18\x01 \x01(\t*\xea\x03\n\x06Status\x12\x10\n\x0cSTATUS_ERROR\x10\x00\x12\x13\n\x0fSTATUS_ESTOPPED\x10\x01\x12\x14\n\x10STATUS_PRE_ARMED\x10\x02\x12\x17\n\x13STATUS_POWERED_DOWN\x10\x03\x12\x16\n\x12STATUS_POWERING_UP\x10\x04\x12\x1c\n\x18STATUS_UPDATE_INSTALLING\x10\x05\x12\x18\n\x14STATUS_MODEL_LOADING\x10\x06\x12\x1b\n\x17STATUS_MODEL_INSTALLING\x10\x07\x12\x12\n\x0eSTATUS_WEEDING\x10\x08\x12\x12\n\x0eSTATUS_STANDBY\x10\t\x12\x12\n\x0eSTATUS_UNKNOWN\x10\n\x12\x17\n\x13STATUS_DISCONNECTED\x10\x0b\x12\x11\n\rSTATUS_LIFTED\x10\x0c\x12\x12\n\x0eSTATUS_LOADING\x10\r\x12$\n STATUS_ALARM_AUTOFIX_IN_PROGRESS\x10\x0e\x12\x1d\n\x19STATUS_FAILED_TO_POWER_UP\x10\x0f\x12\"\n\x1eSTATUS_SERVER_CABINET_COOLDOWN\x10\x10\x12\x1b\n\x17STATUS_CHILLER_COOLDOWN\x10\x11\x12\x1b\n\x17STATUS_TRACTOR_NOT_SAFE\x10\x12*D\n\x0bStatusLevel\x12\x0b\n\x07INVALID\x10\x00\x12\t\n\x05READY\x10\x01\x12\x0b\n\x07LOADING\x10\x02\x12\x10\n\x08\x45STOPPED\x10\x03\x1a\x02\x08\x01\x32\xaf\x02\n\x10StatusBarService\x12^\n\rGetNextStatus\x12\x1f.carbon.frontend.util.Timestamp\x1a,.carbon.frontend.status_bar.StatusBarMessage\x12Z\n\x0bReportIssue\x12..carbon.frontend.status_bar.ReportIssueRequest\x1a\x1b.carbon.frontend.util.Empty\x12_\n\x0fGetSupportPhone\x12\x1b.carbon.frontend.util.Empty\x1a/.carbon.frontend.status_bar.SupportPhoneMessageB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_translation__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_STATUS = _descriptor.EnumDescriptor(
  name='Status',
  full_name='carbon.frontend.status_bar.Status',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='STATUS_ERROR', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_ESTOPPED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_PRE_ARMED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_POWERED_DOWN', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_POWERING_UP', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_UPDATE_INSTALLING', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_MODEL_LOADING', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_MODEL_INSTALLING', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_WEEDING', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_STANDBY', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_UNKNOWN', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_DISCONNECTED', index=11, number=11,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_LIFTED', index=12, number=12,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_LOADING', index=13, number=13,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_ALARM_AUTOFIX_IN_PROGRESS', index=14, number=14,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_FAILED_TO_POWER_UP', index=15, number=15,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_SERVER_CABINET_COOLDOWN', index=16, number=16,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_CHILLER_COOLDOWN', index=17, number=17,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STATUS_TRACTOR_NOT_SAFE', index=18, number=18,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1410,
  serialized_end=1900,
)
_sym_db.RegisterEnumDescriptor(_STATUS)

Status = enum_type_wrapper.EnumTypeWrapper(_STATUS)
_STATUSLEVEL = _descriptor.EnumDescriptor(
  name='StatusLevel',
  full_name='carbon.frontend.status_bar.StatusLevel',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='INVALID', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='READY', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LOADING', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ESTOPPED', index=3, number=3,
      serialized_options=b'\010\001',
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1902,
  serialized_end=1970,
)
_sym_db.RegisterEnumDescriptor(_STATUSLEVEL)

StatusLevel = enum_type_wrapper.EnumTypeWrapper(_STATUSLEVEL)
STATUS_ERROR = 0
STATUS_ESTOPPED = 1
STATUS_PRE_ARMED = 2
STATUS_POWERED_DOWN = 3
STATUS_POWERING_UP = 4
STATUS_UPDATE_INSTALLING = 5
STATUS_MODEL_LOADING = 6
STATUS_MODEL_INSTALLING = 7
STATUS_WEEDING = 8
STATUS_STANDBY = 9
STATUS_UNKNOWN = 10
STATUS_DISCONNECTED = 11
STATUS_LIFTED = 12
STATUS_LOADING = 13
STATUS_ALARM_AUTOFIX_IN_PROGRESS = 14
STATUS_FAILED_TO_POWER_UP = 15
STATUS_SERVER_CABINET_COOLDOWN = 16
STATUS_CHILLER_COOLDOWN = 17
STATUS_TRACTOR_NOT_SAFE = 18
INVALID = 0
READY = 1
LOADING = 2
ESTOPPED = 3



_GLOBALSTATUS = _descriptor.Descriptor(
  name='GlobalStatus',
  full_name='carbon.frontend.status_bar.GlobalStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='hint', full_name='carbon.frontend.status_bar.GlobalStatus.hint', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='icon_name', full_name='carbon.frontend.status_bar.GlobalStatus.icon_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='icon_color', full_name='carbon.frontend.status_bar.GlobalStatus.icon_color', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=124,
  serialized_end=191,
)


_SERVICESTATUS = _descriptor.Descriptor(
  name='ServiceStatus',
  full_name='carbon.frontend.status_bar.ServiceStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.status_bar.ServiceStatus.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_level', full_name='carbon.frontend.status_bar.ServiceStatus.status_level', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=193,
  serialized_end=285,
)


_SERVERSTATUS = _descriptor.Descriptor(
  name='ServerStatus',
  full_name='carbon.frontend.status_bar.ServerStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='status_level', full_name='carbon.frontend.status_bar.ServerStatus.status_level', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='service_status', full_name='carbon.frontend.status_bar.ServerStatus.service_status', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=288,
  serialized_end=432,
)


_TRANSLATEDSTATUSMESSAGEDETAILS = _descriptor.Descriptor(
  name='TranslatedStatusMessageDetails',
  full_name='carbon.frontend.status_bar.TranslatedStatusMessageDetails',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='details_string_key', full_name='carbon.frontend.status_bar.TranslatedStatusMessageDetails.details_string_key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timer', full_name='carbon.frontend.status_bar.TranslatedStatusMessageDetails.timer', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='details', full_name='carbon.frontend.status_bar.TranslatedStatusMessageDetails.details',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=435,
  serialized_end=569,
)


_TRANSLATEDSTATUSMESSAGE = _descriptor.Descriptor(
  name='TranslatedStatusMessage',
  full_name='carbon.frontend.status_bar.TranslatedStatusMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='prefix', full_name='carbon.frontend.status_bar.TranslatedStatusMessage.prefix', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='details', full_name='carbon.frontend.status_bar.TranslatedStatusMessage.details', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=571,
  serialized_end=689,
)


_STATUSBARMESSAGE_ROWSTATUSENTRY = _descriptor.Descriptor(
  name='RowStatusEntry',
  full_name='carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1206,
  serialized_end=1296,
)

_STATUSBARMESSAGE = _descriptor.Descriptor(
  name='StatusBarMessage',
  full_name='carbon.frontend.status_bar.StatusBarMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.status_bar.StatusBarMessage.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lasers_enabled', full_name='carbon.frontend.status_bar.StatusBarMessage.lasers_enabled', index=1,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeding_enabled', full_name='carbon.frontend.status_bar.StatusBarMessage.weeding_enabled', index=2,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_level', full_name='carbon.frontend.status_bar.StatusBarMessage.status_level', index=3,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_message', full_name='carbon.frontend.status_bar.StatusBarMessage.status_message', index=4,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.frontend.status_bar.StatusBarMessage.serial', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_status', full_name='carbon.frontend.status_bar.StatusBarMessage.row_status', index=6,
      number=8, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='command_status', full_name='carbon.frontend.status_bar.StatusBarMessage.command_status', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='global_statuses', full_name='carbon.frontend.status_bar.StatusBarMessage.global_statuses', index=8,
      number=10, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='translated_status_message', full_name='carbon.frontend.status_bar.StatusBarMessage.translated_status_message', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_STATUSBARMESSAGE_ROWSTATUSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=692,
  serialized_end=1296,
)


_REPORTISSUEREQUEST = _descriptor.Descriptor(
  name='ReportIssueRequest',
  full_name='carbon.frontend.status_bar.ReportIssueRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='description', full_name='carbon.frontend.status_bar.ReportIssueRequest.description', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phone_number', full_name='carbon.frontend.status_bar.ReportIssueRequest.phone_number', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1298,
  serialized_end=1361,
)


_SUPPORTPHONEMESSAGE = _descriptor.Descriptor(
  name='SupportPhoneMessage',
  full_name='carbon.frontend.status_bar.SupportPhoneMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='support_phone', full_name='carbon.frontend.status_bar.SupportPhoneMessage.support_phone', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1363,
  serialized_end=1407,
)

_SERVICESTATUS.fields_by_name['status_level'].enum_type = _STATUSLEVEL
_SERVERSTATUS.fields_by_name['status_level'].enum_type = _STATUSLEVEL
_SERVERSTATUS.fields_by_name['service_status'].message_type = _SERVICESTATUS
_TRANSLATEDSTATUSMESSAGEDETAILS.fields_by_name['timer'].message_type = frontend_dot_proto_dot_translation__pb2._DURATIONVALUE
_TRANSLATEDSTATUSMESSAGEDETAILS.oneofs_by_name['details'].fields.append(
  _TRANSLATEDSTATUSMESSAGEDETAILS.fields_by_name['details_string_key'])
_TRANSLATEDSTATUSMESSAGEDETAILS.fields_by_name['details_string_key'].containing_oneof = _TRANSLATEDSTATUSMESSAGEDETAILS.oneofs_by_name['details']
_TRANSLATEDSTATUSMESSAGEDETAILS.oneofs_by_name['details'].fields.append(
  _TRANSLATEDSTATUSMESSAGEDETAILS.fields_by_name['timer'])
_TRANSLATEDSTATUSMESSAGEDETAILS.fields_by_name['timer'].containing_oneof = _TRANSLATEDSTATUSMESSAGEDETAILS.oneofs_by_name['details']
_TRANSLATEDSTATUSMESSAGE.fields_by_name['details'].message_type = _TRANSLATEDSTATUSMESSAGEDETAILS
_STATUSBARMESSAGE_ROWSTATUSENTRY.fields_by_name['value'].message_type = _SERVERSTATUS
_STATUSBARMESSAGE_ROWSTATUSENTRY.containing_type = _STATUSBARMESSAGE
_STATUSBARMESSAGE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_STATUSBARMESSAGE.fields_by_name['status_level'].enum_type = _STATUS
_STATUSBARMESSAGE.fields_by_name['row_status'].message_type = _STATUSBARMESSAGE_ROWSTATUSENTRY
_STATUSBARMESSAGE.fields_by_name['command_status'].message_type = _SERVERSTATUS
_STATUSBARMESSAGE.fields_by_name['global_statuses'].message_type = _GLOBALSTATUS
_STATUSBARMESSAGE.fields_by_name['translated_status_message'].message_type = _TRANSLATEDSTATUSMESSAGE
DESCRIPTOR.message_types_by_name['GlobalStatus'] = _GLOBALSTATUS
DESCRIPTOR.message_types_by_name['ServiceStatus'] = _SERVICESTATUS
DESCRIPTOR.message_types_by_name['ServerStatus'] = _SERVERSTATUS
DESCRIPTOR.message_types_by_name['TranslatedStatusMessageDetails'] = _TRANSLATEDSTATUSMESSAGEDETAILS
DESCRIPTOR.message_types_by_name['TranslatedStatusMessage'] = _TRANSLATEDSTATUSMESSAGE
DESCRIPTOR.message_types_by_name['StatusBarMessage'] = _STATUSBARMESSAGE
DESCRIPTOR.message_types_by_name['ReportIssueRequest'] = _REPORTISSUEREQUEST
DESCRIPTOR.message_types_by_name['SupportPhoneMessage'] = _SUPPORTPHONEMESSAGE
DESCRIPTOR.enum_types_by_name['Status'] = _STATUS
DESCRIPTOR.enum_types_by_name['StatusLevel'] = _STATUSLEVEL
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GlobalStatus = _reflection.GeneratedProtocolMessageType('GlobalStatus', (_message.Message,), {
  'DESCRIPTOR' : _GLOBALSTATUS,
  '__module__' : 'frontend.proto.status_bar_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.GlobalStatus)
  })
_sym_db.RegisterMessage(GlobalStatus)

ServiceStatus = _reflection.GeneratedProtocolMessageType('ServiceStatus', (_message.Message,), {
  'DESCRIPTOR' : _SERVICESTATUS,
  '__module__' : 'frontend.proto.status_bar_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.ServiceStatus)
  })
_sym_db.RegisterMessage(ServiceStatus)

ServerStatus = _reflection.GeneratedProtocolMessageType('ServerStatus', (_message.Message,), {
  'DESCRIPTOR' : _SERVERSTATUS,
  '__module__' : 'frontend.proto.status_bar_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.ServerStatus)
  })
_sym_db.RegisterMessage(ServerStatus)

TranslatedStatusMessageDetails = _reflection.GeneratedProtocolMessageType('TranslatedStatusMessageDetails', (_message.Message,), {
  'DESCRIPTOR' : _TRANSLATEDSTATUSMESSAGEDETAILS,
  '__module__' : 'frontend.proto.status_bar_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
  })
_sym_db.RegisterMessage(TranslatedStatusMessageDetails)

TranslatedStatusMessage = _reflection.GeneratedProtocolMessageType('TranslatedStatusMessage', (_message.Message,), {
  'DESCRIPTOR' : _TRANSLATEDSTATUSMESSAGE,
  '__module__' : 'frontend.proto.status_bar_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.TranslatedStatusMessage)
  })
_sym_db.RegisterMessage(TranslatedStatusMessage)

StatusBarMessage = _reflection.GeneratedProtocolMessageType('StatusBarMessage', (_message.Message,), {

  'RowStatusEntry' : _reflection.GeneratedProtocolMessageType('RowStatusEntry', (_message.Message,), {
    'DESCRIPTOR' : _STATUSBARMESSAGE_ROWSTATUSENTRY,
    '__module__' : 'frontend.proto.status_bar_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.StatusBarMessage.RowStatusEntry)
    })
  ,
  'DESCRIPTOR' : _STATUSBARMESSAGE,
  '__module__' : 'frontend.proto.status_bar_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.StatusBarMessage)
  })
_sym_db.RegisterMessage(StatusBarMessage)
_sym_db.RegisterMessage(StatusBarMessage.RowStatusEntry)

ReportIssueRequest = _reflection.GeneratedProtocolMessageType('ReportIssueRequest', (_message.Message,), {
  'DESCRIPTOR' : _REPORTISSUEREQUEST,
  '__module__' : 'frontend.proto.status_bar_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.ReportIssueRequest)
  })
_sym_db.RegisterMessage(ReportIssueRequest)

SupportPhoneMessage = _reflection.GeneratedProtocolMessageType('SupportPhoneMessage', (_message.Message,), {
  'DESCRIPTOR' : _SUPPORTPHONEMESSAGE,
  '__module__' : 'frontend.proto.status_bar_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.SupportPhoneMessage)
  })
_sym_db.RegisterMessage(SupportPhoneMessage)


DESCRIPTOR._options = None
_STATUSLEVEL.values_by_name["ESTOPPED"]._options = None
_STATUSBARMESSAGE_ROWSTATUSENTRY._options = None

_STATUSBARSERVICE = _descriptor.ServiceDescriptor(
  name='StatusBarService',
  full_name='carbon.frontend.status_bar.StatusBarService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1973,
  serialized_end=2276,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextStatus',
    full_name='carbon.frontend.status_bar.StatusBarService.GetNextStatus',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_STATUSBARMESSAGE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ReportIssue',
    full_name='carbon.frontend.status_bar.StatusBarService.ReportIssue',
    index=1,
    containing_service=None,
    input_type=_REPORTISSUEREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetSupportPhone',
    full_name='carbon.frontend.status_bar.StatusBarService.GetSupportPhone',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_SUPPORTPHONEMESSAGE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_STATUSBARSERVICE)

DESCRIPTOR.services_by_name['StatusBarService'] = _STATUSBARSERVICE

# @@protoc_insertion_point(module_scope)

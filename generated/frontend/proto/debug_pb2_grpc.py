# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import debug_pb2 as frontend_dot_proto_dot_debug__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2


class DebugServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetRobot = channel.unary_unary(
                '/carbon.frontend.debug.DebugService/GetRobot',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_debug__pb2.RobotMessage.FromString,
                )
        self.SetLogLevel = channel.unary_unary(
                '/carbon.frontend.debug.DebugService/SetLogLevel',
                request_serializer=frontend_dot_proto_dot_debug__pb2.SetLogLevelRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.StartSavingCropLineDetectionReplay = channel.unary_unary(
                '/carbon.frontend.debug.DebugService/StartSavingCropLineDetectionReplay',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.StartSavingCropLineDetectionReplayRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.StartRecordingAimbotInputs = channel.unary_unary(
                '/carbon.frontend.debug.DebugService/StartRecordingAimbotInputs',
                request_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordAimbotInputRequest.SerializeToString,
                response_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
                )
        self.AddMockSpatialMetricsBlock = channel.unary_unary(
                '/carbon.frontend.debug.DebugService/AddMockSpatialMetricsBlock',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.DeleteProfileSyncData = channel.unary_unary(
                '/carbon.frontend.debug.DebugService/DeleteProfileSyncData',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )


class DebugServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetRobot(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetLogLevel(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartSavingCropLineDetectionReplay(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def StartRecordingAimbotInputs(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddMockSpatialMetricsBlock(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteProfileSyncData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_DebugServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetRobot': grpc.unary_unary_rpc_method_handler(
                    servicer.GetRobot,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_debug__pb2.RobotMessage.SerializeToString,
            ),
            'SetLogLevel': grpc.unary_unary_rpc_method_handler(
                    servicer.SetLogLevel,
                    request_deserializer=frontend_dot_proto_dot_debug__pb2.SetLogLevelRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'StartSavingCropLineDetectionReplay': grpc.unary_unary_rpc_method_handler(
                    servicer.StartSavingCropLineDetectionReplay,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.StartSavingCropLineDetectionReplayRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'StartRecordingAimbotInputs': grpc.unary_unary_rpc_method_handler(
                    servicer.StartRecordingAimbotInputs,
                    request_deserializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordAimbotInputRequest.FromString,
                    response_serializer=weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.SerializeToString,
            ),
            'AddMockSpatialMetricsBlock': grpc.unary_unary_rpc_method_handler(
                    servicer.AddMockSpatialMetricsBlock,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'DeleteProfileSyncData': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteProfileSyncData,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.debug.DebugService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class DebugService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetRobot(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.DebugService/GetRobot',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_debug__pb2.RobotMessage.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetLogLevel(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.DebugService/SetLogLevel',
            frontend_dot_proto_dot_debug__pb2.SetLogLevelRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartSavingCropLineDetectionReplay(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.DebugService/StartSavingCropLineDetectionReplay',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.StartSavingCropLineDetectionReplayRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def StartRecordingAimbotInputs(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.DebugService/StartRecordingAimbotInputs',
            weed__tracking_dot_proto_dot_weed__tracking__pb2.RecordAimbotInputRequest.SerializeToString,
            weed__tracking_dot_proto_dot_weed__tracking__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddMockSpatialMetricsBlock(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.DebugService/AddMockSpatialMetricsBlock',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteProfileSyncData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.DebugService/DeleteProfileSyncData',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

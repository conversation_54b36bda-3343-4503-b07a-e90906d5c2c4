# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import robot_pb2 as frontend_dot_proto_dot_robot__pb2


class RobotDiagnosticServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextRobotState = channel.unary_unary(
                '/carbon.frontend.robot.RobotDiagnosticService/GetNextRobotState',
                request_serializer=frontend_dot_proto_dot_robot__pb2.RobotStateRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_robot__pb2.TimestampedRobotState.FromString,
                )


class RobotDiagnosticServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextRobotState(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RobotDiagnosticServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextRobotState': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextRobotState,
                    request_deserializer=frontend_dot_proto_dot_robot__pb2.RobotStateRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_robot__pb2.TimestampedRobotState.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.robot.RobotDiagnosticService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class RobotDiagnosticService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextRobotState(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.robot.RobotDiagnosticService/GetNextRobotState',
            frontend_dot_proto_dot_robot__pb2.RobotStateRequest.SerializeToString,
            frontend_dot_proto_dot_robot__pb2.TimestampedRobotState.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

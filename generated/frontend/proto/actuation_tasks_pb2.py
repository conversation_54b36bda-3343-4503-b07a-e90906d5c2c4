# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/actuation_tasks.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.core.controls.exterminator.controllers.aimbot.process.proto import aimbot_pb2 as core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/actuation_tasks.proto',
  package='carbon.frontend.actuation_tasks',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n$frontend/proto/actuation_tasks.proto\x12\x1f\x63\x61rbon.frontend.actuation_tasks\x1aHcore/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto\x1a\x19\x66rontend/proto/util.proto\"^\n GlobalAimbotActuationTaskRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\r\x12*\n\x04task\x18\x02 \x01(\x0b\x32\x1c.aimbot.ActuationTaskRequest\"\x8b\x01\n\x18GlobalActuationTaskState\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0f\n\x07running\x18\x02 \x01(\x08\x12\x17\n\x0f\x65lapsed_time_ms\x18\x03 \x01(\r\x12\x18\n\x10\x65xpected_time_ms\x18\x04 \x01(\r2\xf6\x02\n\x15\x41\x63tuationTasksService\x12}\n\x1fGetNextGlobalActuationTaskState\x12\x1f.carbon.frontend.util.Timestamp\x1a\x39.carbon.frontend.actuation_tasks.GlobalActuationTaskState\x12\x80\x01\n\x1eStartGlobalAimbotActuationTask\x12\x41.carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest\x1a\x1b.carbon.frontend.util.Empty\x12[\n\x1f\x43\x61ncelGlobalAimbotActuationTask\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_GLOBALAIMBOTACTUATIONTASKREQUEST = _descriptor.Descriptor(
  name='GlobalAimbotActuationTaskRequest',
  full_name='carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.row_id', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='task', full_name='carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest.task', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=174,
  serialized_end=268,
)


_GLOBALACTUATIONTASKSTATE = _descriptor.Descriptor(
  name='GlobalActuationTaskState',
  full_name='carbon.frontend.actuation_tasks.GlobalActuationTaskState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.actuation_tasks.GlobalActuationTaskState.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='running', full_name='carbon.frontend.actuation_tasks.GlobalActuationTaskState.running', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='elapsed_time_ms', full_name='carbon.frontend.actuation_tasks.GlobalActuationTaskState.elapsed_time_ms', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='expected_time_ms', full_name='carbon.frontend.actuation_tasks.GlobalActuationTaskState.expected_time_ms', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=271,
  serialized_end=410,
)

_GLOBALAIMBOTACTUATIONTASKREQUEST.fields_by_name['task'].message_type = core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2._ACTUATIONTASKREQUEST
_GLOBALACTUATIONTASKSTATE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['GlobalAimbotActuationTaskRequest'] = _GLOBALAIMBOTACTUATIONTASKREQUEST
DESCRIPTOR.message_types_by_name['GlobalActuationTaskState'] = _GLOBALACTUATIONTASKSTATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GlobalAimbotActuationTaskRequest = _reflection.GeneratedProtocolMessageType('GlobalAimbotActuationTaskRequest', (_message.Message,), {
  'DESCRIPTOR' : _GLOBALAIMBOTACTUATIONTASKREQUEST,
  '__module__' : 'frontend.proto.actuation_tasks_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.actuation_tasks.GlobalAimbotActuationTaskRequest)
  })
_sym_db.RegisterMessage(GlobalAimbotActuationTaskRequest)

GlobalActuationTaskState = _reflection.GeneratedProtocolMessageType('GlobalActuationTaskState', (_message.Message,), {
  'DESCRIPTOR' : _GLOBALACTUATIONTASKSTATE,
  '__module__' : 'frontend.proto.actuation_tasks_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.actuation_tasks.GlobalActuationTaskState)
  })
_sym_db.RegisterMessage(GlobalActuationTaskState)


DESCRIPTOR._options = None

_ACTUATIONTASKSSERVICE = _descriptor.ServiceDescriptor(
  name='ActuationTasksService',
  full_name='carbon.frontend.actuation_tasks.ActuationTasksService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=413,
  serialized_end=787,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextGlobalActuationTaskState',
    full_name='carbon.frontend.actuation_tasks.ActuationTasksService.GetNextGlobalActuationTaskState',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GLOBALACTUATIONTASKSTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='StartGlobalAimbotActuationTask',
    full_name='carbon.frontend.actuation_tasks.ActuationTasksService.StartGlobalAimbotActuationTask',
    index=1,
    containing_service=None,
    input_type=_GLOBALAIMBOTACTUATIONTASKREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='CancelGlobalAimbotActuationTask',
    full_name='carbon.frontend.actuation_tasks.ActuationTasksService.CancelGlobalAimbotActuationTask',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ACTUATIONTASKSSERVICE)

DESCRIPTOR.services_by_name['ActuationTasksService'] = _ACTUATIONTASKSSERVICE

# @@protoc_insertion_point(module_scope)

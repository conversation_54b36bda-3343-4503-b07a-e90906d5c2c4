// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/category_collection.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcategory_5fcollection_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcategory_5fcollection_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
#include "category/proto/category.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fcategory_5fcollection_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fcategory_5fcollection_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcategory_5fcollection_2eproto;
namespace carbon {
namespace frontend {
namespace category_collection {
class GetNextActiveCategoryCollectionIdRequest;
struct GetNextActiveCategoryCollectionIdRequestDefaultTypeInternal;
extern GetNextActiveCategoryCollectionIdRequestDefaultTypeInternal _GetNextActiveCategoryCollectionIdRequest_default_instance_;
class GetNextActiveCategoryCollectionIdResponse;
struct GetNextActiveCategoryCollectionIdResponseDefaultTypeInternal;
extern GetNextActiveCategoryCollectionIdResponseDefaultTypeInternal _GetNextActiveCategoryCollectionIdResponse_default_instance_;
class GetNextCategoryCollectionsDataRequest;
struct GetNextCategoryCollectionsDataRequestDefaultTypeInternal;
extern GetNextCategoryCollectionsDataRequestDefaultTypeInternal _GetNextCategoryCollectionsDataRequest_default_instance_;
class GetNextCategoryCollectionsDataResponse;
struct GetNextCategoryCollectionsDataResponseDefaultTypeInternal;
extern GetNextCategoryCollectionsDataResponseDefaultTypeInternal _GetNextCategoryCollectionsDataResponse_default_instance_;
class SetActiveCategoryCollectionIdRequest;
struct SetActiveCategoryCollectionIdRequestDefaultTypeInternal;
extern SetActiveCategoryCollectionIdRequestDefaultTypeInternal _SetActiveCategoryCollectionIdRequest_default_instance_;
}  // namespace category_collection
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest* Arena::CreateMaybeMessage<::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdRequest>(Arena*);
template<> ::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse* Arena::CreateMaybeMessage<::carbon::frontend::category_collection::GetNextActiveCategoryCollectionIdResponse>(Arena*);
template<> ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest* Arena::CreateMaybeMessage<::carbon::frontend::category_collection::GetNextCategoryCollectionsDataRequest>(Arena*);
template<> ::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse* Arena::CreateMaybeMessage<::carbon::frontend::category_collection::GetNextCategoryCollectionsDataResponse>(Arena*);
template<> ::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest* Arena::CreateMaybeMessage<::carbon::frontend::category_collection::SetActiveCategoryCollectionIdRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace category_collection {

// ===================================================================

class GetNextCategoryCollectionsDataRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest) */ {
 public:
  inline GetNextCategoryCollectionsDataRequest() : GetNextCategoryCollectionsDataRequest(nullptr) {}
  ~GetNextCategoryCollectionsDataRequest() override;
  explicit constexpr GetNextCategoryCollectionsDataRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextCategoryCollectionsDataRequest(const GetNextCategoryCollectionsDataRequest& from);
  GetNextCategoryCollectionsDataRequest(GetNextCategoryCollectionsDataRequest&& from) noexcept
    : GetNextCategoryCollectionsDataRequest() {
    *this = ::std::move(from);
  }

  inline GetNextCategoryCollectionsDataRequest& operator=(const GetNextCategoryCollectionsDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextCategoryCollectionsDataRequest& operator=(GetNextCategoryCollectionsDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextCategoryCollectionsDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextCategoryCollectionsDataRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextCategoryCollectionsDataRequest*>(
               &_GetNextCategoryCollectionsDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetNextCategoryCollectionsDataRequest& a, GetNextCategoryCollectionsDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextCategoryCollectionsDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextCategoryCollectionsDataRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextCategoryCollectionsDataRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextCategoryCollectionsDataRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextCategoryCollectionsDataRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextCategoryCollectionsDataRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextCategoryCollectionsDataRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest";
  }
  protected:
  explicit GetNextCategoryCollectionsDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcategory_5fcollection_2eproto;
};
// -------------------------------------------------------------------

class GetNextCategoryCollectionsDataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse) */ {
 public:
  inline GetNextCategoryCollectionsDataResponse() : GetNextCategoryCollectionsDataResponse(nullptr) {}
  ~GetNextCategoryCollectionsDataResponse() override;
  explicit constexpr GetNextCategoryCollectionsDataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextCategoryCollectionsDataResponse(const GetNextCategoryCollectionsDataResponse& from);
  GetNextCategoryCollectionsDataResponse(GetNextCategoryCollectionsDataResponse&& from) noexcept
    : GetNextCategoryCollectionsDataResponse() {
    *this = ::std::move(from);
  }

  inline GetNextCategoryCollectionsDataResponse& operator=(const GetNextCategoryCollectionsDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextCategoryCollectionsDataResponse& operator=(GetNextCategoryCollectionsDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextCategoryCollectionsDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextCategoryCollectionsDataResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextCategoryCollectionsDataResponse*>(
               &_GetNextCategoryCollectionsDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetNextCategoryCollectionsDataResponse& a, GetNextCategoryCollectionsDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextCategoryCollectionsDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextCategoryCollectionsDataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextCategoryCollectionsDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextCategoryCollectionsDataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextCategoryCollectionsDataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextCategoryCollectionsDataResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextCategoryCollectionsDataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse";
  }
  protected:
  explicit GetNextCategoryCollectionsDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCategoryCollectionsFieldNumber = 3,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.category.CategoryCollection category_collections = 3;
  int category_collections_size() const;
  private:
  int _internal_category_collections_size() const;
  public:
  void clear_category_collections();
  ::carbon::category::CategoryCollection* mutable_category_collections(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::category::CategoryCollection >*
      mutable_category_collections();
  private:
  const ::carbon::category::CategoryCollection& _internal_category_collections(int index) const;
  ::carbon::category::CategoryCollection* _internal_add_category_collections();
  public:
  const ::carbon::category::CategoryCollection& category_collections(int index) const;
  ::carbon::category::CategoryCollection* add_category_collections();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::category::CategoryCollection >&
      category_collections() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::category::CategoryCollection > category_collections_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcategory_5fcollection_2eproto;
};
// -------------------------------------------------------------------

class GetNextActiveCategoryCollectionIdRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest) */ {
 public:
  inline GetNextActiveCategoryCollectionIdRequest() : GetNextActiveCategoryCollectionIdRequest(nullptr) {}
  ~GetNextActiveCategoryCollectionIdRequest() override;
  explicit constexpr GetNextActiveCategoryCollectionIdRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextActiveCategoryCollectionIdRequest(const GetNextActiveCategoryCollectionIdRequest& from);
  GetNextActiveCategoryCollectionIdRequest(GetNextActiveCategoryCollectionIdRequest&& from) noexcept
    : GetNextActiveCategoryCollectionIdRequest() {
    *this = ::std::move(from);
  }

  inline GetNextActiveCategoryCollectionIdRequest& operator=(const GetNextActiveCategoryCollectionIdRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextActiveCategoryCollectionIdRequest& operator=(GetNextActiveCategoryCollectionIdRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextActiveCategoryCollectionIdRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextActiveCategoryCollectionIdRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextActiveCategoryCollectionIdRequest*>(
               &_GetNextActiveCategoryCollectionIdRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GetNextActiveCategoryCollectionIdRequest& a, GetNextActiveCategoryCollectionIdRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextActiveCategoryCollectionIdRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextActiveCategoryCollectionIdRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextActiveCategoryCollectionIdRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextActiveCategoryCollectionIdRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextActiveCategoryCollectionIdRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextActiveCategoryCollectionIdRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextActiveCategoryCollectionIdRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest";
  }
  protected:
  explicit GetNextActiveCategoryCollectionIdRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcategory_5fcollection_2eproto;
};
// -------------------------------------------------------------------

class GetNextActiveCategoryCollectionIdResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse) */ {
 public:
  inline GetNextActiveCategoryCollectionIdResponse() : GetNextActiveCategoryCollectionIdResponse(nullptr) {}
  ~GetNextActiveCategoryCollectionIdResponse() override;
  explicit constexpr GetNextActiveCategoryCollectionIdResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextActiveCategoryCollectionIdResponse(const GetNextActiveCategoryCollectionIdResponse& from);
  GetNextActiveCategoryCollectionIdResponse(GetNextActiveCategoryCollectionIdResponse&& from) noexcept
    : GetNextActiveCategoryCollectionIdResponse() {
    *this = ::std::move(from);
  }

  inline GetNextActiveCategoryCollectionIdResponse& operator=(const GetNextActiveCategoryCollectionIdResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextActiveCategoryCollectionIdResponse& operator=(GetNextActiveCategoryCollectionIdResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextActiveCategoryCollectionIdResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextActiveCategoryCollectionIdResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextActiveCategoryCollectionIdResponse*>(
               &_GetNextActiveCategoryCollectionIdResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetNextActiveCategoryCollectionIdResponse& a, GetNextActiveCategoryCollectionIdResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextActiveCategoryCollectionIdResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextActiveCategoryCollectionIdResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextActiveCategoryCollectionIdResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextActiveCategoryCollectionIdResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextActiveCategoryCollectionIdResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextActiveCategoryCollectionIdResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextActiveCategoryCollectionIdResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse";
  }
  protected:
  explicit GetNextActiveCategoryCollectionIdResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
    kTsFieldNumber = 2,
    kLastUpdatedTimestampMsFieldNumber = 4,
    kReloadRequiredFieldNumber = 3,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // int64 last_updated_timestamp_ms = 4;
  void clear_last_updated_timestamp_ms();
  int64_t last_updated_timestamp_ms() const;
  void set_last_updated_timestamp_ms(int64_t value);
  private:
  int64_t _internal_last_updated_timestamp_ms() const;
  void _internal_set_last_updated_timestamp_ms(int64_t value);
  public:

  // bool reload_required = 3;
  void clear_reload_required();
  bool reload_required() const;
  void set_reload_required(bool value);
  private:
  bool _internal_reload_required() const;
  void _internal_set_reload_required(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  ::carbon::frontend::util::Timestamp* ts_;
  int64_t last_updated_timestamp_ms_;
  bool reload_required_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcategory_5fcollection_2eproto;
};
// -------------------------------------------------------------------

class SetActiveCategoryCollectionIdRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest) */ {
 public:
  inline SetActiveCategoryCollectionIdRequest() : SetActiveCategoryCollectionIdRequest(nullptr) {}
  ~SetActiveCategoryCollectionIdRequest() override;
  explicit constexpr SetActiveCategoryCollectionIdRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveCategoryCollectionIdRequest(const SetActiveCategoryCollectionIdRequest& from);
  SetActiveCategoryCollectionIdRequest(SetActiveCategoryCollectionIdRequest&& from) noexcept
    : SetActiveCategoryCollectionIdRequest() {
    *this = ::std::move(from);
  }

  inline SetActiveCategoryCollectionIdRequest& operator=(const SetActiveCategoryCollectionIdRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveCategoryCollectionIdRequest& operator=(SetActiveCategoryCollectionIdRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveCategoryCollectionIdRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveCategoryCollectionIdRequest* internal_default_instance() {
    return reinterpret_cast<const SetActiveCategoryCollectionIdRequest*>(
               &_SetActiveCategoryCollectionIdRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SetActiveCategoryCollectionIdRequest& a, SetActiveCategoryCollectionIdRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveCategoryCollectionIdRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveCategoryCollectionIdRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveCategoryCollectionIdRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveCategoryCollectionIdRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetActiveCategoryCollectionIdRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetActiveCategoryCollectionIdRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetActiveCategoryCollectionIdRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest";
  }
  protected:
  explicit SetActiveCategoryCollectionIdRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
    kTsFieldNumber = 2,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcategory_5fcollection_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetNextCategoryCollectionsDataRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextCategoryCollectionsDataRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextCategoryCollectionsDataRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextCategoryCollectionsDataRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextCategoryCollectionsDataRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest.ts)
  return _internal_ts();
}
inline void GetNextCategoryCollectionsDataRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextCategoryCollectionsDataRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCategoryCollectionsDataRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCategoryCollectionsDataRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextCategoryCollectionsDataRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest.ts)
  return _msg;
}
inline void GetNextCategoryCollectionsDataRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.category_collection.GetNextCategoryCollectionsDataRequest.ts)
}

// -------------------------------------------------------------------

// GetNextCategoryCollectionsDataResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextCategoryCollectionsDataResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextCategoryCollectionsDataResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextCategoryCollectionsDataResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextCategoryCollectionsDataResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.ts)
  return _internal_ts();
}
inline void GetNextCategoryCollectionsDataResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextCategoryCollectionsDataResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCategoryCollectionsDataResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextCategoryCollectionsDataResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextCategoryCollectionsDataResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.ts)
  return _msg;
}
inline void GetNextCategoryCollectionsDataResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.ts)
}

// repeated .carbon.category.CategoryCollection category_collections = 3;
inline int GetNextCategoryCollectionsDataResponse::_internal_category_collections_size() const {
  return category_collections_.size();
}
inline int GetNextCategoryCollectionsDataResponse::category_collections_size() const {
  return _internal_category_collections_size();
}
inline ::carbon::category::CategoryCollection* GetNextCategoryCollectionsDataResponse::mutable_category_collections(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.category_collections)
  return category_collections_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::category::CategoryCollection >*
GetNextCategoryCollectionsDataResponse::mutable_category_collections() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.category_collections)
  return &category_collections_;
}
inline const ::carbon::category::CategoryCollection& GetNextCategoryCollectionsDataResponse::_internal_category_collections(int index) const {
  return category_collections_.Get(index);
}
inline const ::carbon::category::CategoryCollection& GetNextCategoryCollectionsDataResponse::category_collections(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.category_collections)
  return _internal_category_collections(index);
}
inline ::carbon::category::CategoryCollection* GetNextCategoryCollectionsDataResponse::_internal_add_category_collections() {
  return category_collections_.Add();
}
inline ::carbon::category::CategoryCollection* GetNextCategoryCollectionsDataResponse::add_category_collections() {
  ::carbon::category::CategoryCollection* _add = _internal_add_category_collections();
  // @@protoc_insertion_point(field_add:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.category_collections)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::category::CategoryCollection >&
GetNextCategoryCollectionsDataResponse::category_collections() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.category_collection.GetNextCategoryCollectionsDataResponse.category_collections)
  return category_collections_;
}

// -------------------------------------------------------------------

// GetNextActiveCategoryCollectionIdRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextActiveCategoryCollectionIdRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextActiveCategoryCollectionIdRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveCategoryCollectionIdRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveCategoryCollectionIdRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest.ts)
  return _internal_ts();
}
inline void GetNextActiveCategoryCollectionIdRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveCategoryCollectionIdRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveCategoryCollectionIdRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveCategoryCollectionIdRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveCategoryCollectionIdRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest.ts)
  return _msg;
}
inline void GetNextActiveCategoryCollectionIdRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdRequest.ts)
}

// -------------------------------------------------------------------

// GetNextActiveCategoryCollectionIdResponse

// string uuid = 1;
inline void GetNextActiveCategoryCollectionIdResponse::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& GetNextActiveCategoryCollectionIdResponse::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextActiveCategoryCollectionIdResponse::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.uuid)
}
inline std::string* GetNextActiveCategoryCollectionIdResponse::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.uuid)
  return _s;
}
inline const std::string& GetNextActiveCategoryCollectionIdResponse::_internal_uuid() const {
  return uuid_.Get();
}
inline void GetNextActiveCategoryCollectionIdResponse::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextActiveCategoryCollectionIdResponse::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextActiveCategoryCollectionIdResponse::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextActiveCategoryCollectionIdResponse::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.uuid)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool GetNextActiveCategoryCollectionIdResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextActiveCategoryCollectionIdResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveCategoryCollectionIdResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveCategoryCollectionIdResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.ts)
  return _internal_ts();
}
inline void GetNextActiveCategoryCollectionIdResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveCategoryCollectionIdResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveCategoryCollectionIdResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveCategoryCollectionIdResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveCategoryCollectionIdResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.ts)
  return _msg;
}
inline void GetNextActiveCategoryCollectionIdResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.ts)
}

// bool reload_required = 3;
inline void GetNextActiveCategoryCollectionIdResponse::clear_reload_required() {
  reload_required_ = false;
}
inline bool GetNextActiveCategoryCollectionIdResponse::_internal_reload_required() const {
  return reload_required_;
}
inline bool GetNextActiveCategoryCollectionIdResponse::reload_required() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.reload_required)
  return _internal_reload_required();
}
inline void GetNextActiveCategoryCollectionIdResponse::_internal_set_reload_required(bool value) {
  
  reload_required_ = value;
}
inline void GetNextActiveCategoryCollectionIdResponse::set_reload_required(bool value) {
  _internal_set_reload_required(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.reload_required)
}

// int64 last_updated_timestamp_ms = 4;
inline void GetNextActiveCategoryCollectionIdResponse::clear_last_updated_timestamp_ms() {
  last_updated_timestamp_ms_ = int64_t{0};
}
inline int64_t GetNextActiveCategoryCollectionIdResponse::_internal_last_updated_timestamp_ms() const {
  return last_updated_timestamp_ms_;
}
inline int64_t GetNextActiveCategoryCollectionIdResponse::last_updated_timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.last_updated_timestamp_ms)
  return _internal_last_updated_timestamp_ms();
}
inline void GetNextActiveCategoryCollectionIdResponse::_internal_set_last_updated_timestamp_ms(int64_t value) {
  
  last_updated_timestamp_ms_ = value;
}
inline void GetNextActiveCategoryCollectionIdResponse::set_last_updated_timestamp_ms(int64_t value) {
  _internal_set_last_updated_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.category_collection.GetNextActiveCategoryCollectionIdResponse.last_updated_timestamp_ms)
}

// -------------------------------------------------------------------

// SetActiveCategoryCollectionIdRequest

// string uuid = 1;
inline void SetActiveCategoryCollectionIdRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& SetActiveCategoryCollectionIdRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveCategoryCollectionIdRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.uuid)
}
inline std::string* SetActiveCategoryCollectionIdRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.uuid)
  return _s;
}
inline const std::string& SetActiveCategoryCollectionIdRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void SetActiveCategoryCollectionIdRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveCategoryCollectionIdRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveCategoryCollectionIdRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveCategoryCollectionIdRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.uuid)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool SetActiveCategoryCollectionIdRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool SetActiveCategoryCollectionIdRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& SetActiveCategoryCollectionIdRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& SetActiveCategoryCollectionIdRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.ts)
  return _internal_ts();
}
inline void SetActiveCategoryCollectionIdRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* SetActiveCategoryCollectionIdRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* SetActiveCategoryCollectionIdRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* SetActiveCategoryCollectionIdRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* SetActiveCategoryCollectionIdRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.ts)
  return _msg;
}
inline void SetActiveCategoryCollectionIdRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.category_collection.SetActiveCategoryCollectionIdRequest.ts)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace category_collection
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcategory_5fcollection_2eproto

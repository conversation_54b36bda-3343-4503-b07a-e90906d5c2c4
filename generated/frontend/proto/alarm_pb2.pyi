"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.translation_pb2 import (
    TranslationParameter as frontend___proto___translation_pb2___TranslationParameter,
)

from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

AlarmLevelValue = typing___NewType('AlarmLevelValue', builtin___int)
type___AlarmLevelValue = AlarmLevelValue
AlarmLevel: _AlarmLevel
class _AlarmLevel(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[AlarmLevelValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    AL_UNKNOWN = typing___cast(AlarmLevelValue, 0)
    AL_CRITICAL = typing___cast(AlarmLevelValue, 1)
    AL_HIGH = typing___cast(AlarmLevelValue, 2)
    AL_MEDIUM = typing___cast(AlarmLevelValue, 3)
    AL_LOW = typing___cast(AlarmLevelValue, 4)
    AL_HIDDEN = typing___cast(AlarmLevelValue, 5)
AL_UNKNOWN = typing___cast(AlarmLevelValue, 0)
AL_CRITICAL = typing___cast(AlarmLevelValue, 1)
AL_HIGH = typing___cast(AlarmLevelValue, 2)
AL_MEDIUM = typing___cast(AlarmLevelValue, 3)
AL_LOW = typing___cast(AlarmLevelValue, 4)
AL_HIDDEN = typing___cast(AlarmLevelValue, 5)

AlarmImpactValue = typing___NewType('AlarmImpactValue', builtin___int)
type___AlarmImpactValue = AlarmImpactValue
AlarmImpact: _AlarmImpact
class _AlarmImpact(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[AlarmImpactValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    AI_UNKNOWN = typing___cast(AlarmImpactValue, 0)
    AI_CRITICAL = typing___cast(AlarmImpactValue, 1)
    AI_OFFLINE = typing___cast(AlarmImpactValue, 2)
    AI_DEGRADED = typing___cast(AlarmImpactValue, 3)
    AI_NONE = typing___cast(AlarmImpactValue, 4)
AI_UNKNOWN = typing___cast(AlarmImpactValue, 0)
AI_CRITICAL = typing___cast(AlarmImpactValue, 1)
AI_OFFLINE = typing___cast(AlarmImpactValue, 2)
AI_DEGRADED = typing___cast(AlarmImpactValue, 3)
AI_NONE = typing___cast(AlarmImpactValue, 4)

class AlarmRow(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    timestamp_ms: builtin___int = ...
    alarm_code: typing___Text = ...
    subsystem: typing___Text = ...
    description: typing___Text = ...
    level: type___AlarmLevelValue = ...
    identifier: typing___Text = ...
    acknowledged: builtin___bool = ...
    impact: type___AlarmImpactValue = ...
    stop_timestamp_ms: builtin___int = ...
    autofix_available: builtin___bool = ...
    autofix_attempted: builtin___bool = ...
    autofix_duration_sec: builtin___int = ...
    description_key: typing___Text = ...

    @property
    def translation_parameters(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[frontend___proto___translation_pb2___TranslationParameter]: ...

    def __init__(self,
        *,
        timestamp_ms : typing___Optional[builtin___int] = None,
        alarm_code : typing___Optional[typing___Text] = None,
        subsystem : typing___Optional[typing___Text] = None,
        description : typing___Optional[typing___Text] = None,
        level : typing___Optional[type___AlarmLevelValue] = None,
        identifier : typing___Optional[typing___Text] = None,
        acknowledged : typing___Optional[builtin___bool] = None,
        impact : typing___Optional[type___AlarmImpactValue] = None,
        stop_timestamp_ms : typing___Optional[builtin___int] = None,
        autofix_available : typing___Optional[builtin___bool] = None,
        autofix_attempted : typing___Optional[builtin___bool] = None,
        autofix_duration_sec : typing___Optional[builtin___int] = None,
        description_key : typing___Optional[typing___Text] = None,
        translation_parameters : typing___Optional[typing___Iterable[frontend___proto___translation_pb2___TranslationParameter]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"acknowledged",b"acknowledged",u"alarm_code",b"alarm_code",u"autofix_attempted",b"autofix_attempted",u"autofix_available",b"autofix_available",u"autofix_duration_sec",b"autofix_duration_sec",u"description",b"description",u"description_key",b"description_key",u"identifier",b"identifier",u"impact",b"impact",u"level",b"level",u"stop_timestamp_ms",b"stop_timestamp_ms",u"subsystem",b"subsystem",u"timestamp_ms",b"timestamp_ms",u"translation_parameters",b"translation_parameters"]) -> None: ...
type___AlarmRow = AlarmRow

class AlarmTable(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def alarms(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___AlarmRow]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        alarms : typing___Optional[typing___Iterable[type___AlarmRow]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alarms",b"alarms",u"ts",b"ts"]) -> None: ...
type___AlarmTable = AlarmTable

class AlarmCount(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    count: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        count : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"count",b"count",u"ts",b"ts"]) -> None: ...
type___AlarmCount = AlarmCount

class AcknowledgeRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    identifier: typing___Text = ...

    def __init__(self,
        *,
        identifier : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"identifier",b"identifier"]) -> None: ...
type___AcknowledgeRequest = AcknowledgeRequest

class GetNextAlarmLogRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    from_idx: builtin___int = ...
    to_idx: builtin___int = ...
    visible_only: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        from_idx : typing___Optional[builtin___int] = None,
        to_idx : typing___Optional[builtin___int] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        visible_only : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"from_idx",b"from_idx",u"to_idx",b"to_idx",u"ts",b"ts",u"visible_only",b"visible_only"]) -> None: ...
type___GetNextAlarmLogRequest = GetNextAlarmLogRequest

class GetNextAlarmLogResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def alarms(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___AlarmRow]: ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        alarms : typing___Optional[typing___Iterable[type___AlarmRow]] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"alarms",b"alarms",u"ts",b"ts"]) -> None: ...
type___GetNextAlarmLogResponse = GetNextAlarmLogResponse

class GetNextAlarmLogCountRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    visible_only: builtin___bool = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        visible_only : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts",u"visible_only",b"visible_only"]) -> None: ...
type___GetNextAlarmLogCountRequest = GetNextAlarmLogCountRequest

class GetNextAlarmLogCountResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    num_alarms: builtin___int = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        num_alarms : typing___Optional[builtin___int] = None,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"num_alarms",b"num_alarms",u"ts",b"ts"]) -> None: ...
type___GetNextAlarmLogCountResponse = GetNextAlarmLogCountResponse

class AttemptAutofixAlarmRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    identifier: typing___Text = ...

    def __init__(self,
        *,
        identifier : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"identifier",b"identifier"]) -> None: ...
type___AttemptAutofixAlarmRequest = AttemptAutofixAlarmRequest

class GetNextAutofixAlarmStatusRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> None: ...
type___GetNextAutofixAlarmStatusRequest = GetNextAutofixAlarmStatusRequest

class GetNextAutofixAlarmStatusResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    completed: builtin___bool = ...
    error_message: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        completed : typing___Optional[builtin___bool] = None,
        error_message : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"completed",b"completed",u"error_message",b"error_message",u"ts",b"ts"]) -> None: ...
type___GetNextAutofixAlarmStatusResponse = GetNextAutofixAlarmStatusResponse

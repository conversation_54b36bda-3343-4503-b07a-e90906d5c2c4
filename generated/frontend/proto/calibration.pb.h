// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/calibration.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcalibration_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcalibration_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/camera.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fcalibration_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fcalibration_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcalibration_2eproto;
namespace carbon {
namespace frontend {
namespace color_calibration {
class ColorCalibrationValues;
struct ColorCalibrationValuesDefaultTypeInternal;
extern ColorCalibrationValuesDefaultTypeInternal _ColorCalibrationValues_default_instance_;
}  // namespace color_calibration
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::color_calibration::ColorCalibrationValues* Arena::CreateMaybeMessage<::carbon::frontend::color_calibration::ColorCalibrationValues>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace color_calibration {

// ===================================================================

class ColorCalibrationValues final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.color_calibration.ColorCalibrationValues) */ {
 public:
  inline ColorCalibrationValues() : ColorCalibrationValues(nullptr) {}
  ~ColorCalibrationValues() override;
  explicit constexpr ColorCalibrationValues(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ColorCalibrationValues(const ColorCalibrationValues& from);
  ColorCalibrationValues(ColorCalibrationValues&& from) noexcept
    : ColorCalibrationValues() {
    *this = ::std::move(from);
  }

  inline ColorCalibrationValues& operator=(const ColorCalibrationValues& from) {
    CopyFrom(from);
    return *this;
  }
  inline ColorCalibrationValues& operator=(ColorCalibrationValues&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ColorCalibrationValues& default_instance() {
    return *internal_default_instance();
  }
  static inline const ColorCalibrationValues* internal_default_instance() {
    return reinterpret_cast<const ColorCalibrationValues*>(
               &_ColorCalibrationValues_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ColorCalibrationValues& a, ColorCalibrationValues& b) {
    a.Swap(&b);
  }
  inline void Swap(ColorCalibrationValues* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ColorCalibrationValues* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ColorCalibrationValues* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ColorCalibrationValues>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ColorCalibrationValues& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ColorCalibrationValues& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ColorCalibrationValues* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.color_calibration.ColorCalibrationValues";
  }
  protected:
  explicit ColorCalibrationValues(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 4,
    kRedFieldNumber = 1,
    kGreenFieldNumber = 2,
    kBlueFieldNumber = 3,
  };
  // string cam_id = 4;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // float red = 1;
  void clear_red();
  float red() const;
  void set_red(float value);
  private:
  float _internal_red() const;
  void _internal_set_red(float value);
  public:

  // float green = 2;
  void clear_green();
  float green() const;
  void set_green(float value);
  private:
  float _internal_green() const;
  void _internal_set_green(float value);
  public:

  // float blue = 3;
  void clear_blue();
  float blue() const;
  void set_blue(float value);
  private:
  float _internal_blue() const;
  void _internal_set_blue(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.color_calibration.ColorCalibrationValues)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  float red_;
  float green_;
  float blue_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcalibration_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ColorCalibrationValues

// float red = 1;
inline void ColorCalibrationValues::clear_red() {
  red_ = 0;
}
inline float ColorCalibrationValues::_internal_red() const {
  return red_;
}
inline float ColorCalibrationValues::red() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.color_calibration.ColorCalibrationValues.red)
  return _internal_red();
}
inline void ColorCalibrationValues::_internal_set_red(float value) {
  
  red_ = value;
}
inline void ColorCalibrationValues::set_red(float value) {
  _internal_set_red(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.color_calibration.ColorCalibrationValues.red)
}

// float green = 2;
inline void ColorCalibrationValues::clear_green() {
  green_ = 0;
}
inline float ColorCalibrationValues::_internal_green() const {
  return green_;
}
inline float ColorCalibrationValues::green() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.color_calibration.ColorCalibrationValues.green)
  return _internal_green();
}
inline void ColorCalibrationValues::_internal_set_green(float value) {
  
  green_ = value;
}
inline void ColorCalibrationValues::set_green(float value) {
  _internal_set_green(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.color_calibration.ColorCalibrationValues.green)
}

// float blue = 3;
inline void ColorCalibrationValues::clear_blue() {
  blue_ = 0;
}
inline float ColorCalibrationValues::_internal_blue() const {
  return blue_;
}
inline float ColorCalibrationValues::blue() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.color_calibration.ColorCalibrationValues.blue)
  return _internal_blue();
}
inline void ColorCalibrationValues::_internal_set_blue(float value) {
  
  blue_ = value;
}
inline void ColorCalibrationValues::set_blue(float value) {
  _internal_set_blue(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.color_calibration.ColorCalibrationValues.blue)
}

// string cam_id = 4;
inline void ColorCalibrationValues::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& ColorCalibrationValues::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.color_calibration.ColorCalibrationValues.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ColorCalibrationValues::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.color_calibration.ColorCalibrationValues.cam_id)
}
inline std::string* ColorCalibrationValues::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.color_calibration.ColorCalibrationValues.cam_id)
  return _s;
}
inline const std::string& ColorCalibrationValues::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void ColorCalibrationValues::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ColorCalibrationValues::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ColorCalibrationValues::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.color_calibration.ColorCalibrationValues.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ColorCalibrationValues::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.color_calibration.ColorCalibrationValues.cam_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace color_calibration
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcalibration_2eproto

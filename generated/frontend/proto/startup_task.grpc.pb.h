// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/startup_task.proto
#ifndef GRPC_frontend_2fproto_2fstartup_5ftask_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fstartup_5ftask_2eproto__INCLUDED

#include "frontend/proto/startup_task.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace startup_task {

class StartupTaskService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.startup_task.StartupTaskService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::startup_task::GetNextTasksResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::GetNextTasksResponse>> AsyncGetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::GetNextTasksResponse>>(AsyncGetNextTasksRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::GetNextTasksResponse>> PrepareAsyncGetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::GetNextTasksResponse>>(PrepareAsyncGetNextTasksRaw(context, request, cq));
    }
    virtual ::grpc::Status MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>> AsyncMarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>>(AsyncMarkTaskCompleteRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>> PrepareAsyncMarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>>(PrepareAsyncMarkTaskCompleteRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::GetNextTasksResponse>* AsyncGetNextTasksRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::GetNextTasksResponse>* PrepareAsyncGetNextTasksRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* AsyncMarkTaskCompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* PrepareAsyncMarkTaskCompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::startup_task::GetNextTasksResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::GetNextTasksResponse>> AsyncGetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::GetNextTasksResponse>>(AsyncGetNextTasksRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::GetNextTasksResponse>> PrepareAsyncGetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::GetNextTasksResponse>>(PrepareAsyncGetNextTasksRaw(context, request, cq));
    }
    ::grpc::Status MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>> AsyncMarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>>(AsyncMarkTaskCompleteRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>> PrepareAsyncMarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>>(PrepareAsyncMarkTaskCompleteRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextTasks(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response, std::function<void(::grpc::Status)>) override;
      void MarkTaskComplete(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::GetNextTasksResponse>* AsyncGetNextTasksRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::GetNextTasksResponse>* PrepareAsyncGetNextTasksRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* AsyncMarkTaskCompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* PrepareAsyncMarkTaskCompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextTasks_;
    const ::grpc::internal::RpcMethod rpcmethod_MarkTaskComplete_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextTasks(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response);
    virtual ::grpc::Status MarkTaskComplete(::grpc::ServerContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextTasks() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTasks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::startup_task::GetNextTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextTasks(::grpc::ServerContext* context, ::carbon::frontend::util::Timestamp* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::startup_task::GetNextTasksResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_MarkTaskComplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_MarkTaskComplete() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_MarkTaskComplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkTaskComplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* /*request*/, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMarkTaskComplete(::grpc::ServerContext* context, ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextTasks<WithAsyncMethod_MarkTaskComplete<Service > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextTasks() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::startup_task::GetNextTasksResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::startup_task::GetNextTasksResponse* response) { return this->GetNextTasks(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextTasks(
        ::grpc::MessageAllocator< ::carbon::frontend::util::Timestamp, ::carbon::frontend::startup_task::GetNextTasksResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::util::Timestamp, ::carbon::frontend::startup_task::GetNextTasksResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTasks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::startup_task::GetNextTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextTasks(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::startup_task::GetNextTasksResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_MarkTaskComplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_MarkTaskComplete() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::carbon::frontend::startup_task::MarkTaskCompleteResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* request, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* response) { return this->MarkTaskComplete(context, request, response); }));}
    void SetMessageAllocatorFor_MarkTaskComplete(
        ::grpc::MessageAllocator< ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::carbon::frontend::startup_task::MarkTaskCompleteResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_MarkTaskComplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkTaskComplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* /*request*/, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* MarkTaskComplete(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* /*request*/, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextTasks<WithCallbackMethod_MarkTaskComplete<Service > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextTasks() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTasks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::startup_task::GetNextTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_MarkTaskComplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_MarkTaskComplete() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_MarkTaskComplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkTaskComplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* /*request*/, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextTasks() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTasks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::startup_task::GetNextTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextTasks(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_MarkTaskComplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_MarkTaskComplete() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_MarkTaskComplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkTaskComplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* /*request*/, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestMarkTaskComplete(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextTasks() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextTasks(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextTasks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::startup_task::GetNextTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextTasks(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_MarkTaskComplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_MarkTaskComplete() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->MarkTaskComplete(context, request, response); }));
    }
    ~WithRawCallbackMethod_MarkTaskComplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status MarkTaskComplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* /*request*/, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* MarkTaskComplete(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextTasks : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextTasks() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::util::Timestamp, ::carbon::frontend::startup_task::GetNextTasksResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::util::Timestamp, ::carbon::frontend::startup_task::GetNextTasksResponse>* streamer) {
                       return this->StreamedGetNextTasks(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextTasks() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextTasks(::grpc::ServerContext* /*context*/, const ::carbon::frontend::util::Timestamp* /*request*/, ::carbon::frontend::startup_task::GetNextTasksResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextTasks(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::util::Timestamp,::carbon::frontend::startup_task::GetNextTasksResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_MarkTaskComplete : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_MarkTaskComplete() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::carbon::frontend::startup_task::MarkTaskCompleteResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::startup_task::MarkTaskCompleteRequest, ::carbon::frontend::startup_task::MarkTaskCompleteResponse>* streamer) {
                       return this->StreamedMarkTaskComplete(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_MarkTaskComplete() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status MarkTaskComplete(::grpc::ServerContext* /*context*/, const ::carbon::frontend::startup_task::MarkTaskCompleteRequest* /*request*/, ::carbon::frontend::startup_task::MarkTaskCompleteResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedMarkTaskComplete(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::startup_task::MarkTaskCompleteRequest,::carbon::frontend::startup_task::MarkTaskCompleteResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextTasks<WithStreamedUnaryMethod_MarkTaskComplete<Service > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextTasks<WithStreamedUnaryMethod_MarkTaskComplete<Service > > StreamedService;
};

}  // namespace startup_task
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fstartup_5ftask_2eproto__INCLUDED

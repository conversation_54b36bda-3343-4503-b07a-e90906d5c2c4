# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/dashboard.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import translation_pb2 as frontend_dot_proto_dot_translation__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/dashboard.proto',
  package='carbon.frontend.dashboard',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1e\x66rontend/proto/dashboard.proto\x12\x19\x63\x61rbon.frontend.dashboard\x1a frontend/proto/translation.proto\x1a\x19\x66rontend/proto/util.proto\"\xca\x01\n\x0b\x45xtraStatus\x12\r\n\x05title\x18\x01 \x01(\t\x12\x11\n\ticon_name\x18\x02 \x01(\t\x12\x12\n\nicon_color\x18\x03 \x01(\t\x12\x13\n\x0bstatus_text\x18\x04 \x01(\t\x12\x14\n\x0cstatus_color\x18\x05 \x01(\t\x12\x10\n\x08group_id\x18\x06 \x01(\t\x12\x12\n\nsection_id\x18\x07 \x01(\t\x12\x10\n\x08progress\x18\x08 \x01(\x01\x12\r\n\x05width\x18\t \x01(\r\x12\x13\n\x0b\x62ottom_text\x18\n \x01(\t\" \n\rWeedTargeting\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\";\n\x11ThinningTargeting\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x15\n\talgorithm\x18\x02 \x01(\x04\x42\x02\x18\x01\"\xaf\x02\n\x0eTargetingState\x12<\n\nweed_state\x18\x01 \x01(\x0b\x32(.carbon.frontend.dashboard.WeedTargeting\x12\x44\n\x0ethinning_state\x18\x02 \x01(\x0b\x32,.carbon.frontend.dashboard.ThinningTargeting\x12\x13\n\x07\x65nabled\x18\x03 \x03(\x08\x42\x02\x18\x01\x12P\n\x0c\x65nabled_rows\x18\x04 \x03(\x0b\x32:.carbon.frontend.dashboard.TargetingState.EnabledRowsEntry\x1a\x32\n\x10\x45nabledRowsEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"\xb7\x01\n\x0f\x45xtraConclusion\x12\r\n\x05title\x18\x01 \x01(\t\x12\x17\n\x0f\x66lip_thresholds\x18\x02 \x01(\x08\x12\x1e\n\x16good_threshold_percent\x18\x03 \x01(\r\x12 \n\x18medium_threshold_percent\x18\x04 \x01(\r\x12:\n\x07percent\x18\x05 \x01(\x0b\x32).carbon.frontend.translation.PercentValue\"\x9f\x01\n\x0fRowStateMessage\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\x12\x1d\n\x15target_state_mismatch\x18\x02 \x01(\x08\x12\r\n\x05ready\x18\x03 \x01(\x08\x12M\n\x15safety_override_state\x18\x04 \x01(\x0e\x32..carbon.frontend.dashboard.SafetyOverrideState\"\xa0\r\n\x15\x44\x61shboardStateMessage\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x16\n\x0elasers_enabled\x18\x02 \x01(\x08\x12\x17\n\x0brow_enabled\x18\x03 \x03(\x08\x42\x02\x18\x01\x12\x36\n\x06\x65xtras\x18\x04 \x03(\x0b\x32&.carbon.frontend.dashboard.ExtraStatus\x12<\n\x0eselected_model\x18\x05 \x01(\x0b\x32$.carbon.frontend.dashboard.CropModel\x12\x42\n\x0ftargeting_state\x18\x06 \x01(\x0b\x32).carbon.frontend.dashboard.TargetingState\x12!\n\x15target_state_mismatch\x18\x07 \x03(\x08\x42\x02\x18\x01\x12\x15\n\trow_ready\x18\x08 \x03(\x08\x42\x02\x18\x01\x12\x16\n\nrow_exists\x18\t \x03(\x08\x42\x02\x18\x01\x12\x14\n\x0crow_width_in\x18\n \x01(\x01\x12Q\n\x15safety_override_state\x18\x0b \x03(\x0e\x32..carbon.frontend.dashboard.SafetyOverrideStateB\x02\x18\x01\x12\x42\n\x0fimplement_state\x18\r \x01(\x0e\x32).carbon.frontend.dashboard.ImplementState\x12\x1a\n\x12\x65\x66\x66iciency_enabled\x18\x0e \x01(\x08\x12\x45\n\x12\x65\x66\x66iciency_percent\x18\x0f \x01(\x0b\x32).carbon.frontend.translation.PercentValue\x12\x1a\n\x12\x65rror_rate_enabled\x18\x10 \x01(\x08\x12=\n\nerror_rate\x18\x11 \x01(\x0b\x32).carbon.frontend.translation.PercentValue\x12\x45\n\x11\x65xtra_conclusions\x18\x12 \x03(\x0b\x32*.carbon.frontend.dashboard.ExtraConclusion\x12\x41\n\x11\x61rea_weeded_today\x18\x13 \x01(\x0b\x32&.carbon.frontend.translation.AreaValue\x12\x41\n\x11\x61rea_weeded_total\x18\x14 \x01(\x0b\x32&.carbon.frontend.translation.AreaValue\x12\x45\n\x12weeds_killed_today\x18\x15 \x01(\x0b\x32).carbon.frontend.translation.IntegerValue\x12\x45\n\x12weeds_killed_total\x18\x16 \x01(\x0b\x32).carbon.frontend.translation.IntegerValue\x12\x45\n\x11time_weeded_today\x18\x17 \x01(\x0b\x32*.carbon.frontend.translation.DurationValue\x12\x45\n\x11time_weeded_total\x18\x18 \x01(\x0b\x32*.carbon.frontend.translation.DurationValue\x12\x17\n\x0fweeding_enabled\x18\x19 \x01(\x08\x12\x12\n\ndebug_mode\x18\x1a \x01(\x08\x12\x45\n\x12\x63rops_killed_today\x18\x1b \x01(\x0b\x32).carbon.frontend.translation.IntegerValue\x12\x45\n\x12\x63rops_killed_total\x18\x1c \x01(\x0b\x32).carbon.frontend.translation.IntegerValue\x12\x16\n\x0e\x63ruise_enabled\x18\x1d \x01(\x08\x12S\n\nrow_states\x18\x1e \x03(\x0b\x32?.carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry\x12\x1b\n\x13\x63ruise_allow_enable\x18\x1f \x01(\x08\x12 \n\x18\x63ruise_disallowed_reason\x18  \x01(\t\x1a\\\n\x0eRowStatesEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x39\n\x05value\x18\x02 \x01(\x0b\x32*.carbon.frontend.dashboard.RowStateMessage:\x02\x38\x01J\x04\x08\x0c\x10\r\"T\n\tCropModel\x12\x10\n\x04\x63rop\x18\x01 \x01(\tB\x02\x18\x01\x12\x11\n\thas_model\x18\x02 \x01(\x08\x12\x11\n\tpreferred\x18\x03 \x01(\x08\x12\x0f\n\x07\x63rop_id\x18\x04 \x01(\t\"H\n\x10\x43ropModelOptions\x12\x34\n\x06models\x18\x01 \x03(\x0b\x32$.carbon.frontend.dashboard.CropModel\"\x1b\n\x05RowId\x12\x12\n\nrow_number\x18\x01 \x01(\r\"\xe3\x02\n\x0fWeedingVelocity\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x1c\n\x14\x63urrent_velocity_mph\x18\x02 \x01(\x01\x12\x1b\n\x13target_velocity_mph\x18\x03 \x01(\x01\x12\x15\n\rtolerance_mph\x18\x04 \x01(\x01\x12\'\n\x1fprimary_target_velocity_top_mph\x18\x05 \x01(\x01\x12*\n\"primary_target_velocity_bottom_mph\x18\x06 \x01(\x01\x12)\n!secondary_target_velocity_top_mph\x18\x07 \x01(\x01\x12,\n$secondary_target_velocity_bottom_mph\x18\x08 \x01(\x01\x12#\n\x1b\x63ruise_control_velocity_mph\x18\t \x01(\x01\"\x1b\n\nRowSpacing\x12\r\n\x05width\x18\x01 \x01(\x01\"\x1f\n\x0c\x43ruiseEnable\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08*M\n\x13SafetyOverrideState\x12\x16\n\x12SafetyOverrideNone\x10\x00\x12\x1e\n\x1aSafetyOverrideVelocityStop\x10\x01*)\n\x0eImplementState\x12\n\n\x06RAISED\x10\x00\x12\x0b\n\x07LOWERED\x10\x01\x32\xc5\x06\n\x10\x44\x61shboardService\x12J\n\tToggleRow\x12 .carbon.frontend.dashboard.RowId\x1a\x1b.carbon.frontend.util.Empty\x12H\n\x0cToggleLasers\x12\x1b.carbon.frontend.util.Empty\x1a\x1b.carbon.frontend.util.Empty\x12j\n\x15GetNextDashboardState\x12\x1f.carbon.frontend.util.Timestamp\x1a\x30.carbon.frontend.dashboard.DashboardStateMessage\x12\x64\n\x13GetCropModelOptions\x12\x1b.carbon.frontend.util.Empty\x1a+.carbon.frontend.dashboard.CropModelOptions\"\x03\x88\x02\x01\x12V\n\x0cSetCropModel\x12$.carbon.frontend.dashboard.CropModel\x1a\x1b.carbon.frontend.util.Empty\"\x03\x88\x02\x01\x12\x65\n\x16GetNextWeedingVelocity\x12\x1f.carbon.frontend.util.Timestamp\x1a*.carbon.frontend.dashboard.WeedingVelocity\x12[\n\x11SetTargetingState\x12).carbon.frontend.dashboard.TargetingState\x1a\x1b.carbon.frontend.util.Empty\x12S\n\rSetRowSpacing\x12%.carbon.frontend.dashboard.RowSpacing\x1a\x1b.carbon.frontend.util.Empty\x12X\n\x10SetCruiseEnabled\x12\'.carbon.frontend.dashboard.CruiseEnable\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_translation__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_SAFETYOVERRIDESTATE = _descriptor.EnumDescriptor(
  name='SafetyOverrideState',
  full_name='carbon.frontend.dashboard.SafetyOverrideState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SafetyOverrideNone', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SafetyOverrideVelocityStop', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3384,
  serialized_end=3461,
)
_sym_db.RegisterEnumDescriptor(_SAFETYOVERRIDESTATE)

SafetyOverrideState = enum_type_wrapper.EnumTypeWrapper(_SAFETYOVERRIDESTATE)
_IMPLEMENTSTATE = _descriptor.EnumDescriptor(
  name='ImplementState',
  full_name='carbon.frontend.dashboard.ImplementState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='RAISED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LOWERED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3463,
  serialized_end=3504,
)
_sym_db.RegisterEnumDescriptor(_IMPLEMENTSTATE)

ImplementState = enum_type_wrapper.EnumTypeWrapper(_IMPLEMENTSTATE)
SafetyOverrideNone = 0
SafetyOverrideVelocityStop = 1
RAISED = 0
LOWERED = 1



_EXTRASTATUS = _descriptor.Descriptor(
  name='ExtraStatus',
  full_name='carbon.frontend.dashboard.ExtraStatus',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='title', full_name='carbon.frontend.dashboard.ExtraStatus.title', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='icon_name', full_name='carbon.frontend.dashboard.ExtraStatus.icon_name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='icon_color', full_name='carbon.frontend.dashboard.ExtraStatus.icon_color', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_text', full_name='carbon.frontend.dashboard.ExtraStatus.status_text', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='status_color', full_name='carbon.frontend.dashboard.ExtraStatus.status_color', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='group_id', full_name='carbon.frontend.dashboard.ExtraStatus.group_id', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='section_id', full_name='carbon.frontend.dashboard.ExtraStatus.section_id', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='progress', full_name='carbon.frontend.dashboard.ExtraStatus.progress', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='width', full_name='carbon.frontend.dashboard.ExtraStatus.width', index=8,
      number=9, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bottom_text', full_name='carbon.frontend.dashboard.ExtraStatus.bottom_text', index=9,
      number=10, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=123,
  serialized_end=325,
)


_WEEDTARGETING = _descriptor.Descriptor(
  name='WeedTargeting',
  full_name='carbon.frontend.dashboard.WeedTargeting',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.dashboard.WeedTargeting.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=327,
  serialized_end=359,
)


_THINNINGTARGETING = _descriptor.Descriptor(
  name='ThinningTargeting',
  full_name='carbon.frontend.dashboard.ThinningTargeting',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.dashboard.ThinningTargeting.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='algorithm', full_name='carbon.frontend.dashboard.ThinningTargeting.algorithm', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=361,
  serialized_end=420,
)


_TARGETINGSTATE_ENABLEDROWSENTRY = _descriptor.Descriptor(
  name='EnabledRowsEntry',
  full_name='carbon.frontend.dashboard.TargetingState.EnabledRowsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.dashboard.TargetingState.EnabledRowsEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.dashboard.TargetingState.EnabledRowsEntry.value', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=676,
  serialized_end=726,
)

_TARGETINGSTATE = _descriptor.Descriptor(
  name='TargetingState',
  full_name='carbon.frontend.dashboard.TargetingState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weed_state', full_name='carbon.frontend.dashboard.TargetingState.weed_state', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning_state', full_name='carbon.frontend.dashboard.TargetingState.thinning_state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.dashboard.TargetingState.enabled', index=2,
      number=3, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled_rows', full_name='carbon.frontend.dashboard.TargetingState.enabled_rows', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_TARGETINGSTATE_ENABLEDROWSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=423,
  serialized_end=726,
)


_EXTRACONCLUSION = _descriptor.Descriptor(
  name='ExtraConclusion',
  full_name='carbon.frontend.dashboard.ExtraConclusion',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='title', full_name='carbon.frontend.dashboard.ExtraConclusion.title', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='flip_thresholds', full_name='carbon.frontend.dashboard.ExtraConclusion.flip_thresholds', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='good_threshold_percent', full_name='carbon.frontend.dashboard.ExtraConclusion.good_threshold_percent', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='medium_threshold_percent', full_name='carbon.frontend.dashboard.ExtraConclusion.medium_threshold_percent', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='percent', full_name='carbon.frontend.dashboard.ExtraConclusion.percent', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=729,
  serialized_end=912,
)


_ROWSTATEMESSAGE = _descriptor.Descriptor(
  name='RowStateMessage',
  full_name='carbon.frontend.dashboard.RowStateMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.dashboard.RowStateMessage.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_state_mismatch', full_name='carbon.frontend.dashboard.RowStateMessage.target_state_mismatch', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ready', full_name='carbon.frontend.dashboard.RowStateMessage.ready', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_override_state', full_name='carbon.frontend.dashboard.RowStateMessage.safety_override_state', index=3,
      number=4, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=915,
  serialized_end=1074,
)


_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY = _descriptor.Descriptor(
  name='RowStatesEntry',
  full_name='carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2675,
  serialized_end=2767,
)

_DASHBOARDSTATEMESSAGE = _descriptor.Descriptor(
  name='DashboardStateMessage',
  full_name='carbon.frontend.dashboard.DashboardStateMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.dashboard.DashboardStateMessage.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lasers_enabled', full_name='carbon.frontend.dashboard.DashboardStateMessage.lasers_enabled', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_enabled', full_name='carbon.frontend.dashboard.DashboardStateMessage.row_enabled', index=2,
      number=3, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extras', full_name='carbon.frontend.dashboard.DashboardStateMessage.extras', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='selected_model', full_name='carbon.frontend.dashboard.DashboardStateMessage.selected_model', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='targeting_state', full_name='carbon.frontend.dashboard.DashboardStateMessage.targeting_state', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_state_mismatch', full_name='carbon.frontend.dashboard.DashboardStateMessage.target_state_mismatch', index=6,
      number=7, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_ready', full_name='carbon.frontend.dashboard.DashboardStateMessage.row_ready', index=7,
      number=8, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_exists', full_name='carbon.frontend.dashboard.DashboardStateMessage.row_exists', index=8,
      number=9, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_width_in', full_name='carbon.frontend.dashboard.DashboardStateMessage.row_width_in', index=9,
      number=10, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety_override_state', full_name='carbon.frontend.dashboard.DashboardStateMessage.safety_override_state', index=10,
      number=11, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='implement_state', full_name='carbon.frontend.dashboard.DashboardStateMessage.implement_state', index=11,
      number=13, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='efficiency_enabled', full_name='carbon.frontend.dashboard.DashboardStateMessage.efficiency_enabled', index=12,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='efficiency_percent', full_name='carbon.frontend.dashboard.DashboardStateMessage.efficiency_percent', index=13,
      number=15, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_rate_enabled', full_name='carbon.frontend.dashboard.DashboardStateMessage.error_rate_enabled', index=14,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_rate', full_name='carbon.frontend.dashboard.DashboardStateMessage.error_rate', index=15,
      number=17, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extra_conclusions', full_name='carbon.frontend.dashboard.DashboardStateMessage.extra_conclusions', index=16,
      number=18, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='area_weeded_today', full_name='carbon.frontend.dashboard.DashboardStateMessage.area_weeded_today', index=17,
      number=19, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='area_weeded_total', full_name='carbon.frontend.dashboard.DashboardStateMessage.area_weeded_total', index=18,
      number=20, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeds_killed_today', full_name='carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_today', index=19,
      number=21, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeds_killed_total', full_name='carbon.frontend.dashboard.DashboardStateMessage.weeds_killed_total', index=20,
      number=22, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time_weeded_today', full_name='carbon.frontend.dashboard.DashboardStateMessage.time_weeded_today', index=21,
      number=23, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='time_weeded_total', full_name='carbon.frontend.dashboard.DashboardStateMessage.time_weeded_total', index=22,
      number=24, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeding_enabled', full_name='carbon.frontend.dashboard.DashboardStateMessage.weeding_enabled', index=23,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='debug_mode', full_name='carbon.frontend.dashboard.DashboardStateMessage.debug_mode', index=24,
      number=26, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crops_killed_today', full_name='carbon.frontend.dashboard.DashboardStateMessage.crops_killed_today', index=25,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crops_killed_total', full_name='carbon.frontend.dashboard.DashboardStateMessage.crops_killed_total', index=26,
      number=28, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cruise_enabled', full_name='carbon.frontend.dashboard.DashboardStateMessage.cruise_enabled', index=27,
      number=29, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_states', full_name='carbon.frontend.dashboard.DashboardStateMessage.row_states', index=28,
      number=30, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cruise_allow_enable', full_name='carbon.frontend.dashboard.DashboardStateMessage.cruise_allow_enable', index=29,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cruise_disallowed_reason', full_name='carbon.frontend.dashboard.DashboardStateMessage.cruise_disallowed_reason', index=30,
      number=32, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1077,
  serialized_end=2773,
)


_CROPMODEL = _descriptor.Descriptor(
  name='CropModel',
  full_name='carbon.frontend.dashboard.CropModel',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.dashboard.CropModel.crop', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='has_model', full_name='carbon.frontend.dashboard.CropModel.has_model', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='preferred', full_name='carbon.frontend.dashboard.CropModel.preferred', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_id', full_name='carbon.frontend.dashboard.CropModel.crop_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2775,
  serialized_end=2859,
)


_CROPMODELOPTIONS = _descriptor.Descriptor(
  name='CropModelOptions',
  full_name='carbon.frontend.dashboard.CropModelOptions',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='models', full_name='carbon.frontend.dashboard.CropModelOptions.models', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2861,
  serialized_end=2933,
)


_ROWID = _descriptor.Descriptor(
  name='RowId',
  full_name='carbon.frontend.dashboard.RowId',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_number', full_name='carbon.frontend.dashboard.RowId.row_number', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2935,
  serialized_end=2962,
)


_WEEDINGVELOCITY = _descriptor.Descriptor(
  name='WeedingVelocity',
  full_name='carbon.frontend.dashboard.WeedingVelocity',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.dashboard.WeedingVelocity.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_velocity_mph', full_name='carbon.frontend.dashboard.WeedingVelocity.current_velocity_mph', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_velocity_mph', full_name='carbon.frontend.dashboard.WeedingVelocity.target_velocity_mph', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tolerance_mph', full_name='carbon.frontend.dashboard.WeedingVelocity.tolerance_mph', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='primary_target_velocity_top_mph', full_name='carbon.frontend.dashboard.WeedingVelocity.primary_target_velocity_top_mph', index=4,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='primary_target_velocity_bottom_mph', full_name='carbon.frontend.dashboard.WeedingVelocity.primary_target_velocity_bottom_mph', index=5,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='secondary_target_velocity_top_mph', full_name='carbon.frontend.dashboard.WeedingVelocity.secondary_target_velocity_top_mph', index=6,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='secondary_target_velocity_bottom_mph', full_name='carbon.frontend.dashboard.WeedingVelocity.secondary_target_velocity_bottom_mph', index=7,
      number=8, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cruise_control_velocity_mph', full_name='carbon.frontend.dashboard.WeedingVelocity.cruise_control_velocity_mph', index=8,
      number=9, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2965,
  serialized_end=3320,
)


_ROWSPACING = _descriptor.Descriptor(
  name='RowSpacing',
  full_name='carbon.frontend.dashboard.RowSpacing',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='width', full_name='carbon.frontend.dashboard.RowSpacing.width', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3322,
  serialized_end=3349,
)


_CRUISEENABLE = _descriptor.Descriptor(
  name='CruiseEnable',
  full_name='carbon.frontend.dashboard.CruiseEnable',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.dashboard.CruiseEnable.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3351,
  serialized_end=3382,
)

_TARGETINGSTATE_ENABLEDROWSENTRY.containing_type = _TARGETINGSTATE
_TARGETINGSTATE.fields_by_name['weed_state'].message_type = _WEEDTARGETING
_TARGETINGSTATE.fields_by_name['thinning_state'].message_type = _THINNINGTARGETING
_TARGETINGSTATE.fields_by_name['enabled_rows'].message_type = _TARGETINGSTATE_ENABLEDROWSENTRY
_EXTRACONCLUSION.fields_by_name['percent'].message_type = frontend_dot_proto_dot_translation__pb2._PERCENTVALUE
_ROWSTATEMESSAGE.fields_by_name['safety_override_state'].enum_type = _SAFETYOVERRIDESTATE
_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY.fields_by_name['value'].message_type = _ROWSTATEMESSAGE
_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY.containing_type = _DASHBOARDSTATEMESSAGE
_DASHBOARDSTATEMESSAGE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_DASHBOARDSTATEMESSAGE.fields_by_name['extras'].message_type = _EXTRASTATUS
_DASHBOARDSTATEMESSAGE.fields_by_name['selected_model'].message_type = _CROPMODEL
_DASHBOARDSTATEMESSAGE.fields_by_name['targeting_state'].message_type = _TARGETINGSTATE
_DASHBOARDSTATEMESSAGE.fields_by_name['safety_override_state'].enum_type = _SAFETYOVERRIDESTATE
_DASHBOARDSTATEMESSAGE.fields_by_name['implement_state'].enum_type = _IMPLEMENTSTATE
_DASHBOARDSTATEMESSAGE.fields_by_name['efficiency_percent'].message_type = frontend_dot_proto_dot_translation__pb2._PERCENTVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['error_rate'].message_type = frontend_dot_proto_dot_translation__pb2._PERCENTVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['extra_conclusions'].message_type = _EXTRACONCLUSION
_DASHBOARDSTATEMESSAGE.fields_by_name['area_weeded_today'].message_type = frontend_dot_proto_dot_translation__pb2._AREAVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['area_weeded_total'].message_type = frontend_dot_proto_dot_translation__pb2._AREAVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['weeds_killed_today'].message_type = frontend_dot_proto_dot_translation__pb2._INTEGERVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['weeds_killed_total'].message_type = frontend_dot_proto_dot_translation__pb2._INTEGERVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['time_weeded_today'].message_type = frontend_dot_proto_dot_translation__pb2._DURATIONVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['time_weeded_total'].message_type = frontend_dot_proto_dot_translation__pb2._DURATIONVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['crops_killed_today'].message_type = frontend_dot_proto_dot_translation__pb2._INTEGERVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['crops_killed_total'].message_type = frontend_dot_proto_dot_translation__pb2._INTEGERVALUE
_DASHBOARDSTATEMESSAGE.fields_by_name['row_states'].message_type = _DASHBOARDSTATEMESSAGE_ROWSTATESENTRY
_CROPMODELOPTIONS.fields_by_name['models'].message_type = _CROPMODEL
_WEEDINGVELOCITY.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['ExtraStatus'] = _EXTRASTATUS
DESCRIPTOR.message_types_by_name['WeedTargeting'] = _WEEDTARGETING
DESCRIPTOR.message_types_by_name['ThinningTargeting'] = _THINNINGTARGETING
DESCRIPTOR.message_types_by_name['TargetingState'] = _TARGETINGSTATE
DESCRIPTOR.message_types_by_name['ExtraConclusion'] = _EXTRACONCLUSION
DESCRIPTOR.message_types_by_name['RowStateMessage'] = _ROWSTATEMESSAGE
DESCRIPTOR.message_types_by_name['DashboardStateMessage'] = _DASHBOARDSTATEMESSAGE
DESCRIPTOR.message_types_by_name['CropModel'] = _CROPMODEL
DESCRIPTOR.message_types_by_name['CropModelOptions'] = _CROPMODELOPTIONS
DESCRIPTOR.message_types_by_name['RowId'] = _ROWID
DESCRIPTOR.message_types_by_name['WeedingVelocity'] = _WEEDINGVELOCITY
DESCRIPTOR.message_types_by_name['RowSpacing'] = _ROWSPACING
DESCRIPTOR.message_types_by_name['CruiseEnable'] = _CRUISEENABLE
DESCRIPTOR.enum_types_by_name['SafetyOverrideState'] = _SAFETYOVERRIDESTATE
DESCRIPTOR.enum_types_by_name['ImplementState'] = _IMPLEMENTSTATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ExtraStatus = _reflection.GeneratedProtocolMessageType('ExtraStatus', (_message.Message,), {
  'DESCRIPTOR' : _EXTRASTATUS,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.ExtraStatus)
  })
_sym_db.RegisterMessage(ExtraStatus)

WeedTargeting = _reflection.GeneratedProtocolMessageType('WeedTargeting', (_message.Message,), {
  'DESCRIPTOR' : _WEEDTARGETING,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.WeedTargeting)
  })
_sym_db.RegisterMessage(WeedTargeting)

ThinningTargeting = _reflection.GeneratedProtocolMessageType('ThinningTargeting', (_message.Message,), {
  'DESCRIPTOR' : _THINNINGTARGETING,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.ThinningTargeting)
  })
_sym_db.RegisterMessage(ThinningTargeting)

TargetingState = _reflection.GeneratedProtocolMessageType('TargetingState', (_message.Message,), {

  'EnabledRowsEntry' : _reflection.GeneratedProtocolMessageType('EnabledRowsEntry', (_message.Message,), {
    'DESCRIPTOR' : _TARGETINGSTATE_ENABLEDROWSENTRY,
    '__module__' : 'frontend.proto.dashboard_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.TargetingState.EnabledRowsEntry)
    })
  ,
  'DESCRIPTOR' : _TARGETINGSTATE,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.TargetingState)
  })
_sym_db.RegisterMessage(TargetingState)
_sym_db.RegisterMessage(TargetingState.EnabledRowsEntry)

ExtraConclusion = _reflection.GeneratedProtocolMessageType('ExtraConclusion', (_message.Message,), {
  'DESCRIPTOR' : _EXTRACONCLUSION,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.ExtraConclusion)
  })
_sym_db.RegisterMessage(ExtraConclusion)

RowStateMessage = _reflection.GeneratedProtocolMessageType('RowStateMessage', (_message.Message,), {
  'DESCRIPTOR' : _ROWSTATEMESSAGE,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.RowStateMessage)
  })
_sym_db.RegisterMessage(RowStateMessage)

DashboardStateMessage = _reflection.GeneratedProtocolMessageType('DashboardStateMessage', (_message.Message,), {

  'RowStatesEntry' : _reflection.GeneratedProtocolMessageType('RowStatesEntry', (_message.Message,), {
    'DESCRIPTOR' : _DASHBOARDSTATEMESSAGE_ROWSTATESENTRY,
    '__module__' : 'frontend.proto.dashboard_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.DashboardStateMessage.RowStatesEntry)
    })
  ,
  'DESCRIPTOR' : _DASHBOARDSTATEMESSAGE,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.DashboardStateMessage)
  })
_sym_db.RegisterMessage(DashboardStateMessage)
_sym_db.RegisterMessage(DashboardStateMessage.RowStatesEntry)

CropModel = _reflection.GeneratedProtocolMessageType('CropModel', (_message.Message,), {
  'DESCRIPTOR' : _CROPMODEL,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.CropModel)
  })
_sym_db.RegisterMessage(CropModel)

CropModelOptions = _reflection.GeneratedProtocolMessageType('CropModelOptions', (_message.Message,), {
  'DESCRIPTOR' : _CROPMODELOPTIONS,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.CropModelOptions)
  })
_sym_db.RegisterMessage(CropModelOptions)

RowId = _reflection.GeneratedProtocolMessageType('RowId', (_message.Message,), {
  'DESCRIPTOR' : _ROWID,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.RowId)
  })
_sym_db.RegisterMessage(RowId)

WeedingVelocity = _reflection.GeneratedProtocolMessageType('WeedingVelocity', (_message.Message,), {
  'DESCRIPTOR' : _WEEDINGVELOCITY,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.WeedingVelocity)
  })
_sym_db.RegisterMessage(WeedingVelocity)

RowSpacing = _reflection.GeneratedProtocolMessageType('RowSpacing', (_message.Message,), {
  'DESCRIPTOR' : _ROWSPACING,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.RowSpacing)
  })
_sym_db.RegisterMessage(RowSpacing)

CruiseEnable = _reflection.GeneratedProtocolMessageType('CruiseEnable', (_message.Message,), {
  'DESCRIPTOR' : _CRUISEENABLE,
  '__module__' : 'frontend.proto.dashboard_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.dashboard.CruiseEnable)
  })
_sym_db.RegisterMessage(CruiseEnable)


DESCRIPTOR._options = None
_THINNINGTARGETING.fields_by_name['algorithm']._options = None
_TARGETINGSTATE_ENABLEDROWSENTRY._options = None
_TARGETINGSTATE.fields_by_name['enabled']._options = None
_DASHBOARDSTATEMESSAGE_ROWSTATESENTRY._options = None
_DASHBOARDSTATEMESSAGE.fields_by_name['row_enabled']._options = None
_DASHBOARDSTATEMESSAGE.fields_by_name['target_state_mismatch']._options = None
_DASHBOARDSTATEMESSAGE.fields_by_name['row_ready']._options = None
_DASHBOARDSTATEMESSAGE.fields_by_name['row_exists']._options = None
_DASHBOARDSTATEMESSAGE.fields_by_name['safety_override_state']._options = None
_CROPMODEL.fields_by_name['crop']._options = None

_DASHBOARDSERVICE = _descriptor.ServiceDescriptor(
  name='DashboardService',
  full_name='carbon.frontend.dashboard.DashboardService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=3507,
  serialized_end=4344,
  methods=[
  _descriptor.MethodDescriptor(
    name='ToggleRow',
    full_name='carbon.frontend.dashboard.DashboardService.ToggleRow',
    index=0,
    containing_service=None,
    input_type=_ROWID,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='ToggleLasers',
    full_name='carbon.frontend.dashboard.DashboardService.ToggleLasers',
    index=1,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextDashboardState',
    full_name='carbon.frontend.dashboard.DashboardService.GetNextDashboardState',
    index=2,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_DASHBOARDSTATEMESSAGE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetCropModelOptions',
    full_name='carbon.frontend.dashboard.DashboardService.GetCropModelOptions',
    index=3,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_CROPMODELOPTIONS,
    serialized_options=b'\210\002\001',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetCropModel',
    full_name='carbon.frontend.dashboard.DashboardService.SetCropModel',
    index=4,
    containing_service=None,
    input_type=_CROPMODEL,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=b'\210\002\001',
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextWeedingVelocity',
    full_name='carbon.frontend.dashboard.DashboardService.GetNextWeedingVelocity',
    index=5,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_WEEDINGVELOCITY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetTargetingState',
    full_name='carbon.frontend.dashboard.DashboardService.SetTargetingState',
    index=6,
    containing_service=None,
    input_type=_TARGETINGSTATE,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetRowSpacing',
    full_name='carbon.frontend.dashboard.DashboardService.SetRowSpacing',
    index=7,
    containing_service=None,
    input_type=_ROWSPACING,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetCruiseEnabled',
    full_name='carbon.frontend.dashboard.DashboardService.SetCruiseEnabled',
    index=8,
    containing_service=None,
    input_type=_CRUISEENABLE,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_DASHBOARDSERVICE)

DESCRIPTOR.services_by_name['DashboardService'] = _DASHBOARDSERVICE

# @@protoc_insertion_point(module_scope)

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/category.proto
#ifndef GRPC_frontend_2fproto_2fcategory_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fcategory_2eproto__INCLUDED

#include "frontend/proto/category.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace category {

class CategoryService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.category.CategoryService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::carbon::frontend::category::GetNextCategoryDataResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category::GetNextCategoryDataResponse>> AsyncGetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category::GetNextCategoryDataResponse>>(AsyncGetNextCategoryDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category::GetNextCategoryDataResponse>> PrepareAsyncGetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category::GetNextCategoryDataResponse>>(PrepareAsyncGetNextCategoryDataRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category::GetNextCategoryDataResponse>* AsyncGetNextCategoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::category::GetNextCategoryDataResponse>* PrepareAsyncGetNextCategoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::carbon::frontend::category::GetNextCategoryDataResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category::GetNextCategoryDataResponse>> AsyncGetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category::GetNextCategoryDataResponse>>(AsyncGetNextCategoryDataRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category::GetNextCategoryDataResponse>> PrepareAsyncGetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category::GetNextCategoryDataResponse>>(PrepareAsyncGetNextCategoryDataRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response, std::function<void(::grpc::Status)>) override;
      void GetNextCategoryData(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category::GetNextCategoryDataResponse>* AsyncGetNextCategoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::category::GetNextCategoryDataResponse>* PrepareAsyncGetNextCategoryDataRaw(::grpc::ClientContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextCategoryData_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextCategoryData(::grpc::ServerContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextCategoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextCategoryData() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextCategoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category::GetNextCategoryDataRequest* /*request*/, ::carbon::frontend::category::GetNextCategoryDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCategoryData(::grpc::ServerContext* context, ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::category::GetNextCategoryDataResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextCategoryData<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextCategoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextCategoryData() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::category::GetNextCategoryDataRequest, ::carbon::frontend::category::GetNextCategoryDataResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::category::GetNextCategoryDataRequest* request, ::carbon::frontend::category::GetNextCategoryDataResponse* response) { return this->GetNextCategoryData(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextCategoryData(
        ::grpc::MessageAllocator< ::carbon::frontend::category::GetNextCategoryDataRequest, ::carbon::frontend::category::GetNextCategoryDataResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::category::GetNextCategoryDataRequest, ::carbon::frontend::category::GetNextCategoryDataResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextCategoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category::GetNextCategoryDataRequest* /*request*/, ::carbon::frontend::category::GetNextCategoryDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCategoryData(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::category::GetNextCategoryDataRequest* /*request*/, ::carbon::frontend::category::GetNextCategoryDataResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextCategoryData<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextCategoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextCategoryData() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextCategoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category::GetNextCategoryDataRequest* /*request*/, ::carbon::frontend::category::GetNextCategoryDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextCategoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextCategoryData() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextCategoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category::GetNextCategoryDataRequest* /*request*/, ::carbon::frontend::category::GetNextCategoryDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCategoryData(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextCategoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextCategoryData() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextCategoryData(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextCategoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCategoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category::GetNextCategoryDataRequest* /*request*/, ::carbon::frontend::category::GetNextCategoryDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCategoryData(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextCategoryData : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextCategoryData() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::category::GetNextCategoryDataRequest, ::carbon::frontend::category::GetNextCategoryDataResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::category::GetNextCategoryDataRequest, ::carbon::frontend::category::GetNextCategoryDataResponse>* streamer) {
                       return this->StreamedGetNextCategoryData(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextCategoryData() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextCategoryData(::grpc::ServerContext* /*context*/, const ::carbon::frontend::category::GetNextCategoryDataRequest* /*request*/, ::carbon::frontend::category::GetNextCategoryDataResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextCategoryData(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::category::GetNextCategoryDataRequest,::carbon::frontend::category::GetNextCategoryDataResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextCategoryData<Service > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextCategoryData<Service > StreamedService;
};

}  // namespace category
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fcategory_2eproto__INCLUDED

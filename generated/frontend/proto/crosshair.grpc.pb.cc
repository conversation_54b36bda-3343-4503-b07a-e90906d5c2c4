// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/crosshair.proto

#include "frontend/proto/crosshair.pb.h"
#include "frontend/proto/crosshair.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace crosshair {

static const char* CrosshairService_method_names[] = {
  "/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateCrosshair",
  "/carbon.frontend.crosshair.CrosshairService/StartAutoCalibrateAllCrosshairs",
  "/carbon.frontend.crosshair.CrosshairService/StopAutoCalibrate",
  "/carbon.frontend.crosshair.CrosshairService/GetNextCrosshairState",
  "/carbon.frontend.crosshair.CrosshairService/SetCrosshairPosition",
  "/carbon.frontend.crosshair.CrosshairService/MoveScanner",
  "/carbon.frontend.crosshair.CrosshairService/GetNextAutoCrossHairCalState",
};

std::unique_ptr< CrosshairService::Stub> CrosshairService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< CrosshairService::Stub> stub(new CrosshairService::Stub(channel, options));
  return stub;
}

CrosshairService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_StartAutoCalibrateCrosshair_(CrosshairService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartAutoCalibrateAllCrosshairs_(CrosshairService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopAutoCalibrate_(CrosshairService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextCrosshairState_(CrosshairService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetCrosshairPosition_(CrosshairService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_MoveScanner_(CrosshairService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextAutoCrossHairCalState_(CrosshairService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status CrosshairService::Stub::StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartAutoCalibrateCrosshair_, context, request, response);
}

void CrosshairService::Stub::async::StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoCalibrateCrosshair_, context, request, response, std::move(f));
}

void CrosshairService::Stub::async::StartAutoCalibrateCrosshair(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoCalibrateCrosshair_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::PrepareAsyncStartAutoCalibrateCrosshairRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::camera::CameraRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartAutoCalibrateCrosshair_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::AsyncStartAutoCalibrateCrosshairRaw(::grpc::ClientContext* context, const ::carbon::frontend::camera::CameraRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartAutoCalibrateCrosshairRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CrosshairService::Stub::StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartAutoCalibrateAllCrosshairs_, context, request, response);
}

void CrosshairService::Stub::async::StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoCalibrateAllCrosshairs_, context, request, response, std::move(f));
}

void CrosshairService::Stub::async::StartAutoCalibrateAllCrosshairs(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartAutoCalibrateAllCrosshairs_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::PrepareAsyncStartAutoCalibrateAllCrosshairsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartAutoCalibrateAllCrosshairs_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::AsyncStartAutoCalibrateAllCrosshairsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartAutoCalibrateAllCrosshairsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CrosshairService::Stub::StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopAutoCalibrate_, context, request, response);
}

void CrosshairService::Stub::async::StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopAutoCalibrate_, context, request, response, std::move(f));
}

void CrosshairService::Stub::async::StopAutoCalibrate(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopAutoCalibrate_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::PrepareAsyncStopAutoCalibrateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopAutoCalibrate_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::AsyncStopAutoCalibrateRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopAutoCalibrateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CrosshairService::Stub::GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::carbon::frontend::crosshair::CrosshairPositionState* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::crosshair::CrosshairPositionRequest, ::carbon::frontend::crosshair::CrosshairPositionState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextCrosshairState_, context, request, response);
}

void CrosshairService::Stub::async::GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::crosshair::CrosshairPositionRequest, ::carbon::frontend::crosshair::CrosshairPositionState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCrosshairState_, context, request, response, std::move(f));
}

void CrosshairService::Stub::async::GetNextCrosshairState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextCrosshairState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::CrosshairPositionState>* CrosshairService::Stub::PrepareAsyncGetNextCrosshairStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::crosshair::CrosshairPositionState, ::carbon::frontend::crosshair::CrosshairPositionRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextCrosshairState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::CrosshairPositionState>* CrosshairService::Stub::AsyncGetNextCrosshairStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextCrosshairStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CrosshairService::Stub::SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetCrosshairPosition_, context, request, response);
}

void CrosshairService::Stub::async::SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCrosshairPosition_, context, request, response, std::move(f));
}

void CrosshairService::Stub::async::SetCrosshairPosition(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetCrosshairPosition_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::PrepareAsyncSetCrosshairPositionRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetCrosshairPosition_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::AsyncSetCrosshairPositionRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetCrosshairPositionRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CrosshairService::Stub::MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::crosshair::MoveScannerRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_MoveScanner_, context, request, response);
}

void CrosshairService::Stub::async::MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::crosshair::MoveScannerRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MoveScanner_, context, request, response, std::move(f));
}

void CrosshairService::Stub::async::MoveScanner(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MoveScanner_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::PrepareAsyncMoveScannerRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::crosshair::MoveScannerRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_MoveScanner_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* CrosshairService::Stub::AsyncMoveScannerRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncMoveScannerRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status CrosshairService::Stub::GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAutoCrossHairCalState_, context, request, response);
}

void CrosshairService::Stub::async::GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAutoCrossHairCalState_, context, request, response, std::move(f));
}

void CrosshairService::Stub::async::GetNextAutoCrossHairCalState(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAutoCrossHairCalState_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* CrosshairService::Stub::PrepareAsyncGetNextAutoCrossHairCalStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAutoCrossHairCalState_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>* CrosshairService::Stub::AsyncGetNextAutoCrossHairCalStateRaw(::grpc::ClientContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAutoCrossHairCalStateRaw(context, request, cq);
  result->StartCall();
  return result;
}

CrosshairService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CrosshairService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CrosshairService::Service, ::carbon::frontend::camera::CameraRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CrosshairService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::camera::CameraRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartAutoCalibrateCrosshair(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CrosshairService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CrosshairService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CrosshairService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartAutoCalibrateAllCrosshairs(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CrosshairService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CrosshairService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CrosshairService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StopAutoCalibrate(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CrosshairService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CrosshairService::Service, ::carbon::frontend::crosshair::CrosshairPositionRequest, ::carbon::frontend::crosshair::CrosshairPositionState, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CrosshairService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::crosshair::CrosshairPositionRequest* req,
             ::carbon::frontend::crosshair::CrosshairPositionState* resp) {
               return service->GetNextCrosshairState(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CrosshairService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CrosshairService::Service, ::carbon::frontend::crosshair::SetCrosshairPositionRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CrosshairService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SetCrosshairPosition(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CrosshairService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CrosshairService::Service, ::carbon::frontend::crosshair::MoveScannerRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CrosshairService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::crosshair::MoveScannerRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->MoveScanner(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      CrosshairService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< CrosshairService::Service, ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](CrosshairService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* req,
             ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* resp) {
               return service->GetNextAutoCrossHairCalState(ctx, req, resp);
             }, this)));
}

CrosshairService::Service::~Service() {
}

::grpc::Status CrosshairService::Service::StartAutoCalibrateCrosshair(::grpc::ServerContext* context, const ::carbon::frontend::camera::CameraRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CrosshairService::Service::StartAutoCalibrateAllCrosshairs(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CrosshairService::Service::StopAutoCalibrate(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CrosshairService::Service::GetNextCrosshairState(::grpc::ServerContext* context, const ::carbon::frontend::crosshair::CrosshairPositionRequest* request, ::carbon::frontend::crosshair::CrosshairPositionState* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CrosshairService::Service::SetCrosshairPosition(::grpc::ServerContext* context, const ::carbon::frontend::crosshair::SetCrosshairPositionRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CrosshairService::Service::MoveScanner(::grpc::ServerContext* context, const ::carbon::frontend::crosshair::MoveScannerRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status CrosshairService::Service::GetNextAutoCrossHairCalState(::grpc::ServerContext* context, const ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* request, ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace crosshair


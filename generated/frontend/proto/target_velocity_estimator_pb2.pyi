"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.target_velocity_estimator.target_velocity_estimator_pb2 import (
    ProfileDetails as proto___target_velocity_estimator___target_velocity_estimator_pb2___ProfileDetails,
    TVEProfile as proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GetNextAvailableTVEProfilesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def profiles(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[proto___target_velocity_estimator___target_velocity_estimator_pb2___ProfileDetails]: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        profiles : typing___Optional[typing___Iterable[proto___target_velocity_estimator___target_velocity_estimator_pb2___ProfileDetails]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"profiles",b"profiles",u"ts",b"ts"]) -> None: ...
type___GetNextAvailableTVEProfilesResponse = GetNextAvailableTVEProfilesResponse

class GetNextActiveTVEProfileResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def profile(self) -> proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        profile : typing___Optional[proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"profile",b"profile",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"profile",b"profile",u"ts",b"ts"]) -> None: ...
type___GetNextActiveTVEProfileResponse = GetNextActiveTVEProfileResponse

class LoadTVEProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___LoadTVEProfileRequest = LoadTVEProfileRequest

class LoadTVEProfileResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def profile(self) -> proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile: ...

    def __init__(self,
        *,
        profile : typing___Optional[proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"profile",b"profile"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"profile",b"profile"]) -> None: ...
type___LoadTVEProfileResponse = LoadTVEProfileResponse

class SaveTVEProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    set_active: builtin___bool = ...

    @property
    def profile(self) -> proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile: ...

    def __init__(self,
        *,
        profile : typing___Optional[proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile] = None,
        set_active : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"profile",b"profile"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"profile",b"profile",u"set_active",b"set_active"]) -> None: ...
type___SaveTVEProfileRequest = SaveTVEProfileRequest

class SaveTVEProfileResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___SaveTVEProfileResponse = SaveTVEProfileResponse

class SetActiveTVEProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id"]) -> None: ...
type___SetActiveTVEProfileRequest = SetActiveTVEProfileRequest

class SetActiveTVEProfileResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetActiveTVEProfileResponse = SetActiveTVEProfileResponse

class DeleteTVEProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    id: typing___Text = ...
    new_active_id: typing___Text = ...

    def __init__(self,
        *,
        id : typing___Optional[typing___Text] = None,
        new_active_id : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"new_active_id",b"new_active_id"]) -> None: ...
type___DeleteTVEProfileRequest = DeleteTVEProfileRequest

class DeleteTVEProfileResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___DeleteTVEProfileResponse = DeleteTVEProfileResponse

# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import messages_pb2 as frontend_dot_proto_dot_messages__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class MessagesServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.ReadMessage = channel.unary_unary(
                '/carbon.frontend.debug.MessagesService/ReadMessage',
                request_serializer=frontend_dot_proto_dot_messages__pb2.ReadRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.SendMessage = channel.unary_unary(
                '/carbon.frontend.debug.MessagesService/SendMessage',
                request_serializer=frontend_dot_proto_dot_messages__pb2.MessageRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetNextMessages = channel.unary_unary(
                '/carbon.frontend.debug.MessagesService/GetNextMessages',
                request_serializer=frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_messages__pb2.MessagesResponse.FromString,
                )


class MessagesServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def ReadMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SendMessage(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextMessages(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_MessagesServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'ReadMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.ReadMessage,
                    request_deserializer=frontend_dot_proto_dot_messages__pb2.ReadRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'SendMessage': grpc.unary_unary_rpc_method_handler(
                    servicer.SendMessage,
                    request_deserializer=frontend_dot_proto_dot_messages__pb2.MessageRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetNextMessages': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextMessages,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Timestamp.FromString,
                    response_serializer=frontend_dot_proto_dot_messages__pb2.MessagesResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.debug.MessagesService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class MessagesService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def ReadMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.MessagesService/ReadMessage',
            frontend_dot_proto_dot_messages__pb2.ReadRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SendMessage(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.MessagesService/SendMessage',
            frontend_dot_proto_dot_messages__pb2.MessageRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextMessages(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.debug.MessagesService/GetNextMessages',
            frontend_dot_proto_dot_util__pb2.Timestamp.SerializeToString,
            frontend_dot_proto_dot_messages__pb2.MessagesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/alarm.proto

#include "frontend/proto/alarm.pb.h"
#include "frontend/proto/alarm.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace alarm {

static const char* AlarmService_method_names[] = {
  "/carbon.frontend.alarm.AlarmService/GetNextAlarmList",
  "/carbon.frontend.alarm.AlarmService/GetNextAlarmCount",
  "/carbon.frontend.alarm.AlarmService/GetNextNewAlarmList",
  "/carbon.frontend.alarm.AlarmService/AcknowledgeAlarm",
  "/carbon.frontend.alarm.AlarmService/ResetAlarms",
  "/carbon.frontend.alarm.AlarmService/GetNextAlarmLog",
  "/carbon.frontend.alarm.AlarmService/GetNextAlarmLogCount",
  "/carbon.frontend.alarm.AlarmService/AttemptAutofixAlarm",
  "/carbon.frontend.alarm.AlarmService/GetNextAutofixAlarmStatus",
};

std::unique_ptr< AlarmService::Stub> AlarmService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< AlarmService::Stub> stub(new AlarmService::Stub(channel, options));
  return stub;
}

AlarmService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextAlarmList_(AlarmService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextAlarmCount_(AlarmService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextNewAlarmList_(AlarmService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_AcknowledgeAlarm_(AlarmService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_ResetAlarms_(AlarmService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextAlarmLog_(AlarmService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextAlarmLogCount_(AlarmService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_AttemptAutofixAlarm_(AlarmService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextAutofixAlarmStatus_(AlarmService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status AlarmService::Stub::GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmTable* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAlarmList_, context, request, response);
}

void AlarmService::Stub::async::GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlarmList_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::GetNextAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlarmList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>* AlarmService::Stub::PrepareAsyncGetNextAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::alarm::AlarmTable, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAlarmList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>* AlarmService::Stub::AsyncGetNextAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAlarmListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlarmService::Stub::GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmCount* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmCount, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAlarmCount_, context, request, response);
}

void AlarmService::Stub::async::GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmCount, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlarmCount_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::GetNextAlarmCount(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlarmCount_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmCount>* AlarmService::Stub::PrepareAsyncGetNextAlarmCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::alarm::AlarmCount, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAlarmCount_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmCount>* AlarmService::Stub::AsyncGetNextAlarmCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAlarmCountRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlarmService::Stub::GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::alarm::AlarmTable* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextNewAlarmList_, context, request, response);
}

void AlarmService::Stub::async::GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextNewAlarmList_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::GetNextNewAlarmList(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextNewAlarmList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>* AlarmService::Stub::PrepareAsyncGetNextNewAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::alarm::AlarmTable, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextNewAlarmList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::AlarmTable>* AlarmService::Stub::AsyncGetNextNewAlarmListRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextNewAlarmListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlarmService::Stub::AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::alarm::AcknowledgeRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_AcknowledgeAlarm_, context, request, response);
}

void AlarmService::Stub::async::AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::alarm::AcknowledgeRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AcknowledgeAlarm_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::AcknowledgeAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AcknowledgeAlarm_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlarmService::Stub::PrepareAsyncAcknowledgeAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::alarm::AcknowledgeRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_AcknowledgeAlarm_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlarmService::Stub::AsyncAcknowledgeAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncAcknowledgeAlarmRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlarmService::Stub::ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ResetAlarms_, context, request, response);
}

void AlarmService::Stub::async::ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetAlarms_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::ResetAlarms(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ResetAlarms_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlarmService::Stub::PrepareAsyncResetAlarmsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ResetAlarms_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlarmService::Stub::AsyncResetAlarmsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncResetAlarmsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlarmService::Stub::GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::carbon::frontend::alarm::GetNextAlarmLogResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAlarmLog_, context, request, response);
}

void AlarmService::Stub::async::GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::carbon::frontend::alarm::GetNextAlarmLogResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlarmLog_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::GetNextAlarmLog(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlarmLog_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogResponse>* AlarmService::Stub::PrepareAsyncGetNextAlarmLogRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::alarm::GetNextAlarmLogResponse, ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAlarmLog_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogResponse>* AlarmService::Stub::AsyncGetNextAlarmLogRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAlarmLogRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlarmService::Stub::GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAlarmLogCount_, context, request, response);
}

void AlarmService::Stub::async::GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlarmLogCount_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::GetNextAlarmLogCount(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAlarmLogCount_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* AlarmService::Stub::PrepareAsyncGetNextAlarmLogCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse, ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAlarmLogCount_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAlarmLogCountResponse>* AlarmService::Stub::AsyncGetNextAlarmLogCountRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAlarmLogCountRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlarmService::Stub::AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_AttemptAutofixAlarm_, context, request, response);
}

void AlarmService::Stub::async::AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AttemptAutofixAlarm_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::AttemptAutofixAlarm(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AttemptAutofixAlarm_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlarmService::Stub::PrepareAsyncAttemptAutofixAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_AttemptAutofixAlarm_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* AlarmService::Stub::AsyncAttemptAutofixAlarmRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncAttemptAutofixAlarmRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status AlarmService::Stub::GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextAutofixAlarmStatus_, context, request, response);
}

void AlarmService::Stub::async::GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAutofixAlarmStatus_, context, request, response, std::move(f));
}

void AlarmService::Stub::async::GetNextAutofixAlarmStatus(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextAutofixAlarmStatus_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* AlarmService::Stub::PrepareAsyncGetNextAutofixAlarmStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextAutofixAlarmStatus_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse>* AlarmService::Stub::AsyncGetNextAutofixAlarmStatusRaw(::grpc::ClientContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextAutofixAlarmStatusRaw(context, request, cq);
  result->StartCall();
  return result;
}

AlarmService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::alarm::AlarmTable* resp) {
               return service->GetNextAlarmList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmCount, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::alarm::AlarmCount* resp) {
               return service->GetNextAlarmCount(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::alarm::AlarmTable, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::alarm::AlarmTable* resp) {
               return service->GetNextNewAlarmList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::alarm::AcknowledgeRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::alarm::AcknowledgeRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->AcknowledgeAlarm(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ResetAlarms(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::alarm::GetNextAlarmLogRequest, ::carbon::frontend::alarm::GetNextAlarmLogResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::alarm::GetNextAlarmLogRequest* req,
             ::carbon::frontend::alarm::GetNextAlarmLogResponse* resp) {
               return service->GetNextAlarmLog(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::alarm::GetNextAlarmLogCountRequest, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* req,
             ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* resp) {
               return service->GetNextAlarmLogCount(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::alarm::AttemptAutofixAlarmRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->AttemptAutofixAlarm(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      AlarmService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< AlarmService::Service, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](AlarmService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* req,
             ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* resp) {
               return service->GetNextAutofixAlarmStatus(ctx, req, resp);
             }, this)));
}

AlarmService::Service::~Service() {
}

::grpc::Status AlarmService::Service::GetNextAlarmList(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlarmService::Service::GetNextAlarmCount(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmCount* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlarmService::Service::GetNextNewAlarmList(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::alarm::AlarmTable* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlarmService::Service::AcknowledgeAlarm(::grpc::ServerContext* context, const ::carbon::frontend::alarm::AcknowledgeRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlarmService::Service::ResetAlarms(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlarmService::Service::GetNextAlarmLog(::grpc::ServerContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlarmService::Service::GetNextAlarmLogCount(::grpc::ServerContext* context, const ::carbon::frontend::alarm::GetNextAlarmLogCountRequest* request, ::carbon::frontend::alarm::GetNextAlarmLogCountResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlarmService::Service::AttemptAutofixAlarm(::grpc::ServerContext* context, const ::carbon::frontend::alarm::AttemptAutofixAlarmRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status AlarmService::Service::GetNextAutofixAlarmStatus(::grpc::ServerContext* context, const ::carbon::frontend::alarm::GetNextAutofixAlarmStatusRequest* request, ::carbon::frontend::alarm::GetNextAutofixAlarmStatusResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace alarm


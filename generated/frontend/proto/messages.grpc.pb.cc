// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/messages.proto

#include "frontend/proto/messages.pb.h"
#include "frontend/proto/messages.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace debug {

static const char* MessagesService_method_names[] = {
  "/carbon.frontend.debug.MessagesService/ReadMessage",
  "/carbon.frontend.debug.MessagesService/SendMessage",
  "/carbon.frontend.debug.MessagesService/GetNextMessages",
};

std::unique_ptr< MessagesService::Stub> MessagesService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< MessagesService::Stub> stub(new MessagesService::Stub(channel, options));
  return stub;
}

MessagesService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_ReadMessage_(MessagesService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SendMessage_(MessagesService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextMessages_(MessagesService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status MessagesService::Stub::ReadMessage(::grpc::ClientContext* context, const ::carbon::frontend::debug::ReadRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::debug::ReadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_ReadMessage_, context, request, response);
}

void MessagesService::Stub::async::ReadMessage(::grpc::ClientContext* context, const ::carbon::frontend::debug::ReadRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::debug::ReadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReadMessage_, context, request, response, std::move(f));
}

void MessagesService::Stub::async::ReadMessage(::grpc::ClientContext* context, const ::carbon::frontend::debug::ReadRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_ReadMessage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* MessagesService::Stub::PrepareAsyncReadMessageRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::ReadRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::debug::ReadRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_ReadMessage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* MessagesService::Stub::AsyncReadMessageRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::ReadRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncReadMessageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MessagesService::Stub::SendMessage(::grpc::ClientContext* context, const ::carbon::frontend::debug::MessageRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::debug::MessageRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SendMessage_, context, request, response);
}

void MessagesService::Stub::async::SendMessage(::grpc::ClientContext* context, const ::carbon::frontend::debug::MessageRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::debug::MessageRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SendMessage_, context, request, response, std::move(f));
}

void MessagesService::Stub::async::SendMessage(::grpc::ClientContext* context, const ::carbon::frontend::debug::MessageRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SendMessage_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* MessagesService::Stub::PrepareAsyncSendMessageRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::MessageRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::debug::MessageRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SendMessage_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* MessagesService::Stub::AsyncSendMessageRaw(::grpc::ClientContext* context, const ::carbon::frontend::debug::MessageRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSendMessageRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status MessagesService::Stub::GetNextMessages(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::carbon::frontend::debug::MessagesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::debug::MessagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextMessages_, context, request, response);
}

void MessagesService::Stub::async::GetNextMessages(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::debug::MessagesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Timestamp, ::carbon::frontend::debug::MessagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextMessages_, context, request, response, std::move(f));
}

void MessagesService::Stub::async::GetNextMessages(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::debug::MessagesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextMessages_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::MessagesResponse>* MessagesService::Stub::PrepareAsyncGetNextMessagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::debug::MessagesResponse, ::carbon::frontend::util::Timestamp, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextMessages_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::debug::MessagesResponse>* MessagesService::Stub::AsyncGetNextMessagesRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Timestamp& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextMessagesRaw(context, request, cq);
  result->StartCall();
  return result;
}

MessagesService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MessagesService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MessagesService::Service, ::carbon::frontend::debug::ReadRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MessagesService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::debug::ReadRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->ReadMessage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MessagesService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MessagesService::Service, ::carbon::frontend::debug::MessageRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MessagesService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::debug::MessageRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->SendMessage(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      MessagesService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< MessagesService::Service, ::carbon::frontend::util::Timestamp, ::carbon::frontend::debug::MessagesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](MessagesService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Timestamp* req,
             ::carbon::frontend::debug::MessagesResponse* resp) {
               return service->GetNextMessages(ctx, req, resp);
             }, this)));
}

MessagesService::Service::~Service() {
}

::grpc::Status MessagesService::Service::ReadMessage(::grpc::ServerContext* context, const ::carbon::frontend::debug::ReadRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MessagesService::Service::SendMessage(::grpc::ServerContext* context, const ::carbon::frontend::debug::MessageRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status MessagesService::Service::GetNextMessages(::grpc::ServerContext* context, const ::carbon::frontend::util::Timestamp* request, ::carbon::frontend::debug::MessagesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace debug


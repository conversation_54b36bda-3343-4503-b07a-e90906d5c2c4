// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/jobs.proto

#include "frontend/proto/jobs.pb.h"
#include "frontend/proto/jobs.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace frontend {
namespace jobs {

static const char* JobsService_method_names[] = {
  "/carbon.frontend.jobs.JobsService/GetNextJobs",
  "/carbon.frontend.jobs.JobsService/CreateJob",
  "/carbon.frontend.jobs.JobsService/UpdateJob",
  "/carbon.frontend.jobs.JobsService/StartJob",
  "/carbon.frontend.jobs.JobsService/StopActiveJob",
  "/carbon.frontend.jobs.JobsService/GetNextActiveJobId",
  "/carbon.frontend.jobs.JobsService/GetJob",
  "/carbon.frontend.jobs.JobsService/GetConfigDump",
  "/carbon.frontend.jobs.JobsService/GetActiveJobMetrics",
  "/carbon.frontend.jobs.JobsService/DeleteJob",
  "/carbon.frontend.jobs.JobsService/MarkJobCompleted",
  "/carbon.frontend.jobs.JobsService/MarkJobIncomplete",
  "/carbon.frontend.jobs.JobsService/GetNextJob",
};

std::unique_ptr< JobsService::Stub> JobsService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< JobsService::Stub> stub(new JobsService::Stub(channel, options));
  return stub;
}

JobsService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetNextJobs_(JobsService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_CreateJob_(JobsService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpdateJob_(JobsService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StartJob_(JobsService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_StopActiveJob_(JobsService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextActiveJobId_(JobsService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetJob_(JobsService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetConfigDump_(JobsService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetActiveJobMetrics_(JobsService_method_names[8], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteJob_(JobsService_method_names[9], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_MarkJobCompleted_(JobsService_method_names[10], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_MarkJobIncomplete_(JobsService_method_names[11], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetNextJob_(JobsService_method_names[12], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status JobsService::Stub::GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::carbon::frontend::jobs::GetNextJobsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::GetNextJobsRequest, ::carbon::frontend::jobs::GetNextJobsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextJobs_, context, request, response);
}

void JobsService::Stub::async::GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::GetNextJobsRequest, ::carbon::frontend::jobs::GetNextJobsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextJobs_, context, request, response, std::move(f));
}

void JobsService::Stub::async::GetNextJobs(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextJobs_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobsResponse>* JobsService::Stub::PrepareAsyncGetNextJobsRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::jobs::GetNextJobsResponse, ::carbon::frontend::jobs::GetNextJobsRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextJobs_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobsResponse>* JobsService::Stub::AsyncGetNextJobsRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextJobsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::carbon::frontend::jobs::CreateJobResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::CreateJobRequest, ::carbon::frontend::jobs::CreateJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_CreateJob_, context, request, response);
}

void JobsService::Stub::async::CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::CreateJobRequest, ::carbon::frontend::jobs::CreateJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CreateJob_, context, request, response, std::move(f));
}

void JobsService::Stub::async::CreateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_CreateJob_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::CreateJobResponse>* JobsService::Stub::PrepareAsyncCreateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::jobs::CreateJobResponse, ::carbon::frontend::jobs::CreateJobRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_CreateJob_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::CreateJobResponse>* JobsService::Stub::AsyncCreateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::CreateJobRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncCreateJobRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::UpdateJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UpdateJob_, context, request, response);
}

void JobsService::Stub::async::UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::UpdateJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateJob_, context, request, response, std::move(f));
}

void JobsService::Stub::async::UpdateJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpdateJob_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::PrepareAsyncUpdateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::UpdateJobRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UpdateJob_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::AsyncUpdateJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::UpdateJobRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpdateJobRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::StartJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StartJob_, context, request, response);
}

void JobsService::Stub::async::StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::StartJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartJob_, context, request, response, std::move(f));
}

void JobsService::Stub::async::StartJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StartJob_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::PrepareAsyncStartJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::StartJobRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StartJob_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::AsyncStartJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::StartJobRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStartJobRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_StopActiveJob_, context, request, response);
}

void JobsService::Stub::async::StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopActiveJob_, context, request, response, std::move(f));
}

void JobsService::Stub::async::StopActiveJob(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_StopActiveJob_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::PrepareAsyncStopActiveJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_StopActiveJob_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::AsyncStopActiveJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncStopActiveJobRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::carbon::frontend::jobs::GetNextActiveJobIdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextActiveJobId_, context, request, response);
}

void JobsService::Stub::async::GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::carbon::frontend::jobs::GetNextActiveJobIdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveJobId_, context, request, response, std::move(f));
}

void JobsService::Stub::async::GetNextActiveJobId(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextActiveJobId_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* JobsService::Stub::PrepareAsyncGetNextActiveJobIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::jobs::GetNextActiveJobIdResponse, ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextActiveJobId_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextActiveJobIdResponse>* JobsService::Stub::AsyncGetNextActiveJobIdRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextActiveJobIdRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::carbon::frontend::jobs::GetJobResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::GetJobRequest, ::carbon::frontend::jobs::GetJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetJob_, context, request, response);
}

void JobsService::Stub::async::GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::GetJobRequest, ::carbon::frontend::jobs::GetJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetJob_, context, request, response, std::move(f));
}

void JobsService::Stub::async::GetJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetJob_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetJobResponse>* JobsService::Stub::PrepareAsyncGetJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::jobs::GetJobResponse, ::carbon::frontend::jobs::GetJobRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetJob_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetJobResponse>* JobsService::Stub::AsyncGetJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetJobRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetJobRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::carbon::frontend::jobs::GetConfigDumpResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::GetConfigDumpRequest, ::carbon::frontend::jobs::GetConfigDumpResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetConfigDump_, context, request, response);
}

void JobsService::Stub::async::GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::GetConfigDumpRequest, ::carbon::frontend::jobs::GetConfigDumpResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConfigDump_, context, request, response, std::move(f));
}

void JobsService::Stub::async::GetConfigDump(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetConfigDump_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetConfigDumpResponse>* JobsService::Stub::PrepareAsyncGetConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::jobs::GetConfigDumpResponse, ::carbon::frontend::jobs::GetConfigDumpRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetConfigDump_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetConfigDumpResponse>* JobsService::Stub::AsyncGetConfigDumpRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetConfigDumpRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::GetActiveJobMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetActiveJobMetrics_, context, request, response);
}

void JobsService::Stub::async::GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::GetActiveJobMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetActiveJobMetrics_, context, request, response, std::move(f));
}

void JobsService::Stub::async::GetActiveJobMetrics(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetActiveJobMetrics_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* JobsService::Stub::PrepareAsyncGetActiveJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::jobs::GetActiveJobMetricsResponse, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetActiveJobMetrics_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetActiveJobMetricsResponse>* JobsService::Stub::AsyncGetActiveJobMetricsRaw(::grpc::ClientContext* context, const ::carbon::frontend::util::Empty& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetActiveJobMetricsRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::DeleteJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteJob_, context, request, response);
}

void JobsService::Stub::async::DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::DeleteJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteJob_, context, request, response, std::move(f));
}

void JobsService::Stub::async::DeleteJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteJob_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::PrepareAsyncDeleteJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::DeleteJobRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteJob_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::AsyncDeleteJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::DeleteJobRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteJobRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::MarkJobCompletedRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_MarkJobCompleted_, context, request, response);
}

void JobsService::Stub::async::MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::MarkJobCompletedRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MarkJobCompleted_, context, request, response, std::move(f));
}

void JobsService::Stub::async::MarkJobCompleted(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MarkJobCompleted_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::PrepareAsyncMarkJobCompletedRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::MarkJobCompletedRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_MarkJobCompleted_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::AsyncMarkJobCompletedRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncMarkJobCompletedRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::carbon::frontend::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_MarkJobIncomplete_, context, request, response);
}

void JobsService::Stub::async::MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MarkJobIncomplete_, context, request, response, std::move(f));
}

void JobsService::Stub::async::MarkJobIncomplete(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_MarkJobIncomplete_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::PrepareAsyncMarkJobIncompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_MarkJobIncomplete_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::util::Empty>* JobsService::Stub::AsyncMarkJobIncompleteRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncMarkJobIncompleteRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status JobsService::Stub::GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::carbon::frontend::jobs::GetNextJobResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::frontend::jobs::GetNextJobRequest, ::carbon::frontend::jobs::GetNextJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetNextJob_, context, request, response);
}

void JobsService::Stub::async::GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::frontend::jobs::GetNextJobRequest, ::carbon::frontend::jobs::GetNextJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextJob_, context, request, response, std::move(f));
}

void JobsService::Stub::async::GetNextJob(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetNextJob_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobResponse>* JobsService::Stub::PrepareAsyncGetNextJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::frontend::jobs::GetNextJobResponse, ::carbon::frontend::jobs::GetNextJobRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetNextJob_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::frontend::jobs::GetNextJobResponse>* JobsService::Stub::AsyncGetNextJobRaw(::grpc::ClientContext* context, const ::carbon::frontend::jobs::GetNextJobRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetNextJobRaw(context, request, cq);
  result->StartCall();
  return result;
}

JobsService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::GetNextJobsRequest, ::carbon::frontend::jobs::GetNextJobsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::GetNextJobsRequest* req,
             ::carbon::frontend::jobs::GetNextJobsResponse* resp) {
               return service->GetNextJobs(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::CreateJobRequest, ::carbon::frontend::jobs::CreateJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::CreateJobRequest* req,
             ::carbon::frontend::jobs::CreateJobResponse* resp) {
               return service->CreateJob(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::UpdateJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::UpdateJobRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->UpdateJob(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::StartJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::StartJobRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StartJob(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->StopActiveJob(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::GetNextActiveJobIdRequest, ::carbon::frontend::jobs::GetNextActiveJobIdResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* req,
             ::carbon::frontend::jobs::GetNextActiveJobIdResponse* resp) {
               return service->GetNextActiveJobId(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::GetJobRequest, ::carbon::frontend::jobs::GetJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::GetJobRequest* req,
             ::carbon::frontend::jobs::GetJobResponse* resp) {
               return service->GetJob(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::GetConfigDumpRequest, ::carbon::frontend::jobs::GetConfigDumpResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::GetConfigDumpRequest* req,
             ::carbon::frontend::jobs::GetConfigDumpResponse* resp) {
               return service->GetConfigDump(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[8],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::util::Empty, ::carbon::frontend::jobs::GetActiveJobMetricsResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::util::Empty* req,
             ::carbon::frontend::jobs::GetActiveJobMetricsResponse* resp) {
               return service->GetActiveJobMetrics(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[9],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::DeleteJobRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::DeleteJobRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->DeleteJob(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[10],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::MarkJobCompletedRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::MarkJobCompletedRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->MarkJobCompleted(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[11],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::MarkJobIncompleteRequest, ::carbon::frontend::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::MarkJobIncompleteRequest* req,
             ::carbon::frontend::util::Empty* resp) {
               return service->MarkJobIncomplete(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      JobsService_method_names[12],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< JobsService::Service, ::carbon::frontend::jobs::GetNextJobRequest, ::carbon::frontend::jobs::GetNextJobResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](JobsService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::frontend::jobs::GetNextJobRequest* req,
             ::carbon::frontend::jobs::GetNextJobResponse* resp) {
               return service->GetNextJob(ctx, req, resp);
             }, this)));
}

JobsService::Service::~Service() {
}

::grpc::Status JobsService::Service::GetNextJobs(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetNextJobsRequest* request, ::carbon::frontend::jobs::GetNextJobsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::CreateJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::CreateJobRequest* request, ::carbon::frontend::jobs::CreateJobResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::UpdateJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::UpdateJobRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::StartJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::StartJobRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::StopActiveJob(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::GetNextActiveJobId(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetNextActiveJobIdRequest* request, ::carbon::frontend::jobs::GetNextActiveJobIdResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::GetJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetJobRequest* request, ::carbon::frontend::jobs::GetJobResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::GetConfigDump(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetConfigDumpRequest* request, ::carbon::frontend::jobs::GetConfigDumpResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::GetActiveJobMetrics(::grpc::ServerContext* context, const ::carbon::frontend::util::Empty* request, ::carbon::frontend::jobs::GetActiveJobMetricsResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::DeleteJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::DeleteJobRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::MarkJobCompleted(::grpc::ServerContext* context, const ::carbon::frontend::jobs::MarkJobCompletedRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::MarkJobIncomplete(::grpc::ServerContext* context, const ::carbon::frontend::jobs::MarkJobIncompleteRequest* request, ::carbon::frontend::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status JobsService::Service::GetNextJob(::grpc::ServerContext* context, const ::carbon::frontend::jobs::GetNextJobRequest* request, ::carbon::frontend::jobs::GetNextJobResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace frontend
}  // namespace jobs


// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/module.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmodule_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmodule_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fmodule_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fmodule_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[18]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fmodule_2eproto;
namespace carbon {
namespace frontend {
namespace module {
class AssignModuleRequest;
struct AssignModuleRequestDefaultTypeInternal;
extern AssignModuleRequestDefaultTypeInternal _AssignModuleRequest_default_instance_;
class BarDefinition;
struct BarDefinitionDefaultTypeInternal;
extern BarDefinitionDefaultTypeInternal _BarDefinition_default_instance_;
class ClearModuleAssignmentRequest;
struct ClearModuleAssignmentRequestDefaultTypeInternal;
extern ClearModuleAssignmentRequestDefaultTypeInternal _ClearModuleAssignmentRequest_default_instance_;
class GetCurrentRobotDefinitionResponse;
struct GetCurrentRobotDefinitionResponseDefaultTypeInternal;
extern GetCurrentRobotDefinitionResponseDefaultTypeInternal _GetCurrentRobotDefinitionResponse_default_instance_;
class GetNextActiveModulesRequest;
struct GetNextActiveModulesRequestDefaultTypeInternal;
extern GetNextActiveModulesRequestDefaultTypeInternal _GetNextActiveModulesRequest_default_instance_;
class GetNextActiveModulesResponse;
struct GetNextActiveModulesResponseDefaultTypeInternal;
extern GetNextActiveModulesResponseDefaultTypeInternal _GetNextActiveModulesResponse_default_instance_;
class GetNextModulesListRequest;
struct GetNextModulesListRequestDefaultTypeInternal;
extern GetNextModulesListRequestDefaultTypeInternal _GetNextModulesListRequest_default_instance_;
class GetNextModulesListResponse;
struct GetNextModulesListResponseDefaultTypeInternal;
extern GetNextModulesListResponseDefaultTypeInternal _GetNextModulesListResponse_default_instance_;
class GetPresetsListRequest;
struct GetPresetsListRequestDefaultTypeInternal;
extern GetPresetsListRequestDefaultTypeInternal _GetPresetsListRequest_default_instance_;
class GetPresetsListResponse;
struct GetPresetsListResponseDefaultTypeInternal;
extern GetPresetsListResponseDefaultTypeInternal _GetPresetsListResponse_default_instance_;
class IdentifyModuleRequest;
struct IdentifyModuleRequestDefaultTypeInternal;
extern IdentifyModuleRequestDefaultTypeInternal _IdentifyModuleRequest_default_instance_;
class ModuleDefinition;
struct ModuleDefinitionDefaultTypeInternal;
extern ModuleDefinitionDefaultTypeInternal _ModuleDefinition_default_instance_;
class ModuleIdentity;
struct ModuleIdentityDefaultTypeInternal;
extern ModuleIdentityDefaultTypeInternal _ModuleIdentity_default_instance_;
class Preset;
struct PresetDefaultTypeInternal;
extern PresetDefaultTypeInternal _Preset_default_instance_;
class RobotDefinition;
struct RobotDefinitionDefaultTypeInternal;
extern RobotDefinitionDefaultTypeInternal _RobotDefinition_default_instance_;
class RowDefinition;
struct RowDefinitionDefaultTypeInternal;
extern RowDefinitionDefaultTypeInternal _RowDefinition_default_instance_;
class SetCurrentRobotDefinitionRequest;
struct SetCurrentRobotDefinitionRequestDefaultTypeInternal;
extern SetCurrentRobotDefinitionRequestDefaultTypeInternal _SetCurrentRobotDefinitionRequest_default_instance_;
class SetModuleSerialRequest;
struct SetModuleSerialRequestDefaultTypeInternal;
extern SetModuleSerialRequestDefaultTypeInternal _SetModuleSerialRequest_default_instance_;
}  // namespace module
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::module::AssignModuleRequest* Arena::CreateMaybeMessage<::carbon::frontend::module::AssignModuleRequest>(Arena*);
template<> ::carbon::frontend::module::BarDefinition* Arena::CreateMaybeMessage<::carbon::frontend::module::BarDefinition>(Arena*);
template<> ::carbon::frontend::module::ClearModuleAssignmentRequest* Arena::CreateMaybeMessage<::carbon::frontend::module::ClearModuleAssignmentRequest>(Arena*);
template<> ::carbon::frontend::module::GetCurrentRobotDefinitionResponse* Arena::CreateMaybeMessage<::carbon::frontend::module::GetCurrentRobotDefinitionResponse>(Arena*);
template<> ::carbon::frontend::module::GetNextActiveModulesRequest* Arena::CreateMaybeMessage<::carbon::frontend::module::GetNextActiveModulesRequest>(Arena*);
template<> ::carbon::frontend::module::GetNextActiveModulesResponse* Arena::CreateMaybeMessage<::carbon::frontend::module::GetNextActiveModulesResponse>(Arena*);
template<> ::carbon::frontend::module::GetNextModulesListRequest* Arena::CreateMaybeMessage<::carbon::frontend::module::GetNextModulesListRequest>(Arena*);
template<> ::carbon::frontend::module::GetNextModulesListResponse* Arena::CreateMaybeMessage<::carbon::frontend::module::GetNextModulesListResponse>(Arena*);
template<> ::carbon::frontend::module::GetPresetsListRequest* Arena::CreateMaybeMessage<::carbon::frontend::module::GetPresetsListRequest>(Arena*);
template<> ::carbon::frontend::module::GetPresetsListResponse* Arena::CreateMaybeMessage<::carbon::frontend::module::GetPresetsListResponse>(Arena*);
template<> ::carbon::frontend::module::IdentifyModuleRequest* Arena::CreateMaybeMessage<::carbon::frontend::module::IdentifyModuleRequest>(Arena*);
template<> ::carbon::frontend::module::ModuleDefinition* Arena::CreateMaybeMessage<::carbon::frontend::module::ModuleDefinition>(Arena*);
template<> ::carbon::frontend::module::ModuleIdentity* Arena::CreateMaybeMessage<::carbon::frontend::module::ModuleIdentity>(Arena*);
template<> ::carbon::frontend::module::Preset* Arena::CreateMaybeMessage<::carbon::frontend::module::Preset>(Arena*);
template<> ::carbon::frontend::module::RobotDefinition* Arena::CreateMaybeMessage<::carbon::frontend::module::RobotDefinition>(Arena*);
template<> ::carbon::frontend::module::RowDefinition* Arena::CreateMaybeMessage<::carbon::frontend::module::RowDefinition>(Arena*);
template<> ::carbon::frontend::module::SetCurrentRobotDefinitionRequest* Arena::CreateMaybeMessage<::carbon::frontend::module::SetCurrentRobotDefinitionRequest>(Arena*);
template<> ::carbon::frontend::module::SetModuleSerialRequest* Arena::CreateMaybeMessage<::carbon::frontend::module::SetModuleSerialRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace module {

// ===================================================================

class ModuleIdentity final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.ModuleIdentity) */ {
 public:
  inline ModuleIdentity() : ModuleIdentity(nullptr) {}
  ~ModuleIdentity() override;
  explicit constexpr ModuleIdentity(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModuleIdentity(const ModuleIdentity& from);
  ModuleIdentity(ModuleIdentity&& from) noexcept
    : ModuleIdentity() {
    *this = ::std::move(from);
  }

  inline ModuleIdentity& operator=(const ModuleIdentity& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModuleIdentity& operator=(ModuleIdentity&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModuleIdentity& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModuleIdentity* internal_default_instance() {
    return reinterpret_cast<const ModuleIdentity*>(
               &_ModuleIdentity_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(ModuleIdentity& a, ModuleIdentity& b) {
    a.Swap(&b);
  }
  inline void Swap(ModuleIdentity* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModuleIdentity* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModuleIdentity* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModuleIdentity>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModuleIdentity& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModuleIdentity& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModuleIdentity* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.ModuleIdentity";
  }
  protected:
  explicit ModuleIdentity(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSerialFieldNumber = 2,
    kIdFieldNumber = 1,
  };
  // string serial = 2;
  void clear_serial();
  const std::string& serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial();
  PROTOBUF_NODISCARD std::string* release_serial();
  void set_allocated_serial(std::string* serial);
  private:
  const std::string& _internal_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial(const std::string& value);
  std::string* _internal_mutable_serial();
  public:

  // uint32 id = 1;
  void clear_id();
  uint32_t id() const;
  void set_id(uint32_t value);
  private:
  uint32_t _internal_id() const;
  void _internal_set_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.ModuleIdentity)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_;
  uint32_t id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class GetNextModulesListRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.GetNextModulesListRequest) */ {
 public:
  inline GetNextModulesListRequest() : GetNextModulesListRequest(nullptr) {}
  ~GetNextModulesListRequest() override;
  explicit constexpr GetNextModulesListRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextModulesListRequest(const GetNextModulesListRequest& from);
  GetNextModulesListRequest(GetNextModulesListRequest&& from) noexcept
    : GetNextModulesListRequest() {
    *this = ::std::move(from);
  }

  inline GetNextModulesListRequest& operator=(const GetNextModulesListRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextModulesListRequest& operator=(GetNextModulesListRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextModulesListRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextModulesListRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextModulesListRequest*>(
               &_GetNextModulesListRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetNextModulesListRequest& a, GetNextModulesListRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextModulesListRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextModulesListRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextModulesListRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextModulesListRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextModulesListRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextModulesListRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextModulesListRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.GetNextModulesListRequest";
  }
  protected:
  explicit GetNextModulesListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.GetNextModulesListRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class GetNextModulesListResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.GetNextModulesListResponse) */ {
 public:
  inline GetNextModulesListResponse() : GetNextModulesListResponse(nullptr) {}
  ~GetNextModulesListResponse() override;
  explicit constexpr GetNextModulesListResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextModulesListResponse(const GetNextModulesListResponse& from);
  GetNextModulesListResponse(GetNextModulesListResponse&& from) noexcept
    : GetNextModulesListResponse() {
    *this = ::std::move(from);
  }

  inline GetNextModulesListResponse& operator=(const GetNextModulesListResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextModulesListResponse& operator=(GetNextModulesListResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextModulesListResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextModulesListResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextModulesListResponse*>(
               &_GetNextModulesListResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GetNextModulesListResponse& a, GetNextModulesListResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextModulesListResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextModulesListResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextModulesListResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextModulesListResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextModulesListResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextModulesListResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextModulesListResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.GetNextModulesListResponse";
  }
  protected:
  explicit GetNextModulesListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAssignedModulesFieldNumber = 2,
    kUnassignedModulesFieldNumber = 3,
    kUnsetSerialModulesFieldNumber = 4,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 2;
  int assigned_modules_size() const;
  private:
  int _internal_assigned_modules_size() const;
  public:
  void clear_assigned_modules();
  ::carbon::frontend::module::ModuleIdentity* mutable_assigned_modules(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
      mutable_assigned_modules();
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_assigned_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* _internal_add_assigned_modules();
  public:
  const ::carbon::frontend::module::ModuleIdentity& assigned_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* add_assigned_modules();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
      assigned_modules() const;

  // repeated .carbon.frontend.module.ModuleIdentity unassigned_modules = 3;
  int unassigned_modules_size() const;
  private:
  int _internal_unassigned_modules_size() const;
  public:
  void clear_unassigned_modules();
  ::carbon::frontend::module::ModuleIdentity* mutable_unassigned_modules(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
      mutable_unassigned_modules();
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_unassigned_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* _internal_add_unassigned_modules();
  public:
  const ::carbon::frontend::module::ModuleIdentity& unassigned_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* add_unassigned_modules();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
      unassigned_modules() const;

  // repeated .carbon.frontend.module.ModuleIdentity unset_serial_modules = 4;
  int unset_serial_modules_size() const;
  private:
  int _internal_unset_serial_modules_size() const;
  public:
  void clear_unset_serial_modules();
  ::carbon::frontend::module::ModuleIdentity* mutable_unset_serial_modules(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
      mutable_unset_serial_modules();
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_unset_serial_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* _internal_add_unset_serial_modules();
  public:
  const ::carbon::frontend::module::ModuleIdentity& unset_serial_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* add_unset_serial_modules();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
      unset_serial_modules() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.GetNextModulesListResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity > assigned_modules_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity > unassigned_modules_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity > unset_serial_modules_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class GetNextActiveModulesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.GetNextActiveModulesRequest) */ {
 public:
  inline GetNextActiveModulesRequest() : GetNextActiveModulesRequest(nullptr) {}
  ~GetNextActiveModulesRequest() override;
  explicit constexpr GetNextActiveModulesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextActiveModulesRequest(const GetNextActiveModulesRequest& from);
  GetNextActiveModulesRequest(GetNextActiveModulesRequest&& from) noexcept
    : GetNextActiveModulesRequest() {
    *this = ::std::move(from);
  }

  inline GetNextActiveModulesRequest& operator=(const GetNextActiveModulesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextActiveModulesRequest& operator=(GetNextActiveModulesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextActiveModulesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextActiveModulesRequest* internal_default_instance() {
    return reinterpret_cast<const GetNextActiveModulesRequest*>(
               &_GetNextActiveModulesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(GetNextActiveModulesRequest& a, GetNextActiveModulesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextActiveModulesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextActiveModulesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextActiveModulesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextActiveModulesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextActiveModulesRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextActiveModulesRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextActiveModulesRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.GetNextActiveModulesRequest";
  }
  protected:
  explicit GetNextActiveModulesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.GetNextActiveModulesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class GetNextActiveModulesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.GetNextActiveModulesResponse) */ {
 public:
  inline GetNextActiveModulesResponse() : GetNextActiveModulesResponse(nullptr) {}
  ~GetNextActiveModulesResponse() override;
  explicit constexpr GetNextActiveModulesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextActiveModulesResponse(const GetNextActiveModulesResponse& from);
  GetNextActiveModulesResponse(GetNextActiveModulesResponse&& from) noexcept
    : GetNextActiveModulesResponse() {
    *this = ::std::move(from);
  }

  inline GetNextActiveModulesResponse& operator=(const GetNextActiveModulesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextActiveModulesResponse& operator=(GetNextActiveModulesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextActiveModulesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextActiveModulesResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextActiveModulesResponse*>(
               &_GetNextActiveModulesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetNextActiveModulesResponse& a, GetNextActiveModulesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextActiveModulesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextActiveModulesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextActiveModulesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextActiveModulesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextActiveModulesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextActiveModulesResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextActiveModulesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.GetNextActiveModulesResponse";
  }
  protected:
  explicit GetNextActiveModulesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kActiveModulesFieldNumber = 2,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.frontend.module.ModuleIdentity active_modules = 2;
  int active_modules_size() const;
  private:
  int _internal_active_modules_size() const;
  public:
  void clear_active_modules();
  ::carbon::frontend::module::ModuleIdentity* mutable_active_modules(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
      mutable_active_modules();
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_active_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* _internal_add_active_modules();
  public:
  const ::carbon::frontend::module::ModuleIdentity& active_modules(int index) const;
  ::carbon::frontend::module::ModuleIdentity* add_active_modules();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
      active_modules() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.GetNextActiveModulesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity > active_modules_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class IdentifyModuleRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.IdentifyModuleRequest) */ {
 public:
  inline IdentifyModuleRequest() : IdentifyModuleRequest(nullptr) {}
  ~IdentifyModuleRequest() override;
  explicit constexpr IdentifyModuleRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IdentifyModuleRequest(const IdentifyModuleRequest& from);
  IdentifyModuleRequest(IdentifyModuleRequest&& from) noexcept
    : IdentifyModuleRequest() {
    *this = ::std::move(from);
  }

  inline IdentifyModuleRequest& operator=(const IdentifyModuleRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline IdentifyModuleRequest& operator=(IdentifyModuleRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IdentifyModuleRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const IdentifyModuleRequest* internal_default_instance() {
    return reinterpret_cast<const IdentifyModuleRequest*>(
               &_IdentifyModuleRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(IdentifyModuleRequest& a, IdentifyModuleRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(IdentifyModuleRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IdentifyModuleRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IdentifyModuleRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IdentifyModuleRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IdentifyModuleRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const IdentifyModuleRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IdentifyModuleRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.IdentifyModuleRequest";
  }
  protected:
  explicit IdentifyModuleRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModuleIdentityFieldNumber = 1,
  };
  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  bool has_module_identity() const;
  private:
  bool _internal_has_module_identity() const;
  public:
  void clear_module_identity();
  const ::carbon::frontend::module::ModuleIdentity& module_identity() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::ModuleIdentity* release_module_identity();
  ::carbon::frontend::module::ModuleIdentity* mutable_module_identity();
  void set_allocated_module_identity(::carbon::frontend::module::ModuleIdentity* module_identity);
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_module_identity() const;
  ::carbon::frontend::module::ModuleIdentity* _internal_mutable_module_identity();
  public:
  void unsafe_arena_set_allocated_module_identity(
      ::carbon::frontend::module::ModuleIdentity* module_identity);
  ::carbon::frontend::module::ModuleIdentity* unsafe_arena_release_module_identity();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.IdentifyModuleRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::module::ModuleIdentity* module_identity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class AssignModuleRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.AssignModuleRequest) */ {
 public:
  inline AssignModuleRequest() : AssignModuleRequest(nullptr) {}
  ~AssignModuleRequest() override;
  explicit constexpr AssignModuleRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AssignModuleRequest(const AssignModuleRequest& from);
  AssignModuleRequest(AssignModuleRequest&& from) noexcept
    : AssignModuleRequest() {
    *this = ::std::move(from);
  }

  inline AssignModuleRequest& operator=(const AssignModuleRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline AssignModuleRequest& operator=(AssignModuleRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AssignModuleRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const AssignModuleRequest* internal_default_instance() {
    return reinterpret_cast<const AssignModuleRequest*>(
               &_AssignModuleRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(AssignModuleRequest& a, AssignModuleRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(AssignModuleRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AssignModuleRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AssignModuleRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AssignModuleRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AssignModuleRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AssignModuleRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AssignModuleRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.AssignModuleRequest";
  }
  protected:
  explicit AssignModuleRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModuleIdentityFieldNumber = 1,
  };
  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  bool has_module_identity() const;
  private:
  bool _internal_has_module_identity() const;
  public:
  void clear_module_identity();
  const ::carbon::frontend::module::ModuleIdentity& module_identity() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::ModuleIdentity* release_module_identity();
  ::carbon::frontend::module::ModuleIdentity* mutable_module_identity();
  void set_allocated_module_identity(::carbon::frontend::module::ModuleIdentity* module_identity);
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_module_identity() const;
  ::carbon::frontend::module::ModuleIdentity* _internal_mutable_module_identity();
  public:
  void unsafe_arena_set_allocated_module_identity(
      ::carbon::frontend::module::ModuleIdentity* module_identity);
  ::carbon::frontend::module::ModuleIdentity* unsafe_arena_release_module_identity();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.AssignModuleRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::module::ModuleIdentity* module_identity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class ClearModuleAssignmentRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.ClearModuleAssignmentRequest) */ {
 public:
  inline ClearModuleAssignmentRequest() : ClearModuleAssignmentRequest(nullptr) {}
  ~ClearModuleAssignmentRequest() override;
  explicit constexpr ClearModuleAssignmentRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ClearModuleAssignmentRequest(const ClearModuleAssignmentRequest& from);
  ClearModuleAssignmentRequest(ClearModuleAssignmentRequest&& from) noexcept
    : ClearModuleAssignmentRequest() {
    *this = ::std::move(from);
  }

  inline ClearModuleAssignmentRequest& operator=(const ClearModuleAssignmentRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ClearModuleAssignmentRequest& operator=(ClearModuleAssignmentRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ClearModuleAssignmentRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ClearModuleAssignmentRequest* internal_default_instance() {
    return reinterpret_cast<const ClearModuleAssignmentRequest*>(
               &_ClearModuleAssignmentRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ClearModuleAssignmentRequest& a, ClearModuleAssignmentRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ClearModuleAssignmentRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ClearModuleAssignmentRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ClearModuleAssignmentRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ClearModuleAssignmentRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ClearModuleAssignmentRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ClearModuleAssignmentRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ClearModuleAssignmentRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.ClearModuleAssignmentRequest";
  }
  protected:
  explicit ClearModuleAssignmentRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModuleIdentityFieldNumber = 1,
  };
  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  bool has_module_identity() const;
  private:
  bool _internal_has_module_identity() const;
  public:
  void clear_module_identity();
  const ::carbon::frontend::module::ModuleIdentity& module_identity() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::ModuleIdentity* release_module_identity();
  ::carbon::frontend::module::ModuleIdentity* mutable_module_identity();
  void set_allocated_module_identity(::carbon::frontend::module::ModuleIdentity* module_identity);
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_module_identity() const;
  ::carbon::frontend::module::ModuleIdentity* _internal_mutable_module_identity();
  public:
  void unsafe_arena_set_allocated_module_identity(
      ::carbon::frontend::module::ModuleIdentity* module_identity);
  ::carbon::frontend::module::ModuleIdentity* unsafe_arena_release_module_identity();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.ClearModuleAssignmentRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::module::ModuleIdentity* module_identity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class SetModuleSerialRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.SetModuleSerialRequest) */ {
 public:
  inline SetModuleSerialRequest() : SetModuleSerialRequest(nullptr) {}
  ~SetModuleSerialRequest() override;
  explicit constexpr SetModuleSerialRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetModuleSerialRequest(const SetModuleSerialRequest& from);
  SetModuleSerialRequest(SetModuleSerialRequest&& from) noexcept
    : SetModuleSerialRequest() {
    *this = ::std::move(from);
  }

  inline SetModuleSerialRequest& operator=(const SetModuleSerialRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetModuleSerialRequest& operator=(SetModuleSerialRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetModuleSerialRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetModuleSerialRequest* internal_default_instance() {
    return reinterpret_cast<const SetModuleSerialRequest*>(
               &_SetModuleSerialRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(SetModuleSerialRequest& a, SetModuleSerialRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetModuleSerialRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetModuleSerialRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetModuleSerialRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetModuleSerialRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetModuleSerialRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetModuleSerialRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetModuleSerialRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.SetModuleSerialRequest";
  }
  protected:
  explicit SetModuleSerialRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNewSerialFieldNumber = 2,
    kModuleIdentityFieldNumber = 1,
  };
  // string new_serial = 2;
  void clear_new_serial();
  const std::string& new_serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_new_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_new_serial();
  PROTOBUF_NODISCARD std::string* release_new_serial();
  void set_allocated_new_serial(std::string* new_serial);
  private:
  const std::string& _internal_new_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_new_serial(const std::string& value);
  std::string* _internal_mutable_new_serial();
  public:

  // .carbon.frontend.module.ModuleIdentity module_identity = 1;
  bool has_module_identity() const;
  private:
  bool _internal_has_module_identity() const;
  public:
  void clear_module_identity();
  const ::carbon::frontend::module::ModuleIdentity& module_identity() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::ModuleIdentity* release_module_identity();
  ::carbon::frontend::module::ModuleIdentity* mutable_module_identity();
  void set_allocated_module_identity(::carbon::frontend::module::ModuleIdentity* module_identity);
  private:
  const ::carbon::frontend::module::ModuleIdentity& _internal_module_identity() const;
  ::carbon::frontend::module::ModuleIdentity* _internal_mutable_module_identity();
  public:
  void unsafe_arena_set_allocated_module_identity(
      ::carbon::frontend::module::ModuleIdentity* module_identity);
  ::carbon::frontend::module::ModuleIdentity* unsafe_arena_release_module_identity();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.SetModuleSerialRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr new_serial_;
  ::carbon::frontend::module::ModuleIdentity* module_identity_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class ModuleDefinition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.ModuleDefinition) */ {
 public:
  inline ModuleDefinition() : ModuleDefinition(nullptr) {}
  ~ModuleDefinition() override;
  explicit constexpr ModuleDefinition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModuleDefinition(const ModuleDefinition& from);
  ModuleDefinition(ModuleDefinition&& from) noexcept
    : ModuleDefinition() {
    *this = ::std::move(from);
  }

  inline ModuleDefinition& operator=(const ModuleDefinition& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModuleDefinition& operator=(ModuleDefinition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModuleDefinition& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModuleDefinition* internal_default_instance() {
    return reinterpret_cast<const ModuleDefinition*>(
               &_ModuleDefinition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(ModuleDefinition& a, ModuleDefinition& b) {
    a.Swap(&b);
  }
  inline void Swap(ModuleDefinition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModuleDefinition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModuleDefinition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModuleDefinition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModuleDefinition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModuleDefinition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModuleDefinition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.ModuleDefinition";
  }
  protected:
  explicit ModuleDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModuleIdFieldNumber = 1,
    kModuleSpacingMmFieldNumber = 2,
    kDisabledFieldNumber = 3,
  };
  // uint32 module_id = 1;
  void clear_module_id();
  uint32_t module_id() const;
  void set_module_id(uint32_t value);
  private:
  uint32_t _internal_module_id() const;
  void _internal_set_module_id(uint32_t value);
  public:

  // float module_spacing_mm = 2;
  void clear_module_spacing_mm();
  float module_spacing_mm() const;
  void set_module_spacing_mm(float value);
  private:
  float _internal_module_spacing_mm() const;
  void _internal_set_module_spacing_mm(float value);
  public:

  // bool disabled = 3;
  void clear_disabled();
  bool disabled() const;
  void set_disabled(bool value);
  private:
  bool _internal_disabled() const;
  void _internal_set_disabled(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.ModuleDefinition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t module_id_;
  float module_spacing_mm_;
  bool disabled_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class RowDefinition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.RowDefinition) */ {
 public:
  inline RowDefinition() : RowDefinition(nullptr) {}
  ~RowDefinition() override;
  explicit constexpr RowDefinition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RowDefinition(const RowDefinition& from);
  RowDefinition(RowDefinition&& from) noexcept
    : RowDefinition() {
    *this = ::std::move(from);
  }

  inline RowDefinition& operator=(const RowDefinition& from) {
    CopyFrom(from);
    return *this;
  }
  inline RowDefinition& operator=(RowDefinition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RowDefinition& default_instance() {
    return *internal_default_instance();
  }
  static inline const RowDefinition* internal_default_instance() {
    return reinterpret_cast<const RowDefinition*>(
               &_RowDefinition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(RowDefinition& a, RowDefinition& b) {
    a.Swap(&b);
  }
  inline void Swap(RowDefinition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RowDefinition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RowDefinition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RowDefinition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RowDefinition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RowDefinition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RowDefinition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.RowDefinition";
  }
  protected:
  explicit RowDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kModulesFieldNumber = 2,
    kRowIdFieldNumber = 1,
    kRowSpacingMmFieldNumber = 3,
  };
  // repeated .carbon.frontend.module.ModuleDefinition modules = 2;
  int modules_size() const;
  private:
  int _internal_modules_size() const;
  public:
  void clear_modules();
  ::carbon::frontend::module::ModuleDefinition* mutable_modules(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleDefinition >*
      mutable_modules();
  private:
  const ::carbon::frontend::module::ModuleDefinition& _internal_modules(int index) const;
  ::carbon::frontend::module::ModuleDefinition* _internal_add_modules();
  public:
  const ::carbon::frontend::module::ModuleDefinition& modules(int index) const;
  ::carbon::frontend::module::ModuleDefinition* add_modules();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleDefinition >&
      modules() const;

  // uint32 row_id = 1;
  void clear_row_id();
  uint32_t row_id() const;
  void set_row_id(uint32_t value);
  private:
  uint32_t _internal_row_id() const;
  void _internal_set_row_id(uint32_t value);
  public:

  // float row_spacing_mm = 3;
  void clear_row_spacing_mm();
  float row_spacing_mm() const;
  void set_row_spacing_mm(float value);
  private:
  float _internal_row_spacing_mm() const;
  void _internal_set_row_spacing_mm(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.RowDefinition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleDefinition > modules_;
  uint32_t row_id_;
  float row_spacing_mm_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class BarDefinition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.BarDefinition) */ {
 public:
  inline BarDefinition() : BarDefinition(nullptr) {}
  ~BarDefinition() override;
  explicit constexpr BarDefinition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  BarDefinition(const BarDefinition& from);
  BarDefinition(BarDefinition&& from) noexcept
    : BarDefinition() {
    *this = ::std::move(from);
  }

  inline BarDefinition& operator=(const BarDefinition& from) {
    CopyFrom(from);
    return *this;
  }
  inline BarDefinition& operator=(BarDefinition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const BarDefinition& default_instance() {
    return *internal_default_instance();
  }
  static inline const BarDefinition* internal_default_instance() {
    return reinterpret_cast<const BarDefinition*>(
               &_BarDefinition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(BarDefinition& a, BarDefinition& b) {
    a.Swap(&b);
  }
  inline void Swap(BarDefinition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(BarDefinition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  BarDefinition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<BarDefinition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const BarDefinition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const BarDefinition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(BarDefinition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.BarDefinition";
  }
  protected:
  explicit BarDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kBarLengthMmFieldNumber = 1,
    kFoldingFieldNumber = 2,
  };
  // uint32 bar_length_mm = 1;
  void clear_bar_length_mm();
  uint32_t bar_length_mm() const;
  void set_bar_length_mm(uint32_t value);
  private:
  uint32_t _internal_bar_length_mm() const;
  void _internal_set_bar_length_mm(uint32_t value);
  public:

  // bool folding = 2;
  void clear_folding();
  bool folding() const;
  void set_folding(bool value);
  private:
  bool _internal_folding() const;
  void _internal_set_folding(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.BarDefinition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t bar_length_mm_;
  bool folding_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class RobotDefinition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.RobotDefinition) */ {
 public:
  inline RobotDefinition() : RobotDefinition(nullptr) {}
  ~RobotDefinition() override;
  explicit constexpr RobotDefinition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RobotDefinition(const RobotDefinition& from);
  RobotDefinition(RobotDefinition&& from) noexcept
    : RobotDefinition() {
    *this = ::std::move(from);
  }

  inline RobotDefinition& operator=(const RobotDefinition& from) {
    CopyFrom(from);
    return *this;
  }
  inline RobotDefinition& operator=(RobotDefinition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RobotDefinition& default_instance() {
    return *internal_default_instance();
  }
  static inline const RobotDefinition* internal_default_instance() {
    return reinterpret_cast<const RobotDefinition*>(
               &_RobotDefinition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(RobotDefinition& a, RobotDefinition& b) {
    a.Swap(&b);
  }
  inline void Swap(RobotDefinition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RobotDefinition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RobotDefinition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RobotDefinition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RobotDefinition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RobotDefinition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RobotDefinition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.RobotDefinition";
  }
  protected:
  explicit RobotDefinition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRowsFieldNumber = 1,
    kBarDefinitionFieldNumber = 3,
  };
  // repeated .carbon.frontend.module.RowDefinition rows = 1;
  int rows_size() const;
  private:
  int _internal_rows_size() const;
  public:
  void clear_rows();
  ::carbon::frontend::module::RowDefinition* mutable_rows(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::RowDefinition >*
      mutable_rows();
  private:
  const ::carbon::frontend::module::RowDefinition& _internal_rows(int index) const;
  ::carbon::frontend::module::RowDefinition* _internal_add_rows();
  public:
  const ::carbon::frontend::module::RowDefinition& rows(int index) const;
  ::carbon::frontend::module::RowDefinition* add_rows();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::RowDefinition >&
      rows() const;

  // .carbon.frontend.module.BarDefinition bar_definition = 3;
  bool has_bar_definition() const;
  private:
  bool _internal_has_bar_definition() const;
  public:
  void clear_bar_definition();
  const ::carbon::frontend::module::BarDefinition& bar_definition() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::BarDefinition* release_bar_definition();
  ::carbon::frontend::module::BarDefinition* mutable_bar_definition();
  void set_allocated_bar_definition(::carbon::frontend::module::BarDefinition* bar_definition);
  private:
  const ::carbon::frontend::module::BarDefinition& _internal_bar_definition() const;
  ::carbon::frontend::module::BarDefinition* _internal_mutable_bar_definition();
  public:
  void unsafe_arena_set_allocated_bar_definition(
      ::carbon::frontend::module::BarDefinition* bar_definition);
  ::carbon::frontend::module::BarDefinition* unsafe_arena_release_bar_definition();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.RobotDefinition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::RowDefinition > rows_;
  ::carbon::frontend::module::BarDefinition* bar_definition_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class Preset final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.Preset) */ {
 public:
  inline Preset() : Preset(nullptr) {}
  ~Preset() override;
  explicit constexpr Preset(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  Preset(const Preset& from);
  Preset(Preset&& from) noexcept
    : Preset() {
    *this = ::std::move(from);
  }

  inline Preset& operator=(const Preset& from) {
    CopyFrom(from);
    return *this;
  }
  inline Preset& operator=(Preset&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const Preset& default_instance() {
    return *internal_default_instance();
  }
  static inline const Preset* internal_default_instance() {
    return reinterpret_cast<const Preset*>(
               &_Preset_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(Preset& a, Preset& b) {
    a.Swap(&b);
  }
  inline void Swap(Preset* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(Preset* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  Preset* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<Preset>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const Preset& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const Preset& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(Preset* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.Preset";
  }
  protected:
  explicit Preset(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
    kDisplayNameFieldNumber = 2,
    kDefinitionFieldNumber = 3,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // string display_name = 2;
  void clear_display_name();
  const std::string& display_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_display_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_display_name();
  PROTOBUF_NODISCARD std::string* release_display_name();
  void set_allocated_display_name(std::string* display_name);
  private:
  const std::string& _internal_display_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_display_name(const std::string& value);
  std::string* _internal_mutable_display_name();
  public:

  // .carbon.frontend.module.RobotDefinition definition = 3;
  bool has_definition() const;
  private:
  bool _internal_has_definition() const;
  public:
  void clear_definition();
  const ::carbon::frontend::module::RobotDefinition& definition() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::RobotDefinition* release_definition();
  ::carbon::frontend::module::RobotDefinition* mutable_definition();
  void set_allocated_definition(::carbon::frontend::module::RobotDefinition* definition);
  private:
  const ::carbon::frontend::module::RobotDefinition& _internal_definition() const;
  ::carbon::frontend::module::RobotDefinition* _internal_mutable_definition();
  public:
  void unsafe_arena_set_allocated_definition(
      ::carbon::frontend::module::RobotDefinition* definition);
  ::carbon::frontend::module::RobotDefinition* unsafe_arena_release_definition();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.Preset)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr display_name_;
  ::carbon::frontend::module::RobotDefinition* definition_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class GetPresetsListRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.GetPresetsListRequest) */ {
 public:
  inline GetPresetsListRequest() : GetPresetsListRequest(nullptr) {}
  ~GetPresetsListRequest() override;
  explicit constexpr GetPresetsListRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPresetsListRequest(const GetPresetsListRequest& from);
  GetPresetsListRequest(GetPresetsListRequest&& from) noexcept
    : GetPresetsListRequest() {
    *this = ::std::move(from);
  }

  inline GetPresetsListRequest& operator=(const GetPresetsListRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPresetsListRequest& operator=(GetPresetsListRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPresetsListRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPresetsListRequest* internal_default_instance() {
    return reinterpret_cast<const GetPresetsListRequest*>(
               &_GetPresetsListRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(GetPresetsListRequest& a, GetPresetsListRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPresetsListRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPresetsListRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPresetsListRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPresetsListRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPresetsListRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPresetsListRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPresetsListRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.GetPresetsListRequest";
  }
  protected:
  explicit GetPresetsListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLanguageFieldNumber = 1,
  };
  // string language = 1;
  void clear_language();
  const std::string& language() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_language(ArgT0&& arg0, ArgT... args);
  std::string* mutable_language();
  PROTOBUF_NODISCARD std::string* release_language();
  void set_allocated_language(std::string* language);
  private:
  const std::string& _internal_language() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_language(const std::string& value);
  std::string* _internal_mutable_language();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.GetPresetsListRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr language_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class GetPresetsListResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.GetPresetsListResponse) */ {
 public:
  inline GetPresetsListResponse() : GetPresetsListResponse(nullptr) {}
  ~GetPresetsListResponse() override;
  explicit constexpr GetPresetsListResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetPresetsListResponse(const GetPresetsListResponse& from);
  GetPresetsListResponse(GetPresetsListResponse&& from) noexcept
    : GetPresetsListResponse() {
    *this = ::std::move(from);
  }

  inline GetPresetsListResponse& operator=(const GetPresetsListResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetPresetsListResponse& operator=(GetPresetsListResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetPresetsListResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetPresetsListResponse* internal_default_instance() {
    return reinterpret_cast<const GetPresetsListResponse*>(
               &_GetPresetsListResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(GetPresetsListResponse& a, GetPresetsListResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetPresetsListResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetPresetsListResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetPresetsListResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetPresetsListResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetPresetsListResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetPresetsListResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetPresetsListResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.GetPresetsListResponse";
  }
  protected:
  explicit GetPresetsListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPresetsFieldNumber = 1,
  };
  // repeated .carbon.frontend.module.Preset presets = 1;
  int presets_size() const;
  private:
  int _internal_presets_size() const;
  public:
  void clear_presets();
  ::carbon::frontend::module::Preset* mutable_presets(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::Preset >*
      mutable_presets();
  private:
  const ::carbon::frontend::module::Preset& _internal_presets(int index) const;
  ::carbon::frontend::module::Preset* _internal_add_presets();
  public:
  const ::carbon::frontend::module::Preset& presets(int index) const;
  ::carbon::frontend::module::Preset* add_presets();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::Preset >&
      presets() const;

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.GetPresetsListResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::Preset > presets_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class GetCurrentRobotDefinitionResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.GetCurrentRobotDefinitionResponse) */ {
 public:
  inline GetCurrentRobotDefinitionResponse() : GetCurrentRobotDefinitionResponse(nullptr) {}
  ~GetCurrentRobotDefinitionResponse() override;
  explicit constexpr GetCurrentRobotDefinitionResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetCurrentRobotDefinitionResponse(const GetCurrentRobotDefinitionResponse& from);
  GetCurrentRobotDefinitionResponse(GetCurrentRobotDefinitionResponse&& from) noexcept
    : GetCurrentRobotDefinitionResponse() {
    *this = ::std::move(from);
  }

  inline GetCurrentRobotDefinitionResponse& operator=(const GetCurrentRobotDefinitionResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetCurrentRobotDefinitionResponse& operator=(GetCurrentRobotDefinitionResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetCurrentRobotDefinitionResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetCurrentRobotDefinitionResponse* internal_default_instance() {
    return reinterpret_cast<const GetCurrentRobotDefinitionResponse*>(
               &_GetCurrentRobotDefinitionResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(GetCurrentRobotDefinitionResponse& a, GetCurrentRobotDefinitionResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetCurrentRobotDefinitionResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetCurrentRobotDefinitionResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetCurrentRobotDefinitionResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetCurrentRobotDefinitionResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetCurrentRobotDefinitionResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetCurrentRobotDefinitionResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetCurrentRobotDefinitionResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.GetCurrentRobotDefinitionResponse";
  }
  protected:
  explicit GetCurrentRobotDefinitionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCurrentDefinitionFieldNumber = 1,
  };
  // optional .carbon.frontend.module.RobotDefinition current_definition = 1;
  bool has_current_definition() const;
  private:
  bool _internal_has_current_definition() const;
  public:
  void clear_current_definition();
  const ::carbon::frontend::module::RobotDefinition& current_definition() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::RobotDefinition* release_current_definition();
  ::carbon::frontend::module::RobotDefinition* mutable_current_definition();
  void set_allocated_current_definition(::carbon::frontend::module::RobotDefinition* current_definition);
  private:
  const ::carbon::frontend::module::RobotDefinition& _internal_current_definition() const;
  ::carbon::frontend::module::RobotDefinition* _internal_mutable_current_definition();
  public:
  void unsafe_arena_set_allocated_current_definition(
      ::carbon::frontend::module::RobotDefinition* current_definition);
  ::carbon::frontend::module::RobotDefinition* unsafe_arena_release_current_definition();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.GetCurrentRobotDefinitionResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::HasBits<1> _has_bits_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  ::carbon::frontend::module::RobotDefinition* current_definition_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// -------------------------------------------------------------------

class SetCurrentRobotDefinitionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.module.SetCurrentRobotDefinitionRequest) */ {
 public:
  inline SetCurrentRobotDefinitionRequest() : SetCurrentRobotDefinitionRequest(nullptr) {}
  ~SetCurrentRobotDefinitionRequest() override;
  explicit constexpr SetCurrentRobotDefinitionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetCurrentRobotDefinitionRequest(const SetCurrentRobotDefinitionRequest& from);
  SetCurrentRobotDefinitionRequest(SetCurrentRobotDefinitionRequest&& from) noexcept
    : SetCurrentRobotDefinitionRequest() {
    *this = ::std::move(from);
  }

  inline SetCurrentRobotDefinitionRequest& operator=(const SetCurrentRobotDefinitionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetCurrentRobotDefinitionRequest& operator=(SetCurrentRobotDefinitionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetCurrentRobotDefinitionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetCurrentRobotDefinitionRequest* internal_default_instance() {
    return reinterpret_cast<const SetCurrentRobotDefinitionRequest*>(
               &_SetCurrentRobotDefinitionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(SetCurrentRobotDefinitionRequest& a, SetCurrentRobotDefinitionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetCurrentRobotDefinitionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetCurrentRobotDefinitionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetCurrentRobotDefinitionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetCurrentRobotDefinitionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetCurrentRobotDefinitionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetCurrentRobotDefinitionRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetCurrentRobotDefinitionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.module.SetCurrentRobotDefinitionRequest";
  }
  protected:
  explicit SetCurrentRobotDefinitionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCurrentDefinitionFieldNumber = 1,
  };
  // .carbon.frontend.module.RobotDefinition current_definition = 1;
  bool has_current_definition() const;
  private:
  bool _internal_has_current_definition() const;
  public:
  void clear_current_definition();
  const ::carbon::frontend::module::RobotDefinition& current_definition() const;
  PROTOBUF_NODISCARD ::carbon::frontend::module::RobotDefinition* release_current_definition();
  ::carbon::frontend::module::RobotDefinition* mutable_current_definition();
  void set_allocated_current_definition(::carbon::frontend::module::RobotDefinition* current_definition);
  private:
  const ::carbon::frontend::module::RobotDefinition& _internal_current_definition() const;
  ::carbon::frontend::module::RobotDefinition* _internal_mutable_current_definition();
  public:
  void unsafe_arena_set_allocated_current_definition(
      ::carbon::frontend::module::RobotDefinition* current_definition);
  ::carbon::frontend::module::RobotDefinition* unsafe_arena_release_current_definition();

  // @@protoc_insertion_point(class_scope:carbon.frontend.module.SetCurrentRobotDefinitionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::module::RobotDefinition* current_definition_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fmodule_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// ModuleIdentity

// uint32 id = 1;
inline void ModuleIdentity::clear_id() {
  id_ = 0u;
}
inline uint32_t ModuleIdentity::_internal_id() const {
  return id_;
}
inline uint32_t ModuleIdentity::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.ModuleIdentity.id)
  return _internal_id();
}
inline void ModuleIdentity::_internal_set_id(uint32_t value) {
  
  id_ = value;
}
inline void ModuleIdentity::set_id(uint32_t value) {
  _internal_set_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.module.ModuleIdentity.id)
}

// string serial = 2;
inline void ModuleIdentity::clear_serial() {
  serial_.ClearToEmpty();
}
inline const std::string& ModuleIdentity::serial() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.ModuleIdentity.serial)
  return _internal_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModuleIdentity::set_serial(ArgT0&& arg0, ArgT... args) {
 
 serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.module.ModuleIdentity.serial)
}
inline std::string* ModuleIdentity::mutable_serial() {
  std::string* _s = _internal_mutable_serial();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.ModuleIdentity.serial)
  return _s;
}
inline const std::string& ModuleIdentity::_internal_serial() const {
  return serial_.Get();
}
inline void ModuleIdentity::_internal_set_serial(const std::string& value) {
  
  serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModuleIdentity::_internal_mutable_serial() {
  
  return serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModuleIdentity::release_serial() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.ModuleIdentity.serial)
  return serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModuleIdentity::set_allocated_serial(std::string* serial) {
  if (serial != nullptr) {
    
  } else {
    
  }
  serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.ModuleIdentity.serial)
}

// -------------------------------------------------------------------

// GetNextModulesListRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextModulesListRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextModulesListRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextModulesListRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextModulesListRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetNextModulesListRequest.ts)
  return _internal_ts();
}
inline void GetNextModulesListRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.GetNextModulesListRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextModulesListRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModulesListRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.GetNextModulesListRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModulesListRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextModulesListRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetNextModulesListRequest.ts)
  return _msg;
}
inline void GetNextModulesListRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.GetNextModulesListRequest.ts)
}

// -------------------------------------------------------------------

// GetNextModulesListResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextModulesListResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextModulesListResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextModulesListResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextModulesListResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetNextModulesListResponse.ts)
  return _internal_ts();
}
inline void GetNextModulesListResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.GetNextModulesListResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextModulesListResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModulesListResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.GetNextModulesListResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextModulesListResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextModulesListResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetNextModulesListResponse.ts)
  return _msg;
}
inline void GetNextModulesListResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.GetNextModulesListResponse.ts)
}

// repeated .carbon.frontend.module.ModuleIdentity assigned_modules = 2;
inline int GetNextModulesListResponse::_internal_assigned_modules_size() const {
  return assigned_modules_.size();
}
inline int GetNextModulesListResponse::assigned_modules_size() const {
  return _internal_assigned_modules_size();
}
inline void GetNextModulesListResponse::clear_assigned_modules() {
  assigned_modules_.Clear();
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::mutable_assigned_modules(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetNextModulesListResponse.assigned_modules)
  return assigned_modules_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
GetNextModulesListResponse::mutable_assigned_modules() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.module.GetNextModulesListResponse.assigned_modules)
  return &assigned_modules_;
}
inline const ::carbon::frontend::module::ModuleIdentity& GetNextModulesListResponse::_internal_assigned_modules(int index) const {
  return assigned_modules_.Get(index);
}
inline const ::carbon::frontend::module::ModuleIdentity& GetNextModulesListResponse::assigned_modules(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetNextModulesListResponse.assigned_modules)
  return _internal_assigned_modules(index);
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::_internal_add_assigned_modules() {
  return assigned_modules_.Add();
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::add_assigned_modules() {
  ::carbon::frontend::module::ModuleIdentity* _add = _internal_add_assigned_modules();
  // @@protoc_insertion_point(field_add:carbon.frontend.module.GetNextModulesListResponse.assigned_modules)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
GetNextModulesListResponse::assigned_modules() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.module.GetNextModulesListResponse.assigned_modules)
  return assigned_modules_;
}

// repeated .carbon.frontend.module.ModuleIdentity unassigned_modules = 3;
inline int GetNextModulesListResponse::_internal_unassigned_modules_size() const {
  return unassigned_modules_.size();
}
inline int GetNextModulesListResponse::unassigned_modules_size() const {
  return _internal_unassigned_modules_size();
}
inline void GetNextModulesListResponse::clear_unassigned_modules() {
  unassigned_modules_.Clear();
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::mutable_unassigned_modules(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetNextModulesListResponse.unassigned_modules)
  return unassigned_modules_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
GetNextModulesListResponse::mutable_unassigned_modules() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.module.GetNextModulesListResponse.unassigned_modules)
  return &unassigned_modules_;
}
inline const ::carbon::frontend::module::ModuleIdentity& GetNextModulesListResponse::_internal_unassigned_modules(int index) const {
  return unassigned_modules_.Get(index);
}
inline const ::carbon::frontend::module::ModuleIdentity& GetNextModulesListResponse::unassigned_modules(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetNextModulesListResponse.unassigned_modules)
  return _internal_unassigned_modules(index);
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::_internal_add_unassigned_modules() {
  return unassigned_modules_.Add();
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::add_unassigned_modules() {
  ::carbon::frontend::module::ModuleIdentity* _add = _internal_add_unassigned_modules();
  // @@protoc_insertion_point(field_add:carbon.frontend.module.GetNextModulesListResponse.unassigned_modules)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
GetNextModulesListResponse::unassigned_modules() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.module.GetNextModulesListResponse.unassigned_modules)
  return unassigned_modules_;
}

// repeated .carbon.frontend.module.ModuleIdentity unset_serial_modules = 4;
inline int GetNextModulesListResponse::_internal_unset_serial_modules_size() const {
  return unset_serial_modules_.size();
}
inline int GetNextModulesListResponse::unset_serial_modules_size() const {
  return _internal_unset_serial_modules_size();
}
inline void GetNextModulesListResponse::clear_unset_serial_modules() {
  unset_serial_modules_.Clear();
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::mutable_unset_serial_modules(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetNextModulesListResponse.unset_serial_modules)
  return unset_serial_modules_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
GetNextModulesListResponse::mutable_unset_serial_modules() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.module.GetNextModulesListResponse.unset_serial_modules)
  return &unset_serial_modules_;
}
inline const ::carbon::frontend::module::ModuleIdentity& GetNextModulesListResponse::_internal_unset_serial_modules(int index) const {
  return unset_serial_modules_.Get(index);
}
inline const ::carbon::frontend::module::ModuleIdentity& GetNextModulesListResponse::unset_serial_modules(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetNextModulesListResponse.unset_serial_modules)
  return _internal_unset_serial_modules(index);
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::_internal_add_unset_serial_modules() {
  return unset_serial_modules_.Add();
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextModulesListResponse::add_unset_serial_modules() {
  ::carbon::frontend::module::ModuleIdentity* _add = _internal_add_unset_serial_modules();
  // @@protoc_insertion_point(field_add:carbon.frontend.module.GetNextModulesListResponse.unset_serial_modules)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
GetNextModulesListResponse::unset_serial_modules() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.module.GetNextModulesListResponse.unset_serial_modules)
  return unset_serial_modules_;
}

// -------------------------------------------------------------------

// GetNextActiveModulesRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextActiveModulesRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextActiveModulesRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveModulesRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveModulesRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetNextActiveModulesRequest.ts)
  return _internal_ts();
}
inline void GetNextActiveModulesRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.GetNextActiveModulesRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveModulesRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveModulesRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.GetNextActiveModulesRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveModulesRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveModulesRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetNextActiveModulesRequest.ts)
  return _msg;
}
inline void GetNextActiveModulesRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.GetNextActiveModulesRequest.ts)
}

// -------------------------------------------------------------------

// GetNextActiveModulesResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextActiveModulesResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextActiveModulesResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveModulesResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveModulesResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetNextActiveModulesResponse.ts)
  return _internal_ts();
}
inline void GetNextActiveModulesResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.GetNextActiveModulesResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveModulesResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveModulesResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.GetNextActiveModulesResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveModulesResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveModulesResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetNextActiveModulesResponse.ts)
  return _msg;
}
inline void GetNextActiveModulesResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.GetNextActiveModulesResponse.ts)
}

// repeated .carbon.frontend.module.ModuleIdentity active_modules = 2;
inline int GetNextActiveModulesResponse::_internal_active_modules_size() const {
  return active_modules_.size();
}
inline int GetNextActiveModulesResponse::active_modules_size() const {
  return _internal_active_modules_size();
}
inline void GetNextActiveModulesResponse::clear_active_modules() {
  active_modules_.Clear();
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextActiveModulesResponse::mutable_active_modules(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetNextActiveModulesResponse.active_modules)
  return active_modules_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >*
GetNextActiveModulesResponse::mutable_active_modules() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.module.GetNextActiveModulesResponse.active_modules)
  return &active_modules_;
}
inline const ::carbon::frontend::module::ModuleIdentity& GetNextActiveModulesResponse::_internal_active_modules(int index) const {
  return active_modules_.Get(index);
}
inline const ::carbon::frontend::module::ModuleIdentity& GetNextActiveModulesResponse::active_modules(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetNextActiveModulesResponse.active_modules)
  return _internal_active_modules(index);
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextActiveModulesResponse::_internal_add_active_modules() {
  return active_modules_.Add();
}
inline ::carbon::frontend::module::ModuleIdentity* GetNextActiveModulesResponse::add_active_modules() {
  ::carbon::frontend::module::ModuleIdentity* _add = _internal_add_active_modules();
  // @@protoc_insertion_point(field_add:carbon.frontend.module.GetNextActiveModulesResponse.active_modules)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleIdentity >&
GetNextActiveModulesResponse::active_modules() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.module.GetNextActiveModulesResponse.active_modules)
  return active_modules_;
}

// -------------------------------------------------------------------

// IdentifyModuleRequest

// .carbon.frontend.module.ModuleIdentity module_identity = 1;
inline bool IdentifyModuleRequest::_internal_has_module_identity() const {
  return this != internal_default_instance() && module_identity_ != nullptr;
}
inline bool IdentifyModuleRequest::has_module_identity() const {
  return _internal_has_module_identity();
}
inline void IdentifyModuleRequest::clear_module_identity() {
  if (GetArenaForAllocation() == nullptr && module_identity_ != nullptr) {
    delete module_identity_;
  }
  module_identity_ = nullptr;
}
inline const ::carbon::frontend::module::ModuleIdentity& IdentifyModuleRequest::_internal_module_identity() const {
  const ::carbon::frontend::module::ModuleIdentity* p = module_identity_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::ModuleIdentity&>(
      ::carbon::frontend::module::_ModuleIdentity_default_instance_);
}
inline const ::carbon::frontend::module::ModuleIdentity& IdentifyModuleRequest::module_identity() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.IdentifyModuleRequest.module_identity)
  return _internal_module_identity();
}
inline void IdentifyModuleRequest::unsafe_arena_set_allocated_module_identity(
    ::carbon::frontend::module::ModuleIdentity* module_identity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(module_identity_);
  }
  module_identity_ = module_identity;
  if (module_identity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.IdentifyModuleRequest.module_identity)
}
inline ::carbon::frontend::module::ModuleIdentity* IdentifyModuleRequest::release_module_identity() {
  
  ::carbon::frontend::module::ModuleIdentity* temp = module_identity_;
  module_identity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::ModuleIdentity* IdentifyModuleRequest::unsafe_arena_release_module_identity() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.IdentifyModuleRequest.module_identity)
  
  ::carbon::frontend::module::ModuleIdentity* temp = module_identity_;
  module_identity_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::ModuleIdentity* IdentifyModuleRequest::_internal_mutable_module_identity() {
  
  if (module_identity_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::ModuleIdentity>(GetArenaForAllocation());
    module_identity_ = p;
  }
  return module_identity_;
}
inline ::carbon::frontend::module::ModuleIdentity* IdentifyModuleRequest::mutable_module_identity() {
  ::carbon::frontend::module::ModuleIdentity* _msg = _internal_mutable_module_identity();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.IdentifyModuleRequest.module_identity)
  return _msg;
}
inline void IdentifyModuleRequest::set_allocated_module_identity(::carbon::frontend::module::ModuleIdentity* module_identity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete module_identity_;
  }
  if (module_identity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::module::ModuleIdentity>::GetOwningArena(module_identity);
    if (message_arena != submessage_arena) {
      module_identity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, module_identity, submessage_arena);
    }
    
  } else {
    
  }
  module_identity_ = module_identity;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.IdentifyModuleRequest.module_identity)
}

// -------------------------------------------------------------------

// AssignModuleRequest

// .carbon.frontend.module.ModuleIdentity module_identity = 1;
inline bool AssignModuleRequest::_internal_has_module_identity() const {
  return this != internal_default_instance() && module_identity_ != nullptr;
}
inline bool AssignModuleRequest::has_module_identity() const {
  return _internal_has_module_identity();
}
inline void AssignModuleRequest::clear_module_identity() {
  if (GetArenaForAllocation() == nullptr && module_identity_ != nullptr) {
    delete module_identity_;
  }
  module_identity_ = nullptr;
}
inline const ::carbon::frontend::module::ModuleIdentity& AssignModuleRequest::_internal_module_identity() const {
  const ::carbon::frontend::module::ModuleIdentity* p = module_identity_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::ModuleIdentity&>(
      ::carbon::frontend::module::_ModuleIdentity_default_instance_);
}
inline const ::carbon::frontend::module::ModuleIdentity& AssignModuleRequest::module_identity() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.AssignModuleRequest.module_identity)
  return _internal_module_identity();
}
inline void AssignModuleRequest::unsafe_arena_set_allocated_module_identity(
    ::carbon::frontend::module::ModuleIdentity* module_identity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(module_identity_);
  }
  module_identity_ = module_identity;
  if (module_identity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.AssignModuleRequest.module_identity)
}
inline ::carbon::frontend::module::ModuleIdentity* AssignModuleRequest::release_module_identity() {
  
  ::carbon::frontend::module::ModuleIdentity* temp = module_identity_;
  module_identity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::ModuleIdentity* AssignModuleRequest::unsafe_arena_release_module_identity() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.AssignModuleRequest.module_identity)
  
  ::carbon::frontend::module::ModuleIdentity* temp = module_identity_;
  module_identity_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::ModuleIdentity* AssignModuleRequest::_internal_mutable_module_identity() {
  
  if (module_identity_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::ModuleIdentity>(GetArenaForAllocation());
    module_identity_ = p;
  }
  return module_identity_;
}
inline ::carbon::frontend::module::ModuleIdentity* AssignModuleRequest::mutable_module_identity() {
  ::carbon::frontend::module::ModuleIdentity* _msg = _internal_mutable_module_identity();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.AssignModuleRequest.module_identity)
  return _msg;
}
inline void AssignModuleRequest::set_allocated_module_identity(::carbon::frontend::module::ModuleIdentity* module_identity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete module_identity_;
  }
  if (module_identity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::module::ModuleIdentity>::GetOwningArena(module_identity);
    if (message_arena != submessage_arena) {
      module_identity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, module_identity, submessage_arena);
    }
    
  } else {
    
  }
  module_identity_ = module_identity;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.AssignModuleRequest.module_identity)
}

// -------------------------------------------------------------------

// ClearModuleAssignmentRequest

// .carbon.frontend.module.ModuleIdentity module_identity = 1;
inline bool ClearModuleAssignmentRequest::_internal_has_module_identity() const {
  return this != internal_default_instance() && module_identity_ != nullptr;
}
inline bool ClearModuleAssignmentRequest::has_module_identity() const {
  return _internal_has_module_identity();
}
inline void ClearModuleAssignmentRequest::clear_module_identity() {
  if (GetArenaForAllocation() == nullptr && module_identity_ != nullptr) {
    delete module_identity_;
  }
  module_identity_ = nullptr;
}
inline const ::carbon::frontend::module::ModuleIdentity& ClearModuleAssignmentRequest::_internal_module_identity() const {
  const ::carbon::frontend::module::ModuleIdentity* p = module_identity_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::ModuleIdentity&>(
      ::carbon::frontend::module::_ModuleIdentity_default_instance_);
}
inline const ::carbon::frontend::module::ModuleIdentity& ClearModuleAssignmentRequest::module_identity() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.ClearModuleAssignmentRequest.module_identity)
  return _internal_module_identity();
}
inline void ClearModuleAssignmentRequest::unsafe_arena_set_allocated_module_identity(
    ::carbon::frontend::module::ModuleIdentity* module_identity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(module_identity_);
  }
  module_identity_ = module_identity;
  if (module_identity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.ClearModuleAssignmentRequest.module_identity)
}
inline ::carbon::frontend::module::ModuleIdentity* ClearModuleAssignmentRequest::release_module_identity() {
  
  ::carbon::frontend::module::ModuleIdentity* temp = module_identity_;
  module_identity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::ModuleIdentity* ClearModuleAssignmentRequest::unsafe_arena_release_module_identity() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.ClearModuleAssignmentRequest.module_identity)
  
  ::carbon::frontend::module::ModuleIdentity* temp = module_identity_;
  module_identity_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::ModuleIdentity* ClearModuleAssignmentRequest::_internal_mutable_module_identity() {
  
  if (module_identity_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::ModuleIdentity>(GetArenaForAllocation());
    module_identity_ = p;
  }
  return module_identity_;
}
inline ::carbon::frontend::module::ModuleIdentity* ClearModuleAssignmentRequest::mutable_module_identity() {
  ::carbon::frontend::module::ModuleIdentity* _msg = _internal_mutable_module_identity();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.ClearModuleAssignmentRequest.module_identity)
  return _msg;
}
inline void ClearModuleAssignmentRequest::set_allocated_module_identity(::carbon::frontend::module::ModuleIdentity* module_identity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete module_identity_;
  }
  if (module_identity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::module::ModuleIdentity>::GetOwningArena(module_identity);
    if (message_arena != submessage_arena) {
      module_identity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, module_identity, submessage_arena);
    }
    
  } else {
    
  }
  module_identity_ = module_identity;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.ClearModuleAssignmentRequest.module_identity)
}

// -------------------------------------------------------------------

// SetModuleSerialRequest

// .carbon.frontend.module.ModuleIdentity module_identity = 1;
inline bool SetModuleSerialRequest::_internal_has_module_identity() const {
  return this != internal_default_instance() && module_identity_ != nullptr;
}
inline bool SetModuleSerialRequest::has_module_identity() const {
  return _internal_has_module_identity();
}
inline void SetModuleSerialRequest::clear_module_identity() {
  if (GetArenaForAllocation() == nullptr && module_identity_ != nullptr) {
    delete module_identity_;
  }
  module_identity_ = nullptr;
}
inline const ::carbon::frontend::module::ModuleIdentity& SetModuleSerialRequest::_internal_module_identity() const {
  const ::carbon::frontend::module::ModuleIdentity* p = module_identity_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::ModuleIdentity&>(
      ::carbon::frontend::module::_ModuleIdentity_default_instance_);
}
inline const ::carbon::frontend::module::ModuleIdentity& SetModuleSerialRequest::module_identity() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.SetModuleSerialRequest.module_identity)
  return _internal_module_identity();
}
inline void SetModuleSerialRequest::unsafe_arena_set_allocated_module_identity(
    ::carbon::frontend::module::ModuleIdentity* module_identity) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(module_identity_);
  }
  module_identity_ = module_identity;
  if (module_identity) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.SetModuleSerialRequest.module_identity)
}
inline ::carbon::frontend::module::ModuleIdentity* SetModuleSerialRequest::release_module_identity() {
  
  ::carbon::frontend::module::ModuleIdentity* temp = module_identity_;
  module_identity_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::ModuleIdentity* SetModuleSerialRequest::unsafe_arena_release_module_identity() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.SetModuleSerialRequest.module_identity)
  
  ::carbon::frontend::module::ModuleIdentity* temp = module_identity_;
  module_identity_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::ModuleIdentity* SetModuleSerialRequest::_internal_mutable_module_identity() {
  
  if (module_identity_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::ModuleIdentity>(GetArenaForAllocation());
    module_identity_ = p;
  }
  return module_identity_;
}
inline ::carbon::frontend::module::ModuleIdentity* SetModuleSerialRequest::mutable_module_identity() {
  ::carbon::frontend::module::ModuleIdentity* _msg = _internal_mutable_module_identity();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.SetModuleSerialRequest.module_identity)
  return _msg;
}
inline void SetModuleSerialRequest::set_allocated_module_identity(::carbon::frontend::module::ModuleIdentity* module_identity) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete module_identity_;
  }
  if (module_identity) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::module::ModuleIdentity>::GetOwningArena(module_identity);
    if (message_arena != submessage_arena) {
      module_identity = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, module_identity, submessage_arena);
    }
    
  } else {
    
  }
  module_identity_ = module_identity;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.SetModuleSerialRequest.module_identity)
}

// string new_serial = 2;
inline void SetModuleSerialRequest::clear_new_serial() {
  new_serial_.ClearToEmpty();
}
inline const std::string& SetModuleSerialRequest::new_serial() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.SetModuleSerialRequest.new_serial)
  return _internal_new_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetModuleSerialRequest::set_new_serial(ArgT0&& arg0, ArgT... args) {
 
 new_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.module.SetModuleSerialRequest.new_serial)
}
inline std::string* SetModuleSerialRequest::mutable_new_serial() {
  std::string* _s = _internal_mutable_new_serial();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.SetModuleSerialRequest.new_serial)
  return _s;
}
inline const std::string& SetModuleSerialRequest::_internal_new_serial() const {
  return new_serial_.Get();
}
inline void SetModuleSerialRequest::_internal_set_new_serial(const std::string& value) {
  
  new_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetModuleSerialRequest::_internal_mutable_new_serial() {
  
  return new_serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetModuleSerialRequest::release_new_serial() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.SetModuleSerialRequest.new_serial)
  return new_serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetModuleSerialRequest::set_allocated_new_serial(std::string* new_serial) {
  if (new_serial != nullptr) {
    
  } else {
    
  }
  new_serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), new_serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (new_serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    new_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.SetModuleSerialRequest.new_serial)
}

// -------------------------------------------------------------------

// ModuleDefinition

// uint32 module_id = 1;
inline void ModuleDefinition::clear_module_id() {
  module_id_ = 0u;
}
inline uint32_t ModuleDefinition::_internal_module_id() const {
  return module_id_;
}
inline uint32_t ModuleDefinition::module_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.ModuleDefinition.module_id)
  return _internal_module_id();
}
inline void ModuleDefinition::_internal_set_module_id(uint32_t value) {
  
  module_id_ = value;
}
inline void ModuleDefinition::set_module_id(uint32_t value) {
  _internal_set_module_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.module.ModuleDefinition.module_id)
}

// float module_spacing_mm = 2;
inline void ModuleDefinition::clear_module_spacing_mm() {
  module_spacing_mm_ = 0;
}
inline float ModuleDefinition::_internal_module_spacing_mm() const {
  return module_spacing_mm_;
}
inline float ModuleDefinition::module_spacing_mm() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.ModuleDefinition.module_spacing_mm)
  return _internal_module_spacing_mm();
}
inline void ModuleDefinition::_internal_set_module_spacing_mm(float value) {
  
  module_spacing_mm_ = value;
}
inline void ModuleDefinition::set_module_spacing_mm(float value) {
  _internal_set_module_spacing_mm(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.module.ModuleDefinition.module_spacing_mm)
}

// bool disabled = 3;
inline void ModuleDefinition::clear_disabled() {
  disabled_ = false;
}
inline bool ModuleDefinition::_internal_disabled() const {
  return disabled_;
}
inline bool ModuleDefinition::disabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.ModuleDefinition.disabled)
  return _internal_disabled();
}
inline void ModuleDefinition::_internal_set_disabled(bool value) {
  
  disabled_ = value;
}
inline void ModuleDefinition::set_disabled(bool value) {
  _internal_set_disabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.module.ModuleDefinition.disabled)
}

// -------------------------------------------------------------------

// RowDefinition

// uint32 row_id = 1;
inline void RowDefinition::clear_row_id() {
  row_id_ = 0u;
}
inline uint32_t RowDefinition::_internal_row_id() const {
  return row_id_;
}
inline uint32_t RowDefinition::row_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.RowDefinition.row_id)
  return _internal_row_id();
}
inline void RowDefinition::_internal_set_row_id(uint32_t value) {
  
  row_id_ = value;
}
inline void RowDefinition::set_row_id(uint32_t value) {
  _internal_set_row_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.module.RowDefinition.row_id)
}

// repeated .carbon.frontend.module.ModuleDefinition modules = 2;
inline int RowDefinition::_internal_modules_size() const {
  return modules_.size();
}
inline int RowDefinition::modules_size() const {
  return _internal_modules_size();
}
inline void RowDefinition::clear_modules() {
  modules_.Clear();
}
inline ::carbon::frontend::module::ModuleDefinition* RowDefinition::mutable_modules(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.RowDefinition.modules)
  return modules_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleDefinition >*
RowDefinition::mutable_modules() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.module.RowDefinition.modules)
  return &modules_;
}
inline const ::carbon::frontend::module::ModuleDefinition& RowDefinition::_internal_modules(int index) const {
  return modules_.Get(index);
}
inline const ::carbon::frontend::module::ModuleDefinition& RowDefinition::modules(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.RowDefinition.modules)
  return _internal_modules(index);
}
inline ::carbon::frontend::module::ModuleDefinition* RowDefinition::_internal_add_modules() {
  return modules_.Add();
}
inline ::carbon::frontend::module::ModuleDefinition* RowDefinition::add_modules() {
  ::carbon::frontend::module::ModuleDefinition* _add = _internal_add_modules();
  // @@protoc_insertion_point(field_add:carbon.frontend.module.RowDefinition.modules)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::ModuleDefinition >&
RowDefinition::modules() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.module.RowDefinition.modules)
  return modules_;
}

// float row_spacing_mm = 3;
inline void RowDefinition::clear_row_spacing_mm() {
  row_spacing_mm_ = 0;
}
inline float RowDefinition::_internal_row_spacing_mm() const {
  return row_spacing_mm_;
}
inline float RowDefinition::row_spacing_mm() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.RowDefinition.row_spacing_mm)
  return _internal_row_spacing_mm();
}
inline void RowDefinition::_internal_set_row_spacing_mm(float value) {
  
  row_spacing_mm_ = value;
}
inline void RowDefinition::set_row_spacing_mm(float value) {
  _internal_set_row_spacing_mm(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.module.RowDefinition.row_spacing_mm)
}

// -------------------------------------------------------------------

// BarDefinition

// uint32 bar_length_mm = 1;
inline void BarDefinition::clear_bar_length_mm() {
  bar_length_mm_ = 0u;
}
inline uint32_t BarDefinition::_internal_bar_length_mm() const {
  return bar_length_mm_;
}
inline uint32_t BarDefinition::bar_length_mm() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.BarDefinition.bar_length_mm)
  return _internal_bar_length_mm();
}
inline void BarDefinition::_internal_set_bar_length_mm(uint32_t value) {
  
  bar_length_mm_ = value;
}
inline void BarDefinition::set_bar_length_mm(uint32_t value) {
  _internal_set_bar_length_mm(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.module.BarDefinition.bar_length_mm)
}

// bool folding = 2;
inline void BarDefinition::clear_folding() {
  folding_ = false;
}
inline bool BarDefinition::_internal_folding() const {
  return folding_;
}
inline bool BarDefinition::folding() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.BarDefinition.folding)
  return _internal_folding();
}
inline void BarDefinition::_internal_set_folding(bool value) {
  
  folding_ = value;
}
inline void BarDefinition::set_folding(bool value) {
  _internal_set_folding(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.module.BarDefinition.folding)
}

// -------------------------------------------------------------------

// RobotDefinition

// repeated .carbon.frontend.module.RowDefinition rows = 1;
inline int RobotDefinition::_internal_rows_size() const {
  return rows_.size();
}
inline int RobotDefinition::rows_size() const {
  return _internal_rows_size();
}
inline void RobotDefinition::clear_rows() {
  rows_.Clear();
}
inline ::carbon::frontend::module::RowDefinition* RobotDefinition::mutable_rows(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.RobotDefinition.rows)
  return rows_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::RowDefinition >*
RobotDefinition::mutable_rows() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.module.RobotDefinition.rows)
  return &rows_;
}
inline const ::carbon::frontend::module::RowDefinition& RobotDefinition::_internal_rows(int index) const {
  return rows_.Get(index);
}
inline const ::carbon::frontend::module::RowDefinition& RobotDefinition::rows(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.RobotDefinition.rows)
  return _internal_rows(index);
}
inline ::carbon::frontend::module::RowDefinition* RobotDefinition::_internal_add_rows() {
  return rows_.Add();
}
inline ::carbon::frontend::module::RowDefinition* RobotDefinition::add_rows() {
  ::carbon::frontend::module::RowDefinition* _add = _internal_add_rows();
  // @@protoc_insertion_point(field_add:carbon.frontend.module.RobotDefinition.rows)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::RowDefinition >&
RobotDefinition::rows() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.module.RobotDefinition.rows)
  return rows_;
}

// .carbon.frontend.module.BarDefinition bar_definition = 3;
inline bool RobotDefinition::_internal_has_bar_definition() const {
  return this != internal_default_instance() && bar_definition_ != nullptr;
}
inline bool RobotDefinition::has_bar_definition() const {
  return _internal_has_bar_definition();
}
inline void RobotDefinition::clear_bar_definition() {
  if (GetArenaForAllocation() == nullptr && bar_definition_ != nullptr) {
    delete bar_definition_;
  }
  bar_definition_ = nullptr;
}
inline const ::carbon::frontend::module::BarDefinition& RobotDefinition::_internal_bar_definition() const {
  const ::carbon::frontend::module::BarDefinition* p = bar_definition_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::BarDefinition&>(
      ::carbon::frontend::module::_BarDefinition_default_instance_);
}
inline const ::carbon::frontend::module::BarDefinition& RobotDefinition::bar_definition() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.RobotDefinition.bar_definition)
  return _internal_bar_definition();
}
inline void RobotDefinition::unsafe_arena_set_allocated_bar_definition(
    ::carbon::frontend::module::BarDefinition* bar_definition) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(bar_definition_);
  }
  bar_definition_ = bar_definition;
  if (bar_definition) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.RobotDefinition.bar_definition)
}
inline ::carbon::frontend::module::BarDefinition* RobotDefinition::release_bar_definition() {
  
  ::carbon::frontend::module::BarDefinition* temp = bar_definition_;
  bar_definition_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::BarDefinition* RobotDefinition::unsafe_arena_release_bar_definition() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.RobotDefinition.bar_definition)
  
  ::carbon::frontend::module::BarDefinition* temp = bar_definition_;
  bar_definition_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::BarDefinition* RobotDefinition::_internal_mutable_bar_definition() {
  
  if (bar_definition_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::BarDefinition>(GetArenaForAllocation());
    bar_definition_ = p;
  }
  return bar_definition_;
}
inline ::carbon::frontend::module::BarDefinition* RobotDefinition::mutable_bar_definition() {
  ::carbon::frontend::module::BarDefinition* _msg = _internal_mutable_bar_definition();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.RobotDefinition.bar_definition)
  return _msg;
}
inline void RobotDefinition::set_allocated_bar_definition(::carbon::frontend::module::BarDefinition* bar_definition) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete bar_definition_;
  }
  if (bar_definition) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::module::BarDefinition>::GetOwningArena(bar_definition);
    if (message_arena != submessage_arena) {
      bar_definition = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, bar_definition, submessage_arena);
    }
    
  } else {
    
  }
  bar_definition_ = bar_definition;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.RobotDefinition.bar_definition)
}

// -------------------------------------------------------------------

// Preset

// string uuid = 1;
inline void Preset::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& Preset::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.Preset.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Preset::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.module.Preset.uuid)
}
inline std::string* Preset::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.Preset.uuid)
  return _s;
}
inline const std::string& Preset::_internal_uuid() const {
  return uuid_.Get();
}
inline void Preset::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Preset::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Preset::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.Preset.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Preset::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.Preset.uuid)
}

// string display_name = 2;
inline void Preset::clear_display_name() {
  display_name_.ClearToEmpty();
}
inline const std::string& Preset::display_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.Preset.display_name)
  return _internal_display_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void Preset::set_display_name(ArgT0&& arg0, ArgT... args) {
 
 display_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.module.Preset.display_name)
}
inline std::string* Preset::mutable_display_name() {
  std::string* _s = _internal_mutable_display_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.Preset.display_name)
  return _s;
}
inline const std::string& Preset::_internal_display_name() const {
  return display_name_.Get();
}
inline void Preset::_internal_set_display_name(const std::string& value) {
  
  display_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* Preset::_internal_mutable_display_name() {
  
  return display_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* Preset::release_display_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.Preset.display_name)
  return display_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void Preset::set_allocated_display_name(std::string* display_name) {
  if (display_name != nullptr) {
    
  } else {
    
  }
  display_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), display_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (display_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    display_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.Preset.display_name)
}

// .carbon.frontend.module.RobotDefinition definition = 3;
inline bool Preset::_internal_has_definition() const {
  return this != internal_default_instance() && definition_ != nullptr;
}
inline bool Preset::has_definition() const {
  return _internal_has_definition();
}
inline void Preset::clear_definition() {
  if (GetArenaForAllocation() == nullptr && definition_ != nullptr) {
    delete definition_;
  }
  definition_ = nullptr;
}
inline const ::carbon::frontend::module::RobotDefinition& Preset::_internal_definition() const {
  const ::carbon::frontend::module::RobotDefinition* p = definition_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::RobotDefinition&>(
      ::carbon::frontend::module::_RobotDefinition_default_instance_);
}
inline const ::carbon::frontend::module::RobotDefinition& Preset::definition() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.Preset.definition)
  return _internal_definition();
}
inline void Preset::unsafe_arena_set_allocated_definition(
    ::carbon::frontend::module::RobotDefinition* definition) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(definition_);
  }
  definition_ = definition;
  if (definition) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.Preset.definition)
}
inline ::carbon::frontend::module::RobotDefinition* Preset::release_definition() {
  
  ::carbon::frontend::module::RobotDefinition* temp = definition_;
  definition_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::RobotDefinition* Preset::unsafe_arena_release_definition() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.Preset.definition)
  
  ::carbon::frontend::module::RobotDefinition* temp = definition_;
  definition_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::RobotDefinition* Preset::_internal_mutable_definition() {
  
  if (definition_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::RobotDefinition>(GetArenaForAllocation());
    definition_ = p;
  }
  return definition_;
}
inline ::carbon::frontend::module::RobotDefinition* Preset::mutable_definition() {
  ::carbon::frontend::module::RobotDefinition* _msg = _internal_mutable_definition();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.Preset.definition)
  return _msg;
}
inline void Preset::set_allocated_definition(::carbon::frontend::module::RobotDefinition* definition) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete definition_;
  }
  if (definition) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::module::RobotDefinition>::GetOwningArena(definition);
    if (message_arena != submessage_arena) {
      definition = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, definition, submessage_arena);
    }
    
  } else {
    
  }
  definition_ = definition;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.Preset.definition)
}

// -------------------------------------------------------------------

// GetPresetsListRequest

// string language = 1;
inline void GetPresetsListRequest::clear_language() {
  language_.ClearToEmpty();
}
inline const std::string& GetPresetsListRequest::language() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetPresetsListRequest.language)
  return _internal_language();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetPresetsListRequest::set_language(ArgT0&& arg0, ArgT... args) {
 
 language_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.module.GetPresetsListRequest.language)
}
inline std::string* GetPresetsListRequest::mutable_language() {
  std::string* _s = _internal_mutable_language();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetPresetsListRequest.language)
  return _s;
}
inline const std::string& GetPresetsListRequest::_internal_language() const {
  return language_.Get();
}
inline void GetPresetsListRequest::_internal_set_language(const std::string& value) {
  
  language_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetPresetsListRequest::_internal_mutable_language() {
  
  return language_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetPresetsListRequest::release_language() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.GetPresetsListRequest.language)
  return language_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetPresetsListRequest::set_allocated_language(std::string* language) {
  if (language != nullptr) {
    
  } else {
    
  }
  language_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), language,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (language_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    language_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.GetPresetsListRequest.language)
}

// -------------------------------------------------------------------

// GetPresetsListResponse

// repeated .carbon.frontend.module.Preset presets = 1;
inline int GetPresetsListResponse::_internal_presets_size() const {
  return presets_.size();
}
inline int GetPresetsListResponse::presets_size() const {
  return _internal_presets_size();
}
inline void GetPresetsListResponse::clear_presets() {
  presets_.Clear();
}
inline ::carbon::frontend::module::Preset* GetPresetsListResponse::mutable_presets(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetPresetsListResponse.presets)
  return presets_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::Preset >*
GetPresetsListResponse::mutable_presets() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.module.GetPresetsListResponse.presets)
  return &presets_;
}
inline const ::carbon::frontend::module::Preset& GetPresetsListResponse::_internal_presets(int index) const {
  return presets_.Get(index);
}
inline const ::carbon::frontend::module::Preset& GetPresetsListResponse::presets(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetPresetsListResponse.presets)
  return _internal_presets(index);
}
inline ::carbon::frontend::module::Preset* GetPresetsListResponse::_internal_add_presets() {
  return presets_.Add();
}
inline ::carbon::frontend::module::Preset* GetPresetsListResponse::add_presets() {
  ::carbon::frontend::module::Preset* _add = _internal_add_presets();
  // @@protoc_insertion_point(field_add:carbon.frontend.module.GetPresetsListResponse.presets)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::module::Preset >&
GetPresetsListResponse::presets() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.module.GetPresetsListResponse.presets)
  return presets_;
}

// -------------------------------------------------------------------

// GetCurrentRobotDefinitionResponse

// optional .carbon.frontend.module.RobotDefinition current_definition = 1;
inline bool GetCurrentRobotDefinitionResponse::_internal_has_current_definition() const {
  bool value = (_has_bits_[0] & 0x00000001u) != 0;
  PROTOBUF_ASSUME(!value || current_definition_ != nullptr);
  return value;
}
inline bool GetCurrentRobotDefinitionResponse::has_current_definition() const {
  return _internal_has_current_definition();
}
inline void GetCurrentRobotDefinitionResponse::clear_current_definition() {
  if (current_definition_ != nullptr) current_definition_->Clear();
  _has_bits_[0] &= ~0x00000001u;
}
inline const ::carbon::frontend::module::RobotDefinition& GetCurrentRobotDefinitionResponse::_internal_current_definition() const {
  const ::carbon::frontend::module::RobotDefinition* p = current_definition_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::RobotDefinition&>(
      ::carbon::frontend::module::_RobotDefinition_default_instance_);
}
inline const ::carbon::frontend::module::RobotDefinition& GetCurrentRobotDefinitionResponse::current_definition() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.GetCurrentRobotDefinitionResponse.current_definition)
  return _internal_current_definition();
}
inline void GetCurrentRobotDefinitionResponse::unsafe_arena_set_allocated_current_definition(
    ::carbon::frontend::module::RobotDefinition* current_definition) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_definition_);
  }
  current_definition_ = current_definition;
  if (current_definition) {
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.GetCurrentRobotDefinitionResponse.current_definition)
}
inline ::carbon::frontend::module::RobotDefinition* GetCurrentRobotDefinitionResponse::release_current_definition() {
  _has_bits_[0] &= ~0x00000001u;
  ::carbon::frontend::module::RobotDefinition* temp = current_definition_;
  current_definition_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::RobotDefinition* GetCurrentRobotDefinitionResponse::unsafe_arena_release_current_definition() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.GetCurrentRobotDefinitionResponse.current_definition)
  _has_bits_[0] &= ~0x00000001u;
  ::carbon::frontend::module::RobotDefinition* temp = current_definition_;
  current_definition_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::RobotDefinition* GetCurrentRobotDefinitionResponse::_internal_mutable_current_definition() {
  _has_bits_[0] |= 0x00000001u;
  if (current_definition_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::RobotDefinition>(GetArenaForAllocation());
    current_definition_ = p;
  }
  return current_definition_;
}
inline ::carbon::frontend::module::RobotDefinition* GetCurrentRobotDefinitionResponse::mutable_current_definition() {
  ::carbon::frontend::module::RobotDefinition* _msg = _internal_mutable_current_definition();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.GetCurrentRobotDefinitionResponse.current_definition)
  return _msg;
}
inline void GetCurrentRobotDefinitionResponse::set_allocated_current_definition(::carbon::frontend::module::RobotDefinition* current_definition) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete current_definition_;
  }
  if (current_definition) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::module::RobotDefinition>::GetOwningArena(current_definition);
    if (message_arena != submessage_arena) {
      current_definition = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, current_definition, submessage_arena);
    }
    _has_bits_[0] |= 0x00000001u;
  } else {
    _has_bits_[0] &= ~0x00000001u;
  }
  current_definition_ = current_definition;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.GetCurrentRobotDefinitionResponse.current_definition)
}

// -------------------------------------------------------------------

// SetCurrentRobotDefinitionRequest

// .carbon.frontend.module.RobotDefinition current_definition = 1;
inline bool SetCurrentRobotDefinitionRequest::_internal_has_current_definition() const {
  return this != internal_default_instance() && current_definition_ != nullptr;
}
inline bool SetCurrentRobotDefinitionRequest::has_current_definition() const {
  return _internal_has_current_definition();
}
inline void SetCurrentRobotDefinitionRequest::clear_current_definition() {
  if (GetArenaForAllocation() == nullptr && current_definition_ != nullptr) {
    delete current_definition_;
  }
  current_definition_ = nullptr;
}
inline const ::carbon::frontend::module::RobotDefinition& SetCurrentRobotDefinitionRequest::_internal_current_definition() const {
  const ::carbon::frontend::module::RobotDefinition* p = current_definition_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::module::RobotDefinition&>(
      ::carbon::frontend::module::_RobotDefinition_default_instance_);
}
inline const ::carbon::frontend::module::RobotDefinition& SetCurrentRobotDefinitionRequest::current_definition() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.module.SetCurrentRobotDefinitionRequest.current_definition)
  return _internal_current_definition();
}
inline void SetCurrentRobotDefinitionRequest::unsafe_arena_set_allocated_current_definition(
    ::carbon::frontend::module::RobotDefinition* current_definition) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_definition_);
  }
  current_definition_ = current_definition;
  if (current_definition) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.module.SetCurrentRobotDefinitionRequest.current_definition)
}
inline ::carbon::frontend::module::RobotDefinition* SetCurrentRobotDefinitionRequest::release_current_definition() {
  
  ::carbon::frontend::module::RobotDefinition* temp = current_definition_;
  current_definition_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::module::RobotDefinition* SetCurrentRobotDefinitionRequest::unsafe_arena_release_current_definition() {
  // @@protoc_insertion_point(field_release:carbon.frontend.module.SetCurrentRobotDefinitionRequest.current_definition)
  
  ::carbon::frontend::module::RobotDefinition* temp = current_definition_;
  current_definition_ = nullptr;
  return temp;
}
inline ::carbon::frontend::module::RobotDefinition* SetCurrentRobotDefinitionRequest::_internal_mutable_current_definition() {
  
  if (current_definition_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::module::RobotDefinition>(GetArenaForAllocation());
    current_definition_ = p;
  }
  return current_definition_;
}
inline ::carbon::frontend::module::RobotDefinition* SetCurrentRobotDefinitionRequest::mutable_current_definition() {
  ::carbon::frontend::module::RobotDefinition* _msg = _internal_mutable_current_definition();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.module.SetCurrentRobotDefinitionRequest.current_definition)
  return _msg;
}
inline void SetCurrentRobotDefinitionRequest::set_allocated_current_definition(::carbon::frontend::module::RobotDefinition* current_definition) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete current_definition_;
  }
  if (current_definition) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::module::RobotDefinition>::GetOwningArena(current_definition);
    if (message_arena != submessage_arena) {
      current_definition = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, current_definition, submessage_arena);
    }
    
  } else {
    
  }
  current_definition_ = current_definition;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.module.SetCurrentRobotDefinitionRequest.current_definition)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace module
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fmodule_2eproto

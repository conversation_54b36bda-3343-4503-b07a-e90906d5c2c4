# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/calibration.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import camera_pb2 as frontend_dot_proto_dot_camera__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/calibration.proto',
  package='carbon.frontend.color_calibration',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n frontend/proto/calibration.proto\x12!carbon.frontend.color_calibration\x1a\x1b\x66rontend/proto/camera.proto\x1a\x19\x66rontend/proto/util.proto\"R\n\x16\x43olorCalibrationValues\x12\x0b\n\x03red\x18\x01 \x01(\x02\x12\r\n\x05green\x18\x02 \x01(\x02\x12\x0c\n\x04\x62lue\x18\x03 \x01(\x02\x12\x0e\n\x06\x63\x61m_id\x18\x04 \x01(\t2\xff\x01\n\x12\x43\x61librationService\x12y\n\x15StartColorCalibration\x12%.carbon.frontend.camera.CameraRequest\x1a\x39.carbon.frontend.color_calibration.ColorCalibrationValues\x12n\n\x14SaveColorCalibration\x12\x39.carbon.frontend.color_calibration.ColorCalibrationValues\x1a\x1b.carbon.frontend.util.EmptyB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_camera__pb2.DESCRIPTOR,frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])




_COLORCALIBRATIONVALUES = _descriptor.Descriptor(
  name='ColorCalibrationValues',
  full_name='carbon.frontend.color_calibration.ColorCalibrationValues',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='red', full_name='carbon.frontend.color_calibration.ColorCalibrationValues.red', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='green', full_name='carbon.frontend.color_calibration.ColorCalibrationValues.green', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='blue', full_name='carbon.frontend.color_calibration.ColorCalibrationValues.blue', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cam_id', full_name='carbon.frontend.color_calibration.ColorCalibrationValues.cam_id', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=127,
  serialized_end=209,
)

DESCRIPTOR.message_types_by_name['ColorCalibrationValues'] = _COLORCALIBRATIONVALUES
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ColorCalibrationValues = _reflection.GeneratedProtocolMessageType('ColorCalibrationValues', (_message.Message,), {
  'DESCRIPTOR' : _COLORCALIBRATIONVALUES,
  '__module__' : 'frontend.proto.calibration_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.color_calibration.ColorCalibrationValues)
  })
_sym_db.RegisterMessage(ColorCalibrationValues)


DESCRIPTOR._options = None

_CALIBRATIONSERVICE = _descriptor.ServiceDescriptor(
  name='CalibrationService',
  full_name='carbon.frontend.color_calibration.CalibrationService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=212,
  serialized_end=467,
  methods=[
  _descriptor.MethodDescriptor(
    name='StartColorCalibration',
    full_name='carbon.frontend.color_calibration.CalibrationService.StartColorCalibration',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_camera__pb2._CAMERAREQUEST,
    output_type=_COLORCALIBRATIONVALUES,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SaveColorCalibration',
    full_name='carbon.frontend.color_calibration.CalibrationService.SaveColorCalibration',
    index=1,
    containing_service=None,
    input_type=_COLORCALIBRATIONVALUES,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CALIBRATIONSERVICE)

DESCRIPTOR.services_by_name['CalibrationService'] = _CALIBRATIONSERVICE

# @@protoc_insertion_point(module_scope)

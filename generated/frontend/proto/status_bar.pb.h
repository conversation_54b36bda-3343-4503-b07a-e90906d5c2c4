// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/status_bar.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fstatus_5fbar_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fstatus_5fbar_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/translation.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fstatus_5fbar_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[9]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fstatus_5fbar_2eproto;
namespace carbon {
namespace frontend {
namespace status_bar {
class GlobalStatus;
struct GlobalStatusDefaultTypeInternal;
extern GlobalStatusDefaultTypeInternal _GlobalStatus_default_instance_;
class ReportIssueRequest;
struct ReportIssueRequestDefaultTypeInternal;
extern ReportIssueRequestDefaultTypeInternal _ReportIssueRequest_default_instance_;
class ServerStatus;
struct ServerStatusDefaultTypeInternal;
extern ServerStatusDefaultTypeInternal _ServerStatus_default_instance_;
class ServiceStatus;
struct ServiceStatusDefaultTypeInternal;
extern ServiceStatusDefaultTypeInternal _ServiceStatus_default_instance_;
class StatusBarMessage;
struct StatusBarMessageDefaultTypeInternal;
extern StatusBarMessageDefaultTypeInternal _StatusBarMessage_default_instance_;
class StatusBarMessage_RowStatusEntry_DoNotUse;
struct StatusBarMessage_RowStatusEntry_DoNotUseDefaultTypeInternal;
extern StatusBarMessage_RowStatusEntry_DoNotUseDefaultTypeInternal _StatusBarMessage_RowStatusEntry_DoNotUse_default_instance_;
class SupportPhoneMessage;
struct SupportPhoneMessageDefaultTypeInternal;
extern SupportPhoneMessageDefaultTypeInternal _SupportPhoneMessage_default_instance_;
class TranslatedStatusMessage;
struct TranslatedStatusMessageDefaultTypeInternal;
extern TranslatedStatusMessageDefaultTypeInternal _TranslatedStatusMessage_default_instance_;
class TranslatedStatusMessageDetails;
struct TranslatedStatusMessageDetailsDefaultTypeInternal;
extern TranslatedStatusMessageDetailsDefaultTypeInternal _TranslatedStatusMessageDetails_default_instance_;
}  // namespace status_bar
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::status_bar::GlobalStatus* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::GlobalStatus>(Arena*);
template<> ::carbon::frontend::status_bar::ReportIssueRequest* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::ReportIssueRequest>(Arena*);
template<> ::carbon::frontend::status_bar::ServerStatus* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::ServerStatus>(Arena*);
template<> ::carbon::frontend::status_bar::ServiceStatus* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::ServiceStatus>(Arena*);
template<> ::carbon::frontend::status_bar::StatusBarMessage* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::StatusBarMessage>(Arena*);
template<> ::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::StatusBarMessage_RowStatusEntry_DoNotUse>(Arena*);
template<> ::carbon::frontend::status_bar::SupportPhoneMessage* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::SupportPhoneMessage>(Arena*);
template<> ::carbon::frontend::status_bar::TranslatedStatusMessage* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::TranslatedStatusMessage>(Arena*);
template<> ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* Arena::CreateMaybeMessage<::carbon::frontend::status_bar::TranslatedStatusMessageDetails>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace status_bar {

enum Status : int {
  STATUS_ERROR = 0,
  STATUS_ESTOPPED = 1,
  STATUS_PRE_ARMED = 2,
  STATUS_POWERED_DOWN = 3,
  STATUS_POWERING_UP = 4,
  STATUS_UPDATE_INSTALLING = 5,
  STATUS_MODEL_LOADING = 6,
  STATUS_MODEL_INSTALLING = 7,
  STATUS_WEEDING = 8,
  STATUS_STANDBY = 9,
  STATUS_UNKNOWN = 10,
  STATUS_DISCONNECTED = 11,
  STATUS_LIFTED = 12,
  STATUS_LOADING = 13,
  STATUS_ALARM_AUTOFIX_IN_PROGRESS = 14,
  STATUS_FAILED_TO_POWER_UP = 15,
  STATUS_SERVER_CABINET_COOLDOWN = 16,
  STATUS_CHILLER_COOLDOWN = 17,
  STATUS_TRACTOR_NOT_SAFE = 18,
  Status_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  Status_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool Status_IsValid(int value);
constexpr Status Status_MIN = STATUS_ERROR;
constexpr Status Status_MAX = STATUS_TRACTOR_NOT_SAFE;
constexpr int Status_ARRAYSIZE = Status_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* Status_descriptor();
template<typename T>
inline const std::string& Status_Name(T enum_t_value) {
  static_assert(::std::is_same<T, Status>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function Status_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    Status_descriptor(), enum_t_value);
}
inline bool Status_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, Status* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<Status>(
    Status_descriptor(), name, value);
}
enum StatusLevel : int {
  INVALID = 0,
  READY = 1,
  LOADING = 2,
  ESTOPPED PROTOBUF_DEPRECATED_ENUM = 3,
  StatusLevel_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  StatusLevel_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool StatusLevel_IsValid(int value);
constexpr StatusLevel StatusLevel_MIN = INVALID;
constexpr StatusLevel StatusLevel_MAX = ESTOPPED;
constexpr int StatusLevel_ARRAYSIZE = StatusLevel_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* StatusLevel_descriptor();
template<typename T>
inline const std::string& StatusLevel_Name(T enum_t_value) {
  static_assert(::std::is_same<T, StatusLevel>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function StatusLevel_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    StatusLevel_descriptor(), enum_t_value);
}
inline bool StatusLevel_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, StatusLevel* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<StatusLevel>(
    StatusLevel_descriptor(), name, value);
}
// ===================================================================

class GlobalStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.status_bar.GlobalStatus) */ {
 public:
  inline GlobalStatus() : GlobalStatus(nullptr) {}
  ~GlobalStatus() override;
  explicit constexpr GlobalStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GlobalStatus(const GlobalStatus& from);
  GlobalStatus(GlobalStatus&& from) noexcept
    : GlobalStatus() {
    *this = ::std::move(from);
  }

  inline GlobalStatus& operator=(const GlobalStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline GlobalStatus& operator=(GlobalStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GlobalStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const GlobalStatus* internal_default_instance() {
    return reinterpret_cast<const GlobalStatus*>(
               &_GlobalStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GlobalStatus& a, GlobalStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(GlobalStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GlobalStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GlobalStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GlobalStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GlobalStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GlobalStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GlobalStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.status_bar.GlobalStatus";
  }
  protected:
  explicit GlobalStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHintFieldNumber = 1,
    kIconNameFieldNumber = 2,
    kIconColorFieldNumber = 3,
  };
  // string hint = 1;
  void clear_hint();
  const std::string& hint() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_hint(ArgT0&& arg0, ArgT... args);
  std::string* mutable_hint();
  PROTOBUF_NODISCARD std::string* release_hint();
  void set_allocated_hint(std::string* hint);
  private:
  const std::string& _internal_hint() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_hint(const std::string& value);
  std::string* _internal_mutable_hint();
  public:

  // string icon_name = 2;
  void clear_icon_name();
  const std::string& icon_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_icon_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_icon_name();
  PROTOBUF_NODISCARD std::string* release_icon_name();
  void set_allocated_icon_name(std::string* icon_name);
  private:
  const std::string& _internal_icon_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_icon_name(const std::string& value);
  std::string* _internal_mutable_icon_name();
  public:

  // string icon_color = 3;
  void clear_icon_color();
  const std::string& icon_color() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_icon_color(ArgT0&& arg0, ArgT... args);
  std::string* mutable_icon_color();
  PROTOBUF_NODISCARD std::string* release_icon_color();
  void set_allocated_icon_color(std::string* icon_color);
  private:
  const std::string& _internal_icon_color() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_icon_color(const std::string& value);
  std::string* _internal_mutable_icon_color();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.GlobalStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hint_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr icon_name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr icon_color_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto;
};
// -------------------------------------------------------------------

class ServiceStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.status_bar.ServiceStatus) */ {
 public:
  inline ServiceStatus() : ServiceStatus(nullptr) {}
  ~ServiceStatus() override;
  explicit constexpr ServiceStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ServiceStatus(const ServiceStatus& from);
  ServiceStatus(ServiceStatus&& from) noexcept
    : ServiceStatus() {
    *this = ::std::move(from);
  }

  inline ServiceStatus& operator=(const ServiceStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline ServiceStatus& operator=(ServiceStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ServiceStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const ServiceStatus* internal_default_instance() {
    return reinterpret_cast<const ServiceStatus*>(
               &_ServiceStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ServiceStatus& a, ServiceStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(ServiceStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ServiceStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ServiceStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ServiceStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ServiceStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ServiceStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ServiceStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.status_bar.ServiceStatus";
  }
  protected:
  explicit ServiceStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kStatusLevelFieldNumber = 2,
  };
  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.frontend.status_bar.StatusLevel status_level = 2;
  void clear_status_level();
  ::carbon::frontend::status_bar::StatusLevel status_level() const;
  void set_status_level(::carbon::frontend::status_bar::StatusLevel value);
  private:
  ::carbon::frontend::status_bar::StatusLevel _internal_status_level() const;
  void _internal_set_status_level(::carbon::frontend::status_bar::StatusLevel value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.ServiceStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  int status_level_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto;
};
// -------------------------------------------------------------------

class ServerStatus final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.status_bar.ServerStatus) */ {
 public:
  inline ServerStatus() : ServerStatus(nullptr) {}
  ~ServerStatus() override;
  explicit constexpr ServerStatus(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ServerStatus(const ServerStatus& from);
  ServerStatus(ServerStatus&& from) noexcept
    : ServerStatus() {
    *this = ::std::move(from);
  }

  inline ServerStatus& operator=(const ServerStatus& from) {
    CopyFrom(from);
    return *this;
  }
  inline ServerStatus& operator=(ServerStatus&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ServerStatus& default_instance() {
    return *internal_default_instance();
  }
  static inline const ServerStatus* internal_default_instance() {
    return reinterpret_cast<const ServerStatus*>(
               &_ServerStatus_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ServerStatus& a, ServerStatus& b) {
    a.Swap(&b);
  }
  inline void Swap(ServerStatus* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ServerStatus* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ServerStatus* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ServerStatus>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ServerStatus& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ServerStatus& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ServerStatus* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.status_bar.ServerStatus";
  }
  protected:
  explicit ServerStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kServiceStatusFieldNumber = 2,
    kStatusLevelFieldNumber = 1,
  };
  // repeated .carbon.frontend.status_bar.ServiceStatus service_status = 2;
  int service_status_size() const;
  private:
  int _internal_service_status_size() const;
  public:
  void clear_service_status();
  ::carbon::frontend::status_bar::ServiceStatus* mutable_service_status(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::ServiceStatus >*
      mutable_service_status();
  private:
  const ::carbon::frontend::status_bar::ServiceStatus& _internal_service_status(int index) const;
  ::carbon::frontend::status_bar::ServiceStatus* _internal_add_service_status();
  public:
  const ::carbon::frontend::status_bar::ServiceStatus& service_status(int index) const;
  ::carbon::frontend::status_bar::ServiceStatus* add_service_status();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::ServiceStatus >&
      service_status() const;

  // .carbon.frontend.status_bar.StatusLevel status_level = 1;
  void clear_status_level();
  ::carbon::frontend::status_bar::StatusLevel status_level() const;
  void set_status_level(::carbon::frontend::status_bar::StatusLevel value);
  private:
  ::carbon::frontend::status_bar::StatusLevel _internal_status_level() const;
  void _internal_set_status_level(::carbon::frontend::status_bar::StatusLevel value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.ServerStatus)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::ServiceStatus > service_status_;
  int status_level_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto;
};
// -------------------------------------------------------------------

class TranslatedStatusMessageDetails final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.status_bar.TranslatedStatusMessageDetails) */ {
 public:
  inline TranslatedStatusMessageDetails() : TranslatedStatusMessageDetails(nullptr) {}
  ~TranslatedStatusMessageDetails() override;
  explicit constexpr TranslatedStatusMessageDetails(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TranslatedStatusMessageDetails(const TranslatedStatusMessageDetails& from);
  TranslatedStatusMessageDetails(TranslatedStatusMessageDetails&& from) noexcept
    : TranslatedStatusMessageDetails() {
    *this = ::std::move(from);
  }

  inline TranslatedStatusMessageDetails& operator=(const TranslatedStatusMessageDetails& from) {
    CopyFrom(from);
    return *this;
  }
  inline TranslatedStatusMessageDetails& operator=(TranslatedStatusMessageDetails&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TranslatedStatusMessageDetails& default_instance() {
    return *internal_default_instance();
  }
  enum DetailsCase {
    kDetailsStringKey = 1,
    kTimer = 2,
    DETAILS_NOT_SET = 0,
  };

  static inline const TranslatedStatusMessageDetails* internal_default_instance() {
    return reinterpret_cast<const TranslatedStatusMessageDetails*>(
               &_TranslatedStatusMessageDetails_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(TranslatedStatusMessageDetails& a, TranslatedStatusMessageDetails& b) {
    a.Swap(&b);
  }
  inline void Swap(TranslatedStatusMessageDetails* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TranslatedStatusMessageDetails* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TranslatedStatusMessageDetails* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TranslatedStatusMessageDetails>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TranslatedStatusMessageDetails& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TranslatedStatusMessageDetails& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TranslatedStatusMessageDetails* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.status_bar.TranslatedStatusMessageDetails";
  }
  protected:
  explicit TranslatedStatusMessageDetails(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDetailsStringKeyFieldNumber = 1,
    kTimerFieldNumber = 2,
  };
  // string details_string_key = 1;
  bool has_details_string_key() const;
  private:
  bool _internal_has_details_string_key() const;
  public:
  void clear_details_string_key();
  const std::string& details_string_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_details_string_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_details_string_key();
  PROTOBUF_NODISCARD std::string* release_details_string_key();
  void set_allocated_details_string_key(std::string* details_string_key);
  private:
  const std::string& _internal_details_string_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_details_string_key(const std::string& value);
  std::string* _internal_mutable_details_string_key();
  public:

  // .carbon.frontend.translation.DurationValue timer = 2;
  bool has_timer() const;
  private:
  bool _internal_has_timer() const;
  public:
  void clear_timer();
  const ::carbon::frontend::translation::DurationValue& timer() const;
  PROTOBUF_NODISCARD ::carbon::frontend::translation::DurationValue* release_timer();
  ::carbon::frontend::translation::DurationValue* mutable_timer();
  void set_allocated_timer(::carbon::frontend::translation::DurationValue* timer);
  private:
  const ::carbon::frontend::translation::DurationValue& _internal_timer() const;
  ::carbon::frontend::translation::DurationValue* _internal_mutable_timer();
  public:
  void unsafe_arena_set_allocated_timer(
      ::carbon::frontend::translation::DurationValue* timer);
  ::carbon::frontend::translation::DurationValue* unsafe_arena_release_timer();

  void clear_details();
  DetailsCase details_case() const;
  // @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.TranslatedStatusMessageDetails)
 private:
  class _Internal;
  void set_has_details_string_key();
  void set_has_timer();

  inline bool has_details() const;
  inline void clear_has_details();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union DetailsUnion {
    constexpr DetailsUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr details_string_key_;
    ::carbon::frontend::translation::DurationValue* timer_;
  } details_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto;
};
// -------------------------------------------------------------------

class TranslatedStatusMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.status_bar.TranslatedStatusMessage) */ {
 public:
  inline TranslatedStatusMessage() : TranslatedStatusMessage(nullptr) {}
  ~TranslatedStatusMessage() override;
  explicit constexpr TranslatedStatusMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TranslatedStatusMessage(const TranslatedStatusMessage& from);
  TranslatedStatusMessage(TranslatedStatusMessage&& from) noexcept
    : TranslatedStatusMessage() {
    *this = ::std::move(from);
  }

  inline TranslatedStatusMessage& operator=(const TranslatedStatusMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline TranslatedStatusMessage& operator=(TranslatedStatusMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TranslatedStatusMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const TranslatedStatusMessage* internal_default_instance() {
    return reinterpret_cast<const TranslatedStatusMessage*>(
               &_TranslatedStatusMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(TranslatedStatusMessage& a, TranslatedStatusMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(TranslatedStatusMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TranslatedStatusMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TranslatedStatusMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TranslatedStatusMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TranslatedStatusMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TranslatedStatusMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TranslatedStatusMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.status_bar.TranslatedStatusMessage";
  }
  protected:
  explicit TranslatedStatusMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kPrefixFieldNumber = 1,
    kDetailsFieldNumber = 2,
  };
  // string prefix = 1;
  void clear_prefix();
  const std::string& prefix() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_prefix(ArgT0&& arg0, ArgT... args);
  std::string* mutable_prefix();
  PROTOBUF_NODISCARD std::string* release_prefix();
  void set_allocated_prefix(std::string* prefix);
  private:
  const std::string& _internal_prefix() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_prefix(const std::string& value);
  std::string* _internal_mutable_prefix();
  public:

  // .carbon.frontend.status_bar.TranslatedStatusMessageDetails details = 2;
  bool has_details() const;
  private:
  bool _internal_has_details() const;
  public:
  void clear_details();
  const ::carbon::frontend::status_bar::TranslatedStatusMessageDetails& details() const;
  PROTOBUF_NODISCARD ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* release_details();
  ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* mutable_details();
  void set_allocated_details(::carbon::frontend::status_bar::TranslatedStatusMessageDetails* details);
  private:
  const ::carbon::frontend::status_bar::TranslatedStatusMessageDetails& _internal_details() const;
  ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* _internal_mutable_details();
  public:
  void unsafe_arena_set_allocated_details(
      ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* details);
  ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* unsafe_arena_release_details();

  // @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.TranslatedStatusMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr prefix_;
  ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* details_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto;
};
// -------------------------------------------------------------------

class StatusBarMessage_RowStatusEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<StatusBarMessage_RowStatusEntry_DoNotUse, 
    int32_t, ::carbon::frontend::status_bar::ServerStatus,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<StatusBarMessage_RowStatusEntry_DoNotUse, 
    int32_t, ::carbon::frontend::status_bar::ServerStatus,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  StatusBarMessage_RowStatusEntry_DoNotUse();
  explicit constexpr StatusBarMessage_RowStatusEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit StatusBarMessage_RowStatusEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const StatusBarMessage_RowStatusEntry_DoNotUse& other);
  static const StatusBarMessage_RowStatusEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const StatusBarMessage_RowStatusEntry_DoNotUse*>(&_StatusBarMessage_RowStatusEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(void*) { return true; }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class StatusBarMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.status_bar.StatusBarMessage) */ {
 public:
  inline StatusBarMessage() : StatusBarMessage(nullptr) {}
  ~StatusBarMessage() override;
  explicit constexpr StatusBarMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StatusBarMessage(const StatusBarMessage& from);
  StatusBarMessage(StatusBarMessage&& from) noexcept
    : StatusBarMessage() {
    *this = ::std::move(from);
  }

  inline StatusBarMessage& operator=(const StatusBarMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline StatusBarMessage& operator=(StatusBarMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StatusBarMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const StatusBarMessage* internal_default_instance() {
    return reinterpret_cast<const StatusBarMessage*>(
               &_StatusBarMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(StatusBarMessage& a, StatusBarMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(StatusBarMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StatusBarMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StatusBarMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StatusBarMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StatusBarMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StatusBarMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StatusBarMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.status_bar.StatusBarMessage";
  }
  protected:
  explicit StatusBarMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kRowStatusFieldNumber = 8,
    kGlobalStatusesFieldNumber = 10,
    kStatusMessageFieldNumber = 6,
    kSerialFieldNumber = 7,
    kTsFieldNumber = 1,
    kCommandStatusFieldNumber = 9,
    kTranslatedStatusMessageFieldNumber = 11,
    kLasersEnabledFieldNumber = 3,
    kWeedingEnabledFieldNumber = 4,
    kStatusLevelFieldNumber = 5,
  };
  // map<int32, .carbon.frontend.status_bar.ServerStatus> row_status = 8;
  int row_status_size() const;
  private:
  int _internal_row_status_size() const;
  public:
  void clear_row_status();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >&
      _internal_row_status() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >*
      _internal_mutable_row_status();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >&
      row_status() const;
  ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >*
      mutable_row_status();

  // repeated .carbon.frontend.status_bar.GlobalStatus global_statuses = 10;
  int global_statuses_size() const;
  private:
  int _internal_global_statuses_size() const;
  public:
  void clear_global_statuses();
  ::carbon::frontend::status_bar::GlobalStatus* mutable_global_statuses(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::GlobalStatus >*
      mutable_global_statuses();
  private:
  const ::carbon::frontend::status_bar::GlobalStatus& _internal_global_statuses(int index) const;
  ::carbon::frontend::status_bar::GlobalStatus* _internal_add_global_statuses();
  public:
  const ::carbon::frontend::status_bar::GlobalStatus& global_statuses(int index) const;
  ::carbon::frontend::status_bar::GlobalStatus* add_global_statuses();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::GlobalStatus >&
      global_statuses() const;

  // string status_message = 6;
  void clear_status_message();
  const std::string& status_message() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_status_message(ArgT0&& arg0, ArgT... args);
  std::string* mutable_status_message();
  PROTOBUF_NODISCARD std::string* release_status_message();
  void set_allocated_status_message(std::string* status_message);
  private:
  const std::string& _internal_status_message() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_status_message(const std::string& value);
  std::string* _internal_mutable_status_message();
  public:

  // string serial = 7;
  void clear_serial();
  const std::string& serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_serial();
  PROTOBUF_NODISCARD std::string* release_serial();
  void set_allocated_serial(std::string* serial);
  private:
  const std::string& _internal_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_serial(const std::string& value);
  std::string* _internal_mutable_serial();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.status_bar.ServerStatus command_status = 9;
  bool has_command_status() const;
  private:
  bool _internal_has_command_status() const;
  public:
  void clear_command_status();
  const ::carbon::frontend::status_bar::ServerStatus& command_status() const;
  PROTOBUF_NODISCARD ::carbon::frontend::status_bar::ServerStatus* release_command_status();
  ::carbon::frontend::status_bar::ServerStatus* mutable_command_status();
  void set_allocated_command_status(::carbon::frontend::status_bar::ServerStatus* command_status);
  private:
  const ::carbon::frontend::status_bar::ServerStatus& _internal_command_status() const;
  ::carbon::frontend::status_bar::ServerStatus* _internal_mutable_command_status();
  public:
  void unsafe_arena_set_allocated_command_status(
      ::carbon::frontend::status_bar::ServerStatus* command_status);
  ::carbon::frontend::status_bar::ServerStatus* unsafe_arena_release_command_status();

  // .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 11;
  bool has_translated_status_message() const;
  private:
  bool _internal_has_translated_status_message() const;
  public:
  void clear_translated_status_message();
  const ::carbon::frontend::status_bar::TranslatedStatusMessage& translated_status_message() const;
  PROTOBUF_NODISCARD ::carbon::frontend::status_bar::TranslatedStatusMessage* release_translated_status_message();
  ::carbon::frontend::status_bar::TranslatedStatusMessage* mutable_translated_status_message();
  void set_allocated_translated_status_message(::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message);
  private:
  const ::carbon::frontend::status_bar::TranslatedStatusMessage& _internal_translated_status_message() const;
  ::carbon::frontend::status_bar::TranslatedStatusMessage* _internal_mutable_translated_status_message();
  public:
  void unsafe_arena_set_allocated_translated_status_message(
      ::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message);
  ::carbon::frontend::status_bar::TranslatedStatusMessage* unsafe_arena_release_translated_status_message();

  // bool lasers_enabled = 3;
  void clear_lasers_enabled();
  bool lasers_enabled() const;
  void set_lasers_enabled(bool value);
  private:
  bool _internal_lasers_enabled() const;
  void _internal_set_lasers_enabled(bool value);
  public:

  // bool weeding_enabled = 4;
  void clear_weeding_enabled();
  bool weeding_enabled() const;
  void set_weeding_enabled(bool value);
  private:
  bool _internal_weeding_enabled() const;
  void _internal_set_weeding_enabled(bool value);
  public:

  // .carbon.frontend.status_bar.Status status_level = 5;
  void clear_status_level();
  ::carbon::frontend::status_bar::Status status_level() const;
  void set_status_level(::carbon::frontend::status_bar::Status value);
  private:
  ::carbon::frontend::status_bar::Status _internal_status_level() const;
  void _internal_set_status_level(::carbon::frontend::status_bar::Status value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.StatusBarMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      StatusBarMessage_RowStatusEntry_DoNotUse,
      int32_t, ::carbon::frontend::status_bar::ServerStatus,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_INT32,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> row_status_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::GlobalStatus > global_statuses_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr status_message_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr serial_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::status_bar::ServerStatus* command_status_;
  ::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message_;
  bool lasers_enabled_;
  bool weeding_enabled_;
  int status_level_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto;
};
// -------------------------------------------------------------------

class ReportIssueRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.status_bar.ReportIssueRequest) */ {
 public:
  inline ReportIssueRequest() : ReportIssueRequest(nullptr) {}
  ~ReportIssueRequest() override;
  explicit constexpr ReportIssueRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ReportIssueRequest(const ReportIssueRequest& from);
  ReportIssueRequest(ReportIssueRequest&& from) noexcept
    : ReportIssueRequest() {
    *this = ::std::move(from);
  }

  inline ReportIssueRequest& operator=(const ReportIssueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline ReportIssueRequest& operator=(ReportIssueRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ReportIssueRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const ReportIssueRequest* internal_default_instance() {
    return reinterpret_cast<const ReportIssueRequest*>(
               &_ReportIssueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ReportIssueRequest& a, ReportIssueRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(ReportIssueRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ReportIssueRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ReportIssueRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ReportIssueRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ReportIssueRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ReportIssueRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ReportIssueRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.status_bar.ReportIssueRequest";
  }
  protected:
  explicit ReportIssueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDescriptionFieldNumber = 1,
    kPhoneNumberFieldNumber = 2,
  };
  // string description = 1;
  void clear_description();
  const std::string& description() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_description(ArgT0&& arg0, ArgT... args);
  std::string* mutable_description();
  PROTOBUF_NODISCARD std::string* release_description();
  void set_allocated_description(std::string* description);
  private:
  const std::string& _internal_description() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_description(const std::string& value);
  std::string* _internal_mutable_description();
  public:

  // string phone_number = 2;
  void clear_phone_number();
  const std::string& phone_number() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_phone_number(ArgT0&& arg0, ArgT... args);
  std::string* mutable_phone_number();
  PROTOBUF_NODISCARD std::string* release_phone_number();
  void set_allocated_phone_number(std::string* phone_number);
  private:
  const std::string& _internal_phone_number() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_phone_number(const std::string& value);
  std::string* _internal_mutable_phone_number();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.ReportIssueRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr description_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr phone_number_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto;
};
// -------------------------------------------------------------------

class SupportPhoneMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.status_bar.SupportPhoneMessage) */ {
 public:
  inline SupportPhoneMessage() : SupportPhoneMessage(nullptr) {}
  ~SupportPhoneMessage() override;
  explicit constexpr SupportPhoneMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SupportPhoneMessage(const SupportPhoneMessage& from);
  SupportPhoneMessage(SupportPhoneMessage&& from) noexcept
    : SupportPhoneMessage() {
    *this = ::std::move(from);
  }

  inline SupportPhoneMessage& operator=(const SupportPhoneMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline SupportPhoneMessage& operator=(SupportPhoneMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SupportPhoneMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const SupportPhoneMessage* internal_default_instance() {
    return reinterpret_cast<const SupportPhoneMessage*>(
               &_SupportPhoneMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(SupportPhoneMessage& a, SupportPhoneMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(SupportPhoneMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SupportPhoneMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SupportPhoneMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SupportPhoneMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SupportPhoneMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SupportPhoneMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SupportPhoneMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.status_bar.SupportPhoneMessage";
  }
  protected:
  explicit SupportPhoneMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSupportPhoneFieldNumber = 1,
  };
  // string support_phone = 1;
  void clear_support_phone();
  const std::string& support_phone() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_support_phone(ArgT0&& arg0, ArgT... args);
  std::string* mutable_support_phone();
  PROTOBUF_NODISCARD std::string* release_support_phone();
  void set_allocated_support_phone(std::string* support_phone);
  private:
  const std::string& _internal_support_phone() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_support_phone(const std::string& value);
  std::string* _internal_mutable_support_phone();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.status_bar.SupportPhoneMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr support_phone_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fstatus_5fbar_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GlobalStatus

// string hint = 1;
inline void GlobalStatus::clear_hint() {
  hint_.ClearToEmpty();
}
inline const std::string& GlobalStatus::hint() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.GlobalStatus.hint)
  return _internal_hint();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GlobalStatus::set_hint(ArgT0&& arg0, ArgT... args) {
 
 hint_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.GlobalStatus.hint)
}
inline std::string* GlobalStatus::mutable_hint() {
  std::string* _s = _internal_mutable_hint();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.GlobalStatus.hint)
  return _s;
}
inline const std::string& GlobalStatus::_internal_hint() const {
  return hint_.Get();
}
inline void GlobalStatus::_internal_set_hint(const std::string& value) {
  
  hint_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GlobalStatus::_internal_mutable_hint() {
  
  return hint_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GlobalStatus::release_hint() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.GlobalStatus.hint)
  return hint_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GlobalStatus::set_allocated_hint(std::string* hint) {
  if (hint != nullptr) {
    
  } else {
    
  }
  hint_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), hint,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (hint_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    hint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.GlobalStatus.hint)
}

// string icon_name = 2;
inline void GlobalStatus::clear_icon_name() {
  icon_name_.ClearToEmpty();
}
inline const std::string& GlobalStatus::icon_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.GlobalStatus.icon_name)
  return _internal_icon_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GlobalStatus::set_icon_name(ArgT0&& arg0, ArgT... args) {
 
 icon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.GlobalStatus.icon_name)
}
inline std::string* GlobalStatus::mutable_icon_name() {
  std::string* _s = _internal_mutable_icon_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.GlobalStatus.icon_name)
  return _s;
}
inline const std::string& GlobalStatus::_internal_icon_name() const {
  return icon_name_.Get();
}
inline void GlobalStatus::_internal_set_icon_name(const std::string& value) {
  
  icon_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GlobalStatus::_internal_mutable_icon_name() {
  
  return icon_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GlobalStatus::release_icon_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.GlobalStatus.icon_name)
  return icon_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GlobalStatus::set_allocated_icon_name(std::string* icon_name) {
  if (icon_name != nullptr) {
    
  } else {
    
  }
  icon_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), icon_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (icon_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    icon_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.GlobalStatus.icon_name)
}

// string icon_color = 3;
inline void GlobalStatus::clear_icon_color() {
  icon_color_.ClearToEmpty();
}
inline const std::string& GlobalStatus::icon_color() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.GlobalStatus.icon_color)
  return _internal_icon_color();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GlobalStatus::set_icon_color(ArgT0&& arg0, ArgT... args) {
 
 icon_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.GlobalStatus.icon_color)
}
inline std::string* GlobalStatus::mutable_icon_color() {
  std::string* _s = _internal_mutable_icon_color();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.GlobalStatus.icon_color)
  return _s;
}
inline const std::string& GlobalStatus::_internal_icon_color() const {
  return icon_color_.Get();
}
inline void GlobalStatus::_internal_set_icon_color(const std::string& value) {
  
  icon_color_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GlobalStatus::_internal_mutable_icon_color() {
  
  return icon_color_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GlobalStatus::release_icon_color() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.GlobalStatus.icon_color)
  return icon_color_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GlobalStatus::set_allocated_icon_color(std::string* icon_color) {
  if (icon_color != nullptr) {
    
  } else {
    
  }
  icon_color_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), icon_color,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (icon_color_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    icon_color_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.GlobalStatus.icon_color)
}

// -------------------------------------------------------------------

// ServiceStatus

// string name = 1;
inline void ServiceStatus::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& ServiceStatus::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.ServiceStatus.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ServiceStatus::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.ServiceStatus.name)
}
inline std::string* ServiceStatus::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.ServiceStatus.name)
  return _s;
}
inline const std::string& ServiceStatus::_internal_name() const {
  return name_.Get();
}
inline void ServiceStatus::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ServiceStatus::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ServiceStatus::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.ServiceStatus.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ServiceStatus::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.ServiceStatus.name)
}

// .carbon.frontend.status_bar.StatusLevel status_level = 2;
inline void ServiceStatus::clear_status_level() {
  status_level_ = 0;
}
inline ::carbon::frontend::status_bar::StatusLevel ServiceStatus::_internal_status_level() const {
  return static_cast< ::carbon::frontend::status_bar::StatusLevel >(status_level_);
}
inline ::carbon::frontend::status_bar::StatusLevel ServiceStatus::status_level() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.ServiceStatus.status_level)
  return _internal_status_level();
}
inline void ServiceStatus::_internal_set_status_level(::carbon::frontend::status_bar::StatusLevel value) {
  
  status_level_ = value;
}
inline void ServiceStatus::set_status_level(::carbon::frontend::status_bar::StatusLevel value) {
  _internal_set_status_level(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.ServiceStatus.status_level)
}

// -------------------------------------------------------------------

// ServerStatus

// .carbon.frontend.status_bar.StatusLevel status_level = 1;
inline void ServerStatus::clear_status_level() {
  status_level_ = 0;
}
inline ::carbon::frontend::status_bar::StatusLevel ServerStatus::_internal_status_level() const {
  return static_cast< ::carbon::frontend::status_bar::StatusLevel >(status_level_);
}
inline ::carbon::frontend::status_bar::StatusLevel ServerStatus::status_level() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.ServerStatus.status_level)
  return _internal_status_level();
}
inline void ServerStatus::_internal_set_status_level(::carbon::frontend::status_bar::StatusLevel value) {
  
  status_level_ = value;
}
inline void ServerStatus::set_status_level(::carbon::frontend::status_bar::StatusLevel value) {
  _internal_set_status_level(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.ServerStatus.status_level)
}

// repeated .carbon.frontend.status_bar.ServiceStatus service_status = 2;
inline int ServerStatus::_internal_service_status_size() const {
  return service_status_.size();
}
inline int ServerStatus::service_status_size() const {
  return _internal_service_status_size();
}
inline void ServerStatus::clear_service_status() {
  service_status_.Clear();
}
inline ::carbon::frontend::status_bar::ServiceStatus* ServerStatus::mutable_service_status(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.ServerStatus.service_status)
  return service_status_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::ServiceStatus >*
ServerStatus::mutable_service_status() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.status_bar.ServerStatus.service_status)
  return &service_status_;
}
inline const ::carbon::frontend::status_bar::ServiceStatus& ServerStatus::_internal_service_status(int index) const {
  return service_status_.Get(index);
}
inline const ::carbon::frontend::status_bar::ServiceStatus& ServerStatus::service_status(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.ServerStatus.service_status)
  return _internal_service_status(index);
}
inline ::carbon::frontend::status_bar::ServiceStatus* ServerStatus::_internal_add_service_status() {
  return service_status_.Add();
}
inline ::carbon::frontend::status_bar::ServiceStatus* ServerStatus::add_service_status() {
  ::carbon::frontend::status_bar::ServiceStatus* _add = _internal_add_service_status();
  // @@protoc_insertion_point(field_add:carbon.frontend.status_bar.ServerStatus.service_status)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::ServiceStatus >&
ServerStatus::service_status() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.status_bar.ServerStatus.service_status)
  return service_status_;
}

// -------------------------------------------------------------------

// TranslatedStatusMessageDetails

// string details_string_key = 1;
inline bool TranslatedStatusMessageDetails::_internal_has_details_string_key() const {
  return details_case() == kDetailsStringKey;
}
inline bool TranslatedStatusMessageDetails::has_details_string_key() const {
  return _internal_has_details_string_key();
}
inline void TranslatedStatusMessageDetails::set_has_details_string_key() {
  _oneof_case_[0] = kDetailsStringKey;
}
inline void TranslatedStatusMessageDetails::clear_details_string_key() {
  if (_internal_has_details_string_key()) {
    details_.details_string_key_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
    clear_has_details();
  }
}
inline const std::string& TranslatedStatusMessageDetails::details_string_key() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.TranslatedStatusMessageDetails.details_string_key)
  return _internal_details_string_key();
}
template <typename ArgT0, typename... ArgT>
inline void TranslatedStatusMessageDetails::set_details_string_key(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_details_string_key()) {
    clear_details();
    set_has_details_string_key();
    details_.details_string_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  details_.details_string_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.TranslatedStatusMessageDetails.details_string_key)
}
inline std::string* TranslatedStatusMessageDetails::mutable_details_string_key() {
  std::string* _s = _internal_mutable_details_string_key();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.TranslatedStatusMessageDetails.details_string_key)
  return _s;
}
inline const std::string& TranslatedStatusMessageDetails::_internal_details_string_key() const {
  if (_internal_has_details_string_key()) {
    return details_.details_string_key_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void TranslatedStatusMessageDetails::_internal_set_details_string_key(const std::string& value) {
  if (!_internal_has_details_string_key()) {
    clear_details();
    set_has_details_string_key();
    details_.details_string_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  details_.details_string_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TranslatedStatusMessageDetails::_internal_mutable_details_string_key() {
  if (!_internal_has_details_string_key()) {
    clear_details();
    set_has_details_string_key();
    details_.details_string_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return details_.details_string_key_.Mutable(
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TranslatedStatusMessageDetails::release_details_string_key() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.TranslatedStatusMessageDetails.details_string_key)
  if (_internal_has_details_string_key()) {
    clear_has_details();
    return details_.details_string_key_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
  } else {
    return nullptr;
  }
}
inline void TranslatedStatusMessageDetails::set_allocated_details_string_key(std::string* details_string_key) {
  if (has_details()) {
    clear_details();
  }
  if (details_string_key != nullptr) {
    set_has_details_string_key();
    details_.details_string_key_.UnsafeSetDefault(details_string_key);
    ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaForAllocation();
    if (arena != nullptr) {
      arena->Own(details_string_key);
    }
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.TranslatedStatusMessageDetails.details_string_key)
}

// .carbon.frontend.translation.DurationValue timer = 2;
inline bool TranslatedStatusMessageDetails::_internal_has_timer() const {
  return details_case() == kTimer;
}
inline bool TranslatedStatusMessageDetails::has_timer() const {
  return _internal_has_timer();
}
inline void TranslatedStatusMessageDetails::set_has_timer() {
  _oneof_case_[0] = kTimer;
}
inline ::carbon::frontend::translation::DurationValue* TranslatedStatusMessageDetails::release_timer() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.TranslatedStatusMessageDetails.timer)
  if (_internal_has_timer()) {
    clear_has_details();
      ::carbon::frontend::translation::DurationValue* temp = details_.timer_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    details_.timer_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::translation::DurationValue& TranslatedStatusMessageDetails::_internal_timer() const {
  return _internal_has_timer()
      ? *details_.timer_
      : reinterpret_cast< ::carbon::frontend::translation::DurationValue&>(::carbon::frontend::translation::_DurationValue_default_instance_);
}
inline const ::carbon::frontend::translation::DurationValue& TranslatedStatusMessageDetails::timer() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.TranslatedStatusMessageDetails.timer)
  return _internal_timer();
}
inline ::carbon::frontend::translation::DurationValue* TranslatedStatusMessageDetails::unsafe_arena_release_timer() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.frontend.status_bar.TranslatedStatusMessageDetails.timer)
  if (_internal_has_timer()) {
    clear_has_details();
    ::carbon::frontend::translation::DurationValue* temp = details_.timer_;
    details_.timer_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void TranslatedStatusMessageDetails::unsafe_arena_set_allocated_timer(::carbon::frontend::translation::DurationValue* timer) {
  clear_details();
  if (timer) {
    set_has_timer();
    details_.timer_ = timer;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.status_bar.TranslatedStatusMessageDetails.timer)
}
inline ::carbon::frontend::translation::DurationValue* TranslatedStatusMessageDetails::_internal_mutable_timer() {
  if (!_internal_has_timer()) {
    clear_details();
    set_has_timer();
    details_.timer_ = CreateMaybeMessage< ::carbon::frontend::translation::DurationValue >(GetArenaForAllocation());
  }
  return details_.timer_;
}
inline ::carbon::frontend::translation::DurationValue* TranslatedStatusMessageDetails::mutable_timer() {
  ::carbon::frontend::translation::DurationValue* _msg = _internal_mutable_timer();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.TranslatedStatusMessageDetails.timer)
  return _msg;
}

inline bool TranslatedStatusMessageDetails::has_details() const {
  return details_case() != DETAILS_NOT_SET;
}
inline void TranslatedStatusMessageDetails::clear_has_details() {
  _oneof_case_[0] = DETAILS_NOT_SET;
}
inline TranslatedStatusMessageDetails::DetailsCase TranslatedStatusMessageDetails::details_case() const {
  return TranslatedStatusMessageDetails::DetailsCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// TranslatedStatusMessage

// string prefix = 1;
inline void TranslatedStatusMessage::clear_prefix() {
  prefix_.ClearToEmpty();
}
inline const std::string& TranslatedStatusMessage::prefix() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.TranslatedStatusMessage.prefix)
  return _internal_prefix();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TranslatedStatusMessage::set_prefix(ArgT0&& arg0, ArgT... args) {
 
 prefix_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.TranslatedStatusMessage.prefix)
}
inline std::string* TranslatedStatusMessage::mutable_prefix() {
  std::string* _s = _internal_mutable_prefix();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.TranslatedStatusMessage.prefix)
  return _s;
}
inline const std::string& TranslatedStatusMessage::_internal_prefix() const {
  return prefix_.Get();
}
inline void TranslatedStatusMessage::_internal_set_prefix(const std::string& value) {
  
  prefix_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TranslatedStatusMessage::_internal_mutable_prefix() {
  
  return prefix_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TranslatedStatusMessage::release_prefix() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.TranslatedStatusMessage.prefix)
  return prefix_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TranslatedStatusMessage::set_allocated_prefix(std::string* prefix) {
  if (prefix != nullptr) {
    
  } else {
    
  }
  prefix_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), prefix,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (prefix_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    prefix_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.TranslatedStatusMessage.prefix)
}

// .carbon.frontend.status_bar.TranslatedStatusMessageDetails details = 2;
inline bool TranslatedStatusMessage::_internal_has_details() const {
  return this != internal_default_instance() && details_ != nullptr;
}
inline bool TranslatedStatusMessage::has_details() const {
  return _internal_has_details();
}
inline void TranslatedStatusMessage::clear_details() {
  if (GetArenaForAllocation() == nullptr && details_ != nullptr) {
    delete details_;
  }
  details_ = nullptr;
}
inline const ::carbon::frontend::status_bar::TranslatedStatusMessageDetails& TranslatedStatusMessage::_internal_details() const {
  const ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* p = details_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::status_bar::TranslatedStatusMessageDetails&>(
      ::carbon::frontend::status_bar::_TranslatedStatusMessageDetails_default_instance_);
}
inline const ::carbon::frontend::status_bar::TranslatedStatusMessageDetails& TranslatedStatusMessage::details() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.TranslatedStatusMessage.details)
  return _internal_details();
}
inline void TranslatedStatusMessage::unsafe_arena_set_allocated_details(
    ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* details) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(details_);
  }
  details_ = details;
  if (details) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.status_bar.TranslatedStatusMessage.details)
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* TranslatedStatusMessage::release_details() {
  
  ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* temp = details_;
  details_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* TranslatedStatusMessage::unsafe_arena_release_details() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.TranslatedStatusMessage.details)
  
  ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* temp = details_;
  details_ = nullptr;
  return temp;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* TranslatedStatusMessage::_internal_mutable_details() {
  
  if (details_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::status_bar::TranslatedStatusMessageDetails>(GetArenaForAllocation());
    details_ = p;
  }
  return details_;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* TranslatedStatusMessage::mutable_details() {
  ::carbon::frontend::status_bar::TranslatedStatusMessageDetails* _msg = _internal_mutable_details();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.TranslatedStatusMessage.details)
  return _msg;
}
inline void TranslatedStatusMessage::set_allocated_details(::carbon::frontend::status_bar::TranslatedStatusMessageDetails* details) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete details_;
  }
  if (details) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::status_bar::TranslatedStatusMessageDetails>::GetOwningArena(details);
    if (message_arena != submessage_arena) {
      details = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, details, submessage_arena);
    }
    
  } else {
    
  }
  details_ = details;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.TranslatedStatusMessage.details)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// StatusBarMessage

// .carbon.frontend.util.Timestamp ts = 1;
inline bool StatusBarMessage::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool StatusBarMessage::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& StatusBarMessage::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& StatusBarMessage::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.ts)
  return _internal_ts();
}
inline void StatusBarMessage::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.status_bar.StatusBarMessage.ts)
}
inline ::carbon::frontend::util::Timestamp* StatusBarMessage::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* StatusBarMessage::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.StatusBarMessage.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* StatusBarMessage::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* StatusBarMessage::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.StatusBarMessage.ts)
  return _msg;
}
inline void StatusBarMessage::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.StatusBarMessage.ts)
}

// bool lasers_enabled = 3;
inline void StatusBarMessage::clear_lasers_enabled() {
  lasers_enabled_ = false;
}
inline bool StatusBarMessage::_internal_lasers_enabled() const {
  return lasers_enabled_;
}
inline bool StatusBarMessage::lasers_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.lasers_enabled)
  return _internal_lasers_enabled();
}
inline void StatusBarMessage::_internal_set_lasers_enabled(bool value) {
  
  lasers_enabled_ = value;
}
inline void StatusBarMessage::set_lasers_enabled(bool value) {
  _internal_set_lasers_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.StatusBarMessage.lasers_enabled)
}

// bool weeding_enabled = 4;
inline void StatusBarMessage::clear_weeding_enabled() {
  weeding_enabled_ = false;
}
inline bool StatusBarMessage::_internal_weeding_enabled() const {
  return weeding_enabled_;
}
inline bool StatusBarMessage::weeding_enabled() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.weeding_enabled)
  return _internal_weeding_enabled();
}
inline void StatusBarMessage::_internal_set_weeding_enabled(bool value) {
  
  weeding_enabled_ = value;
}
inline void StatusBarMessage::set_weeding_enabled(bool value) {
  _internal_set_weeding_enabled(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.StatusBarMessage.weeding_enabled)
}

// .carbon.frontend.status_bar.Status status_level = 5;
inline void StatusBarMessage::clear_status_level() {
  status_level_ = 0;
}
inline ::carbon::frontend::status_bar::Status StatusBarMessage::_internal_status_level() const {
  return static_cast< ::carbon::frontend::status_bar::Status >(status_level_);
}
inline ::carbon::frontend::status_bar::Status StatusBarMessage::status_level() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.status_level)
  return _internal_status_level();
}
inline void StatusBarMessage::_internal_set_status_level(::carbon::frontend::status_bar::Status value) {
  
  status_level_ = value;
}
inline void StatusBarMessage::set_status_level(::carbon::frontend::status_bar::Status value) {
  _internal_set_status_level(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.StatusBarMessage.status_level)
}

// string status_message = 6;
inline void StatusBarMessage::clear_status_message() {
  status_message_.ClearToEmpty();
}
inline const std::string& StatusBarMessage::status_message() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.status_message)
  return _internal_status_message();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StatusBarMessage::set_status_message(ArgT0&& arg0, ArgT... args) {
 
 status_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.StatusBarMessage.status_message)
}
inline std::string* StatusBarMessage::mutable_status_message() {
  std::string* _s = _internal_mutable_status_message();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.StatusBarMessage.status_message)
  return _s;
}
inline const std::string& StatusBarMessage::_internal_status_message() const {
  return status_message_.Get();
}
inline void StatusBarMessage::_internal_set_status_message(const std::string& value) {
  
  status_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StatusBarMessage::_internal_mutable_status_message() {
  
  return status_message_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StatusBarMessage::release_status_message() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.StatusBarMessage.status_message)
  return status_message_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StatusBarMessage::set_allocated_status_message(std::string* status_message) {
  if (status_message != nullptr) {
    
  } else {
    
  }
  status_message_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), status_message,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (status_message_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    status_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.StatusBarMessage.status_message)
}

// string serial = 7;
inline void StatusBarMessage::clear_serial() {
  serial_.ClearToEmpty();
}
inline const std::string& StatusBarMessage::serial() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.serial)
  return _internal_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void StatusBarMessage::set_serial(ArgT0&& arg0, ArgT... args) {
 
 serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.StatusBarMessage.serial)
}
inline std::string* StatusBarMessage::mutable_serial() {
  std::string* _s = _internal_mutable_serial();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.StatusBarMessage.serial)
  return _s;
}
inline const std::string& StatusBarMessage::_internal_serial() const {
  return serial_.Get();
}
inline void StatusBarMessage::_internal_set_serial(const std::string& value) {
  
  serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* StatusBarMessage::_internal_mutable_serial() {
  
  return serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* StatusBarMessage::release_serial() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.StatusBarMessage.serial)
  return serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void StatusBarMessage::set_allocated_serial(std::string* serial) {
  if (serial != nullptr) {
    
  } else {
    
  }
  serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.StatusBarMessage.serial)
}

// map<int32, .carbon.frontend.status_bar.ServerStatus> row_status = 8;
inline int StatusBarMessage::_internal_row_status_size() const {
  return row_status_.size();
}
inline int StatusBarMessage::row_status_size() const {
  return _internal_row_status_size();
}
inline void StatusBarMessage::clear_row_status() {
  row_status_.Clear();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >&
StatusBarMessage::_internal_row_status() const {
  return row_status_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >&
StatusBarMessage::row_status() const {
  // @@protoc_insertion_point(field_map:carbon.frontend.status_bar.StatusBarMessage.row_status)
  return _internal_row_status();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >*
StatusBarMessage::_internal_mutable_row_status() {
  return row_status_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< int32_t, ::carbon::frontend::status_bar::ServerStatus >*
StatusBarMessage::mutable_row_status() {
  // @@protoc_insertion_point(field_mutable_map:carbon.frontend.status_bar.StatusBarMessage.row_status)
  return _internal_mutable_row_status();
}

// .carbon.frontend.status_bar.ServerStatus command_status = 9;
inline bool StatusBarMessage::_internal_has_command_status() const {
  return this != internal_default_instance() && command_status_ != nullptr;
}
inline bool StatusBarMessage::has_command_status() const {
  return _internal_has_command_status();
}
inline void StatusBarMessage::clear_command_status() {
  if (GetArenaForAllocation() == nullptr && command_status_ != nullptr) {
    delete command_status_;
  }
  command_status_ = nullptr;
}
inline const ::carbon::frontend::status_bar::ServerStatus& StatusBarMessage::_internal_command_status() const {
  const ::carbon::frontend::status_bar::ServerStatus* p = command_status_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::status_bar::ServerStatus&>(
      ::carbon::frontend::status_bar::_ServerStatus_default_instance_);
}
inline const ::carbon::frontend::status_bar::ServerStatus& StatusBarMessage::command_status() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.command_status)
  return _internal_command_status();
}
inline void StatusBarMessage::unsafe_arena_set_allocated_command_status(
    ::carbon::frontend::status_bar::ServerStatus* command_status) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(command_status_);
  }
  command_status_ = command_status;
  if (command_status) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.status_bar.StatusBarMessage.command_status)
}
inline ::carbon::frontend::status_bar::ServerStatus* StatusBarMessage::release_command_status() {
  
  ::carbon::frontend::status_bar::ServerStatus* temp = command_status_;
  command_status_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::status_bar::ServerStatus* StatusBarMessage::unsafe_arena_release_command_status() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.StatusBarMessage.command_status)
  
  ::carbon::frontend::status_bar::ServerStatus* temp = command_status_;
  command_status_ = nullptr;
  return temp;
}
inline ::carbon::frontend::status_bar::ServerStatus* StatusBarMessage::_internal_mutable_command_status() {
  
  if (command_status_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::status_bar::ServerStatus>(GetArenaForAllocation());
    command_status_ = p;
  }
  return command_status_;
}
inline ::carbon::frontend::status_bar::ServerStatus* StatusBarMessage::mutable_command_status() {
  ::carbon::frontend::status_bar::ServerStatus* _msg = _internal_mutable_command_status();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.StatusBarMessage.command_status)
  return _msg;
}
inline void StatusBarMessage::set_allocated_command_status(::carbon::frontend::status_bar::ServerStatus* command_status) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete command_status_;
  }
  if (command_status) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::status_bar::ServerStatus>::GetOwningArena(command_status);
    if (message_arena != submessage_arena) {
      command_status = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, command_status, submessage_arena);
    }
    
  } else {
    
  }
  command_status_ = command_status;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.StatusBarMessage.command_status)
}

// repeated .carbon.frontend.status_bar.GlobalStatus global_statuses = 10;
inline int StatusBarMessage::_internal_global_statuses_size() const {
  return global_statuses_.size();
}
inline int StatusBarMessage::global_statuses_size() const {
  return _internal_global_statuses_size();
}
inline void StatusBarMessage::clear_global_statuses() {
  global_statuses_.Clear();
}
inline ::carbon::frontend::status_bar::GlobalStatus* StatusBarMessage::mutable_global_statuses(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.StatusBarMessage.global_statuses)
  return global_statuses_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::GlobalStatus >*
StatusBarMessage::mutable_global_statuses() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.status_bar.StatusBarMessage.global_statuses)
  return &global_statuses_;
}
inline const ::carbon::frontend::status_bar::GlobalStatus& StatusBarMessage::_internal_global_statuses(int index) const {
  return global_statuses_.Get(index);
}
inline const ::carbon::frontend::status_bar::GlobalStatus& StatusBarMessage::global_statuses(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.global_statuses)
  return _internal_global_statuses(index);
}
inline ::carbon::frontend::status_bar::GlobalStatus* StatusBarMessage::_internal_add_global_statuses() {
  return global_statuses_.Add();
}
inline ::carbon::frontend::status_bar::GlobalStatus* StatusBarMessage::add_global_statuses() {
  ::carbon::frontend::status_bar::GlobalStatus* _add = _internal_add_global_statuses();
  // @@protoc_insertion_point(field_add:carbon.frontend.status_bar.StatusBarMessage.global_statuses)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::status_bar::GlobalStatus >&
StatusBarMessage::global_statuses() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.status_bar.StatusBarMessage.global_statuses)
  return global_statuses_;
}

// .carbon.frontend.status_bar.TranslatedStatusMessage translated_status_message = 11;
inline bool StatusBarMessage::_internal_has_translated_status_message() const {
  return this != internal_default_instance() && translated_status_message_ != nullptr;
}
inline bool StatusBarMessage::has_translated_status_message() const {
  return _internal_has_translated_status_message();
}
inline void StatusBarMessage::clear_translated_status_message() {
  if (GetArenaForAllocation() == nullptr && translated_status_message_ != nullptr) {
    delete translated_status_message_;
  }
  translated_status_message_ = nullptr;
}
inline const ::carbon::frontend::status_bar::TranslatedStatusMessage& StatusBarMessage::_internal_translated_status_message() const {
  const ::carbon::frontend::status_bar::TranslatedStatusMessage* p = translated_status_message_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::status_bar::TranslatedStatusMessage&>(
      ::carbon::frontend::status_bar::_TranslatedStatusMessage_default_instance_);
}
inline const ::carbon::frontend::status_bar::TranslatedStatusMessage& StatusBarMessage::translated_status_message() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.StatusBarMessage.translated_status_message)
  return _internal_translated_status_message();
}
inline void StatusBarMessage::unsafe_arena_set_allocated_translated_status_message(
    ::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(translated_status_message_);
  }
  translated_status_message_ = translated_status_message;
  if (translated_status_message) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.status_bar.StatusBarMessage.translated_status_message)
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessage* StatusBarMessage::release_translated_status_message() {
  
  ::carbon::frontend::status_bar::TranslatedStatusMessage* temp = translated_status_message_;
  translated_status_message_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessage* StatusBarMessage::unsafe_arena_release_translated_status_message() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.StatusBarMessage.translated_status_message)
  
  ::carbon::frontend::status_bar::TranslatedStatusMessage* temp = translated_status_message_;
  translated_status_message_ = nullptr;
  return temp;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessage* StatusBarMessage::_internal_mutable_translated_status_message() {
  
  if (translated_status_message_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::status_bar::TranslatedStatusMessage>(GetArenaForAllocation());
    translated_status_message_ = p;
  }
  return translated_status_message_;
}
inline ::carbon::frontend::status_bar::TranslatedStatusMessage* StatusBarMessage::mutable_translated_status_message() {
  ::carbon::frontend::status_bar::TranslatedStatusMessage* _msg = _internal_mutable_translated_status_message();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.StatusBarMessage.translated_status_message)
  return _msg;
}
inline void StatusBarMessage::set_allocated_translated_status_message(::carbon::frontend::status_bar::TranslatedStatusMessage* translated_status_message) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete translated_status_message_;
  }
  if (translated_status_message) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::status_bar::TranslatedStatusMessage>::GetOwningArena(translated_status_message);
    if (message_arena != submessage_arena) {
      translated_status_message = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, translated_status_message, submessage_arena);
    }
    
  } else {
    
  }
  translated_status_message_ = translated_status_message;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.StatusBarMessage.translated_status_message)
}

// -------------------------------------------------------------------

// ReportIssueRequest

// string description = 1;
inline void ReportIssueRequest::clear_description() {
  description_.ClearToEmpty();
}
inline const std::string& ReportIssueRequest::description() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.ReportIssueRequest.description)
  return _internal_description();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ReportIssueRequest::set_description(ArgT0&& arg0, ArgT... args) {
 
 description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.ReportIssueRequest.description)
}
inline std::string* ReportIssueRequest::mutable_description() {
  std::string* _s = _internal_mutable_description();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.ReportIssueRequest.description)
  return _s;
}
inline const std::string& ReportIssueRequest::_internal_description() const {
  return description_.Get();
}
inline void ReportIssueRequest::_internal_set_description(const std::string& value) {
  
  description_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ReportIssueRequest::_internal_mutable_description() {
  
  return description_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ReportIssueRequest::release_description() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.ReportIssueRequest.description)
  return description_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ReportIssueRequest::set_allocated_description(std::string* description) {
  if (description != nullptr) {
    
  } else {
    
  }
  description_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), description,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (description_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    description_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.ReportIssueRequest.description)
}

// string phone_number = 2;
inline void ReportIssueRequest::clear_phone_number() {
  phone_number_.ClearToEmpty();
}
inline const std::string& ReportIssueRequest::phone_number() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.ReportIssueRequest.phone_number)
  return _internal_phone_number();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ReportIssueRequest::set_phone_number(ArgT0&& arg0, ArgT... args) {
 
 phone_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.ReportIssueRequest.phone_number)
}
inline std::string* ReportIssueRequest::mutable_phone_number() {
  std::string* _s = _internal_mutable_phone_number();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.ReportIssueRequest.phone_number)
  return _s;
}
inline const std::string& ReportIssueRequest::_internal_phone_number() const {
  return phone_number_.Get();
}
inline void ReportIssueRequest::_internal_set_phone_number(const std::string& value) {
  
  phone_number_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ReportIssueRequest::_internal_mutable_phone_number() {
  
  return phone_number_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ReportIssueRequest::release_phone_number() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.ReportIssueRequest.phone_number)
  return phone_number_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ReportIssueRequest::set_allocated_phone_number(std::string* phone_number) {
  if (phone_number != nullptr) {
    
  } else {
    
  }
  phone_number_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), phone_number,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (phone_number_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    phone_number_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.ReportIssueRequest.phone_number)
}

// -------------------------------------------------------------------

// SupportPhoneMessage

// string support_phone = 1;
inline void SupportPhoneMessage::clear_support_phone() {
  support_phone_.ClearToEmpty();
}
inline const std::string& SupportPhoneMessage::support_phone() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.status_bar.SupportPhoneMessage.support_phone)
  return _internal_support_phone();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SupportPhoneMessage::set_support_phone(ArgT0&& arg0, ArgT... args) {
 
 support_phone_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.status_bar.SupportPhoneMessage.support_phone)
}
inline std::string* SupportPhoneMessage::mutable_support_phone() {
  std::string* _s = _internal_mutable_support_phone();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.status_bar.SupportPhoneMessage.support_phone)
  return _s;
}
inline const std::string& SupportPhoneMessage::_internal_support_phone() const {
  return support_phone_.Get();
}
inline void SupportPhoneMessage::_internal_set_support_phone(const std::string& value) {
  
  support_phone_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SupportPhoneMessage::_internal_mutable_support_phone() {
  
  return support_phone_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SupportPhoneMessage::release_support_phone() {
  // @@protoc_insertion_point(field_release:carbon.frontend.status_bar.SupportPhoneMessage.support_phone)
  return support_phone_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SupportPhoneMessage::set_allocated_support_phone(std::string* support_phone) {
  if (support_phone != nullptr) {
    
  } else {
    
  }
  support_phone_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), support_phone,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (support_phone_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    support_phone_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.status_bar.SupportPhoneMessage.support_phone)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace status_bar
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::status_bar::Status> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::status_bar::Status>() {
  return ::carbon::frontend::status_bar::Status_descriptor();
}
template <> struct is_proto_enum< ::carbon::frontend::status_bar::StatusLevel> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::status_bar::StatusLevel>() {
  return ::carbon::frontend::status_bar::StatusLevel_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fstatus_5fbar_2eproto

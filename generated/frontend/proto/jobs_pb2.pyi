"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.frontend.proto.profile_sync_pb2 import (
    ProfileTypeValue as frontend___proto___profile_sync_pb2___ProfileTypeValue,
)

from generated.frontend.proto.util_pb2 import (
    Timestamp as frontend___proto___util_pb2___Timestamp,
)

from generated.frontend.proto.weeding_diagnostics_pb2 import (
    ConfigNodeSnapshot as frontend___proto___weeding_diagnostics_pb2___ConfigNodeSnapshot,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedComposite<PERSON>ieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.metrics.proto.metrics_aggregator_service_pb2 import (
    Metrics as metrics___proto___metrics_aggregator_service_pb2___Metrics,
)

from typing import (
    Iterable as typing___Iterable,
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class JobDescription(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...
    name: typing___Text = ...
    timestampMs: builtin___int = ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        timestampMs : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId",u"name",b"name",u"timestampMs",b"timestampMs"]) -> None: ...
type___JobDescription = JobDescription

class ActiveProfile(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    profile_type: frontend___proto___profile_sync_pb2___ProfileTypeValue = ...
    id: typing___Text = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        profile_type : typing___Optional[frontend___proto___profile_sync_pb2___ProfileTypeValue] = None,
        id : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"id",b"id",u"name",b"name",u"profile_type",b"profile_type"]) -> None: ...
type___ActiveProfile = ActiveProfile

class Job(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ActiveProfilesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: builtin___int = ...

        @property
        def value(self) -> type___ActiveProfile: ...

        def __init__(self,
            *,
            key : typing___Optional[builtin___int] = None,
            value : typing___Optional[type___ActiveProfile] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ActiveProfilesEntry = ActiveProfilesEntry

    bandingProfile: typing___Text = ...
    thinningProfile: typing___Text = ...
    stopTimeMs: builtin___int = ...
    lastUpdateTimeMs: builtin___int = ...
    expectedAcreage: builtin___float = ...
    completed: builtin___bool = ...
    almanac: typing___Text = ...
    discriminator: typing___Text = ...
    crop_id: typing___Text = ...
    bandingProfileUUID: typing___Text = ...
    thinningProfileUUID: typing___Text = ...
    almanacProfileUUID: typing___Text = ...
    discriminatorProfileUUID: typing___Text = ...
    lastUsedTimeMs: builtin___int = ...

    @property
    def jobDescription(self) -> type___JobDescription: ...

    @property
    def active_profiles(self) -> google___protobuf___internal___containers___MessageMap[builtin___int, type___ActiveProfile]: ...

    def __init__(self,
        *,
        jobDescription : typing___Optional[type___JobDescription] = None,
        bandingProfile : typing___Optional[typing___Text] = None,
        thinningProfile : typing___Optional[typing___Text] = None,
        stopTimeMs : typing___Optional[builtin___int] = None,
        lastUpdateTimeMs : typing___Optional[builtin___int] = None,
        expectedAcreage : typing___Optional[builtin___float] = None,
        completed : typing___Optional[builtin___bool] = None,
        almanac : typing___Optional[typing___Text] = None,
        discriminator : typing___Optional[typing___Text] = None,
        crop_id : typing___Optional[typing___Text] = None,
        bandingProfileUUID : typing___Optional[typing___Text] = None,
        thinningProfileUUID : typing___Optional[typing___Text] = None,
        almanacProfileUUID : typing___Optional[typing___Text] = None,
        discriminatorProfileUUID : typing___Optional[typing___Text] = None,
        active_profiles : typing___Optional[typing___Mapping[builtin___int, type___ActiveProfile]] = None,
        lastUsedTimeMs : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"jobDescription",b"jobDescription"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active_profiles",b"active_profiles",u"almanac",b"almanac",u"almanacProfileUUID",b"almanacProfileUUID",u"bandingProfile",b"bandingProfile",u"bandingProfileUUID",b"bandingProfileUUID",u"completed",b"completed",u"crop_id",b"crop_id",u"discriminator",b"discriminator",u"discriminatorProfileUUID",b"discriminatorProfileUUID",u"expectedAcreage",b"expectedAcreage",u"jobDescription",b"jobDescription",u"lastUpdateTimeMs",b"lastUpdateTimeMs",u"lastUsedTimeMs",b"lastUsedTimeMs",u"stopTimeMs",b"stopTimeMs",u"thinningProfile",b"thinningProfile",u"thinningProfileUUID",b"thinningProfileUUID"]) -> None: ...
type___Job = Job

class CreateJobRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...
    active: builtin___bool = ...
    expectedAcreage: builtin___float = ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        active : typing___Optional[builtin___bool] = None,
        expectedAcreage : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"active",b"active",u"expectedAcreage",b"expectedAcreage",u"name",b"name"]) -> None: ...
type___CreateJobRequest = CreateJobRequest

class CreateJobResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId"]) -> None: ...
type___CreateJobResponse = CreateJobResponse

class UpdateJobRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    expectedAcreage: builtin___float = ...

    @property
    def jobDescription(self) -> type___JobDescription: ...

    def __init__(self,
        *,
        jobDescription : typing___Optional[type___JobDescription] = None,
        expectedAcreage : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"jobDescription",b"jobDescription"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"expectedAcreage",b"expectedAcreage",u"jobDescription",b"jobDescription"]) -> None: ...
type___UpdateJobRequest = UpdateJobRequest

class GetNextJobsRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def timestamp(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        timestamp : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> None: ...
type___GetNextJobsRequest = GetNextJobsRequest

class JobWithMetrics(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def job(self) -> type___Job: ...

    @property
    def metrics(self) -> metrics___proto___metrics_aggregator_service_pb2___Metrics: ...

    def __init__(self,
        *,
        job : typing___Optional[type___Job] = None,
        metrics : typing___Optional[metrics___proto___metrics_aggregator_service_pb2___Metrics] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"job",b"job",u"metrics",b"metrics"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"job",b"job",u"metrics",b"metrics"]) -> None: ...
type___JobWithMetrics = JobWithMetrics

class GetNextJobsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    activeJobId: typing___Text = ...

    @property
    def jobs(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___JobWithMetrics]: ...

    @property
    def timestamp(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        jobs : typing___Optional[typing___Iterable[type___JobWithMetrics]] = None,
        activeJobId : typing___Optional[typing___Text] = None,
        timestamp : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"activeJobId",b"activeJobId",u"jobs",b"jobs",u"timestamp",b"timestamp"]) -> None: ...
type___GetNextJobsResponse = GetNextJobsResponse

class GetNextActiveJobIdRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def timestamp(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        timestamp : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> None: ...
type___GetNextActiveJobIdRequest = GetNextActiveJobIdRequest

class GetNextActiveJobIdResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    activeJobId: typing___Text = ...

    @property
    def timestamp(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        activeJobId : typing___Optional[typing___Text] = None,
        timestamp : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"timestamp",b"timestamp"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"activeJobId",b"activeJobId",u"timestamp",b"timestamp"]) -> None: ...
type___GetNextActiveJobIdResponse = GetNextActiveJobIdResponse

class GetJobRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId"]) -> None: ...
type___GetJobRequest = GetJobRequest

class GetJobResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def job(self) -> type___Job: ...

    def __init__(self,
        *,
        job : typing___Optional[type___Job] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"job",b"job"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"job",b"job"]) -> None: ...
type___GetJobResponse = GetJobResponse

class StartJobRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId"]) -> None: ...
type___StartJobRequest = StartJobRequest

class GetConfigDumpRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId"]) -> None: ...
type___GetConfigDumpRequest = GetConfigDumpRequest

class GetConfigDumpResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def rootConfig(self) -> frontend___proto___weeding_diagnostics_pb2___ConfigNodeSnapshot: ...

    def __init__(self,
        *,
        rootConfig : typing___Optional[frontend___proto___weeding_diagnostics_pb2___ConfigNodeSnapshot] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"rootConfig",b"rootConfig"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"rootConfig",b"rootConfig"]) -> None: ...
type___GetConfigDumpResponse = GetConfigDumpResponse

class GetActiveJobMetricsResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def jobMetrics(self) -> metrics___proto___metrics_aggregator_service_pb2___Metrics: ...

    def __init__(self,
        *,
        jobMetrics : typing___Optional[metrics___proto___metrics_aggregator_service_pb2___Metrics] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"jobMetrics",b"jobMetrics"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobMetrics",b"jobMetrics"]) -> None: ...
type___GetActiveJobMetricsResponse = GetActiveJobMetricsResponse

class DeleteJobRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId"]) -> None: ...
type___DeleteJobRequest = DeleteJobRequest

class MarkJobCompletedRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId"]) -> None: ...
type___MarkJobCompletedRequest = MarkJobCompletedRequest

class MarkJobIncompleteRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    def __init__(self,
        *,
        jobId : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId"]) -> None: ...
type___MarkJobIncompleteRequest = MarkJobIncompleteRequest

class GetNextJobRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    jobId: typing___Text = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        jobId : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"jobId",b"jobId",u"ts",b"ts"]) -> None: ...
type___GetNextJobRequest = GetNextJobRequest

class GetNextJobResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def ts(self) -> frontend___proto___util_pb2___Timestamp: ...

    @property
    def job(self) -> type___JobWithMetrics: ...

    def __init__(self,
        *,
        ts : typing___Optional[frontend___proto___util_pb2___Timestamp] = None,
        job : typing___Optional[type___JobWithMetrics] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"job",b"job",u"ts",b"ts"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"job",b"job",u"ts",b"ts"]) -> None: ...
type___GetNextJobResponse = GetNextJobResponse

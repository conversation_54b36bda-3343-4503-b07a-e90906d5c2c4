# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import calibration_pb2 as frontend_dot_proto_dot_calibration__pb2
from generated.frontend.proto import camera_pb2 as frontend_dot_proto_dot_camera__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class CalibrationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.StartColorCalibration = channel.unary_unary(
                '/carbon.frontend.color_calibration.CalibrationService/StartColorCalibration',
                request_serializer=frontend_dot_proto_dot_camera__pb2.CameraRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_calibration__pb2.ColorCalibrationValues.FromString,
                )
        self.SaveColorCalibration = channel.unary_unary(
                '/carbon.frontend.color_calibration.CalibrationService/SaveColorCalibration',
                request_serializer=frontend_dot_proto_dot_calibration__pb2.ColorCalibrationValues.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )


class CalibrationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def StartColorCalibration(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SaveColorCalibration(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CalibrationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'StartColorCalibration': grpc.unary_unary_rpc_method_handler(
                    servicer.StartColorCalibration,
                    request_deserializer=frontend_dot_proto_dot_camera__pb2.CameraRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_calibration__pb2.ColorCalibrationValues.SerializeToString,
            ),
            'SaveColorCalibration': grpc.unary_unary_rpc_method_handler(
                    servicer.SaveColorCalibration,
                    request_deserializer=frontend_dot_proto_dot_calibration__pb2.ColorCalibrationValues.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.color_calibration.CalibrationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CalibrationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def StartColorCalibration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.color_calibration.CalibrationService/StartColorCalibration',
            frontend_dot_proto_dot_camera__pb2.CameraRequest.SerializeToString,
            frontend_dot_proto_dot_calibration__pb2.ColorCalibrationValues.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SaveColorCalibration(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.color_calibration.CalibrationService/SaveColorCalibration',
            frontend_dot_proto_dot_calibration__pb2.ColorCalibrationValues.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

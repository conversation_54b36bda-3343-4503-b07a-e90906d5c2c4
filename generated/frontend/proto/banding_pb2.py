# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/banding.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.core.controls.exterminator.controllers.aimbot.process.proto import aimbot_pb2 as core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2
from generated.weed_tracking.proto import weed_tracking_pb2 as weed__tracking_dot_proto_dot_weed__tracking__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/banding.proto',
  package='carbon.frontend.banding',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1c\x66rontend/proto/banding.proto\x12\x17\x63\x61rbon.frontend.banding\x1a\x19\x66rontend/proto/util.proto\x1aHcore/controls/exterminator/controllers/aimbot/process/proto/aimbot.proto\x1a\'weed_tracking/proto/weed_tracking.proto\"J\n\nBandingRow\x12\x0e\n\x06row_id\x18\x01 \x01(\x05\x12,\n\x05\x62\x61nds\x18\x02 \x03(\x0b\x32\x1d.weed_tracking.BandDefinition\"[\n\nBandingDef\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x31\n\x04rows\x18\x02 \x03(\x0b\x32#.carbon.frontend.banding.BandingRow\x12\x0c\n\x04uuid\x18\x03 \x01(\t\"c\n\x15SaveBandingDefRequest\x12\x37\n\nbandingDef\x18\x01 \x01(\x0b\x32#.carbon.frontend.banding.BandingDef\x12\x11\n\tsetActive\x18\x02 \x01(\x08\"\x81\x01\n\x17LoadBandingDefsResponse\x12\x38\n\x0b\x62\x61ndingDefs\x18\x01 \x03(\x0b\x32#.carbon.frontend.banding.BandingDef\x12\x15\n\tactiveDef\x18\x02 \x01(\tB\x02\x18\x01\x12\x15\n\ractiveDefUUID\x18\x03 \x01(\t\"<\n\x1aSetActiveBandingDefRequest\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\x0c\n\x04uuid\x18\x02 \x01(\t\"=\n\x1bGetActiveBandingDefResponse\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\x0c\n\x04uuid\x18\x02 \x01(\t\"9\n\x17\x44\x65leteBandingDefRequest\x12\x10\n\x04name\x18\x01 \x01(\tB\x02\x18\x01\x12\x0c\n\x04uuid\x18\x02 \x01(\t\"N\n\x11VisualizationData\x12\x0c\n\x04x_mm\x18\x01 \x01(\x05\x12\x0c\n\x04y_mm\x18\x02 \x01(\x05\x12\x0c\n\x04z_mm\x18\x03 \x01(\x05\x12\x0f\n\x07is_weed\x18\x04 \x01(\x08\"\xf3\x01\n\x1fGetNextVisualizationDataRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x0e\n\x06row_id\x18\x02 \x01(\x05\x12M\n\x10types_to_include\x18\x03 \x03(\x0e\x32\x33.carbon.frontend.banding.VisualizationTypeToInclude\x12\x44\n\x11threshold_filters\x18\x04 \x01(\x0b\x32).carbon.frontend.banding.ThresholdFilters\"\xb7\x01\n GetNextVisualizationDataResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x38\n\x04\x64\x61ta\x18\x02 \x03(\x0b\x32*.carbon.frontend.banding.VisualizationData\x12,\n\x05\x62\x61nds\x18\x03 \x03(\x0b\x32\x1d.weed_tracking.BandDefinition\"\x82\x01\n!GetNextVisualizationData2Response\x12\x30\n\x04\x64\x61ta\x18\x01 \x01(\x0b\x32\".weed_tracking.DiagnosticsSnapshot\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"&\n\x14GetDimensionsRequest\x12\x0e\n\x06row_id\x18\x01 \x01(\x05\"+\n\x18SetBandingEnabledRequest\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"\x1b\n\x19SetBandingEnabledResponse\"+\n\x18IsBandingEnabledResponse\x12\x0f\n\x07\x65nabled\x18\x01 \x01(\x08\"\xe3\x01\n GetVisualizationMetadataResponse\x12~\n\x1d\x63rop_safety_radius_mm_per_row\x18\x01 \x03(\x0b\x32W.carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry\x1a?\n\x1d\x43ropSafetyRadiusMmPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\r\n\x05value\x18\x02 \x01(\x02:\x02\x38\x01\"\xc0\x01\n\x0fThresholdFilter\x12\x38\n\x07weeding\x18\x01 \x01(\x0e\x32\'.carbon.frontend.banding.ThresholdState\x12\x39\n\x08thinning\x18\x02 \x01(\x0e\x32\'.carbon.frontend.banding.ThresholdState\x12\x38\n\x07\x62\x61nding\x18\x03 \x01(\x0e\x32\'.carbon.frontend.banding.ThresholdState\"\x82\x01\n\x10ThresholdFilters\x12\x36\n\x04\x63rop\x18\x01 \x01(\x0b\x32(.carbon.frontend.banding.ThresholdFilter\x12\x36\n\x04weed\x18\x02 \x01(\x0b\x32(.carbon.frontend.banding.ThresholdFilter\"\xed\x01\n)GetNextVisualizationDataForAllRowsRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12M\n\x10types_to_include\x18\x02 \x03(\x0e\x32\x33.carbon.frontend.banding.VisualizationTypeToInclude\x12\x44\n\x11threshold_filters\x18\x03 \x01(\x0b\x32).carbon.frontend.banding.ThresholdFilters\"\xea\x02\n*GetNextVisualizationDataForAllRowsResponse\x12i\n\x0c\x64\x61ta_per_row\x18\x01 \x03(\x0b\x32S.carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry\x12+\n\x02ts\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12M\n\x10types_to_include\x18\x03 \x03(\x0e\x32\x33.carbon.frontend.banding.VisualizationTypeToInclude\x1aU\n\x0f\x44\x61taPerRowEntry\x12\x0b\n\x03key\x18\x01 \x01(\x05\x12\x31\n\x05value\x18\x02 \x01(\x0b\x32\".weed_tracking.DiagnosticsSnapshot:\x02\x38\x01\"\xdb\x01\n\x1bGetNextBandingStateResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x38\n\x0b\x62\x61ndingDefs\x18\x02 \x03(\x0b\x32#.carbon.frontend.banding.BandingDef\x12\x15\n\ractiveDefUUID\x18\x03 \x01(\t\x12\x1a\n\x12is_banding_enabled\x18\x04 \x01(\x08\x12\"\n\x1ais_dynamic_banding_enabled\x18\x05 \x01(\x08*\x94\x02\n\x1aVisualizationTypeToInclude\x12\x12\n\x0e\x44UPLICATE_WEED\x10\x00\x12\x12\n\x0e\x44UPLICATE_CROP\x10\x01\x12\x0f\n\x0bKILLED_WEED\x10\x02\x12\x0f\n\x0bKILLED_CROP\x10\x03\x12\x10\n\x0cKILLING_WEED\x10\x04\x12\x10\n\x0cIGNORED_WEED\x10\x05\x12\x10\n\x0cKILLING_CROP\x10\x06\x12\x0e\n\nERROR_WEED\x10\x07\x12\x0e\n\nERROR_CROP\x10\x08\x12\x10\n\x0cIGNORED_CROP\x10\t\x12\x08\n\x04WEED\x10\n\x12\x08\n\x04\x43ROP\x10\x0b\x12\x0f\n\x0b\x43ROP_RADIUS\x10\x0c\x12\r\n\tCROP_KEPT\x10\r\x12\x10\n\x0cTHINNING_BOX\x10\x0e*-\n\x0eThresholdState\x12\x07\n\x03\x41NY\x10\x00\x12\x08\n\x04PASS\x10\x01\x12\x08\n\x04\x46\x41IL\x10\x02\x32\xed\r\n\x0e\x42\x61ndingService\x12`\n\x0fLoadBandingDefs\x12\x1b.carbon.frontend.util.Empty\x1a\x30.carbon.frontend.banding.LoadBandingDefsResponse\x12]\n\x0eSaveBandingDef\x12..carbon.frontend.banding.SaveBandingDefRequest\x1a\x1b.carbon.frontend.util.Empty\x12\x61\n\x10\x44\x65leteBandingDef\x12\x30.carbon.frontend.banding.DeleteBandingDefRequest\x1a\x1b.carbon.frontend.util.Empty\x12g\n\x13SetActiveBandingDef\x12\x33.carbon.frontend.banding.SetActiveBandingDefRequest\x1a\x1b.carbon.frontend.util.Empty\x12h\n\x13GetActiveBandingDef\x12\x1b.carbon.frontend.util.Empty\x1a\x34.carbon.frontend.banding.GetActiveBandingDefResponse\x12\x8f\x01\n\x18GetNextVisualizationData\x12\x38.carbon.frontend.banding.GetNextVisualizationDataRequest\x1a\x39.carbon.frontend.banding.GetNextVisualizationDataResponse\x12\x91\x01\n\x19GetNextVisualizationData2\x12\x38.carbon.frontend.banding.GetNextVisualizationDataRequest\x1a:.carbon.frontend.banding.GetNextVisualizationData2Response\x12\xad\x01\n\"GetNextVisualizationDataForAllRows\x12\x42.carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest\x1a\x43.carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse\x12]\n\rGetDimensions\x12-.carbon.frontend.banding.GetDimensionsRequest\x1a\x1d.aimbot.GetDimensionsResponse\x12z\n\x11SetBandingEnabled\x12\x31.carbon.frontend.banding.SetBandingEnabledRequest\x1a\x32.carbon.frontend.banding.SetBandingEnabledResponse\x12\x62\n\x10IsBandingEnabled\x12\x1b.carbon.frontend.util.Empty\x1a\x31.carbon.frontend.banding.IsBandingEnabledResponse\x12\x81\x01\n\x18SetDynamicBandingEnabled\x12\x31.carbon.frontend.banding.SetBandingEnabledRequest\x1a\x32.carbon.frontend.banding.SetBandingEnabledResponse\x12i\n\x17IsDynamicBandingEnabled\x12\x1b.carbon.frontend.util.Empty\x1a\x31.carbon.frontend.banding.IsBandingEnabledResponse\x12r\n\x18GetVisualizationMetadata\x12\x1b.carbon.frontend.util.Empty\x1a\x39.carbon.frontend.banding.GetVisualizationMetadataResponse\x12l\n\x13GetNextBandingState\x12\x1f.carbon.frontend.util.Timestamp\x1a\x34.carbon.frontend.banding.GetNextBandingStateResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2.DESCRIPTOR,weed__tracking_dot_proto_dot_weed__tracking__pb2.DESCRIPTOR,])

_VISUALIZATIONTYPETOINCLUDE = _descriptor.EnumDescriptor(
  name='VisualizationTypeToInclude',
  full_name='carbon.frontend.banding.VisualizationTypeToInclude',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='DUPLICATE_WEED', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DUPLICATE_CROP', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='KILLED_WEED', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='KILLED_CROP', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='KILLING_WEED', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IGNORED_WEED', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='KILLING_CROP', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ERROR_WEED', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ERROR_CROP', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='IGNORED_CROP', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='WEED', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CROP', index=11, number=11,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CROP_RADIUS', index=12, number=12,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CROP_KEPT', index=13, number=13,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='THINNING_BOX', index=14, number=14,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2975,
  serialized_end=3251,
)
_sym_db.RegisterEnumDescriptor(_VISUALIZATIONTYPETOINCLUDE)

VisualizationTypeToInclude = enum_type_wrapper.EnumTypeWrapper(_VISUALIZATIONTYPETOINCLUDE)
_THRESHOLDSTATE = _descriptor.EnumDescriptor(
  name='ThresholdState',
  full_name='carbon.frontend.banding.ThresholdState',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ANY', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='PASS', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FAIL', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=3253,
  serialized_end=3298,
)
_sym_db.RegisterEnumDescriptor(_THRESHOLDSTATE)

ThresholdState = enum_type_wrapper.EnumTypeWrapper(_THRESHOLDSTATE)
DUPLICATE_WEED = 0
DUPLICATE_CROP = 1
KILLED_WEED = 2
KILLED_CROP = 3
KILLING_WEED = 4
IGNORED_WEED = 5
KILLING_CROP = 6
ERROR_WEED = 7
ERROR_CROP = 8
IGNORED_CROP = 9
WEED = 10
CROP = 11
CROP_RADIUS = 12
CROP_KEPT = 13
THINNING_BOX = 14
ANY = 0
PASS = 1
FAIL = 2



_BANDINGROW = _descriptor.Descriptor(
  name='BandingRow',
  full_name='carbon.frontend.banding.BandingRow',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.banding.BandingRow.row_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bands', full_name='carbon.frontend.banding.BandingRow.bands', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=199,
  serialized_end=273,
)


_BANDINGDEF = _descriptor.Descriptor(
  name='BandingDef',
  full_name='carbon.frontend.banding.BandingDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.banding.BandingDef.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rows', full_name='carbon.frontend.banding.BandingDef.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.frontend.banding.BandingDef.uuid', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=275,
  serialized_end=366,
)


_SAVEBANDINGDEFREQUEST = _descriptor.Descriptor(
  name='SaveBandingDefRequest',
  full_name='carbon.frontend.banding.SaveBandingDefRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bandingDef', full_name='carbon.frontend.banding.SaveBandingDefRequest.bandingDef', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='setActive', full_name='carbon.frontend.banding.SaveBandingDefRequest.setActive', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=368,
  serialized_end=467,
)


_LOADBANDINGDEFSRESPONSE = _descriptor.Descriptor(
  name='LoadBandingDefsResponse',
  full_name='carbon.frontend.banding.LoadBandingDefsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='bandingDefs', full_name='carbon.frontend.banding.LoadBandingDefsResponse.bandingDefs', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='activeDef', full_name='carbon.frontend.banding.LoadBandingDefsResponse.activeDef', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='activeDefUUID', full_name='carbon.frontend.banding.LoadBandingDefsResponse.activeDefUUID', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=470,
  serialized_end=599,
)


_SETACTIVEBANDINGDEFREQUEST = _descriptor.Descriptor(
  name='SetActiveBandingDefRequest',
  full_name='carbon.frontend.banding.SetActiveBandingDefRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.banding.SetActiveBandingDefRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.frontend.banding.SetActiveBandingDefRequest.uuid', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=601,
  serialized_end=661,
)


_GETACTIVEBANDINGDEFRESPONSE = _descriptor.Descriptor(
  name='GetActiveBandingDefResponse',
  full_name='carbon.frontend.banding.GetActiveBandingDefResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.banding.GetActiveBandingDefResponse.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.frontend.banding.GetActiveBandingDefResponse.uuid', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=663,
  serialized_end=724,
)


_DELETEBANDINGDEFREQUEST = _descriptor.Descriptor(
  name='DeleteBandingDefRequest',
  full_name='carbon.frontend.banding.DeleteBandingDefRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.banding.DeleteBandingDefRequest.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=b'\030\001', file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.frontend.banding.DeleteBandingDefRequest.uuid', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=726,
  serialized_end=783,
)


_VISUALIZATIONDATA = _descriptor.Descriptor(
  name='VisualizationData',
  full_name='carbon.frontend.banding.VisualizationData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x_mm', full_name='carbon.frontend.banding.VisualizationData.x_mm', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y_mm', full_name='carbon.frontend.banding.VisualizationData.y_mm', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='z_mm', full_name='carbon.frontend.banding.VisualizationData.z_mm', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_weed', full_name='carbon.frontend.banding.VisualizationData.is_weed', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=785,
  serialized_end=863,
)


_GETNEXTVISUALIZATIONDATAREQUEST = _descriptor.Descriptor(
  name='GetNextVisualizationDataRequest',
  full_name='carbon.frontend.banding.GetNextVisualizationDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.banding.GetNextVisualizationDataRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.banding.GetNextVisualizationDataRequest.row_id', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='types_to_include', full_name='carbon.frontend.banding.GetNextVisualizationDataRequest.types_to_include', index=2,
      number=3, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='threshold_filters', full_name='carbon.frontend.banding.GetNextVisualizationDataRequest.threshold_filters', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=866,
  serialized_end=1109,
)


_GETNEXTVISUALIZATIONDATARESPONSE = _descriptor.Descriptor(
  name='GetNextVisualizationDataResponse',
  full_name='carbon.frontend.banding.GetNextVisualizationDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.banding.GetNextVisualizationDataResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data', full_name='carbon.frontend.banding.GetNextVisualizationDataResponse.data', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bands', full_name='carbon.frontend.banding.GetNextVisualizationDataResponse.bands', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1112,
  serialized_end=1295,
)


_GETNEXTVISUALIZATIONDATA2RESPONSE = _descriptor.Descriptor(
  name='GetNextVisualizationData2Response',
  full_name='carbon.frontend.banding.GetNextVisualizationData2Response',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='data', full_name='carbon.frontend.banding.GetNextVisualizationData2Response.data', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.banding.GetNextVisualizationData2Response.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1298,
  serialized_end=1428,
)


_GETDIMENSIONSREQUEST = _descriptor.Descriptor(
  name='GetDimensionsRequest',
  full_name='carbon.frontend.banding.GetDimensionsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='row_id', full_name='carbon.frontend.banding.GetDimensionsRequest.row_id', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1430,
  serialized_end=1468,
)


_SETBANDINGENABLEDREQUEST = _descriptor.Descriptor(
  name='SetBandingEnabledRequest',
  full_name='carbon.frontend.banding.SetBandingEnabledRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.banding.SetBandingEnabledRequest.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1470,
  serialized_end=1513,
)


_SETBANDINGENABLEDRESPONSE = _descriptor.Descriptor(
  name='SetBandingEnabledResponse',
  full_name='carbon.frontend.banding.SetBandingEnabledResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1515,
  serialized_end=1542,
)


_ISBANDINGENABLEDRESPONSE = _descriptor.Descriptor(
  name='IsBandingEnabledResponse',
  full_name='carbon.frontend.banding.IsBandingEnabledResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.banding.IsBandingEnabledResponse.enabled', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1544,
  serialized_end=1587,
)


_GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY = _descriptor.Descriptor(
  name='CropSafetyRadiusMmPerRowEntry',
  full_name='carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry.value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1754,
  serialized_end=1817,
)

_GETVISUALIZATIONMETADATARESPONSE = _descriptor.Descriptor(
  name='GetVisualizationMetadataResponse',
  full_name='carbon.frontend.banding.GetVisualizationMetadataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crop_safety_radius_mm_per_row', full_name='carbon.frontend.banding.GetVisualizationMetadataResponse.crop_safety_radius_mm_per_row', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1590,
  serialized_end=1817,
)


_THRESHOLDFILTER = _descriptor.Descriptor(
  name='ThresholdFilter',
  full_name='carbon.frontend.banding.ThresholdFilter',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weeding', full_name='carbon.frontend.banding.ThresholdFilter.weeding', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning', full_name='carbon.frontend.banding.ThresholdFilter.thinning', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding', full_name='carbon.frontend.banding.ThresholdFilter.banding', index=2,
      number=3, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1820,
  serialized_end=2012,
)


_THRESHOLDFILTERS = _descriptor.Descriptor(
  name='ThresholdFilters',
  full_name='carbon.frontend.banding.ThresholdFilters',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='crop', full_name='carbon.frontend.banding.ThresholdFilters.crop', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed', full_name='carbon.frontend.banding.ThresholdFilters.weed', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2015,
  serialized_end=2145,
)


_GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST = _descriptor.Descriptor(
  name='GetNextVisualizationDataForAllRowsRequest',
  full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='types_to_include', full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.types_to_include', index=1,
      number=2, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='threshold_filters', full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest.threshold_filters', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2148,
  serialized_end=2385,
)


_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY = _descriptor.Descriptor(
  name='DataPerRowEntry',
  full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry.key', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2665,
  serialized_end=2750,
)

_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE = _descriptor.Descriptor(
  name='GetNextVisualizationDataForAllRowsResponse',
  full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='data_per_row', full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.data_per_row', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.ts', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='types_to_include', full_name='carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.types_to_include', index=2,
      number=3, type=14, cpp_type=8, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2388,
  serialized_end=2750,
)


_GETNEXTBANDINGSTATERESPONSE = _descriptor.Descriptor(
  name='GetNextBandingStateResponse',
  full_name='carbon.frontend.banding.GetNextBandingStateResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.banding.GetNextBandingStateResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bandingDefs', full_name='carbon.frontend.banding.GetNextBandingStateResponse.bandingDefs', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='activeDefUUID', full_name='carbon.frontend.banding.GetNextBandingStateResponse.activeDefUUID', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_banding_enabled', full_name='carbon.frontend.banding.GetNextBandingStateResponse.is_banding_enabled', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='is_dynamic_banding_enabled', full_name='carbon.frontend.banding.GetNextBandingStateResponse.is_dynamic_banding_enabled', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2753,
  serialized_end=2972,
)

_BANDINGROW.fields_by_name['bands'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._BANDDEFINITION
_BANDINGDEF.fields_by_name['rows'].message_type = _BANDINGROW
_SAVEBANDINGDEFREQUEST.fields_by_name['bandingDef'].message_type = _BANDINGDEF
_LOADBANDINGDEFSRESPONSE.fields_by_name['bandingDefs'].message_type = _BANDINGDEF
_GETNEXTVISUALIZATIONDATAREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTVISUALIZATIONDATAREQUEST.fields_by_name['types_to_include'].enum_type = _VISUALIZATIONTYPETOINCLUDE
_GETNEXTVISUALIZATIONDATAREQUEST.fields_by_name['threshold_filters'].message_type = _THRESHOLDFILTERS
_GETNEXTVISUALIZATIONDATARESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTVISUALIZATIONDATARESPONSE.fields_by_name['data'].message_type = _VISUALIZATIONDATA
_GETNEXTVISUALIZATIONDATARESPONSE.fields_by_name['bands'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._BANDDEFINITION
_GETNEXTVISUALIZATIONDATA2RESPONSE.fields_by_name['data'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._DIAGNOSTICSSNAPSHOT
_GETNEXTVISUALIZATIONDATA2RESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY.containing_type = _GETVISUALIZATIONMETADATARESPONSE
_GETVISUALIZATIONMETADATARESPONSE.fields_by_name['crop_safety_radius_mm_per_row'].message_type = _GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY
_THRESHOLDFILTER.fields_by_name['weeding'].enum_type = _THRESHOLDSTATE
_THRESHOLDFILTER.fields_by_name['thinning'].enum_type = _THRESHOLDSTATE
_THRESHOLDFILTER.fields_by_name['banding'].enum_type = _THRESHOLDSTATE
_THRESHOLDFILTERS.fields_by_name['crop'].message_type = _THRESHOLDFILTER
_THRESHOLDFILTERS.fields_by_name['weed'].message_type = _THRESHOLDFILTER
_GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST.fields_by_name['types_to_include'].enum_type = _VISUALIZATIONTYPETOINCLUDE
_GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST.fields_by_name['threshold_filters'].message_type = _THRESHOLDFILTERS
_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY.fields_by_name['value'].message_type = weed__tracking_dot_proto_dot_weed__tracking__pb2._DIAGNOSTICSSNAPSHOT
_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY.containing_type = _GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE
_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE.fields_by_name['data_per_row'].message_type = _GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY
_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE.fields_by_name['types_to_include'].enum_type = _VISUALIZATIONTYPETOINCLUDE
_GETNEXTBANDINGSTATERESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTBANDINGSTATERESPONSE.fields_by_name['bandingDefs'].message_type = _BANDINGDEF
DESCRIPTOR.message_types_by_name['BandingRow'] = _BANDINGROW
DESCRIPTOR.message_types_by_name['BandingDef'] = _BANDINGDEF
DESCRIPTOR.message_types_by_name['SaveBandingDefRequest'] = _SAVEBANDINGDEFREQUEST
DESCRIPTOR.message_types_by_name['LoadBandingDefsResponse'] = _LOADBANDINGDEFSRESPONSE
DESCRIPTOR.message_types_by_name['SetActiveBandingDefRequest'] = _SETACTIVEBANDINGDEFREQUEST
DESCRIPTOR.message_types_by_name['GetActiveBandingDefResponse'] = _GETACTIVEBANDINGDEFRESPONSE
DESCRIPTOR.message_types_by_name['DeleteBandingDefRequest'] = _DELETEBANDINGDEFREQUEST
DESCRIPTOR.message_types_by_name['VisualizationData'] = _VISUALIZATIONDATA
DESCRIPTOR.message_types_by_name['GetNextVisualizationDataRequest'] = _GETNEXTVISUALIZATIONDATAREQUEST
DESCRIPTOR.message_types_by_name['GetNextVisualizationDataResponse'] = _GETNEXTVISUALIZATIONDATARESPONSE
DESCRIPTOR.message_types_by_name['GetNextVisualizationData2Response'] = _GETNEXTVISUALIZATIONDATA2RESPONSE
DESCRIPTOR.message_types_by_name['GetDimensionsRequest'] = _GETDIMENSIONSREQUEST
DESCRIPTOR.message_types_by_name['SetBandingEnabledRequest'] = _SETBANDINGENABLEDREQUEST
DESCRIPTOR.message_types_by_name['SetBandingEnabledResponse'] = _SETBANDINGENABLEDRESPONSE
DESCRIPTOR.message_types_by_name['IsBandingEnabledResponse'] = _ISBANDINGENABLEDRESPONSE
DESCRIPTOR.message_types_by_name['GetVisualizationMetadataResponse'] = _GETVISUALIZATIONMETADATARESPONSE
DESCRIPTOR.message_types_by_name['ThresholdFilter'] = _THRESHOLDFILTER
DESCRIPTOR.message_types_by_name['ThresholdFilters'] = _THRESHOLDFILTERS
DESCRIPTOR.message_types_by_name['GetNextVisualizationDataForAllRowsRequest'] = _GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST
DESCRIPTOR.message_types_by_name['GetNextVisualizationDataForAllRowsResponse'] = _GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE
DESCRIPTOR.message_types_by_name['GetNextBandingStateResponse'] = _GETNEXTBANDINGSTATERESPONSE
DESCRIPTOR.enum_types_by_name['VisualizationTypeToInclude'] = _VISUALIZATIONTYPETOINCLUDE
DESCRIPTOR.enum_types_by_name['ThresholdState'] = _THRESHOLDSTATE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BandingRow = _reflection.GeneratedProtocolMessageType('BandingRow', (_message.Message,), {
  'DESCRIPTOR' : _BANDINGROW,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.BandingRow)
  })
_sym_db.RegisterMessage(BandingRow)

BandingDef = _reflection.GeneratedProtocolMessageType('BandingDef', (_message.Message,), {
  'DESCRIPTOR' : _BANDINGDEF,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.BandingDef)
  })
_sym_db.RegisterMessage(BandingDef)

SaveBandingDefRequest = _reflection.GeneratedProtocolMessageType('SaveBandingDefRequest', (_message.Message,), {
  'DESCRIPTOR' : _SAVEBANDINGDEFREQUEST,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.SaveBandingDefRequest)
  })
_sym_db.RegisterMessage(SaveBandingDefRequest)

LoadBandingDefsResponse = _reflection.GeneratedProtocolMessageType('LoadBandingDefsResponse', (_message.Message,), {
  'DESCRIPTOR' : _LOADBANDINGDEFSRESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.LoadBandingDefsResponse)
  })
_sym_db.RegisterMessage(LoadBandingDefsResponse)

SetActiveBandingDefRequest = _reflection.GeneratedProtocolMessageType('SetActiveBandingDefRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETACTIVEBANDINGDEFREQUEST,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.SetActiveBandingDefRequest)
  })
_sym_db.RegisterMessage(SetActiveBandingDefRequest)

GetActiveBandingDefResponse = _reflection.GeneratedProtocolMessageType('GetActiveBandingDefResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETACTIVEBANDINGDEFRESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetActiveBandingDefResponse)
  })
_sym_db.RegisterMessage(GetActiveBandingDefResponse)

DeleteBandingDefRequest = _reflection.GeneratedProtocolMessageType('DeleteBandingDefRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETEBANDINGDEFREQUEST,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.DeleteBandingDefRequest)
  })
_sym_db.RegisterMessage(DeleteBandingDefRequest)

VisualizationData = _reflection.GeneratedProtocolMessageType('VisualizationData', (_message.Message,), {
  'DESCRIPTOR' : _VISUALIZATIONDATA,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.VisualizationData)
  })
_sym_db.RegisterMessage(VisualizationData)

GetNextVisualizationDataRequest = _reflection.GeneratedProtocolMessageType('GetNextVisualizationDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTVISUALIZATIONDATAREQUEST,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataRequest)
  })
_sym_db.RegisterMessage(GetNextVisualizationDataRequest)

GetNextVisualizationDataResponse = _reflection.GeneratedProtocolMessageType('GetNextVisualizationDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTVISUALIZATIONDATARESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataResponse)
  })
_sym_db.RegisterMessage(GetNextVisualizationDataResponse)

GetNextVisualizationData2Response = _reflection.GeneratedProtocolMessageType('GetNextVisualizationData2Response', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTVISUALIZATIONDATA2RESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationData2Response)
  })
_sym_db.RegisterMessage(GetNextVisualizationData2Response)

GetDimensionsRequest = _reflection.GeneratedProtocolMessageType('GetDimensionsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETDIMENSIONSREQUEST,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetDimensionsRequest)
  })
_sym_db.RegisterMessage(GetDimensionsRequest)

SetBandingEnabledRequest = _reflection.GeneratedProtocolMessageType('SetBandingEnabledRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETBANDINGENABLEDREQUEST,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.SetBandingEnabledRequest)
  })
_sym_db.RegisterMessage(SetBandingEnabledRequest)

SetBandingEnabledResponse = _reflection.GeneratedProtocolMessageType('SetBandingEnabledResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETBANDINGENABLEDRESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.SetBandingEnabledResponse)
  })
_sym_db.RegisterMessage(SetBandingEnabledResponse)

IsBandingEnabledResponse = _reflection.GeneratedProtocolMessageType('IsBandingEnabledResponse', (_message.Message,), {
  'DESCRIPTOR' : _ISBANDINGENABLEDRESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.IsBandingEnabledResponse)
  })
_sym_db.RegisterMessage(IsBandingEnabledResponse)

GetVisualizationMetadataResponse = _reflection.GeneratedProtocolMessageType('GetVisualizationMetadataResponse', (_message.Message,), {

  'CropSafetyRadiusMmPerRowEntry' : _reflection.GeneratedProtocolMessageType('CropSafetyRadiusMmPerRowEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY,
    '__module__' : 'frontend.proto.banding_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry)
    })
  ,
  'DESCRIPTOR' : _GETVISUALIZATIONMETADATARESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetVisualizationMetadataResponse)
  })
_sym_db.RegisterMessage(GetVisualizationMetadataResponse)
_sym_db.RegisterMessage(GetVisualizationMetadataResponse.CropSafetyRadiusMmPerRowEntry)

ThresholdFilter = _reflection.GeneratedProtocolMessageType('ThresholdFilter', (_message.Message,), {
  'DESCRIPTOR' : _THRESHOLDFILTER,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.ThresholdFilter)
  })
_sym_db.RegisterMessage(ThresholdFilter)

ThresholdFilters = _reflection.GeneratedProtocolMessageType('ThresholdFilters', (_message.Message,), {
  'DESCRIPTOR' : _THRESHOLDFILTERS,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.ThresholdFilters)
  })
_sym_db.RegisterMessage(ThresholdFilters)

GetNextVisualizationDataForAllRowsRequest = _reflection.GeneratedProtocolMessageType('GetNextVisualizationDataForAllRowsRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataForAllRowsRequest)
  })
_sym_db.RegisterMessage(GetNextVisualizationDataForAllRowsRequest)

GetNextVisualizationDataForAllRowsResponse = _reflection.GeneratedProtocolMessageType('GetNextVisualizationDataForAllRowsResponse', (_message.Message,), {

  'DataPerRowEntry' : _reflection.GeneratedProtocolMessageType('DataPerRowEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY,
    '__module__' : 'frontend.proto.banding_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry)
    })
  ,
  'DESCRIPTOR' : _GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextVisualizationDataForAllRowsResponse)
  })
_sym_db.RegisterMessage(GetNextVisualizationDataForAllRowsResponse)
_sym_db.RegisterMessage(GetNextVisualizationDataForAllRowsResponse.DataPerRowEntry)

GetNextBandingStateResponse = _reflection.GeneratedProtocolMessageType('GetNextBandingStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTBANDINGSTATERESPONSE,
  '__module__' : 'frontend.proto.banding_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.banding.GetNextBandingStateResponse)
  })
_sym_db.RegisterMessage(GetNextBandingStateResponse)


DESCRIPTOR._options = None
_LOADBANDINGDEFSRESPONSE.fields_by_name['activeDef']._options = None
_SETACTIVEBANDINGDEFREQUEST.fields_by_name['name']._options = None
_GETACTIVEBANDINGDEFRESPONSE.fields_by_name['name']._options = None
_DELETEBANDINGDEFREQUEST.fields_by_name['name']._options = None
_GETVISUALIZATIONMETADATARESPONSE_CROPSAFETYRADIUSMMPERROWENTRY._options = None
_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE_DATAPERROWENTRY._options = None

_BANDINGSERVICE = _descriptor.ServiceDescriptor(
  name='BandingService',
  full_name='carbon.frontend.banding.BandingService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=3301,
  serialized_end=5074,
  methods=[
  _descriptor.MethodDescriptor(
    name='LoadBandingDefs',
    full_name='carbon.frontend.banding.BandingService.LoadBandingDefs',
    index=0,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_LOADBANDINGDEFSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SaveBandingDef',
    full_name='carbon.frontend.banding.BandingService.SaveBandingDef',
    index=1,
    containing_service=None,
    input_type=_SAVEBANDINGDEFREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteBandingDef',
    full_name='carbon.frontend.banding.BandingService.DeleteBandingDef',
    index=2,
    containing_service=None,
    input_type=_DELETEBANDINGDEFREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetActiveBandingDef',
    full_name='carbon.frontend.banding.BandingService.SetActiveBandingDef',
    index=3,
    containing_service=None,
    input_type=_SETACTIVEBANDINGDEFREQUEST,
    output_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetActiveBandingDef',
    full_name='carbon.frontend.banding.BandingService.GetActiveBandingDef',
    index=4,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_GETACTIVEBANDINGDEFRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextVisualizationData',
    full_name='carbon.frontend.banding.BandingService.GetNextVisualizationData',
    index=5,
    containing_service=None,
    input_type=_GETNEXTVISUALIZATIONDATAREQUEST,
    output_type=_GETNEXTVISUALIZATIONDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextVisualizationData2',
    full_name='carbon.frontend.banding.BandingService.GetNextVisualizationData2',
    index=6,
    containing_service=None,
    input_type=_GETNEXTVISUALIZATIONDATAREQUEST,
    output_type=_GETNEXTVISUALIZATIONDATA2RESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextVisualizationDataForAllRows',
    full_name='carbon.frontend.banding.BandingService.GetNextVisualizationDataForAllRows',
    index=7,
    containing_service=None,
    input_type=_GETNEXTVISUALIZATIONDATAFORALLROWSREQUEST,
    output_type=_GETNEXTVISUALIZATIONDATAFORALLROWSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetDimensions',
    full_name='carbon.frontend.banding.BandingService.GetDimensions',
    index=8,
    containing_service=None,
    input_type=_GETDIMENSIONSREQUEST,
    output_type=core_dot_controls_dot_exterminator_dot_controllers_dot_aimbot_dot_process_dot_proto_dot_aimbot__pb2._GETDIMENSIONSRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetBandingEnabled',
    full_name='carbon.frontend.banding.BandingService.SetBandingEnabled',
    index=9,
    containing_service=None,
    input_type=_SETBANDINGENABLEDREQUEST,
    output_type=_SETBANDINGENABLEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='IsBandingEnabled',
    full_name='carbon.frontend.banding.BandingService.IsBandingEnabled',
    index=10,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_ISBANDINGENABLEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetDynamicBandingEnabled',
    full_name='carbon.frontend.banding.BandingService.SetDynamicBandingEnabled',
    index=11,
    containing_service=None,
    input_type=_SETBANDINGENABLEDREQUEST,
    output_type=_SETBANDINGENABLEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='IsDynamicBandingEnabled',
    full_name='carbon.frontend.banding.BandingService.IsDynamicBandingEnabled',
    index=12,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_ISBANDINGENABLEDRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetVisualizationMetadata',
    full_name='carbon.frontend.banding.BandingService.GetVisualizationMetadata',
    index=13,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._EMPTY,
    output_type=_GETVISUALIZATIONMETADATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetNextBandingState',
    full_name='carbon.frontend.banding.BandingService.GetNextBandingState',
    index=14,
    containing_service=None,
    input_type=frontend_dot_proto_dot_util__pb2._TIMESTAMP,
    output_type=_GETNEXTBANDINGSTATERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_BANDINGSERVICE)

DESCRIPTOR.services_by_name['BandingService'] = _BANDINGSERVICE

# @@protoc_insertion_point(module_scope)

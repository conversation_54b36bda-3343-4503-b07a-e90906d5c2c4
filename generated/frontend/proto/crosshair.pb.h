// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/crosshair.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcrosshair_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcrosshair_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/camera.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fcrosshair_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fcrosshair_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[7]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcrosshair_2eproto;
namespace carbon {
namespace frontend {
namespace crosshair {
class AutoCrossHairCalStateRequest;
struct AutoCrossHairCalStateRequestDefaultTypeInternal;
extern AutoCrossHairCalStateRequestDefaultTypeInternal _AutoCrossHairCalStateRequest_default_instance_;
class AutoCrossHairCalStateResponse;
struct AutoCrossHairCalStateResponseDefaultTypeInternal;
extern AutoCrossHairCalStateResponseDefaultTypeInternal _AutoCrossHairCalStateResponse_default_instance_;
class CrosshairPosition;
struct CrosshairPositionDefaultTypeInternal;
extern CrosshairPositionDefaultTypeInternal _CrosshairPosition_default_instance_;
class CrosshairPositionRequest;
struct CrosshairPositionRequestDefaultTypeInternal;
extern CrosshairPositionRequestDefaultTypeInternal _CrosshairPositionRequest_default_instance_;
class CrosshairPositionState;
struct CrosshairPositionStateDefaultTypeInternal;
extern CrosshairPositionStateDefaultTypeInternal _CrosshairPositionState_default_instance_;
class MoveScannerRequest;
struct MoveScannerRequestDefaultTypeInternal;
extern MoveScannerRequestDefaultTypeInternal _MoveScannerRequest_default_instance_;
class SetCrosshairPositionRequest;
struct SetCrosshairPositionRequestDefaultTypeInternal;
extern SetCrosshairPositionRequestDefaultTypeInternal _SetCrosshairPositionRequest_default_instance_;
}  // namespace crosshair
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* Arena::CreateMaybeMessage<::carbon::frontend::crosshair::AutoCrossHairCalStateRequest>(Arena*);
template<> ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* Arena::CreateMaybeMessage<::carbon::frontend::crosshair::AutoCrossHairCalStateResponse>(Arena*);
template<> ::carbon::frontend::crosshair::CrosshairPosition* Arena::CreateMaybeMessage<::carbon::frontend::crosshair::CrosshairPosition>(Arena*);
template<> ::carbon::frontend::crosshair::CrosshairPositionRequest* Arena::CreateMaybeMessage<::carbon::frontend::crosshair::CrosshairPositionRequest>(Arena*);
template<> ::carbon::frontend::crosshair::CrosshairPositionState* Arena::CreateMaybeMessage<::carbon::frontend::crosshair::CrosshairPositionState>(Arena*);
template<> ::carbon::frontend::crosshair::MoveScannerRequest* Arena::CreateMaybeMessage<::carbon::frontend::crosshair::MoveScannerRequest>(Arena*);
template<> ::carbon::frontend::crosshair::SetCrosshairPositionRequest* Arena::CreateMaybeMessage<::carbon::frontend::crosshair::SetCrosshairPositionRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace crosshair {

// ===================================================================

class CrosshairPosition final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.crosshair.CrosshairPosition) */ {
 public:
  inline CrosshairPosition() : CrosshairPosition(nullptr) {}
  ~CrosshairPosition() override;
  explicit constexpr CrosshairPosition(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CrosshairPosition(const CrosshairPosition& from);
  CrosshairPosition(CrosshairPosition&& from) noexcept
    : CrosshairPosition() {
    *this = ::std::move(from);
  }

  inline CrosshairPosition& operator=(const CrosshairPosition& from) {
    CopyFrom(from);
    return *this;
  }
  inline CrosshairPosition& operator=(CrosshairPosition&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CrosshairPosition& default_instance() {
    return *internal_default_instance();
  }
  static inline const CrosshairPosition* internal_default_instance() {
    return reinterpret_cast<const CrosshairPosition*>(
               &_CrosshairPosition_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(CrosshairPosition& a, CrosshairPosition& b) {
    a.Swap(&b);
  }
  inline void Swap(CrosshairPosition* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CrosshairPosition* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CrosshairPosition* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CrosshairPosition>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CrosshairPosition& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CrosshairPosition& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CrosshairPosition* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.crosshair.CrosshairPosition";
  }
  protected:
  explicit CrosshairPosition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
    kYFieldNumber = 2,
  };
  // float x = 1;
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // float y = 2;
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.CrosshairPosition)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float x_;
  float y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcrosshair_2eproto;
};
// -------------------------------------------------------------------

class CrosshairPositionState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.crosshair.CrosshairPositionState) */ {
 public:
  inline CrosshairPositionState() : CrosshairPositionState(nullptr) {}
  ~CrosshairPositionState() override;
  explicit constexpr CrosshairPositionState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CrosshairPositionState(const CrosshairPositionState& from);
  CrosshairPositionState(CrosshairPositionState&& from) noexcept
    : CrosshairPositionState() {
    *this = ::std::move(from);
  }

  inline CrosshairPositionState& operator=(const CrosshairPositionState& from) {
    CopyFrom(from);
    return *this;
  }
  inline CrosshairPositionState& operator=(CrosshairPositionState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CrosshairPositionState& default_instance() {
    return *internal_default_instance();
  }
  static inline const CrosshairPositionState* internal_default_instance() {
    return reinterpret_cast<const CrosshairPositionState*>(
               &_CrosshairPositionState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(CrosshairPositionState& a, CrosshairPositionState& b) {
    a.Swap(&b);
  }
  inline void Swap(CrosshairPositionState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CrosshairPositionState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CrosshairPositionState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CrosshairPositionState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CrosshairPositionState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CrosshairPositionState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CrosshairPositionState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.crosshair.CrosshairPositionState";
  }
  protected:
  explicit CrosshairPositionState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kPosFieldNumber = 2,
    kCalibratingFieldNumber = 3,
    kCalibrationFailedFieldNumber = 4,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.crosshair.CrosshairPosition pos = 2;
  bool has_pos() const;
  private:
  bool _internal_has_pos() const;
  public:
  void clear_pos();
  const ::carbon::frontend::crosshair::CrosshairPosition& pos() const;
  PROTOBUF_NODISCARD ::carbon::frontend::crosshair::CrosshairPosition* release_pos();
  ::carbon::frontend::crosshair::CrosshairPosition* mutable_pos();
  void set_allocated_pos(::carbon::frontend::crosshair::CrosshairPosition* pos);
  private:
  const ::carbon::frontend::crosshair::CrosshairPosition& _internal_pos() const;
  ::carbon::frontend::crosshair::CrosshairPosition* _internal_mutable_pos();
  public:
  void unsafe_arena_set_allocated_pos(
      ::carbon::frontend::crosshair::CrosshairPosition* pos);
  ::carbon::frontend::crosshair::CrosshairPosition* unsafe_arena_release_pos();

  // bool calibrating = 3;
  void clear_calibrating();
  bool calibrating() const;
  void set_calibrating(bool value);
  private:
  bool _internal_calibrating() const;
  void _internal_set_calibrating(bool value);
  public:

  // bool calibration_failed = 4;
  void clear_calibration_failed();
  bool calibration_failed() const;
  void set_calibration_failed(bool value);
  private:
  bool _internal_calibration_failed() const;
  void _internal_set_calibration_failed(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.CrosshairPositionState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::crosshair::CrosshairPosition* pos_;
  bool calibrating_;
  bool calibration_failed_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcrosshair_2eproto;
};
// -------------------------------------------------------------------

class CrosshairPositionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.crosshair.CrosshairPositionRequest) */ {
 public:
  inline CrosshairPositionRequest() : CrosshairPositionRequest(nullptr) {}
  ~CrosshairPositionRequest() override;
  explicit constexpr CrosshairPositionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  CrosshairPositionRequest(const CrosshairPositionRequest& from);
  CrosshairPositionRequest(CrosshairPositionRequest&& from) noexcept
    : CrosshairPositionRequest() {
    *this = ::std::move(from);
  }

  inline CrosshairPositionRequest& operator=(const CrosshairPositionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline CrosshairPositionRequest& operator=(CrosshairPositionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const CrosshairPositionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const CrosshairPositionRequest* internal_default_instance() {
    return reinterpret_cast<const CrosshairPositionRequest*>(
               &_CrosshairPositionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(CrosshairPositionRequest& a, CrosshairPositionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(CrosshairPositionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(CrosshairPositionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  CrosshairPositionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<CrosshairPositionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const CrosshairPositionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const CrosshairPositionRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(CrosshairPositionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.crosshair.CrosshairPositionRequest";
  }
  protected:
  explicit CrosshairPositionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 1,
    kTsFieldNumber = 2,
  };
  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 2;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.CrosshairPositionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcrosshair_2eproto;
};
// -------------------------------------------------------------------

class SetCrosshairPositionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.crosshair.SetCrosshairPositionRequest) */ {
 public:
  inline SetCrosshairPositionRequest() : SetCrosshairPositionRequest(nullptr) {}
  ~SetCrosshairPositionRequest() override;
  explicit constexpr SetCrosshairPositionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetCrosshairPositionRequest(const SetCrosshairPositionRequest& from);
  SetCrosshairPositionRequest(SetCrosshairPositionRequest&& from) noexcept
    : SetCrosshairPositionRequest() {
    *this = ::std::move(from);
  }

  inline SetCrosshairPositionRequest& operator=(const SetCrosshairPositionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetCrosshairPositionRequest& operator=(SetCrosshairPositionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetCrosshairPositionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetCrosshairPositionRequest* internal_default_instance() {
    return reinterpret_cast<const SetCrosshairPositionRequest*>(
               &_SetCrosshairPositionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SetCrosshairPositionRequest& a, SetCrosshairPositionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetCrosshairPositionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetCrosshairPositionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetCrosshairPositionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetCrosshairPositionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetCrosshairPositionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetCrosshairPositionRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetCrosshairPositionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.crosshair.SetCrosshairPositionRequest";
  }
  protected:
  explicit SetCrosshairPositionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 1,
    kPosFieldNumber = 2,
  };
  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // .carbon.frontend.crosshair.CrosshairPosition pos = 2;
  bool has_pos() const;
  private:
  bool _internal_has_pos() const;
  public:
  void clear_pos();
  const ::carbon::frontend::crosshair::CrosshairPosition& pos() const;
  PROTOBUF_NODISCARD ::carbon::frontend::crosshair::CrosshairPosition* release_pos();
  ::carbon::frontend::crosshair::CrosshairPosition* mutable_pos();
  void set_allocated_pos(::carbon::frontend::crosshair::CrosshairPosition* pos);
  private:
  const ::carbon::frontend::crosshair::CrosshairPosition& _internal_pos() const;
  ::carbon::frontend::crosshair::CrosshairPosition* _internal_mutable_pos();
  public:
  void unsafe_arena_set_allocated_pos(
      ::carbon::frontend::crosshair::CrosshairPosition* pos);
  ::carbon::frontend::crosshair::CrosshairPosition* unsafe_arena_release_pos();

  // @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.SetCrosshairPositionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  ::carbon::frontend::crosshair::CrosshairPosition* pos_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcrosshair_2eproto;
};
// -------------------------------------------------------------------

class MoveScannerRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.crosshair.MoveScannerRequest) */ {
 public:
  inline MoveScannerRequest() : MoveScannerRequest(nullptr) {}
  ~MoveScannerRequest() override;
  explicit constexpr MoveScannerRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  MoveScannerRequest(const MoveScannerRequest& from);
  MoveScannerRequest(MoveScannerRequest&& from) noexcept
    : MoveScannerRequest() {
    *this = ::std::move(from);
  }

  inline MoveScannerRequest& operator=(const MoveScannerRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline MoveScannerRequest& operator=(MoveScannerRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const MoveScannerRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const MoveScannerRequest* internal_default_instance() {
    return reinterpret_cast<const MoveScannerRequest*>(
               &_MoveScannerRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(MoveScannerRequest& a, MoveScannerRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(MoveScannerRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(MoveScannerRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  MoveScannerRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<MoveScannerRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const MoveScannerRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const MoveScannerRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(MoveScannerRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.crosshair.MoveScannerRequest";
  }
  protected:
  explicit MoveScannerRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kCamIdFieldNumber = 1,
    kXFieldNumber = 2,
    kYFieldNumber = 3,
  };
  // string cam_id = 1;
  void clear_cam_id();
  const std::string& cam_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_cam_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_cam_id();
  PROTOBUF_NODISCARD std::string* release_cam_id();
  void set_allocated_cam_id(std::string* cam_id);
  private:
  const std::string& _internal_cam_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_cam_id(const std::string& value);
  std::string* _internal_mutable_cam_id();
  public:

  // float x = 2;
  void clear_x();
  float x() const;
  void set_x(float value);
  private:
  float _internal_x() const;
  void _internal_set_x(float value);
  public:

  // float y = 3;
  void clear_y();
  float y() const;
  void set_y(float value);
  private:
  float _internal_y() const;
  void _internal_set_y(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.MoveScannerRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr cam_id_;
  float x_;
  float y_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcrosshair_2eproto;
};
// -------------------------------------------------------------------

class AutoCrossHairCalStateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.crosshair.AutoCrossHairCalStateRequest) */ {
 public:
  inline AutoCrossHairCalStateRequest() : AutoCrossHairCalStateRequest(nullptr) {}
  ~AutoCrossHairCalStateRequest() override;
  explicit constexpr AutoCrossHairCalStateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutoCrossHairCalStateRequest(const AutoCrossHairCalStateRequest& from);
  AutoCrossHairCalStateRequest(AutoCrossHairCalStateRequest&& from) noexcept
    : AutoCrossHairCalStateRequest() {
    *this = ::std::move(from);
  }

  inline AutoCrossHairCalStateRequest& operator=(const AutoCrossHairCalStateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutoCrossHairCalStateRequest& operator=(AutoCrossHairCalStateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutoCrossHairCalStateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutoCrossHairCalStateRequest* internal_default_instance() {
    return reinterpret_cast<const AutoCrossHairCalStateRequest*>(
               &_AutoCrossHairCalStateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(AutoCrossHairCalStateRequest& a, AutoCrossHairCalStateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(AutoCrossHairCalStateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutoCrossHairCalStateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutoCrossHairCalStateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutoCrossHairCalStateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutoCrossHairCalStateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AutoCrossHairCalStateRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutoCrossHairCalStateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.crosshair.AutoCrossHairCalStateRequest";
  }
  protected:
  explicit AutoCrossHairCalStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcrosshair_2eproto;
};
// -------------------------------------------------------------------

class AutoCrossHairCalStateResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.crosshair.AutoCrossHairCalStateResponse) */ {
 public:
  inline AutoCrossHairCalStateResponse() : AutoCrossHairCalStateResponse(nullptr) {}
  ~AutoCrossHairCalStateResponse() override;
  explicit constexpr AutoCrossHairCalStateResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AutoCrossHairCalStateResponse(const AutoCrossHairCalStateResponse& from);
  AutoCrossHairCalStateResponse(AutoCrossHairCalStateResponse&& from) noexcept
    : AutoCrossHairCalStateResponse() {
    *this = ::std::move(from);
  }

  inline AutoCrossHairCalStateResponse& operator=(const AutoCrossHairCalStateResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline AutoCrossHairCalStateResponse& operator=(AutoCrossHairCalStateResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AutoCrossHairCalStateResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const AutoCrossHairCalStateResponse* internal_default_instance() {
    return reinterpret_cast<const AutoCrossHairCalStateResponse*>(
               &_AutoCrossHairCalStateResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(AutoCrossHairCalStateResponse& a, AutoCrossHairCalStateResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(AutoCrossHairCalStateResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AutoCrossHairCalStateResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AutoCrossHairCalStateResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AutoCrossHairCalStateResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AutoCrossHairCalStateResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AutoCrossHairCalStateResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AutoCrossHairCalStateResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.crosshair.AutoCrossHairCalStateResponse";
  }
  protected:
  explicit AutoCrossHairCalStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kFailedFieldNumber = 4,
    kTsFieldNumber = 1,
    kInProgressFieldNumber = 2,
    kProgressFieldNumber = 3,
  };
  // repeated string failed = 4;
  int failed_size() const;
  private:
  int _internal_failed_size() const;
  public:
  void clear_failed();
  const std::string& failed(int index) const;
  std::string* mutable_failed(int index);
  void set_failed(int index, const std::string& value);
  void set_failed(int index, std::string&& value);
  void set_failed(int index, const char* value);
  void set_failed(int index, const char* value, size_t size);
  std::string* add_failed();
  void add_failed(const std::string& value);
  void add_failed(std::string&& value);
  void add_failed(const char* value);
  void add_failed(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& failed() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_failed();
  private:
  const std::string& _internal_failed(int index) const;
  std::string* _internal_add_failed();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // bool in_progress = 2;
  void clear_in_progress();
  bool in_progress() const;
  void set_in_progress(bool value);
  private:
  bool _internal_in_progress() const;
  void _internal_set_in_progress(bool value);
  public:

  // float progress = 3;
  void clear_progress();
  float progress() const;
  void set_progress(float value);
  private:
  float _internal_progress() const;
  void _internal_set_progress(float value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> failed_;
  ::carbon::frontend::util::Timestamp* ts_;
  bool in_progress_;
  float progress_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fcrosshair_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// CrosshairPosition

// float x = 1;
inline void CrosshairPosition::clear_x() {
  x_ = 0;
}
inline float CrosshairPosition::_internal_x() const {
  return x_;
}
inline float CrosshairPosition::x() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.CrosshairPosition.x)
  return _internal_x();
}
inline void CrosshairPosition::_internal_set_x(float value) {
  
  x_ = value;
}
inline void CrosshairPosition::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.CrosshairPosition.x)
}

// float y = 2;
inline void CrosshairPosition::clear_y() {
  y_ = 0;
}
inline float CrosshairPosition::_internal_y() const {
  return y_;
}
inline float CrosshairPosition::y() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.CrosshairPosition.y)
  return _internal_y();
}
inline void CrosshairPosition::_internal_set_y(float value) {
  
  y_ = value;
}
inline void CrosshairPosition::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.CrosshairPosition.y)
}

// -------------------------------------------------------------------

// CrosshairPositionState

// .carbon.frontend.util.Timestamp ts = 1;
inline bool CrosshairPositionState::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool CrosshairPositionState::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& CrosshairPositionState::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& CrosshairPositionState::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.CrosshairPositionState.ts)
  return _internal_ts();
}
inline void CrosshairPositionState::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.crosshair.CrosshairPositionState.ts)
}
inline ::carbon::frontend::util::Timestamp* CrosshairPositionState::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CrosshairPositionState::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.CrosshairPositionState.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CrosshairPositionState::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* CrosshairPositionState::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.CrosshairPositionState.ts)
  return _msg;
}
inline void CrosshairPositionState::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.CrosshairPositionState.ts)
}

// .carbon.frontend.crosshair.CrosshairPosition pos = 2;
inline bool CrosshairPositionState::_internal_has_pos() const {
  return this != internal_default_instance() && pos_ != nullptr;
}
inline bool CrosshairPositionState::has_pos() const {
  return _internal_has_pos();
}
inline void CrosshairPositionState::clear_pos() {
  if (GetArenaForAllocation() == nullptr && pos_ != nullptr) {
    delete pos_;
  }
  pos_ = nullptr;
}
inline const ::carbon::frontend::crosshair::CrosshairPosition& CrosshairPositionState::_internal_pos() const {
  const ::carbon::frontend::crosshair::CrosshairPosition* p = pos_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::crosshair::CrosshairPosition&>(
      ::carbon::frontend::crosshair::_CrosshairPosition_default_instance_);
}
inline const ::carbon::frontend::crosshair::CrosshairPosition& CrosshairPositionState::pos() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.CrosshairPositionState.pos)
  return _internal_pos();
}
inline void CrosshairPositionState::unsafe_arena_set_allocated_pos(
    ::carbon::frontend::crosshair::CrosshairPosition* pos) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(pos_);
  }
  pos_ = pos;
  if (pos) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.crosshair.CrosshairPositionState.pos)
}
inline ::carbon::frontend::crosshair::CrosshairPosition* CrosshairPositionState::release_pos() {
  
  ::carbon::frontend::crosshair::CrosshairPosition* temp = pos_;
  pos_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::crosshair::CrosshairPosition* CrosshairPositionState::unsafe_arena_release_pos() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.CrosshairPositionState.pos)
  
  ::carbon::frontend::crosshair::CrosshairPosition* temp = pos_;
  pos_ = nullptr;
  return temp;
}
inline ::carbon::frontend::crosshair::CrosshairPosition* CrosshairPositionState::_internal_mutable_pos() {
  
  if (pos_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::crosshair::CrosshairPosition>(GetArenaForAllocation());
    pos_ = p;
  }
  return pos_;
}
inline ::carbon::frontend::crosshair::CrosshairPosition* CrosshairPositionState::mutable_pos() {
  ::carbon::frontend::crosshair::CrosshairPosition* _msg = _internal_mutable_pos();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.CrosshairPositionState.pos)
  return _msg;
}
inline void CrosshairPositionState::set_allocated_pos(::carbon::frontend::crosshair::CrosshairPosition* pos) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete pos_;
  }
  if (pos) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::crosshair::CrosshairPosition>::GetOwningArena(pos);
    if (message_arena != submessage_arena) {
      pos = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pos, submessage_arena);
    }
    
  } else {
    
  }
  pos_ = pos;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.CrosshairPositionState.pos)
}

// bool calibrating = 3;
inline void CrosshairPositionState::clear_calibrating() {
  calibrating_ = false;
}
inline bool CrosshairPositionState::_internal_calibrating() const {
  return calibrating_;
}
inline bool CrosshairPositionState::calibrating() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.CrosshairPositionState.calibrating)
  return _internal_calibrating();
}
inline void CrosshairPositionState::_internal_set_calibrating(bool value) {
  
  calibrating_ = value;
}
inline void CrosshairPositionState::set_calibrating(bool value) {
  _internal_set_calibrating(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.CrosshairPositionState.calibrating)
}

// bool calibration_failed = 4;
inline void CrosshairPositionState::clear_calibration_failed() {
  calibration_failed_ = false;
}
inline bool CrosshairPositionState::_internal_calibration_failed() const {
  return calibration_failed_;
}
inline bool CrosshairPositionState::calibration_failed() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.CrosshairPositionState.calibration_failed)
  return _internal_calibration_failed();
}
inline void CrosshairPositionState::_internal_set_calibration_failed(bool value) {
  
  calibration_failed_ = value;
}
inline void CrosshairPositionState::set_calibration_failed(bool value) {
  _internal_set_calibration_failed(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.CrosshairPositionState.calibration_failed)
}

// -------------------------------------------------------------------

// CrosshairPositionRequest

// string cam_id = 1;
inline void CrosshairPositionRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& CrosshairPositionRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.CrosshairPositionRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void CrosshairPositionRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.CrosshairPositionRequest.cam_id)
}
inline std::string* CrosshairPositionRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.CrosshairPositionRequest.cam_id)
  return _s;
}
inline const std::string& CrosshairPositionRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void CrosshairPositionRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* CrosshairPositionRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* CrosshairPositionRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.CrosshairPositionRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void CrosshairPositionRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.CrosshairPositionRequest.cam_id)
}

// .carbon.frontend.util.Timestamp ts = 2;
inline bool CrosshairPositionRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool CrosshairPositionRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& CrosshairPositionRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& CrosshairPositionRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.CrosshairPositionRequest.ts)
  return _internal_ts();
}
inline void CrosshairPositionRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.crosshair.CrosshairPositionRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* CrosshairPositionRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CrosshairPositionRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.CrosshairPositionRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* CrosshairPositionRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* CrosshairPositionRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.CrosshairPositionRequest.ts)
  return _msg;
}
inline void CrosshairPositionRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.CrosshairPositionRequest.ts)
}

// -------------------------------------------------------------------

// SetCrosshairPositionRequest

// string cam_id = 1;
inline void SetCrosshairPositionRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& SetCrosshairPositionRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.SetCrosshairPositionRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetCrosshairPositionRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.SetCrosshairPositionRequest.cam_id)
}
inline std::string* SetCrosshairPositionRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.SetCrosshairPositionRequest.cam_id)
  return _s;
}
inline const std::string& SetCrosshairPositionRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void SetCrosshairPositionRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetCrosshairPositionRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetCrosshairPositionRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.SetCrosshairPositionRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetCrosshairPositionRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.SetCrosshairPositionRequest.cam_id)
}

// .carbon.frontend.crosshair.CrosshairPosition pos = 2;
inline bool SetCrosshairPositionRequest::_internal_has_pos() const {
  return this != internal_default_instance() && pos_ != nullptr;
}
inline bool SetCrosshairPositionRequest::has_pos() const {
  return _internal_has_pos();
}
inline void SetCrosshairPositionRequest::clear_pos() {
  if (GetArenaForAllocation() == nullptr && pos_ != nullptr) {
    delete pos_;
  }
  pos_ = nullptr;
}
inline const ::carbon::frontend::crosshair::CrosshairPosition& SetCrosshairPositionRequest::_internal_pos() const {
  const ::carbon::frontend::crosshair::CrosshairPosition* p = pos_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::crosshair::CrosshairPosition&>(
      ::carbon::frontend::crosshair::_CrosshairPosition_default_instance_);
}
inline const ::carbon::frontend::crosshair::CrosshairPosition& SetCrosshairPositionRequest::pos() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.SetCrosshairPositionRequest.pos)
  return _internal_pos();
}
inline void SetCrosshairPositionRequest::unsafe_arena_set_allocated_pos(
    ::carbon::frontend::crosshair::CrosshairPosition* pos) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(pos_);
  }
  pos_ = pos;
  if (pos) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.crosshair.SetCrosshairPositionRequest.pos)
}
inline ::carbon::frontend::crosshair::CrosshairPosition* SetCrosshairPositionRequest::release_pos() {
  
  ::carbon::frontend::crosshair::CrosshairPosition* temp = pos_;
  pos_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::crosshair::CrosshairPosition* SetCrosshairPositionRequest::unsafe_arena_release_pos() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.SetCrosshairPositionRequest.pos)
  
  ::carbon::frontend::crosshair::CrosshairPosition* temp = pos_;
  pos_ = nullptr;
  return temp;
}
inline ::carbon::frontend::crosshair::CrosshairPosition* SetCrosshairPositionRequest::_internal_mutable_pos() {
  
  if (pos_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::crosshair::CrosshairPosition>(GetArenaForAllocation());
    pos_ = p;
  }
  return pos_;
}
inline ::carbon::frontend::crosshair::CrosshairPosition* SetCrosshairPositionRequest::mutable_pos() {
  ::carbon::frontend::crosshair::CrosshairPosition* _msg = _internal_mutable_pos();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.SetCrosshairPositionRequest.pos)
  return _msg;
}
inline void SetCrosshairPositionRequest::set_allocated_pos(::carbon::frontend::crosshair::CrosshairPosition* pos) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete pos_;
  }
  if (pos) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::crosshair::CrosshairPosition>::GetOwningArena(pos);
    if (message_arena != submessage_arena) {
      pos = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, pos, submessage_arena);
    }
    
  } else {
    
  }
  pos_ = pos;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.SetCrosshairPositionRequest.pos)
}

// -------------------------------------------------------------------

// MoveScannerRequest

// string cam_id = 1;
inline void MoveScannerRequest::clear_cam_id() {
  cam_id_.ClearToEmpty();
}
inline const std::string& MoveScannerRequest::cam_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.MoveScannerRequest.cam_id)
  return _internal_cam_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void MoveScannerRequest::set_cam_id(ArgT0&& arg0, ArgT... args) {
 
 cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.MoveScannerRequest.cam_id)
}
inline std::string* MoveScannerRequest::mutable_cam_id() {
  std::string* _s = _internal_mutable_cam_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.MoveScannerRequest.cam_id)
  return _s;
}
inline const std::string& MoveScannerRequest::_internal_cam_id() const {
  return cam_id_.Get();
}
inline void MoveScannerRequest::_internal_set_cam_id(const std::string& value) {
  
  cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* MoveScannerRequest::_internal_mutable_cam_id() {
  
  return cam_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* MoveScannerRequest::release_cam_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.MoveScannerRequest.cam_id)
  return cam_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void MoveScannerRequest::set_allocated_cam_id(std::string* cam_id) {
  if (cam_id != nullptr) {
    
  } else {
    
  }
  cam_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), cam_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (cam_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.MoveScannerRequest.cam_id)
}

// float x = 2;
inline void MoveScannerRequest::clear_x() {
  x_ = 0;
}
inline float MoveScannerRequest::_internal_x() const {
  return x_;
}
inline float MoveScannerRequest::x() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.MoveScannerRequest.x)
  return _internal_x();
}
inline void MoveScannerRequest::_internal_set_x(float value) {
  
  x_ = value;
}
inline void MoveScannerRequest::set_x(float value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.MoveScannerRequest.x)
}

// float y = 3;
inline void MoveScannerRequest::clear_y() {
  y_ = 0;
}
inline float MoveScannerRequest::_internal_y() const {
  return y_;
}
inline float MoveScannerRequest::y() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.MoveScannerRequest.y)
  return _internal_y();
}
inline void MoveScannerRequest::_internal_set_y(float value) {
  
  y_ = value;
}
inline void MoveScannerRequest::set_y(float value) {
  _internal_set_y(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.MoveScannerRequest.y)
}

// -------------------------------------------------------------------

// AutoCrossHairCalStateRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool AutoCrossHairCalStateRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool AutoCrossHairCalStateRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& AutoCrossHairCalStateRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& AutoCrossHairCalStateRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.AutoCrossHairCalStateRequest.ts)
  return _internal_ts();
}
inline void AutoCrossHairCalStateRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.crosshair.AutoCrossHairCalStateRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* AutoCrossHairCalStateRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* AutoCrossHairCalStateRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.AutoCrossHairCalStateRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* AutoCrossHairCalStateRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* AutoCrossHairCalStateRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.AutoCrossHairCalStateRequest.ts)
  return _msg;
}
inline void AutoCrossHairCalStateRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.AutoCrossHairCalStateRequest.ts)
}

// -------------------------------------------------------------------

// AutoCrossHairCalStateResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool AutoCrossHairCalStateResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool AutoCrossHairCalStateResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& AutoCrossHairCalStateResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& AutoCrossHairCalStateResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.ts)
  return _internal_ts();
}
inline void AutoCrossHairCalStateResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* AutoCrossHairCalStateResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* AutoCrossHairCalStateResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* AutoCrossHairCalStateResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* AutoCrossHairCalStateResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.ts)
  return _msg;
}
inline void AutoCrossHairCalStateResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.ts)
}

// bool in_progress = 2;
inline void AutoCrossHairCalStateResponse::clear_in_progress() {
  in_progress_ = false;
}
inline bool AutoCrossHairCalStateResponse::_internal_in_progress() const {
  return in_progress_;
}
inline bool AutoCrossHairCalStateResponse::in_progress() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.in_progress)
  return _internal_in_progress();
}
inline void AutoCrossHairCalStateResponse::_internal_set_in_progress(bool value) {
  
  in_progress_ = value;
}
inline void AutoCrossHairCalStateResponse::set_in_progress(bool value) {
  _internal_set_in_progress(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.in_progress)
}

// float progress = 3;
inline void AutoCrossHairCalStateResponse::clear_progress() {
  progress_ = 0;
}
inline float AutoCrossHairCalStateResponse::_internal_progress() const {
  return progress_;
}
inline float AutoCrossHairCalStateResponse::progress() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.progress)
  return _internal_progress();
}
inline void AutoCrossHairCalStateResponse::_internal_set_progress(float value) {
  
  progress_ = value;
}
inline void AutoCrossHairCalStateResponse::set_progress(float value) {
  _internal_set_progress(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.progress)
}

// repeated string failed = 4;
inline int AutoCrossHairCalStateResponse::_internal_failed_size() const {
  return failed_.size();
}
inline int AutoCrossHairCalStateResponse::failed_size() const {
  return _internal_failed_size();
}
inline void AutoCrossHairCalStateResponse::clear_failed() {
  failed_.Clear();
}
inline std::string* AutoCrossHairCalStateResponse::add_failed() {
  std::string* _s = _internal_add_failed();
  // @@protoc_insertion_point(field_add_mutable:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
  return _s;
}
inline const std::string& AutoCrossHairCalStateResponse::_internal_failed(int index) const {
  return failed_.Get(index);
}
inline const std::string& AutoCrossHairCalStateResponse::failed(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
  return _internal_failed(index);
}
inline std::string* AutoCrossHairCalStateResponse::mutable_failed(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
  return failed_.Mutable(index);
}
inline void AutoCrossHairCalStateResponse::set_failed(int index, const std::string& value) {
  failed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
}
inline void AutoCrossHairCalStateResponse::set_failed(int index, std::string&& value) {
  failed_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
}
inline void AutoCrossHairCalStateResponse::set_failed(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  failed_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
}
inline void AutoCrossHairCalStateResponse::set_failed(int index, const char* value, size_t size) {
  failed_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
}
inline std::string* AutoCrossHairCalStateResponse::_internal_add_failed() {
  return failed_.Add();
}
inline void AutoCrossHairCalStateResponse::add_failed(const std::string& value) {
  failed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
}
inline void AutoCrossHairCalStateResponse::add_failed(std::string&& value) {
  failed_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
}
inline void AutoCrossHairCalStateResponse::add_failed(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  failed_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
}
inline void AutoCrossHairCalStateResponse::add_failed(const char* value, size_t size) {
  failed_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
AutoCrossHairCalStateResponse::failed() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
  return failed_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
AutoCrossHairCalStateResponse::mutable_failed() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed)
  return &failed_;
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace crosshair
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fcrosshair_2eproto

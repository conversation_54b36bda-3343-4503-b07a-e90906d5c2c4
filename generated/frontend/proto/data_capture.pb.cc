// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/data_capture.proto

#include "frontend/proto/data_capture.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace data_capture {
constexpr DataCaptureRate::DataCaptureRate(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : rate_(0){}
struct DataCaptureRateDefaultTypeInternal {
  constexpr DataCaptureRateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DataCaptureRateDefaultTypeInternal() {}
  union {
    DataCaptureRate _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DataCaptureRateDefaultTypeInternal _DataCaptureRate_default_instance_;
constexpr DataCaptureState::DataCaptureState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : capture_status_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , upload_status_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , session_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , error_message_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr)
  , rate_(nullptr)
  , images_taken_(0u)
  , target_images_taken_(0u)
  , estimated_capture_remaining_time_ms_(uint64_t{0u})
  , images_uploaded_(0u)
  , target_images_uploaded_(0u)
  , estimated_upload_remaining_time_ms_(uint64_t{0u})
  , wireless_upload_available_(false)
  , usb_storage_connected_(false)
  , step_(0)
{}
struct DataCaptureStateDefaultTypeInternal {
  constexpr DataCaptureStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DataCaptureStateDefaultTypeInternal() {}
  union {
    DataCaptureState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DataCaptureStateDefaultTypeInternal _DataCaptureState_default_instance_;
constexpr DataCaptureSession::DataCaptureSession(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DataCaptureSessionDefaultTypeInternal {
  constexpr DataCaptureSessionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DataCaptureSessionDefaultTypeInternal() {}
  union {
    DataCaptureSession _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DataCaptureSessionDefaultTypeInternal _DataCaptureSession_default_instance_;
constexpr StartDataCaptureRequest::StartDataCaptureRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , rate_(0)
  , snap_capture_(false){}
struct StartDataCaptureRequestDefaultTypeInternal {
  constexpr StartDataCaptureRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StartDataCaptureRequestDefaultTypeInternal() {}
  union {
    StartDataCaptureRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StartDataCaptureRequestDefaultTypeInternal _StartDataCaptureRequest_default_instance_;
constexpr SnapImagesRequest::SnapImagesRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : crop_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , crop_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , session_name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , timestamp_ms_(int64_t{0}){}
struct SnapImagesRequestDefaultTypeInternal {
  constexpr SnapImagesRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SnapImagesRequestDefaultTypeInternal() {}
  union {
    SnapImagesRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SnapImagesRequestDefaultTypeInternal _SnapImagesRequest_default_instance_;
constexpr Session::Session(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , images_remaining_(0u)
  , is_uploading_(false)
  , has_completed_(false)
  , is_capturing_(false){}
struct SessionDefaultTypeInternal {
  constexpr SessionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SessionDefaultTypeInternal() {}
  union {
    Session _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SessionDefaultTypeInternal _Session_default_instance_;
constexpr AvailableSessionResponse::AvailableSessionResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : sessions_(){}
struct AvailableSessionResponseDefaultTypeInternal {
  constexpr AvailableSessionResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AvailableSessionResponseDefaultTypeInternal() {}
  union {
    AvailableSessionResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AvailableSessionResponseDefaultTypeInternal _AvailableSessionResponse_default_instance_;
constexpr SessionName::SessionName(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SessionNameDefaultTypeInternal {
  constexpr SessionNameDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SessionNameDefaultTypeInternal() {}
  union {
    SessionName _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SessionNameDefaultTypeInternal _SessionName_default_instance_;
constexpr RegularCaptureStatus::RegularCaptureStatus(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : uploaded_(0u)
  , budget_(0u)
  , last_upload_timestamp_(int64_t{0}){}
struct RegularCaptureStatusDefaultTypeInternal {
  constexpr RegularCaptureStatusDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RegularCaptureStatusDefaultTypeInternal() {}
  union {
    RegularCaptureStatus _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RegularCaptureStatusDefaultTypeInternal _RegularCaptureStatus_default_instance_;
}  // namespace data_capture
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[9];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fdata_5fcapture_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fdata_5fcapture_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureRate, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureRate, rate_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, images_taken_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, target_images_taken_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, estimated_capture_remaining_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, images_uploaded_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, target_images_uploaded_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, estimated_upload_remaining_time_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, rate_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, wireless_upload_available_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, usb_storage_connected_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, capture_status_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, upload_status_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, session_name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, step_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, error_message_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureState, crop_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureSession, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::DataCaptureSession, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::StartDataCaptureRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::StartDataCaptureRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::StartDataCaptureRequest, rate_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::StartDataCaptureRequest, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::StartDataCaptureRequest, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::StartDataCaptureRequest, snap_capture_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SnapImagesRequest, _has_bits_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SnapImagesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SnapImagesRequest, crop_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SnapImagesRequest, crop_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SnapImagesRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SnapImagesRequest, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SnapImagesRequest, session_name_),
  ~0u,
  ~0u,
  0,
  1,
  ~0u,
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::Session, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::Session, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::Session, images_remaining_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::Session, is_uploading_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::Session, has_completed_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::Session, is_capturing_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::AvailableSessionResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::AvailableSessionResponse, sessions_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SessionName, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::SessionName, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::RegularCaptureStatus, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::RegularCaptureStatus, uploaded_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::RegularCaptureStatus, budget_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::data_capture::RegularCaptureStatus, last_upload_timestamp_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::data_capture::DataCaptureRate)},
  { 7, -1, -1, sizeof(::carbon::frontend::data_capture::DataCaptureState)},
  { 30, -1, -1, sizeof(::carbon::frontend::data_capture::DataCaptureSession)},
  { 37, -1, -1, sizeof(::carbon::frontend::data_capture::StartDataCaptureRequest)},
  { 48, 59, -1, sizeof(::carbon::frontend::data_capture::SnapImagesRequest)},
  { 64, -1, -1, sizeof(::carbon::frontend::data_capture::Session)},
  { 75, -1, -1, sizeof(::carbon::frontend::data_capture::AvailableSessionResponse)},
  { 82, -1, -1, sizeof(::carbon::frontend::data_capture::SessionName)},
  { 89, -1, -1, sizeof(::carbon::frontend::data_capture::RegularCaptureStatus)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_DataCaptureRate_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_DataCaptureState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_DataCaptureSession_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_StartDataCaptureRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_SnapImagesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_Session_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_AvailableSessionResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_SessionName_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::data_capture::_RegularCaptureStatus_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fdata_5fcapture_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n!frontend/proto/data_capture.proto\022\034car"
  "bon.frontend.data_capture\032\031frontend/prot"
  "o/util.proto\"\037\n\017DataCaptureRate\022\014\n\004rate\030"
  "\001 \001(\001\"\271\004\n\020DataCaptureState\022+\n\002ts\030\001 \001(\0132\037"
  ".carbon.frontend.util.Timestamp\022\024\n\014image"
  "s_taken\030\002 \001(\r\022\033\n\023target_images_taken\030\003 \001"
  "(\r\022+\n#estimated_capture_remaining_time_m"
  "s\030\004 \001(\004\022\027\n\017images_uploaded\030\005 \001(\r\022\036\n\026targ"
  "et_images_uploaded\030\006 \001(\r\022*\n\"estimated_up"
  "load_remaining_time_ms\030\007 \001(\004\022;\n\004rate\030\010 \001"
  "(\0132-.carbon.frontend.data_capture.DataCa"
  "ptureRate\022!\n\031wireless_upload_available\030\t"
  " \001(\010\022\035\n\025usb_storage_connected\030\n \001(\010\022\026\n\016c"
  "apture_status\030\013 \001(\t\022\025\n\rupload_status\030\014 \001"
  "(\t\022\024\n\014session_name\030\r \001(\t\0229\n\004step\030\016 \001(\0162+"
  ".carbon.frontend.data_capture.ProcedureS"
  "tep\022\014\n\004crop\030\017 \001(\t\022\025\n\rerror_message\030\020 \001(\t"
  "\022\017\n\007crop_id\030\021 \001(\t\"\"\n\022DataCaptureSession\022"
  "\014\n\004name\030\001 \001(\t\"j\n\027StartDataCaptureRequest"
  "\022\014\n\004name\030\001 \001(\t\022\014\n\004rate\030\002 \001(\001\022\014\n\004crop\030\003 \001"
  "(\t\022\017\n\007crop_id\030\004 \001(\t\022\024\n\014snap_capture\030\005 \001("
  "\010\"\224\001\n\021SnapImagesRequest\022\014\n\004crop\030\001 \001(\t\022\017\n"
  "\007crop_id\030\002 \001(\t\022\023\n\006cam_id\030\003 \001(\tH\000\210\001\001\022\031\n\014t"
  "imestamp_ms\030\004 \001(\003H\001\210\001\001\022\024\n\014session_name\030\005"
  " \001(\tB\t\n\007_cam_idB\017\n\r_timestamp_ms\"t\n\007Sess"
  "ion\022\014\n\004name\030\001 \001(\t\022\030\n\020images_remaining\030\002 "
  "\001(\r\022\024\n\014is_uploading\030\003 \001(\010\022\025\n\rhas_complet"
  "ed\030\004 \001(\010\022\024\n\014is_capturing\030\005 \001(\010\"S\n\030Availa"
  "bleSessionResponse\0227\n\010sessions\030\001 \003(\0132%.c"
  "arbon.frontend.data_capture.Session\"\033\n\013S"
  "essionName\022\014\n\004name\030\001 \001(\t\"W\n\024RegularCaptu"
  "reStatus\022\020\n\010uploaded\030\001 \001(\r\022\016\n\006budget\030\002 \001"
  "(\r\022\035\n\025last_upload_timestamp\030\003 \001(\003*%\n\014Upl"
  "oadMethod\022\014\n\010WIRELESS\020\000\022\007\n\003USB\020\001*\315\001\n\rPro"
  "cedureStep\022\007\n\003NEW\020\000\022\r\n\tCAPTURING\020\001\022\022\n\016CA"
  "PTURE_PAUSED\020\002\022\024\n\020CAPTURE_COMPLETE\020\003\022\026\n\022"
  "UPLOADING_WIRELESS\020\004\022\035\n\031UPLOADING_WIRELE"
  "SS_PAUSED\020\005\022\021\n\rUPLOADING_USB\020\006\022\030\n\024UPLOAD"
  "ING_USB_PAUSED\020\007\022\026\n\022UPLOADING_COMPLETE\020\010"
  "2\245\016\n\022DataCaptureService\022f\n\020StartDataCapt"
  "ure\0225.carbon.frontend.data_capture.Start"
  "DataCaptureRequest\032\033.carbon.frontend.uti"
  "l.Empty\022L\n\020PauseDataCapture\022\033.carbon.fro"
  "ntend.util.Empty\032\033.carbon.frontend.util."
  "Empty\022K\n\017StopDataCapture\022\033.carbon.fronte"
  "nd.util.Empty\032\033.carbon.frontend.util.Emp"
  "ty\022M\n\021ResumeDataCapture\022\033.carbon.fronten"
  "d.util.Empty\032\033.carbon.frontend.util.Empt"
  "y\022O\n\023CompleteDataCapture\022\033.carbon.fronte"
  "nd.util.Empty\032\033.carbon.frontend.util.Emp"
  "ty\022Z\n\036StartDataCaptureWirelessUpload\022\033.c"
  "arbon.frontend.util.Empty\032\033.carbon.front"
  "end.util.Empty\022U\n\031StartDataCaptureUSBUpl"
  "oad\022\033.carbon.frontend.util.Empty\032\033.carbo"
  "n.frontend.util.Empty\022Q\n\025StopDataCapture"
  "Upload\022\033.carbon.frontend.util.Empty\032\033.ca"
  "rbon.frontend.util.Empty\022R\n\026PauseDataCap"
  "tureUpload\022\033.carbon.frontend.util.Empty\032"
  "\033.carbon.frontend.util.Empty\022S\n\027ResumeDa"
  "taCaptureUpload\022\033.carbon.frontend.util.E"
  "mpty\032\033.carbon.frontend.util.Empty\022r\n(Sta"
  "rtBackgroundDataCaptureWirelessUpload\022)."
  "carbon.frontend.data_capture.SessionName"
  "\032\033.carbon.frontend.util.Empty\022m\n#StartBa"
  "ckgroundDataCaptureUSBUpload\022).carbon.fr"
  "ontend.data_capture.SessionName\032\033.carbon"
  ".frontend.util.Empty\022i\n\037StopBackgroundDa"
  "taCaptureUpload\022).carbon.frontend.data_c"
  "apture.SessionName\032\033.carbon.frontend.uti"
  "l.Empty\022j\n PauseBackgroundDataCaptureUpl"
  "oad\022).carbon.frontend.data_capture.Sessi"
  "onName\032\033.carbon.frontend.util.Empty\022k\n!R"
  "esumeBackgroundDataCaptureUpload\022).carbo"
  "n.frontend.data_capture.SessionName\032\033.ca"
  "rbon.frontend.util.Empty\022j\n\027GetNextDataC"
  "aptureState\022\037.carbon.frontend.util.Times"
  "tamp\032..carbon.frontend.data_capture.Data"
  "CaptureState\022Z\n\nSnapImages\022/.carbon.fron"
  "tend.data_capture.SnapImagesRequest\032\033.ca"
  "rbon.frontend.util.Empty\022b\n\013GetSessions\022"
  "\033.carbon.frontend.util.Empty\0326.carbon.fr"
  "ontend.data_capture.AvailableSessionResp"
  "onse\022j\n\027GetRegularCaptureStatus\022\033.carbon"
  ".frontend.util.Empty\0322.carbon.frontend.d"
  "ata_capture.RegularCaptureStatusB\020Z\016prot"
  "o/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_deps[1] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto = {
  false, false, 3418, descriptor_table_protodef_frontend_2fproto_2fdata_5fcapture_2eproto, "frontend/proto/data_capture.proto", 
  &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once, descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_deps, 1, 9,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fdata_5fcapture_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto, file_level_enum_descriptors_frontend_2fproto_2fdata_5fcapture_2eproto, file_level_service_descriptors_frontend_2fproto_2fdata_5fcapture_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fdata_5fcapture_2eproto(&descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto);
namespace carbon {
namespace frontend {
namespace data_capture {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* UploadMethod_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fdata_5fcapture_2eproto[0];
}
bool UploadMethod_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ProcedureStep_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fdata_5fcapture_2eproto[1];
}
bool ProcedureStep_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
    case 7:
    case 8:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class DataCaptureRate::_Internal {
 public:
};

DataCaptureRate::DataCaptureRate(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.DataCaptureRate)
}
DataCaptureRate::DataCaptureRate(const DataCaptureRate& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  rate_ = from.rate_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.DataCaptureRate)
}

inline void DataCaptureRate::SharedCtor() {
rate_ = 0;
}

DataCaptureRate::~DataCaptureRate() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.DataCaptureRate)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DataCaptureRate::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DataCaptureRate::ArenaDtor(void* object) {
  DataCaptureRate* _this = reinterpret_cast< DataCaptureRate* >(object);
  (void)_this;
}
void DataCaptureRate::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DataCaptureRate::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DataCaptureRate::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.DataCaptureRate)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  rate_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DataCaptureRate::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double rate = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          rate_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DataCaptureRate::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.DataCaptureRate)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double rate = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_rate = this->_internal_rate();
  uint64_t raw_rate;
  memcpy(&raw_rate, &tmp_rate, sizeof(tmp_rate));
  if (raw_rate != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_rate(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.DataCaptureRate)
  return target;
}

size_t DataCaptureRate::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.DataCaptureRate)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double rate = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_rate = this->_internal_rate();
  uint64_t raw_rate;
  memcpy(&raw_rate, &tmp_rate, sizeof(tmp_rate));
  if (raw_rate != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DataCaptureRate::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DataCaptureRate::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DataCaptureRate::GetClassData() const { return &_class_data_; }

void DataCaptureRate::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DataCaptureRate *>(to)->MergeFrom(
      static_cast<const DataCaptureRate &>(from));
}


void DataCaptureRate::MergeFrom(const DataCaptureRate& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.DataCaptureRate)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_rate = from._internal_rate();
  uint64_t raw_rate;
  memcpy(&raw_rate, &tmp_rate, sizeof(tmp_rate));
  if (raw_rate != 0) {
    _internal_set_rate(from._internal_rate());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DataCaptureRate::CopyFrom(const DataCaptureRate& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.DataCaptureRate)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataCaptureRate::IsInitialized() const {
  return true;
}

void DataCaptureRate::InternalSwap(DataCaptureRate* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(rate_, other->rate_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DataCaptureRate::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[0]);
}

// ===================================================================

class DataCaptureState::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const DataCaptureState* msg);
  static const ::carbon::frontend::data_capture::DataCaptureRate& rate(const DataCaptureState* msg);
};

const ::carbon::frontend::util::Timestamp&
DataCaptureState::_Internal::ts(const DataCaptureState* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::data_capture::DataCaptureRate&
DataCaptureState::_Internal::rate(const DataCaptureState* msg) {
  return *msg->rate_;
}
void DataCaptureState::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
DataCaptureState::DataCaptureState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.DataCaptureState)
}
DataCaptureState::DataCaptureState(const DataCaptureState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  capture_status_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    capture_status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_capture_status().empty()) {
    capture_status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_capture_status(), 
      GetArenaForAllocation());
  }
  upload_status_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    upload_status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_upload_status().empty()) {
    upload_status_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_upload_status(), 
      GetArenaForAllocation());
  }
  session_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    session_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_session_name().empty()) {
    session_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_session_name(), 
      GetArenaForAllocation());
  }
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  error_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_error_message().empty()) {
    error_message_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_error_message(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_rate()) {
    rate_ = new ::carbon::frontend::data_capture::DataCaptureRate(*from.rate_);
  } else {
    rate_ = nullptr;
  }
  ::memcpy(&images_taken_, &from.images_taken_,
    static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&images_taken_)) + sizeof(step_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.DataCaptureState)
}

inline void DataCaptureState::SharedCtor() {
capture_status_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  capture_status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
upload_status_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  upload_status_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
session_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  session_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
error_message_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  error_message_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(step_));
}

DataCaptureState::~DataCaptureState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.DataCaptureState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DataCaptureState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  capture_status_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  upload_status_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  session_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  error_message_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete rate_;
}

void DataCaptureState::ArenaDtor(void* object) {
  DataCaptureState* _this = reinterpret_cast< DataCaptureState* >(object);
  (void)_this;
}
void DataCaptureState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DataCaptureState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DataCaptureState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.DataCaptureState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  capture_status_.ClearToEmpty();
  upload_status_.ClearToEmpty();
  session_name_.ClearToEmpty();
  crop_.ClearToEmpty();
  error_message_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && rate_ != nullptr) {
    delete rate_;
  }
  rate_ = nullptr;
  ::memset(&images_taken_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&step_) -
      reinterpret_cast<char*>(&images_taken_)) + sizeof(step_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DataCaptureState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 images_taken = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          images_taken_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 target_images_taken = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          target_images_taken_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 estimated_capture_remaining_time_ms = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          estimated_capture_remaining_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 images_uploaded = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          images_uploaded_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 target_images_uploaded = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          target_images_uploaded_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 estimated_upload_remaining_time_ms = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          estimated_upload_remaining_time_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.data_capture.DataCaptureRate rate = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 66)) {
          ptr = ctx->ParseMessage(_internal_mutable_rate(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool wireless_upload_available = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          wireless_upload_available_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool usb_storage_connected = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 80)) {
          usb_storage_connected_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string capture_status = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 90)) {
          auto str = _internal_mutable_capture_status();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.DataCaptureState.capture_status"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string upload_status = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 98)) {
          auto str = _internal_mutable_upload_status();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.DataCaptureState.upload_status"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string session_name = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          auto str = _internal_mutable_session_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.DataCaptureState.session_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.data_capture.ProcedureStep step = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 112)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_step(static_cast<::carbon::frontend::data_capture::ProcedureStep>(val));
        } else
          goto handle_unusual;
        continue;
      // string crop = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.DataCaptureState.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string error_message = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 130)) {
          auto str = _internal_mutable_error_message();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.DataCaptureState.error_message"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 138)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.DataCaptureState.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DataCaptureState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.DataCaptureState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // uint32 images_taken = 2;
  if (this->_internal_images_taken() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_images_taken(), target);
  }

  // uint32 target_images_taken = 3;
  if (this->_internal_target_images_taken() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(3, this->_internal_target_images_taken(), target);
  }

  // uint64 estimated_capture_remaining_time_ms = 4;
  if (this->_internal_estimated_capture_remaining_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(4, this->_internal_estimated_capture_remaining_time_ms(), target);
  }

  // uint32 images_uploaded = 5;
  if (this->_internal_images_uploaded() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(5, this->_internal_images_uploaded(), target);
  }

  // uint32 target_images_uploaded = 6;
  if (this->_internal_target_images_uploaded() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(6, this->_internal_target_images_uploaded(), target);
  }

  // uint64 estimated_upload_remaining_time_ms = 7;
  if (this->_internal_estimated_upload_remaining_time_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(7, this->_internal_estimated_upload_remaining_time_ms(), target);
  }

  // .carbon.frontend.data_capture.DataCaptureRate rate = 8;
  if (this->_internal_has_rate()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        8, _Internal::rate(this), target, stream);
  }

  // bool wireless_upload_available = 9;
  if (this->_internal_wireless_upload_available() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(9, this->_internal_wireless_upload_available(), target);
  }

  // bool usb_storage_connected = 10;
  if (this->_internal_usb_storage_connected() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(10, this->_internal_usb_storage_connected(), target);
  }

  // string capture_status = 11;
  if (!this->_internal_capture_status().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_capture_status().data(), static_cast<int>(this->_internal_capture_status().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.DataCaptureState.capture_status");
    target = stream->WriteStringMaybeAliased(
        11, this->_internal_capture_status(), target);
  }

  // string upload_status = 12;
  if (!this->_internal_upload_status().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_upload_status().data(), static_cast<int>(this->_internal_upload_status().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.DataCaptureState.upload_status");
    target = stream->WriteStringMaybeAliased(
        12, this->_internal_upload_status(), target);
  }

  // string session_name = 13;
  if (!this->_internal_session_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_session_name().data(), static_cast<int>(this->_internal_session_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.DataCaptureState.session_name");
    target = stream->WriteStringMaybeAliased(
        13, this->_internal_session_name(), target);
  }

  // .carbon.frontend.data_capture.ProcedureStep step = 14;
  if (this->_internal_step() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      14, this->_internal_step(), target);
  }

  // string crop = 15;
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.DataCaptureState.crop");
    target = stream->WriteStringMaybeAliased(
        15, this->_internal_crop(), target);
  }

  // string error_message = 16;
  if (!this->_internal_error_message().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_error_message().data(), static_cast<int>(this->_internal_error_message().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.DataCaptureState.error_message");
    target = stream->WriteStringMaybeAliased(
        16, this->_internal_error_message(), target);
  }

  // string crop_id = 17;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.DataCaptureState.crop_id");
    target = stream->WriteStringMaybeAliased(
        17, this->_internal_crop_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.DataCaptureState)
  return target;
}

size_t DataCaptureState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.DataCaptureState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string capture_status = 11;
  if (!this->_internal_capture_status().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_capture_status());
  }

  // string upload_status = 12;
  if (!this->_internal_upload_status().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_upload_status());
  }

  // string session_name = 13;
  if (!this->_internal_session_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_session_name());
  }

  // string crop = 15;
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string error_message = 16;
  if (!this->_internal_error_message().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_error_message());
  }

  // string crop_id = 17;
  if (!this->_internal_crop_id().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.data_capture.DataCaptureRate rate = 8;
  if (this->_internal_has_rate()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *rate_);
  }

  // uint32 images_taken = 2;
  if (this->_internal_images_taken() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_images_taken());
  }

  // uint32 target_images_taken = 3;
  if (this->_internal_target_images_taken() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_target_images_taken());
  }

  // uint64 estimated_capture_remaining_time_ms = 4;
  if (this->_internal_estimated_capture_remaining_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_estimated_capture_remaining_time_ms());
  }

  // uint32 images_uploaded = 5;
  if (this->_internal_images_uploaded() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_images_uploaded());
  }

  // uint32 target_images_uploaded = 6;
  if (this->_internal_target_images_uploaded() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_target_images_uploaded());
  }

  // uint64 estimated_upload_remaining_time_ms = 7;
  if (this->_internal_estimated_upload_remaining_time_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_estimated_upload_remaining_time_ms());
  }

  // bool wireless_upload_available = 9;
  if (this->_internal_wireless_upload_available() != 0) {
    total_size += 1 + 1;
  }

  // bool usb_storage_connected = 10;
  if (this->_internal_usb_storage_connected() != 0) {
    total_size += 1 + 1;
  }

  // .carbon.frontend.data_capture.ProcedureStep step = 14;
  if (this->_internal_step() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_step());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DataCaptureState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DataCaptureState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DataCaptureState::GetClassData() const { return &_class_data_; }

void DataCaptureState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DataCaptureState *>(to)->MergeFrom(
      static_cast<const DataCaptureState &>(from));
}


void DataCaptureState::MergeFrom(const DataCaptureState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.DataCaptureState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_capture_status().empty()) {
    _internal_set_capture_status(from._internal_capture_status());
  }
  if (!from._internal_upload_status().empty()) {
    _internal_set_upload_status(from._internal_upload_status());
  }
  if (!from._internal_session_name().empty()) {
    _internal_set_session_name(from._internal_session_name());
  }
  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_error_message().empty()) {
    _internal_set_error_message(from._internal_error_message());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_rate()) {
    _internal_mutable_rate()->::carbon::frontend::data_capture::DataCaptureRate::MergeFrom(from._internal_rate());
  }
  if (from._internal_images_taken() != 0) {
    _internal_set_images_taken(from._internal_images_taken());
  }
  if (from._internal_target_images_taken() != 0) {
    _internal_set_target_images_taken(from._internal_target_images_taken());
  }
  if (from._internal_estimated_capture_remaining_time_ms() != 0) {
    _internal_set_estimated_capture_remaining_time_ms(from._internal_estimated_capture_remaining_time_ms());
  }
  if (from._internal_images_uploaded() != 0) {
    _internal_set_images_uploaded(from._internal_images_uploaded());
  }
  if (from._internal_target_images_uploaded() != 0) {
    _internal_set_target_images_uploaded(from._internal_target_images_uploaded());
  }
  if (from._internal_estimated_upload_remaining_time_ms() != 0) {
    _internal_set_estimated_upload_remaining_time_ms(from._internal_estimated_upload_remaining_time_ms());
  }
  if (from._internal_wireless_upload_available() != 0) {
    _internal_set_wireless_upload_available(from._internal_wireless_upload_available());
  }
  if (from._internal_usb_storage_connected() != 0) {
    _internal_set_usb_storage_connected(from._internal_usb_storage_connected());
  }
  if (from._internal_step() != 0) {
    _internal_set_step(from._internal_step());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DataCaptureState::CopyFrom(const DataCaptureState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.DataCaptureState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataCaptureState::IsInitialized() const {
  return true;
}

void DataCaptureState::InternalSwap(DataCaptureState* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &capture_status_, lhs_arena,
      &other->capture_status_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &upload_status_, lhs_arena,
      &other->upload_status_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &session_name_, lhs_arena,
      &other->session_name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &error_message_, lhs_arena,
      &other->error_message_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DataCaptureState, step_)
      + sizeof(DataCaptureState::step_)
      - PROTOBUF_FIELD_OFFSET(DataCaptureState, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DataCaptureState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[1]);
}

// ===================================================================

class DataCaptureSession::_Internal {
 public:
};

DataCaptureSession::DataCaptureSession(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.DataCaptureSession)
}
DataCaptureSession::DataCaptureSession(const DataCaptureSession& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.DataCaptureSession)
}

inline void DataCaptureSession::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DataCaptureSession::~DataCaptureSession() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.DataCaptureSession)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DataCaptureSession::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DataCaptureSession::ArenaDtor(void* object) {
  DataCaptureSession* _this = reinterpret_cast< DataCaptureSession* >(object);
  (void)_this;
}
void DataCaptureSession::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DataCaptureSession::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DataCaptureSession::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.DataCaptureSession)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DataCaptureSession::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.DataCaptureSession.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DataCaptureSession::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.DataCaptureSession)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.DataCaptureSession.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.DataCaptureSession)
  return target;
}

size_t DataCaptureSession::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.DataCaptureSession)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DataCaptureSession::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DataCaptureSession::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DataCaptureSession::GetClassData() const { return &_class_data_; }

void DataCaptureSession::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DataCaptureSession *>(to)->MergeFrom(
      static_cast<const DataCaptureSession &>(from));
}


void DataCaptureSession::MergeFrom(const DataCaptureSession& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.DataCaptureSession)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DataCaptureSession::CopyFrom(const DataCaptureSession& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.DataCaptureSession)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DataCaptureSession::IsInitialized() const {
  return true;
}

void DataCaptureSession::InternalSwap(DataCaptureSession* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DataCaptureSession::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[2]);
}

// ===================================================================

class StartDataCaptureRequest::_Internal {
 public:
};

StartDataCaptureRequest::StartDataCaptureRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.StartDataCaptureRequest)
}
StartDataCaptureRequest::StartDataCaptureRequest(const StartDataCaptureRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&rate_, &from.rate_,
    static_cast<size_t>(reinterpret_cast<char*>(&snap_capture_) -
    reinterpret_cast<char*>(&rate_)) + sizeof(snap_capture_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.StartDataCaptureRequest)
}

inline void StartDataCaptureRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&rate_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&snap_capture_) -
    reinterpret_cast<char*>(&rate_)) + sizeof(snap_capture_));
}

StartDataCaptureRequest::~StartDataCaptureRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.StartDataCaptureRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StartDataCaptureRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void StartDataCaptureRequest::ArenaDtor(void* object) {
  StartDataCaptureRequest* _this = reinterpret_cast< StartDataCaptureRequest* >(object);
  (void)_this;
}
void StartDataCaptureRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StartDataCaptureRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StartDataCaptureRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.StartDataCaptureRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  crop_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  ::memset(&rate_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&snap_capture_) -
      reinterpret_cast<char*>(&rate_)) + sizeof(snap_capture_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StartDataCaptureRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.StartDataCaptureRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double rate = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          rate_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // string crop = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.StartDataCaptureRequest.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.StartDataCaptureRequest.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool snap_capture = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          snap_capture_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StartDataCaptureRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.StartDataCaptureRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.StartDataCaptureRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // double rate = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_rate = this->_internal_rate();
  uint64_t raw_rate;
  memcpy(&raw_rate, &tmp_rate, sizeof(tmp_rate));
  if (raw_rate != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_rate(), target);
  }

  // string crop = 3;
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.StartDataCaptureRequest.crop");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_crop(), target);
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.StartDataCaptureRequest.crop_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_crop_id(), target);
  }

  // bool snap_capture = 5;
  if (this->_internal_snap_capture() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_snap_capture(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.StartDataCaptureRequest)
  return target;
}

size_t StartDataCaptureRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.StartDataCaptureRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string crop = 3;
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string crop_id = 4;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // double rate = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_rate = this->_internal_rate();
  uint64_t raw_rate;
  memcpy(&raw_rate, &tmp_rate, sizeof(tmp_rate));
  if (raw_rate != 0) {
    total_size += 1 + 8;
  }

  // bool snap_capture = 5;
  if (this->_internal_snap_capture() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StartDataCaptureRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StartDataCaptureRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StartDataCaptureRequest::GetClassData() const { return &_class_data_; }

void StartDataCaptureRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StartDataCaptureRequest *>(to)->MergeFrom(
      static_cast<const StartDataCaptureRequest &>(from));
}


void StartDataCaptureRequest::MergeFrom(const StartDataCaptureRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.StartDataCaptureRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_rate = from._internal_rate();
  uint64_t raw_rate;
  memcpy(&raw_rate, &tmp_rate, sizeof(tmp_rate));
  if (raw_rate != 0) {
    _internal_set_rate(from._internal_rate());
  }
  if (from._internal_snap_capture() != 0) {
    _internal_set_snap_capture(from._internal_snap_capture());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StartDataCaptureRequest::CopyFrom(const StartDataCaptureRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.StartDataCaptureRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StartDataCaptureRequest::IsInitialized() const {
  return true;
}

void StartDataCaptureRequest::InternalSwap(StartDataCaptureRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(StartDataCaptureRequest, snap_capture_)
      + sizeof(StartDataCaptureRequest::snap_capture_)
      - PROTOBUF_FIELD_OFFSET(StartDataCaptureRequest, rate_)>(
          reinterpret_cast<char*>(&rate_),
          reinterpret_cast<char*>(&other->rate_));
}

::PROTOBUF_NAMESPACE_ID::Metadata StartDataCaptureRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[3]);
}

// ===================================================================

class SnapImagesRequest::_Internal {
 public:
  using HasBits = decltype(std::declval<SnapImagesRequest>()._has_bits_);
  static void set_has_cam_id(HasBits* has_bits) {
    (*has_bits)[0] |= 1u;
  }
  static void set_has_timestamp_ms(HasBits* has_bits) {
    (*has_bits)[0] |= 2u;
  }
};

SnapImagesRequest::SnapImagesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.SnapImagesRequest)
}
SnapImagesRequest::SnapImagesRequest(const SnapImagesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      _has_bits_(from._has_bits_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop().empty()) {
    crop_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop(), 
      GetArenaForAllocation());
  }
  crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_crop_id().empty()) {
    crop_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_crop_id(), 
      GetArenaForAllocation());
  }
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from._internal_has_cam_id()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  session_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    session_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_session_name().empty()) {
    session_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_session_name(), 
      GetArenaForAllocation());
  }
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.SnapImagesRequest)
}

inline void SnapImagesRequest::SharedCtor() {
crop_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
crop_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  crop_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
session_name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  session_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
timestamp_ms_ = int64_t{0};
}

SnapImagesRequest::~SnapImagesRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.SnapImagesRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SnapImagesRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  crop_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  crop_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  session_name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SnapImagesRequest::ArenaDtor(void* object) {
  SnapImagesRequest* _this = reinterpret_cast< SnapImagesRequest* >(object);
  (void)_this;
}
void SnapImagesRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SnapImagesRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SnapImagesRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.SnapImagesRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  crop_.ClearToEmpty();
  crop_id_.ClearToEmpty();
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    cam_id_.ClearNonDefaultToEmpty();
  }
  session_name_.ClearToEmpty();
  timestamp_ms_ = int64_t{0};
  _has_bits_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SnapImagesRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  _Internal::HasBits has_bits{};
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string crop = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_crop();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.SnapImagesRequest.crop"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string crop_id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_crop_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.SnapImagesRequest.crop_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional string cam_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.SnapImagesRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // optional int64 timestamp_ms = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          _Internal::set_has_timestamp_ms(&has_bits);
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string session_name = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_session_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.SnapImagesRequest.session_name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  _has_bits_.Or(has_bits);
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SnapImagesRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.SnapImagesRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string crop = 1;
  if (!this->_internal_crop().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop().data(), static_cast<int>(this->_internal_crop().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.SnapImagesRequest.crop");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_crop(), target);
  }

  // string crop_id = 2;
  if (!this->_internal_crop_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_crop_id().data(), static_cast<int>(this->_internal_crop_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.SnapImagesRequest.crop_id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_crop_id(), target);
  }

  // optional string cam_id = 3;
  if (_internal_has_cam_id()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.SnapImagesRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_cam_id(), target);
  }

  // optional int64 timestamp_ms = 4;
  if (_internal_has_timestamp_ms()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(4, this->_internal_timestamp_ms(), target);
  }

  // string session_name = 5;
  if (!this->_internal_session_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_session_name().data(), static_cast<int>(this->_internal_session_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.SnapImagesRequest.session_name");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_session_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.SnapImagesRequest)
  return target;
}

size_t SnapImagesRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.SnapImagesRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string crop = 1;
  if (!this->_internal_crop().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop());
  }

  // string crop_id = 2;
  if (!this->_internal_crop_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_crop_id());
  }

  // optional string cam_id = 3;
  cached_has_bits = _has_bits_[0];
  if (cached_has_bits & 0x00000001u) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // string session_name = 5;
  if (!this->_internal_session_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_session_name());
  }

  // optional int64 timestamp_ms = 4;
  if (cached_has_bits & 0x00000002u) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SnapImagesRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SnapImagesRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SnapImagesRequest::GetClassData() const { return &_class_data_; }

void SnapImagesRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SnapImagesRequest *>(to)->MergeFrom(
      static_cast<const SnapImagesRequest &>(from));
}


void SnapImagesRequest::MergeFrom(const SnapImagesRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.SnapImagesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_crop().empty()) {
    _internal_set_crop(from._internal_crop());
  }
  if (!from._internal_crop_id().empty()) {
    _internal_set_crop_id(from._internal_crop_id());
  }
  if (from._internal_has_cam_id()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  if (!from._internal_session_name().empty()) {
    _internal_set_session_name(from._internal_session_name());
  }
  if (from._internal_has_timestamp_ms()) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SnapImagesRequest::CopyFrom(const SnapImagesRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.SnapImagesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SnapImagesRequest::IsInitialized() const {
  return true;
}

void SnapImagesRequest::InternalSwap(SnapImagesRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(_has_bits_[0], other->_has_bits_[0]);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_, lhs_arena,
      &other->crop_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &crop_id_, lhs_arena,
      &other->crop_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &session_name_, lhs_arena,
      &other->session_name_, rhs_arena
  );
  swap(timestamp_ms_, other->timestamp_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SnapImagesRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[4]);
}

// ===================================================================

class Session::_Internal {
 public:
};

Session::Session(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.Session)
}
Session::Session(const Session& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  ::memcpy(&images_remaining_, &from.images_remaining_,
    static_cast<size_t>(reinterpret_cast<char*>(&is_capturing_) -
    reinterpret_cast<char*>(&images_remaining_)) + sizeof(is_capturing_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.Session)
}

inline void Session::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&images_remaining_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&is_capturing_) -
    reinterpret_cast<char*>(&images_remaining_)) + sizeof(is_capturing_));
}

Session::~Session() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.Session)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void Session::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void Session::ArenaDtor(void* object) {
  Session* _this = reinterpret_cast< Session* >(object);
  (void)_this;
}
void Session::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void Session::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void Session::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.Session)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  ::memset(&images_remaining_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&is_capturing_) -
      reinterpret_cast<char*>(&images_remaining_)) + sizeof(is_capturing_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* Session::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.Session.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 images_remaining = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          images_remaining_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_uploading = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          is_uploading_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool has_completed = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          has_completed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool is_capturing = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          is_capturing_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* Session::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.Session)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.Session.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // uint32 images_remaining = 2;
  if (this->_internal_images_remaining() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_images_remaining(), target);
  }

  // bool is_uploading = 3;
  if (this->_internal_is_uploading() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_is_uploading(), target);
  }

  // bool has_completed = 4;
  if (this->_internal_has_completed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_has_completed(), target);
  }

  // bool is_capturing = 5;
  if (this->_internal_is_capturing() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_is_capturing(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.Session)
  return target;
}

size_t Session::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.Session)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // uint32 images_remaining = 2;
  if (this->_internal_images_remaining() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_images_remaining());
  }

  // bool is_uploading = 3;
  if (this->_internal_is_uploading() != 0) {
    total_size += 1 + 1;
  }

  // bool has_completed = 4;
  if (this->_internal_has_completed() != 0) {
    total_size += 1 + 1;
  }

  // bool is_capturing = 5;
  if (this->_internal_is_capturing() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData Session::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    Session::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*Session::GetClassData() const { return &_class_data_; }

void Session::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<Session *>(to)->MergeFrom(
      static_cast<const Session &>(from));
}


void Session::MergeFrom(const Session& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.Session)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_images_remaining() != 0) {
    _internal_set_images_remaining(from._internal_images_remaining());
  }
  if (from._internal_is_uploading() != 0) {
    _internal_set_is_uploading(from._internal_is_uploading());
  }
  if (from._internal_has_completed() != 0) {
    _internal_set_has_completed(from._internal_has_completed());
  }
  if (from._internal_is_capturing() != 0) {
    _internal_set_is_capturing(from._internal_is_capturing());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void Session::CopyFrom(const Session& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.Session)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool Session::IsInitialized() const {
  return true;
}

void Session::InternalSwap(Session* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(Session, is_capturing_)
      + sizeof(Session::is_capturing_)
      - PROTOBUF_FIELD_OFFSET(Session, images_remaining_)>(
          reinterpret_cast<char*>(&images_remaining_),
          reinterpret_cast<char*>(&other->images_remaining_));
}

::PROTOBUF_NAMESPACE_ID::Metadata Session::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[5]);
}

// ===================================================================

class AvailableSessionResponse::_Internal {
 public:
};

AvailableSessionResponse::AvailableSessionResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  sessions_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.AvailableSessionResponse)
}
AvailableSessionResponse::AvailableSessionResponse(const AvailableSessionResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      sessions_(from.sessions_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.AvailableSessionResponse)
}

inline void AvailableSessionResponse::SharedCtor() {
}

AvailableSessionResponse::~AvailableSessionResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.AvailableSessionResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AvailableSessionResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void AvailableSessionResponse::ArenaDtor(void* object) {
  AvailableSessionResponse* _this = reinterpret_cast< AvailableSessionResponse* >(object);
  (void)_this;
}
void AvailableSessionResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AvailableSessionResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AvailableSessionResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.AvailableSessionResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  sessions_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AvailableSessionResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.frontend.data_capture.Session sessions = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_sessions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AvailableSessionResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.AvailableSessionResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.frontend.data_capture.Session sessions = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_sessions_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_sessions(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.AvailableSessionResponse)
  return target;
}

size_t AvailableSessionResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.AvailableSessionResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.frontend.data_capture.Session sessions = 1;
  total_size += 1UL * this->_internal_sessions_size();
  for (const auto& msg : this->sessions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AvailableSessionResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AvailableSessionResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AvailableSessionResponse::GetClassData() const { return &_class_data_; }

void AvailableSessionResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AvailableSessionResponse *>(to)->MergeFrom(
      static_cast<const AvailableSessionResponse &>(from));
}


void AvailableSessionResponse::MergeFrom(const AvailableSessionResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.AvailableSessionResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  sessions_.MergeFrom(from.sessions_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AvailableSessionResponse::CopyFrom(const AvailableSessionResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.AvailableSessionResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AvailableSessionResponse::IsInitialized() const {
  return true;
}

void AvailableSessionResponse::InternalSwap(AvailableSessionResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  sessions_.InternalSwap(&other->sessions_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AvailableSessionResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[6]);
}

// ===================================================================

class SessionName::_Internal {
 public:
};

SessionName::SessionName(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.SessionName)
}
SessionName::SessionName(const SessionName& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.SessionName)
}

inline void SessionName::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SessionName::~SessionName() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.SessionName)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SessionName::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SessionName::ArenaDtor(void* object) {
  SessionName* _this = reinterpret_cast< SessionName* >(object);
  (void)_this;
}
void SessionName::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SessionName::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SessionName::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.SessionName)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SessionName::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.data_capture.SessionName.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SessionName::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.SessionName)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.data_capture.SessionName.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.SessionName)
  return target;
}

size_t SessionName::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.SessionName)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SessionName::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SessionName::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SessionName::GetClassData() const { return &_class_data_; }

void SessionName::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SessionName *>(to)->MergeFrom(
      static_cast<const SessionName &>(from));
}


void SessionName::MergeFrom(const SessionName& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.SessionName)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SessionName::CopyFrom(const SessionName& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.SessionName)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SessionName::IsInitialized() const {
  return true;
}

void SessionName::InternalSwap(SessionName* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SessionName::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[7]);
}

// ===================================================================

class RegularCaptureStatus::_Internal {
 public:
};

RegularCaptureStatus::RegularCaptureStatus(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.data_capture.RegularCaptureStatus)
}
RegularCaptureStatus::RegularCaptureStatus(const RegularCaptureStatus& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&uploaded_, &from.uploaded_,
    static_cast<size_t>(reinterpret_cast<char*>(&last_upload_timestamp_) -
    reinterpret_cast<char*>(&uploaded_)) + sizeof(last_upload_timestamp_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.data_capture.RegularCaptureStatus)
}

inline void RegularCaptureStatus::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&uploaded_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&last_upload_timestamp_) -
    reinterpret_cast<char*>(&uploaded_)) + sizeof(last_upload_timestamp_));
}

RegularCaptureStatus::~RegularCaptureStatus() {
  // @@protoc_insertion_point(destructor:carbon.frontend.data_capture.RegularCaptureStatus)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RegularCaptureStatus::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RegularCaptureStatus::ArenaDtor(void* object) {
  RegularCaptureStatus* _this = reinterpret_cast< RegularCaptureStatus* >(object);
  (void)_this;
}
void RegularCaptureStatus::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RegularCaptureStatus::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RegularCaptureStatus::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.data_capture.RegularCaptureStatus)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&uploaded_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&last_upload_timestamp_) -
      reinterpret_cast<char*>(&uploaded_)) + sizeof(last_upload_timestamp_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RegularCaptureStatus::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 uploaded = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uploaded_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint32 budget = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          budget_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 last_upload_timestamp = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          last_upload_timestamp_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RegularCaptureStatus::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.data_capture.RegularCaptureStatus)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 uploaded = 1;
  if (this->_internal_uploaded() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_uploaded(), target);
  }

  // uint32 budget = 2;
  if (this->_internal_budget() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(2, this->_internal_budget(), target);
  }

  // int64 last_upload_timestamp = 3;
  if (this->_internal_last_upload_timestamp() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_last_upload_timestamp(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.data_capture.RegularCaptureStatus)
  return target;
}

size_t RegularCaptureStatus::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.data_capture.RegularCaptureStatus)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint32 uploaded = 1;
  if (this->_internal_uploaded() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_uploaded());
  }

  // uint32 budget = 2;
  if (this->_internal_budget() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_budget());
  }

  // int64 last_upload_timestamp = 3;
  if (this->_internal_last_upload_timestamp() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_last_upload_timestamp());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RegularCaptureStatus::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RegularCaptureStatus::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RegularCaptureStatus::GetClassData() const { return &_class_data_; }

void RegularCaptureStatus::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RegularCaptureStatus *>(to)->MergeFrom(
      static_cast<const RegularCaptureStatus &>(from));
}


void RegularCaptureStatus::MergeFrom(const RegularCaptureStatus& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.data_capture.RegularCaptureStatus)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_uploaded() != 0) {
    _internal_set_uploaded(from._internal_uploaded());
  }
  if (from._internal_budget() != 0) {
    _internal_set_budget(from._internal_budget());
  }
  if (from._internal_last_upload_timestamp() != 0) {
    _internal_set_last_upload_timestamp(from._internal_last_upload_timestamp());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RegularCaptureStatus::CopyFrom(const RegularCaptureStatus& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.data_capture.RegularCaptureStatus)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RegularCaptureStatus::IsInitialized() const {
  return true;
}

void RegularCaptureStatus::InternalSwap(RegularCaptureStatus* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RegularCaptureStatus, last_upload_timestamp_)
      + sizeof(RegularCaptureStatus::last_upload_timestamp_)
      - PROTOBUF_FIELD_OFFSET(RegularCaptureStatus, uploaded_)>(
          reinterpret_cast<char*>(&uploaded_),
          reinterpret_cast<char*>(&other->uploaded_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RegularCaptureStatus::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_getter, &descriptor_table_frontend_2fproto_2fdata_5fcapture_2eproto_once,
      file_level_metadata_frontend_2fproto_2fdata_5fcapture_2eproto[8]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace data_capture
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::DataCaptureRate* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::DataCaptureRate >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::DataCaptureRate >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::DataCaptureState* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::DataCaptureState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::DataCaptureState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::DataCaptureSession* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::DataCaptureSession >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::DataCaptureSession >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::StartDataCaptureRequest* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::StartDataCaptureRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::StartDataCaptureRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::SnapImagesRequest* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::SnapImagesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::SnapImagesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::Session* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::Session >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::Session >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::AvailableSessionResponse* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::AvailableSessionResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::AvailableSessionResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::SessionName* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::SessionName >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::SessionName >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::data_capture::RegularCaptureStatus* Arena::CreateMaybeMessage< ::carbon::frontend::data_capture::RegularCaptureStatus >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::data_capture::RegularCaptureStatus >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/robot.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/robot.proto',
  package='carbon.frontend.robot',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1a\x66rontend/proto/robot.proto\x12\x15\x63\x61rbon.frontend.robot\x1a\x19\x66rontend/proto/util.proto\"~\n\nBoardState\x12\x11\n\tboard_rev\x18\x01 \x01(\t\x12\x11\n\tconnected\x18\x03 \x01(\x08\x12\x0e\n\x06\x62ooted\x18\x04 \x01(\x08\x12\r\n\x05\x65rror\x18\x05 \x01(\x08\x12\x11\n\ttimestamp\x18\x06 \x01(\x04\x12\x18\n\x10\x66irmware_version\x18\x07 \x01(\t\"t\n\x08GPSCoord\x12\x10\n\x08latitude\x18\x02 \x01(\x01\x12\x11\n\tlongitude\x18\x03 \x01(\x01\x12\x13\n\x0b\x61ltitude_mm\x18\x04 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_x\x18\x05 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_y\x18\x06 \x01(\x01\x12\x0e\n\x06\x65\x63\x65\x66_z\x18\x07 \x01(\x01\"\xbf\x01\n\rGPSBoardState\x12\x30\n\x05\x62oard\x18\x01 \x01(\x0b\x32!.carbon.frontend.robot.BoardState\x12\x10\n\x08have_fix\x18\x02 \x01(\x08\x12\x17\n\x0fhave_approx_fix\x18\x03 \x01(\x08\x12\x10\n\x08num_sats\x18\x04 \x01(\x05\x12\x0c\n\x04hdop\x18\x05 \x01(\x02\x12\x31\n\x08position\x18\x06 \x01(\x0b\x32\x1f.carbon.frontend.robot.GPSCoord\"q\n\nWheelState\x12\x1a\n\x12last_tick_position\x18\x01 \x01(\x05\x12\x0f\n\x07vel_mph\x18\x02 \x01(\x02\x12\x10\n\x08\x66iltered\x18\x03 \x01(\x08\x12\x0f\n\x07\x65nabled\x18\x04 \x01(\x08\x12\x13\n\x0b\x64iameter_in\x18\x05 \x01(\x02\"}\n\x16WheelEncoderBoardState\x12\x30\n\x05\x62oard\x18\x01 \x01(\x0b\x32!.carbon.frontend.robot.BoardState\x12\x31\n\x06wheels\x18\x02 \x03(\x0b\x32!.carbon.frontend.robot.WheelState\"\x8f\x01\n\x10StrobeBoardState\x12\x30\n\x05\x62oard\x18\x01 \x01(\x0b\x32!.carbon.frontend.robot.BoardState\x12\x13\n\x0b\x65xposure_us\x18\x02 \x01(\x05\x12\x11\n\tperiod_us\x18\x03 \x01(\x05\x12!\n\x19targets_per_predict_ratio\x18\x04 \x01(\x05\"\r\n\x0bISOBusState\"\x0e\n\x0c\x43hillerState\"\x15\n\x13\x41irConditionerState\";\n\rUSBDriveState\x12\x11\n\tconnected\x18\x01 \x01(\x08\x12\x17\n\x0futilization_pct\x18\x02 \x01(\x02\"\xfa\x06\n\x13SupervisoryPLCState\x12\x14\n\x0c\x61\x63_frequency\x18\x01 \x01(\x02\x12\x14\n\x0c\x61\x63_voltage_a\x18\x02 \x01(\x02\x12\x16\n\x0e\x61\x63_voltage_a_b\x18\x03 \x01(\x02\x12\x16\n\x0e\x61\x63_voltage_a_c\x18\x04 \x01(\x02\x12\x14\n\x0c\x61\x63_voltage_b\x18\x05 \x01(\x02\x12\x16\n\x0e\x61\x63_voltage_b_c\x18\x06 \x01(\x02\x12\x14\n\x0c\x61\x63_voltage_c\x18\x07 \x01(\x02\x12\x18\n\x10phase_power_va_3\x18\x08 \x01(\x02\x12\x17\n\x0fphase_power_w_3\x18\t \x01(\x02\x12\x14\n\x0cpower_factor\x18\n \x01(\x02\x12\x11\n\tpower_bad\x18\x0b \x01(\x08\x12\x12\n\npower_good\x18\x0c \x01(\x08\x12\x16\n\x0epower_very_bad\x18\r \x01(\x08\x12\x1b\n\x13\x62\x61ttery_voltage_12v\x18\x0e \x01(\x02\x12 \n\x18\x61ir_conditioner_disabled\x18\x0f \x01(\x08\x12\x18\n\x10\x63hiller_disabled\x18\x10 \x01(\x08\x12\x14\n\x0cgps_disabled\x18\x11 \x01(\x08\x12\x17\n\x0fstrobe_disabled\x18\x12 \x01(\x08\x12\x1e\n\x16wheel_encoder_disabled\x18\x13 \x01(\x08\x12\x14\n\x0c\x62tl_disabled\x18\x14 \x03(\x08\x12\x17\n\x0fserver_disabled\x18\x15 \x03(\x08\x12\x19\n\x11scanners_disabled\x18\x16 \x03(\x08\x12\x1f\n\x17main_contactor_disabled\x18\x17 \x01(\x08\x12 \n\x18main_contactor_status_fb\x18\x18 \x01(\x08\x12\x1f\n\x17server_cabinet_humidity\x18\x19 \x01(\x02\x12\x1b\n\x13server_cabinet_temp\x18\x1a \x01(\x02\x12\x19\n\x11humidity_bypassed\x18\x1b \x01(\x08\x12\x15\n\rtemp_bypassed\x18\x1c \x01(\x08\x12\x17\n\x0fhumidity_status\x18\x1d \x01(\x08\x12\x13\n\x0btemp_status\x18\x1e \x01(\x08\x12\x1c\n\x14temp_humidity_status\x18\x1f \x01(\x08\x12\x15\n\rlifted_status\x18  \x01(\x08\x12\x15\n\rtractor_power\x18! \x01(\x08\x12\x1c\n\x14water_protect_status\x18\" \x01(\x08\"\xd5\x01\n\x0eSafetyPLCState\x12\x10\n\x08\x65stopped\x18\x01 \x01(\x08\x12\x17\n\x0fin_cab_estopped\x18\x02 \x01(\x08\x12\x11\n\tinterlock\x18\x03 \x01(\x08\x12\x11\n\tlaser_key\x18\x04 \x01(\x08\x12\x15\n\rleft_estopped\x18\x05 \x01(\x08\x12\x16\n\x0eright_estopped\x18\x06 \x01(\x08\x12\x0e\n\x06lifted\x18\x07 \x01(\x08\x12\x15\n\rwater_protect\x18\x08 \x01(\x08\x12\x1c\n\x14lift_sensor_bypassed\x18\t \x01(\x08\"q\n\x08NicState\x12\x12\n\nptp_online\x18\x01 \x01(\x08\x12\x12\n\nptp_offset\x18\x02 \x01(\x03\x12\x13\n\x0blink_online\x18\x03 \x01(\x08\x12\x14\n\x0clink_correct\x18\x04 \x01(\x08\x12\x12\n\nlink_speed\x18\x05 \x01(\x05\"3\n\x11StorageDriveState\x12\x10\n\x08\x63\x61pacity\x18\x01 \x01(\x04\x12\x0c\n\x04used\x18\x02 \x01(\x04\"\xc8\x01\n\x08GPUState\x12\x0e\n\x06online\x18\x01 \x01(\x08\x12\r\n\x05index\x18\x02 \x01(\r\x12\r\n\x05model\x18\x03 \x01(\t\x12\x15\n\rtemperature_C\x18\x04 \x01(\x02\x12\x12\n\npower_used\x18\x05 \x01(\x02\x12\x16\n\x0epower_capacity\x18\x06 \x01(\x02\x12\x13\n\x0bmemory_used\x18\x07 \x01(\x02\x12\x17\n\x0fmemory_capacity\x18\x08 \x01(\x02\x12\x0b\n\x03\x66\x61n\x18\t \x01(\x02\x12\x10\n\x08gpu_util\x18\n \x01(\x02\"\xcf\x03\n\tHostState\x12\x0e\n\x06online\x18\x01 \x01(\x08\x12\x1a\n\x12ptp_checker_online\x18\x02 \x01(\x08\x12\x1f\n\x17\x65thernet_checker_online\x18\x03 \x01(\x08\x12\x1e\n\x16storage_checker_online\x18\x04 \x01(\x08\x12\x1a\n\x12gpu_checker_online\x18\x05 \x01(\x08\x12\x38\n\x04nics\x18\x06 \x03(\x0b\x32*.carbon.frontend.robot.HostState.NicsEntry\x12@\n\x0emain_partition\x18\x07 \x01(\x0b\x32(.carbon.frontend.robot.StorageDriveState\x12@\n\x0e\x64\x61ta_partition\x18\x08 \x01(\x0b\x32(.carbon.frontend.robot.StorageDriveState\x12-\n\x04gpus\x18\t \x03(\x0b\x32\x1f.carbon.frontend.robot.GPUState\x1aL\n\tNicsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12.\n\x05value\x18\x02 \x01(\x0b\x32\x1f.carbon.frontend.robot.NicState:\x02\x38\x01\"\xf2\x01\n\x1aSoftwareUpdateVersionState\x12\x0b\n\x03tag\x18\x01 \x01(\t\x12\x0e\n\x06system\x18\x02 \x01(\t\x12\x11\n\tavailable\x18\x03 \x01(\x08\x12\r\n\x05ready\x18\x04 \x01(\x08\x12^\n\x0fimages_required\x18\x05 \x03(\x0b\x32\x45.carbon.frontend.robot.SoftwareUpdateVersionState.ImagesRequiredEntry\x1a\x35\n\x13ImagesRequiredEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\":\n\x14OperatingSystemState\x12\x11\n\tpartition\x18\x01 \x01(\t\x12\x0f\n\x07version\x18\x02 \x01(\t\"\x8e\x01\n\x12SystemVersionState\x12<\n\x07\x63urrent\x18\x01 \x01(\x0b\x32+.carbon.frontend.robot.OperatingSystemState\x12:\n\x05other\x18\x02 \x01(\x0b\x32+.carbon.frontend.robot.OperatingSystemState\"\x9e\x02\n\x15SoftwareVersionsState\x12\x42\n\x07\x63urrent\x18\x01 \x01(\x0b\x32\x31.carbon.frontend.robot.SoftwareUpdateVersionState\x12\x43\n\x08previous\x18\x02 \x01(\x0b\x32\x31.carbon.frontend.robot.SoftwareUpdateVersionState\x12\x41\n\x06target\x18\x03 \x01(\x0b\x32\x31.carbon.frontend.robot.SoftwareUpdateVersionState\x12\x39\n\x06system\x18\x04 \x01(\x0b\x32).carbon.frontend.robot.SystemVersionState\"`\n\x14SoftwareProcessState\x12\x0c\n\x04name\x18\x01 \x01(\t\x12:\n\x05stage\x18\x02 \x01(\x0e\x32+.carbon.frontend.robot.SoftwareProcessStage\"\xd7\x02\n\rSoftwareState\x12\x46\n\tprocesses\x18\x01 \x03(\x0b\x32\x33.carbon.frontend.robot.SoftwareState.ProcessesEntry\x12>\n\x08versions\x18\x02 \x01(\x0b\x32,.carbon.frontend.robot.SoftwareVersionsState\x12\x12\n\nrestarting\x18\x03 \x01(\x08\x12\x1c\n\x14last_restart_time_ms\x18\x04 \x01(\x03\x12\x10\n\x08updating\x18\x05 \x01(\x08\x12\x1b\n\x13last_update_time_ms\x18\x06 \x01(\x03\x1a]\n\x0eProcessesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12:\n\x05value\x18\x02 \x01(\x0b\x32+.carbon.frontend.robot.SoftwareProcessState:\x02\x38\x01\"-\n\x12\x44\x65\x65pweedModelState\x12\x17\n\x0fviable_crop_ids\x18\x01 \x03(\t\"\x0f\n\rP2PModelState\"\xcb\x01\n\nModelState\x12\x10\n\x08model_id\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\x12=\n\x08\x64\x65\x65pweed\x18\x03 \x01(\x0b\x32).carbon.frontend.robot.DeepweedModelStateH\x00\x12\x33\n\x03p2p\x18\x04 \x01(\x0b\x32$.carbon.frontend.robot.P2PModelStateH\x00\x12\r\n\x05ready\x18\x05 \x01(\x08\x12\x0e\n\x06loaded\x18\x06 \x01(\x08\x42\n\n\x08specific\"B\n\rRowModelState\x12\x31\n\x06models\x18\x01 \x03(\x0b\x32!.carbon.frontend.robot.ModelState\"\xd7\x01\n\x11ModelManagerState\x12\x37\n\x0clocal_models\x18\x01 \x03(\x0b\x32!.carbon.frontend.robot.ModelState\x12\x38\n\nrow_models\x18\x02 \x03(\x0b\x32$.carbon.frontend.robot.RowModelState\x12\x16\n\x0e\x61\x63tive_crop_id\x18\x03 \x01(\t\x12\x1d\n\x15\x61\x63tive_deepweed_model\x18\x04 \x01(\t\x12\x18\n\x10\x61\x63tive_p2p_model\x18\x05 \x01(\t\"w\n\rComputerState\x12\x36\n\x08software\x18\x01 \x01(\x0b\x32$.carbon.frontend.robot.SoftwareState\x12.\n\x04host\x18\x02 \x01(\x0b\x32 .carbon.frontend.robot.HostState\"\xfd\x02\n\x0ePowerTimeState\x12\x1c\n\x14\x63ommander_on_time_ms\x18\x01 \x01(\x03\x12\x18\n\x10power_on_time_ms\x18\x02 \x01(\x03\x12\'\n\x1f\x61ir_conditioner_enabled_time_ms\x18\x03 \x01(\x03\x12\x1f\n\x17\x63hiller_enabled_time_ms\x18\x04 \x01(\x03\x12\x1b\n\x13gps_enabled_time_ms\x18\x05 \x01(\x03\x12\x1e\n\x16strobe_enabled_time_ms\x18\x06 \x01(\x03\x12%\n\x1dwheel_encoder_enabled_time_ms\x18\x07 \x01(\x03\x12\x1b\n\x13\x62tl_enabled_time_ms\x18\x08 \x03(\x03\x12\x1e\n\x16server_enabled_time_ms\x18\t \x03(\x03\x12 \n\x18scanners_enabled_time_ms\x18\n \x03(\x03\x12&\n\x1emain_contactor_enabled_time_ms\x18\x0b \x01(\x03\"k\n\rVelocityState\x12\x1c\n\x14\x63urrent_velocity_mph\x18\x01 \x01(\x02\x12\x1b\n\x13target_velocity_mph\x18\x02 \x01(\x02\x12\x1f\n\x17row_target_velocity_mph\x18\x03 \x03(\x02\"\x88\x01\n\x0cWeedingState\x12\x0f\n\x07weeding\x18\x01 \x03(\x08\x12/\n\x05stage\x18\x02 \x01(\x0e\x32 .carbon.frontend.robot.BootStage\x12\x36\n\x08velocity\x18\x03 \x01(\x0b\x32$.carbon.frontend.robot.VelocityState\"\xfa\x05\n\x0bGlobalState\x12\x36\n\x08\x63omputer\x18\x01 \x01(\x0b\x32$.carbon.frontend.robot.ComputerState\x12\x31\n\x03gps\x18\x02 \x01(\x0b\x32$.carbon.frontend.robot.GPSBoardState\x12<\n\x05wheel\x18\x03 \x01(\x0b\x32-.carbon.frontend.robot.WheelEncoderBoardState\x12\x37\n\x06strobe\x18\x04 \x01(\x0b\x32\'.carbon.frontend.robot.StrobeBoardState\x12?\n\x0bsupervisory\x18\x05 \x01(\x0b\x32*.carbon.frontend.robot.SupervisoryPLCState\x12\x35\n\x06safety\x18\x06 \x01(\x0b\x32%.carbon.frontend.robot.SafetyPLCState\x12\x34\n\x07\x63hiller\x18\x08 \x01(\x0b\x32#.carbon.frontend.robot.ChillerState\x12\x43\n\x0f\x61ir_conditioner\x18\t \x01(\x0b\x32*.carbon.frontend.robot.AirConditionerState\x12\x32\n\x06isobus\x18\n \x01(\x0b\x32\".carbon.frontend.robot.ISOBusState\x12\x39\n\npower_time\x18\x0b \x01(\x0b\x32%.carbon.frontend.robot.PowerTimeState\x12\x38\n\x06models\x18\x0c \x01(\x0b\x32(.carbon.frontend.robot.ModelManagerState\x12\x34\n\x07weeding\x18\r \x01(\x0b\x32#.carbon.frontend.robot.WeedingState\x12\x31\n\x03usb\x18\x0e \x01(\x0b\x32$.carbon.frontend.robot.USBDriveStateJ\x04\x08\x07\x10\x08\"\x8c\x01\n\x0b\x43\x61meraState\x12\x0e\n\x06vendor\x18\x01 \x01(\t\x12\r\n\x05model\x18\x02 \x01(\t\x12\n\n\x02ip\x18\x03 \x01(\t\x12\x0e\n\x06serial\x18\x04 \x01(\t\x12\n\n\x02id\x18\x05 \x01(\t\x12\x11\n\tconnected\x18\x06 \x01(\x08\x12\x13\n\x0b\x65xposure_us\x18\x07 \x01(\x02\x12\x0e\n\x06gpu_id\x18\x08 \x01(\x05\"\x9d\x01\n\rMaxonPidState\x12\x11\n\tcurrent_p\x18\x01 \x01(\r\x12\x11\n\tcurrent_i\x18\x02 \x01(\r\x12\x12\n\nposition_p\x18\x03 \x01(\r\x12\x12\n\nposition_i\x18\x04 \x01(\r\x12\x12\n\nposition_d\x18\x05 \x01(\r\x12\x14\n\x0cposition_ffv\x18\x06 \x01(\r\x12\x14\n\x0cposition_ffa\x18\x07 \x01(\r\"\xf9\x01\n\nServoState\x12\x0c\n\x04type\x18\x01 \x01(\t\x12\x0b\n\x03min\x18\x02 \x01(\x05\x12\x0b\n\x03max\x18\x03 \x01(\x05\x12\x1a\n\x12last_read_position\x18\x04 \x01(\x05\x12\x1f\n\x17last_requested_position\x18\x05 \x01(\x05\x12\x1f\n\x17last_requested_velocity\x18\x06 \x01(\r\x12&\n\x1elast_requested_follow_velocity\x18\x07 \x01(\r\x12\x35\n\x05maxon\x18\x08 \x01(\x0b\x32$.carbon.frontend.robot.MaxonPidStateH\x00\x42\x06\n\x04pids\"\xe4\x01\n\x12ScannerMetricState\x12\x1b\n\x13n_entities_targeted\x18\x01 \x01(\x02\x12\x14\n\x0c\x61vg_accuracy\x18\x02 \x01(\x02\x12\x1c\n\x14\x61vg_initial_accuracy\x18\x03 \x01(\x02\x12\x19\n\x11\x61vg_overhead_time\x18\x04 \x01(\x02\x12\x12\n\nerror_rate\x18\x05 \x01(\x02\x12\x16\n\x0enot_found_rate\x18\x06 \x01(\x02\x12\x19\n\x11out_of_range_rate\x18\x07 \x01(\x02\x12\x1b\n\x13target_changed_rate\x18\x08 \x01(\x02\"\xed\x02\n\x11ScannerLaserState\x12\x0e\n\x06serial\x18\x01 \x01(\t\x12\x0f\n\x07n_shots\x18\x02 \x01(\x03\x12\x14\n\x0c\x66ire_time_ms\x18\x03 \x01(\x03\x12\x1a\n\x12\x63ontrolled_time_ms\x18\x04 \x01(\x03\x12\x12\n\nlpsu_power\x18\x05 \x01(\x08\x12\x14\n\x0clpsu_current\x18\x06 \x01(\x02\x12\x14\n\x0ctherm_sensor\x18\x07 \x01(\x02\x12\x11\n\ttherm_ref\x18\x08 \x01(\x02\x12\x12\n\ndelta_temp\x18\t \x01(\x02\x12\x16\n\x0epower_bypassed\x18\n \x01(\x08\x12\r\n\x05\x61rmed\x18\x0b \x01(\x08\x12\x10\n\x08watchdog\x18\x0c \x01(\x08\x12\x18\n\x10\x66iring_requested\x18\r \x01(\x08\x12\x0e\n\x06\x66iring\x18\x0e \x01(\x08\x12\x11\n\tintensity\x18\x0f \x01(\x02\x12\x0f\n\x07wattage\x18\x10 \x01(\x02\x12\x17\n\x0fnominal_wattage\x18\x11 \x01(\x02\"O\n\x15ScannerCrosshairState\x12 \n\x18last_update_timestamp_ms\x18\x01 \x01(\x03\x12\t\n\x01x\x18\x02 \x01(\x02\x12\t\n\x01y\x18\x03 \x01(\x02\"F\n\x10ScannerLensState\x12\x1c\n\x14last_requested_value\x18\x01 \x01(\x02\x12\x14\n\x0c\x61\x63tual_value\x18\x02 \x01(\x02\"\xad\x03\n\x0cScannerState\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x30\n\x05\x62oard\x18\x02 \x01(\x0b\x32!.carbon.frontend.robot.BoardState\x12\x0f\n\x07\x65nabled\x18\x03 \x01(\x08\x12.\n\x03pan\x18\x04 \x01(\x0b\x32!.carbon.frontend.robot.ServoState\x12/\n\x04tilt\x18\x05 \x01(\x0b\x32!.carbon.frontend.robot.ServoState\x12?\n\tcrosshair\x18\x06 \x01(\x0b\x32,.carbon.frontend.robot.ScannerCrosshairState\x12\x35\n\x04lens\x18\x07 \x01(\x0b\x32\'.carbon.frontend.robot.ScannerLensState\x12\x37\n\x05laser\x18\x08 \x01(\x0b\x32(.carbon.frontend.robot.ScannerLaserState\x12:\n\x07metrics\x18\t \x01(\x0b\x32).carbon.frontend.robot.ScannerMetricState\"\xe6\x01\n\x0b\x43VNodeState\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x10\n\x08mean_fps\x18\x02 \x01(\x02\x12\x1b\n\x13mean_output_latency\x18\x03 \x01(\x02\x12\x1a\n\x12p99_output_latency\x18\x04 \x01(\x02\x12\x19\n\x11mean_real_latency\x18\x05 \x01(\x02\x12\x18\n\x10p99_real_latency\x18\x06 \x01(\x02\x12\r\n\x05state\x18\x07 \x01(\t\x12\x0f\n\x07pull_ms\x18\x08 \x01(\x02\x12\x15\n\rprocessing_ms\x18\t \x01(\x02\x12\x12\n\npushing_ms\x18\n \x01(\x02\"<\n\x07\x43VState\x12\x31\n\x05nodes\x18\x01 \x03(\x0b\x32\".carbon.frontend.robot.CVNodeState\"F\n\x0cTrackerState\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x16\n\x0en_trajectories\x18\x02 \x01(\r\x12\x10\n\x08\x63\x61pacity\x18\x03 \x01(\r\"R\n\x11IngestClientState\x12\x0c\n\x04name\x18\x01 \x01(\t\x12\x15\n\rover_capacity\x18\x02 \x01(\x08\x12\x18\n\x10over_constraints\x18\x03 \x01(\x08\"\x7f\n\x0b\x41imbotState\x12\x35\n\x08trackers\x18\x01 \x03(\x0b\x32#.carbon.frontend.robot.TrackerState\x12\x39\n\x07ingests\x18\x02 \x03(\x0b\x32(.carbon.frontend.robot.IngestClientState\"\xf8\x02\n\x08RowState\x12\x36\n\x08\x63omputer\x18\x01 \x01(\x0b\x32$.carbon.frontend.robot.ComputerState\x12\x35\n\x08scanners\x18\x02 \x03(\x0b\x32#.carbon.frontend.robot.ScannerState\x12\x34\n\x08predicts\x18\x03 \x03(\x0b\x32\".carbon.frontend.robot.CameraState\x12\x33\n\x07targets\x18\x04 \x03(\x0b\x32\".carbon.frontend.robot.CameraState\x12\x32\n\x06\x65xtras\x18\x05 \x03(\x0b\x32\".carbon.frontend.robot.CameraState\x12*\n\x02\x63v\x18\x06 \x01(\x0b\x32\x1e.carbon.frontend.robot.CVState\x12\x32\n\x06\x61imbot\x18\x07 \x01(\x0b\x32\".carbon.frontend.robot.AimbotState\"o\n\nRobotState\x12\x32\n\x06global\x18\x01 \x01(\x0b\x32\".carbon.frontend.robot.GlobalState\x12-\n\x04rows\x18\x02 \x03(\x0b\x32\x1f.carbon.frontend.robot.RowState\"v\n\x15TimestampedRobotState\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12\x30\n\x05state\x18\x02 \x01(\x0b\x32!.carbon.frontend.robot.RobotState\"@\n\x11RobotStateRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp*V\n\x14SoftwareProcessStage\x12\x0f\n\x0bSPS_UNKNOWN\x10\x00\x12\r\n\tSPS_ERROR\x10\x01\x12\x0f\n\x0bSPS_BOOTING\x10\x02\x12\r\n\tSPS_READY\x10\x03*\xeb\x01\n\tBootStage\x12\x18\n\x14\x42S_UPDATE_INSTALLING\x10\x00\x12\x13\n\x0f\x42S_POWERED_DOWN\x10\x01\x12\x12\n\x0e\x42S_POWERING_UP\x10\x02\x12\x17\n\x13\x42S_SOFTWARE_LOADING\x10\x03\x12\x13\n\x0f\x42S_BOOT_FAILURE\x10\x04\x12\x14\n\x10\x42S_MODEL_LOADING\x10\x05\x12\x17\n\x13\x42S_CRITICAL_FAILURE\x10\x06\x12\r\n\tBS_LIFTED\x10\x07\x12\x0f\n\x0b\x42S_ESTOPPED\x10\x08\x12\x0e\n\nBS_WEEDING\x10\t\x12\x0e\n\nBS_STANDBY\x10\n2\x85\x01\n\x16RobotDiagnosticService\x12k\n\x11GetNextRobotState\x12(.carbon.frontend.robot.RobotStateRequest\x1a,.carbon.frontend.robot.TimestampedRobotStateB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,])

_SOFTWAREPROCESSSTAGE = _descriptor.EnumDescriptor(
  name='SoftwareProcessStage',
  full_name='carbon.frontend.robot.SoftwareProcessStage',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='SPS_UNKNOWN', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPS_ERROR', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPS_BOOTING', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='SPS_READY', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=9223,
  serialized_end=9309,
)
_sym_db.RegisterEnumDescriptor(_SOFTWAREPROCESSSTAGE)

SoftwareProcessStage = enum_type_wrapper.EnumTypeWrapper(_SOFTWAREPROCESSSTAGE)
_BOOTSTAGE = _descriptor.EnumDescriptor(
  name='BootStage',
  full_name='carbon.frontend.robot.BootStage',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='BS_UPDATE_INSTALLING', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_POWERED_DOWN', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_POWERING_UP', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_SOFTWARE_LOADING', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_BOOT_FAILURE', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_MODEL_LOADING', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_CRITICAL_FAILURE', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_LIFTED', index=7, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_ESTOPPED', index=8, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_WEEDING', index=9, number=9,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BS_STANDBY', index=10, number=10,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=9312,
  serialized_end=9547,
)
_sym_db.RegisterEnumDescriptor(_BOOTSTAGE)

BootStage = enum_type_wrapper.EnumTypeWrapper(_BOOTSTAGE)
SPS_UNKNOWN = 0
SPS_ERROR = 1
SPS_BOOTING = 2
SPS_READY = 3
BS_UPDATE_INSTALLING = 0
BS_POWERED_DOWN = 1
BS_POWERING_UP = 2
BS_SOFTWARE_LOADING = 3
BS_BOOT_FAILURE = 4
BS_MODEL_LOADING = 5
BS_CRITICAL_FAILURE = 6
BS_LIFTED = 7
BS_ESTOPPED = 8
BS_WEEDING = 9
BS_STANDBY = 10



_BOARDSTATE = _descriptor.Descriptor(
  name='BoardState',
  full_name='carbon.frontend.robot.BoardState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='board_rev', full_name='carbon.frontend.robot.BoardState.board_rev', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connected', full_name='carbon.frontend.robot.BoardState.connected', index=1,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='booted', full_name='carbon.frontend.robot.BoardState.booted', index=2,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error', full_name='carbon.frontend.robot.BoardState.error', index=3,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp', full_name='carbon.frontend.robot.BoardState.timestamp', index=4,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firmware_version', full_name='carbon.frontend.robot.BoardState.firmware_version', index=5,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=80,
  serialized_end=206,
)


_GPSCOORD = _descriptor.Descriptor(
  name='GPSCoord',
  full_name='carbon.frontend.robot.GPSCoord',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='latitude', full_name='carbon.frontend.robot.GPSCoord.latitude', index=0,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='longitude', full_name='carbon.frontend.robot.GPSCoord.longitude', index=1,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='altitude_mm', full_name='carbon.frontend.robot.GPSCoord.altitude_mm', index=2,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_x', full_name='carbon.frontend.robot.GPSCoord.ecef_x', index=3,
      number=5, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_y', full_name='carbon.frontend.robot.GPSCoord.ecef_y', index=4,
      number=6, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ecef_z', full_name='carbon.frontend.robot.GPSCoord.ecef_z', index=5,
      number=7, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=208,
  serialized_end=324,
)


_GPSBOARDSTATE = _descriptor.Descriptor(
  name='GPSBoardState',
  full_name='carbon.frontend.robot.GPSBoardState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='board', full_name='carbon.frontend.robot.GPSBoardState.board', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='have_fix', full_name='carbon.frontend.robot.GPSBoardState.have_fix', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='have_approx_fix', full_name='carbon.frontend.robot.GPSBoardState.have_approx_fix', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='num_sats', full_name='carbon.frontend.robot.GPSBoardState.num_sats', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hdop', full_name='carbon.frontend.robot.GPSBoardState.hdop', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position', full_name='carbon.frontend.robot.GPSBoardState.position', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=327,
  serialized_end=518,
)


_WHEELSTATE = _descriptor.Descriptor(
  name='WheelState',
  full_name='carbon.frontend.robot.WheelState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='last_tick_position', full_name='carbon.frontend.robot.WheelState.last_tick_position', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='vel_mph', full_name='carbon.frontend.robot.WheelState.vel_mph', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='filtered', full_name='carbon.frontend.robot.WheelState.filtered', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.robot.WheelState.enabled', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='diameter_in', full_name='carbon.frontend.robot.WheelState.diameter_in', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=520,
  serialized_end=633,
)


_WHEELENCODERBOARDSTATE = _descriptor.Descriptor(
  name='WheelEncoderBoardState',
  full_name='carbon.frontend.robot.WheelEncoderBoardState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='board', full_name='carbon.frontend.robot.WheelEncoderBoardState.board', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheels', full_name='carbon.frontend.robot.WheelEncoderBoardState.wheels', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=635,
  serialized_end=760,
)


_STROBEBOARDSTATE = _descriptor.Descriptor(
  name='StrobeBoardState',
  full_name='carbon.frontend.robot.StrobeBoardState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='board', full_name='carbon.frontend.robot.StrobeBoardState.board', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='carbon.frontend.robot.StrobeBoardState.exposure_us', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='period_us', full_name='carbon.frontend.robot.StrobeBoardState.period_us', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='targets_per_predict_ratio', full_name='carbon.frontend.robot.StrobeBoardState.targets_per_predict_ratio', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=763,
  serialized_end=906,
)


_ISOBUSSTATE = _descriptor.Descriptor(
  name='ISOBusState',
  full_name='carbon.frontend.robot.ISOBusState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=908,
  serialized_end=921,
)


_CHILLERSTATE = _descriptor.Descriptor(
  name='ChillerState',
  full_name='carbon.frontend.robot.ChillerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=923,
  serialized_end=937,
)


_AIRCONDITIONERSTATE = _descriptor.Descriptor(
  name='AirConditionerState',
  full_name='carbon.frontend.robot.AirConditionerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=939,
  serialized_end=960,
)


_USBDRIVESTATE = _descriptor.Descriptor(
  name='USBDriveState',
  full_name='carbon.frontend.robot.USBDriveState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='connected', full_name='carbon.frontend.robot.USBDriveState.connected', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='utilization_pct', full_name='carbon.frontend.robot.USBDriveState.utilization_pct', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=962,
  serialized_end=1021,
)


_SUPERVISORYPLCSTATE = _descriptor.Descriptor(
  name='SupervisoryPLCState',
  full_name='carbon.frontend.robot.SupervisoryPLCState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ac_frequency', full_name='carbon.frontend.robot.SupervisoryPLCState.ac_frequency', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a', full_name='carbon.frontend.robot.SupervisoryPLCState.ac_voltage_a', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a_b', full_name='carbon.frontend.robot.SupervisoryPLCState.ac_voltage_a_b', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_a_c', full_name='carbon.frontend.robot.SupervisoryPLCState.ac_voltage_a_c', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_b', full_name='carbon.frontend.robot.SupervisoryPLCState.ac_voltage_b', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_b_c', full_name='carbon.frontend.robot.SupervisoryPLCState.ac_voltage_b_c', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ac_voltage_c', full_name='carbon.frontend.robot.SupervisoryPLCState.ac_voltage_c', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_power_va_3', full_name='carbon.frontend.robot.SupervisoryPLCState.phase_power_va_3', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='phase_power_w_3', full_name='carbon.frontend.robot.SupervisoryPLCState.phase_power_w_3', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_factor', full_name='carbon.frontend.robot.SupervisoryPLCState.power_factor', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_bad', full_name='carbon.frontend.robot.SupervisoryPLCState.power_bad', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_good', full_name='carbon.frontend.robot.SupervisoryPLCState.power_good', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_very_bad', full_name='carbon.frontend.robot.SupervisoryPLCState.power_very_bad', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='battery_voltage_12v', full_name='carbon.frontend.robot.SupervisoryPLCState.battery_voltage_12v', index=13,
      number=14, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='air_conditioner_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.air_conditioner_disabled', index=14,
      number=15, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.chiller_disabled', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.gps_disabled', index=16,
      number=17, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.strobe_disabled', index=17,
      number=18, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_encoder_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.wheel_encoder_disabled', index=18,
      number=19, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='btl_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.btl_disabled', index=19,
      number=20, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.server_disabled', index=20,
      number=21, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanners_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.scanners_disabled', index=21,
      number=22, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_disabled', full_name='carbon.frontend.robot.SupervisoryPLCState.main_contactor_disabled', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_status_fb', full_name='carbon.frontend.robot.SupervisoryPLCState.main_contactor_status_fb', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_cabinet_humidity', full_name='carbon.frontend.robot.SupervisoryPLCState.server_cabinet_humidity', index=24,
      number=25, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_cabinet_temp', full_name='carbon.frontend.robot.SupervisoryPLCState.server_cabinet_temp', index=25,
      number=26, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity_bypassed', full_name='carbon.frontend.robot.SupervisoryPLCState.humidity_bypassed', index=26,
      number=27, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp_bypassed', full_name='carbon.frontend.robot.SupervisoryPLCState.temp_bypassed', index=27,
      number=28, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='humidity_status', full_name='carbon.frontend.robot.SupervisoryPLCState.humidity_status', index=28,
      number=29, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp_status', full_name='carbon.frontend.robot.SupervisoryPLCState.temp_status', index=29,
      number=30, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temp_humidity_status', full_name='carbon.frontend.robot.SupervisoryPLCState.temp_humidity_status', index=30,
      number=31, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifted_status', full_name='carbon.frontend.robot.SupervisoryPLCState.lifted_status', index=31,
      number=32, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tractor_power', full_name='carbon.frontend.robot.SupervisoryPLCState.tractor_power', index=32,
      number=33, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='water_protect_status', full_name='carbon.frontend.robot.SupervisoryPLCState.water_protect_status', index=33,
      number=34, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1024,
  serialized_end=1914,
)


_SAFETYPLCSTATE = _descriptor.Descriptor(
  name='SafetyPLCState',
  full_name='carbon.frontend.robot.SafetyPLCState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='estopped', full_name='carbon.frontend.robot.SafetyPLCState.estopped', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='in_cab_estopped', full_name='carbon.frontend.robot.SafetyPLCState.in_cab_estopped', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='interlock', full_name='carbon.frontend.robot.SafetyPLCState.interlock', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser_key', full_name='carbon.frontend.robot.SafetyPLCState.laser_key', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='left_estopped', full_name='carbon.frontend.robot.SafetyPLCState.left_estopped', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='right_estopped', full_name='carbon.frontend.robot.SafetyPLCState.right_estopped', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lifted', full_name='carbon.frontend.robot.SafetyPLCState.lifted', index=6,
      number=7, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='water_protect', full_name='carbon.frontend.robot.SafetyPLCState.water_protect', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lift_sensor_bypassed', full_name='carbon.frontend.robot.SafetyPLCState.lift_sensor_bypassed', index=8,
      number=9, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1917,
  serialized_end=2130,
)


_NICSTATE = _descriptor.Descriptor(
  name='NicState',
  full_name='carbon.frontend.robot.NicState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ptp_online', full_name='carbon.frontend.robot.NicState.ptp_online', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ptp_offset', full_name='carbon.frontend.robot.NicState.ptp_offset', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='link_online', full_name='carbon.frontend.robot.NicState.link_online', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='link_correct', full_name='carbon.frontend.robot.NicState.link_correct', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='link_speed', full_name='carbon.frontend.robot.NicState.link_speed', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2132,
  serialized_end=2245,
)


_STORAGEDRIVESTATE = _descriptor.Descriptor(
  name='StorageDriveState',
  full_name='carbon.frontend.robot.StorageDriveState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='capacity', full_name='carbon.frontend.robot.StorageDriveState.capacity', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='used', full_name='carbon.frontend.robot.StorageDriveState.used', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2247,
  serialized_end=2298,
)


_GPUSTATE = _descriptor.Descriptor(
  name='GPUState',
  full_name='carbon.frontend.robot.GPUState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='online', full_name='carbon.frontend.robot.GPUState.online', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='index', full_name='carbon.frontend.robot.GPUState.index', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model', full_name='carbon.frontend.robot.GPUState.model', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='temperature_C', full_name='carbon.frontend.robot.GPUState.temperature_C', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_used', full_name='carbon.frontend.robot.GPUState.power_used', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_capacity', full_name='carbon.frontend.robot.GPUState.power_capacity', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='memory_used', full_name='carbon.frontend.robot.GPUState.memory_used', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='memory_capacity', full_name='carbon.frontend.robot.GPUState.memory_capacity', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fan', full_name='carbon.frontend.robot.GPUState.fan', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gpu_util', full_name='carbon.frontend.robot.GPUState.gpu_util', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2301,
  serialized_end=2501,
)


_HOSTSTATE_NICSENTRY = _descriptor.Descriptor(
  name='NicsEntry',
  full_name='carbon.frontend.robot.HostState.NicsEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.robot.HostState.NicsEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.robot.HostState.NicsEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2891,
  serialized_end=2967,
)

_HOSTSTATE = _descriptor.Descriptor(
  name='HostState',
  full_name='carbon.frontend.robot.HostState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='online', full_name='carbon.frontend.robot.HostState.online', index=0,
      number=1, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ptp_checker_online', full_name='carbon.frontend.robot.HostState.ptp_checker_online', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ethernet_checker_online', full_name='carbon.frontend.robot.HostState.ethernet_checker_online', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='storage_checker_online', full_name='carbon.frontend.robot.HostState.storage_checker_online', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gpu_checker_online', full_name='carbon.frontend.robot.HostState.gpu_checker_online', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nics', full_name='carbon.frontend.robot.HostState.nics', index=5,
      number=6, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_partition', full_name='carbon.frontend.robot.HostState.main_partition', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='data_partition', full_name='carbon.frontend.robot.HostState.data_partition', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gpus', full_name='carbon.frontend.robot.HostState.gpus', index=8,
      number=9, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_HOSTSTATE_NICSENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2504,
  serialized_end=2967,
)


_SOFTWAREUPDATEVERSIONSTATE_IMAGESREQUIREDENTRY = _descriptor.Descriptor(
  name='ImagesRequiredEntry',
  full_name='carbon.frontend.robot.SoftwareUpdateVersionState.ImagesRequiredEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.robot.SoftwareUpdateVersionState.ImagesRequiredEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.robot.SoftwareUpdateVersionState.ImagesRequiredEntry.value', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3159,
  serialized_end=3212,
)

_SOFTWAREUPDATEVERSIONSTATE = _descriptor.Descriptor(
  name='SoftwareUpdateVersionState',
  full_name='carbon.frontend.robot.SoftwareUpdateVersionState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='tag', full_name='carbon.frontend.robot.SoftwareUpdateVersionState.tag', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='system', full_name='carbon.frontend.robot.SoftwareUpdateVersionState.system', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='available', full_name='carbon.frontend.robot.SoftwareUpdateVersionState.available', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ready', full_name='carbon.frontend.robot.SoftwareUpdateVersionState.ready', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='images_required', full_name='carbon.frontend.robot.SoftwareUpdateVersionState.images_required', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_SOFTWAREUPDATEVERSIONSTATE_IMAGESREQUIREDENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=2970,
  serialized_end=3212,
)


_OPERATINGSYSTEMSTATE = _descriptor.Descriptor(
  name='OperatingSystemState',
  full_name='carbon.frontend.robot.OperatingSystemState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='partition', full_name='carbon.frontend.robot.OperatingSystemState.partition', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='carbon.frontend.robot.OperatingSystemState.version', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3214,
  serialized_end=3272,
)


_SYSTEMVERSIONSTATE = _descriptor.Descriptor(
  name='SystemVersionState',
  full_name='carbon.frontend.robot.SystemVersionState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current', full_name='carbon.frontend.robot.SystemVersionState.current', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='other', full_name='carbon.frontend.robot.SystemVersionState.other', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3275,
  serialized_end=3417,
)


_SOFTWAREVERSIONSSTATE = _descriptor.Descriptor(
  name='SoftwareVersionsState',
  full_name='carbon.frontend.robot.SoftwareVersionsState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current', full_name='carbon.frontend.robot.SoftwareVersionsState.current', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='previous', full_name='carbon.frontend.robot.SoftwareVersionsState.previous', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target', full_name='carbon.frontend.robot.SoftwareVersionsState.target', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='system', full_name='carbon.frontend.robot.SoftwareVersionsState.system', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3420,
  serialized_end=3706,
)


_SOFTWAREPROCESSSTATE = _descriptor.Descriptor(
  name='SoftwareProcessState',
  full_name='carbon.frontend.robot.SoftwareProcessState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.robot.SoftwareProcessState.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stage', full_name='carbon.frontend.robot.SoftwareProcessState.stage', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3708,
  serialized_end=3804,
)


_SOFTWARESTATE_PROCESSESENTRY = _descriptor.Descriptor(
  name='ProcessesEntry',
  full_name='carbon.frontend.robot.SoftwareState.ProcessesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.frontend.robot.SoftwareState.ProcessesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.frontend.robot.SoftwareState.ProcessesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4057,
  serialized_end=4150,
)

_SOFTWARESTATE = _descriptor.Descriptor(
  name='SoftwareState',
  full_name='carbon.frontend.robot.SoftwareState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='processes', full_name='carbon.frontend.robot.SoftwareState.processes', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='versions', full_name='carbon.frontend.robot.SoftwareState.versions', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='restarting', full_name='carbon.frontend.robot.SoftwareState.restarting', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_restart_time_ms', full_name='carbon.frontend.robot.SoftwareState.last_restart_time_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='updating', full_name='carbon.frontend.robot.SoftwareState.updating', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_update_time_ms', full_name='carbon.frontend.robot.SoftwareState.last_update_time_ms', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_SOFTWARESTATE_PROCESSESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=3807,
  serialized_end=4150,
)


_DEEPWEEDMODELSTATE = _descriptor.Descriptor(
  name='DeepweedModelState',
  full_name='carbon.frontend.robot.DeepweedModelState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='viable_crop_ids', full_name='carbon.frontend.robot.DeepweedModelState.viable_crop_ids', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4152,
  serialized_end=4197,
)


_P2PMODELSTATE = _descriptor.Descriptor(
  name='P2PModelState',
  full_name='carbon.frontend.robot.P2PModelState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4199,
  serialized_end=4214,
)


_MODELSTATE = _descriptor.Descriptor(
  name='ModelState',
  full_name='carbon.frontend.robot.ModelState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='model_id', full_name='carbon.frontend.robot.ModelState.model_id', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.frontend.robot.ModelState.type', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deepweed', full_name='carbon.frontend.robot.ModelState.deepweed', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p2p', full_name='carbon.frontend.robot.ModelState.p2p', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ready', full_name='carbon.frontend.robot.ModelState.ready', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='loaded', full_name='carbon.frontend.robot.ModelState.loaded', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='specific', full_name='carbon.frontend.robot.ModelState.specific',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=4217,
  serialized_end=4420,
)


_ROWMODELSTATE = _descriptor.Descriptor(
  name='RowModelState',
  full_name='carbon.frontend.robot.RowModelState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='models', full_name='carbon.frontend.robot.RowModelState.models', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4422,
  serialized_end=4488,
)


_MODELMANAGERSTATE = _descriptor.Descriptor(
  name='ModelManagerState',
  full_name='carbon.frontend.robot.ModelManagerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='local_models', full_name='carbon.frontend.robot.ModelManagerState.local_models', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_models', full_name='carbon.frontend.robot.ModelManagerState.row_models', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_crop_id', full_name='carbon.frontend.robot.ModelManagerState.active_crop_id', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_deepweed_model', full_name='carbon.frontend.robot.ModelManagerState.active_deepweed_model', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='active_p2p_model', full_name='carbon.frontend.robot.ModelManagerState.active_p2p_model', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4491,
  serialized_end=4706,
)


_COMPUTERSTATE = _descriptor.Descriptor(
  name='ComputerState',
  full_name='carbon.frontend.robot.ComputerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='software', full_name='carbon.frontend.robot.ComputerState.software', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='host', full_name='carbon.frontend.robot.ComputerState.host', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4708,
  serialized_end=4827,
)


_POWERTIMESTATE = _descriptor.Descriptor(
  name='PowerTimeState',
  full_name='carbon.frontend.robot.PowerTimeState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='commander_on_time_ms', full_name='carbon.frontend.robot.PowerTimeState.commander_on_time_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_on_time_ms', full_name='carbon.frontend.robot.PowerTimeState.power_on_time_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='air_conditioner_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.air_conditioner_enabled_time_ms', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.chiller_enabled_time_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.gps_enabled_time_ms', index=4,
      number=5, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.strobe_enabled_time_ms', index=5,
      number=6, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel_encoder_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.wheel_encoder_enabled_time_ms', index=6,
      number=7, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='btl_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.btl_enabled_time_ms', index=7,
      number=8, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='server_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.server_enabled_time_ms', index=8,
      number=9, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanners_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.scanners_enabled_time_ms', index=9,
      number=10, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='main_contactor_enabled_time_ms', full_name='carbon.frontend.robot.PowerTimeState.main_contactor_enabled_time_ms', index=10,
      number=11, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=4830,
  serialized_end=5211,
)


_VELOCITYSTATE = _descriptor.Descriptor(
  name='VelocityState',
  full_name='carbon.frontend.robot.VelocityState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_velocity_mph', full_name='carbon.frontend.robot.VelocityState.current_velocity_mph', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_velocity_mph', full_name='carbon.frontend.robot.VelocityState.target_velocity_mph', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='row_target_velocity_mph', full_name='carbon.frontend.robot.VelocityState.row_target_velocity_mph', index=2,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5213,
  serialized_end=5320,
)


_WEEDINGSTATE = _descriptor.Descriptor(
  name='WeedingState',
  full_name='carbon.frontend.robot.WeedingState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='weeding', full_name='carbon.frontend.robot.WeedingState.weeding', index=0,
      number=1, type=8, cpp_type=7, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stage', full_name='carbon.frontend.robot.WeedingState.stage', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='velocity', full_name='carbon.frontend.robot.WeedingState.velocity', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5323,
  serialized_end=5459,
)


_GLOBALSTATE = _descriptor.Descriptor(
  name='GlobalState',
  full_name='carbon.frontend.robot.GlobalState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='computer', full_name='carbon.frontend.robot.GlobalState.computer', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gps', full_name='carbon.frontend.robot.GlobalState.gps', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wheel', full_name='carbon.frontend.robot.GlobalState.wheel', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='strobe', full_name='carbon.frontend.robot.GlobalState.strobe', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='supervisory', full_name='carbon.frontend.robot.GlobalState.supervisory', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='safety', full_name='carbon.frontend.robot.GlobalState.safety', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='chiller', full_name='carbon.frontend.robot.GlobalState.chiller', index=6,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='air_conditioner', full_name='carbon.frontend.robot.GlobalState.air_conditioner', index=7,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='isobus', full_name='carbon.frontend.robot.GlobalState.isobus', index=8,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_time', full_name='carbon.frontend.robot.GlobalState.power_time', index=9,
      number=11, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='models', full_name='carbon.frontend.robot.GlobalState.models', index=10,
      number=12, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weeding', full_name='carbon.frontend.robot.GlobalState.weeding', index=11,
      number=13, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='usb', full_name='carbon.frontend.robot.GlobalState.usb', index=12,
      number=14, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=5462,
  serialized_end=6224,
)


_CAMERASTATE = _descriptor.Descriptor(
  name='CameraState',
  full_name='carbon.frontend.robot.CameraState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='vendor', full_name='carbon.frontend.robot.CameraState.vendor', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model', full_name='carbon.frontend.robot.CameraState.model', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ip', full_name='carbon.frontend.robot.CameraState.ip', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.frontend.robot.CameraState.serial', index=3,
      number=4, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='id', full_name='carbon.frontend.robot.CameraState.id', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='connected', full_name='carbon.frontend.robot.CameraState.connected', index=5,
      number=6, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='exposure_us', full_name='carbon.frontend.robot.CameraState.exposure_us', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='gpu_id', full_name='carbon.frontend.robot.CameraState.gpu_id', index=7,
      number=8, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6227,
  serialized_end=6367,
)


_MAXONPIDSTATE = _descriptor.Descriptor(
  name='MaxonPidState',
  full_name='carbon.frontend.robot.MaxonPidState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='current_p', full_name='carbon.frontend.robot.MaxonPidState.current_p', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='current_i', full_name='carbon.frontend.robot.MaxonPidState.current_i', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position_p', full_name='carbon.frontend.robot.MaxonPidState.position_p', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position_i', full_name='carbon.frontend.robot.MaxonPidState.position_i', index=3,
      number=4, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position_d', full_name='carbon.frontend.robot.MaxonPidState.position_d', index=4,
      number=5, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position_ffv', full_name='carbon.frontend.robot.MaxonPidState.position_ffv', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='position_ffa', full_name='carbon.frontend.robot.MaxonPidState.position_ffa', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6370,
  serialized_end=6527,
)


_SERVOSTATE = _descriptor.Descriptor(
  name='ServoState',
  full_name='carbon.frontend.robot.ServoState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.frontend.robot.ServoState.type', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min', full_name='carbon.frontend.robot.ServoState.min', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max', full_name='carbon.frontend.robot.ServoState.max', index=2,
      number=3, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_read_position', full_name='carbon.frontend.robot.ServoState.last_read_position', index=3,
      number=4, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_requested_position', full_name='carbon.frontend.robot.ServoState.last_requested_position', index=4,
      number=5, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_requested_velocity', full_name='carbon.frontend.robot.ServoState.last_requested_velocity', index=5,
      number=6, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_requested_follow_velocity', full_name='carbon.frontend.robot.ServoState.last_requested_follow_velocity', index=6,
      number=7, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='maxon', full_name='carbon.frontend.robot.ServoState.maxon', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='pids', full_name='carbon.frontend.robot.ServoState.pids',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=6530,
  serialized_end=6779,
)


_SCANNERMETRICSTATE = _descriptor.Descriptor(
  name='ScannerMetricState',
  full_name='carbon.frontend.robot.ScannerMetricState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='n_entities_targeted', full_name='carbon.frontend.robot.ScannerMetricState.n_entities_targeted', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='avg_accuracy', full_name='carbon.frontend.robot.ScannerMetricState.avg_accuracy', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='avg_initial_accuracy', full_name='carbon.frontend.robot.ScannerMetricState.avg_initial_accuracy', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='avg_overhead_time', full_name='carbon.frontend.robot.ScannerMetricState.avg_overhead_time', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='error_rate', full_name='carbon.frontend.robot.ScannerMetricState.error_rate', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='not_found_rate', full_name='carbon.frontend.robot.ScannerMetricState.not_found_rate', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='out_of_range_rate', full_name='carbon.frontend.robot.ScannerMetricState.out_of_range_rate', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_changed_rate', full_name='carbon.frontend.robot.ScannerMetricState.target_changed_rate', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=6782,
  serialized_end=7010,
)


_SCANNERLASERSTATE = _descriptor.Descriptor(
  name='ScannerLaserState',
  full_name='carbon.frontend.robot.ScannerLaserState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='serial', full_name='carbon.frontend.robot.ScannerLaserState.serial', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='n_shots', full_name='carbon.frontend.robot.ScannerLaserState.n_shots', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='fire_time_ms', full_name='carbon.frontend.robot.ScannerLaserState.fire_time_ms', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='controlled_time_ms', full_name='carbon.frontend.robot.ScannerLaserState.controlled_time_ms', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpsu_power', full_name='carbon.frontend.robot.ScannerLaserState.lpsu_power', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lpsu_current', full_name='carbon.frontend.robot.ScannerLaserState.lpsu_current', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='therm_sensor', full_name='carbon.frontend.robot.ScannerLaserState.therm_sensor', index=6,
      number=7, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='therm_ref', full_name='carbon.frontend.robot.ScannerLaserState.therm_ref', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='delta_temp', full_name='carbon.frontend.robot.ScannerLaserState.delta_temp', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='power_bypassed', full_name='carbon.frontend.robot.ScannerLaserState.power_bypassed', index=9,
      number=10, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='armed', full_name='carbon.frontend.robot.ScannerLaserState.armed', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='watchdog', full_name='carbon.frontend.robot.ScannerLaserState.watchdog', index=11,
      number=12, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firing_requested', full_name='carbon.frontend.robot.ScannerLaserState.firing_requested', index=12,
      number=13, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='firing', full_name='carbon.frontend.robot.ScannerLaserState.firing', index=13,
      number=14, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='intensity', full_name='carbon.frontend.robot.ScannerLaserState.intensity', index=14,
      number=15, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='wattage', full_name='carbon.frontend.robot.ScannerLaserState.wattage', index=15,
      number=16, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='nominal_wattage', full_name='carbon.frontend.robot.ScannerLaserState.nominal_wattage', index=16,
      number=17, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7013,
  serialized_end=7378,
)


_SCANNERCROSSHAIRSTATE = _descriptor.Descriptor(
  name='ScannerCrosshairState',
  full_name='carbon.frontend.robot.ScannerCrosshairState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='last_update_timestamp_ms', full_name='carbon.frontend.robot.ScannerCrosshairState.last_update_timestamp_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='x', full_name='carbon.frontend.robot.ScannerCrosshairState.x', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='y', full_name='carbon.frontend.robot.ScannerCrosshairState.y', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7380,
  serialized_end=7459,
)


_SCANNERLENSSTATE = _descriptor.Descriptor(
  name='ScannerLensState',
  full_name='carbon.frontend.robot.ScannerLensState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='last_requested_value', full_name='carbon.frontend.robot.ScannerLensState.last_requested_value', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='actual_value', full_name='carbon.frontend.robot.ScannerLensState.actual_value', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7461,
  serialized_end=7531,
)


_SCANNERSTATE = _descriptor.Descriptor(
  name='ScannerState',
  full_name='carbon.frontend.robot.ScannerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.robot.ScannerState.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='board', full_name='carbon.frontend.robot.ScannerState.board', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='enabled', full_name='carbon.frontend.robot.ScannerState.enabled', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pan', full_name='carbon.frontend.robot.ScannerState.pan', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tilt', full_name='carbon.frontend.robot.ScannerState.tilt', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crosshair', full_name='carbon.frontend.robot.ScannerState.crosshair', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='lens', full_name='carbon.frontend.robot.ScannerState.lens', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='laser', full_name='carbon.frontend.robot.ScannerState.laser', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metrics', full_name='carbon.frontend.robot.ScannerState.metrics', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7534,
  serialized_end=7963,
)


_CVNODESTATE = _descriptor.Descriptor(
  name='CVNodeState',
  full_name='carbon.frontend.robot.CVNodeState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.robot.CVNodeState.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mean_fps', full_name='carbon.frontend.robot.CVNodeState.mean_fps', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mean_output_latency', full_name='carbon.frontend.robot.CVNodeState.mean_output_latency', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p99_output_latency', full_name='carbon.frontend.robot.CVNodeState.p99_output_latency', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='mean_real_latency', full_name='carbon.frontend.robot.CVNodeState.mean_real_latency', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='p99_real_latency', full_name='carbon.frontend.robot.CVNodeState.p99_real_latency', index=5,
      number=6, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.frontend.robot.CVNodeState.state', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pull_ms', full_name='carbon.frontend.robot.CVNodeState.pull_ms', index=7,
      number=8, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='processing_ms', full_name='carbon.frontend.robot.CVNodeState.processing_ms', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='pushing_ms', full_name='carbon.frontend.robot.CVNodeState.pushing_ms', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=7966,
  serialized_end=8196,
)


_CVSTATE = _descriptor.Descriptor(
  name='CVState',
  full_name='carbon.frontend.robot.CVState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='nodes', full_name='carbon.frontend.robot.CVState.nodes', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8198,
  serialized_end=8258,
)


_TRACKERSTATE = _descriptor.Descriptor(
  name='TrackerState',
  full_name='carbon.frontend.robot.TrackerState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.robot.TrackerState.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='n_trajectories', full_name='carbon.frontend.robot.TrackerState.n_trajectories', index=1,
      number=2, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='capacity', full_name='carbon.frontend.robot.TrackerState.capacity', index=2,
      number=3, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8260,
  serialized_end=8330,
)


_INGESTCLIENTSTATE = _descriptor.Descriptor(
  name='IngestClientState',
  full_name='carbon.frontend.robot.IngestClientState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.frontend.robot.IngestClientState.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='over_capacity', full_name='carbon.frontend.robot.IngestClientState.over_capacity', index=1,
      number=2, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='over_constraints', full_name='carbon.frontend.robot.IngestClientState.over_constraints', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8332,
  serialized_end=8414,
)


_AIMBOTSTATE = _descriptor.Descriptor(
  name='AimbotState',
  full_name='carbon.frontend.robot.AimbotState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='trackers', full_name='carbon.frontend.robot.AimbotState.trackers', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ingests', full_name='carbon.frontend.robot.AimbotState.ingests', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8416,
  serialized_end=8543,
)


_ROWSTATE = _descriptor.Descriptor(
  name='RowState',
  full_name='carbon.frontend.robot.RowState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='computer', full_name='carbon.frontend.robot.RowState.computer', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scanners', full_name='carbon.frontend.robot.RowState.scanners', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='predicts', full_name='carbon.frontend.robot.RowState.predicts', index=2,
      number=3, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='targets', full_name='carbon.frontend.robot.RowState.targets', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='extras', full_name='carbon.frontend.robot.RowState.extras', index=4,
      number=5, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='cv', full_name='carbon.frontend.robot.RowState.cv', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='aimbot', full_name='carbon.frontend.robot.RowState.aimbot', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8546,
  serialized_end=8922,
)


_ROBOTSTATE = _descriptor.Descriptor(
  name='RobotState',
  full_name='carbon.frontend.robot.RobotState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='global', full_name='carbon.frontend.robot.RobotState.global', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='rows', full_name='carbon.frontend.robot.RobotState.rows', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=8924,
  serialized_end=9035,
)


_TIMESTAMPEDROBOTSTATE = _descriptor.Descriptor(
  name='TimestampedRobotState',
  full_name='carbon.frontend.robot.TimestampedRobotState',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.robot.TimestampedRobotState.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='state', full_name='carbon.frontend.robot.TimestampedRobotState.state', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9037,
  serialized_end=9155,
)


_ROBOTSTATEREQUEST = _descriptor.Descriptor(
  name='RobotStateRequest',
  full_name='carbon.frontend.robot.RobotStateRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.robot.RobotStateRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=9157,
  serialized_end=9221,
)

_GPSBOARDSTATE.fields_by_name['board'].message_type = _BOARDSTATE
_GPSBOARDSTATE.fields_by_name['position'].message_type = _GPSCOORD
_WHEELENCODERBOARDSTATE.fields_by_name['board'].message_type = _BOARDSTATE
_WHEELENCODERBOARDSTATE.fields_by_name['wheels'].message_type = _WHEELSTATE
_STROBEBOARDSTATE.fields_by_name['board'].message_type = _BOARDSTATE
_HOSTSTATE_NICSENTRY.fields_by_name['value'].message_type = _NICSTATE
_HOSTSTATE_NICSENTRY.containing_type = _HOSTSTATE
_HOSTSTATE.fields_by_name['nics'].message_type = _HOSTSTATE_NICSENTRY
_HOSTSTATE.fields_by_name['main_partition'].message_type = _STORAGEDRIVESTATE
_HOSTSTATE.fields_by_name['data_partition'].message_type = _STORAGEDRIVESTATE
_HOSTSTATE.fields_by_name['gpus'].message_type = _GPUSTATE
_SOFTWAREUPDATEVERSIONSTATE_IMAGESREQUIREDENTRY.containing_type = _SOFTWAREUPDATEVERSIONSTATE
_SOFTWAREUPDATEVERSIONSTATE.fields_by_name['images_required'].message_type = _SOFTWAREUPDATEVERSIONSTATE_IMAGESREQUIREDENTRY
_SYSTEMVERSIONSTATE.fields_by_name['current'].message_type = _OPERATINGSYSTEMSTATE
_SYSTEMVERSIONSTATE.fields_by_name['other'].message_type = _OPERATINGSYSTEMSTATE
_SOFTWAREVERSIONSSTATE.fields_by_name['current'].message_type = _SOFTWAREUPDATEVERSIONSTATE
_SOFTWAREVERSIONSSTATE.fields_by_name['previous'].message_type = _SOFTWAREUPDATEVERSIONSTATE
_SOFTWAREVERSIONSSTATE.fields_by_name['target'].message_type = _SOFTWAREUPDATEVERSIONSTATE
_SOFTWAREVERSIONSSTATE.fields_by_name['system'].message_type = _SYSTEMVERSIONSTATE
_SOFTWAREPROCESSSTATE.fields_by_name['stage'].enum_type = _SOFTWAREPROCESSSTAGE
_SOFTWARESTATE_PROCESSESENTRY.fields_by_name['value'].message_type = _SOFTWAREPROCESSSTATE
_SOFTWARESTATE_PROCESSESENTRY.containing_type = _SOFTWARESTATE
_SOFTWARESTATE.fields_by_name['processes'].message_type = _SOFTWARESTATE_PROCESSESENTRY
_SOFTWARESTATE.fields_by_name['versions'].message_type = _SOFTWAREVERSIONSSTATE
_MODELSTATE.fields_by_name['deepweed'].message_type = _DEEPWEEDMODELSTATE
_MODELSTATE.fields_by_name['p2p'].message_type = _P2PMODELSTATE
_MODELSTATE.oneofs_by_name['specific'].fields.append(
  _MODELSTATE.fields_by_name['deepweed'])
_MODELSTATE.fields_by_name['deepweed'].containing_oneof = _MODELSTATE.oneofs_by_name['specific']
_MODELSTATE.oneofs_by_name['specific'].fields.append(
  _MODELSTATE.fields_by_name['p2p'])
_MODELSTATE.fields_by_name['p2p'].containing_oneof = _MODELSTATE.oneofs_by_name['specific']
_ROWMODELSTATE.fields_by_name['models'].message_type = _MODELSTATE
_MODELMANAGERSTATE.fields_by_name['local_models'].message_type = _MODELSTATE
_MODELMANAGERSTATE.fields_by_name['row_models'].message_type = _ROWMODELSTATE
_COMPUTERSTATE.fields_by_name['software'].message_type = _SOFTWARESTATE
_COMPUTERSTATE.fields_by_name['host'].message_type = _HOSTSTATE
_WEEDINGSTATE.fields_by_name['stage'].enum_type = _BOOTSTAGE
_WEEDINGSTATE.fields_by_name['velocity'].message_type = _VELOCITYSTATE
_GLOBALSTATE.fields_by_name['computer'].message_type = _COMPUTERSTATE
_GLOBALSTATE.fields_by_name['gps'].message_type = _GPSBOARDSTATE
_GLOBALSTATE.fields_by_name['wheel'].message_type = _WHEELENCODERBOARDSTATE
_GLOBALSTATE.fields_by_name['strobe'].message_type = _STROBEBOARDSTATE
_GLOBALSTATE.fields_by_name['supervisory'].message_type = _SUPERVISORYPLCSTATE
_GLOBALSTATE.fields_by_name['safety'].message_type = _SAFETYPLCSTATE
_GLOBALSTATE.fields_by_name['chiller'].message_type = _CHILLERSTATE
_GLOBALSTATE.fields_by_name['air_conditioner'].message_type = _AIRCONDITIONERSTATE
_GLOBALSTATE.fields_by_name['isobus'].message_type = _ISOBUSSTATE
_GLOBALSTATE.fields_by_name['power_time'].message_type = _POWERTIMESTATE
_GLOBALSTATE.fields_by_name['models'].message_type = _MODELMANAGERSTATE
_GLOBALSTATE.fields_by_name['weeding'].message_type = _WEEDINGSTATE
_GLOBALSTATE.fields_by_name['usb'].message_type = _USBDRIVESTATE
_SERVOSTATE.fields_by_name['maxon'].message_type = _MAXONPIDSTATE
_SERVOSTATE.oneofs_by_name['pids'].fields.append(
  _SERVOSTATE.fields_by_name['maxon'])
_SERVOSTATE.fields_by_name['maxon'].containing_oneof = _SERVOSTATE.oneofs_by_name['pids']
_SCANNERSTATE.fields_by_name['board'].message_type = _BOARDSTATE
_SCANNERSTATE.fields_by_name['pan'].message_type = _SERVOSTATE
_SCANNERSTATE.fields_by_name['tilt'].message_type = _SERVOSTATE
_SCANNERSTATE.fields_by_name['crosshair'].message_type = _SCANNERCROSSHAIRSTATE
_SCANNERSTATE.fields_by_name['lens'].message_type = _SCANNERLENSSTATE
_SCANNERSTATE.fields_by_name['laser'].message_type = _SCANNERLASERSTATE
_SCANNERSTATE.fields_by_name['metrics'].message_type = _SCANNERMETRICSTATE
_CVSTATE.fields_by_name['nodes'].message_type = _CVNODESTATE
_AIMBOTSTATE.fields_by_name['trackers'].message_type = _TRACKERSTATE
_AIMBOTSTATE.fields_by_name['ingests'].message_type = _INGESTCLIENTSTATE
_ROWSTATE.fields_by_name['computer'].message_type = _COMPUTERSTATE
_ROWSTATE.fields_by_name['scanners'].message_type = _SCANNERSTATE
_ROWSTATE.fields_by_name['predicts'].message_type = _CAMERASTATE
_ROWSTATE.fields_by_name['targets'].message_type = _CAMERASTATE
_ROWSTATE.fields_by_name['extras'].message_type = _CAMERASTATE
_ROWSTATE.fields_by_name['cv'].message_type = _CVSTATE
_ROWSTATE.fields_by_name['aimbot'].message_type = _AIMBOTSTATE
_ROBOTSTATE.fields_by_name['global'].message_type = _GLOBALSTATE
_ROBOTSTATE.fields_by_name['rows'].message_type = _ROWSTATE
_TIMESTAMPEDROBOTSTATE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_TIMESTAMPEDROBOTSTATE.fields_by_name['state'].message_type = _ROBOTSTATE
_ROBOTSTATEREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
DESCRIPTOR.message_types_by_name['BoardState'] = _BOARDSTATE
DESCRIPTOR.message_types_by_name['GPSCoord'] = _GPSCOORD
DESCRIPTOR.message_types_by_name['GPSBoardState'] = _GPSBOARDSTATE
DESCRIPTOR.message_types_by_name['WheelState'] = _WHEELSTATE
DESCRIPTOR.message_types_by_name['WheelEncoderBoardState'] = _WHEELENCODERBOARDSTATE
DESCRIPTOR.message_types_by_name['StrobeBoardState'] = _STROBEBOARDSTATE
DESCRIPTOR.message_types_by_name['ISOBusState'] = _ISOBUSSTATE
DESCRIPTOR.message_types_by_name['ChillerState'] = _CHILLERSTATE
DESCRIPTOR.message_types_by_name['AirConditionerState'] = _AIRCONDITIONERSTATE
DESCRIPTOR.message_types_by_name['USBDriveState'] = _USBDRIVESTATE
DESCRIPTOR.message_types_by_name['SupervisoryPLCState'] = _SUPERVISORYPLCSTATE
DESCRIPTOR.message_types_by_name['SafetyPLCState'] = _SAFETYPLCSTATE
DESCRIPTOR.message_types_by_name['NicState'] = _NICSTATE
DESCRIPTOR.message_types_by_name['StorageDriveState'] = _STORAGEDRIVESTATE
DESCRIPTOR.message_types_by_name['GPUState'] = _GPUSTATE
DESCRIPTOR.message_types_by_name['HostState'] = _HOSTSTATE
DESCRIPTOR.message_types_by_name['SoftwareUpdateVersionState'] = _SOFTWAREUPDATEVERSIONSTATE
DESCRIPTOR.message_types_by_name['OperatingSystemState'] = _OPERATINGSYSTEMSTATE
DESCRIPTOR.message_types_by_name['SystemVersionState'] = _SYSTEMVERSIONSTATE
DESCRIPTOR.message_types_by_name['SoftwareVersionsState'] = _SOFTWAREVERSIONSSTATE
DESCRIPTOR.message_types_by_name['SoftwareProcessState'] = _SOFTWAREPROCESSSTATE
DESCRIPTOR.message_types_by_name['SoftwareState'] = _SOFTWARESTATE
DESCRIPTOR.message_types_by_name['DeepweedModelState'] = _DEEPWEEDMODELSTATE
DESCRIPTOR.message_types_by_name['P2PModelState'] = _P2PMODELSTATE
DESCRIPTOR.message_types_by_name['ModelState'] = _MODELSTATE
DESCRIPTOR.message_types_by_name['RowModelState'] = _ROWMODELSTATE
DESCRIPTOR.message_types_by_name['ModelManagerState'] = _MODELMANAGERSTATE
DESCRIPTOR.message_types_by_name['ComputerState'] = _COMPUTERSTATE
DESCRIPTOR.message_types_by_name['PowerTimeState'] = _POWERTIMESTATE
DESCRIPTOR.message_types_by_name['VelocityState'] = _VELOCITYSTATE
DESCRIPTOR.message_types_by_name['WeedingState'] = _WEEDINGSTATE
DESCRIPTOR.message_types_by_name['GlobalState'] = _GLOBALSTATE
DESCRIPTOR.message_types_by_name['CameraState'] = _CAMERASTATE
DESCRIPTOR.message_types_by_name['MaxonPidState'] = _MAXONPIDSTATE
DESCRIPTOR.message_types_by_name['ServoState'] = _SERVOSTATE
DESCRIPTOR.message_types_by_name['ScannerMetricState'] = _SCANNERMETRICSTATE
DESCRIPTOR.message_types_by_name['ScannerLaserState'] = _SCANNERLASERSTATE
DESCRIPTOR.message_types_by_name['ScannerCrosshairState'] = _SCANNERCROSSHAIRSTATE
DESCRIPTOR.message_types_by_name['ScannerLensState'] = _SCANNERLENSSTATE
DESCRIPTOR.message_types_by_name['ScannerState'] = _SCANNERSTATE
DESCRIPTOR.message_types_by_name['CVNodeState'] = _CVNODESTATE
DESCRIPTOR.message_types_by_name['CVState'] = _CVSTATE
DESCRIPTOR.message_types_by_name['TrackerState'] = _TRACKERSTATE
DESCRIPTOR.message_types_by_name['IngestClientState'] = _INGESTCLIENTSTATE
DESCRIPTOR.message_types_by_name['AimbotState'] = _AIMBOTSTATE
DESCRIPTOR.message_types_by_name['RowState'] = _ROWSTATE
DESCRIPTOR.message_types_by_name['RobotState'] = _ROBOTSTATE
DESCRIPTOR.message_types_by_name['TimestampedRobotState'] = _TIMESTAMPEDROBOTSTATE
DESCRIPTOR.message_types_by_name['RobotStateRequest'] = _ROBOTSTATEREQUEST
DESCRIPTOR.enum_types_by_name['SoftwareProcessStage'] = _SOFTWAREPROCESSSTAGE
DESCRIPTOR.enum_types_by_name['BootStage'] = _BOOTSTAGE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

BoardState = _reflection.GeneratedProtocolMessageType('BoardState', (_message.Message,), {
  'DESCRIPTOR' : _BOARDSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.BoardState)
  })
_sym_db.RegisterMessage(BoardState)

GPSCoord = _reflection.GeneratedProtocolMessageType('GPSCoord', (_message.Message,), {
  'DESCRIPTOR' : _GPSCOORD,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.GPSCoord)
  })
_sym_db.RegisterMessage(GPSCoord)

GPSBoardState = _reflection.GeneratedProtocolMessageType('GPSBoardState', (_message.Message,), {
  'DESCRIPTOR' : _GPSBOARDSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.GPSBoardState)
  })
_sym_db.RegisterMessage(GPSBoardState)

WheelState = _reflection.GeneratedProtocolMessageType('WheelState', (_message.Message,), {
  'DESCRIPTOR' : _WHEELSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.WheelState)
  })
_sym_db.RegisterMessage(WheelState)

WheelEncoderBoardState = _reflection.GeneratedProtocolMessageType('WheelEncoderBoardState', (_message.Message,), {
  'DESCRIPTOR' : _WHEELENCODERBOARDSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.WheelEncoderBoardState)
  })
_sym_db.RegisterMessage(WheelEncoderBoardState)

StrobeBoardState = _reflection.GeneratedProtocolMessageType('StrobeBoardState', (_message.Message,), {
  'DESCRIPTOR' : _STROBEBOARDSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.StrobeBoardState)
  })
_sym_db.RegisterMessage(StrobeBoardState)

ISOBusState = _reflection.GeneratedProtocolMessageType('ISOBusState', (_message.Message,), {
  'DESCRIPTOR' : _ISOBUSSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ISOBusState)
  })
_sym_db.RegisterMessage(ISOBusState)

ChillerState = _reflection.GeneratedProtocolMessageType('ChillerState', (_message.Message,), {
  'DESCRIPTOR' : _CHILLERSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ChillerState)
  })
_sym_db.RegisterMessage(ChillerState)

AirConditionerState = _reflection.GeneratedProtocolMessageType('AirConditionerState', (_message.Message,), {
  'DESCRIPTOR' : _AIRCONDITIONERSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.AirConditionerState)
  })
_sym_db.RegisterMessage(AirConditionerState)

USBDriveState = _reflection.GeneratedProtocolMessageType('USBDriveState', (_message.Message,), {
  'DESCRIPTOR' : _USBDRIVESTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.USBDriveState)
  })
_sym_db.RegisterMessage(USBDriveState)

SupervisoryPLCState = _reflection.GeneratedProtocolMessageType('SupervisoryPLCState', (_message.Message,), {
  'DESCRIPTOR' : _SUPERVISORYPLCSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SupervisoryPLCState)
  })
_sym_db.RegisterMessage(SupervisoryPLCState)

SafetyPLCState = _reflection.GeneratedProtocolMessageType('SafetyPLCState', (_message.Message,), {
  'DESCRIPTOR' : _SAFETYPLCSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SafetyPLCState)
  })
_sym_db.RegisterMessage(SafetyPLCState)

NicState = _reflection.GeneratedProtocolMessageType('NicState', (_message.Message,), {
  'DESCRIPTOR' : _NICSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.NicState)
  })
_sym_db.RegisterMessage(NicState)

StorageDriveState = _reflection.GeneratedProtocolMessageType('StorageDriveState', (_message.Message,), {
  'DESCRIPTOR' : _STORAGEDRIVESTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.StorageDriveState)
  })
_sym_db.RegisterMessage(StorageDriveState)

GPUState = _reflection.GeneratedProtocolMessageType('GPUState', (_message.Message,), {
  'DESCRIPTOR' : _GPUSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.GPUState)
  })
_sym_db.RegisterMessage(GPUState)

HostState = _reflection.GeneratedProtocolMessageType('HostState', (_message.Message,), {

  'NicsEntry' : _reflection.GeneratedProtocolMessageType('NicsEntry', (_message.Message,), {
    'DESCRIPTOR' : _HOSTSTATE_NICSENTRY,
    '__module__' : 'frontend.proto.robot_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.robot.HostState.NicsEntry)
    })
  ,
  'DESCRIPTOR' : _HOSTSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.HostState)
  })
_sym_db.RegisterMessage(HostState)
_sym_db.RegisterMessage(HostState.NicsEntry)

SoftwareUpdateVersionState = _reflection.GeneratedProtocolMessageType('SoftwareUpdateVersionState', (_message.Message,), {

  'ImagesRequiredEntry' : _reflection.GeneratedProtocolMessageType('ImagesRequiredEntry', (_message.Message,), {
    'DESCRIPTOR' : _SOFTWAREUPDATEVERSIONSTATE_IMAGESREQUIREDENTRY,
    '__module__' : 'frontend.proto.robot_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SoftwareUpdateVersionState.ImagesRequiredEntry)
    })
  ,
  'DESCRIPTOR' : _SOFTWAREUPDATEVERSIONSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SoftwareUpdateVersionState)
  })
_sym_db.RegisterMessage(SoftwareUpdateVersionState)
_sym_db.RegisterMessage(SoftwareUpdateVersionState.ImagesRequiredEntry)

OperatingSystemState = _reflection.GeneratedProtocolMessageType('OperatingSystemState', (_message.Message,), {
  'DESCRIPTOR' : _OPERATINGSYSTEMSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.OperatingSystemState)
  })
_sym_db.RegisterMessage(OperatingSystemState)

SystemVersionState = _reflection.GeneratedProtocolMessageType('SystemVersionState', (_message.Message,), {
  'DESCRIPTOR' : _SYSTEMVERSIONSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SystemVersionState)
  })
_sym_db.RegisterMessage(SystemVersionState)

SoftwareVersionsState = _reflection.GeneratedProtocolMessageType('SoftwareVersionsState', (_message.Message,), {
  'DESCRIPTOR' : _SOFTWAREVERSIONSSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SoftwareVersionsState)
  })
_sym_db.RegisterMessage(SoftwareVersionsState)

SoftwareProcessState = _reflection.GeneratedProtocolMessageType('SoftwareProcessState', (_message.Message,), {
  'DESCRIPTOR' : _SOFTWAREPROCESSSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SoftwareProcessState)
  })
_sym_db.RegisterMessage(SoftwareProcessState)

SoftwareState = _reflection.GeneratedProtocolMessageType('SoftwareState', (_message.Message,), {

  'ProcessesEntry' : _reflection.GeneratedProtocolMessageType('ProcessesEntry', (_message.Message,), {
    'DESCRIPTOR' : _SOFTWARESTATE_PROCESSESENTRY,
    '__module__' : 'frontend.proto.robot_pb2'
    # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SoftwareState.ProcessesEntry)
    })
  ,
  'DESCRIPTOR' : _SOFTWARESTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.SoftwareState)
  })
_sym_db.RegisterMessage(SoftwareState)
_sym_db.RegisterMessage(SoftwareState.ProcessesEntry)

DeepweedModelState = _reflection.GeneratedProtocolMessageType('DeepweedModelState', (_message.Message,), {
  'DESCRIPTOR' : _DEEPWEEDMODELSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.DeepweedModelState)
  })
_sym_db.RegisterMessage(DeepweedModelState)

P2PModelState = _reflection.GeneratedProtocolMessageType('P2PModelState', (_message.Message,), {
  'DESCRIPTOR' : _P2PMODELSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.P2PModelState)
  })
_sym_db.RegisterMessage(P2PModelState)

ModelState = _reflection.GeneratedProtocolMessageType('ModelState', (_message.Message,), {
  'DESCRIPTOR' : _MODELSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ModelState)
  })
_sym_db.RegisterMessage(ModelState)

RowModelState = _reflection.GeneratedProtocolMessageType('RowModelState', (_message.Message,), {
  'DESCRIPTOR' : _ROWMODELSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.RowModelState)
  })
_sym_db.RegisterMessage(RowModelState)

ModelManagerState = _reflection.GeneratedProtocolMessageType('ModelManagerState', (_message.Message,), {
  'DESCRIPTOR' : _MODELMANAGERSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ModelManagerState)
  })
_sym_db.RegisterMessage(ModelManagerState)

ComputerState = _reflection.GeneratedProtocolMessageType('ComputerState', (_message.Message,), {
  'DESCRIPTOR' : _COMPUTERSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ComputerState)
  })
_sym_db.RegisterMessage(ComputerState)

PowerTimeState = _reflection.GeneratedProtocolMessageType('PowerTimeState', (_message.Message,), {
  'DESCRIPTOR' : _POWERTIMESTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.PowerTimeState)
  })
_sym_db.RegisterMessage(PowerTimeState)

VelocityState = _reflection.GeneratedProtocolMessageType('VelocityState', (_message.Message,), {
  'DESCRIPTOR' : _VELOCITYSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.VelocityState)
  })
_sym_db.RegisterMessage(VelocityState)

WeedingState = _reflection.GeneratedProtocolMessageType('WeedingState', (_message.Message,), {
  'DESCRIPTOR' : _WEEDINGSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.WeedingState)
  })
_sym_db.RegisterMessage(WeedingState)

GlobalState = _reflection.GeneratedProtocolMessageType('GlobalState', (_message.Message,), {
  'DESCRIPTOR' : _GLOBALSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.GlobalState)
  })
_sym_db.RegisterMessage(GlobalState)

CameraState = _reflection.GeneratedProtocolMessageType('CameraState', (_message.Message,), {
  'DESCRIPTOR' : _CAMERASTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.CameraState)
  })
_sym_db.RegisterMessage(CameraState)

MaxonPidState = _reflection.GeneratedProtocolMessageType('MaxonPidState', (_message.Message,), {
  'DESCRIPTOR' : _MAXONPIDSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.MaxonPidState)
  })
_sym_db.RegisterMessage(MaxonPidState)

ServoState = _reflection.GeneratedProtocolMessageType('ServoState', (_message.Message,), {
  'DESCRIPTOR' : _SERVOSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ServoState)
  })
_sym_db.RegisterMessage(ServoState)

ScannerMetricState = _reflection.GeneratedProtocolMessageType('ScannerMetricState', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERMETRICSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ScannerMetricState)
  })
_sym_db.RegisterMessage(ScannerMetricState)

ScannerLaserState = _reflection.GeneratedProtocolMessageType('ScannerLaserState', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERLASERSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ScannerLaserState)
  })
_sym_db.RegisterMessage(ScannerLaserState)

ScannerCrosshairState = _reflection.GeneratedProtocolMessageType('ScannerCrosshairState', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERCROSSHAIRSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ScannerCrosshairState)
  })
_sym_db.RegisterMessage(ScannerCrosshairState)

ScannerLensState = _reflection.GeneratedProtocolMessageType('ScannerLensState', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERLENSSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ScannerLensState)
  })
_sym_db.RegisterMessage(ScannerLensState)

ScannerState = _reflection.GeneratedProtocolMessageType('ScannerState', (_message.Message,), {
  'DESCRIPTOR' : _SCANNERSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.ScannerState)
  })
_sym_db.RegisterMessage(ScannerState)

CVNodeState = _reflection.GeneratedProtocolMessageType('CVNodeState', (_message.Message,), {
  'DESCRIPTOR' : _CVNODESTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.CVNodeState)
  })
_sym_db.RegisterMessage(CVNodeState)

CVState = _reflection.GeneratedProtocolMessageType('CVState', (_message.Message,), {
  'DESCRIPTOR' : _CVSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.CVState)
  })
_sym_db.RegisterMessage(CVState)

TrackerState = _reflection.GeneratedProtocolMessageType('TrackerState', (_message.Message,), {
  'DESCRIPTOR' : _TRACKERSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.TrackerState)
  })
_sym_db.RegisterMessage(TrackerState)

IngestClientState = _reflection.GeneratedProtocolMessageType('IngestClientState', (_message.Message,), {
  'DESCRIPTOR' : _INGESTCLIENTSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.IngestClientState)
  })
_sym_db.RegisterMessage(IngestClientState)

AimbotState = _reflection.GeneratedProtocolMessageType('AimbotState', (_message.Message,), {
  'DESCRIPTOR' : _AIMBOTSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.AimbotState)
  })
_sym_db.RegisterMessage(AimbotState)

RowState = _reflection.GeneratedProtocolMessageType('RowState', (_message.Message,), {
  'DESCRIPTOR' : _ROWSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.RowState)
  })
_sym_db.RegisterMessage(RowState)

RobotState = _reflection.GeneratedProtocolMessageType('RobotState', (_message.Message,), {
  'DESCRIPTOR' : _ROBOTSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.RobotState)
  })
_sym_db.RegisterMessage(RobotState)

TimestampedRobotState = _reflection.GeneratedProtocolMessageType('TimestampedRobotState', (_message.Message,), {
  'DESCRIPTOR' : _TIMESTAMPEDROBOTSTATE,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.TimestampedRobotState)
  })
_sym_db.RegisterMessage(TimestampedRobotState)

RobotStateRequest = _reflection.GeneratedProtocolMessageType('RobotStateRequest', (_message.Message,), {
  'DESCRIPTOR' : _ROBOTSTATEREQUEST,
  '__module__' : 'frontend.proto.robot_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.robot.RobotStateRequest)
  })
_sym_db.RegisterMessage(RobotStateRequest)


DESCRIPTOR._options = None
_HOSTSTATE_NICSENTRY._options = None
_SOFTWAREUPDATEVERSIONSTATE_IMAGESREQUIREDENTRY._options = None
_SOFTWARESTATE_PROCESSESENTRY._options = None

_ROBOTDIAGNOSTICSERVICE = _descriptor.ServiceDescriptor(
  name='RobotDiagnosticService',
  full_name='carbon.frontend.robot.RobotDiagnosticService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=9550,
  serialized_end=9683,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextRobotState',
    full_name='carbon.frontend.robot.RobotDiagnosticService.GetNextRobotState',
    index=0,
    containing_service=None,
    input_type=_ROBOTSTATEREQUEST,
    output_type=_TIMESTAMPEDROBOTSTATE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ROBOTDIAGNOSTICSERVICE)

DESCRIPTOR.services_by_name['RobotDiagnosticService'] = _ROBOTDIAGNOSTICSERVICE

# @@protoc_insertion_point(module_scope)

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: frontend/proto/image_stream.proto
#ifndef GRPC_frontend_2fproto_2fimage_5fstream_2eproto__INCLUDED
#define GRPC_frontend_2fproto_2fimage_5fstream_2eproto__INCLUDED

#include "frontend/proto/image_stream.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace frontend {
namespace image_stream {

class ImageStreamService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.frontend.image_stream.ImageStreamService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::carbon::frontend::image_stream::Image* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::Image>> AsyncGetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::Image>>(AsyncGetNextCameraImageRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::Image>> PrepareAsyncGetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::Image>>(PrepareAsyncGetNextCameraImageRaw(context, request, cq));
    }
    virtual ::grpc::Status GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>> AsyncGetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>>(AsyncGetPredictImageByTimestampRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>> PrepareAsyncGetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>>(PrepareAsyncGetPredictImageByTimestampRaw(context, request, cq));
    }
    virtual ::grpc::Status GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>> AsyncGetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>>(AsyncGetMultiPredictPerspectivesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>> PrepareAsyncGetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>>(PrepareAsyncGetMultiPredictPerspectivesRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::Image>* AsyncGetNextCameraImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::Image>* PrepareAsyncGetNextCameraImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* AsyncGetPredictImageByTimestampRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* PrepareAsyncGetPredictImageByTimestampRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* AsyncGetMultiPredictPerspectivesRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* PrepareAsyncGetMultiPredictPerspectivesRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::carbon::frontend::image_stream::Image* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::Image>> AsyncGetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::Image>>(AsyncGetNextCameraImageRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::Image>> PrepareAsyncGetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::Image>>(PrepareAsyncGetNextCameraImageRaw(context, request, cq));
    }
    ::grpc::Status GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>> AsyncGetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>>(AsyncGetPredictImageByTimestampRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>> PrepareAsyncGetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>>(PrepareAsyncGetPredictImageByTimestampRaw(context, request, cq));
    }
    ::grpc::Status GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>> AsyncGetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>>(AsyncGetMultiPredictPerspectivesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>> PrepareAsyncGetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>>(PrepareAsyncGetMultiPredictPerspectivesRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response, std::function<void(::grpc::Status)>) override;
      void GetNextCameraImage(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response, std::function<void(::grpc::Status)>) override;
      void GetPredictImageByTimestamp(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response, std::function<void(::grpc::Status)>) override;
      void GetMultiPredictPerspectives(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::Image>* AsyncGetNextCameraImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::Image>* PrepareAsyncGetNextCameraImageRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::CameraImageRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* AsyncGetPredictImageByTimestampRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* PrepareAsyncGetPredictImageByTimestampRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* AsyncGetMultiPredictPerspectivesRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* PrepareAsyncGetMultiPredictPerspectivesRaw(::grpc::ClientContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_GetNextCameraImage_;
    const ::grpc::internal::RpcMethod rpcmethod_GetPredictImageByTimestamp_;
    const ::grpc::internal::RpcMethod rpcmethod_GetMultiPredictPerspectives_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status GetNextCameraImage(::grpc::ServerContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response);
    virtual ::grpc::Status GetPredictImageByTimestamp(::grpc::ServerContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response);
    virtual ::grpc::Status GetMultiPredictPerspectives(::grpc::ServerContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_GetNextCameraImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetNextCameraImage() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_GetNextCameraImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::CameraImageRequest* /*request*/, ::carbon::frontend::image_stream::Image* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCameraImage(::grpc::ServerContext* context, ::carbon::frontend::image_stream::CameraImageRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::image_stream::Image>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetPredictImageByTimestamp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetPredictImageByTimestamp() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_GetPredictImageByTimestamp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageByTimestamp(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* /*request*/, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPredictImageByTimestamp(::grpc::ServerContext* context, ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetMultiPredictPerspectives : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetMultiPredictPerspectives() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetMultiPredictPerspectives() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMultiPredictPerspectives(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* /*request*/, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetMultiPredictPerspectives(::grpc::ServerContext* context, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_GetNextCameraImage<WithAsyncMethod_GetPredictImageByTimestamp<WithAsyncMethod_GetMultiPredictPerspectives<Service > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_GetNextCameraImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetNextCameraImage() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::image_stream::CameraImageRequest, ::carbon::frontend::image_stream::Image>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::image_stream::CameraImageRequest* request, ::carbon::frontend::image_stream::Image* response) { return this->GetNextCameraImage(context, request, response); }));}
    void SetMessageAllocatorFor_GetNextCameraImage(
        ::grpc::MessageAllocator< ::carbon::frontend::image_stream::CameraImageRequest, ::carbon::frontend::image_stream::Image>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::image_stream::CameraImageRequest, ::carbon::frontend::image_stream::Image>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetNextCameraImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::CameraImageRequest* /*request*/, ::carbon::frontend::image_stream::Image* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCameraImage(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::image_stream::CameraImageRequest* /*request*/, ::carbon::frontend::image_stream::Image* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetPredictImageByTimestamp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetPredictImageByTimestamp() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* request, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* response) { return this->GetPredictImageByTimestamp(context, request, response); }));}
    void SetMessageAllocatorFor_GetPredictImageByTimestamp(
        ::grpc::MessageAllocator< ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetPredictImageByTimestamp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageByTimestamp(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* /*request*/, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPredictImageByTimestamp(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* /*request*/, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetMultiPredictPerspectives : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetMultiPredictPerspectives() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* request, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* response) { return this->GetMultiPredictPerspectives(context, request, response); }));}
    void SetMessageAllocatorFor_GetMultiPredictPerspectives(
        ::grpc::MessageAllocator< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetMultiPredictPerspectives() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMultiPredictPerspectives(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* /*request*/, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetMultiPredictPerspectives(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* /*request*/, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_GetNextCameraImage<WithCallbackMethod_GetPredictImageByTimestamp<WithCallbackMethod_GetMultiPredictPerspectives<Service > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_GetNextCameraImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetNextCameraImage() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_GetNextCameraImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::CameraImageRequest* /*request*/, ::carbon::frontend::image_stream::Image* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetPredictImageByTimestamp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetPredictImageByTimestamp() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_GetPredictImageByTimestamp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageByTimestamp(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* /*request*/, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetMultiPredictPerspectives : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetMultiPredictPerspectives() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetMultiPredictPerspectives() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMultiPredictPerspectives(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* /*request*/, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetNextCameraImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetNextCameraImage() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_GetNextCameraImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::CameraImageRequest* /*request*/, ::carbon::frontend::image_stream::Image* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetNextCameraImage(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetPredictImageByTimestamp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetPredictImageByTimestamp() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_GetPredictImageByTimestamp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageByTimestamp(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* /*request*/, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetPredictImageByTimestamp(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetMultiPredictPerspectives : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetMultiPredictPerspectives() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetMultiPredictPerspectives() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMultiPredictPerspectives(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* /*request*/, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetMultiPredictPerspectives(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetNextCameraImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetNextCameraImage() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetNextCameraImage(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetNextCameraImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetNextCameraImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::CameraImageRequest* /*request*/, ::carbon::frontend::image_stream::Image* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetNextCameraImage(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetPredictImageByTimestamp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetPredictImageByTimestamp() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetPredictImageByTimestamp(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetPredictImageByTimestamp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetPredictImageByTimestamp(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* /*request*/, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetPredictImageByTimestamp(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetMultiPredictPerspectives : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetMultiPredictPerspectives() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetMultiPredictPerspectives(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetMultiPredictPerspectives() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetMultiPredictPerspectives(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* /*request*/, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetMultiPredictPerspectives(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetNextCameraImage : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetNextCameraImage() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::image_stream::CameraImageRequest, ::carbon::frontend::image_stream::Image>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::image_stream::CameraImageRequest, ::carbon::frontend::image_stream::Image>* streamer) {
                       return this->StreamedGetNextCameraImage(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetNextCameraImage() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetNextCameraImage(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::CameraImageRequest* /*request*/, ::carbon::frontend::image_stream::Image* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetNextCameraImage(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::image_stream::CameraImageRequest,::carbon::frontend::image_stream::Image>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetPredictImageByTimestamp : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetPredictImageByTimestamp() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* streamer) {
                       return this->StreamedGetPredictImageByTimestamp(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetPredictImageByTimestamp() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetPredictImageByTimestamp(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest* /*request*/, ::carbon::frontend::image_stream::GetPredictImageByTimestampResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetPredictImageByTimestamp(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::image_stream::GetPredictImageByTimestampRequest,::carbon::frontend::image_stream::GetPredictImageByTimestampResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetMultiPredictPerspectives : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetMultiPredictPerspectives() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* streamer) {
                       return this->StreamedGetMultiPredictPerspectives(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetMultiPredictPerspectives() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetMultiPredictPerspectives(::grpc::ServerContext* /*context*/, const ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest* /*request*/, ::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetMultiPredictPerspectives(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::frontend::image_stream::GetMultiPredictPerspectivesRequest,::carbon::frontend::image_stream::GetMultiPredictPerspectivesResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_GetNextCameraImage<WithStreamedUnaryMethod_GetPredictImageByTimestamp<WithStreamedUnaryMethod_GetMultiPredictPerspectives<Service > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_GetNextCameraImage<WithStreamedUnaryMethod_GetPredictImageByTimestamp<WithStreamedUnaryMethod_GetMultiPredictPerspectives<Service > > > StreamedService;
};

}  // namespace image_stream
}  // namespace frontend
}  // namespace carbon


#endif  // GRPC_frontend_2fproto_2fimage_5fstream_2eproto__INCLUDED

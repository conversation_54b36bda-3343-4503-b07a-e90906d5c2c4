// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/crosshair.proto

#include "frontend/proto/crosshair.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace crosshair {
constexpr CrosshairPosition::CrosshairPosition(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0)
  , y_(0){}
struct CrosshairPositionDefaultTypeInternal {
  constexpr CrosshairPositionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CrosshairPositionDefaultTypeInternal() {}
  union {
    CrosshairPosition _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CrosshairPositionDefaultTypeInternal _CrosshairPosition_default_instance_;
constexpr CrosshairPositionState::CrosshairPositionState(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr)
  , pos_(nullptr)
  , calibrating_(false)
  , calibration_failed_(false){}
struct CrosshairPositionStateDefaultTypeInternal {
  constexpr CrosshairPositionStateDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CrosshairPositionStateDefaultTypeInternal() {}
  union {
    CrosshairPositionState _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CrosshairPositionStateDefaultTypeInternal _CrosshairPositionState_default_instance_;
constexpr CrosshairPositionRequest::CrosshairPositionRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct CrosshairPositionRequestDefaultTypeInternal {
  constexpr CrosshairPositionRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~CrosshairPositionRequestDefaultTypeInternal() {}
  union {
    CrosshairPositionRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT CrosshairPositionRequestDefaultTypeInternal _CrosshairPositionRequest_default_instance_;
constexpr SetCrosshairPositionRequest::SetCrosshairPositionRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , pos_(nullptr){}
struct SetCrosshairPositionRequestDefaultTypeInternal {
  constexpr SetCrosshairPositionRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetCrosshairPositionRequestDefaultTypeInternal() {}
  union {
    SetCrosshairPositionRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetCrosshairPositionRequestDefaultTypeInternal _SetCrosshairPositionRequest_default_instance_;
constexpr MoveScannerRequest::MoveScannerRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : cam_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , x_(0)
  , y_(0){}
struct MoveScannerRequestDefaultTypeInternal {
  constexpr MoveScannerRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~MoveScannerRequestDefaultTypeInternal() {}
  union {
    MoveScannerRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT MoveScannerRequestDefaultTypeInternal _MoveScannerRequest_default_instance_;
constexpr AutoCrossHairCalStateRequest::AutoCrossHairCalStateRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : ts_(nullptr){}
struct AutoCrossHairCalStateRequestDefaultTypeInternal {
  constexpr AutoCrossHairCalStateRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AutoCrossHairCalStateRequestDefaultTypeInternal() {}
  union {
    AutoCrossHairCalStateRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AutoCrossHairCalStateRequestDefaultTypeInternal _AutoCrossHairCalStateRequest_default_instance_;
constexpr AutoCrossHairCalStateResponse::AutoCrossHairCalStateResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : failed_()
  , ts_(nullptr)
  , in_progress_(false)
  , progress_(0){}
struct AutoCrossHairCalStateResponseDefaultTypeInternal {
  constexpr AutoCrossHairCalStateResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AutoCrossHairCalStateResponseDefaultTypeInternal() {}
  union {
    AutoCrossHairCalStateResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AutoCrossHairCalStateResponseDefaultTypeInternal _AutoCrossHairCalStateResponse_default_instance_;
}  // namespace crosshair
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fcrosshair_2eproto[7];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_frontend_2fproto_2fcrosshair_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fcrosshair_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fcrosshair_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPosition, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPosition, x_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPosition, y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPositionState, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPositionState, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPositionState, pos_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPositionState, calibrating_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPositionState, calibration_failed_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPositionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPositionRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::CrosshairPositionRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::SetCrosshairPositionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::SetCrosshairPositionRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::SetCrosshairPositionRequest, pos_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::MoveScannerRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::MoveScannerRequest, cam_id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::MoveScannerRequest, x_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::MoveScannerRequest, y_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::AutoCrossHairCalStateRequest, ts_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, in_progress_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, progress_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::crosshair::AutoCrossHairCalStateResponse, failed_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::crosshair::CrosshairPosition)},
  { 8, -1, -1, sizeof(::carbon::frontend::crosshair::CrosshairPositionState)},
  { 18, -1, -1, sizeof(::carbon::frontend::crosshair::CrosshairPositionRequest)},
  { 26, -1, -1, sizeof(::carbon::frontend::crosshair::SetCrosshairPositionRequest)},
  { 34, -1, -1, sizeof(::carbon::frontend::crosshair::MoveScannerRequest)},
  { 43, -1, -1, sizeof(::carbon::frontend::crosshair::AutoCrossHairCalStateRequest)},
  { 50, -1, -1, sizeof(::carbon::frontend::crosshair::AutoCrossHairCalStateResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::crosshair::_CrosshairPosition_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::crosshair::_CrosshairPositionState_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::crosshair::_CrosshairPositionRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::crosshair::_SetCrosshairPositionRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::crosshair::_MoveScannerRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::crosshair::_AutoCrossHairCalStateRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::crosshair::_AutoCrossHairCalStateResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fcrosshair_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\036frontend/proto/crosshair.proto\022\031carbon"
  ".frontend.crosshair\032\033frontend/proto/came"
  "ra.proto\032\031frontend/proto/util.proto\")\n\021C"
  "rosshairPosition\022\t\n\001x\030\001 \001(\002\022\t\n\001y\030\002 \001(\002\"\261"
  "\001\n\026CrosshairPositionState\022+\n\002ts\030\001 \001(\0132\037."
  "carbon.frontend.util.Timestamp\0229\n\003pos\030\002 "
  "\001(\0132,.carbon.frontend.crosshair.Crosshai"
  "rPosition\022\023\n\013calibrating\030\003 \001(\010\022\032\n\022calibr"
  "ation_failed\030\004 \001(\010\"W\n\030CrosshairPositionR"
  "equest\022\016\n\006cam_id\030\001 \001(\t\022+\n\002ts\030\002 \001(\0132\037.car"
  "bon.frontend.util.Timestamp\"h\n\033SetCrossh"
  "airPositionRequest\022\016\n\006cam_id\030\001 \001(\t\0229\n\003po"
  "s\030\002 \001(\0132,.carbon.frontend.crosshair.Cros"
  "shairPosition\":\n\022MoveScannerRequest\022\016\n\006c"
  "am_id\030\001 \001(\t\022\t\n\001x\030\002 \001(\002\022\t\n\001y\030\003 \001(\002\"K\n\034Aut"
  "oCrossHairCalStateRequest\022+\n\002ts\030\001 \001(\0132\037."
  "carbon.frontend.util.Timestamp\"\203\001\n\035AutoC"
  "rossHairCalStateResponse\022+\n\002ts\030\001 \001(\0132\037.c"
  "arbon.frontend.util.Timestamp\022\023\n\013in_prog"
  "ress\030\002 \001(\010\022\020\n\010progress\030\003 \001(\002\022\016\n\006failed\030\004"
  " \003(\t2\376\005\n\020CrosshairService\022a\n\033StartAutoCa"
  "librateCrosshair\022%.carbon.frontend.camer"
  "a.CameraRequest\032\033.carbon.frontend.util.E"
  "mpty\022[\n\037StartAutoCalibrateAllCrosshairs\022"
  "\033.carbon.frontend.util.Empty\032\033.carbon.fr"
  "ontend.util.Empty\022M\n\021StopAutoCalibrate\022\033"
  ".carbon.frontend.util.Empty\032\033.carbon.fro"
  "ntend.util.Empty\022\177\n\025GetNextCrosshairStat"
  "e\0223.carbon.frontend.crosshair.CrosshairP"
  "ositionRequest\0321.carbon.frontend.crossha"
  "ir.CrosshairPositionState\022k\n\024SetCrosshai"
  "rPosition\0226.carbon.frontend.crosshair.Se"
  "tCrosshairPositionRequest\032\033.carbon.front"
  "end.util.Empty\022Y\n\013MoveScanner\022-.carbon.f"
  "rontend.crosshair.MoveScannerRequest\032\033.c"
  "arbon.frontend.util.Empty\022\221\001\n\034GetNextAut"
  "oCrossHairCalState\0227.carbon.frontend.cro"
  "sshair.AutoCrossHairCalStateRequest\0328.ca"
  "rbon.frontend.crosshair.AutoCrossHairCal"
  "StateResponseB\020Z\016proto/frontendb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fcrosshair_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2fcamera_2eproto,
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fcrosshair_2eproto = {
  false, false, 1599, descriptor_table_protodef_frontend_2fproto_2fcrosshair_2eproto, "frontend/proto/crosshair.proto", 
  &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once, descriptor_table_frontend_2fproto_2fcrosshair_2eproto_deps, 2, 7,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fcrosshair_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fcrosshair_2eproto, file_level_enum_descriptors_frontend_2fproto_2fcrosshair_2eproto, file_level_service_descriptors_frontend_2fproto_2fcrosshair_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fcrosshair_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fcrosshair_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fcrosshair_2eproto(&descriptor_table_frontend_2fproto_2fcrosshair_2eproto);
namespace carbon {
namespace frontend {
namespace crosshair {

// ===================================================================

class CrosshairPosition::_Internal {
 public:
};

CrosshairPosition::CrosshairPosition(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.crosshair.CrosshairPosition)
}
CrosshairPosition::CrosshairPosition(const CrosshairPosition& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&y_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.crosshair.CrosshairPosition)
}

inline void CrosshairPosition::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&y_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_));
}

CrosshairPosition::~CrosshairPosition() {
  // @@protoc_insertion_point(destructor:carbon.frontend.crosshair.CrosshairPosition)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CrosshairPosition::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void CrosshairPosition::ArenaDtor(void* object) {
  CrosshairPosition* _this = reinterpret_cast< CrosshairPosition* >(object);
  (void)_this;
}
void CrosshairPosition::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CrosshairPosition::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CrosshairPosition::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.crosshair.CrosshairPosition)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CrosshairPosition::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CrosshairPosition::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.crosshair.CrosshairPosition)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.crosshair.CrosshairPosition)
  return target;
}

size_t CrosshairPosition::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.crosshair.CrosshairPosition)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CrosshairPosition::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CrosshairPosition::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CrosshairPosition::GetClassData() const { return &_class_data_; }

void CrosshairPosition::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CrosshairPosition *>(to)->MergeFrom(
      static_cast<const CrosshairPosition &>(from));
}


void CrosshairPosition::MergeFrom(const CrosshairPosition& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.crosshair.CrosshairPosition)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _internal_set_y(from._internal_y());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CrosshairPosition::CopyFrom(const CrosshairPosition& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.crosshair.CrosshairPosition)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CrosshairPosition::IsInitialized() const {
  return true;
}

void CrosshairPosition::InternalSwap(CrosshairPosition* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CrosshairPosition, y_)
      + sizeof(CrosshairPosition::y_)
      - PROTOBUF_FIELD_OFFSET(CrosshairPosition, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CrosshairPosition::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_getter, &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcrosshair_2eproto[0]);
}

// ===================================================================

class CrosshairPositionState::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const CrosshairPositionState* msg);
  static const ::carbon::frontend::crosshair::CrosshairPosition& pos(const CrosshairPositionState* msg);
};

const ::carbon::frontend::util::Timestamp&
CrosshairPositionState::_Internal::ts(const CrosshairPositionState* msg) {
  return *msg->ts_;
}
const ::carbon::frontend::crosshair::CrosshairPosition&
CrosshairPositionState::_Internal::pos(const CrosshairPositionState* msg) {
  return *msg->pos_;
}
void CrosshairPositionState::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
CrosshairPositionState::CrosshairPositionState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.crosshair.CrosshairPositionState)
}
CrosshairPositionState::CrosshairPositionState(const CrosshairPositionState& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  if (from._internal_has_pos()) {
    pos_ = new ::carbon::frontend::crosshair::CrosshairPosition(*from.pos_);
  } else {
    pos_ = nullptr;
  }
  ::memcpy(&calibrating_, &from.calibrating_,
    static_cast<size_t>(reinterpret_cast<char*>(&calibration_failed_) -
    reinterpret_cast<char*>(&calibrating_)) + sizeof(calibration_failed_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.crosshair.CrosshairPositionState)
}

inline void CrosshairPositionState::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&calibration_failed_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(calibration_failed_));
}

CrosshairPositionState::~CrosshairPositionState() {
  // @@protoc_insertion_point(destructor:carbon.frontend.crosshair.CrosshairPositionState)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CrosshairPositionState::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
  if (this != internal_default_instance()) delete pos_;
}

void CrosshairPositionState::ArenaDtor(void* object) {
  CrosshairPositionState* _this = reinterpret_cast< CrosshairPositionState* >(object);
  (void)_this;
}
void CrosshairPositionState::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CrosshairPositionState::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CrosshairPositionState::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.crosshair.CrosshairPositionState)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  if (GetArenaForAllocation() == nullptr && pos_ != nullptr) {
    delete pos_;
  }
  pos_ = nullptr;
  ::memset(&calibrating_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&calibration_failed_) -
      reinterpret_cast<char*>(&calibrating_)) + sizeof(calibration_failed_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CrosshairPositionState::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.crosshair.CrosshairPosition pos = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_pos(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool calibrating = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          calibrating_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool calibration_failed = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          calibration_failed_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CrosshairPositionState::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.crosshair.CrosshairPositionState)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // .carbon.frontend.crosshair.CrosshairPosition pos = 2;
  if (this->_internal_has_pos()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::pos(this), target, stream);
  }

  // bool calibrating = 3;
  if (this->_internal_calibrating() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_calibrating(), target);
  }

  // bool calibration_failed = 4;
  if (this->_internal_calibration_failed() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(4, this->_internal_calibration_failed(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.crosshair.CrosshairPositionState)
  return target;
}

size_t CrosshairPositionState::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.crosshair.CrosshairPositionState)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // .carbon.frontend.crosshair.CrosshairPosition pos = 2;
  if (this->_internal_has_pos()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *pos_);
  }

  // bool calibrating = 3;
  if (this->_internal_calibrating() != 0) {
    total_size += 1 + 1;
  }

  // bool calibration_failed = 4;
  if (this->_internal_calibration_failed() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CrosshairPositionState::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CrosshairPositionState::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CrosshairPositionState::GetClassData() const { return &_class_data_; }

void CrosshairPositionState::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CrosshairPositionState *>(to)->MergeFrom(
      static_cast<const CrosshairPositionState &>(from));
}


void CrosshairPositionState::MergeFrom(const CrosshairPositionState& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.crosshair.CrosshairPositionState)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_has_pos()) {
    _internal_mutable_pos()->::carbon::frontend::crosshair::CrosshairPosition::MergeFrom(from._internal_pos());
  }
  if (from._internal_calibrating() != 0) {
    _internal_set_calibrating(from._internal_calibrating());
  }
  if (from._internal_calibration_failed() != 0) {
    _internal_set_calibration_failed(from._internal_calibration_failed());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CrosshairPositionState::CopyFrom(const CrosshairPositionState& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.crosshair.CrosshairPositionState)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CrosshairPositionState::IsInitialized() const {
  return true;
}

void CrosshairPositionState::InternalSwap(CrosshairPositionState* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(CrosshairPositionState, calibration_failed_)
      + sizeof(CrosshairPositionState::calibration_failed_)
      - PROTOBUF_FIELD_OFFSET(CrosshairPositionState, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata CrosshairPositionState::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_getter, &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcrosshair_2eproto[1]);
}

// ===================================================================

class CrosshairPositionRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const CrosshairPositionRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
CrosshairPositionRequest::_Internal::ts(const CrosshairPositionRequest* msg) {
  return *msg->ts_;
}
void CrosshairPositionRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
CrosshairPositionRequest::CrosshairPositionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.crosshair.CrosshairPositionRequest)
}
CrosshairPositionRequest::CrosshairPositionRequest(const CrosshairPositionRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.crosshair.CrosshairPositionRequest)
}

inline void CrosshairPositionRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

CrosshairPositionRequest::~CrosshairPositionRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.crosshair.CrosshairPositionRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void CrosshairPositionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void CrosshairPositionRequest::ArenaDtor(void* object) {
  CrosshairPositionRequest* _this = reinterpret_cast< CrosshairPositionRequest* >(object);
  (void)_this;
}
void CrosshairPositionRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void CrosshairPositionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void CrosshairPositionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.crosshair.CrosshairPositionRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* CrosshairPositionRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.crosshair.CrosshairPositionRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.util.Timestamp ts = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* CrosshairPositionRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.crosshair.CrosshairPositionRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.crosshair.CrosshairPositionRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.crosshair.CrosshairPositionRequest)
  return target;
}

size_t CrosshairPositionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.crosshair.CrosshairPositionRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // .carbon.frontend.util.Timestamp ts = 2;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData CrosshairPositionRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    CrosshairPositionRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*CrosshairPositionRequest::GetClassData() const { return &_class_data_; }

void CrosshairPositionRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<CrosshairPositionRequest *>(to)->MergeFrom(
      static_cast<const CrosshairPositionRequest &>(from));
}


void CrosshairPositionRequest::MergeFrom(const CrosshairPositionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.crosshair.CrosshairPositionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void CrosshairPositionRequest::CopyFrom(const CrosshairPositionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.crosshair.CrosshairPositionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool CrosshairPositionRequest::IsInitialized() const {
  return true;
}

void CrosshairPositionRequest::InternalSwap(CrosshairPositionRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata CrosshairPositionRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_getter, &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcrosshair_2eproto[2]);
}

// ===================================================================

class SetCrosshairPositionRequest::_Internal {
 public:
  static const ::carbon::frontend::crosshair::CrosshairPosition& pos(const SetCrosshairPositionRequest* msg);
};

const ::carbon::frontend::crosshair::CrosshairPosition&
SetCrosshairPositionRequest::_Internal::pos(const SetCrosshairPositionRequest* msg) {
  return *msg->pos_;
}
SetCrosshairPositionRequest::SetCrosshairPositionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.crosshair.SetCrosshairPositionRequest)
}
SetCrosshairPositionRequest::SetCrosshairPositionRequest(const SetCrosshairPositionRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_pos()) {
    pos_ = new ::carbon::frontend::crosshair::CrosshairPosition(*from.pos_);
  } else {
    pos_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.crosshair.SetCrosshairPositionRequest)
}

inline void SetCrosshairPositionRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
pos_ = nullptr;
}

SetCrosshairPositionRequest::~SetCrosshairPositionRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.crosshair.SetCrosshairPositionRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetCrosshairPositionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete pos_;
}

void SetCrosshairPositionRequest::ArenaDtor(void* object) {
  SetCrosshairPositionRequest* _this = reinterpret_cast< SetCrosshairPositionRequest* >(object);
  (void)_this;
}
void SetCrosshairPositionRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetCrosshairPositionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetCrosshairPositionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.crosshair.SetCrosshairPositionRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && pos_ != nullptr) {
    delete pos_;
  }
  pos_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetCrosshairPositionRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.crosshair.SetCrosshairPositionRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.crosshair.CrosshairPosition pos = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_pos(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetCrosshairPositionRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.crosshair.SetCrosshairPositionRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.crosshair.SetCrosshairPositionRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  // .carbon.frontend.crosshair.CrosshairPosition pos = 2;
  if (this->_internal_has_pos()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::pos(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.crosshair.SetCrosshairPositionRequest)
  return target;
}

size_t SetCrosshairPositionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.crosshair.SetCrosshairPositionRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // .carbon.frontend.crosshair.CrosshairPosition pos = 2;
  if (this->_internal_has_pos()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *pos_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetCrosshairPositionRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetCrosshairPositionRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetCrosshairPositionRequest::GetClassData() const { return &_class_data_; }

void SetCrosshairPositionRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetCrosshairPositionRequest *>(to)->MergeFrom(
      static_cast<const SetCrosshairPositionRequest &>(from));
}


void SetCrosshairPositionRequest::MergeFrom(const SetCrosshairPositionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.crosshair.SetCrosshairPositionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  if (from._internal_has_pos()) {
    _internal_mutable_pos()->::carbon::frontend::crosshair::CrosshairPosition::MergeFrom(from._internal_pos());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetCrosshairPositionRequest::CopyFrom(const SetCrosshairPositionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.crosshair.SetCrosshairPositionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetCrosshairPositionRequest::IsInitialized() const {
  return true;
}

void SetCrosshairPositionRequest::InternalSwap(SetCrosshairPositionRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  swap(pos_, other->pos_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetCrosshairPositionRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_getter, &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcrosshair_2eproto[3]);
}

// ===================================================================

class MoveScannerRequest::_Internal {
 public:
};

MoveScannerRequest::MoveScannerRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.crosshair.MoveScannerRequest)
}
MoveScannerRequest::MoveScannerRequest(const MoveScannerRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_cam_id().empty()) {
    cam_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_cam_id(), 
      GetArenaForAllocation());
  }
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&y_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.crosshair.MoveScannerRequest)
}

inline void MoveScannerRequest::SharedCtor() {
cam_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  cam_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&y_) -
    reinterpret_cast<char*>(&x_)) + sizeof(y_));
}

MoveScannerRequest::~MoveScannerRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.crosshair.MoveScannerRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void MoveScannerRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  cam_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void MoveScannerRequest::ArenaDtor(void* object) {
  MoveScannerRequest* _this = reinterpret_cast< MoveScannerRequest* >(object);
  (void)_this;
}
void MoveScannerRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void MoveScannerRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void MoveScannerRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.crosshair.MoveScannerRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  cam_id_.ClearToEmpty();
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&y_) -
      reinterpret_cast<char*>(&x_)) + sizeof(y_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* MoveScannerRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string cam_id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_cam_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.crosshair.MoveScannerRequest.cam_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float x = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* MoveScannerRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.crosshair.MoveScannerRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_cam_id().data(), static_cast<int>(this->_internal_cam_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.crosshair.MoveScannerRequest.cam_id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_cam_id(), target);
  }

  // float x = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_x(), target);
  }

  // float y = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_y(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.crosshair.MoveScannerRequest)
  return target;
}

size_t MoveScannerRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.crosshair.MoveScannerRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string cam_id = 1;
  if (!this->_internal_cam_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_cam_id());
  }

  // float x = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData MoveScannerRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    MoveScannerRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*MoveScannerRequest::GetClassData() const { return &_class_data_; }

void MoveScannerRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<MoveScannerRequest *>(to)->MergeFrom(
      static_cast<const MoveScannerRequest &>(from));
}


void MoveScannerRequest::MergeFrom(const MoveScannerRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.crosshair.MoveScannerRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_cam_id().empty()) {
    _internal_set_cam_id(from._internal_cam_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _internal_set_y(from._internal_y());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void MoveScannerRequest::CopyFrom(const MoveScannerRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.crosshair.MoveScannerRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool MoveScannerRequest::IsInitialized() const {
  return true;
}

void MoveScannerRequest::InternalSwap(MoveScannerRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &cam_id_, lhs_arena,
      &other->cam_id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(MoveScannerRequest, y_)
      + sizeof(MoveScannerRequest::y_)
      - PROTOBUF_FIELD_OFFSET(MoveScannerRequest, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata MoveScannerRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_getter, &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcrosshair_2eproto[4]);
}

// ===================================================================

class AutoCrossHairCalStateRequest::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const AutoCrossHairCalStateRequest* msg);
};

const ::carbon::frontend::util::Timestamp&
AutoCrossHairCalStateRequest::_Internal::ts(const AutoCrossHairCalStateRequest* msg) {
  return *msg->ts_;
}
void AutoCrossHairCalStateRequest::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
AutoCrossHairCalStateRequest::AutoCrossHairCalStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
}
AutoCrossHairCalStateRequest::AutoCrossHairCalStateRequest(const AutoCrossHairCalStateRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
}

inline void AutoCrossHairCalStateRequest::SharedCtor() {
ts_ = nullptr;
}

AutoCrossHairCalStateRequest::~AutoCrossHairCalStateRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AutoCrossHairCalStateRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void AutoCrossHairCalStateRequest::ArenaDtor(void* object) {
  AutoCrossHairCalStateRequest* _this = reinterpret_cast< AutoCrossHairCalStateRequest* >(object);
  (void)_this;
}
void AutoCrossHairCalStateRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AutoCrossHairCalStateRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AutoCrossHairCalStateRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AutoCrossHairCalStateRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AutoCrossHairCalStateRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
  return target;
}

size_t AutoCrossHairCalStateRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AutoCrossHairCalStateRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AutoCrossHairCalStateRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AutoCrossHairCalStateRequest::GetClassData() const { return &_class_data_; }

void AutoCrossHairCalStateRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AutoCrossHairCalStateRequest *>(to)->MergeFrom(
      static_cast<const AutoCrossHairCalStateRequest &>(from));
}


void AutoCrossHairCalStateRequest::MergeFrom(const AutoCrossHairCalStateRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AutoCrossHairCalStateRequest::CopyFrom(const AutoCrossHairCalStateRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.crosshair.AutoCrossHairCalStateRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AutoCrossHairCalStateRequest::IsInitialized() const {
  return true;
}

void AutoCrossHairCalStateRequest::InternalSwap(AutoCrossHairCalStateRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata AutoCrossHairCalStateRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_getter, &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcrosshair_2eproto[5]);
}

// ===================================================================

class AutoCrossHairCalStateResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const AutoCrossHairCalStateResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
AutoCrossHairCalStateResponse::_Internal::ts(const AutoCrossHairCalStateResponse* msg) {
  return *msg->ts_;
}
void AutoCrossHairCalStateResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
AutoCrossHairCalStateResponse::AutoCrossHairCalStateResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  failed_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
}
AutoCrossHairCalStateResponse::AutoCrossHairCalStateResponse(const AutoCrossHairCalStateResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      failed_(from.failed_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  ::memcpy(&in_progress_, &from.in_progress_,
    static_cast<size_t>(reinterpret_cast<char*>(&progress_) -
    reinterpret_cast<char*>(&in_progress_)) + sizeof(progress_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
}

inline void AutoCrossHairCalStateResponse::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&ts_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&progress_) -
    reinterpret_cast<char*>(&ts_)) + sizeof(progress_));
}

AutoCrossHairCalStateResponse::~AutoCrossHairCalStateResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AutoCrossHairCalStateResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete ts_;
}

void AutoCrossHairCalStateResponse::ArenaDtor(void* object) {
  AutoCrossHairCalStateResponse* _this = reinterpret_cast< AutoCrossHairCalStateResponse* >(object);
  (void)_this;
}
void AutoCrossHairCalStateResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AutoCrossHairCalStateResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AutoCrossHairCalStateResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  failed_.Clear();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  ::memset(&in_progress_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&progress_) -
      reinterpret_cast<char*>(&in_progress_)) + sizeof(progress_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AutoCrossHairCalStateResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool in_progress = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          in_progress_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float progress = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          progress_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated string failed = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_failed();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AutoCrossHairCalStateResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // bool in_progress = 2;
  if (this->_internal_in_progress() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_in_progress(), target);
  }

  // float progress = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_progress = this->_internal_progress();
  uint32_t raw_progress;
  memcpy(&raw_progress, &tmp_progress, sizeof(tmp_progress));
  if (raw_progress != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_progress(), target);
  }

  // repeated string failed = 4;
  for (int i = 0, n = this->_internal_failed_size(); i < n; i++) {
    const auto& s = this->_internal_failed(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.crosshair.AutoCrossHairCalStateResponse.failed");
    target = stream->WriteString(4, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
  return target;
}

size_t AutoCrossHairCalStateResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string failed = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(failed_.size());
  for (int i = 0, n = failed_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      failed_.Get(i));
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  // bool in_progress = 2;
  if (this->_internal_in_progress() != 0) {
    total_size += 1 + 1;
  }

  // float progress = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_progress = this->_internal_progress();
  uint32_t raw_progress;
  memcpy(&raw_progress, &tmp_progress, sizeof(tmp_progress));
  if (raw_progress != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AutoCrossHairCalStateResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AutoCrossHairCalStateResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AutoCrossHairCalStateResponse::GetClassData() const { return &_class_data_; }

void AutoCrossHairCalStateResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AutoCrossHairCalStateResponse *>(to)->MergeFrom(
      static_cast<const AutoCrossHairCalStateResponse &>(from));
}


void AutoCrossHairCalStateResponse::MergeFrom(const AutoCrossHairCalStateResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  failed_.MergeFrom(from.failed_);
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  if (from._internal_in_progress() != 0) {
    _internal_set_in_progress(from._internal_in_progress());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_progress = from._internal_progress();
  uint32_t raw_progress;
  memcpy(&raw_progress, &tmp_progress, sizeof(tmp_progress));
  if (raw_progress != 0) {
    _internal_set_progress(from._internal_progress());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AutoCrossHairCalStateResponse::CopyFrom(const AutoCrossHairCalStateResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.crosshair.AutoCrossHairCalStateResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AutoCrossHairCalStateResponse::IsInitialized() const {
  return true;
}

void AutoCrossHairCalStateResponse::InternalSwap(AutoCrossHairCalStateResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  failed_.InternalSwap(&other->failed_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(AutoCrossHairCalStateResponse, progress_)
      + sizeof(AutoCrossHairCalStateResponse::progress_)
      - PROTOBUF_FIELD_OFFSET(AutoCrossHairCalStateResponse, ts_)>(
          reinterpret_cast<char*>(&ts_),
          reinterpret_cast<char*>(&other->ts_));
}

::PROTOBUF_NAMESPACE_ID::Metadata AutoCrossHairCalStateResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_getter, &descriptor_table_frontend_2fproto_2fcrosshair_2eproto_once,
      file_level_metadata_frontend_2fproto_2fcrosshair_2eproto[6]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace crosshair
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::crosshair::CrosshairPosition* Arena::CreateMaybeMessage< ::carbon::frontend::crosshair::CrosshairPosition >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::crosshair::CrosshairPosition >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::crosshair::CrosshairPositionState* Arena::CreateMaybeMessage< ::carbon::frontend::crosshair::CrosshairPositionState >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::crosshair::CrosshairPositionState >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::crosshair::CrosshairPositionRequest* Arena::CreateMaybeMessage< ::carbon::frontend::crosshair::CrosshairPositionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::crosshair::CrosshairPositionRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::crosshair::SetCrosshairPositionRequest* Arena::CreateMaybeMessage< ::carbon::frontend::crosshair::SetCrosshairPositionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::crosshair::SetCrosshairPositionRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::crosshair::MoveScannerRequest* Arena::CreateMaybeMessage< ::carbon::frontend::crosshair::MoveScannerRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::crosshair::MoveScannerRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest* Arena::CreateMaybeMessage< ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::crosshair::AutoCrossHairCalStateRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse* Arena::CreateMaybeMessage< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::crosshair::AutoCrossHairCalStateResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

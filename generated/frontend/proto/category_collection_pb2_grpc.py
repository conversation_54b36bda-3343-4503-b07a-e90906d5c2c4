# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.frontend.proto import category_collection_pb2 as frontend_dot_proto_dot_category__collection__pb2
from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2


class CategoryCollectionServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetNextCategoryCollectionsData = channel.unary_unary(
                '/carbon.frontend.category_collection.CategoryCollectionService/GetNextCategoryCollectionsData',
                request_serializer=frontend_dot_proto_dot_category__collection__pb2.GetNextCategoryCollectionsDataRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_category__collection__pb2.GetNextCategoryCollectionsDataResponse.FromString,
                )
        self.GetNextActiveCategoryCollectionId = channel.unary_unary(
                '/carbon.frontend.category_collection.CategoryCollectionService/GetNextActiveCategoryCollectionId',
                request_serializer=frontend_dot_proto_dot_category__collection__pb2.GetNextActiveCategoryCollectionIdRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_category__collection__pb2.GetNextActiveCategoryCollectionIdResponse.FromString,
                )
        self.SetActiveCategoryCollectionId = channel.unary_unary(
                '/carbon.frontend.category_collection.CategoryCollectionService/SetActiveCategoryCollectionId',
                request_serializer=frontend_dot_proto_dot_category__collection__pb2.SetActiveCategoryCollectionIdRequest.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.ReloadCategoryCollection = channel.unary_unary(
                '/carbon.frontend.category_collection.CategoryCollectionService/ReloadCategoryCollection',
                request_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
                response_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                )


class CategoryCollectionServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetNextCategoryCollectionsData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetNextActiveCategoryCollectionId(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetActiveCategoryCollectionId(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def ReloadCategoryCollection(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_CategoryCollectionServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetNextCategoryCollectionsData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextCategoryCollectionsData,
                    request_deserializer=frontend_dot_proto_dot_category__collection__pb2.GetNextCategoryCollectionsDataRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_category__collection__pb2.GetNextCategoryCollectionsDataResponse.SerializeToString,
            ),
            'GetNextActiveCategoryCollectionId': grpc.unary_unary_rpc_method_handler(
                    servicer.GetNextActiveCategoryCollectionId,
                    request_deserializer=frontend_dot_proto_dot_category__collection__pb2.GetNextActiveCategoryCollectionIdRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_category__collection__pb2.GetNextActiveCategoryCollectionIdResponse.SerializeToString,
            ),
            'SetActiveCategoryCollectionId': grpc.unary_unary_rpc_method_handler(
                    servicer.SetActiveCategoryCollectionId,
                    request_deserializer=frontend_dot_proto_dot_category__collection__pb2.SetActiveCategoryCollectionIdRequest.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'ReloadCategoryCollection': grpc.unary_unary_rpc_method_handler(
                    servicer.ReloadCategoryCollection,
                    request_deserializer=frontend_dot_proto_dot_util__pb2.Empty.FromString,
                    response_serializer=frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.frontend.category_collection.CategoryCollectionService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class CategoryCollectionService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetNextCategoryCollectionsData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.category_collection.CategoryCollectionService/GetNextCategoryCollectionsData',
            frontend_dot_proto_dot_category__collection__pb2.GetNextCategoryCollectionsDataRequest.SerializeToString,
            frontend_dot_proto_dot_category__collection__pb2.GetNextCategoryCollectionsDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetNextActiveCategoryCollectionId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.category_collection.CategoryCollectionService/GetNextActiveCategoryCollectionId',
            frontend_dot_proto_dot_category__collection__pb2.GetNextActiveCategoryCollectionIdRequest.SerializeToString,
            frontend_dot_proto_dot_category__collection__pb2.GetNextActiveCategoryCollectionIdResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetActiveCategoryCollectionId(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.category_collection.CategoryCollectionService/SetActiveCategoryCollectionId',
            frontend_dot_proto_dot_category__collection__pb2.SetActiveCategoryCollectionIdRequest.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def ReloadCategoryCollection(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.frontend.category_collection.CategoryCollectionService/ReloadCategoryCollection',
            frontend_dot_proto_dot_util__pb2.Empty.SerializeToString,
            frontend_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

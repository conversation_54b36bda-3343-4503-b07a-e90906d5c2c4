// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/thinning.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fthinning_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fthinning_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
#include "proto/thinning/thinning.pb.h"
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fthinning_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fthinning_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fthinning_2eproto;
namespace carbon {
namespace frontend {
namespace thinning {
class DefineConfigurationRequest;
struct DefineConfigurationRequestDefaultTypeInternal;
extern DefineConfigurationRequestDefaultTypeInternal _DefineConfigurationRequest_default_instance_;
class DefineConfigurationResponse;
struct DefineConfigurationResponseDefaultTypeInternal;
extern DefineConfigurationResponseDefaultTypeInternal _DefineConfigurationResponse_default_instance_;
class DeleteConfigRequest;
struct DeleteConfigRequestDefaultTypeInternal;
extern DeleteConfigRequestDefaultTypeInternal _DeleteConfigRequest_default_instance_;
class DeleteConfigResponse;
struct DeleteConfigResponseDefaultTypeInternal;
extern DeleteConfigResponseDefaultTypeInternal _DeleteConfigResponse_default_instance_;
class GetNextActiveConfResponse;
struct GetNextActiveConfResponseDefaultTypeInternal;
extern GetNextActiveConfResponseDefaultTypeInternal _GetNextActiveConfResponse_default_instance_;
class GetNextConfigurationsResponse;
struct GetNextConfigurationsResponseDefaultTypeInternal;
extern GetNextConfigurationsResponseDefaultTypeInternal _GetNextConfigurationsResponse_default_instance_;
class SetActiveConfigRequest;
struct SetActiveConfigRequestDefaultTypeInternal;
extern SetActiveConfigRequestDefaultTypeInternal _SetActiveConfigRequest_default_instance_;
class SetActiveConfigResponse;
struct SetActiveConfigResponseDefaultTypeInternal;
extern SetActiveConfigResponseDefaultTypeInternal _SetActiveConfigResponse_default_instance_;
}  // namespace thinning
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::thinning::DefineConfigurationRequest* Arena::CreateMaybeMessage<::carbon::frontend::thinning::DefineConfigurationRequest>(Arena*);
template<> ::carbon::frontend::thinning::DefineConfigurationResponse* Arena::CreateMaybeMessage<::carbon::frontend::thinning::DefineConfigurationResponse>(Arena*);
template<> ::carbon::frontend::thinning::DeleteConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::thinning::DeleteConfigRequest>(Arena*);
template<> ::carbon::frontend::thinning::DeleteConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::thinning::DeleteConfigResponse>(Arena*);
template<> ::carbon::frontend::thinning::GetNextActiveConfResponse* Arena::CreateMaybeMessage<::carbon::frontend::thinning::GetNextActiveConfResponse>(Arena*);
template<> ::carbon::frontend::thinning::GetNextConfigurationsResponse* Arena::CreateMaybeMessage<::carbon::frontend::thinning::GetNextConfigurationsResponse>(Arena*);
template<> ::carbon::frontend::thinning::SetActiveConfigRequest* Arena::CreateMaybeMessage<::carbon::frontend::thinning::SetActiveConfigRequest>(Arena*);
template<> ::carbon::frontend::thinning::SetActiveConfigResponse* Arena::CreateMaybeMessage<::carbon::frontend::thinning::SetActiveConfigResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace thinning {

enum ThinningConfVer : int {
  THIN_CONF_V1 = 0,
  THIN_CONF_V2 = 1,
  ThinningConfVer_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ThinningConfVer_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ThinningConfVer_IsValid(int value);
constexpr ThinningConfVer ThinningConfVer_MIN = THIN_CONF_V1;
constexpr ThinningConfVer ThinningConfVer_MAX = THIN_CONF_V2;
constexpr int ThinningConfVer_ARRAYSIZE = ThinningConfVer_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ThinningConfVer_descriptor();
template<typename T>
inline const std::string& ThinningConfVer_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ThinningConfVer>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ThinningConfVer_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ThinningConfVer_descriptor(), enum_t_value);
}
inline bool ThinningConfVer_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ThinningConfVer* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ThinningConfVer>(
    ThinningConfVer_descriptor(), name, value);
}
// ===================================================================

class GetNextConfigurationsResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.thinning.GetNextConfigurationsResponse) */ {
 public:
  inline GetNextConfigurationsResponse() : GetNextConfigurationsResponse(nullptr) {}
  ~GetNextConfigurationsResponse() override;
  explicit constexpr GetNextConfigurationsResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextConfigurationsResponse(const GetNextConfigurationsResponse& from);
  GetNextConfigurationsResponse(GetNextConfigurationsResponse&& from) noexcept
    : GetNextConfigurationsResponse() {
    *this = ::std::move(from);
  }

  inline GetNextConfigurationsResponse& operator=(const GetNextConfigurationsResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextConfigurationsResponse& operator=(GetNextConfigurationsResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextConfigurationsResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextConfigurationsResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextConfigurationsResponse*>(
               &_GetNextConfigurationsResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetNextConfigurationsResponse& a, GetNextConfigurationsResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextConfigurationsResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextConfigurationsResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextConfigurationsResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextConfigurationsResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextConfigurationsResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextConfigurationsResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextConfigurationsResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.thinning.GetNextConfigurationsResponse";
  }
  protected:
  explicit GetNextConfigurationsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDefinitionsFieldNumber = 2,
    kActiveIdFieldNumber = 3,
    kTsFieldNumber = 1,
  };
  // repeated .carbon.thinning.ConfigDefinition definitions = 2;
  int definitions_size() const;
  private:
  int _internal_definitions_size() const;
  public:
  void clear_definitions();
  ::carbon::thinning::ConfigDefinition* mutable_definitions(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::thinning::ConfigDefinition >*
      mutable_definitions();
  private:
  const ::carbon::thinning::ConfigDefinition& _internal_definitions(int index) const;
  ::carbon::thinning::ConfigDefinition* _internal_add_definitions();
  public:
  const ::carbon::thinning::ConfigDefinition& definitions(int index) const;
  ::carbon::thinning::ConfigDefinition* add_definitions();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::thinning::ConfigDefinition >&
      definitions() const;

  // string active_id = 3;
  void clear_active_id();
  const std::string& active_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_active_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_active_id();
  PROTOBUF_NODISCARD std::string* release_active_id();
  void set_allocated_active_id(std::string* active_id);
  private:
  const std::string& _internal_active_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_active_id(const std::string& value);
  std::string* _internal_mutable_active_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.thinning.GetNextConfigurationsResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::thinning::ConfigDefinition > definitions_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr active_id_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class GetNextActiveConfResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.thinning.GetNextActiveConfResponse) */ {
 public:
  inline GetNextActiveConfResponse() : GetNextActiveConfResponse(nullptr) {}
  ~GetNextActiveConfResponse() override;
  explicit constexpr GetNextActiveConfResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetNextActiveConfResponse(const GetNextActiveConfResponse& from);
  GetNextActiveConfResponse(GetNextActiveConfResponse&& from) noexcept
    : GetNextActiveConfResponse() {
    *this = ::std::move(from);
  }

  inline GetNextActiveConfResponse& operator=(const GetNextActiveConfResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetNextActiveConfResponse& operator=(GetNextActiveConfResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetNextActiveConfResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetNextActiveConfResponse* internal_default_instance() {
    return reinterpret_cast<const GetNextActiveConfResponse*>(
               &_GetNextActiveConfResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(GetNextActiveConfResponse& a, GetNextActiveConfResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetNextActiveConfResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetNextActiveConfResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetNextActiveConfResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetNextActiveConfResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetNextActiveConfResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetNextActiveConfResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetNextActiveConfResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.thinning.GetNextActiveConfResponse";
  }
  protected:
  explicit GetNextActiveConfResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 2,
    kIdFieldNumber = 3,
    kTsFieldNumber = 1,
  };
  // string name = 2 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_name();
  PROTOBUF_DEPRECATED const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_name(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_name();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_name();
  PROTOBUF_DEPRECATED void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string id = 3;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // @@protoc_insertion_point(class_scope:carbon.frontend.thinning.GetNextActiveConfResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::carbon::frontend::util::Timestamp* ts_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class DefineConfigurationRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.thinning.DefineConfigurationRequest) */ {
 public:
  inline DefineConfigurationRequest() : DefineConfigurationRequest(nullptr) {}
  ~DefineConfigurationRequest() override;
  explicit constexpr DefineConfigurationRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DefineConfigurationRequest(const DefineConfigurationRequest& from);
  DefineConfigurationRequest(DefineConfigurationRequest&& from) noexcept
    : DefineConfigurationRequest() {
    *this = ::std::move(from);
  }

  inline DefineConfigurationRequest& operator=(const DefineConfigurationRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DefineConfigurationRequest& operator=(DefineConfigurationRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DefineConfigurationRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DefineConfigurationRequest* internal_default_instance() {
    return reinterpret_cast<const DefineConfigurationRequest*>(
               &_DefineConfigurationRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(DefineConfigurationRequest& a, DefineConfigurationRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DefineConfigurationRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DefineConfigurationRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DefineConfigurationRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DefineConfigurationRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DefineConfigurationRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DefineConfigurationRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DefineConfigurationRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.thinning.DefineConfigurationRequest";
  }
  protected:
  explicit DefineConfigurationRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kDefinitionFieldNumber = 1,
    kSetActiveFieldNumber = 2,
    kVerFieldNumber = 3,
  };
  // .carbon.thinning.ConfigDefinition definition = 1;
  bool has_definition() const;
  private:
  bool _internal_has_definition() const;
  public:
  void clear_definition();
  const ::carbon::thinning::ConfigDefinition& definition() const;
  PROTOBUF_NODISCARD ::carbon::thinning::ConfigDefinition* release_definition();
  ::carbon::thinning::ConfigDefinition* mutable_definition();
  void set_allocated_definition(::carbon::thinning::ConfigDefinition* definition);
  private:
  const ::carbon::thinning::ConfigDefinition& _internal_definition() const;
  ::carbon::thinning::ConfigDefinition* _internal_mutable_definition();
  public:
  void unsafe_arena_set_allocated_definition(
      ::carbon::thinning::ConfigDefinition* definition);
  ::carbon::thinning::ConfigDefinition* unsafe_arena_release_definition();

  // bool set_active = 2;
  void clear_set_active();
  bool set_active() const;
  void set_set_active(bool value);
  private:
  bool _internal_set_active() const;
  void _internal_set_set_active(bool value);
  public:

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  void clear_ver();
  ::carbon::frontend::thinning::ThinningConfVer ver() const;
  void set_ver(::carbon::frontend::thinning::ThinningConfVer value);
  private:
  ::carbon::frontend::thinning::ThinningConfVer _internal_ver() const;
  void _internal_set_ver(::carbon::frontend::thinning::ThinningConfVer value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.thinning.DefineConfigurationRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::thinning::ConfigDefinition* definition_;
  bool set_active_;
  int ver_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class DefineConfigurationResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.thinning.DefineConfigurationResponse) */ {
 public:
  inline DefineConfigurationResponse() : DefineConfigurationResponse(nullptr) {}
  ~DefineConfigurationResponse() override;
  explicit constexpr DefineConfigurationResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DefineConfigurationResponse(const DefineConfigurationResponse& from);
  DefineConfigurationResponse(DefineConfigurationResponse&& from) noexcept
    : DefineConfigurationResponse() {
    *this = ::std::move(from);
  }

  inline DefineConfigurationResponse& operator=(const DefineConfigurationResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DefineConfigurationResponse& operator=(DefineConfigurationResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DefineConfigurationResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const DefineConfigurationResponse* internal_default_instance() {
    return reinterpret_cast<const DefineConfigurationResponse*>(
               &_DefineConfigurationResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(DefineConfigurationResponse& a, DefineConfigurationResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DefineConfigurationResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DefineConfigurationResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DefineConfigurationResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DefineConfigurationResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DefineConfigurationResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DefineConfigurationResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DefineConfigurationResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.thinning.DefineConfigurationResponse";
  }
  protected:
  explicit DefineConfigurationResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kIdFieldNumber = 1,
  };
  // string id = 1;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.thinning.DefineConfigurationResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class SetActiveConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.thinning.SetActiveConfigRequest) */ {
 public:
  inline SetActiveConfigRequest() : SetActiveConfigRequest(nullptr) {}
  ~SetActiveConfigRequest() override;
  explicit constexpr SetActiveConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveConfigRequest(const SetActiveConfigRequest& from);
  SetActiveConfigRequest(SetActiveConfigRequest&& from) noexcept
    : SetActiveConfigRequest() {
    *this = ::std::move(from);
  }

  inline SetActiveConfigRequest& operator=(const SetActiveConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveConfigRequest& operator=(SetActiveConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveConfigRequest* internal_default_instance() {
    return reinterpret_cast<const SetActiveConfigRequest*>(
               &_SetActiveConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(SetActiveConfigRequest& a, SetActiveConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetActiveConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetActiveConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetActiveConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.thinning.SetActiveConfigRequest";
  }
  protected:
  explicit SetActiveConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kIdFieldNumber = 2,
    kVerFieldNumber = 3,
  };
  // string name = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_name();
  PROTOBUF_DEPRECATED const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_name(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_name();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_name();
  PROTOBUF_DEPRECATED void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string id = 2;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  void clear_ver();
  ::carbon::frontend::thinning::ThinningConfVer ver() const;
  void set_ver(::carbon::frontend::thinning::ThinningConfVer value);
  private:
  ::carbon::frontend::thinning::ThinningConfVer _internal_ver() const;
  void _internal_set_ver(::carbon::frontend::thinning::ThinningConfVer value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.thinning.SetActiveConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  int ver_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class SetActiveConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.thinning.SetActiveConfigResponse) */ {
 public:
  inline SetActiveConfigResponse() : SetActiveConfigResponse(nullptr) {}
  explicit constexpr SetActiveConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetActiveConfigResponse(const SetActiveConfigResponse& from);
  SetActiveConfigResponse(SetActiveConfigResponse&& from) noexcept
    : SetActiveConfigResponse() {
    *this = ::std::move(from);
  }

  inline SetActiveConfigResponse& operator=(const SetActiveConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetActiveConfigResponse& operator=(SetActiveConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetActiveConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetActiveConfigResponse* internal_default_instance() {
    return reinterpret_cast<const SetActiveConfigResponse*>(
               &_SetActiveConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(SetActiveConfigResponse& a, SetActiveConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetActiveConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetActiveConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetActiveConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetActiveConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetActiveConfigResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetActiveConfigResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.thinning.SetActiveConfigResponse";
  }
  protected:
  explicit SetActiveConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.thinning.SetActiveConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class DeleteConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.thinning.DeleteConfigRequest) */ {
 public:
  inline DeleteConfigRequest() : DeleteConfigRequest(nullptr) {}
  ~DeleteConfigRequest() override;
  explicit constexpr DeleteConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteConfigRequest(const DeleteConfigRequest& from);
  DeleteConfigRequest(DeleteConfigRequest&& from) noexcept
    : DeleteConfigRequest() {
    *this = ::std::move(from);
  }

  inline DeleteConfigRequest& operator=(const DeleteConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteConfigRequest& operator=(DeleteConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteConfigRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteConfigRequest*>(
               &_DeleteConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DeleteConfigRequest& a, DeleteConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeleteConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.thinning.DeleteConfigRequest";
  }
  protected:
  explicit DeleteConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNameFieldNumber = 1,
    kIdFieldNumber = 2,
    kNewActiveIdFieldNumber = 4,
    kVerFieldNumber = 3,
  };
  // string name = 1 [deprecated = true];
  PROTOBUF_DEPRECATED void clear_name();
  PROTOBUF_DEPRECATED const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  PROTOBUF_DEPRECATED void set_name(ArgT0&& arg0, ArgT... args);
  PROTOBUF_DEPRECATED std::string* mutable_name();
  PROTOBUF_NODISCARD PROTOBUF_DEPRECATED std::string* release_name();
  PROTOBUF_DEPRECATED void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // string id = 2;
  void clear_id();
  const std::string& id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_id();
  PROTOBUF_NODISCARD std::string* release_id();
  void set_allocated_id(std::string* id);
  private:
  const std::string& _internal_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_id(const std::string& value);
  std::string* _internal_mutable_id();
  public:

  // string new_active_id = 4;
  void clear_new_active_id();
  const std::string& new_active_id() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_new_active_id(ArgT0&& arg0, ArgT... args);
  std::string* mutable_new_active_id();
  PROTOBUF_NODISCARD std::string* release_new_active_id();
  void set_allocated_new_active_id(std::string* new_active_id);
  private:
  const std::string& _internal_new_active_id() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_new_active_id(const std::string& value);
  std::string* _internal_mutable_new_active_id();
  public:

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  void clear_ver();
  ::carbon::frontend::thinning::ThinningConfVer ver() const;
  void set_ver(::carbon::frontend::thinning::ThinningConfVer value);
  private:
  ::carbon::frontend::thinning::ThinningConfVer _internal_ver() const;
  void _internal_set_ver(::carbon::frontend::thinning::ThinningConfVer value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.thinning.DeleteConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr id_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr new_active_id_;
  int ver_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fthinning_2eproto;
};
// -------------------------------------------------------------------

class DeleteConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.frontend.thinning.DeleteConfigResponse) */ {
 public:
  inline DeleteConfigResponse() : DeleteConfigResponse(nullptr) {}
  explicit constexpr DeleteConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteConfigResponse(const DeleteConfigResponse& from);
  DeleteConfigResponse(DeleteConfigResponse&& from) noexcept
    : DeleteConfigResponse() {
    *this = ::std::move(from);
  }

  inline DeleteConfigResponse& operator=(const DeleteConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteConfigResponse& operator=(DeleteConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteConfigResponse* internal_default_instance() {
    return reinterpret_cast<const DeleteConfigResponse*>(
               &_DeleteConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(DeleteConfigResponse& a, DeleteConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const DeleteConfigResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const DeleteConfigResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.thinning.DeleteConfigResponse";
  }
  protected:
  explicit DeleteConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.frontend.thinning.DeleteConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fthinning_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetNextConfigurationsResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextConfigurationsResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextConfigurationsResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextConfigurationsResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextConfigurationsResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.GetNextConfigurationsResponse.ts)
  return _internal_ts();
}
inline void GetNextConfigurationsResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.thinning.GetNextConfigurationsResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigurationsResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigurationsResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.GetNextConfigurationsResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigurationsResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextConfigurationsResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.GetNextConfigurationsResponse.ts)
  return _msg;
}
inline void GetNextConfigurationsResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.GetNextConfigurationsResponse.ts)
}

// repeated .carbon.thinning.ConfigDefinition definitions = 2;
inline int GetNextConfigurationsResponse::_internal_definitions_size() const {
  return definitions_.size();
}
inline int GetNextConfigurationsResponse::definitions_size() const {
  return _internal_definitions_size();
}
inline ::carbon::thinning::ConfigDefinition* GetNextConfigurationsResponse::mutable_definitions(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.GetNextConfigurationsResponse.definitions)
  return definitions_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::thinning::ConfigDefinition >*
GetNextConfigurationsResponse::mutable_definitions() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.thinning.GetNextConfigurationsResponse.definitions)
  return &definitions_;
}
inline const ::carbon::thinning::ConfigDefinition& GetNextConfigurationsResponse::_internal_definitions(int index) const {
  return definitions_.Get(index);
}
inline const ::carbon::thinning::ConfigDefinition& GetNextConfigurationsResponse::definitions(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.GetNextConfigurationsResponse.definitions)
  return _internal_definitions(index);
}
inline ::carbon::thinning::ConfigDefinition* GetNextConfigurationsResponse::_internal_add_definitions() {
  return definitions_.Add();
}
inline ::carbon::thinning::ConfigDefinition* GetNextConfigurationsResponse::add_definitions() {
  ::carbon::thinning::ConfigDefinition* _add = _internal_add_definitions();
  // @@protoc_insertion_point(field_add:carbon.frontend.thinning.GetNextConfigurationsResponse.definitions)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::thinning::ConfigDefinition >&
GetNextConfigurationsResponse::definitions() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.thinning.GetNextConfigurationsResponse.definitions)
  return definitions_;
}

// string active_id = 3;
inline void GetNextConfigurationsResponse::clear_active_id() {
  active_id_.ClearToEmpty();
}
inline const std::string& GetNextConfigurationsResponse::active_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.GetNextConfigurationsResponse.active_id)
  return _internal_active_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextConfigurationsResponse::set_active_id(ArgT0&& arg0, ArgT... args) {
 
 active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.GetNextConfigurationsResponse.active_id)
}
inline std::string* GetNextConfigurationsResponse::mutable_active_id() {
  std::string* _s = _internal_mutable_active_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.GetNextConfigurationsResponse.active_id)
  return _s;
}
inline const std::string& GetNextConfigurationsResponse::_internal_active_id() const {
  return active_id_.Get();
}
inline void GetNextConfigurationsResponse::_internal_set_active_id(const std::string& value) {
  
  active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextConfigurationsResponse::_internal_mutable_active_id() {
  
  return active_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextConfigurationsResponse::release_active_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.GetNextConfigurationsResponse.active_id)
  return active_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextConfigurationsResponse::set_allocated_active_id(std::string* active_id) {
  if (active_id != nullptr) {
    
  } else {
    
  }
  active_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), active_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (active_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.GetNextConfigurationsResponse.active_id)
}

// -------------------------------------------------------------------

// GetNextActiveConfResponse

// .carbon.frontend.util.Timestamp ts = 1;
inline bool GetNextActiveConfResponse::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool GetNextActiveConfResponse::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveConfResponse::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& GetNextActiveConfResponse::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.GetNextActiveConfResponse.ts)
  return _internal_ts();
}
inline void GetNextActiveConfResponse::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.thinning.GetNextActiveConfResponse.ts)
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveConfResponse::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveConfResponse::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.GetNextActiveConfResponse.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveConfResponse::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* GetNextActiveConfResponse::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.GetNextActiveConfResponse.ts)
  return _msg;
}
inline void GetNextActiveConfResponse::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.GetNextActiveConfResponse.ts)
}

// string name = 2 [deprecated = true];
inline void GetNextActiveConfResponse::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& GetNextActiveConfResponse::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.GetNextActiveConfResponse.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextActiveConfResponse::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.GetNextActiveConfResponse.name)
}
inline std::string* GetNextActiveConfResponse::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.GetNextActiveConfResponse.name)
  return _s;
}
inline const std::string& GetNextActiveConfResponse::_internal_name() const {
  return name_.Get();
}
inline void GetNextActiveConfResponse::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextActiveConfResponse::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextActiveConfResponse::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.GetNextActiveConfResponse.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextActiveConfResponse::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.GetNextActiveConfResponse.name)
}

// string id = 3;
inline void GetNextActiveConfResponse::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& GetNextActiveConfResponse::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.GetNextActiveConfResponse.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetNextActiveConfResponse::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.GetNextActiveConfResponse.id)
}
inline std::string* GetNextActiveConfResponse::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.GetNextActiveConfResponse.id)
  return _s;
}
inline const std::string& GetNextActiveConfResponse::_internal_id() const {
  return id_.Get();
}
inline void GetNextActiveConfResponse::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetNextActiveConfResponse::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetNextActiveConfResponse::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.GetNextActiveConfResponse.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetNextActiveConfResponse::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.GetNextActiveConfResponse.id)
}

// -------------------------------------------------------------------

// DefineConfigurationRequest

// .carbon.thinning.ConfigDefinition definition = 1;
inline bool DefineConfigurationRequest::_internal_has_definition() const {
  return this != internal_default_instance() && definition_ != nullptr;
}
inline bool DefineConfigurationRequest::has_definition() const {
  return _internal_has_definition();
}
inline const ::carbon::thinning::ConfigDefinition& DefineConfigurationRequest::_internal_definition() const {
  const ::carbon::thinning::ConfigDefinition* p = definition_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::thinning::ConfigDefinition&>(
      ::carbon::thinning::_ConfigDefinition_default_instance_);
}
inline const ::carbon::thinning::ConfigDefinition& DefineConfigurationRequest::definition() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.DefineConfigurationRequest.definition)
  return _internal_definition();
}
inline void DefineConfigurationRequest::unsafe_arena_set_allocated_definition(
    ::carbon::thinning::ConfigDefinition* definition) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(definition_);
  }
  definition_ = definition;
  if (definition) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.thinning.DefineConfigurationRequest.definition)
}
inline ::carbon::thinning::ConfigDefinition* DefineConfigurationRequest::release_definition() {
  
  ::carbon::thinning::ConfigDefinition* temp = definition_;
  definition_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::thinning::ConfigDefinition* DefineConfigurationRequest::unsafe_arena_release_definition() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.DefineConfigurationRequest.definition)
  
  ::carbon::thinning::ConfigDefinition* temp = definition_;
  definition_ = nullptr;
  return temp;
}
inline ::carbon::thinning::ConfigDefinition* DefineConfigurationRequest::_internal_mutable_definition() {
  
  if (definition_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::thinning::ConfigDefinition>(GetArenaForAllocation());
    definition_ = p;
  }
  return definition_;
}
inline ::carbon::thinning::ConfigDefinition* DefineConfigurationRequest::mutable_definition() {
  ::carbon::thinning::ConfigDefinition* _msg = _internal_mutable_definition();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.DefineConfigurationRequest.definition)
  return _msg;
}
inline void DefineConfigurationRequest::set_allocated_definition(::carbon::thinning::ConfigDefinition* definition) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(definition_);
  }
  if (definition) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(definition));
    if (message_arena != submessage_arena) {
      definition = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, definition, submessage_arena);
    }
    
  } else {
    
  }
  definition_ = definition;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.DefineConfigurationRequest.definition)
}

// bool set_active = 2;
inline void DefineConfigurationRequest::clear_set_active() {
  set_active_ = false;
}
inline bool DefineConfigurationRequest::_internal_set_active() const {
  return set_active_;
}
inline bool DefineConfigurationRequest::set_active() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.DefineConfigurationRequest.set_active)
  return _internal_set_active();
}
inline void DefineConfigurationRequest::_internal_set_set_active(bool value) {
  
  set_active_ = value;
}
inline void DefineConfigurationRequest::set_set_active(bool value) {
  _internal_set_set_active(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.DefineConfigurationRequest.set_active)
}

// .carbon.frontend.thinning.ThinningConfVer ver = 3;
inline void DefineConfigurationRequest::clear_ver() {
  ver_ = 0;
}
inline ::carbon::frontend::thinning::ThinningConfVer DefineConfigurationRequest::_internal_ver() const {
  return static_cast< ::carbon::frontend::thinning::ThinningConfVer >(ver_);
}
inline ::carbon::frontend::thinning::ThinningConfVer DefineConfigurationRequest::ver() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.DefineConfigurationRequest.ver)
  return _internal_ver();
}
inline void DefineConfigurationRequest::_internal_set_ver(::carbon::frontend::thinning::ThinningConfVer value) {
  
  ver_ = value;
}
inline void DefineConfigurationRequest::set_ver(::carbon::frontend::thinning::ThinningConfVer value) {
  _internal_set_ver(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.DefineConfigurationRequest.ver)
}

// -------------------------------------------------------------------

// DefineConfigurationResponse

// string id = 1;
inline void DefineConfigurationResponse::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& DefineConfigurationResponse::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.DefineConfigurationResponse.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DefineConfigurationResponse::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.DefineConfigurationResponse.id)
}
inline std::string* DefineConfigurationResponse::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.DefineConfigurationResponse.id)
  return _s;
}
inline const std::string& DefineConfigurationResponse::_internal_id() const {
  return id_.Get();
}
inline void DefineConfigurationResponse::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DefineConfigurationResponse::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DefineConfigurationResponse::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.DefineConfigurationResponse.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DefineConfigurationResponse::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.DefineConfigurationResponse.id)
}

// -------------------------------------------------------------------

// SetActiveConfigRequest

// string name = 1 [deprecated = true];
inline void SetActiveConfigRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& SetActiveConfigRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.SetActiveConfigRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveConfigRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.SetActiveConfigRequest.name)
}
inline std::string* SetActiveConfigRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.SetActiveConfigRequest.name)
  return _s;
}
inline const std::string& SetActiveConfigRequest::_internal_name() const {
  return name_.Get();
}
inline void SetActiveConfigRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveConfigRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveConfigRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.SetActiveConfigRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveConfigRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.SetActiveConfigRequest.name)
}

// string id = 2;
inline void SetActiveConfigRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& SetActiveConfigRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.SetActiveConfigRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetActiveConfigRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.SetActiveConfigRequest.id)
}
inline std::string* SetActiveConfigRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.SetActiveConfigRequest.id)
  return _s;
}
inline const std::string& SetActiveConfigRequest::_internal_id() const {
  return id_.Get();
}
inline void SetActiveConfigRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetActiveConfigRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetActiveConfigRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.SetActiveConfigRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetActiveConfigRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.SetActiveConfigRequest.id)
}

// .carbon.frontend.thinning.ThinningConfVer ver = 3;
inline void SetActiveConfigRequest::clear_ver() {
  ver_ = 0;
}
inline ::carbon::frontend::thinning::ThinningConfVer SetActiveConfigRequest::_internal_ver() const {
  return static_cast< ::carbon::frontend::thinning::ThinningConfVer >(ver_);
}
inline ::carbon::frontend::thinning::ThinningConfVer SetActiveConfigRequest::ver() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.SetActiveConfigRequest.ver)
  return _internal_ver();
}
inline void SetActiveConfigRequest::_internal_set_ver(::carbon::frontend::thinning::ThinningConfVer value) {
  
  ver_ = value;
}
inline void SetActiveConfigRequest::set_ver(::carbon::frontend::thinning::ThinningConfVer value) {
  _internal_set_ver(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.SetActiveConfigRequest.ver)
}

// -------------------------------------------------------------------

// SetActiveConfigResponse

// -------------------------------------------------------------------

// DeleteConfigRequest

// string name = 1 [deprecated = true];
inline void DeleteConfigRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& DeleteConfigRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.DeleteConfigRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteConfigRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.DeleteConfigRequest.name)
}
inline std::string* DeleteConfigRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.DeleteConfigRequest.name)
  return _s;
}
inline const std::string& DeleteConfigRequest::_internal_name() const {
  return name_.Get();
}
inline void DeleteConfigRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteConfigRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteConfigRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.DeleteConfigRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteConfigRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.DeleteConfigRequest.name)
}

// string id = 2;
inline void DeleteConfigRequest::clear_id() {
  id_.ClearToEmpty();
}
inline const std::string& DeleteConfigRequest::id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.DeleteConfigRequest.id)
  return _internal_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteConfigRequest::set_id(ArgT0&& arg0, ArgT... args) {
 
 id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.DeleteConfigRequest.id)
}
inline std::string* DeleteConfigRequest::mutable_id() {
  std::string* _s = _internal_mutable_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.DeleteConfigRequest.id)
  return _s;
}
inline const std::string& DeleteConfigRequest::_internal_id() const {
  return id_.Get();
}
inline void DeleteConfigRequest::_internal_set_id(const std::string& value) {
  
  id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteConfigRequest::_internal_mutable_id() {
  
  return id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteConfigRequest::release_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.DeleteConfigRequest.id)
  return id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteConfigRequest::set_allocated_id(std::string* id) {
  if (id != nullptr) {
    
  } else {
    
  }
  id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.DeleteConfigRequest.id)
}

// .carbon.frontend.thinning.ThinningConfVer ver = 3;
inline void DeleteConfigRequest::clear_ver() {
  ver_ = 0;
}
inline ::carbon::frontend::thinning::ThinningConfVer DeleteConfigRequest::_internal_ver() const {
  return static_cast< ::carbon::frontend::thinning::ThinningConfVer >(ver_);
}
inline ::carbon::frontend::thinning::ThinningConfVer DeleteConfigRequest::ver() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.DeleteConfigRequest.ver)
  return _internal_ver();
}
inline void DeleteConfigRequest::_internal_set_ver(::carbon::frontend::thinning::ThinningConfVer value) {
  
  ver_ = value;
}
inline void DeleteConfigRequest::set_ver(::carbon::frontend::thinning::ThinningConfVer value) {
  _internal_set_ver(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.DeleteConfigRequest.ver)
}

// string new_active_id = 4;
inline void DeleteConfigRequest::clear_new_active_id() {
  new_active_id_.ClearToEmpty();
}
inline const std::string& DeleteConfigRequest::new_active_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.thinning.DeleteConfigRequest.new_active_id)
  return _internal_new_active_id();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteConfigRequest::set_new_active_id(ArgT0&& arg0, ArgT... args) {
 
 new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.thinning.DeleteConfigRequest.new_active_id)
}
inline std::string* DeleteConfigRequest::mutable_new_active_id() {
  std::string* _s = _internal_mutable_new_active_id();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.thinning.DeleteConfigRequest.new_active_id)
  return _s;
}
inline const std::string& DeleteConfigRequest::_internal_new_active_id() const {
  return new_active_id_.Get();
}
inline void DeleteConfigRequest::_internal_set_new_active_id(const std::string& value) {
  
  new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteConfigRequest::_internal_mutable_new_active_id() {
  
  return new_active_id_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteConfigRequest::release_new_active_id() {
  // @@protoc_insertion_point(field_release:carbon.frontend.thinning.DeleteConfigRequest.new_active_id)
  return new_active_id_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteConfigRequest::set_allocated_new_active_id(std::string* new_active_id) {
  if (new_active_id != nullptr) {
    
  } else {
    
  }
  new_active_id_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), new_active_id,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (new_active_id_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.thinning.DeleteConfigRequest.new_active_id)
}

// -------------------------------------------------------------------

// DeleteConfigResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace thinning
}  // namespace frontend
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::frontend::thinning::ThinningConfVer> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::frontend::thinning::ThinningConfVer>() {
  return ::carbon::frontend::thinning::ThinningConfVer_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fthinning_2eproto

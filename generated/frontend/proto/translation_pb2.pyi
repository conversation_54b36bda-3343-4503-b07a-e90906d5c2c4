"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class IntegerValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    value: builtin___int = ...

    def __init__(self,
        *,
        value : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> None: ...
type___IntegerValue = IntegerValue

class DoubleValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    value: builtin___float = ...

    def __init__(self,
        *,
        value : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> None: ...
type___DoubleValue = DoubleValue

class StringValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    value: typing___Text = ...

    def __init__(self,
        *,
        value : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> None: ...
type___StringValue = StringValue

class TemperatureValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    celcius: builtin___float = ...
    fahrenheit: builtin___float = ...

    def __init__(self,
        *,
        celcius : typing___Optional[builtin___float] = None,
        fahrenheit : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"celcius",b"celcius",u"fahrenheit",b"fahrenheit",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"celcius",b"celcius",u"fahrenheit",b"fahrenheit",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["celcius","fahrenheit"]: ...
type___TemperatureValue = TemperatureValue

class PercentValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    percent: builtin___int = ...

    def __init__(self,
        *,
        percent : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"percent",b"percent"]) -> None: ...
type___PercentValue = PercentValue

class VoltageValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    volts: builtin___float = ...

    def __init__(self,
        *,
        volts : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"volts",b"volts"]) -> None: ...
type___VoltageValue = VoltageValue

class FrequencyValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    hertz: builtin___float = ...

    def __init__(self,
        *,
        hertz : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"hertz",b"hertz"]) -> None: ...
type___FrequencyValue = FrequencyValue

class AreaValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    acres: builtin___float = ...
    hectares: builtin___float = ...
    square_feet: builtin___float = ...
    square_meters: builtin___float = ...

    def __init__(self,
        *,
        acres : typing___Optional[builtin___float] = None,
        hectares : typing___Optional[builtin___float] = None,
        square_feet : typing___Optional[builtin___float] = None,
        square_meters : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"acres",b"acres",u"hectares",b"hectares",u"square_feet",b"square_feet",u"square_meters",b"square_meters",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"acres",b"acres",u"hectares",b"hectares",u"square_feet",b"square_feet",u"square_meters",b"square_meters",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["acres","hectares","square_feet","square_meters"]: ...
type___AreaValue = AreaValue

class DurationValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    milliseconds: builtin___int = ...
    seconds: builtin___int = ...
    minutes: builtin___int = ...
    hours: builtin___int = ...

    def __init__(self,
        *,
        milliseconds : typing___Optional[builtin___int] = None,
        seconds : typing___Optional[builtin___int] = None,
        minutes : typing___Optional[builtin___int] = None,
        hours : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"hours",b"hours",u"milliseconds",b"milliseconds",u"minutes",b"minutes",u"seconds",b"seconds",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"hours",b"hours",u"milliseconds",b"milliseconds",u"minutes",b"minutes",u"seconds",b"seconds",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["milliseconds","seconds","minutes","hours"]: ...
type___DurationValue = DurationValue

class DistanceValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    millimeters: builtin___float = ...
    meters: builtin___float = ...
    kilometers: builtin___float = ...
    inches: builtin___float = ...
    feet: builtin___float = ...
    miles: builtin___float = ...
    centimeters: builtin___float = ...

    def __init__(self,
        *,
        millimeters : typing___Optional[builtin___float] = None,
        meters : typing___Optional[builtin___float] = None,
        kilometers : typing___Optional[builtin___float] = None,
        inches : typing___Optional[builtin___float] = None,
        feet : typing___Optional[builtin___float] = None,
        miles : typing___Optional[builtin___float] = None,
        centimeters : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"centimeters",b"centimeters",u"feet",b"feet",u"inches",b"inches",u"kilometers",b"kilometers",u"meters",b"meters",u"miles",b"miles",u"millimeters",b"millimeters",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"centimeters",b"centimeters",u"feet",b"feet",u"inches",b"inches",u"kilometers",b"kilometers",u"meters",b"meters",u"miles",b"miles",u"millimeters",b"millimeters",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["millimeters","meters","kilometers","inches","feet","miles","centimeters"]: ...
type___DistanceValue = DistanceValue

class SpeedValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    kilometers_per_hour: builtin___float = ...
    miles_per_hour: builtin___float = ...

    def __init__(self,
        *,
        kilometers_per_hour : typing___Optional[builtin___float] = None,
        miles_per_hour : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"kilometers_per_hour",b"kilometers_per_hour",u"miles_per_hour",b"miles_per_hour",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"kilometers_per_hour",b"kilometers_per_hour",u"miles_per_hour",b"miles_per_hour",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["kilometers_per_hour","miles_per_hour"]: ...
type___SpeedValue = SpeedValue

class TranslationParameter(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    @property
    def int_value(self) -> type___IntegerValue: ...

    @property
    def double_value(self) -> type___DoubleValue: ...

    @property
    def string_value(self) -> type___StringValue: ...

    @property
    def temperature_value(self) -> type___TemperatureValue: ...

    @property
    def percent_value(self) -> type___PercentValue: ...

    @property
    def voltage_value(self) -> type___VoltageValue: ...

    @property
    def frequency_value(self) -> type___FrequencyValue: ...

    @property
    def area_value(self) -> type___AreaValue: ...

    @property
    def duration_value(self) -> type___DurationValue: ...

    @property
    def distance_value(self) -> type___DistanceValue: ...

    @property
    def speed_value(self) -> type___SpeedValue: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        int_value : typing___Optional[type___IntegerValue] = None,
        double_value : typing___Optional[type___DoubleValue] = None,
        string_value : typing___Optional[type___StringValue] = None,
        temperature_value : typing___Optional[type___TemperatureValue] = None,
        percent_value : typing___Optional[type___PercentValue] = None,
        voltage_value : typing___Optional[type___VoltageValue] = None,
        frequency_value : typing___Optional[type___FrequencyValue] = None,
        area_value : typing___Optional[type___AreaValue] = None,
        duration_value : typing___Optional[type___DurationValue] = None,
        distance_value : typing___Optional[type___DistanceValue] = None,
        speed_value : typing___Optional[type___SpeedValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"area_value",b"area_value",u"distance_value",b"distance_value",u"double_value",b"double_value",u"duration_value",b"duration_value",u"frequency_value",b"frequency_value",u"int_value",b"int_value",u"percent_value",b"percent_value",u"speed_value",b"speed_value",u"string_value",b"string_value",u"temperature_value",b"temperature_value",u"value",b"value",u"voltage_value",b"voltage_value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"area_value",b"area_value",u"distance_value",b"distance_value",u"double_value",b"double_value",u"duration_value",b"duration_value",u"frequency_value",b"frequency_value",u"int_value",b"int_value",u"name",b"name",u"percent_value",b"percent_value",u"speed_value",b"speed_value",u"string_value",b"string_value",u"temperature_value",b"temperature_value",u"value",b"value",u"voltage_value",b"voltage_value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["int_value","double_value","string_value","temperature_value","percent_value","voltage_value","frequency_value","area_value","duration_value","distance_value","speed_value"]: ...
type___TranslationParameter = TranslationParameter

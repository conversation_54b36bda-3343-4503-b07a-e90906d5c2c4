# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/profile_sync.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/profile_sync.proto',
  package='carbon.frontend.profile_sync',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n!frontend/proto/profile_sync.proto\x12\x1c\x63\x61rbon.frontend.profile_sync\"\x92\x01\n\x0fProfileSyncData\x12?\n\x0cprofile_type\x18\x01 \x01(\x0e\x32).carbon.frontend.profile_sync.ProfileType\x12\x1a\n\x12last_updated_ts_ms\x18\x02 \x01(\x03\x12\x0f\n\x07\x64\x65leted\x18\x03 \x01(\x08\x12\x11\n\tprotected\x18\x04 \x01(\x08*\x9f\x01\n\x0bProfileType\x12\x0b\n\x07\x41LMANAC\x10\x00\x12\x11\n\rDISCRIMINATOR\x10\x01\x12\x0f\n\x0bMODELINATOR\x10\x03\x12\x0b\n\x07\x42\x41NDING\x10\x04\x12\x0c\n\x08THINNING\x10\x05\x12\x1d\n\x19TARGET_VELOCITY_ESTIMATOR\x10\x06\x12\x17\n\x13\x43\x41TEGORY_COLLECTION\x10\x07\x12\x0c\n\x08\x43\x41TEGORY\x10\x08\x42\x10Z\x0eproto/frontendb\x06proto3'
)

_PROFILETYPE = _descriptor.EnumDescriptor(
  name='ProfileType',
  full_name='carbon.frontend.profile_sync.ProfileType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='ALMANAC', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DISCRIMINATOR', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='MODELINATOR', index=2, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BANDING', index=3, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='THINNING', index=4, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='TARGET_VELOCITY_ESTIMATOR', index=5, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CATEGORY_COLLECTION', index=6, number=7,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='CATEGORY', index=7, number=8,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=217,
  serialized_end=376,
)
_sym_db.RegisterEnumDescriptor(_PROFILETYPE)

ProfileType = enum_type_wrapper.EnumTypeWrapper(_PROFILETYPE)
ALMANAC = 0
DISCRIMINATOR = 1
MODELINATOR = 3
BANDING = 4
THINNING = 5
TARGET_VELOCITY_ESTIMATOR = 6
CATEGORY_COLLECTION = 7
CATEGORY = 8



_PROFILESYNCDATA = _descriptor.Descriptor(
  name='ProfileSyncData',
  full_name='carbon.frontend.profile_sync.ProfileSyncData',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='profile_type', full_name='carbon.frontend.profile_sync.ProfileSyncData.profile_type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='last_updated_ts_ms', full_name='carbon.frontend.profile_sync.ProfileSyncData.last_updated_ts_ms', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='deleted', full_name='carbon.frontend.profile_sync.ProfileSyncData.deleted', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='protected', full_name='carbon.frontend.profile_sync.ProfileSyncData.protected', index=3,
      number=4, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=68,
  serialized_end=214,
)

_PROFILESYNCDATA.fields_by_name['profile_type'].enum_type = _PROFILETYPE
DESCRIPTOR.message_types_by_name['ProfileSyncData'] = _PROFILESYNCDATA
DESCRIPTOR.enum_types_by_name['ProfileType'] = _PROFILETYPE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

ProfileSyncData = _reflection.GeneratedProtocolMessageType('ProfileSyncData', (_message.Message,), {
  'DESCRIPTOR' : _PROFILESYNCDATA,
  '__module__' : 'frontend.proto.profile_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.profile_sync.ProfileSyncData)
  })
_sym_db.RegisterMessage(ProfileSyncData)


DESCRIPTOR._options = None
# @@protoc_insertion_point(module_scope)

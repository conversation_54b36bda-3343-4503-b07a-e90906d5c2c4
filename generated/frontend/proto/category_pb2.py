# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: frontend/proto/category.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.frontend.proto import util_pb2 as frontend_dot_proto_dot_util__pb2
from generated.category.proto import category_pb2 as category_dot_proto_dot_category__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='frontend/proto/category.proto',
  package='carbon.frontend.category',
  syntax='proto3',
  serialized_options=b'Z\016proto/frontend',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n\x1d\x66rontend/proto/category.proto\x12\x18\x63\x61rbon.frontend.category\x1a\x19\x66rontend/proto/util.proto\x1a\x1d\x63\x61tegory/proto/category.proto\"I\n\x1aGetNextCategoryDataRequest\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\"y\n\x1bGetNextCategoryDataResponse\x12+\n\x02ts\x18\x01 \x01(\x0b\x32\x1f.carbon.frontend.util.Timestamp\x12-\n\ncategories\x18\x02 \x03(\x0b\x32\x19.carbon.category.Category2\x96\x01\n\x0f\x43\x61tegoryService\x12\x82\x01\n\x13GetNextCategoryData\x12\x34.carbon.frontend.category.GetNextCategoryDataRequest\x1a\x35.carbon.frontend.category.GetNextCategoryDataResponseB\x10Z\x0eproto/frontendb\x06proto3'
  ,
  dependencies=[frontend_dot_proto_dot_util__pb2.DESCRIPTOR,category_dot_proto_dot_category__pb2.DESCRIPTOR,])




_GETNEXTCATEGORYDATAREQUEST = _descriptor.Descriptor(
  name='GetNextCategoryDataRequest',
  full_name='carbon.frontend.category.GetNextCategoryDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.category.GetNextCategoryDataRequest.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=117,
  serialized_end=190,
)


_GETNEXTCATEGORYDATARESPONSE = _descriptor.Descriptor(
  name='GetNextCategoryDataResponse',
  full_name='carbon.frontend.category.GetNextCategoryDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='ts', full_name='carbon.frontend.category.GetNextCategoryDataResponse.ts', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='categories', full_name='carbon.frontend.category.GetNextCategoryDataResponse.categories', index=1,
      number=2, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=192,
  serialized_end=313,
)

_GETNEXTCATEGORYDATAREQUEST.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCATEGORYDATARESPONSE.fields_by_name['ts'].message_type = frontend_dot_proto_dot_util__pb2._TIMESTAMP
_GETNEXTCATEGORYDATARESPONSE.fields_by_name['categories'].message_type = category_dot_proto_dot_category__pb2._CATEGORY
DESCRIPTOR.message_types_by_name['GetNextCategoryDataRequest'] = _GETNEXTCATEGORYDATAREQUEST
DESCRIPTOR.message_types_by_name['GetNextCategoryDataResponse'] = _GETNEXTCATEGORYDATARESPONSE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetNextCategoryDataRequest = _reflection.GeneratedProtocolMessageType('GetNextCategoryDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCATEGORYDATAREQUEST,
  '__module__' : 'frontend.proto.category_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.category.GetNextCategoryDataRequest)
  })
_sym_db.RegisterMessage(GetNextCategoryDataRequest)

GetNextCategoryDataResponse = _reflection.GeneratedProtocolMessageType('GetNextCategoryDataResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETNEXTCATEGORYDATARESPONSE,
  '__module__' : 'frontend.proto.category_pb2'
  # @@protoc_insertion_point(class_scope:carbon.frontend.category.GetNextCategoryDataResponse)
  })
_sym_db.RegisterMessage(GetNextCategoryDataResponse)


DESCRIPTOR._options = None

_CATEGORYSERVICE = _descriptor.ServiceDescriptor(
  name='CategoryService',
  full_name='carbon.frontend.category.CategoryService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=316,
  serialized_end=466,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetNextCategoryData',
    full_name='carbon.frontend.category.CategoryService.GetNextCategoryData',
    index=0,
    containing_service=None,
    input_type=_GETNEXTCATEGORYDATAREQUEST,
    output_type=_GETNEXTCATEGORYDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CATEGORYSERVICE)

DESCRIPTOR.services_by_name['CategoryService'] = _CATEGORYSERVICE

# @@protoc_insertion_point(module_scope)

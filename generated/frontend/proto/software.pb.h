// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/software.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fsoftware_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fsoftware_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "frontend/proto/util.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_frontend_2fproto_2fsoftware_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_frontend_2fproto_2fsoftware_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[5]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fsoftware_2eproto;
namespace carbon {
namespace frontend {
namespace software {
class HostSoftwareVersionState;
struct HostSoftwareVersionStateDefaultTypeInternal;
extern HostSoftwareVersionStateDefaultTypeInternal _HostSoftwareVersionState_default_instance_;
class SoftwareVersion;
struct SoftwareVersionDefaultTypeInternal;
extern SoftwareVersionDefaultTypeInternal _SoftwareVersion_default_instance_;
class SoftwareVersionState;
struct SoftwareVersionStateDefaultTypeInternal;
extern SoftwareVersionStateDefaultTypeInternal _SoftwareVersionState_default_instance_;
class SoftwareVersionStateRequest;
struct SoftwareVersionStateRequestDefaultTypeInternal;
extern SoftwareVersionStateRequestDefaultTypeInternal _SoftwareVersionStateRequest_default_instance_;
class UpdateHostRequest;
struct UpdateHostRequestDefaultTypeInternal;
extern UpdateHostRequestDefaultTypeInternal _UpdateHostRequest_default_instance_;
}  // namespace software
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::frontend::software::HostSoftwareVersionState* Arena::CreateMaybeMessage<::carbon::frontend::software::HostSoftwareVersionState>(Arena*);
template<> ::carbon::frontend::software::SoftwareVersion* Arena::CreateMaybeMessage<::carbon::frontend::software::SoftwareVersion>(Arena*);
template<> ::carbon::frontend::software::SoftwareVersionState* Arena::CreateMaybeMessage<::carbon::frontend::software::SoftwareVersionState>(Arena*);
template<> ::carbon::frontend::software::SoftwareVersionStateRequest* Arena::CreateMaybeMessage<::carbon::frontend::software::SoftwareVersionStateRequest>(Arena*);
template<> ::carbon::frontend::software::UpdateHostRequest* Arena::CreateMaybeMessage<::carbon::frontend::software::UpdateHostRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace frontend {
namespace software {

// ===================================================================

class SoftwareVersion final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.software.SoftwareVersion) */ {
 public:
  inline SoftwareVersion() : SoftwareVersion(nullptr) {}
  ~SoftwareVersion() override;
  explicit constexpr SoftwareVersion(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SoftwareVersion(const SoftwareVersion& from);
  SoftwareVersion(SoftwareVersion&& from) noexcept
    : SoftwareVersion() {
    *this = ::std::move(from);
  }

  inline SoftwareVersion& operator=(const SoftwareVersion& from) {
    CopyFrom(from);
    return *this;
  }
  inline SoftwareVersion& operator=(SoftwareVersion&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SoftwareVersion& default_instance() {
    return *internal_default_instance();
  }
  static inline const SoftwareVersion* internal_default_instance() {
    return reinterpret_cast<const SoftwareVersion*>(
               &_SoftwareVersion_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SoftwareVersion& a, SoftwareVersion& b) {
    a.Swap(&b);
  }
  inline void Swap(SoftwareVersion* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SoftwareVersion* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SoftwareVersion* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SoftwareVersion>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SoftwareVersion& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SoftwareVersion& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SoftwareVersion* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.software.SoftwareVersion";
  }
  protected:
  explicit SoftwareVersion(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTagFieldNumber = 1,
    kAvailableFieldNumber = 2,
    kReadyFieldNumber = 3,
  };
  // string tag = 1;
  void clear_tag();
  const std::string& tag() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tag(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tag();
  PROTOBUF_NODISCARD std::string* release_tag();
  void set_allocated_tag(std::string* tag);
  private:
  const std::string& _internal_tag() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tag(const std::string& value);
  std::string* _internal_mutable_tag();
  public:

  // bool available = 2;
  void clear_available();
  bool available() const;
  void set_available(bool value);
  private:
  bool _internal_available() const;
  void _internal_set_available(bool value);
  public:

  // bool ready = 3;
  void clear_ready();
  bool ready() const;
  void set_ready(bool value);
  private:
  bool _internal_ready() const;
  void _internal_set_ready(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.software.SoftwareVersion)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tag_;
  bool available_;
  bool ready_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fsoftware_2eproto;
};
// -------------------------------------------------------------------

class HostSoftwareVersionState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.software.HostSoftwareVersionState) */ {
 public:
  inline HostSoftwareVersionState() : HostSoftwareVersionState(nullptr) {}
  ~HostSoftwareVersionState() override;
  explicit constexpr HostSoftwareVersionState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  HostSoftwareVersionState(const HostSoftwareVersionState& from);
  HostSoftwareVersionState(HostSoftwareVersionState&& from) noexcept
    : HostSoftwareVersionState() {
    *this = ::std::move(from);
  }

  inline HostSoftwareVersionState& operator=(const HostSoftwareVersionState& from) {
    CopyFrom(from);
    return *this;
  }
  inline HostSoftwareVersionState& operator=(HostSoftwareVersionState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const HostSoftwareVersionState& default_instance() {
    return *internal_default_instance();
  }
  static inline const HostSoftwareVersionState* internal_default_instance() {
    return reinterpret_cast<const HostSoftwareVersionState*>(
               &_HostSoftwareVersionState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(HostSoftwareVersionState& a, HostSoftwareVersionState& b) {
    a.Swap(&b);
  }
  inline void Swap(HostSoftwareVersionState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(HostSoftwareVersionState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  HostSoftwareVersionState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<HostSoftwareVersionState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const HostSoftwareVersionState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const HostSoftwareVersionState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(HostSoftwareVersionState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.software.HostSoftwareVersionState";
  }
  protected:
  explicit HostSoftwareVersionState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHostNameFieldNumber = 1,
    kCurrentFieldNumber = 4,
    kTargetFieldNumber = 5,
    kPreviousFieldNumber = 6,
    kHostIdFieldNumber = 2,
    kActiveFieldNumber = 3,
    kUpdatingFieldNumber = 7,
  };
  // string host_name = 1;
  void clear_host_name();
  const std::string& host_name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_host_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_host_name();
  PROTOBUF_NODISCARD std::string* release_host_name();
  void set_allocated_host_name(std::string* host_name);
  private:
  const std::string& _internal_host_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_host_name(const std::string& value);
  std::string* _internal_mutable_host_name();
  public:

  // .carbon.frontend.software.SoftwareVersion current = 4;
  bool has_current() const;
  private:
  bool _internal_has_current() const;
  public:
  void clear_current();
  const ::carbon::frontend::software::SoftwareVersion& current() const;
  PROTOBUF_NODISCARD ::carbon::frontend::software::SoftwareVersion* release_current();
  ::carbon::frontend::software::SoftwareVersion* mutable_current();
  void set_allocated_current(::carbon::frontend::software::SoftwareVersion* current);
  private:
  const ::carbon::frontend::software::SoftwareVersion& _internal_current() const;
  ::carbon::frontend::software::SoftwareVersion* _internal_mutable_current();
  public:
  void unsafe_arena_set_allocated_current(
      ::carbon::frontend::software::SoftwareVersion* current);
  ::carbon::frontend::software::SoftwareVersion* unsafe_arena_release_current();

  // .carbon.frontend.software.SoftwareVersion target = 5;
  bool has_target() const;
  private:
  bool _internal_has_target() const;
  public:
  void clear_target();
  const ::carbon::frontend::software::SoftwareVersion& target() const;
  PROTOBUF_NODISCARD ::carbon::frontend::software::SoftwareVersion* release_target();
  ::carbon::frontend::software::SoftwareVersion* mutable_target();
  void set_allocated_target(::carbon::frontend::software::SoftwareVersion* target);
  private:
  const ::carbon::frontend::software::SoftwareVersion& _internal_target() const;
  ::carbon::frontend::software::SoftwareVersion* _internal_mutable_target();
  public:
  void unsafe_arena_set_allocated_target(
      ::carbon::frontend::software::SoftwareVersion* target);
  ::carbon::frontend::software::SoftwareVersion* unsafe_arena_release_target();

  // .carbon.frontend.software.SoftwareVersion previous = 6;
  bool has_previous() const;
  private:
  bool _internal_has_previous() const;
  public:
  void clear_previous();
  const ::carbon::frontend::software::SoftwareVersion& previous() const;
  PROTOBUF_NODISCARD ::carbon::frontend::software::SoftwareVersion* release_previous();
  ::carbon::frontend::software::SoftwareVersion* mutable_previous();
  void set_allocated_previous(::carbon::frontend::software::SoftwareVersion* previous);
  private:
  const ::carbon::frontend::software::SoftwareVersion& _internal_previous() const;
  ::carbon::frontend::software::SoftwareVersion* _internal_mutable_previous();
  public:
  void unsafe_arena_set_allocated_previous(
      ::carbon::frontend::software::SoftwareVersion* previous);
  ::carbon::frontend::software::SoftwareVersion* unsafe_arena_release_previous();

  // uint32 host_id = 2;
  void clear_host_id();
  uint32_t host_id() const;
  void set_host_id(uint32_t value);
  private:
  uint32_t _internal_host_id() const;
  void _internal_set_host_id(uint32_t value);
  public:

  // bool active = 3;
  void clear_active();
  bool active() const;
  void set_active(bool value);
  private:
  bool _internal_active() const;
  void _internal_set_active(bool value);
  public:

  // bool updating = 7;
  void clear_updating();
  bool updating() const;
  void set_updating(bool value);
  private:
  bool _internal_updating() const;
  void _internal_set_updating(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.software.HostSoftwareVersionState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr host_name_;
  ::carbon::frontend::software::SoftwareVersion* current_;
  ::carbon::frontend::software::SoftwareVersion* target_;
  ::carbon::frontend::software::SoftwareVersion* previous_;
  uint32_t host_id_;
  bool active_;
  bool updating_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fsoftware_2eproto;
};
// -------------------------------------------------------------------

class SoftwareVersionState final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.software.SoftwareVersionState) */ {
 public:
  inline SoftwareVersionState() : SoftwareVersionState(nullptr) {}
  ~SoftwareVersionState() override;
  explicit constexpr SoftwareVersionState(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SoftwareVersionState(const SoftwareVersionState& from);
  SoftwareVersionState(SoftwareVersionState&& from) noexcept
    : SoftwareVersionState() {
    *this = ::std::move(from);
  }

  inline SoftwareVersionState& operator=(const SoftwareVersionState& from) {
    CopyFrom(from);
    return *this;
  }
  inline SoftwareVersionState& operator=(SoftwareVersionState&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SoftwareVersionState& default_instance() {
    return *internal_default_instance();
  }
  static inline const SoftwareVersionState* internal_default_instance() {
    return reinterpret_cast<const SoftwareVersionState*>(
               &_SoftwareVersionState_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(SoftwareVersionState& a, SoftwareVersionState& b) {
    a.Swap(&b);
  }
  inline void Swap(SoftwareVersionState* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SoftwareVersionState* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SoftwareVersionState* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SoftwareVersionState>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SoftwareVersionState& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SoftwareVersionState& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SoftwareVersionState* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.software.SoftwareVersionState";
  }
  protected:
  explicit SoftwareVersionState(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHostStatesFieldNumber = 7,
    kTsFieldNumber = 1,
    kCurrentFieldNumber = 2,
    kTargetFieldNumber = 3,
    kPreviousFieldNumber = 4,
    kUpdatingFieldNumber = 5,
    kShowSoftwareUpdateToUserFieldNumber = 6,
    kVersionMismatchFieldNumber = 8,
  };
  // repeated .carbon.frontend.software.HostSoftwareVersionState host_states = 7;
  int host_states_size() const;
  private:
  int _internal_host_states_size() const;
  public:
  void clear_host_states();
  ::carbon::frontend::software::HostSoftwareVersionState* mutable_host_states(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::software::HostSoftwareVersionState >*
      mutable_host_states();
  private:
  const ::carbon::frontend::software::HostSoftwareVersionState& _internal_host_states(int index) const;
  ::carbon::frontend::software::HostSoftwareVersionState* _internal_add_host_states();
  public:
  const ::carbon::frontend::software::HostSoftwareVersionState& host_states(int index) const;
  ::carbon::frontend::software::HostSoftwareVersionState* add_host_states();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::software::HostSoftwareVersionState >&
      host_states() const;

  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // .carbon.frontend.software.SoftwareVersion current = 2;
  bool has_current() const;
  private:
  bool _internal_has_current() const;
  public:
  void clear_current();
  const ::carbon::frontend::software::SoftwareVersion& current() const;
  PROTOBUF_NODISCARD ::carbon::frontend::software::SoftwareVersion* release_current();
  ::carbon::frontend::software::SoftwareVersion* mutable_current();
  void set_allocated_current(::carbon::frontend::software::SoftwareVersion* current);
  private:
  const ::carbon::frontend::software::SoftwareVersion& _internal_current() const;
  ::carbon::frontend::software::SoftwareVersion* _internal_mutable_current();
  public:
  void unsafe_arena_set_allocated_current(
      ::carbon::frontend::software::SoftwareVersion* current);
  ::carbon::frontend::software::SoftwareVersion* unsafe_arena_release_current();

  // .carbon.frontend.software.SoftwareVersion target = 3;
  bool has_target() const;
  private:
  bool _internal_has_target() const;
  public:
  void clear_target();
  const ::carbon::frontend::software::SoftwareVersion& target() const;
  PROTOBUF_NODISCARD ::carbon::frontend::software::SoftwareVersion* release_target();
  ::carbon::frontend::software::SoftwareVersion* mutable_target();
  void set_allocated_target(::carbon::frontend::software::SoftwareVersion* target);
  private:
  const ::carbon::frontend::software::SoftwareVersion& _internal_target() const;
  ::carbon::frontend::software::SoftwareVersion* _internal_mutable_target();
  public:
  void unsafe_arena_set_allocated_target(
      ::carbon::frontend::software::SoftwareVersion* target);
  ::carbon::frontend::software::SoftwareVersion* unsafe_arena_release_target();

  // .carbon.frontend.software.SoftwareVersion previous = 4;
  bool has_previous() const;
  private:
  bool _internal_has_previous() const;
  public:
  void clear_previous();
  const ::carbon::frontend::software::SoftwareVersion& previous() const;
  PROTOBUF_NODISCARD ::carbon::frontend::software::SoftwareVersion* release_previous();
  ::carbon::frontend::software::SoftwareVersion* mutable_previous();
  void set_allocated_previous(::carbon::frontend::software::SoftwareVersion* previous);
  private:
  const ::carbon::frontend::software::SoftwareVersion& _internal_previous() const;
  ::carbon::frontend::software::SoftwareVersion* _internal_mutable_previous();
  public:
  void unsafe_arena_set_allocated_previous(
      ::carbon::frontend::software::SoftwareVersion* previous);
  ::carbon::frontend::software::SoftwareVersion* unsafe_arena_release_previous();

  // bool updating = 5;
  void clear_updating();
  bool updating() const;
  void set_updating(bool value);
  private:
  bool _internal_updating() const;
  void _internal_set_updating(bool value);
  public:

  // bool show_software_update_to_user = 6;
  void clear_show_software_update_to_user();
  bool show_software_update_to_user() const;
  void set_show_software_update_to_user(bool value);
  private:
  bool _internal_show_software_update_to_user() const;
  void _internal_set_show_software_update_to_user(bool value);
  public:

  // bool version_mismatch = 8;
  void clear_version_mismatch();
  bool version_mismatch() const;
  void set_version_mismatch(bool value);
  private:
  bool _internal_version_mismatch() const;
  void _internal_set_version_mismatch(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.software.SoftwareVersionState)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::software::HostSoftwareVersionState > host_states_;
  ::carbon::frontend::util::Timestamp* ts_;
  ::carbon::frontend::software::SoftwareVersion* current_;
  ::carbon::frontend::software::SoftwareVersion* target_;
  ::carbon::frontend::software::SoftwareVersion* previous_;
  bool updating_;
  bool show_software_update_to_user_;
  bool version_mismatch_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fsoftware_2eproto;
};
// -------------------------------------------------------------------

class SoftwareVersionStateRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.software.SoftwareVersionStateRequest) */ {
 public:
  inline SoftwareVersionStateRequest() : SoftwareVersionStateRequest(nullptr) {}
  ~SoftwareVersionStateRequest() override;
  explicit constexpr SoftwareVersionStateRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SoftwareVersionStateRequest(const SoftwareVersionStateRequest& from);
  SoftwareVersionStateRequest(SoftwareVersionStateRequest&& from) noexcept
    : SoftwareVersionStateRequest() {
    *this = ::std::move(from);
  }

  inline SoftwareVersionStateRequest& operator=(const SoftwareVersionStateRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SoftwareVersionStateRequest& operator=(SoftwareVersionStateRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SoftwareVersionStateRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SoftwareVersionStateRequest* internal_default_instance() {
    return reinterpret_cast<const SoftwareVersionStateRequest*>(
               &_SoftwareVersionStateRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(SoftwareVersionStateRequest& a, SoftwareVersionStateRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SoftwareVersionStateRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SoftwareVersionStateRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SoftwareVersionStateRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SoftwareVersionStateRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SoftwareVersionStateRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SoftwareVersionStateRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SoftwareVersionStateRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.software.SoftwareVersionStateRequest";
  }
  protected:
  explicit SoftwareVersionStateRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTsFieldNumber = 1,
    kGetHostStatesFieldNumber = 2,
  };
  // .carbon.frontend.util.Timestamp ts = 1;
  bool has_ts() const;
  private:
  bool _internal_has_ts() const;
  public:
  void clear_ts();
  const ::carbon::frontend::util::Timestamp& ts() const;
  PROTOBUF_NODISCARD ::carbon::frontend::util::Timestamp* release_ts();
  ::carbon::frontend::util::Timestamp* mutable_ts();
  void set_allocated_ts(::carbon::frontend::util::Timestamp* ts);
  private:
  const ::carbon::frontend::util::Timestamp& _internal_ts() const;
  ::carbon::frontend::util::Timestamp* _internal_mutable_ts();
  public:
  void unsafe_arena_set_allocated_ts(
      ::carbon::frontend::util::Timestamp* ts);
  ::carbon::frontend::util::Timestamp* unsafe_arena_release_ts();

  // bool get_host_states = 2;
  void clear_get_host_states();
  bool get_host_states() const;
  void set_get_host_states(bool value);
  private:
  bool _internal_get_host_states() const;
  void _internal_set_get_host_states(bool value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.software.SoftwareVersionStateRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::frontend::util::Timestamp* ts_;
  bool get_host_states_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fsoftware_2eproto;
};
// -------------------------------------------------------------------

class UpdateHostRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.frontend.software.UpdateHostRequest) */ {
 public:
  inline UpdateHostRequest() : UpdateHostRequest(nullptr) {}
  ~UpdateHostRequest() override;
  explicit constexpr UpdateHostRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpdateHostRequest(const UpdateHostRequest& from);
  UpdateHostRequest(UpdateHostRequest&& from) noexcept
    : UpdateHostRequest() {
    *this = ::std::move(from);
  }

  inline UpdateHostRequest& operator=(const UpdateHostRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpdateHostRequest& operator=(UpdateHostRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpdateHostRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpdateHostRequest* internal_default_instance() {
    return reinterpret_cast<const UpdateHostRequest*>(
               &_UpdateHostRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(UpdateHostRequest& a, UpdateHostRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UpdateHostRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpdateHostRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpdateHostRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpdateHostRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UpdateHostRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UpdateHostRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpdateHostRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.frontend.software.UpdateHostRequest";
  }
  protected:
  explicit UpdateHostRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHostIdFieldNumber = 1,
  };
  // uint32 host_id = 1;
  void clear_host_id();
  uint32_t host_id() const;
  void set_host_id(uint32_t value);
  private:
  uint32_t _internal_host_id() const;
  void _internal_set_host_id(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.frontend.software.UpdateHostRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint32_t host_id_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_frontend_2fproto_2fsoftware_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SoftwareVersion

// string tag = 1;
inline void SoftwareVersion::clear_tag() {
  tag_.ClearToEmpty();
}
inline const std::string& SoftwareVersion::tag() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersion.tag)
  return _internal_tag();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SoftwareVersion::set_tag(ArgT0&& arg0, ArgT... args) {
 
 tag_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.software.SoftwareVersion.tag)
}
inline std::string* SoftwareVersion::mutable_tag() {
  std::string* _s = _internal_mutable_tag();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.SoftwareVersion.tag)
  return _s;
}
inline const std::string& SoftwareVersion::_internal_tag() const {
  return tag_.Get();
}
inline void SoftwareVersion::_internal_set_tag(const std::string& value) {
  
  tag_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SoftwareVersion::_internal_mutable_tag() {
  
  return tag_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SoftwareVersion::release_tag() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.SoftwareVersion.tag)
  return tag_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SoftwareVersion::set_allocated_tag(std::string* tag) {
  if (tag != nullptr) {
    
  } else {
    
  }
  tag_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tag,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (tag_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    tag_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.SoftwareVersion.tag)
}

// bool available = 2;
inline void SoftwareVersion::clear_available() {
  available_ = false;
}
inline bool SoftwareVersion::_internal_available() const {
  return available_;
}
inline bool SoftwareVersion::available() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersion.available)
  return _internal_available();
}
inline void SoftwareVersion::_internal_set_available(bool value) {
  
  available_ = value;
}
inline void SoftwareVersion::set_available(bool value) {
  _internal_set_available(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.SoftwareVersion.available)
}

// bool ready = 3;
inline void SoftwareVersion::clear_ready() {
  ready_ = false;
}
inline bool SoftwareVersion::_internal_ready() const {
  return ready_;
}
inline bool SoftwareVersion::ready() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersion.ready)
  return _internal_ready();
}
inline void SoftwareVersion::_internal_set_ready(bool value) {
  
  ready_ = value;
}
inline void SoftwareVersion::set_ready(bool value) {
  _internal_set_ready(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.SoftwareVersion.ready)
}

// -------------------------------------------------------------------

// HostSoftwareVersionState

// string host_name = 1;
inline void HostSoftwareVersionState::clear_host_name() {
  host_name_.ClearToEmpty();
}
inline const std::string& HostSoftwareVersionState::host_name() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.HostSoftwareVersionState.host_name)
  return _internal_host_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void HostSoftwareVersionState::set_host_name(ArgT0&& arg0, ArgT... args) {
 
 host_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.frontend.software.HostSoftwareVersionState.host_name)
}
inline std::string* HostSoftwareVersionState::mutable_host_name() {
  std::string* _s = _internal_mutable_host_name();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.HostSoftwareVersionState.host_name)
  return _s;
}
inline const std::string& HostSoftwareVersionState::_internal_host_name() const {
  return host_name_.Get();
}
inline void HostSoftwareVersionState::_internal_set_host_name(const std::string& value) {
  
  host_name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* HostSoftwareVersionState::_internal_mutable_host_name() {
  
  return host_name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* HostSoftwareVersionState::release_host_name() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.HostSoftwareVersionState.host_name)
  return host_name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void HostSoftwareVersionState::set_allocated_host_name(std::string* host_name) {
  if (host_name != nullptr) {
    
  } else {
    
  }
  host_name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), host_name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (host_name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    host_name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.HostSoftwareVersionState.host_name)
}

// uint32 host_id = 2;
inline void HostSoftwareVersionState::clear_host_id() {
  host_id_ = 0u;
}
inline uint32_t HostSoftwareVersionState::_internal_host_id() const {
  return host_id_;
}
inline uint32_t HostSoftwareVersionState::host_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.HostSoftwareVersionState.host_id)
  return _internal_host_id();
}
inline void HostSoftwareVersionState::_internal_set_host_id(uint32_t value) {
  
  host_id_ = value;
}
inline void HostSoftwareVersionState::set_host_id(uint32_t value) {
  _internal_set_host_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.HostSoftwareVersionState.host_id)
}

// bool active = 3;
inline void HostSoftwareVersionState::clear_active() {
  active_ = false;
}
inline bool HostSoftwareVersionState::_internal_active() const {
  return active_;
}
inline bool HostSoftwareVersionState::active() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.HostSoftwareVersionState.active)
  return _internal_active();
}
inline void HostSoftwareVersionState::_internal_set_active(bool value) {
  
  active_ = value;
}
inline void HostSoftwareVersionState::set_active(bool value) {
  _internal_set_active(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.HostSoftwareVersionState.active)
}

// .carbon.frontend.software.SoftwareVersion current = 4;
inline bool HostSoftwareVersionState::_internal_has_current() const {
  return this != internal_default_instance() && current_ != nullptr;
}
inline bool HostSoftwareVersionState::has_current() const {
  return _internal_has_current();
}
inline void HostSoftwareVersionState::clear_current() {
  if (GetArenaForAllocation() == nullptr && current_ != nullptr) {
    delete current_;
  }
  current_ = nullptr;
}
inline const ::carbon::frontend::software::SoftwareVersion& HostSoftwareVersionState::_internal_current() const {
  const ::carbon::frontend::software::SoftwareVersion* p = current_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::software::SoftwareVersion&>(
      ::carbon::frontend::software::_SoftwareVersion_default_instance_);
}
inline const ::carbon::frontend::software::SoftwareVersion& HostSoftwareVersionState::current() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.HostSoftwareVersionState.current)
  return _internal_current();
}
inline void HostSoftwareVersionState::unsafe_arena_set_allocated_current(
    ::carbon::frontend::software::SoftwareVersion* current) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_);
  }
  current_ = current;
  if (current) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.software.HostSoftwareVersionState.current)
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::release_current() {
  
  ::carbon::frontend::software::SoftwareVersion* temp = current_;
  current_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::unsafe_arena_release_current() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.HostSoftwareVersionState.current)
  
  ::carbon::frontend::software::SoftwareVersion* temp = current_;
  current_ = nullptr;
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::_internal_mutable_current() {
  
  if (current_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::software::SoftwareVersion>(GetArenaForAllocation());
    current_ = p;
  }
  return current_;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::mutable_current() {
  ::carbon::frontend::software::SoftwareVersion* _msg = _internal_mutable_current();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.HostSoftwareVersionState.current)
  return _msg;
}
inline void HostSoftwareVersionState::set_allocated_current(::carbon::frontend::software::SoftwareVersion* current) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete current_;
  }
  if (current) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::software::SoftwareVersion>::GetOwningArena(current);
    if (message_arena != submessage_arena) {
      current = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, current, submessage_arena);
    }
    
  } else {
    
  }
  current_ = current;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.HostSoftwareVersionState.current)
}

// .carbon.frontend.software.SoftwareVersion target = 5;
inline bool HostSoftwareVersionState::_internal_has_target() const {
  return this != internal_default_instance() && target_ != nullptr;
}
inline bool HostSoftwareVersionState::has_target() const {
  return _internal_has_target();
}
inline void HostSoftwareVersionState::clear_target() {
  if (GetArenaForAllocation() == nullptr && target_ != nullptr) {
    delete target_;
  }
  target_ = nullptr;
}
inline const ::carbon::frontend::software::SoftwareVersion& HostSoftwareVersionState::_internal_target() const {
  const ::carbon::frontend::software::SoftwareVersion* p = target_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::software::SoftwareVersion&>(
      ::carbon::frontend::software::_SoftwareVersion_default_instance_);
}
inline const ::carbon::frontend::software::SoftwareVersion& HostSoftwareVersionState::target() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.HostSoftwareVersionState.target)
  return _internal_target();
}
inline void HostSoftwareVersionState::unsafe_arena_set_allocated_target(
    ::carbon::frontend::software::SoftwareVersion* target) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(target_);
  }
  target_ = target;
  if (target) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.software.HostSoftwareVersionState.target)
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::release_target() {
  
  ::carbon::frontend::software::SoftwareVersion* temp = target_;
  target_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::unsafe_arena_release_target() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.HostSoftwareVersionState.target)
  
  ::carbon::frontend::software::SoftwareVersion* temp = target_;
  target_ = nullptr;
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::_internal_mutable_target() {
  
  if (target_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::software::SoftwareVersion>(GetArenaForAllocation());
    target_ = p;
  }
  return target_;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::mutable_target() {
  ::carbon::frontend::software::SoftwareVersion* _msg = _internal_mutable_target();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.HostSoftwareVersionState.target)
  return _msg;
}
inline void HostSoftwareVersionState::set_allocated_target(::carbon::frontend::software::SoftwareVersion* target) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete target_;
  }
  if (target) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::software::SoftwareVersion>::GetOwningArena(target);
    if (message_arena != submessage_arena) {
      target = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target, submessage_arena);
    }
    
  } else {
    
  }
  target_ = target;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.HostSoftwareVersionState.target)
}

// .carbon.frontend.software.SoftwareVersion previous = 6;
inline bool HostSoftwareVersionState::_internal_has_previous() const {
  return this != internal_default_instance() && previous_ != nullptr;
}
inline bool HostSoftwareVersionState::has_previous() const {
  return _internal_has_previous();
}
inline void HostSoftwareVersionState::clear_previous() {
  if (GetArenaForAllocation() == nullptr && previous_ != nullptr) {
    delete previous_;
  }
  previous_ = nullptr;
}
inline const ::carbon::frontend::software::SoftwareVersion& HostSoftwareVersionState::_internal_previous() const {
  const ::carbon::frontend::software::SoftwareVersion* p = previous_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::software::SoftwareVersion&>(
      ::carbon::frontend::software::_SoftwareVersion_default_instance_);
}
inline const ::carbon::frontend::software::SoftwareVersion& HostSoftwareVersionState::previous() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.HostSoftwareVersionState.previous)
  return _internal_previous();
}
inline void HostSoftwareVersionState::unsafe_arena_set_allocated_previous(
    ::carbon::frontend::software::SoftwareVersion* previous) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(previous_);
  }
  previous_ = previous;
  if (previous) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.software.HostSoftwareVersionState.previous)
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::release_previous() {
  
  ::carbon::frontend::software::SoftwareVersion* temp = previous_;
  previous_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::unsafe_arena_release_previous() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.HostSoftwareVersionState.previous)
  
  ::carbon::frontend::software::SoftwareVersion* temp = previous_;
  previous_ = nullptr;
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::_internal_mutable_previous() {
  
  if (previous_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::software::SoftwareVersion>(GetArenaForAllocation());
    previous_ = p;
  }
  return previous_;
}
inline ::carbon::frontend::software::SoftwareVersion* HostSoftwareVersionState::mutable_previous() {
  ::carbon::frontend::software::SoftwareVersion* _msg = _internal_mutable_previous();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.HostSoftwareVersionState.previous)
  return _msg;
}
inline void HostSoftwareVersionState::set_allocated_previous(::carbon::frontend::software::SoftwareVersion* previous) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete previous_;
  }
  if (previous) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::software::SoftwareVersion>::GetOwningArena(previous);
    if (message_arena != submessage_arena) {
      previous = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, previous, submessage_arena);
    }
    
  } else {
    
  }
  previous_ = previous;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.HostSoftwareVersionState.previous)
}

// bool updating = 7;
inline void HostSoftwareVersionState::clear_updating() {
  updating_ = false;
}
inline bool HostSoftwareVersionState::_internal_updating() const {
  return updating_;
}
inline bool HostSoftwareVersionState::updating() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.HostSoftwareVersionState.updating)
  return _internal_updating();
}
inline void HostSoftwareVersionState::_internal_set_updating(bool value) {
  
  updating_ = value;
}
inline void HostSoftwareVersionState::set_updating(bool value) {
  _internal_set_updating(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.HostSoftwareVersionState.updating)
}

// -------------------------------------------------------------------

// SoftwareVersionState

// .carbon.frontend.util.Timestamp ts = 1;
inline bool SoftwareVersionState::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool SoftwareVersionState::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& SoftwareVersionState::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& SoftwareVersionState::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionState.ts)
  return _internal_ts();
}
inline void SoftwareVersionState::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.software.SoftwareVersionState.ts)
}
inline ::carbon::frontend::util::Timestamp* SoftwareVersionState::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* SoftwareVersionState::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.SoftwareVersionState.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* SoftwareVersionState::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* SoftwareVersionState::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.SoftwareVersionState.ts)
  return _msg;
}
inline void SoftwareVersionState::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.SoftwareVersionState.ts)
}

// .carbon.frontend.software.SoftwareVersion current = 2;
inline bool SoftwareVersionState::_internal_has_current() const {
  return this != internal_default_instance() && current_ != nullptr;
}
inline bool SoftwareVersionState::has_current() const {
  return _internal_has_current();
}
inline void SoftwareVersionState::clear_current() {
  if (GetArenaForAllocation() == nullptr && current_ != nullptr) {
    delete current_;
  }
  current_ = nullptr;
}
inline const ::carbon::frontend::software::SoftwareVersion& SoftwareVersionState::_internal_current() const {
  const ::carbon::frontend::software::SoftwareVersion* p = current_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::software::SoftwareVersion&>(
      ::carbon::frontend::software::_SoftwareVersion_default_instance_);
}
inline const ::carbon::frontend::software::SoftwareVersion& SoftwareVersionState::current() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionState.current)
  return _internal_current();
}
inline void SoftwareVersionState::unsafe_arena_set_allocated_current(
    ::carbon::frontend::software::SoftwareVersion* current) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(current_);
  }
  current_ = current;
  if (current) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.software.SoftwareVersionState.current)
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::release_current() {
  
  ::carbon::frontend::software::SoftwareVersion* temp = current_;
  current_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::unsafe_arena_release_current() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.SoftwareVersionState.current)
  
  ::carbon::frontend::software::SoftwareVersion* temp = current_;
  current_ = nullptr;
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::_internal_mutable_current() {
  
  if (current_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::software::SoftwareVersion>(GetArenaForAllocation());
    current_ = p;
  }
  return current_;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::mutable_current() {
  ::carbon::frontend::software::SoftwareVersion* _msg = _internal_mutable_current();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.SoftwareVersionState.current)
  return _msg;
}
inline void SoftwareVersionState::set_allocated_current(::carbon::frontend::software::SoftwareVersion* current) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete current_;
  }
  if (current) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::software::SoftwareVersion>::GetOwningArena(current);
    if (message_arena != submessage_arena) {
      current = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, current, submessage_arena);
    }
    
  } else {
    
  }
  current_ = current;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.SoftwareVersionState.current)
}

// .carbon.frontend.software.SoftwareVersion target = 3;
inline bool SoftwareVersionState::_internal_has_target() const {
  return this != internal_default_instance() && target_ != nullptr;
}
inline bool SoftwareVersionState::has_target() const {
  return _internal_has_target();
}
inline void SoftwareVersionState::clear_target() {
  if (GetArenaForAllocation() == nullptr && target_ != nullptr) {
    delete target_;
  }
  target_ = nullptr;
}
inline const ::carbon::frontend::software::SoftwareVersion& SoftwareVersionState::_internal_target() const {
  const ::carbon::frontend::software::SoftwareVersion* p = target_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::software::SoftwareVersion&>(
      ::carbon::frontend::software::_SoftwareVersion_default_instance_);
}
inline const ::carbon::frontend::software::SoftwareVersion& SoftwareVersionState::target() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionState.target)
  return _internal_target();
}
inline void SoftwareVersionState::unsafe_arena_set_allocated_target(
    ::carbon::frontend::software::SoftwareVersion* target) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(target_);
  }
  target_ = target;
  if (target) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.software.SoftwareVersionState.target)
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::release_target() {
  
  ::carbon::frontend::software::SoftwareVersion* temp = target_;
  target_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::unsafe_arena_release_target() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.SoftwareVersionState.target)
  
  ::carbon::frontend::software::SoftwareVersion* temp = target_;
  target_ = nullptr;
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::_internal_mutable_target() {
  
  if (target_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::software::SoftwareVersion>(GetArenaForAllocation());
    target_ = p;
  }
  return target_;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::mutable_target() {
  ::carbon::frontend::software::SoftwareVersion* _msg = _internal_mutable_target();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.SoftwareVersionState.target)
  return _msg;
}
inline void SoftwareVersionState::set_allocated_target(::carbon::frontend::software::SoftwareVersion* target) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete target_;
  }
  if (target) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::software::SoftwareVersion>::GetOwningArena(target);
    if (message_arena != submessage_arena) {
      target = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, target, submessage_arena);
    }
    
  } else {
    
  }
  target_ = target;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.SoftwareVersionState.target)
}

// .carbon.frontend.software.SoftwareVersion previous = 4;
inline bool SoftwareVersionState::_internal_has_previous() const {
  return this != internal_default_instance() && previous_ != nullptr;
}
inline bool SoftwareVersionState::has_previous() const {
  return _internal_has_previous();
}
inline void SoftwareVersionState::clear_previous() {
  if (GetArenaForAllocation() == nullptr && previous_ != nullptr) {
    delete previous_;
  }
  previous_ = nullptr;
}
inline const ::carbon::frontend::software::SoftwareVersion& SoftwareVersionState::_internal_previous() const {
  const ::carbon::frontend::software::SoftwareVersion* p = previous_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::software::SoftwareVersion&>(
      ::carbon::frontend::software::_SoftwareVersion_default_instance_);
}
inline const ::carbon::frontend::software::SoftwareVersion& SoftwareVersionState::previous() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionState.previous)
  return _internal_previous();
}
inline void SoftwareVersionState::unsafe_arena_set_allocated_previous(
    ::carbon::frontend::software::SoftwareVersion* previous) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(previous_);
  }
  previous_ = previous;
  if (previous) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.software.SoftwareVersionState.previous)
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::release_previous() {
  
  ::carbon::frontend::software::SoftwareVersion* temp = previous_;
  previous_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::unsafe_arena_release_previous() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.SoftwareVersionState.previous)
  
  ::carbon::frontend::software::SoftwareVersion* temp = previous_;
  previous_ = nullptr;
  return temp;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::_internal_mutable_previous() {
  
  if (previous_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::software::SoftwareVersion>(GetArenaForAllocation());
    previous_ = p;
  }
  return previous_;
}
inline ::carbon::frontend::software::SoftwareVersion* SoftwareVersionState::mutable_previous() {
  ::carbon::frontend::software::SoftwareVersion* _msg = _internal_mutable_previous();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.SoftwareVersionState.previous)
  return _msg;
}
inline void SoftwareVersionState::set_allocated_previous(::carbon::frontend::software::SoftwareVersion* previous) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete previous_;
  }
  if (previous) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::frontend::software::SoftwareVersion>::GetOwningArena(previous);
    if (message_arena != submessage_arena) {
      previous = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, previous, submessage_arena);
    }
    
  } else {
    
  }
  previous_ = previous;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.SoftwareVersionState.previous)
}

// bool updating = 5;
inline void SoftwareVersionState::clear_updating() {
  updating_ = false;
}
inline bool SoftwareVersionState::_internal_updating() const {
  return updating_;
}
inline bool SoftwareVersionState::updating() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionState.updating)
  return _internal_updating();
}
inline void SoftwareVersionState::_internal_set_updating(bool value) {
  
  updating_ = value;
}
inline void SoftwareVersionState::set_updating(bool value) {
  _internal_set_updating(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.SoftwareVersionState.updating)
}

// bool show_software_update_to_user = 6;
inline void SoftwareVersionState::clear_show_software_update_to_user() {
  show_software_update_to_user_ = false;
}
inline bool SoftwareVersionState::_internal_show_software_update_to_user() const {
  return show_software_update_to_user_;
}
inline bool SoftwareVersionState::show_software_update_to_user() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionState.show_software_update_to_user)
  return _internal_show_software_update_to_user();
}
inline void SoftwareVersionState::_internal_set_show_software_update_to_user(bool value) {
  
  show_software_update_to_user_ = value;
}
inline void SoftwareVersionState::set_show_software_update_to_user(bool value) {
  _internal_set_show_software_update_to_user(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.SoftwareVersionState.show_software_update_to_user)
}

// repeated .carbon.frontend.software.HostSoftwareVersionState host_states = 7;
inline int SoftwareVersionState::_internal_host_states_size() const {
  return host_states_.size();
}
inline int SoftwareVersionState::host_states_size() const {
  return _internal_host_states_size();
}
inline void SoftwareVersionState::clear_host_states() {
  host_states_.Clear();
}
inline ::carbon::frontend::software::HostSoftwareVersionState* SoftwareVersionState::mutable_host_states(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.SoftwareVersionState.host_states)
  return host_states_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::software::HostSoftwareVersionState >*
SoftwareVersionState::mutable_host_states() {
  // @@protoc_insertion_point(field_mutable_list:carbon.frontend.software.SoftwareVersionState.host_states)
  return &host_states_;
}
inline const ::carbon::frontend::software::HostSoftwareVersionState& SoftwareVersionState::_internal_host_states(int index) const {
  return host_states_.Get(index);
}
inline const ::carbon::frontend::software::HostSoftwareVersionState& SoftwareVersionState::host_states(int index) const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionState.host_states)
  return _internal_host_states(index);
}
inline ::carbon::frontend::software::HostSoftwareVersionState* SoftwareVersionState::_internal_add_host_states() {
  return host_states_.Add();
}
inline ::carbon::frontend::software::HostSoftwareVersionState* SoftwareVersionState::add_host_states() {
  ::carbon::frontend::software::HostSoftwareVersionState* _add = _internal_add_host_states();
  // @@protoc_insertion_point(field_add:carbon.frontend.software.SoftwareVersionState.host_states)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::frontend::software::HostSoftwareVersionState >&
SoftwareVersionState::host_states() const {
  // @@protoc_insertion_point(field_list:carbon.frontend.software.SoftwareVersionState.host_states)
  return host_states_;
}

// bool version_mismatch = 8;
inline void SoftwareVersionState::clear_version_mismatch() {
  version_mismatch_ = false;
}
inline bool SoftwareVersionState::_internal_version_mismatch() const {
  return version_mismatch_;
}
inline bool SoftwareVersionState::version_mismatch() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionState.version_mismatch)
  return _internal_version_mismatch();
}
inline void SoftwareVersionState::_internal_set_version_mismatch(bool value) {
  
  version_mismatch_ = value;
}
inline void SoftwareVersionState::set_version_mismatch(bool value) {
  _internal_set_version_mismatch(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.SoftwareVersionState.version_mismatch)
}

// -------------------------------------------------------------------

// SoftwareVersionStateRequest

// .carbon.frontend.util.Timestamp ts = 1;
inline bool SoftwareVersionStateRequest::_internal_has_ts() const {
  return this != internal_default_instance() && ts_ != nullptr;
}
inline bool SoftwareVersionStateRequest::has_ts() const {
  return _internal_has_ts();
}
inline const ::carbon::frontend::util::Timestamp& SoftwareVersionStateRequest::_internal_ts() const {
  const ::carbon::frontend::util::Timestamp* p = ts_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::frontend::util::Timestamp&>(
      ::carbon::frontend::util::_Timestamp_default_instance_);
}
inline const ::carbon::frontend::util::Timestamp& SoftwareVersionStateRequest::ts() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionStateRequest.ts)
  return _internal_ts();
}
inline void SoftwareVersionStateRequest::unsafe_arena_set_allocated_ts(
    ::carbon::frontend::util::Timestamp* ts) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  ts_ = ts;
  if (ts) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.frontend.software.SoftwareVersionStateRequest.ts)
}
inline ::carbon::frontend::util::Timestamp* SoftwareVersionStateRequest::release_ts() {
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::frontend::util::Timestamp* SoftwareVersionStateRequest::unsafe_arena_release_ts() {
  // @@protoc_insertion_point(field_release:carbon.frontend.software.SoftwareVersionStateRequest.ts)
  
  ::carbon::frontend::util::Timestamp* temp = ts_;
  ts_ = nullptr;
  return temp;
}
inline ::carbon::frontend::util::Timestamp* SoftwareVersionStateRequest::_internal_mutable_ts() {
  
  if (ts_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::frontend::util::Timestamp>(GetArenaForAllocation());
    ts_ = p;
  }
  return ts_;
}
inline ::carbon::frontend::util::Timestamp* SoftwareVersionStateRequest::mutable_ts() {
  ::carbon::frontend::util::Timestamp* _msg = _internal_mutable_ts();
  // @@protoc_insertion_point(field_mutable:carbon.frontend.software.SoftwareVersionStateRequest.ts)
  return _msg;
}
inline void SoftwareVersionStateRequest::set_allocated_ts(::carbon::frontend::util::Timestamp* ts) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts_);
  }
  if (ts) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(ts));
    if (message_arena != submessage_arena) {
      ts = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, ts, submessage_arena);
    }
    
  } else {
    
  }
  ts_ = ts;
  // @@protoc_insertion_point(field_set_allocated:carbon.frontend.software.SoftwareVersionStateRequest.ts)
}

// bool get_host_states = 2;
inline void SoftwareVersionStateRequest::clear_get_host_states() {
  get_host_states_ = false;
}
inline bool SoftwareVersionStateRequest::_internal_get_host_states() const {
  return get_host_states_;
}
inline bool SoftwareVersionStateRequest::get_host_states() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.SoftwareVersionStateRequest.get_host_states)
  return _internal_get_host_states();
}
inline void SoftwareVersionStateRequest::_internal_set_get_host_states(bool value) {
  
  get_host_states_ = value;
}
inline void SoftwareVersionStateRequest::set_get_host_states(bool value) {
  _internal_set_get_host_states(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.SoftwareVersionStateRequest.get_host_states)
}

// -------------------------------------------------------------------

// UpdateHostRequest

// uint32 host_id = 1;
inline void UpdateHostRequest::clear_host_id() {
  host_id_ = 0u;
}
inline uint32_t UpdateHostRequest::_internal_host_id() const {
  return host_id_;
}
inline uint32_t UpdateHostRequest::host_id() const {
  // @@protoc_insertion_point(field_get:carbon.frontend.software.UpdateHostRequest.host_id)
  return _internal_host_id();
}
inline void UpdateHostRequest::_internal_set_host_id(uint32_t value) {
  
  host_id_ = value;
}
inline void UpdateHostRequest::set_host_id(uint32_t value) {
  _internal_set_host_id(value);
  // @@protoc_insertion_point(field_set:carbon.frontend.software.UpdateHostRequest.host_id)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace software
}  // namespace frontend
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_frontend_2fproto_2fsoftware_2eproto

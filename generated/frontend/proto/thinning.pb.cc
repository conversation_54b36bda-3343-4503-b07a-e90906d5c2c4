// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: frontend/proto/thinning.proto

#include "frontend/proto/thinning.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace frontend {
namespace thinning {
constexpr GetNextConfigurationsResponse::GetNextConfigurationsResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : definitions_()
  , active_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextConfigurationsResponseDefaultTypeInternal {
  constexpr GetNextConfigurationsResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextConfigurationsResponseDefaultTypeInternal() {}
  union {
    GetNextConfigurationsResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextConfigurationsResponseDefaultTypeInternal _GetNextConfigurationsResponse_default_instance_;
constexpr GetNextActiveConfResponse::GetNextActiveConfResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ts_(nullptr){}
struct GetNextActiveConfResponseDefaultTypeInternal {
  constexpr GetNextActiveConfResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetNextActiveConfResponseDefaultTypeInternal() {}
  union {
    GetNextActiveConfResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetNextActiveConfResponseDefaultTypeInternal _GetNextActiveConfResponse_default_instance_;
constexpr DefineConfigurationRequest::DefineConfigurationRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : definition_(nullptr)
  , set_active_(false)
  , ver_(0)
{}
struct DefineConfigurationRequestDefaultTypeInternal {
  constexpr DefineConfigurationRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DefineConfigurationRequestDefaultTypeInternal() {}
  union {
    DefineConfigurationRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DefineConfigurationRequestDefaultTypeInternal _DefineConfigurationRequest_default_instance_;
constexpr DefineConfigurationResponse::DefineConfigurationResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct DefineConfigurationResponseDefaultTypeInternal {
  constexpr DefineConfigurationResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DefineConfigurationResponseDefaultTypeInternal() {}
  union {
    DefineConfigurationResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DefineConfigurationResponseDefaultTypeInternal _DefineConfigurationResponse_default_instance_;
constexpr SetActiveConfigRequest::SetActiveConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ver_(0)
{}
struct SetActiveConfigRequestDefaultTypeInternal {
  constexpr SetActiveConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveConfigRequestDefaultTypeInternal() {}
  union {
    SetActiveConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveConfigRequestDefaultTypeInternal _SetActiveConfigRequest_default_instance_;
constexpr SetActiveConfigResponse::SetActiveConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetActiveConfigResponseDefaultTypeInternal {
  constexpr SetActiveConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetActiveConfigResponseDefaultTypeInternal() {}
  union {
    SetActiveConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetActiveConfigResponseDefaultTypeInternal _SetActiveConfigResponse_default_instance_;
constexpr DeleteConfigRequest::DeleteConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , new_active_id_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , ver_(0)
{}
struct DeleteConfigRequestDefaultTypeInternal {
  constexpr DeleteConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteConfigRequestDefaultTypeInternal() {}
  union {
    DeleteConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteConfigRequestDefaultTypeInternal _DeleteConfigRequest_default_instance_;
constexpr DeleteConfigResponse::DeleteConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct DeleteConfigResponseDefaultTypeInternal {
  constexpr DeleteConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeleteConfigResponseDefaultTypeInternal() {}
  union {
    DeleteConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeleteConfigResponseDefaultTypeInternal _DeleteConfigResponse_default_instance_;
}  // namespace thinning
}  // namespace frontend
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_frontend_2fproto_2fthinning_2eproto[8];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_frontend_2fproto_2fthinning_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_frontend_2fproto_2fthinning_2eproto = nullptr;

const uint32_t TableStruct_frontend_2fproto_2fthinning_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::GetNextConfigurationsResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::GetNextConfigurationsResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::GetNextConfigurationsResponse, definitions_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::GetNextConfigurationsResponse, active_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::GetNextActiveConfResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::GetNextActiveConfResponse, ts_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::GetNextActiveConfResponse, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::GetNextActiveConfResponse, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DefineConfigurationRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DefineConfigurationRequest, definition_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DefineConfigurationRequest, set_active_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DefineConfigurationRequest, ver_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DefineConfigurationResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DefineConfigurationResponse, id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::SetActiveConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::SetActiveConfigRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::SetActiveConfigRequest, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::SetActiveConfigRequest, ver_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::SetActiveConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DeleteConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DeleteConfigRequest, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DeleteConfigRequest, id_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DeleteConfigRequest, ver_),
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DeleteConfigRequest, new_active_id_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::frontend::thinning::DeleteConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::frontend::thinning::GetNextConfigurationsResponse)},
  { 9, -1, -1, sizeof(::carbon::frontend::thinning::GetNextActiveConfResponse)},
  { 18, -1, -1, sizeof(::carbon::frontend::thinning::DefineConfigurationRequest)},
  { 27, -1, -1, sizeof(::carbon::frontend::thinning::DefineConfigurationResponse)},
  { 34, -1, -1, sizeof(::carbon::frontend::thinning::SetActiveConfigRequest)},
  { 43, -1, -1, sizeof(::carbon::frontend::thinning::SetActiveConfigResponse)},
  { 49, -1, -1, sizeof(::carbon::frontend::thinning::DeleteConfigRequest)},
  { 59, -1, -1, sizeof(::carbon::frontend::thinning::DeleteConfigResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::thinning::_GetNextConfigurationsResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::thinning::_GetNextActiveConfResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::thinning::_DefineConfigurationRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::thinning::_DefineConfigurationResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::thinning::_SetActiveConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::thinning::_SetActiveConfigResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::thinning::_DeleteConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::frontend::thinning::_DeleteConfigResponse_default_instance_),
};

const char descriptor_table_protodef_frontend_2fproto_2fthinning_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035frontend/proto/thinning.proto\022\030carbon."
  "frontend.thinning\032\035proto/thinning/thinni"
  "ng.proto\032\031frontend/proto/util.proto\"\227\001\n\035"
  "GetNextConfigurationsResponse\022+\n\002ts\030\001 \001("
  "\0132\037.carbon.frontend.util.Timestamp\0226\n\013de"
  "finitions\030\002 \003(\0132!.carbon.thinning.Config"
  "Definition\022\021\n\tactive_id\030\003 \001(\t\"f\n\031GetNext"
  "ActiveConfResponse\022+\n\002ts\030\001 \001(\0132\037.carbon."
  "frontend.util.Timestamp\022\020\n\004name\030\002 \001(\tB\002\030"
  "\001\022\n\n\002id\030\003 \001(\t\"\237\001\n\032DefineConfigurationReq"
  "uest\0225\n\ndefinition\030\001 \001(\0132!.carbon.thinni"
  "ng.ConfigDefinition\022\022\n\nset_active\030\002 \001(\010\022"
  "6\n\003ver\030\003 \001(\0162).carbon.frontend.thinning."
  "ThinningConfVer\")\n\033DefineConfigurationRe"
  "sponse\022\n\n\002id\030\001 \001(\t\"n\n\026SetActiveConfigReq"
  "uest\022\020\n\004name\030\001 \001(\tB\002\030\001\022\n\n\002id\030\002 \001(\t\0226\n\003ve"
  "r\030\003 \001(\0162).carbon.frontend.thinning.Thinn"
  "ingConfVer\"\031\n\027SetActiveConfigResponse\"\202\001"
  "\n\023DeleteConfigRequest\022\020\n\004name\030\001 \001(\tB\002\030\001\022"
  "\n\n\002id\030\002 \001(\t\0226\n\003ver\030\003 \001(\0162).carbon.fronte"
  "nd.thinning.ThinningConfVer\022\025\n\rnew_activ"
  "e_id\030\004 \001(\t\"\026\n\024DeleteConfigResponse*5\n\017Th"
  "inningConfVer\022\020\n\014THIN_CONF_V1\020\000\022\020\n\014THIN_"
  "CONF_V2\020\0012\333\004\n\017ThinningService\022q\n\025GetNext"
  "Configurations\022\037.carbon.frontend.util.Ti"
  "mestamp\0327.carbon.frontend.thinning.GetNe"
  "xtConfigurationsResponse\022i\n\021GetNextActiv"
  "eConf\022\037.carbon.frontend.util.Timestamp\0323"
  ".carbon.frontend.thinning.GetNextActiveC"
  "onfResponse\022\202\001\n\023DefineConfiguration\0224.ca"
  "rbon.frontend.thinning.DefineConfigurati"
  "onRequest\0325.carbon.frontend.thinning.Def"
  "ineConfigurationResponse\022v\n\017SetActiveCon"
  "fig\0220.carbon.frontend.thinning.SetActive"
  "ConfigRequest\0321.carbon.frontend.thinning"
  ".SetActiveConfigResponse\022m\n\014DeleteConfig"
  "\022-.carbon.frontend.thinning.DeleteConfig"
  "Request\032..carbon.frontend.thinning.Delet"
  "eConfigResponseB\020Z\016proto/frontendb\006proto"
  "3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_frontend_2fproto_2fthinning_2eproto_deps[2] = {
  &::descriptor_table_frontend_2fproto_2futil_2eproto,
  &::descriptor_table_proto_2fthinning_2fthinning_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_frontend_2fproto_2fthinning_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_frontend_2fproto_2fthinning_2eproto = {
  false, false, 1561, descriptor_table_protodef_frontend_2fproto_2fthinning_2eproto, "frontend/proto/thinning.proto", 
  &descriptor_table_frontend_2fproto_2fthinning_2eproto_once, descriptor_table_frontend_2fproto_2fthinning_2eproto_deps, 2, 8,
  schemas, file_default_instances, TableStruct_frontend_2fproto_2fthinning_2eproto::offsets,
  file_level_metadata_frontend_2fproto_2fthinning_2eproto, file_level_enum_descriptors_frontend_2fproto_2fthinning_2eproto, file_level_service_descriptors_frontend_2fproto_2fthinning_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_frontend_2fproto_2fthinning_2eproto_getter() {
  return &descriptor_table_frontend_2fproto_2fthinning_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_frontend_2fproto_2fthinning_2eproto(&descriptor_table_frontend_2fproto_2fthinning_2eproto);
namespace carbon {
namespace frontend {
namespace thinning {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ThinningConfVer_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_frontend_2fproto_2fthinning_2eproto);
  return file_level_enum_descriptors_frontend_2fproto_2fthinning_2eproto[0];
}
bool ThinningConfVer_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class GetNextConfigurationsResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextConfigurationsResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextConfigurationsResponse::_Internal::ts(const GetNextConfigurationsResponse* msg) {
  return *msg->ts_;
}
void GetNextConfigurationsResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
void GetNextConfigurationsResponse::clear_definitions() {
  definitions_.Clear();
}
GetNextConfigurationsResponse::GetNextConfigurationsResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  definitions_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.thinning.GetNextConfigurationsResponse)
}
GetNextConfigurationsResponse::GetNextConfigurationsResponse(const GetNextConfigurationsResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      definitions_(from.definitions_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  active_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_active_id().empty()) {
    active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_active_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.thinning.GetNextConfigurationsResponse)
}

inline void GetNextConfigurationsResponse::SharedCtor() {
active_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextConfigurationsResponse::~GetNextConfigurationsResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.thinning.GetNextConfigurationsResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextConfigurationsResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  active_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextConfigurationsResponse::ArenaDtor(void* object) {
  GetNextConfigurationsResponse* _this = reinterpret_cast< GetNextConfigurationsResponse* >(object);
  (void)_this;
}
void GetNextConfigurationsResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextConfigurationsResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextConfigurationsResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.thinning.GetNextConfigurationsResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  definitions_.Clear();
  active_id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextConfigurationsResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.thinning.ConfigDefinition definitions = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_definitions(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string active_id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_active_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.GetNextConfigurationsResponse.active_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextConfigurationsResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.thinning.GetNextConfigurationsResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // repeated .carbon.thinning.ConfigDefinition definitions = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_definitions_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_definitions(i), target, stream);
  }

  // string active_id = 3;
  if (!this->_internal_active_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_active_id().data(), static_cast<int>(this->_internal_active_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.GetNextConfigurationsResponse.active_id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_active_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.thinning.GetNextConfigurationsResponse)
  return target;
}

size_t GetNextConfigurationsResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.thinning.GetNextConfigurationsResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.thinning.ConfigDefinition definitions = 2;
  total_size += 1UL * this->_internal_definitions_size();
  for (const auto& msg : this->definitions_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string active_id = 3;
  if (!this->_internal_active_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_active_id());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextConfigurationsResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextConfigurationsResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextConfigurationsResponse::GetClassData() const { return &_class_data_; }

void GetNextConfigurationsResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextConfigurationsResponse *>(to)->MergeFrom(
      static_cast<const GetNextConfigurationsResponse &>(from));
}


void GetNextConfigurationsResponse::MergeFrom(const GetNextConfigurationsResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.thinning.GetNextConfigurationsResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  definitions_.MergeFrom(from.definitions_);
  if (!from._internal_active_id().empty()) {
    _internal_set_active_id(from._internal_active_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextConfigurationsResponse::CopyFrom(const GetNextConfigurationsResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.thinning.GetNextConfigurationsResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextConfigurationsResponse::IsInitialized() const {
  return true;
}

void GetNextConfigurationsResponse::InternalSwap(GetNextConfigurationsResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  definitions_.InternalSwap(&other->definitions_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &active_id_, lhs_arena,
      &other->active_id_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextConfigurationsResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fthinning_2eproto_getter, &descriptor_table_frontend_2fproto_2fthinning_2eproto_once,
      file_level_metadata_frontend_2fproto_2fthinning_2eproto[0]);
}

// ===================================================================

class GetNextActiveConfResponse::_Internal {
 public:
  static const ::carbon::frontend::util::Timestamp& ts(const GetNextActiveConfResponse* msg);
};

const ::carbon::frontend::util::Timestamp&
GetNextActiveConfResponse::_Internal::ts(const GetNextActiveConfResponse* msg) {
  return *msg->ts_;
}
void GetNextActiveConfResponse::clear_ts() {
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
}
GetNextActiveConfResponse::GetNextActiveConfResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.thinning.GetNextActiveConfResponse)
}
GetNextActiveConfResponse::GetNextActiveConfResponse(const GetNextActiveConfResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_ts()) {
    ts_ = new ::carbon::frontend::util::Timestamp(*from.ts_);
  } else {
    ts_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.thinning.GetNextActiveConfResponse)
}

inline void GetNextActiveConfResponse::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ts_ = nullptr;
}

GetNextActiveConfResponse::~GetNextActiveConfResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.thinning.GetNextActiveConfResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetNextActiveConfResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete ts_;
}

void GetNextActiveConfResponse::ArenaDtor(void* object) {
  GetNextActiveConfResponse* _this = reinterpret_cast< GetNextActiveConfResponse* >(object);
  (void)_this;
}
void GetNextActiveConfResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetNextActiveConfResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetNextActiveConfResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.thinning.GetNextActiveConfResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  id_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && ts_ != nullptr) {
    delete ts_;
  }
  ts_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetNextActiveConfResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.frontend.util.Timestamp ts = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_ts(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2 [deprecated = true];
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.GetNextActiveConfResponse.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string id = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.GetNextActiveConfResponse.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetNextActiveConfResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.thinning.GetNextActiveConfResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::ts(this), target, stream);
  }

  // string name = 2 [deprecated = true];
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.GetNextActiveConfResponse.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  // string id = 3;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.GetNextActiveConfResponse.id");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.thinning.GetNextActiveConfResponse)
  return target;
}

size_t GetNextActiveConfResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.thinning.GetNextActiveConfResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 2 [deprecated = true];
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string id = 3;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // .carbon.frontend.util.Timestamp ts = 1;
  if (this->_internal_has_ts()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *ts_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetNextActiveConfResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetNextActiveConfResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetNextActiveConfResponse::GetClassData() const { return &_class_data_; }

void GetNextActiveConfResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetNextActiveConfResponse *>(to)->MergeFrom(
      static_cast<const GetNextActiveConfResponse &>(from));
}


void GetNextActiveConfResponse::MergeFrom(const GetNextActiveConfResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.thinning.GetNextActiveConfResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_has_ts()) {
    _internal_mutable_ts()->::carbon::frontend::util::Timestamp::MergeFrom(from._internal_ts());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetNextActiveConfResponse::CopyFrom(const GetNextActiveConfResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.thinning.GetNextActiveConfResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetNextActiveConfResponse::IsInitialized() const {
  return true;
}

void GetNextActiveConfResponse::InternalSwap(GetNextActiveConfResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  swap(ts_, other->ts_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetNextActiveConfResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fthinning_2eproto_getter, &descriptor_table_frontend_2fproto_2fthinning_2eproto_once,
      file_level_metadata_frontend_2fproto_2fthinning_2eproto[1]);
}

// ===================================================================

class DefineConfigurationRequest::_Internal {
 public:
  static const ::carbon::thinning::ConfigDefinition& definition(const DefineConfigurationRequest* msg);
};

const ::carbon::thinning::ConfigDefinition&
DefineConfigurationRequest::_Internal::definition(const DefineConfigurationRequest* msg) {
  return *msg->definition_;
}
void DefineConfigurationRequest::clear_definition() {
  if (GetArenaForAllocation() == nullptr && definition_ != nullptr) {
    delete definition_;
  }
  definition_ = nullptr;
}
DefineConfigurationRequest::DefineConfigurationRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.thinning.DefineConfigurationRequest)
}
DefineConfigurationRequest::DefineConfigurationRequest(const DefineConfigurationRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_definition()) {
    definition_ = new ::carbon::thinning::ConfigDefinition(*from.definition_);
  } else {
    definition_ = nullptr;
  }
  ::memcpy(&set_active_, &from.set_active_,
    static_cast<size_t>(reinterpret_cast<char*>(&ver_) -
    reinterpret_cast<char*>(&set_active_)) + sizeof(ver_));
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.thinning.DefineConfigurationRequest)
}

inline void DefineConfigurationRequest::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&definition_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&ver_) -
    reinterpret_cast<char*>(&definition_)) + sizeof(ver_));
}

DefineConfigurationRequest::~DefineConfigurationRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.thinning.DefineConfigurationRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DefineConfigurationRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete definition_;
}

void DefineConfigurationRequest::ArenaDtor(void* object) {
  DefineConfigurationRequest* _this = reinterpret_cast< DefineConfigurationRequest* >(object);
  (void)_this;
}
void DefineConfigurationRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DefineConfigurationRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DefineConfigurationRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.thinning.DefineConfigurationRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && definition_ != nullptr) {
    delete definition_;
  }
  definition_ = nullptr;
  ::memset(&set_active_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&ver_) -
      reinterpret_cast<char*>(&set_active_)) + sizeof(ver_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DefineConfigurationRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.thinning.ConfigDefinition definition = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_definition(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool set_active = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          set_active_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.thinning.ThinningConfVer ver = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_ver(static_cast<::carbon::frontend::thinning::ThinningConfVer>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DefineConfigurationRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.thinning.DefineConfigurationRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.thinning.ConfigDefinition definition = 1;
  if (this->_internal_has_definition()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::definition(this), target, stream);
  }

  // bool set_active = 2;
  if (this->_internal_set_active() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(2, this->_internal_set_active(), target);
  }

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  if (this->_internal_ver() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_ver(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.thinning.DefineConfigurationRequest)
  return target;
}

size_t DefineConfigurationRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.thinning.DefineConfigurationRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.thinning.ConfigDefinition definition = 1;
  if (this->_internal_has_definition()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *definition_);
  }

  // bool set_active = 2;
  if (this->_internal_set_active() != 0) {
    total_size += 1 + 1;
  }

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  if (this->_internal_ver() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_ver());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DefineConfigurationRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DefineConfigurationRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DefineConfigurationRequest::GetClassData() const { return &_class_data_; }

void DefineConfigurationRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DefineConfigurationRequest *>(to)->MergeFrom(
      static_cast<const DefineConfigurationRequest &>(from));
}


void DefineConfigurationRequest::MergeFrom(const DefineConfigurationRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.thinning.DefineConfigurationRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_definition()) {
    _internal_mutable_definition()->::carbon::thinning::ConfigDefinition::MergeFrom(from._internal_definition());
  }
  if (from._internal_set_active() != 0) {
    _internal_set_set_active(from._internal_set_active());
  }
  if (from._internal_ver() != 0) {
    _internal_set_ver(from._internal_ver());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DefineConfigurationRequest::CopyFrom(const DefineConfigurationRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.thinning.DefineConfigurationRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DefineConfigurationRequest::IsInitialized() const {
  return true;
}

void DefineConfigurationRequest::InternalSwap(DefineConfigurationRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DefineConfigurationRequest, ver_)
      + sizeof(DefineConfigurationRequest::ver_)
      - PROTOBUF_FIELD_OFFSET(DefineConfigurationRequest, definition_)>(
          reinterpret_cast<char*>(&definition_),
          reinterpret_cast<char*>(&other->definition_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DefineConfigurationRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fthinning_2eproto_getter, &descriptor_table_frontend_2fproto_2fthinning_2eproto_once,
      file_level_metadata_frontend_2fproto_2fthinning_2eproto[2]);
}

// ===================================================================

class DefineConfigurationResponse::_Internal {
 public:
};

DefineConfigurationResponse::DefineConfigurationResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.thinning.DefineConfigurationResponse)
}
DefineConfigurationResponse::DefineConfigurationResponse(const DefineConfigurationResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.thinning.DefineConfigurationResponse)
}

inline void DefineConfigurationResponse::SharedCtor() {
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

DefineConfigurationResponse::~DefineConfigurationResponse() {
  // @@protoc_insertion_point(destructor:carbon.frontend.thinning.DefineConfigurationResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DefineConfigurationResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DefineConfigurationResponse::ArenaDtor(void* object) {
  DefineConfigurationResponse* _this = reinterpret_cast< DefineConfigurationResponse* >(object);
  (void)_this;
}
void DefineConfigurationResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DefineConfigurationResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DefineConfigurationResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.thinning.DefineConfigurationResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  id_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DefineConfigurationResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string id = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.DefineConfigurationResponse.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DefineConfigurationResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.thinning.DefineConfigurationResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.DefineConfigurationResponse.id");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.thinning.DefineConfigurationResponse)
  return target;
}

size_t DefineConfigurationResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.thinning.DefineConfigurationResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string id = 1;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DefineConfigurationResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DefineConfigurationResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DefineConfigurationResponse::GetClassData() const { return &_class_data_; }

void DefineConfigurationResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DefineConfigurationResponse *>(to)->MergeFrom(
      static_cast<const DefineConfigurationResponse &>(from));
}


void DefineConfigurationResponse::MergeFrom(const DefineConfigurationResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.thinning.DefineConfigurationResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DefineConfigurationResponse::CopyFrom(const DefineConfigurationResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.thinning.DefineConfigurationResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DefineConfigurationResponse::IsInitialized() const {
  return true;
}

void DefineConfigurationResponse::InternalSwap(DefineConfigurationResponse* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata DefineConfigurationResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fthinning_2eproto_getter, &descriptor_table_frontend_2fproto_2fthinning_2eproto_once,
      file_level_metadata_frontend_2fproto_2fthinning_2eproto[3]);
}

// ===================================================================

class SetActiveConfigRequest::_Internal {
 public:
};

SetActiveConfigRequest::SetActiveConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.thinning.SetActiveConfigRequest)
}
SetActiveConfigRequest::SetActiveConfigRequest(const SetActiveConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  ver_ = from.ver_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.thinning.SetActiveConfigRequest)
}

inline void SetActiveConfigRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ver_ = 0;
}

SetActiveConfigRequest::~SetActiveConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.thinning.SetActiveConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetActiveConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SetActiveConfigRequest::ArenaDtor(void* object) {
  SetActiveConfigRequest* _this = reinterpret_cast< SetActiveConfigRequest* >(object);
  (void)_this;
}
void SetActiveConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetActiveConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetActiveConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.thinning.SetActiveConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  id_.ClearToEmpty();
  ver_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetActiveConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.SetActiveConfigRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.SetActiveConfigRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.thinning.ThinningConfVer ver = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_ver(static_cast<::carbon::frontend::thinning::ThinningConfVer>(val));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetActiveConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.thinning.SetActiveConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.SetActiveConfigRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // string id = 2;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.SetActiveConfigRequest.id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_id(), target);
  }

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  if (this->_internal_ver() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_ver(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.thinning.SetActiveConfigRequest)
  return target;
}

size_t SetActiveConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.thinning.SetActiveConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string id = 2;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  if (this->_internal_ver() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_ver());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetActiveConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveConfigRequest::GetClassData() const { return &_class_data_; }

void SetActiveConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetActiveConfigRequest *>(to)->MergeFrom(
      static_cast<const SetActiveConfigRequest &>(from));
}


void SetActiveConfigRequest::MergeFrom(const SetActiveConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.thinning.SetActiveConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (from._internal_ver() != 0) {
    _internal_set_ver(from._internal_ver());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetActiveConfigRequest::CopyFrom(const SetActiveConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.thinning.SetActiveConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetActiveConfigRequest::IsInitialized() const {
  return true;
}

void SetActiveConfigRequest::InternalSwap(SetActiveConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  swap(ver_, other->ver_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetActiveConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fthinning_2eproto_getter, &descriptor_table_frontend_2fproto_2fthinning_2eproto_once,
      file_level_metadata_frontend_2fproto_2fthinning_2eproto[4]);
}

// ===================================================================

class SetActiveConfigResponse::_Internal {
 public:
};

SetActiveConfigResponse::SetActiveConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.thinning.SetActiveConfigResponse)
}
SetActiveConfigResponse::SetActiveConfigResponse(const SetActiveConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.thinning.SetActiveConfigResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetActiveConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetActiveConfigResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetActiveConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fthinning_2eproto_getter, &descriptor_table_frontend_2fproto_2fthinning_2eproto_once,
      file_level_metadata_frontend_2fproto_2fthinning_2eproto[5]);
}

// ===================================================================

class DeleteConfigRequest::_Internal {
 public:
};

DeleteConfigRequest::DeleteConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.thinning.DeleteConfigRequest)
}
DeleteConfigRequest::DeleteConfigRequest(const DeleteConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_id().empty()) {
    id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_id(), 
      GetArenaForAllocation());
  }
  new_active_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_new_active_id().empty()) {
    new_active_id_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_new_active_id(), 
      GetArenaForAllocation());
  }
  ver_ = from.ver_;
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.thinning.DeleteConfigRequest)
}

inline void DeleteConfigRequest::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
new_active_id_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  new_active_id_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
ver_ = 0;
}

DeleteConfigRequest::~DeleteConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.frontend.thinning.DeleteConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeleteConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  new_active_id_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DeleteConfigRequest::ArenaDtor(void* object) {
  DeleteConfigRequest* _this = reinterpret_cast< DeleteConfigRequest* >(object);
  (void)_this;
}
void DeleteConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeleteConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeleteConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.frontend.thinning.DeleteConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  name_.ClearToEmpty();
  id_.ClearToEmpty();
  new_active_id_.ClearToEmpty();
  ver_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeleteConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1 [deprecated = true];
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.DeleteConfigRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string id = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.DeleteConfigRequest.id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.frontend.thinning.ThinningConfVer ver = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_ver(static_cast<::carbon::frontend::thinning::ThinningConfVer>(val));
        } else
          goto handle_unusual;
        continue;
      // string new_active_id = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          auto str = _internal_mutable_new_active_id();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.frontend.thinning.DeleteConfigRequest.new_active_id"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeleteConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.frontend.thinning.DeleteConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.DeleteConfigRequest.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // string id = 2;
  if (!this->_internal_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_id().data(), static_cast<int>(this->_internal_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.DeleteConfigRequest.id");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_id(), target);
  }

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  if (this->_internal_ver() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_ver(), target);
  }

  // string new_active_id = 4;
  if (!this->_internal_new_active_id().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_new_active_id().data(), static_cast<int>(this->_internal_new_active_id().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.frontend.thinning.DeleteConfigRequest.new_active_id");
    target = stream->WriteStringMaybeAliased(
        4, this->_internal_new_active_id(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.frontend.thinning.DeleteConfigRequest)
  return target;
}

size_t DeleteConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.frontend.thinning.DeleteConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string name = 1 [deprecated = true];
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // string id = 2;
  if (!this->_internal_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_id());
  }

  // string new_active_id = 4;
  if (!this->_internal_new_active_id().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_new_active_id());
  }

  // .carbon.frontend.thinning.ThinningConfVer ver = 3;
  if (this->_internal_ver() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_ver());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeleteConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteConfigRequest::GetClassData() const { return &_class_data_; }

void DeleteConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeleteConfigRequest *>(to)->MergeFrom(
      static_cast<const DeleteConfigRequest &>(from));
}


void DeleteConfigRequest::MergeFrom(const DeleteConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.frontend.thinning.DeleteConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (!from._internal_id().empty()) {
    _internal_set_id(from._internal_id());
  }
  if (!from._internal_new_active_id().empty()) {
    _internal_set_new_active_id(from._internal_new_active_id());
  }
  if (from._internal_ver() != 0) {
    _internal_set_ver(from._internal_ver());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeleteConfigRequest::CopyFrom(const DeleteConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.frontend.thinning.DeleteConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeleteConfigRequest::IsInitialized() const {
  return true;
}

void DeleteConfigRequest::InternalSwap(DeleteConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &id_, lhs_arena,
      &other->id_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &new_active_id_, lhs_arena,
      &other->new_active_id_, rhs_arena
  );
  swap(ver_, other->ver_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DeleteConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fthinning_2eproto_getter, &descriptor_table_frontend_2fproto_2fthinning_2eproto_once,
      file_level_metadata_frontend_2fproto_2fthinning_2eproto[6]);
}

// ===================================================================

class DeleteConfigResponse::_Internal {
 public:
};

DeleteConfigResponse::DeleteConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.frontend.thinning.DeleteConfigResponse)
}
DeleteConfigResponse::DeleteConfigResponse(const DeleteConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.frontend.thinning.DeleteConfigResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeleteConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeleteConfigResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata DeleteConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_frontend_2fproto_2fthinning_2eproto_getter, &descriptor_table_frontend_2fproto_2fthinning_2eproto_once,
      file_level_metadata_frontend_2fproto_2fthinning_2eproto[7]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace thinning
}  // namespace frontend
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::frontend::thinning::GetNextConfigurationsResponse* Arena::CreateMaybeMessage< ::carbon::frontend::thinning::GetNextConfigurationsResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::thinning::GetNextConfigurationsResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::thinning::GetNextActiveConfResponse* Arena::CreateMaybeMessage< ::carbon::frontend::thinning::GetNextActiveConfResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::thinning::GetNextActiveConfResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::thinning::DefineConfigurationRequest* Arena::CreateMaybeMessage< ::carbon::frontend::thinning::DefineConfigurationRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::thinning::DefineConfigurationRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::thinning::DefineConfigurationResponse* Arena::CreateMaybeMessage< ::carbon::frontend::thinning::DefineConfigurationResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::thinning::DefineConfigurationResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::thinning::SetActiveConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::thinning::SetActiveConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::thinning::SetActiveConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::thinning::SetActiveConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::thinning::SetActiveConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::thinning::SetActiveConfigResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::thinning::DeleteConfigRequest* Arena::CreateMaybeMessage< ::carbon::frontend::thinning::DeleteConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::thinning::DeleteConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::frontend::thinning::DeleteConfigResponse* Arena::CreateMaybeMessage< ::carbon::frontend::thinning::DeleteConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::frontend::thinning::DeleteConfigResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

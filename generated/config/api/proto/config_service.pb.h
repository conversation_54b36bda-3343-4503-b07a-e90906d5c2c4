// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: config/api/proto/config_service.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_config_2fapi_2fproto_2fconfig_5fservice_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_config_2fapi_2fproto_2fconfig_5fservice_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_bases.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_config_2fapi_2fproto_2fconfig_5fservice_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[26]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
namespace carbon {
namespace config {
namespace proto {
class AddToListRequest;
struct AddToListRequestDefaultTypeInternal;
extern AddToListRequestDefaultTypeInternal _AddToListRequest_default_instance_;
class AddToListResponse;
struct AddToListResponseDefaultTypeInternal;
extern AddToListResponseDefaultTypeInternal _AddToListResponse_default_instance_;
class ConfigDef;
struct ConfigDefDefaultTypeInternal;
extern ConfigDefDefaultTypeInternal _ConfigDef_default_instance_;
class ConfigLeaf;
struct ConfigLeafDefaultTypeInternal;
extern ConfigLeafDefaultTypeInternal _ConfigLeaf_default_instance_;
class ConfigNode;
struct ConfigNodeDefaultTypeInternal;
extern ConfigNodeDefaultTypeInternal _ConfigNode_default_instance_;
class ConfigValue;
struct ConfigValueDefaultTypeInternal;
extern ConfigValueDefaultTypeInternal _ConfigValue_default_instance_;
class FloatConfigDef;
struct FloatConfigDefDefaultTypeInternal;
extern FloatConfigDefDefaultTypeInternal _FloatConfigDef_default_instance_;
class GetLeavesRequest;
struct GetLeavesRequestDefaultTypeInternal;
extern GetLeavesRequestDefaultTypeInternal _GetLeavesRequest_default_instance_;
class GetLeavesResponse;
struct GetLeavesResponseDefaultTypeInternal;
extern GetLeavesResponseDefaultTypeInternal _GetLeavesResponse_default_instance_;
class GetTreeRequest;
struct GetTreeRequestDefaultTypeInternal;
extern GetTreeRequestDefaultTypeInternal _GetTreeRequest_default_instance_;
class GetTreeResponse;
struct GetTreeResponseDefaultTypeInternal;
extern GetTreeResponseDefaultTypeInternal _GetTreeResponse_default_instance_;
class IntConfigDef;
struct IntConfigDefDefaultTypeInternal;
extern IntConfigDefDefaultTypeInternal _IntConfigDef_default_instance_;
class PingRequest;
struct PingRequestDefaultTypeInternal;
extern PingRequestDefaultTypeInternal _PingRequest_default_instance_;
class PongResponse;
struct PongResponseDefaultTypeInternal;
extern PongResponseDefaultTypeInternal _PongResponse_default_instance_;
class RemoveFromListRequest;
struct RemoveFromListRequestDefaultTypeInternal;
extern RemoveFromListRequestDefaultTypeInternal _RemoveFromListRequest_default_instance_;
class RemoveFromListResponse;
struct RemoveFromListResponseDefaultTypeInternal;
extern RemoveFromListResponseDefaultTypeInternal _RemoveFromListResponse_default_instance_;
class SetTreeRequest;
struct SetTreeRequestDefaultTypeInternal;
extern SetTreeRequestDefaultTypeInternal _SetTreeRequest_default_instance_;
class SetTreeResponse;
struct SetTreeResponseDefaultTypeInternal;
extern SetTreeResponseDefaultTypeInternal _SetTreeResponse_default_instance_;
class SetValueRequest;
struct SetValueRequestDefaultTypeInternal;
extern SetValueRequestDefaultTypeInternal _SetValueRequest_default_instance_;
class SetValueResponse;
struct SetValueResponseDefaultTypeInternal;
extern SetValueResponseDefaultTypeInternal _SetValueResponse_default_instance_;
class StringConfigDef;
struct StringConfigDefDefaultTypeInternal;
extern StringConfigDefDefaultTypeInternal _StringConfigDef_default_instance_;
class SubscriptionNotifyMessage;
struct SubscriptionNotifyMessageDefaultTypeInternal;
extern SubscriptionNotifyMessageDefaultTypeInternal _SubscriptionNotifyMessage_default_instance_;
class SubscriptionRequest;
struct SubscriptionRequestDefaultTypeInternal;
extern SubscriptionRequestDefaultTypeInternal _SubscriptionRequest_default_instance_;
class UIntConfigDef;
struct UIntConfigDefDefaultTypeInternal;
extern UIntConfigDefDefaultTypeInternal _UIntConfigDef_default_instance_;
class UpgradeCloudConfigRequest;
struct UpgradeCloudConfigRequestDefaultTypeInternal;
extern UpgradeCloudConfigRequestDefaultTypeInternal _UpgradeCloudConfigRequest_default_instance_;
class UpgradeCloudConfigResponse;
struct UpgradeCloudConfigResponseDefaultTypeInternal;
extern UpgradeCloudConfigResponseDefaultTypeInternal _UpgradeCloudConfigResponse_default_instance_;
}  // namespace proto
}  // namespace config
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::config::proto::AddToListRequest* Arena::CreateMaybeMessage<::carbon::config::proto::AddToListRequest>(Arena*);
template<> ::carbon::config::proto::AddToListResponse* Arena::CreateMaybeMessage<::carbon::config::proto::AddToListResponse>(Arena*);
template<> ::carbon::config::proto::ConfigDef* Arena::CreateMaybeMessage<::carbon::config::proto::ConfigDef>(Arena*);
template<> ::carbon::config::proto::ConfigLeaf* Arena::CreateMaybeMessage<::carbon::config::proto::ConfigLeaf>(Arena*);
template<> ::carbon::config::proto::ConfigNode* Arena::CreateMaybeMessage<::carbon::config::proto::ConfigNode>(Arena*);
template<> ::carbon::config::proto::ConfigValue* Arena::CreateMaybeMessage<::carbon::config::proto::ConfigValue>(Arena*);
template<> ::carbon::config::proto::FloatConfigDef* Arena::CreateMaybeMessage<::carbon::config::proto::FloatConfigDef>(Arena*);
template<> ::carbon::config::proto::GetLeavesRequest* Arena::CreateMaybeMessage<::carbon::config::proto::GetLeavesRequest>(Arena*);
template<> ::carbon::config::proto::GetLeavesResponse* Arena::CreateMaybeMessage<::carbon::config::proto::GetLeavesResponse>(Arena*);
template<> ::carbon::config::proto::GetTreeRequest* Arena::CreateMaybeMessage<::carbon::config::proto::GetTreeRequest>(Arena*);
template<> ::carbon::config::proto::GetTreeResponse* Arena::CreateMaybeMessage<::carbon::config::proto::GetTreeResponse>(Arena*);
template<> ::carbon::config::proto::IntConfigDef* Arena::CreateMaybeMessage<::carbon::config::proto::IntConfigDef>(Arena*);
template<> ::carbon::config::proto::PingRequest* Arena::CreateMaybeMessage<::carbon::config::proto::PingRequest>(Arena*);
template<> ::carbon::config::proto::PongResponse* Arena::CreateMaybeMessage<::carbon::config::proto::PongResponse>(Arena*);
template<> ::carbon::config::proto::RemoveFromListRequest* Arena::CreateMaybeMessage<::carbon::config::proto::RemoveFromListRequest>(Arena*);
template<> ::carbon::config::proto::RemoveFromListResponse* Arena::CreateMaybeMessage<::carbon::config::proto::RemoveFromListResponse>(Arena*);
template<> ::carbon::config::proto::SetTreeRequest* Arena::CreateMaybeMessage<::carbon::config::proto::SetTreeRequest>(Arena*);
template<> ::carbon::config::proto::SetTreeResponse* Arena::CreateMaybeMessage<::carbon::config::proto::SetTreeResponse>(Arena*);
template<> ::carbon::config::proto::SetValueRequest* Arena::CreateMaybeMessage<::carbon::config::proto::SetValueRequest>(Arena*);
template<> ::carbon::config::proto::SetValueResponse* Arena::CreateMaybeMessage<::carbon::config::proto::SetValueResponse>(Arena*);
template<> ::carbon::config::proto::StringConfigDef* Arena::CreateMaybeMessage<::carbon::config::proto::StringConfigDef>(Arena*);
template<> ::carbon::config::proto::SubscriptionNotifyMessage* Arena::CreateMaybeMessage<::carbon::config::proto::SubscriptionNotifyMessage>(Arena*);
template<> ::carbon::config::proto::SubscriptionRequest* Arena::CreateMaybeMessage<::carbon::config::proto::SubscriptionRequest>(Arena*);
template<> ::carbon::config::proto::UIntConfigDef* Arena::CreateMaybeMessage<::carbon::config::proto::UIntConfigDef>(Arena*);
template<> ::carbon::config::proto::UpgradeCloudConfigRequest* Arena::CreateMaybeMessage<::carbon::config::proto::UpgradeCloudConfigRequest>(Arena*);
template<> ::carbon::config::proto::UpgradeCloudConfigResponse* Arena::CreateMaybeMessage<::carbon::config::proto::UpgradeCloudConfigResponse>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace config {
namespace proto {

enum ConfigType : int {
  NODE = 0,
  LIST = 1,
  STRING = 2,
  INT = 3,
  UINT = 4,
  FLOAT = 5,
  BOOL = 6,
  ConfigType_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ConfigType_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ConfigType_IsValid(int value);
constexpr ConfigType ConfigType_MIN = NODE;
constexpr ConfigType ConfigType_MAX = BOOL;
constexpr int ConfigType_ARRAYSIZE = ConfigType_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConfigType_descriptor();
template<typename T>
inline const std::string& ConfigType_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConfigType>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConfigType_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConfigType_descriptor(), enum_t_value);
}
inline bool ConfigType_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ConfigType* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConfigType>(
    ConfigType_descriptor(), name, value);
}
enum ConfigComplexity : int {
  USER = 0,
  ADVANCED = 1,
  EXPERT = 2,
  DEVELOPER = 3,
  ConfigComplexity_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  ConfigComplexity_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool ConfigComplexity_IsValid(int value);
constexpr ConfigComplexity ConfigComplexity_MIN = USER;
constexpr ConfigComplexity ConfigComplexity_MAX = DEVELOPER;
constexpr int ConfigComplexity_ARRAYSIZE = ConfigComplexity_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConfigComplexity_descriptor();
template<typename T>
inline const std::string& ConfigComplexity_Name(T enum_t_value) {
  static_assert(::std::is_same<T, ConfigComplexity>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function ConfigComplexity_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    ConfigComplexity_descriptor(), enum_t_value);
}
inline bool ConfigComplexity_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, ConfigComplexity* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<ConfigComplexity>(
    ConfigComplexity_descriptor(), name, value);
}
// ===================================================================

class PingRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.PingRequest) */ {
 public:
  inline PingRequest() : PingRequest(nullptr) {}
  ~PingRequest() override;
  explicit constexpr PingRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PingRequest(const PingRequest& from);
  PingRequest(PingRequest&& from) noexcept
    : PingRequest() {
    *this = ::std::move(from);
  }

  inline PingRequest& operator=(const PingRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PingRequest& operator=(PingRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PingRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PingRequest* internal_default_instance() {
    return reinterpret_cast<const PingRequest*>(
               &_PingRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(PingRequest& a, PingRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PingRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PingRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PingRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PingRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PingRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PingRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PingRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.PingRequest";
  }
  protected:
  explicit PingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
  };
  // int32 x = 1;
  void clear_x();
  int32_t x() const;
  void set_x(int32_t value);
  private:
  int32_t _internal_x() const;
  void _internal_set_x(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.PingRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t x_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class PongResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.PongResponse) */ {
 public:
  inline PongResponse() : PongResponse(nullptr) {}
  ~PongResponse() override;
  explicit constexpr PongResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PongResponse(const PongResponse& from);
  PongResponse(PongResponse&& from) noexcept
    : PongResponse() {
    *this = ::std::move(from);
  }

  inline PongResponse& operator=(const PongResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline PongResponse& operator=(PongResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PongResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const PongResponse* internal_default_instance() {
    return reinterpret_cast<const PongResponse*>(
               &_PongResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(PongResponse& a, PongResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(PongResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PongResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PongResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PongResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PongResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PongResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PongResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.PongResponse";
  }
  protected:
  explicit PongResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kXFieldNumber = 1,
  };
  // int32 x = 1;
  void clear_x();
  int32_t x() const;
  void set_x(int32_t value);
  private:
  int32_t _internal_x() const;
  void _internal_set_x(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.PongResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t x_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ConfigValue final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.ConfigValue) */ {
 public:
  inline ConfigValue() : ConfigValue(nullptr) {}
  ~ConfigValue() override;
  explicit constexpr ConfigValue(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigValue(const ConfigValue& from);
  ConfigValue(ConfigValue&& from) noexcept
    : ConfigValue() {
    *this = ::std::move(from);
  }

  inline ConfigValue& operator=(const ConfigValue& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigValue& operator=(ConfigValue&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigValue& default_instance() {
    return *internal_default_instance();
  }
  enum ValueCase {
    kInt64Val = 1,
    kUint64Val = 2,
    kBoolVal = 3,
    kFloatVal = 4,
    kStringVal = 5,
    VALUE_NOT_SET = 0,
  };

  static inline const ConfigValue* internal_default_instance() {
    return reinterpret_cast<const ConfigValue*>(
               &_ConfigValue_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ConfigValue& a, ConfigValue& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigValue* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigValue* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigValue* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigValue>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigValue& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConfigValue& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigValue* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.ConfigValue";
  }
  protected:
  explicit ConfigValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kTimestampMsFieldNumber = 6,
    kInt64ValFieldNumber = 1,
    kUint64ValFieldNumber = 2,
    kBoolValFieldNumber = 3,
    kFloatValFieldNumber = 4,
    kStringValFieldNumber = 5,
  };
  // uint64 timestamp_ms = 6;
  void clear_timestamp_ms();
  uint64_t timestamp_ms() const;
  void set_timestamp_ms(uint64_t value);
  private:
  uint64_t _internal_timestamp_ms() const;
  void _internal_set_timestamp_ms(uint64_t value);
  public:

  // int64 int64_val = 1;
  bool has_int64_val() const;
  private:
  bool _internal_has_int64_val() const;
  public:
  void clear_int64_val();
  int64_t int64_val() const;
  void set_int64_val(int64_t value);
  private:
  int64_t _internal_int64_val() const;
  void _internal_set_int64_val(int64_t value);
  public:

  // uint64 uint64_val = 2;
  bool has_uint64_val() const;
  private:
  bool _internal_has_uint64_val() const;
  public:
  void clear_uint64_val();
  uint64_t uint64_val() const;
  void set_uint64_val(uint64_t value);
  private:
  uint64_t _internal_uint64_val() const;
  void _internal_set_uint64_val(uint64_t value);
  public:

  // bool bool_val = 3;
  bool has_bool_val() const;
  private:
  bool _internal_has_bool_val() const;
  public:
  void clear_bool_val();
  bool bool_val() const;
  void set_bool_val(bool value);
  private:
  bool _internal_bool_val() const;
  void _internal_set_bool_val(bool value);
  public:

  // double float_val = 4;
  bool has_float_val() const;
  private:
  bool _internal_has_float_val() const;
  public:
  void clear_float_val();
  double float_val() const;
  void set_float_val(double value);
  private:
  double _internal_float_val() const;
  void _internal_set_float_val(double value);
  public:

  // string string_val = 5;
  bool has_string_val() const;
  private:
  bool _internal_has_string_val() const;
  public:
  void clear_string_val();
  const std::string& string_val() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_string_val(ArgT0&& arg0, ArgT... args);
  std::string* mutable_string_val();
  PROTOBUF_NODISCARD std::string* release_string_val();
  void set_allocated_string_val(std::string* string_val);
  private:
  const std::string& _internal_string_val() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_string_val(const std::string& value);
  std::string* _internal_mutable_string_val();
  public:

  void clear_value();
  ValueCase value_case() const;
  // @@protoc_insertion_point(class_scope:carbon.config.proto.ConfigValue)
 private:
  class _Internal;
  void set_has_int64_val();
  void set_has_uint64_val();
  void set_has_bool_val();
  void set_has_float_val();
  void set_has_string_val();

  inline bool has_value() const;
  inline void clear_has_value();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t timestamp_ms_;
  union ValueUnion {
    constexpr ValueUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    int64_t int64_val_;
    uint64_t uint64_val_;
    bool bool_val_;
    double float_val_;
    ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr string_val_;
  } value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class IntConfigDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.IntConfigDef) */ {
 public:
  inline IntConfigDef() : IntConfigDef(nullptr) {}
  ~IntConfigDef() override;
  explicit constexpr IntConfigDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  IntConfigDef(const IntConfigDef& from);
  IntConfigDef(IntConfigDef&& from) noexcept
    : IntConfigDef() {
    *this = ::std::move(from);
  }

  inline IntConfigDef& operator=(const IntConfigDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline IntConfigDef& operator=(IntConfigDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const IntConfigDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const IntConfigDef* internal_default_instance() {
    return reinterpret_cast<const IntConfigDef*>(
               &_IntConfigDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(IntConfigDef& a, IntConfigDef& b) {
    a.Swap(&b);
  }
  inline void Swap(IntConfigDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(IntConfigDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  IntConfigDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<IntConfigDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const IntConfigDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const IntConfigDef& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(IntConfigDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.IntConfigDef";
  }
  protected:
  explicit IntConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinFieldNumber = 1,
    kMaxFieldNumber = 2,
    kStepFieldNumber = 3,
  };
  // int64 min = 1;
  void clear_min();
  int64_t min() const;
  void set_min(int64_t value);
  private:
  int64_t _internal_min() const;
  void _internal_set_min(int64_t value);
  public:

  // int64 max = 2;
  void clear_max();
  int64_t max() const;
  void set_max(int64_t value);
  private:
  int64_t _internal_max() const;
  void _internal_set_max(int64_t value);
  public:

  // int64 step = 3;
  void clear_step();
  int64_t step() const;
  void set_step(int64_t value);
  private:
  int64_t _internal_step() const;
  void _internal_set_step(int64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.IntConfigDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int64_t min_;
  int64_t max_;
  int64_t step_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class UIntConfigDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.UIntConfigDef) */ {
 public:
  inline UIntConfigDef() : UIntConfigDef(nullptr) {}
  ~UIntConfigDef() override;
  explicit constexpr UIntConfigDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UIntConfigDef(const UIntConfigDef& from);
  UIntConfigDef(UIntConfigDef&& from) noexcept
    : UIntConfigDef() {
    *this = ::std::move(from);
  }

  inline UIntConfigDef& operator=(const UIntConfigDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline UIntConfigDef& operator=(UIntConfigDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UIntConfigDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const UIntConfigDef* internal_default_instance() {
    return reinterpret_cast<const UIntConfigDef*>(
               &_UIntConfigDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(UIntConfigDef& a, UIntConfigDef& b) {
    a.Swap(&b);
  }
  inline void Swap(UIntConfigDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UIntConfigDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UIntConfigDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UIntConfigDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UIntConfigDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UIntConfigDef& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UIntConfigDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.UIntConfigDef";
  }
  protected:
  explicit UIntConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinFieldNumber = 1,
    kMaxFieldNumber = 2,
    kStepFieldNumber = 3,
  };
  // uint64 min = 1;
  void clear_min();
  uint64_t min() const;
  void set_min(uint64_t value);
  private:
  uint64_t _internal_min() const;
  void _internal_set_min(uint64_t value);
  public:

  // uint64 max = 2;
  void clear_max();
  uint64_t max() const;
  void set_max(uint64_t value);
  private:
  uint64_t _internal_max() const;
  void _internal_set_max(uint64_t value);
  public:

  // uint64 step = 3;
  void clear_step();
  uint64_t step() const;
  void set_step(uint64_t value);
  private:
  uint64_t _internal_step() const;
  void _internal_set_step(uint64_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.UIntConfigDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  uint64_t min_;
  uint64_t max_;
  uint64_t step_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class FloatConfigDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.FloatConfigDef) */ {
 public:
  inline FloatConfigDef() : FloatConfigDef(nullptr) {}
  ~FloatConfigDef() override;
  explicit constexpr FloatConfigDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  FloatConfigDef(const FloatConfigDef& from);
  FloatConfigDef(FloatConfigDef&& from) noexcept
    : FloatConfigDef() {
    *this = ::std::move(from);
  }

  inline FloatConfigDef& operator=(const FloatConfigDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline FloatConfigDef& operator=(FloatConfigDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const FloatConfigDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const FloatConfigDef* internal_default_instance() {
    return reinterpret_cast<const FloatConfigDef*>(
               &_FloatConfigDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(FloatConfigDef& a, FloatConfigDef& b) {
    a.Swap(&b);
  }
  inline void Swap(FloatConfigDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(FloatConfigDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  FloatConfigDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<FloatConfigDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const FloatConfigDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const FloatConfigDef& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(FloatConfigDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.FloatConfigDef";
  }
  protected:
  explicit FloatConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinFieldNumber = 1,
    kMaxFieldNumber = 2,
    kStepFieldNumber = 3,
  };
  // double min = 1;
  void clear_min();
  double min() const;
  void set_min(double value);
  private:
  double _internal_min() const;
  void _internal_set_min(double value);
  public:

  // double max = 2;
  void clear_max();
  double max() const;
  void set_max(double value);
  private:
  double _internal_max() const;
  void _internal_set_max(double value);
  public:

  // double step = 3;
  void clear_step();
  double step() const;
  void set_step(double value);
  private:
  double _internal_step() const;
  void _internal_set_step(double value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.FloatConfigDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  double min_;
  double max_;
  double step_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class StringConfigDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.StringConfigDef) */ {
 public:
  inline StringConfigDef() : StringConfigDef(nullptr) {}
  ~StringConfigDef() override;
  explicit constexpr StringConfigDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  StringConfigDef(const StringConfigDef& from);
  StringConfigDef(StringConfigDef&& from) noexcept
    : StringConfigDef() {
    *this = ::std::move(from);
  }

  inline StringConfigDef& operator=(const StringConfigDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline StringConfigDef& operator=(StringConfigDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const StringConfigDef& default_instance() {
    return *internal_default_instance();
  }
  static inline const StringConfigDef* internal_default_instance() {
    return reinterpret_cast<const StringConfigDef*>(
               &_StringConfigDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(StringConfigDef& a, StringConfigDef& b) {
    a.Swap(&b);
  }
  inline void Swap(StringConfigDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(StringConfigDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  StringConfigDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<StringConfigDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const StringConfigDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const StringConfigDef& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(StringConfigDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.StringConfigDef";
  }
  protected:
  explicit StringConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChoicesFieldNumber = 3,
    kSizeLimitFieldNumber = 1,
  };
  // repeated string choices = 3;
  int choices_size() const;
  private:
  int _internal_choices_size() const;
  public:
  void clear_choices();
  const std::string& choices(int index) const;
  std::string* mutable_choices(int index);
  void set_choices(int index, const std::string& value);
  void set_choices(int index, std::string&& value);
  void set_choices(int index, const char* value);
  void set_choices(int index, const char* value, size_t size);
  std::string* add_choices();
  void add_choices(const std::string& value);
  void add_choices(std::string&& value);
  void add_choices(const char* value);
  void add_choices(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& choices() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_choices();
  private:
  const std::string& _internal_choices(int index) const;
  std::string* _internal_add_choices();
  public:

  // uint32 size_limit = 1;
  void clear_size_limit();
  uint32_t size_limit() const;
  void set_size_limit(uint32_t value);
  private:
  uint32_t _internal_size_limit() const;
  void _internal_set_size_limit(uint32_t value);
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.StringConfigDef)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> choices_;
  uint32_t size_limit_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ConfigDef final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.ConfigDef) */ {
 public:
  inline ConfigDef() : ConfigDef(nullptr) {}
  ~ConfigDef() override;
  explicit constexpr ConfigDef(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigDef(const ConfigDef& from);
  ConfigDef(ConfigDef&& from) noexcept
    : ConfigDef() {
    *this = ::std::move(from);
  }

  inline ConfigDef& operator=(const ConfigDef& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigDef& operator=(ConfigDef&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigDef& default_instance() {
    return *internal_default_instance();
  }
  enum ExtraCase {
    kIntDef = 3,
    kUintDef = 4,
    kFloatDef = 5,
    kStringDef = 6,
    EXTRA_NOT_SET = 0,
  };

  static inline const ConfigDef* internal_default_instance() {
    return reinterpret_cast<const ConfigDef*>(
               &_ConfigDef_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(ConfigDef& a, ConfigDef& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigDef* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigDef* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigDef* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigDef>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigDef& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConfigDef& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigDef* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.ConfigDef";
  }
  protected:
  explicit ConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kHintFieldNumber = 7,
    kUnitsFieldNumber = 9,
    kTypeFieldNumber = 1,
    kComplexityFieldNumber = 2,
    kDefaultRecommendedFieldNumber = 8,
    kIntDefFieldNumber = 3,
    kUintDefFieldNumber = 4,
    kFloatDefFieldNumber = 5,
    kStringDefFieldNumber = 6,
  };
  // string hint = 7;
  void clear_hint();
  const std::string& hint() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_hint(ArgT0&& arg0, ArgT... args);
  std::string* mutable_hint();
  PROTOBUF_NODISCARD std::string* release_hint();
  void set_allocated_hint(std::string* hint);
  private:
  const std::string& _internal_hint() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_hint(const std::string& value);
  std::string* _internal_mutable_hint();
  public:

  // string units = 9;
  void clear_units();
  const std::string& units() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_units(ArgT0&& arg0, ArgT... args);
  std::string* mutable_units();
  PROTOBUF_NODISCARD std::string* release_units();
  void set_allocated_units(std::string* units);
  private:
  const std::string& _internal_units() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_units(const std::string& value);
  std::string* _internal_mutable_units();
  public:

  // .carbon.config.proto.ConfigType type = 1;
  void clear_type();
  ::carbon::config::proto::ConfigType type() const;
  void set_type(::carbon::config::proto::ConfigType value);
  private:
  ::carbon::config::proto::ConfigType _internal_type() const;
  void _internal_set_type(::carbon::config::proto::ConfigType value);
  public:

  // .carbon.config.proto.ConfigComplexity complexity = 2;
  void clear_complexity();
  ::carbon::config::proto::ConfigComplexity complexity() const;
  void set_complexity(::carbon::config::proto::ConfigComplexity value);
  private:
  ::carbon::config::proto::ConfigComplexity _internal_complexity() const;
  void _internal_set_complexity(::carbon::config::proto::ConfigComplexity value);
  public:

  // bool default_recommended = 8;
  void clear_default_recommended();
  bool default_recommended() const;
  void set_default_recommended(bool value);
  private:
  bool _internal_default_recommended() const;
  void _internal_set_default_recommended(bool value);
  public:

  // .carbon.config.proto.IntConfigDef int_def = 3;
  bool has_int_def() const;
  private:
  bool _internal_has_int_def() const;
  public:
  void clear_int_def();
  const ::carbon::config::proto::IntConfigDef& int_def() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::IntConfigDef* release_int_def();
  ::carbon::config::proto::IntConfigDef* mutable_int_def();
  void set_allocated_int_def(::carbon::config::proto::IntConfigDef* int_def);
  private:
  const ::carbon::config::proto::IntConfigDef& _internal_int_def() const;
  ::carbon::config::proto::IntConfigDef* _internal_mutable_int_def();
  public:
  void unsafe_arena_set_allocated_int_def(
      ::carbon::config::proto::IntConfigDef* int_def);
  ::carbon::config::proto::IntConfigDef* unsafe_arena_release_int_def();

  // .carbon.config.proto.UIntConfigDef uint_def = 4;
  bool has_uint_def() const;
  private:
  bool _internal_has_uint_def() const;
  public:
  void clear_uint_def();
  const ::carbon::config::proto::UIntConfigDef& uint_def() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::UIntConfigDef* release_uint_def();
  ::carbon::config::proto::UIntConfigDef* mutable_uint_def();
  void set_allocated_uint_def(::carbon::config::proto::UIntConfigDef* uint_def);
  private:
  const ::carbon::config::proto::UIntConfigDef& _internal_uint_def() const;
  ::carbon::config::proto::UIntConfigDef* _internal_mutable_uint_def();
  public:
  void unsafe_arena_set_allocated_uint_def(
      ::carbon::config::proto::UIntConfigDef* uint_def);
  ::carbon::config::proto::UIntConfigDef* unsafe_arena_release_uint_def();

  // .carbon.config.proto.FloatConfigDef float_def = 5;
  bool has_float_def() const;
  private:
  bool _internal_has_float_def() const;
  public:
  void clear_float_def();
  const ::carbon::config::proto::FloatConfigDef& float_def() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::FloatConfigDef* release_float_def();
  ::carbon::config::proto::FloatConfigDef* mutable_float_def();
  void set_allocated_float_def(::carbon::config::proto::FloatConfigDef* float_def);
  private:
  const ::carbon::config::proto::FloatConfigDef& _internal_float_def() const;
  ::carbon::config::proto::FloatConfigDef* _internal_mutable_float_def();
  public:
  void unsafe_arena_set_allocated_float_def(
      ::carbon::config::proto::FloatConfigDef* float_def);
  ::carbon::config::proto::FloatConfigDef* unsafe_arena_release_float_def();

  // .carbon.config.proto.StringConfigDef string_def = 6;
  bool has_string_def() const;
  private:
  bool _internal_has_string_def() const;
  public:
  void clear_string_def();
  const ::carbon::config::proto::StringConfigDef& string_def() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::StringConfigDef* release_string_def();
  ::carbon::config::proto::StringConfigDef* mutable_string_def();
  void set_allocated_string_def(::carbon::config::proto::StringConfigDef* string_def);
  private:
  const ::carbon::config::proto::StringConfigDef& _internal_string_def() const;
  ::carbon::config::proto::StringConfigDef* _internal_mutable_string_def();
  public:
  void unsafe_arena_set_allocated_string_def(
      ::carbon::config::proto::StringConfigDef* string_def);
  ::carbon::config::proto::StringConfigDef* unsafe_arena_release_string_def();

  void clear_extra();
  ExtraCase extra_case() const;
  // @@protoc_insertion_point(class_scope:carbon.config.proto.ConfigDef)
 private:
  class _Internal;
  void set_has_int_def();
  void set_has_uint_def();
  void set_has_float_def();
  void set_has_string_def();

  inline bool has_extra() const;
  inline void clear_has_extra();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr hint_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr units_;
  int type_;
  int complexity_;
  bool default_recommended_;
  union ExtraUnion {
    constexpr ExtraUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::config::proto::IntConfigDef* int_def_;
    ::carbon::config::proto::UIntConfigDef* uint_def_;
    ::carbon::config::proto::FloatConfigDef* float_def_;
    ::carbon::config::proto::StringConfigDef* string_def_;
  } extra_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ConfigNode final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.ConfigNode) */ {
 public:
  inline ConfigNode() : ConfigNode(nullptr) {}
  ~ConfigNode() override;
  explicit constexpr ConfigNode(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigNode(const ConfigNode& from);
  ConfigNode(ConfigNode&& from) noexcept
    : ConfigNode() {
    *this = ::std::move(from);
  }

  inline ConfigNode& operator=(const ConfigNode& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigNode& operator=(ConfigNode&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigNode& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigNode* internal_default_instance() {
    return reinterpret_cast<const ConfigNode*>(
               &_ConfigNode_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    8;

  friend void swap(ConfigNode& a, ConfigNode& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigNode* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigNode* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigNode* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigNode>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigNode& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConfigNode& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigNode* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.ConfigNode";
  }
  protected:
  explicit ConfigNode(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kChildrenFieldNumber = 4,
    kNameFieldNumber = 1,
    kValueFieldNumber = 2,
    kDefFieldNumber = 3,
  };
  // repeated .carbon.config.proto.ConfigNode children = 4;
  int children_size() const;
  private:
  int _internal_children_size() const;
  public:
  void clear_children();
  ::carbon::config::proto::ConfigNode* mutable_children(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigNode >*
      mutable_children();
  private:
  const ::carbon::config::proto::ConfigNode& _internal_children(int index) const;
  ::carbon::config::proto::ConfigNode* _internal_add_children();
  public:
  const ::carbon::config::proto::ConfigNode& children(int index) const;
  ::carbon::config::proto::ConfigNode* add_children();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigNode >&
      children() const;

  // string name = 1;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // .carbon.config.proto.ConfigValue value = 2;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::carbon::config::proto::ConfigValue& value() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::ConfigValue* release_value();
  ::carbon::config::proto::ConfigValue* mutable_value();
  void set_allocated_value(::carbon::config::proto::ConfigValue* value);
  private:
  const ::carbon::config::proto::ConfigValue& _internal_value() const;
  ::carbon::config::proto::ConfigValue* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::carbon::config::proto::ConfigValue* value);
  ::carbon::config::proto::ConfigValue* unsafe_arena_release_value();

  // .carbon.config.proto.ConfigDef def = 3;
  bool has_def() const;
  private:
  bool _internal_has_def() const;
  public:
  void clear_def();
  const ::carbon::config::proto::ConfigDef& def() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::ConfigDef* release_def();
  ::carbon::config::proto::ConfigDef* mutable_def();
  void set_allocated_def(::carbon::config::proto::ConfigDef* def);
  private:
  const ::carbon::config::proto::ConfigDef& _internal_def() const;
  ::carbon::config::proto::ConfigDef* _internal_mutable_def();
  public:
  void unsafe_arena_set_allocated_def(
      ::carbon::config::proto::ConfigDef* def);
  ::carbon::config::proto::ConfigDef* unsafe_arena_release_def();

  // @@protoc_insertion_point(class_scope:carbon.config.proto.ConfigNode)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigNode > children_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  ::carbon::config::proto::ConfigValue* value_;
  ::carbon::config::proto::ConfigDef* def_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class ConfigLeaf final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.ConfigLeaf) */ {
 public:
  inline ConfigLeaf() : ConfigLeaf(nullptr) {}
  ~ConfigLeaf() override;
  explicit constexpr ConfigLeaf(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ConfigLeaf(const ConfigLeaf& from);
  ConfigLeaf(ConfigLeaf&& from) noexcept
    : ConfigLeaf() {
    *this = ::std::move(from);
  }

  inline ConfigLeaf& operator=(const ConfigLeaf& from) {
    CopyFrom(from);
    return *this;
  }
  inline ConfigLeaf& operator=(ConfigLeaf&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ConfigLeaf& default_instance() {
    return *internal_default_instance();
  }
  static inline const ConfigLeaf* internal_default_instance() {
    return reinterpret_cast<const ConfigLeaf*>(
               &_ConfigLeaf_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    9;

  friend void swap(ConfigLeaf& a, ConfigLeaf& b) {
    a.Swap(&b);
  }
  inline void Swap(ConfigLeaf* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ConfigLeaf* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ConfigLeaf* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ConfigLeaf>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ConfigLeaf& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ConfigLeaf& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ConfigLeaf* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.ConfigLeaf";
  }
  protected:
  explicit ConfigLeaf(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // .carbon.config.proto.ConfigValue value = 2;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::carbon::config::proto::ConfigValue& value() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::ConfigValue* release_value();
  ::carbon::config::proto::ConfigValue* mutable_value();
  void set_allocated_value(::carbon::config::proto::ConfigValue* value);
  private:
  const ::carbon::config::proto::ConfigValue& _internal_value() const;
  ::carbon::config::proto::ConfigValue* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::carbon::config::proto::ConfigValue* value);
  ::carbon::config::proto::ConfigValue* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:carbon.config.proto.ConfigLeaf)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::carbon::config::proto::ConfigValue* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SetValueRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.SetValueRequest) */ {
 public:
  inline SetValueRequest() : SetValueRequest(nullptr) {}
  ~SetValueRequest() override;
  explicit constexpr SetValueRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetValueRequest(const SetValueRequest& from);
  SetValueRequest(SetValueRequest&& from) noexcept
    : SetValueRequest() {
    *this = ::std::move(from);
  }

  inline SetValueRequest& operator=(const SetValueRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetValueRequest& operator=(SetValueRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetValueRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetValueRequest* internal_default_instance() {
    return reinterpret_cast<const SetValueRequest*>(
               &_SetValueRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    10;

  friend void swap(SetValueRequest& a, SetValueRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetValueRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetValueRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetValueRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetValueRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetValueRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetValueRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetValueRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.SetValueRequest";
  }
  protected:
  explicit SetValueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kValueFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // .carbon.config.proto.ConfigValue value = 2;
  bool has_value() const;
  private:
  bool _internal_has_value() const;
  public:
  void clear_value();
  const ::carbon::config::proto::ConfigValue& value() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::ConfigValue* release_value();
  ::carbon::config::proto::ConfigValue* mutable_value();
  void set_allocated_value(::carbon::config::proto::ConfigValue* value);
  private:
  const ::carbon::config::proto::ConfigValue& _internal_value() const;
  ::carbon::config::proto::ConfigValue* _internal_mutable_value();
  public:
  void unsafe_arena_set_allocated_value(
      ::carbon::config::proto::ConfigValue* value);
  ::carbon::config::proto::ConfigValue* unsafe_arena_release_value();

  // @@protoc_insertion_point(class_scope:carbon.config.proto.SetValueRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::carbon::config::proto::ConfigValue* value_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SetValueResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.config.proto.SetValueResponse) */ {
 public:
  inline SetValueResponse() : SetValueResponse(nullptr) {}
  explicit constexpr SetValueResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetValueResponse(const SetValueResponse& from);
  SetValueResponse(SetValueResponse&& from) noexcept
    : SetValueResponse() {
    *this = ::std::move(from);
  }

  inline SetValueResponse& operator=(const SetValueResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetValueResponse& operator=(SetValueResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetValueResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetValueResponse* internal_default_instance() {
    return reinterpret_cast<const SetValueResponse*>(
               &_SetValueResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    11;

  friend void swap(SetValueResponse& a, SetValueResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetValueResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetValueResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetValueResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetValueResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetValueResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetValueResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.SetValueResponse";
  }
  protected:
  explicit SetValueResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.config.proto.SetValueResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetTreeRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.GetTreeRequest) */ {
 public:
  inline GetTreeRequest() : GetTreeRequest(nullptr) {}
  ~GetTreeRequest() override;
  explicit constexpr GetTreeRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetTreeRequest(const GetTreeRequest& from);
  GetTreeRequest(GetTreeRequest&& from) noexcept
    : GetTreeRequest() {
    *this = ::std::move(from);
  }

  inline GetTreeRequest& operator=(const GetTreeRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetTreeRequest& operator=(GetTreeRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetTreeRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetTreeRequest* internal_default_instance() {
    return reinterpret_cast<const GetTreeRequest*>(
               &_GetTreeRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    12;

  friend void swap(GetTreeRequest& a, GetTreeRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetTreeRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetTreeRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetTreeRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetTreeRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetTreeRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetTreeRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetTreeRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.GetTreeRequest";
  }
  protected:
  explicit GetTreeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.GetTreeRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetTreeResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.GetTreeResponse) */ {
 public:
  inline GetTreeResponse() : GetTreeResponse(nullptr) {}
  ~GetTreeResponse() override;
  explicit constexpr GetTreeResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetTreeResponse(const GetTreeResponse& from);
  GetTreeResponse(GetTreeResponse&& from) noexcept
    : GetTreeResponse() {
    *this = ::std::move(from);
  }

  inline GetTreeResponse& operator=(const GetTreeResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetTreeResponse& operator=(GetTreeResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetTreeResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetTreeResponse* internal_default_instance() {
    return reinterpret_cast<const GetTreeResponse*>(
               &_GetTreeResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    13;

  friend void swap(GetTreeResponse& a, GetTreeResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetTreeResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetTreeResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetTreeResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetTreeResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetTreeResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetTreeResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetTreeResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.GetTreeResponse";
  }
  protected:
  explicit GetTreeResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kNodeFieldNumber = 1,
  };
  // .carbon.config.proto.ConfigNode node = 1;
  bool has_node() const;
  private:
  bool _internal_has_node() const;
  public:
  void clear_node();
  const ::carbon::config::proto::ConfigNode& node() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::ConfigNode* release_node();
  ::carbon::config::proto::ConfigNode* mutable_node();
  void set_allocated_node(::carbon::config::proto::ConfigNode* node);
  private:
  const ::carbon::config::proto::ConfigNode& _internal_node() const;
  ::carbon::config::proto::ConfigNode* _internal_mutable_node();
  public:
  void unsafe_arena_set_allocated_node(
      ::carbon::config::proto::ConfigNode* node);
  ::carbon::config::proto::ConfigNode* unsafe_arena_release_node();

  // @@protoc_insertion_point(class_scope:carbon.config.proto.GetTreeResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::carbon::config::proto::ConfigNode* node_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SetTreeRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.SetTreeRequest) */ {
 public:
  inline SetTreeRequest() : SetTreeRequest(nullptr) {}
  ~SetTreeRequest() override;
  explicit constexpr SetTreeRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetTreeRequest(const SetTreeRequest& from);
  SetTreeRequest(SetTreeRequest&& from) noexcept
    : SetTreeRequest() {
    *this = ::std::move(from);
  }

  inline SetTreeRequest& operator=(const SetTreeRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetTreeRequest& operator=(SetTreeRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetTreeRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetTreeRequest* internal_default_instance() {
    return reinterpret_cast<const SetTreeRequest*>(
               &_SetTreeRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    14;

  friend void swap(SetTreeRequest& a, SetTreeRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SetTreeRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetTreeRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetTreeRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetTreeRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SetTreeRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SetTreeRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SetTreeRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.SetTreeRequest";
  }
  protected:
  explicit SetTreeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kNodeFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // .carbon.config.proto.ConfigNode node = 2;
  bool has_node() const;
  private:
  bool _internal_has_node() const;
  public:
  void clear_node();
  const ::carbon::config::proto::ConfigNode& node() const;
  PROTOBUF_NODISCARD ::carbon::config::proto::ConfigNode* release_node();
  ::carbon::config::proto::ConfigNode* mutable_node();
  void set_allocated_node(::carbon::config::proto::ConfigNode* node);
  private:
  const ::carbon::config::proto::ConfigNode& _internal_node() const;
  ::carbon::config::proto::ConfigNode* _internal_mutable_node();
  public:
  void unsafe_arena_set_allocated_node(
      ::carbon::config::proto::ConfigNode* node);
  ::carbon::config::proto::ConfigNode* unsafe_arena_release_node();

  // @@protoc_insertion_point(class_scope:carbon.config.proto.SetTreeRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::carbon::config::proto::ConfigNode* node_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SetTreeResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.config.proto.SetTreeResponse) */ {
 public:
  inline SetTreeResponse() : SetTreeResponse(nullptr) {}
  explicit constexpr SetTreeResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SetTreeResponse(const SetTreeResponse& from);
  SetTreeResponse(SetTreeResponse&& from) noexcept
    : SetTreeResponse() {
    *this = ::std::move(from);
  }

  inline SetTreeResponse& operator=(const SetTreeResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline SetTreeResponse& operator=(SetTreeResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SetTreeResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const SetTreeResponse* internal_default_instance() {
    return reinterpret_cast<const SetTreeResponse*>(
               &_SetTreeResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    15;

  friend void swap(SetTreeResponse& a, SetTreeResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(SetTreeResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SetTreeResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SetTreeResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SetTreeResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const SetTreeResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const SetTreeResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.SetTreeResponse";
  }
  protected:
  explicit SetTreeResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.config.proto.SetTreeResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetLeavesRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.GetLeavesRequest) */ {
 public:
  inline GetLeavesRequest() : GetLeavesRequest(nullptr) {}
  ~GetLeavesRequest() override;
  explicit constexpr GetLeavesRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetLeavesRequest(const GetLeavesRequest& from);
  GetLeavesRequest(GetLeavesRequest&& from) noexcept
    : GetLeavesRequest() {
    *this = ::std::move(from);
  }

  inline GetLeavesRequest& operator=(const GetLeavesRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetLeavesRequest& operator=(GetLeavesRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetLeavesRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetLeavesRequest* internal_default_instance() {
    return reinterpret_cast<const GetLeavesRequest*>(
               &_GetLeavesRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    16;

  friend void swap(GetLeavesRequest& a, GetLeavesRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetLeavesRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetLeavesRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetLeavesRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetLeavesRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetLeavesRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetLeavesRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetLeavesRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.GetLeavesRequest";
  }
  protected:
  explicit GetLeavesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.GetLeavesRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class GetLeavesResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.GetLeavesResponse) */ {
 public:
  inline GetLeavesResponse() : GetLeavesResponse(nullptr) {}
  ~GetLeavesResponse() override;
  explicit constexpr GetLeavesResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetLeavesResponse(const GetLeavesResponse& from);
  GetLeavesResponse(GetLeavesResponse&& from) noexcept
    : GetLeavesResponse() {
    *this = ::std::move(from);
  }

  inline GetLeavesResponse& operator=(const GetLeavesResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetLeavesResponse& operator=(GetLeavesResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetLeavesResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetLeavesResponse* internal_default_instance() {
    return reinterpret_cast<const GetLeavesResponse*>(
               &_GetLeavesResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    17;

  friend void swap(GetLeavesResponse& a, GetLeavesResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetLeavesResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetLeavesResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetLeavesResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetLeavesResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetLeavesResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetLeavesResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetLeavesResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.GetLeavesResponse";
  }
  protected:
  explicit GetLeavesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kLeavesFieldNumber = 1,
  };
  // repeated .carbon.config.proto.ConfigLeaf leaves = 1;
  int leaves_size() const;
  private:
  int _internal_leaves_size() const;
  public:
  void clear_leaves();
  ::carbon::config::proto::ConfigLeaf* mutable_leaves(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigLeaf >*
      mutable_leaves();
  private:
  const ::carbon::config::proto::ConfigLeaf& _internal_leaves(int index) const;
  ::carbon::config::proto::ConfigLeaf* _internal_add_leaves();
  public:
  const ::carbon::config::proto::ConfigLeaf& leaves(int index) const;
  ::carbon::config::proto::ConfigLeaf* add_leaves();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigLeaf >&
      leaves() const;

  // @@protoc_insertion_point(class_scope:carbon.config.proto.GetLeavesResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigLeaf > leaves_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class AddToListRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.AddToListRequest) */ {
 public:
  inline AddToListRequest() : AddToListRequest(nullptr) {}
  ~AddToListRequest() override;
  explicit constexpr AddToListRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AddToListRequest(const AddToListRequest& from);
  AddToListRequest(AddToListRequest&& from) noexcept
    : AddToListRequest() {
    *this = ::std::move(from);
  }

  inline AddToListRequest& operator=(const AddToListRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline AddToListRequest& operator=(AddToListRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AddToListRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const AddToListRequest* internal_default_instance() {
    return reinterpret_cast<const AddToListRequest*>(
               &_AddToListRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    18;

  friend void swap(AddToListRequest& a, AddToListRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(AddToListRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AddToListRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AddToListRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AddToListRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const AddToListRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const AddToListRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(AddToListRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.AddToListRequest";
  }
  protected:
  explicit AddToListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kNameFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.AddToListRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class AddToListResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.config.proto.AddToListResponse) */ {
 public:
  inline AddToListResponse() : AddToListResponse(nullptr) {}
  explicit constexpr AddToListResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  AddToListResponse(const AddToListResponse& from);
  AddToListResponse(AddToListResponse&& from) noexcept
    : AddToListResponse() {
    *this = ::std::move(from);
  }

  inline AddToListResponse& operator=(const AddToListResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline AddToListResponse& operator=(AddToListResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const AddToListResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const AddToListResponse* internal_default_instance() {
    return reinterpret_cast<const AddToListResponse*>(
               &_AddToListResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    19;

  friend void swap(AddToListResponse& a, AddToListResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(AddToListResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(AddToListResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  AddToListResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<AddToListResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const AddToListResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const AddToListResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.AddToListResponse";
  }
  protected:
  explicit AddToListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.config.proto.AddToListResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RemoveFromListRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.RemoveFromListRequest) */ {
 public:
  inline RemoveFromListRequest() : RemoveFromListRequest(nullptr) {}
  ~RemoveFromListRequest() override;
  explicit constexpr RemoveFromListRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RemoveFromListRequest(const RemoveFromListRequest& from);
  RemoveFromListRequest(RemoveFromListRequest&& from) noexcept
    : RemoveFromListRequest() {
    *this = ::std::move(from);
  }

  inline RemoveFromListRequest& operator=(const RemoveFromListRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline RemoveFromListRequest& operator=(RemoveFromListRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RemoveFromListRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const RemoveFromListRequest* internal_default_instance() {
    return reinterpret_cast<const RemoveFromListRequest*>(
               &_RemoveFromListRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    20;

  friend void swap(RemoveFromListRequest& a, RemoveFromListRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(RemoveFromListRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RemoveFromListRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RemoveFromListRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RemoveFromListRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const RemoveFromListRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const RemoveFromListRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(RemoveFromListRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.RemoveFromListRequest";
  }
  protected:
  explicit RemoveFromListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeyFieldNumber = 1,
    kNameFieldNumber = 2,
  };
  // string key = 1;
  void clear_key();
  const std::string& key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_key();
  PROTOBUF_NODISCARD std::string* release_key();
  void set_allocated_key(std::string* key);
  private:
  const std::string& _internal_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_key(const std::string& value);
  std::string* _internal_mutable_key();
  public:

  // string name = 2;
  void clear_name();
  const std::string& name() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_name(ArgT0&& arg0, ArgT... args);
  std::string* mutable_name();
  PROTOBUF_NODISCARD std::string* release_name();
  void set_allocated_name(std::string* name);
  private:
  const std::string& _internal_name() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_name(const std::string& value);
  std::string* _internal_mutable_name();
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.RemoveFromListRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr name_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class RemoveFromListResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.config.proto.RemoveFromListResponse) */ {
 public:
  inline RemoveFromListResponse() : RemoveFromListResponse(nullptr) {}
  explicit constexpr RemoveFromListResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  RemoveFromListResponse(const RemoveFromListResponse& from);
  RemoveFromListResponse(RemoveFromListResponse&& from) noexcept
    : RemoveFromListResponse() {
    *this = ::std::move(from);
  }

  inline RemoveFromListResponse& operator=(const RemoveFromListResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline RemoveFromListResponse& operator=(RemoveFromListResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const RemoveFromListResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const RemoveFromListResponse* internal_default_instance() {
    return reinterpret_cast<const RemoveFromListResponse*>(
               &_RemoveFromListResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    21;

  friend void swap(RemoveFromListResponse& a, RemoveFromListResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(RemoveFromListResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(RemoveFromListResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  RemoveFromListResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<RemoveFromListResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const RemoveFromListResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const RemoveFromListResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.RemoveFromListResponse";
  }
  protected:
  explicit RemoveFromListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.config.proto.RemoveFromListResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SubscriptionRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.SubscriptionRequest) */ {
 public:
  inline SubscriptionRequest() : SubscriptionRequest(nullptr) {}
  ~SubscriptionRequest() override;
  explicit constexpr SubscriptionRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SubscriptionRequest(const SubscriptionRequest& from);
  SubscriptionRequest(SubscriptionRequest&& from) noexcept
    : SubscriptionRequest() {
    *this = ::std::move(from);
  }

  inline SubscriptionRequest& operator=(const SubscriptionRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline SubscriptionRequest& operator=(SubscriptionRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SubscriptionRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const SubscriptionRequest* internal_default_instance() {
    return reinterpret_cast<const SubscriptionRequest*>(
               &_SubscriptionRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    22;

  friend void swap(SubscriptionRequest& a, SubscriptionRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(SubscriptionRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SubscriptionRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SubscriptionRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SubscriptionRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SubscriptionRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SubscriptionRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SubscriptionRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.SubscriptionRequest";
  }
  protected:
  explicit SubscriptionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kKeysFieldNumber = 1,
  };
  // repeated string keys = 1;
  int keys_size() const;
  private:
  int _internal_keys_size() const;
  public:
  void clear_keys();
  const std::string& keys(int index) const;
  std::string* mutable_keys(int index);
  void set_keys(int index, const std::string& value);
  void set_keys(int index, std::string&& value);
  void set_keys(int index, const char* value);
  void set_keys(int index, const char* value, size_t size);
  std::string* add_keys();
  void add_keys(const std::string& value);
  void add_keys(std::string&& value);
  void add_keys(const char* value);
  void add_keys(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& keys() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_keys();
  private:
  const std::string& _internal_keys(int index) const;
  std::string* _internal_add_keys();
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.SubscriptionRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> keys_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class SubscriptionNotifyMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.SubscriptionNotifyMessage) */ {
 public:
  inline SubscriptionNotifyMessage() : SubscriptionNotifyMessage(nullptr) {}
  ~SubscriptionNotifyMessage() override;
  explicit constexpr SubscriptionNotifyMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SubscriptionNotifyMessage(const SubscriptionNotifyMessage& from);
  SubscriptionNotifyMessage(SubscriptionNotifyMessage&& from) noexcept
    : SubscriptionNotifyMessage() {
    *this = ::std::move(from);
  }

  inline SubscriptionNotifyMessage& operator=(const SubscriptionNotifyMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline SubscriptionNotifyMessage& operator=(SubscriptionNotifyMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SubscriptionNotifyMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const SubscriptionNotifyMessage* internal_default_instance() {
    return reinterpret_cast<const SubscriptionNotifyMessage*>(
               &_SubscriptionNotifyMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    23;

  friend void swap(SubscriptionNotifyMessage& a, SubscriptionNotifyMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(SubscriptionNotifyMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SubscriptionNotifyMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SubscriptionNotifyMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SubscriptionNotifyMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SubscriptionNotifyMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SubscriptionNotifyMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SubscriptionNotifyMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.SubscriptionNotifyMessage";
  }
  protected:
  explicit SubscriptionNotifyMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kSubscriptionKeyFieldNumber = 1,
    kNotifyKeyFieldNumber = 2,
  };
  // string subscription_key = 1;
  void clear_subscription_key();
  const std::string& subscription_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_subscription_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_subscription_key();
  PROTOBUF_NODISCARD std::string* release_subscription_key();
  void set_allocated_subscription_key(std::string* subscription_key);
  private:
  const std::string& _internal_subscription_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_subscription_key(const std::string& value);
  std::string* _internal_mutable_subscription_key();
  public:

  // string notify_key = 2;
  void clear_notify_key();
  const std::string& notify_key() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_notify_key(ArgT0&& arg0, ArgT... args);
  std::string* mutable_notify_key();
  PROTOBUF_NODISCARD std::string* release_notify_key();
  void set_allocated_notify_key(std::string* notify_key);
  private:
  const std::string& _internal_notify_key() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_notify_key(const std::string& value);
  std::string* _internal_mutable_notify_key();
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.SubscriptionNotifyMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr subscription_key_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr notify_key_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class UpgradeCloudConfigRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.config.proto.UpgradeCloudConfigRequest) */ {
 public:
  inline UpgradeCloudConfigRequest() : UpgradeCloudConfigRequest(nullptr) {}
  ~UpgradeCloudConfigRequest() override;
  explicit constexpr UpgradeCloudConfigRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpgradeCloudConfigRequest(const UpgradeCloudConfigRequest& from);
  UpgradeCloudConfigRequest(UpgradeCloudConfigRequest&& from) noexcept
    : UpgradeCloudConfigRequest() {
    *this = ::std::move(from);
  }

  inline UpgradeCloudConfigRequest& operator=(const UpgradeCloudConfigRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpgradeCloudConfigRequest& operator=(UpgradeCloudConfigRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpgradeCloudConfigRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpgradeCloudConfigRequest* internal_default_instance() {
    return reinterpret_cast<const UpgradeCloudConfigRequest*>(
               &_UpgradeCloudConfigRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    24;

  friend void swap(UpgradeCloudConfigRequest& a, UpgradeCloudConfigRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UpgradeCloudConfigRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpgradeCloudConfigRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpgradeCloudConfigRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpgradeCloudConfigRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UpgradeCloudConfigRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UpgradeCloudConfigRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpgradeCloudConfigRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.UpgradeCloudConfigRequest";
  }
  protected:
  explicit UpgradeCloudConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotFieldNumber = 1,
    kToTemplateFieldNumber = 2,
    kFromTemplateFieldNumber = 3,
  };
  // string robot = 1;
  void clear_robot();
  const std::string& robot() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot();
  PROTOBUF_NODISCARD std::string* release_robot();
  void set_allocated_robot(std::string* robot);
  private:
  const std::string& _internal_robot() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot(const std::string& value);
  std::string* _internal_mutable_robot();
  public:

  // string to_template = 2;
  void clear_to_template();
  const std::string& to_template() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_to_template(ArgT0&& arg0, ArgT... args);
  std::string* mutable_to_template();
  PROTOBUF_NODISCARD std::string* release_to_template();
  void set_allocated_to_template(std::string* to_template);
  private:
  const std::string& _internal_to_template() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_to_template(const std::string& value);
  std::string* _internal_mutable_to_template();
  public:

  // string from_template = 3;
  void clear_from_template();
  const std::string& from_template() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_from_template(ArgT0&& arg0, ArgT... args);
  std::string* mutable_from_template();
  PROTOBUF_NODISCARD std::string* release_from_template();
  void set_allocated_from_template(std::string* from_template);
  private:
  const std::string& _internal_from_template() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_from_template(const std::string& value);
  std::string* _internal_mutable_from_template();
  public:

  // @@protoc_insertion_point(class_scope:carbon.config.proto.UpgradeCloudConfigRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr to_template_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr from_template_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// -------------------------------------------------------------------

class UpgradeCloudConfigResponse final :
    public ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase /* @@protoc_insertion_point(class_definition:carbon.config.proto.UpgradeCloudConfigResponse) */ {
 public:
  inline UpgradeCloudConfigResponse() : UpgradeCloudConfigResponse(nullptr) {}
  explicit constexpr UpgradeCloudConfigResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpgradeCloudConfigResponse(const UpgradeCloudConfigResponse& from);
  UpgradeCloudConfigResponse(UpgradeCloudConfigResponse&& from) noexcept
    : UpgradeCloudConfigResponse() {
    *this = ::std::move(from);
  }

  inline UpgradeCloudConfigResponse& operator=(const UpgradeCloudConfigResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpgradeCloudConfigResponse& operator=(UpgradeCloudConfigResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpgradeCloudConfigResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpgradeCloudConfigResponse* internal_default_instance() {
    return reinterpret_cast<const UpgradeCloudConfigResponse*>(
               &_UpgradeCloudConfigResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    25;

  friend void swap(UpgradeCloudConfigResponse& a, UpgradeCloudConfigResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(UpgradeCloudConfigResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpgradeCloudConfigResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpgradeCloudConfigResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpgradeCloudConfigResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyFrom;
  inline void CopyFrom(const UpgradeCloudConfigResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl(this, from);
  }
  using ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeFrom;
  void MergeFrom(const UpgradeCloudConfigResponse& from) {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl(this, from);
  }
  public:

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.config.proto.UpgradeCloudConfigResponse";
  }
  protected:
  explicit UpgradeCloudConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  // @@protoc_insertion_point(class_scope:carbon.config.proto.UpgradeCloudConfigResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// PingRequest

// int32 x = 1;
inline void PingRequest::clear_x() {
  x_ = 0;
}
inline int32_t PingRequest::_internal_x() const {
  return x_;
}
inline int32_t PingRequest::x() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.PingRequest.x)
  return _internal_x();
}
inline void PingRequest::_internal_set_x(int32_t value) {
  
  x_ = value;
}
inline void PingRequest::set_x(int32_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.PingRequest.x)
}

// -------------------------------------------------------------------

// PongResponse

// int32 x = 1;
inline void PongResponse::clear_x() {
  x_ = 0;
}
inline int32_t PongResponse::_internal_x() const {
  return x_;
}
inline int32_t PongResponse::x() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.PongResponse.x)
  return _internal_x();
}
inline void PongResponse::_internal_set_x(int32_t value) {
  
  x_ = value;
}
inline void PongResponse::set_x(int32_t value) {
  _internal_set_x(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.PongResponse.x)
}

// -------------------------------------------------------------------

// ConfigValue

// int64 int64_val = 1;
inline bool ConfigValue::_internal_has_int64_val() const {
  return value_case() == kInt64Val;
}
inline bool ConfigValue::has_int64_val() const {
  return _internal_has_int64_val();
}
inline void ConfigValue::set_has_int64_val() {
  _oneof_case_[0] = kInt64Val;
}
inline void ConfigValue::clear_int64_val() {
  if (_internal_has_int64_val()) {
    value_.int64_val_ = int64_t{0};
    clear_has_value();
  }
}
inline int64_t ConfigValue::_internal_int64_val() const {
  if (_internal_has_int64_val()) {
    return value_.int64_val_;
  }
  return int64_t{0};
}
inline void ConfigValue::_internal_set_int64_val(int64_t value) {
  if (!_internal_has_int64_val()) {
    clear_value();
    set_has_int64_val();
  }
  value_.int64_val_ = value;
}
inline int64_t ConfigValue::int64_val() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigValue.int64_val)
  return _internal_int64_val();
}
inline void ConfigValue::set_int64_val(int64_t value) {
  _internal_set_int64_val(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigValue.int64_val)
}

// uint64 uint64_val = 2;
inline bool ConfigValue::_internal_has_uint64_val() const {
  return value_case() == kUint64Val;
}
inline bool ConfigValue::has_uint64_val() const {
  return _internal_has_uint64_val();
}
inline void ConfigValue::set_has_uint64_val() {
  _oneof_case_[0] = kUint64Val;
}
inline void ConfigValue::clear_uint64_val() {
  if (_internal_has_uint64_val()) {
    value_.uint64_val_ = uint64_t{0u};
    clear_has_value();
  }
}
inline uint64_t ConfigValue::_internal_uint64_val() const {
  if (_internal_has_uint64_val()) {
    return value_.uint64_val_;
  }
  return uint64_t{0u};
}
inline void ConfigValue::_internal_set_uint64_val(uint64_t value) {
  if (!_internal_has_uint64_val()) {
    clear_value();
    set_has_uint64_val();
  }
  value_.uint64_val_ = value;
}
inline uint64_t ConfigValue::uint64_val() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigValue.uint64_val)
  return _internal_uint64_val();
}
inline void ConfigValue::set_uint64_val(uint64_t value) {
  _internal_set_uint64_val(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigValue.uint64_val)
}

// bool bool_val = 3;
inline bool ConfigValue::_internal_has_bool_val() const {
  return value_case() == kBoolVal;
}
inline bool ConfigValue::has_bool_val() const {
  return _internal_has_bool_val();
}
inline void ConfigValue::set_has_bool_val() {
  _oneof_case_[0] = kBoolVal;
}
inline void ConfigValue::clear_bool_val() {
  if (_internal_has_bool_val()) {
    value_.bool_val_ = false;
    clear_has_value();
  }
}
inline bool ConfigValue::_internal_bool_val() const {
  if (_internal_has_bool_val()) {
    return value_.bool_val_;
  }
  return false;
}
inline void ConfigValue::_internal_set_bool_val(bool value) {
  if (!_internal_has_bool_val()) {
    clear_value();
    set_has_bool_val();
  }
  value_.bool_val_ = value;
}
inline bool ConfigValue::bool_val() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigValue.bool_val)
  return _internal_bool_val();
}
inline void ConfigValue::set_bool_val(bool value) {
  _internal_set_bool_val(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigValue.bool_val)
}

// double float_val = 4;
inline bool ConfigValue::_internal_has_float_val() const {
  return value_case() == kFloatVal;
}
inline bool ConfigValue::has_float_val() const {
  return _internal_has_float_val();
}
inline void ConfigValue::set_has_float_val() {
  _oneof_case_[0] = kFloatVal;
}
inline void ConfigValue::clear_float_val() {
  if (_internal_has_float_val()) {
    value_.float_val_ = 0;
    clear_has_value();
  }
}
inline double ConfigValue::_internal_float_val() const {
  if (_internal_has_float_val()) {
    return value_.float_val_;
  }
  return 0;
}
inline void ConfigValue::_internal_set_float_val(double value) {
  if (!_internal_has_float_val()) {
    clear_value();
    set_has_float_val();
  }
  value_.float_val_ = value;
}
inline double ConfigValue::float_val() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigValue.float_val)
  return _internal_float_val();
}
inline void ConfigValue::set_float_val(double value) {
  _internal_set_float_val(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigValue.float_val)
}

// string string_val = 5;
inline bool ConfigValue::_internal_has_string_val() const {
  return value_case() == kStringVal;
}
inline bool ConfigValue::has_string_val() const {
  return _internal_has_string_val();
}
inline void ConfigValue::set_has_string_val() {
  _oneof_case_[0] = kStringVal;
}
inline void ConfigValue::clear_string_val() {
  if (_internal_has_string_val()) {
    value_.string_val_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
    clear_has_value();
  }
}
inline const std::string& ConfigValue::string_val() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigValue.string_val)
  return _internal_string_val();
}
template <typename ArgT0, typename... ArgT>
inline void ConfigValue::set_string_val(ArgT0&& arg0, ArgT... args) {
  if (!_internal_has_string_val()) {
    clear_value();
    set_has_string_val();
    value_.string_val_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.string_val_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigValue.string_val)
}
inline std::string* ConfigValue::mutable_string_val() {
  std::string* _s = _internal_mutable_string_val();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigValue.string_val)
  return _s;
}
inline const std::string& ConfigValue::_internal_string_val() const {
  if (_internal_has_string_val()) {
    return value_.string_val_.Get();
  }
  return ::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited();
}
inline void ConfigValue::_internal_set_string_val(const std::string& value) {
  if (!_internal_has_string_val()) {
    clear_value();
    set_has_string_val();
    value_.string_val_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  value_.string_val_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigValue::_internal_mutable_string_val() {
  if (!_internal_has_string_val()) {
    clear_value();
    set_has_string_val();
    value_.string_val_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  }
  return value_.string_val_.Mutable(
      ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigValue::release_string_val() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigValue.string_val)
  if (_internal_has_string_val()) {
    clear_has_value();
    return value_.string_val_.ReleaseNonDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
  } else {
    return nullptr;
  }
}
inline void ConfigValue::set_allocated_string_val(std::string* string_val) {
  if (has_value()) {
    clear_value();
  }
  if (string_val != nullptr) {
    set_has_string_val();
    value_.string_val_.UnsafeSetDefault(string_val);
    ::PROTOBUF_NAMESPACE_ID::Arena* arena = GetArenaForAllocation();
    if (arena != nullptr) {
      arena->Own(string_val);
    }
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigValue.string_val)
}

// uint64 timestamp_ms = 6;
inline void ConfigValue::clear_timestamp_ms() {
  timestamp_ms_ = uint64_t{0u};
}
inline uint64_t ConfigValue::_internal_timestamp_ms() const {
  return timestamp_ms_;
}
inline uint64_t ConfigValue::timestamp_ms() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigValue.timestamp_ms)
  return _internal_timestamp_ms();
}
inline void ConfigValue::_internal_set_timestamp_ms(uint64_t value) {
  
  timestamp_ms_ = value;
}
inline void ConfigValue::set_timestamp_ms(uint64_t value) {
  _internal_set_timestamp_ms(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigValue.timestamp_ms)
}

inline bool ConfigValue::has_value() const {
  return value_case() != VALUE_NOT_SET;
}
inline void ConfigValue::clear_has_value() {
  _oneof_case_[0] = VALUE_NOT_SET;
}
inline ConfigValue::ValueCase ConfigValue::value_case() const {
  return ConfigValue::ValueCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// IntConfigDef

// int64 min = 1;
inline void IntConfigDef::clear_min() {
  min_ = int64_t{0};
}
inline int64_t IntConfigDef::_internal_min() const {
  return min_;
}
inline int64_t IntConfigDef::min() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.IntConfigDef.min)
  return _internal_min();
}
inline void IntConfigDef::_internal_set_min(int64_t value) {
  
  min_ = value;
}
inline void IntConfigDef::set_min(int64_t value) {
  _internal_set_min(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.IntConfigDef.min)
}

// int64 max = 2;
inline void IntConfigDef::clear_max() {
  max_ = int64_t{0};
}
inline int64_t IntConfigDef::_internal_max() const {
  return max_;
}
inline int64_t IntConfigDef::max() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.IntConfigDef.max)
  return _internal_max();
}
inline void IntConfigDef::_internal_set_max(int64_t value) {
  
  max_ = value;
}
inline void IntConfigDef::set_max(int64_t value) {
  _internal_set_max(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.IntConfigDef.max)
}

// int64 step = 3;
inline void IntConfigDef::clear_step() {
  step_ = int64_t{0};
}
inline int64_t IntConfigDef::_internal_step() const {
  return step_;
}
inline int64_t IntConfigDef::step() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.IntConfigDef.step)
  return _internal_step();
}
inline void IntConfigDef::_internal_set_step(int64_t value) {
  
  step_ = value;
}
inline void IntConfigDef::set_step(int64_t value) {
  _internal_set_step(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.IntConfigDef.step)
}

// -------------------------------------------------------------------

// UIntConfigDef

// uint64 min = 1;
inline void UIntConfigDef::clear_min() {
  min_ = uint64_t{0u};
}
inline uint64_t UIntConfigDef::_internal_min() const {
  return min_;
}
inline uint64_t UIntConfigDef::min() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.UIntConfigDef.min)
  return _internal_min();
}
inline void UIntConfigDef::_internal_set_min(uint64_t value) {
  
  min_ = value;
}
inline void UIntConfigDef::set_min(uint64_t value) {
  _internal_set_min(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.UIntConfigDef.min)
}

// uint64 max = 2;
inline void UIntConfigDef::clear_max() {
  max_ = uint64_t{0u};
}
inline uint64_t UIntConfigDef::_internal_max() const {
  return max_;
}
inline uint64_t UIntConfigDef::max() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.UIntConfigDef.max)
  return _internal_max();
}
inline void UIntConfigDef::_internal_set_max(uint64_t value) {
  
  max_ = value;
}
inline void UIntConfigDef::set_max(uint64_t value) {
  _internal_set_max(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.UIntConfigDef.max)
}

// uint64 step = 3;
inline void UIntConfigDef::clear_step() {
  step_ = uint64_t{0u};
}
inline uint64_t UIntConfigDef::_internal_step() const {
  return step_;
}
inline uint64_t UIntConfigDef::step() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.UIntConfigDef.step)
  return _internal_step();
}
inline void UIntConfigDef::_internal_set_step(uint64_t value) {
  
  step_ = value;
}
inline void UIntConfigDef::set_step(uint64_t value) {
  _internal_set_step(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.UIntConfigDef.step)
}

// -------------------------------------------------------------------

// FloatConfigDef

// double min = 1;
inline void FloatConfigDef::clear_min() {
  min_ = 0;
}
inline double FloatConfigDef::_internal_min() const {
  return min_;
}
inline double FloatConfigDef::min() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.FloatConfigDef.min)
  return _internal_min();
}
inline void FloatConfigDef::_internal_set_min(double value) {
  
  min_ = value;
}
inline void FloatConfigDef::set_min(double value) {
  _internal_set_min(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.FloatConfigDef.min)
}

// double max = 2;
inline void FloatConfigDef::clear_max() {
  max_ = 0;
}
inline double FloatConfigDef::_internal_max() const {
  return max_;
}
inline double FloatConfigDef::max() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.FloatConfigDef.max)
  return _internal_max();
}
inline void FloatConfigDef::_internal_set_max(double value) {
  
  max_ = value;
}
inline void FloatConfigDef::set_max(double value) {
  _internal_set_max(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.FloatConfigDef.max)
}

// double step = 3;
inline void FloatConfigDef::clear_step() {
  step_ = 0;
}
inline double FloatConfigDef::_internal_step() const {
  return step_;
}
inline double FloatConfigDef::step() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.FloatConfigDef.step)
  return _internal_step();
}
inline void FloatConfigDef::_internal_set_step(double value) {
  
  step_ = value;
}
inline void FloatConfigDef::set_step(double value) {
  _internal_set_step(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.FloatConfigDef.step)
}

// -------------------------------------------------------------------

// StringConfigDef

// uint32 size_limit = 1;
inline void StringConfigDef::clear_size_limit() {
  size_limit_ = 0u;
}
inline uint32_t StringConfigDef::_internal_size_limit() const {
  return size_limit_;
}
inline uint32_t StringConfigDef::size_limit() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.StringConfigDef.size_limit)
  return _internal_size_limit();
}
inline void StringConfigDef::_internal_set_size_limit(uint32_t value) {
  
  size_limit_ = value;
}
inline void StringConfigDef::set_size_limit(uint32_t value) {
  _internal_set_size_limit(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.StringConfigDef.size_limit)
}

// repeated string choices = 3;
inline int StringConfigDef::_internal_choices_size() const {
  return choices_.size();
}
inline int StringConfigDef::choices_size() const {
  return _internal_choices_size();
}
inline void StringConfigDef::clear_choices() {
  choices_.Clear();
}
inline std::string* StringConfigDef::add_choices() {
  std::string* _s = _internal_add_choices();
  // @@protoc_insertion_point(field_add_mutable:carbon.config.proto.StringConfigDef.choices)
  return _s;
}
inline const std::string& StringConfigDef::_internal_choices(int index) const {
  return choices_.Get(index);
}
inline const std::string& StringConfigDef::choices(int index) const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.StringConfigDef.choices)
  return _internal_choices(index);
}
inline std::string* StringConfigDef::mutable_choices(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.StringConfigDef.choices)
  return choices_.Mutable(index);
}
inline void StringConfigDef::set_choices(int index, const std::string& value) {
  choices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.StringConfigDef.choices)
}
inline void StringConfigDef::set_choices(int index, std::string&& value) {
  choices_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.config.proto.StringConfigDef.choices)
}
inline void StringConfigDef::set_choices(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  choices_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.config.proto.StringConfigDef.choices)
}
inline void StringConfigDef::set_choices(int index, const char* value, size_t size) {
  choices_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.config.proto.StringConfigDef.choices)
}
inline std::string* StringConfigDef::_internal_add_choices() {
  return choices_.Add();
}
inline void StringConfigDef::add_choices(const std::string& value) {
  choices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.config.proto.StringConfigDef.choices)
}
inline void StringConfigDef::add_choices(std::string&& value) {
  choices_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.config.proto.StringConfigDef.choices)
}
inline void StringConfigDef::add_choices(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  choices_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.config.proto.StringConfigDef.choices)
}
inline void StringConfigDef::add_choices(const char* value, size_t size) {
  choices_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.config.proto.StringConfigDef.choices)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
StringConfigDef::choices() const {
  // @@protoc_insertion_point(field_list:carbon.config.proto.StringConfigDef.choices)
  return choices_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
StringConfigDef::mutable_choices() {
  // @@protoc_insertion_point(field_mutable_list:carbon.config.proto.StringConfigDef.choices)
  return &choices_;
}

// -------------------------------------------------------------------

// ConfigDef

// .carbon.config.proto.ConfigType type = 1;
inline void ConfigDef::clear_type() {
  type_ = 0;
}
inline ::carbon::config::proto::ConfigType ConfigDef::_internal_type() const {
  return static_cast< ::carbon::config::proto::ConfigType >(type_);
}
inline ::carbon::config::proto::ConfigType ConfigDef::type() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.type)
  return _internal_type();
}
inline void ConfigDef::_internal_set_type(::carbon::config::proto::ConfigType value) {
  
  type_ = value;
}
inline void ConfigDef::set_type(::carbon::config::proto::ConfigType value) {
  _internal_set_type(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigDef.type)
}

// .carbon.config.proto.ConfigComplexity complexity = 2;
inline void ConfigDef::clear_complexity() {
  complexity_ = 0;
}
inline ::carbon::config::proto::ConfigComplexity ConfigDef::_internal_complexity() const {
  return static_cast< ::carbon::config::proto::ConfigComplexity >(complexity_);
}
inline ::carbon::config::proto::ConfigComplexity ConfigDef::complexity() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.complexity)
  return _internal_complexity();
}
inline void ConfigDef::_internal_set_complexity(::carbon::config::proto::ConfigComplexity value) {
  
  complexity_ = value;
}
inline void ConfigDef::set_complexity(::carbon::config::proto::ConfigComplexity value) {
  _internal_set_complexity(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigDef.complexity)
}

// .carbon.config.proto.IntConfigDef int_def = 3;
inline bool ConfigDef::_internal_has_int_def() const {
  return extra_case() == kIntDef;
}
inline bool ConfigDef::has_int_def() const {
  return _internal_has_int_def();
}
inline void ConfigDef::set_has_int_def() {
  _oneof_case_[0] = kIntDef;
}
inline void ConfigDef::clear_int_def() {
  if (_internal_has_int_def()) {
    if (GetArenaForAllocation() == nullptr) {
      delete extra_.int_def_;
    }
    clear_has_extra();
  }
}
inline ::carbon::config::proto::IntConfigDef* ConfigDef::release_int_def() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigDef.int_def)
  if (_internal_has_int_def()) {
    clear_has_extra();
      ::carbon::config::proto::IntConfigDef* temp = extra_.int_def_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    extra_.int_def_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::config::proto::IntConfigDef& ConfigDef::_internal_int_def() const {
  return _internal_has_int_def()
      ? *extra_.int_def_
      : reinterpret_cast< ::carbon::config::proto::IntConfigDef&>(::carbon::config::proto::_IntConfigDef_default_instance_);
}
inline const ::carbon::config::proto::IntConfigDef& ConfigDef::int_def() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.int_def)
  return _internal_int_def();
}
inline ::carbon::config::proto::IntConfigDef* ConfigDef::unsafe_arena_release_int_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.config.proto.ConfigDef.int_def)
  if (_internal_has_int_def()) {
    clear_has_extra();
    ::carbon::config::proto::IntConfigDef* temp = extra_.int_def_;
    extra_.int_def_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ConfigDef::unsafe_arena_set_allocated_int_def(::carbon::config::proto::IntConfigDef* int_def) {
  clear_extra();
  if (int_def) {
    set_has_int_def();
    extra_.int_def_ = int_def;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.ConfigDef.int_def)
}
inline ::carbon::config::proto::IntConfigDef* ConfigDef::_internal_mutable_int_def() {
  if (!_internal_has_int_def()) {
    clear_extra();
    set_has_int_def();
    extra_.int_def_ = CreateMaybeMessage< ::carbon::config::proto::IntConfigDef >(GetArenaForAllocation());
  }
  return extra_.int_def_;
}
inline ::carbon::config::proto::IntConfigDef* ConfigDef::mutable_int_def() {
  ::carbon::config::proto::IntConfigDef* _msg = _internal_mutable_int_def();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigDef.int_def)
  return _msg;
}

// .carbon.config.proto.UIntConfigDef uint_def = 4;
inline bool ConfigDef::_internal_has_uint_def() const {
  return extra_case() == kUintDef;
}
inline bool ConfigDef::has_uint_def() const {
  return _internal_has_uint_def();
}
inline void ConfigDef::set_has_uint_def() {
  _oneof_case_[0] = kUintDef;
}
inline void ConfigDef::clear_uint_def() {
  if (_internal_has_uint_def()) {
    if (GetArenaForAllocation() == nullptr) {
      delete extra_.uint_def_;
    }
    clear_has_extra();
  }
}
inline ::carbon::config::proto::UIntConfigDef* ConfigDef::release_uint_def() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigDef.uint_def)
  if (_internal_has_uint_def()) {
    clear_has_extra();
      ::carbon::config::proto::UIntConfigDef* temp = extra_.uint_def_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    extra_.uint_def_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::config::proto::UIntConfigDef& ConfigDef::_internal_uint_def() const {
  return _internal_has_uint_def()
      ? *extra_.uint_def_
      : reinterpret_cast< ::carbon::config::proto::UIntConfigDef&>(::carbon::config::proto::_UIntConfigDef_default_instance_);
}
inline const ::carbon::config::proto::UIntConfigDef& ConfigDef::uint_def() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.uint_def)
  return _internal_uint_def();
}
inline ::carbon::config::proto::UIntConfigDef* ConfigDef::unsafe_arena_release_uint_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.config.proto.ConfigDef.uint_def)
  if (_internal_has_uint_def()) {
    clear_has_extra();
    ::carbon::config::proto::UIntConfigDef* temp = extra_.uint_def_;
    extra_.uint_def_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ConfigDef::unsafe_arena_set_allocated_uint_def(::carbon::config::proto::UIntConfigDef* uint_def) {
  clear_extra();
  if (uint_def) {
    set_has_uint_def();
    extra_.uint_def_ = uint_def;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.ConfigDef.uint_def)
}
inline ::carbon::config::proto::UIntConfigDef* ConfigDef::_internal_mutable_uint_def() {
  if (!_internal_has_uint_def()) {
    clear_extra();
    set_has_uint_def();
    extra_.uint_def_ = CreateMaybeMessage< ::carbon::config::proto::UIntConfigDef >(GetArenaForAllocation());
  }
  return extra_.uint_def_;
}
inline ::carbon::config::proto::UIntConfigDef* ConfigDef::mutable_uint_def() {
  ::carbon::config::proto::UIntConfigDef* _msg = _internal_mutable_uint_def();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigDef.uint_def)
  return _msg;
}

// .carbon.config.proto.FloatConfigDef float_def = 5;
inline bool ConfigDef::_internal_has_float_def() const {
  return extra_case() == kFloatDef;
}
inline bool ConfigDef::has_float_def() const {
  return _internal_has_float_def();
}
inline void ConfigDef::set_has_float_def() {
  _oneof_case_[0] = kFloatDef;
}
inline void ConfigDef::clear_float_def() {
  if (_internal_has_float_def()) {
    if (GetArenaForAllocation() == nullptr) {
      delete extra_.float_def_;
    }
    clear_has_extra();
  }
}
inline ::carbon::config::proto::FloatConfigDef* ConfigDef::release_float_def() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigDef.float_def)
  if (_internal_has_float_def()) {
    clear_has_extra();
      ::carbon::config::proto::FloatConfigDef* temp = extra_.float_def_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    extra_.float_def_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::config::proto::FloatConfigDef& ConfigDef::_internal_float_def() const {
  return _internal_has_float_def()
      ? *extra_.float_def_
      : reinterpret_cast< ::carbon::config::proto::FloatConfigDef&>(::carbon::config::proto::_FloatConfigDef_default_instance_);
}
inline const ::carbon::config::proto::FloatConfigDef& ConfigDef::float_def() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.float_def)
  return _internal_float_def();
}
inline ::carbon::config::proto::FloatConfigDef* ConfigDef::unsafe_arena_release_float_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.config.proto.ConfigDef.float_def)
  if (_internal_has_float_def()) {
    clear_has_extra();
    ::carbon::config::proto::FloatConfigDef* temp = extra_.float_def_;
    extra_.float_def_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ConfigDef::unsafe_arena_set_allocated_float_def(::carbon::config::proto::FloatConfigDef* float_def) {
  clear_extra();
  if (float_def) {
    set_has_float_def();
    extra_.float_def_ = float_def;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.ConfigDef.float_def)
}
inline ::carbon::config::proto::FloatConfigDef* ConfigDef::_internal_mutable_float_def() {
  if (!_internal_has_float_def()) {
    clear_extra();
    set_has_float_def();
    extra_.float_def_ = CreateMaybeMessage< ::carbon::config::proto::FloatConfigDef >(GetArenaForAllocation());
  }
  return extra_.float_def_;
}
inline ::carbon::config::proto::FloatConfigDef* ConfigDef::mutable_float_def() {
  ::carbon::config::proto::FloatConfigDef* _msg = _internal_mutable_float_def();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigDef.float_def)
  return _msg;
}

// .carbon.config.proto.StringConfigDef string_def = 6;
inline bool ConfigDef::_internal_has_string_def() const {
  return extra_case() == kStringDef;
}
inline bool ConfigDef::has_string_def() const {
  return _internal_has_string_def();
}
inline void ConfigDef::set_has_string_def() {
  _oneof_case_[0] = kStringDef;
}
inline void ConfigDef::clear_string_def() {
  if (_internal_has_string_def()) {
    if (GetArenaForAllocation() == nullptr) {
      delete extra_.string_def_;
    }
    clear_has_extra();
  }
}
inline ::carbon::config::proto::StringConfigDef* ConfigDef::release_string_def() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigDef.string_def)
  if (_internal_has_string_def()) {
    clear_has_extra();
      ::carbon::config::proto::StringConfigDef* temp = extra_.string_def_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    extra_.string_def_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::config::proto::StringConfigDef& ConfigDef::_internal_string_def() const {
  return _internal_has_string_def()
      ? *extra_.string_def_
      : reinterpret_cast< ::carbon::config::proto::StringConfigDef&>(::carbon::config::proto::_StringConfigDef_default_instance_);
}
inline const ::carbon::config::proto::StringConfigDef& ConfigDef::string_def() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.string_def)
  return _internal_string_def();
}
inline ::carbon::config::proto::StringConfigDef* ConfigDef::unsafe_arena_release_string_def() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.config.proto.ConfigDef.string_def)
  if (_internal_has_string_def()) {
    clear_has_extra();
    ::carbon::config::proto::StringConfigDef* temp = extra_.string_def_;
    extra_.string_def_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void ConfigDef::unsafe_arena_set_allocated_string_def(::carbon::config::proto::StringConfigDef* string_def) {
  clear_extra();
  if (string_def) {
    set_has_string_def();
    extra_.string_def_ = string_def;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.ConfigDef.string_def)
}
inline ::carbon::config::proto::StringConfigDef* ConfigDef::_internal_mutable_string_def() {
  if (!_internal_has_string_def()) {
    clear_extra();
    set_has_string_def();
    extra_.string_def_ = CreateMaybeMessage< ::carbon::config::proto::StringConfigDef >(GetArenaForAllocation());
  }
  return extra_.string_def_;
}
inline ::carbon::config::proto::StringConfigDef* ConfigDef::mutable_string_def() {
  ::carbon::config::proto::StringConfigDef* _msg = _internal_mutable_string_def();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigDef.string_def)
  return _msg;
}

// string hint = 7;
inline void ConfigDef::clear_hint() {
  hint_.ClearToEmpty();
}
inline const std::string& ConfigDef::hint() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.hint)
  return _internal_hint();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigDef::set_hint(ArgT0&& arg0, ArgT... args) {
 
 hint_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigDef.hint)
}
inline std::string* ConfigDef::mutable_hint() {
  std::string* _s = _internal_mutable_hint();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigDef.hint)
  return _s;
}
inline const std::string& ConfigDef::_internal_hint() const {
  return hint_.Get();
}
inline void ConfigDef::_internal_set_hint(const std::string& value) {
  
  hint_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigDef::_internal_mutable_hint() {
  
  return hint_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigDef::release_hint() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigDef.hint)
  return hint_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigDef::set_allocated_hint(std::string* hint) {
  if (hint != nullptr) {
    
  } else {
    
  }
  hint_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), hint,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (hint_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    hint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigDef.hint)
}

// bool default_recommended = 8;
inline void ConfigDef::clear_default_recommended() {
  default_recommended_ = false;
}
inline bool ConfigDef::_internal_default_recommended() const {
  return default_recommended_;
}
inline bool ConfigDef::default_recommended() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.default_recommended)
  return _internal_default_recommended();
}
inline void ConfigDef::_internal_set_default_recommended(bool value) {
  
  default_recommended_ = value;
}
inline void ConfigDef::set_default_recommended(bool value) {
  _internal_set_default_recommended(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigDef.default_recommended)
}

// string units = 9;
inline void ConfigDef::clear_units() {
  units_.ClearToEmpty();
}
inline const std::string& ConfigDef::units() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigDef.units)
  return _internal_units();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigDef::set_units(ArgT0&& arg0, ArgT... args) {
 
 units_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigDef.units)
}
inline std::string* ConfigDef::mutable_units() {
  std::string* _s = _internal_mutable_units();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigDef.units)
  return _s;
}
inline const std::string& ConfigDef::_internal_units() const {
  return units_.Get();
}
inline void ConfigDef::_internal_set_units(const std::string& value) {
  
  units_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigDef::_internal_mutable_units() {
  
  return units_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigDef::release_units() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigDef.units)
  return units_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigDef::set_allocated_units(std::string* units) {
  if (units != nullptr) {
    
  } else {
    
  }
  units_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), units,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (units_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    units_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigDef.units)
}

inline bool ConfigDef::has_extra() const {
  return extra_case() != EXTRA_NOT_SET;
}
inline void ConfigDef::clear_has_extra() {
  _oneof_case_[0] = EXTRA_NOT_SET;
}
inline ConfigDef::ExtraCase ConfigDef::extra_case() const {
  return ConfigDef::ExtraCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// ConfigNode

// string name = 1;
inline void ConfigNode::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& ConfigNode::name() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigNode.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigNode::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigNode.name)
}
inline std::string* ConfigNode::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigNode.name)
  return _s;
}
inline const std::string& ConfigNode::_internal_name() const {
  return name_.Get();
}
inline void ConfigNode::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigNode::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigNode::release_name() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigNode.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigNode::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigNode.name)
}

// .carbon.config.proto.ConfigValue value = 2;
inline bool ConfigNode::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool ConfigNode::has_value() const {
  return _internal_has_value();
}
inline void ConfigNode::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
inline const ::carbon::config::proto::ConfigValue& ConfigNode::_internal_value() const {
  const ::carbon::config::proto::ConfigValue* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::config::proto::ConfigValue&>(
      ::carbon::config::proto::_ConfigValue_default_instance_);
}
inline const ::carbon::config::proto::ConfigValue& ConfigNode::value() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigNode.value)
  return _internal_value();
}
inline void ConfigNode::unsafe_arena_set_allocated_value(
    ::carbon::config::proto::ConfigValue* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.ConfigNode.value)
}
inline ::carbon::config::proto::ConfigValue* ConfigNode::release_value() {
  
  ::carbon::config::proto::ConfigValue* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::config::proto::ConfigValue* ConfigNode::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigNode.value)
  
  ::carbon::config::proto::ConfigValue* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::carbon::config::proto::ConfigValue* ConfigNode::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::config::proto::ConfigValue>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::carbon::config::proto::ConfigValue* ConfigNode::mutable_value() {
  ::carbon::config::proto::ConfigValue* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigNode.value)
  return _msg;
}
inline void ConfigNode::set_allocated_value(::carbon::config::proto::ConfigValue* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete value_;
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::ConfigValue>::GetOwningArena(value);
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigNode.value)
}

// .carbon.config.proto.ConfigDef def = 3;
inline bool ConfigNode::_internal_has_def() const {
  return this != internal_default_instance() && def_ != nullptr;
}
inline bool ConfigNode::has_def() const {
  return _internal_has_def();
}
inline void ConfigNode::clear_def() {
  if (GetArenaForAllocation() == nullptr && def_ != nullptr) {
    delete def_;
  }
  def_ = nullptr;
}
inline const ::carbon::config::proto::ConfigDef& ConfigNode::_internal_def() const {
  const ::carbon::config::proto::ConfigDef* p = def_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::config::proto::ConfigDef&>(
      ::carbon::config::proto::_ConfigDef_default_instance_);
}
inline const ::carbon::config::proto::ConfigDef& ConfigNode::def() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigNode.def)
  return _internal_def();
}
inline void ConfigNode::unsafe_arena_set_allocated_def(
    ::carbon::config::proto::ConfigDef* def) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(def_);
  }
  def_ = def;
  if (def) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.ConfigNode.def)
}
inline ::carbon::config::proto::ConfigDef* ConfigNode::release_def() {
  
  ::carbon::config::proto::ConfigDef* temp = def_;
  def_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::config::proto::ConfigDef* ConfigNode::unsafe_arena_release_def() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigNode.def)
  
  ::carbon::config::proto::ConfigDef* temp = def_;
  def_ = nullptr;
  return temp;
}
inline ::carbon::config::proto::ConfigDef* ConfigNode::_internal_mutable_def() {
  
  if (def_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::config::proto::ConfigDef>(GetArenaForAllocation());
    def_ = p;
  }
  return def_;
}
inline ::carbon::config::proto::ConfigDef* ConfigNode::mutable_def() {
  ::carbon::config::proto::ConfigDef* _msg = _internal_mutable_def();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigNode.def)
  return _msg;
}
inline void ConfigNode::set_allocated_def(::carbon::config::proto::ConfigDef* def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete def_;
  }
  if (def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::ConfigDef>::GetOwningArena(def);
    if (message_arena != submessage_arena) {
      def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, def, submessage_arena);
    }
    
  } else {
    
  }
  def_ = def;
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigNode.def)
}

// repeated .carbon.config.proto.ConfigNode children = 4;
inline int ConfigNode::_internal_children_size() const {
  return children_.size();
}
inline int ConfigNode::children_size() const {
  return _internal_children_size();
}
inline void ConfigNode::clear_children() {
  children_.Clear();
}
inline ::carbon::config::proto::ConfigNode* ConfigNode::mutable_children(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigNode.children)
  return children_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigNode >*
ConfigNode::mutable_children() {
  // @@protoc_insertion_point(field_mutable_list:carbon.config.proto.ConfigNode.children)
  return &children_;
}
inline const ::carbon::config::proto::ConfigNode& ConfigNode::_internal_children(int index) const {
  return children_.Get(index);
}
inline const ::carbon::config::proto::ConfigNode& ConfigNode::children(int index) const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigNode.children)
  return _internal_children(index);
}
inline ::carbon::config::proto::ConfigNode* ConfigNode::_internal_add_children() {
  return children_.Add();
}
inline ::carbon::config::proto::ConfigNode* ConfigNode::add_children() {
  ::carbon::config::proto::ConfigNode* _add = _internal_add_children();
  // @@protoc_insertion_point(field_add:carbon.config.proto.ConfigNode.children)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigNode >&
ConfigNode::children() const {
  // @@protoc_insertion_point(field_list:carbon.config.proto.ConfigNode.children)
  return children_;
}

// -------------------------------------------------------------------

// ConfigLeaf

// string key = 1;
inline void ConfigLeaf::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& ConfigLeaf::key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigLeaf.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ConfigLeaf::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.ConfigLeaf.key)
}
inline std::string* ConfigLeaf::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigLeaf.key)
  return _s;
}
inline const std::string& ConfigLeaf::_internal_key() const {
  return key_.Get();
}
inline void ConfigLeaf::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ConfigLeaf::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ConfigLeaf::release_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigLeaf.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ConfigLeaf::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigLeaf.key)
}

// .carbon.config.proto.ConfigValue value = 2;
inline bool ConfigLeaf::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool ConfigLeaf::has_value() const {
  return _internal_has_value();
}
inline void ConfigLeaf::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
inline const ::carbon::config::proto::ConfigValue& ConfigLeaf::_internal_value() const {
  const ::carbon::config::proto::ConfigValue* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::config::proto::ConfigValue&>(
      ::carbon::config::proto::_ConfigValue_default_instance_);
}
inline const ::carbon::config::proto::ConfigValue& ConfigLeaf::value() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.ConfigLeaf.value)
  return _internal_value();
}
inline void ConfigLeaf::unsafe_arena_set_allocated_value(
    ::carbon::config::proto::ConfigValue* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.ConfigLeaf.value)
}
inline ::carbon::config::proto::ConfigValue* ConfigLeaf::release_value() {
  
  ::carbon::config::proto::ConfigValue* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::config::proto::ConfigValue* ConfigLeaf::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.ConfigLeaf.value)
  
  ::carbon::config::proto::ConfigValue* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::carbon::config::proto::ConfigValue* ConfigLeaf::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::config::proto::ConfigValue>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::carbon::config::proto::ConfigValue* ConfigLeaf::mutable_value() {
  ::carbon::config::proto::ConfigValue* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.ConfigLeaf.value)
  return _msg;
}
inline void ConfigLeaf::set_allocated_value(::carbon::config::proto::ConfigValue* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete value_;
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::ConfigValue>::GetOwningArena(value);
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigLeaf.value)
}

// -------------------------------------------------------------------

// SetValueRequest

// string key = 1;
inline void SetValueRequest::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& SetValueRequest::key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.SetValueRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetValueRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.SetValueRequest.key)
}
inline std::string* SetValueRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.SetValueRequest.key)
  return _s;
}
inline const std::string& SetValueRequest::_internal_key() const {
  return key_.Get();
}
inline void SetValueRequest::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetValueRequest::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetValueRequest::release_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.SetValueRequest.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetValueRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.SetValueRequest.key)
}

// .carbon.config.proto.ConfigValue value = 2;
inline bool SetValueRequest::_internal_has_value() const {
  return this != internal_default_instance() && value_ != nullptr;
}
inline bool SetValueRequest::has_value() const {
  return _internal_has_value();
}
inline void SetValueRequest::clear_value() {
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
}
inline const ::carbon::config::proto::ConfigValue& SetValueRequest::_internal_value() const {
  const ::carbon::config::proto::ConfigValue* p = value_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::config::proto::ConfigValue&>(
      ::carbon::config::proto::_ConfigValue_default_instance_);
}
inline const ::carbon::config::proto::ConfigValue& SetValueRequest::value() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.SetValueRequest.value)
  return _internal_value();
}
inline void SetValueRequest::unsafe_arena_set_allocated_value(
    ::carbon::config::proto::ConfigValue* value) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(value_);
  }
  value_ = value;
  if (value) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.SetValueRequest.value)
}
inline ::carbon::config::proto::ConfigValue* SetValueRequest::release_value() {
  
  ::carbon::config::proto::ConfigValue* temp = value_;
  value_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::config::proto::ConfigValue* SetValueRequest::unsafe_arena_release_value() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.SetValueRequest.value)
  
  ::carbon::config::proto::ConfigValue* temp = value_;
  value_ = nullptr;
  return temp;
}
inline ::carbon::config::proto::ConfigValue* SetValueRequest::_internal_mutable_value() {
  
  if (value_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::config::proto::ConfigValue>(GetArenaForAllocation());
    value_ = p;
  }
  return value_;
}
inline ::carbon::config::proto::ConfigValue* SetValueRequest::mutable_value() {
  ::carbon::config::proto::ConfigValue* _msg = _internal_mutable_value();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.SetValueRequest.value)
  return _msg;
}
inline void SetValueRequest::set_allocated_value(::carbon::config::proto::ConfigValue* value) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete value_;
  }
  if (value) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::ConfigValue>::GetOwningArena(value);
    if (message_arena != submessage_arena) {
      value = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, value, submessage_arena);
    }
    
  } else {
    
  }
  value_ = value;
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.SetValueRequest.value)
}

// -------------------------------------------------------------------

// SetValueResponse

// -------------------------------------------------------------------

// GetTreeRequest

// string key = 1;
inline void GetTreeRequest::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& GetTreeRequest::key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.GetTreeRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetTreeRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.GetTreeRequest.key)
}
inline std::string* GetTreeRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.GetTreeRequest.key)
  return _s;
}
inline const std::string& GetTreeRequest::_internal_key() const {
  return key_.Get();
}
inline void GetTreeRequest::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetTreeRequest::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetTreeRequest::release_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.GetTreeRequest.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetTreeRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.GetTreeRequest.key)
}

// -------------------------------------------------------------------

// GetTreeResponse

// .carbon.config.proto.ConfigNode node = 1;
inline bool GetTreeResponse::_internal_has_node() const {
  return this != internal_default_instance() && node_ != nullptr;
}
inline bool GetTreeResponse::has_node() const {
  return _internal_has_node();
}
inline void GetTreeResponse::clear_node() {
  if (GetArenaForAllocation() == nullptr && node_ != nullptr) {
    delete node_;
  }
  node_ = nullptr;
}
inline const ::carbon::config::proto::ConfigNode& GetTreeResponse::_internal_node() const {
  const ::carbon::config::proto::ConfigNode* p = node_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::config::proto::ConfigNode&>(
      ::carbon::config::proto::_ConfigNode_default_instance_);
}
inline const ::carbon::config::proto::ConfigNode& GetTreeResponse::node() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.GetTreeResponse.node)
  return _internal_node();
}
inline void GetTreeResponse::unsafe_arena_set_allocated_node(
    ::carbon::config::proto::ConfigNode* node) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(node_);
  }
  node_ = node;
  if (node) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.GetTreeResponse.node)
}
inline ::carbon::config::proto::ConfigNode* GetTreeResponse::release_node() {
  
  ::carbon::config::proto::ConfigNode* temp = node_;
  node_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::config::proto::ConfigNode* GetTreeResponse::unsafe_arena_release_node() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.GetTreeResponse.node)
  
  ::carbon::config::proto::ConfigNode* temp = node_;
  node_ = nullptr;
  return temp;
}
inline ::carbon::config::proto::ConfigNode* GetTreeResponse::_internal_mutable_node() {
  
  if (node_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::config::proto::ConfigNode>(GetArenaForAllocation());
    node_ = p;
  }
  return node_;
}
inline ::carbon::config::proto::ConfigNode* GetTreeResponse::mutable_node() {
  ::carbon::config::proto::ConfigNode* _msg = _internal_mutable_node();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.GetTreeResponse.node)
  return _msg;
}
inline void GetTreeResponse::set_allocated_node(::carbon::config::proto::ConfigNode* node) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete node_;
  }
  if (node) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::ConfigNode>::GetOwningArena(node);
    if (message_arena != submessage_arena) {
      node = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, node, submessage_arena);
    }
    
  } else {
    
  }
  node_ = node;
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.GetTreeResponse.node)
}

// -------------------------------------------------------------------

// SetTreeRequest

// string key = 1;
inline void SetTreeRequest::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& SetTreeRequest::key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.SetTreeRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SetTreeRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.SetTreeRequest.key)
}
inline std::string* SetTreeRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.SetTreeRequest.key)
  return _s;
}
inline const std::string& SetTreeRequest::_internal_key() const {
  return key_.Get();
}
inline void SetTreeRequest::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SetTreeRequest::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SetTreeRequest::release_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.SetTreeRequest.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SetTreeRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.SetTreeRequest.key)
}

// .carbon.config.proto.ConfigNode node = 2;
inline bool SetTreeRequest::_internal_has_node() const {
  return this != internal_default_instance() && node_ != nullptr;
}
inline bool SetTreeRequest::has_node() const {
  return _internal_has_node();
}
inline void SetTreeRequest::clear_node() {
  if (GetArenaForAllocation() == nullptr && node_ != nullptr) {
    delete node_;
  }
  node_ = nullptr;
}
inline const ::carbon::config::proto::ConfigNode& SetTreeRequest::_internal_node() const {
  const ::carbon::config::proto::ConfigNode* p = node_;
  return p != nullptr ? *p : reinterpret_cast<const ::carbon::config::proto::ConfigNode&>(
      ::carbon::config::proto::_ConfigNode_default_instance_);
}
inline const ::carbon::config::proto::ConfigNode& SetTreeRequest::node() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.SetTreeRequest.node)
  return _internal_node();
}
inline void SetTreeRequest::unsafe_arena_set_allocated_node(
    ::carbon::config::proto::ConfigNode* node) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(node_);
  }
  node_ = node;
  if (node) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.config.proto.SetTreeRequest.node)
}
inline ::carbon::config::proto::ConfigNode* SetTreeRequest::release_node() {
  
  ::carbon::config::proto::ConfigNode* temp = node_;
  node_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::carbon::config::proto::ConfigNode* SetTreeRequest::unsafe_arena_release_node() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.SetTreeRequest.node)
  
  ::carbon::config::proto::ConfigNode* temp = node_;
  node_ = nullptr;
  return temp;
}
inline ::carbon::config::proto::ConfigNode* SetTreeRequest::_internal_mutable_node() {
  
  if (node_ == nullptr) {
    auto* p = CreateMaybeMessage<::carbon::config::proto::ConfigNode>(GetArenaForAllocation());
    node_ = p;
  }
  return node_;
}
inline ::carbon::config::proto::ConfigNode* SetTreeRequest::mutable_node() {
  ::carbon::config::proto::ConfigNode* _msg = _internal_mutable_node();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.SetTreeRequest.node)
  return _msg;
}
inline void SetTreeRequest::set_allocated_node(::carbon::config::proto::ConfigNode* node) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete node_;
  }
  if (node) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::ConfigNode>::GetOwningArena(node);
    if (message_arena != submessage_arena) {
      node = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, node, submessage_arena);
    }
    
  } else {
    
  }
  node_ = node;
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.SetTreeRequest.node)
}

// -------------------------------------------------------------------

// SetTreeResponse

// -------------------------------------------------------------------

// GetLeavesRequest

// string key = 1;
inline void GetLeavesRequest::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& GetLeavesRequest::key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.GetLeavesRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetLeavesRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.GetLeavesRequest.key)
}
inline std::string* GetLeavesRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.GetLeavesRequest.key)
  return _s;
}
inline const std::string& GetLeavesRequest::_internal_key() const {
  return key_.Get();
}
inline void GetLeavesRequest::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetLeavesRequest::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetLeavesRequest::release_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.GetLeavesRequest.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetLeavesRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.GetLeavesRequest.key)
}

// -------------------------------------------------------------------

// GetLeavesResponse

// repeated .carbon.config.proto.ConfigLeaf leaves = 1;
inline int GetLeavesResponse::_internal_leaves_size() const {
  return leaves_.size();
}
inline int GetLeavesResponse::leaves_size() const {
  return _internal_leaves_size();
}
inline void GetLeavesResponse::clear_leaves() {
  leaves_.Clear();
}
inline ::carbon::config::proto::ConfigLeaf* GetLeavesResponse::mutable_leaves(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.GetLeavesResponse.leaves)
  return leaves_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigLeaf >*
GetLeavesResponse::mutable_leaves() {
  // @@protoc_insertion_point(field_mutable_list:carbon.config.proto.GetLeavesResponse.leaves)
  return &leaves_;
}
inline const ::carbon::config::proto::ConfigLeaf& GetLeavesResponse::_internal_leaves(int index) const {
  return leaves_.Get(index);
}
inline const ::carbon::config::proto::ConfigLeaf& GetLeavesResponse::leaves(int index) const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.GetLeavesResponse.leaves)
  return _internal_leaves(index);
}
inline ::carbon::config::proto::ConfigLeaf* GetLeavesResponse::_internal_add_leaves() {
  return leaves_.Add();
}
inline ::carbon::config::proto::ConfigLeaf* GetLeavesResponse::add_leaves() {
  ::carbon::config::proto::ConfigLeaf* _add = _internal_add_leaves();
  // @@protoc_insertion_point(field_add:carbon.config.proto.GetLeavesResponse.leaves)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::carbon::config::proto::ConfigLeaf >&
GetLeavesResponse::leaves() const {
  // @@protoc_insertion_point(field_list:carbon.config.proto.GetLeavesResponse.leaves)
  return leaves_;
}

// -------------------------------------------------------------------

// AddToListRequest

// string key = 1;
inline void AddToListRequest::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& AddToListRequest::key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.AddToListRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AddToListRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.AddToListRequest.key)
}
inline std::string* AddToListRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.AddToListRequest.key)
  return _s;
}
inline const std::string& AddToListRequest::_internal_key() const {
  return key_.Get();
}
inline void AddToListRequest::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AddToListRequest::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AddToListRequest::release_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.AddToListRequest.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AddToListRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.AddToListRequest.key)
}

// string name = 2;
inline void AddToListRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& AddToListRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.AddToListRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void AddToListRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.AddToListRequest.name)
}
inline std::string* AddToListRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.AddToListRequest.name)
  return _s;
}
inline const std::string& AddToListRequest::_internal_name() const {
  return name_.Get();
}
inline void AddToListRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* AddToListRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* AddToListRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.AddToListRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void AddToListRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.AddToListRequest.name)
}

// -------------------------------------------------------------------

// AddToListResponse

// -------------------------------------------------------------------

// RemoveFromListRequest

// string key = 1;
inline void RemoveFromListRequest::clear_key() {
  key_.ClearToEmpty();
}
inline const std::string& RemoveFromListRequest::key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.RemoveFromListRequest.key)
  return _internal_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RemoveFromListRequest::set_key(ArgT0&& arg0, ArgT... args) {
 
 key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.RemoveFromListRequest.key)
}
inline std::string* RemoveFromListRequest::mutable_key() {
  std::string* _s = _internal_mutable_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.RemoveFromListRequest.key)
  return _s;
}
inline const std::string& RemoveFromListRequest::_internal_key() const {
  return key_.Get();
}
inline void RemoveFromListRequest::_internal_set_key(const std::string& value) {
  
  key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RemoveFromListRequest::_internal_mutable_key() {
  
  return key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RemoveFromListRequest::release_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.RemoveFromListRequest.key)
  return key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RemoveFromListRequest::set_allocated_key(std::string* key) {
  if (key != nullptr) {
    
  } else {
    
  }
  key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.RemoveFromListRequest.key)
}

// string name = 2;
inline void RemoveFromListRequest::clear_name() {
  name_.ClearToEmpty();
}
inline const std::string& RemoveFromListRequest::name() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.RemoveFromListRequest.name)
  return _internal_name();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void RemoveFromListRequest::set_name(ArgT0&& arg0, ArgT... args) {
 
 name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.RemoveFromListRequest.name)
}
inline std::string* RemoveFromListRequest::mutable_name() {
  std::string* _s = _internal_mutable_name();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.RemoveFromListRequest.name)
  return _s;
}
inline const std::string& RemoveFromListRequest::_internal_name() const {
  return name_.Get();
}
inline void RemoveFromListRequest::_internal_set_name(const std::string& value) {
  
  name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* RemoveFromListRequest::_internal_mutable_name() {
  
  return name_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* RemoveFromListRequest::release_name() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.RemoveFromListRequest.name)
  return name_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void RemoveFromListRequest::set_allocated_name(std::string* name) {
  if (name != nullptr) {
    
  } else {
    
  }
  name_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), name,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (name_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.RemoveFromListRequest.name)
}

// -------------------------------------------------------------------

// RemoveFromListResponse

// -------------------------------------------------------------------

// SubscriptionRequest

// repeated string keys = 1;
inline int SubscriptionRequest::_internal_keys_size() const {
  return keys_.size();
}
inline int SubscriptionRequest::keys_size() const {
  return _internal_keys_size();
}
inline void SubscriptionRequest::clear_keys() {
  keys_.Clear();
}
inline std::string* SubscriptionRequest::add_keys() {
  std::string* _s = _internal_add_keys();
  // @@protoc_insertion_point(field_add_mutable:carbon.config.proto.SubscriptionRequest.keys)
  return _s;
}
inline const std::string& SubscriptionRequest::_internal_keys(int index) const {
  return keys_.Get(index);
}
inline const std::string& SubscriptionRequest::keys(int index) const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.SubscriptionRequest.keys)
  return _internal_keys(index);
}
inline std::string* SubscriptionRequest::mutable_keys(int index) {
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.SubscriptionRequest.keys)
  return keys_.Mutable(index);
}
inline void SubscriptionRequest::set_keys(int index, const std::string& value) {
  keys_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:carbon.config.proto.SubscriptionRequest.keys)
}
inline void SubscriptionRequest::set_keys(int index, std::string&& value) {
  keys_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:carbon.config.proto.SubscriptionRequest.keys)
}
inline void SubscriptionRequest::set_keys(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  keys_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:carbon.config.proto.SubscriptionRequest.keys)
}
inline void SubscriptionRequest::set_keys(int index, const char* value, size_t size) {
  keys_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:carbon.config.proto.SubscriptionRequest.keys)
}
inline std::string* SubscriptionRequest::_internal_add_keys() {
  return keys_.Add();
}
inline void SubscriptionRequest::add_keys(const std::string& value) {
  keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:carbon.config.proto.SubscriptionRequest.keys)
}
inline void SubscriptionRequest::add_keys(std::string&& value) {
  keys_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:carbon.config.proto.SubscriptionRequest.keys)
}
inline void SubscriptionRequest::add_keys(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  keys_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:carbon.config.proto.SubscriptionRequest.keys)
}
inline void SubscriptionRequest::add_keys(const char* value, size_t size) {
  keys_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:carbon.config.proto.SubscriptionRequest.keys)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
SubscriptionRequest::keys() const {
  // @@protoc_insertion_point(field_list:carbon.config.proto.SubscriptionRequest.keys)
  return keys_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
SubscriptionRequest::mutable_keys() {
  // @@protoc_insertion_point(field_mutable_list:carbon.config.proto.SubscriptionRequest.keys)
  return &keys_;
}

// -------------------------------------------------------------------

// SubscriptionNotifyMessage

// string subscription_key = 1;
inline void SubscriptionNotifyMessage::clear_subscription_key() {
  subscription_key_.ClearToEmpty();
}
inline const std::string& SubscriptionNotifyMessage::subscription_key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.SubscriptionNotifyMessage.subscription_key)
  return _internal_subscription_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SubscriptionNotifyMessage::set_subscription_key(ArgT0&& arg0, ArgT... args) {
 
 subscription_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.SubscriptionNotifyMessage.subscription_key)
}
inline std::string* SubscriptionNotifyMessage::mutable_subscription_key() {
  std::string* _s = _internal_mutable_subscription_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.SubscriptionNotifyMessage.subscription_key)
  return _s;
}
inline const std::string& SubscriptionNotifyMessage::_internal_subscription_key() const {
  return subscription_key_.Get();
}
inline void SubscriptionNotifyMessage::_internal_set_subscription_key(const std::string& value) {
  
  subscription_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SubscriptionNotifyMessage::_internal_mutable_subscription_key() {
  
  return subscription_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SubscriptionNotifyMessage::release_subscription_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.SubscriptionNotifyMessage.subscription_key)
  return subscription_key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SubscriptionNotifyMessage::set_allocated_subscription_key(std::string* subscription_key) {
  if (subscription_key != nullptr) {
    
  } else {
    
  }
  subscription_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), subscription_key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (subscription_key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    subscription_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.SubscriptionNotifyMessage.subscription_key)
}

// string notify_key = 2;
inline void SubscriptionNotifyMessage::clear_notify_key() {
  notify_key_.ClearToEmpty();
}
inline const std::string& SubscriptionNotifyMessage::notify_key() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.SubscriptionNotifyMessage.notify_key)
  return _internal_notify_key();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void SubscriptionNotifyMessage::set_notify_key(ArgT0&& arg0, ArgT... args) {
 
 notify_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.SubscriptionNotifyMessage.notify_key)
}
inline std::string* SubscriptionNotifyMessage::mutable_notify_key() {
  std::string* _s = _internal_mutable_notify_key();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.SubscriptionNotifyMessage.notify_key)
  return _s;
}
inline const std::string& SubscriptionNotifyMessage::_internal_notify_key() const {
  return notify_key_.Get();
}
inline void SubscriptionNotifyMessage::_internal_set_notify_key(const std::string& value) {
  
  notify_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* SubscriptionNotifyMessage::_internal_mutable_notify_key() {
  
  return notify_key_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* SubscriptionNotifyMessage::release_notify_key() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.SubscriptionNotifyMessage.notify_key)
  return notify_key_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void SubscriptionNotifyMessage::set_allocated_notify_key(std::string* notify_key) {
  if (notify_key != nullptr) {
    
  } else {
    
  }
  notify_key_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), notify_key,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (notify_key_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    notify_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.SubscriptionNotifyMessage.notify_key)
}

// -------------------------------------------------------------------

// UpgradeCloudConfigRequest

// string robot = 1;
inline void UpgradeCloudConfigRequest::clear_robot() {
  robot_.ClearToEmpty();
}
inline const std::string& UpgradeCloudConfigRequest::robot() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.UpgradeCloudConfigRequest.robot)
  return _internal_robot();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UpgradeCloudConfigRequest::set_robot(ArgT0&& arg0, ArgT... args) {
 
 robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.UpgradeCloudConfigRequest.robot)
}
inline std::string* UpgradeCloudConfigRequest::mutable_robot() {
  std::string* _s = _internal_mutable_robot();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.UpgradeCloudConfigRequest.robot)
  return _s;
}
inline const std::string& UpgradeCloudConfigRequest::_internal_robot() const {
  return robot_.Get();
}
inline void UpgradeCloudConfigRequest::_internal_set_robot(const std::string& value) {
  
  robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UpgradeCloudConfigRequest::_internal_mutable_robot() {
  
  return robot_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UpgradeCloudConfigRequest::release_robot() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.UpgradeCloudConfigRequest.robot)
  return robot_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UpgradeCloudConfigRequest::set_allocated_robot(std::string* robot) {
  if (robot != nullptr) {
    
  } else {
    
  }
  robot_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.UpgradeCloudConfigRequest.robot)
}

// string to_template = 2;
inline void UpgradeCloudConfigRequest::clear_to_template() {
  to_template_.ClearToEmpty();
}
inline const std::string& UpgradeCloudConfigRequest::to_template() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.UpgradeCloudConfigRequest.to_template)
  return _internal_to_template();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UpgradeCloudConfigRequest::set_to_template(ArgT0&& arg0, ArgT... args) {
 
 to_template_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.UpgradeCloudConfigRequest.to_template)
}
inline std::string* UpgradeCloudConfigRequest::mutable_to_template() {
  std::string* _s = _internal_mutable_to_template();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.UpgradeCloudConfigRequest.to_template)
  return _s;
}
inline const std::string& UpgradeCloudConfigRequest::_internal_to_template() const {
  return to_template_.Get();
}
inline void UpgradeCloudConfigRequest::_internal_set_to_template(const std::string& value) {
  
  to_template_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UpgradeCloudConfigRequest::_internal_mutable_to_template() {
  
  return to_template_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UpgradeCloudConfigRequest::release_to_template() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.UpgradeCloudConfigRequest.to_template)
  return to_template_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UpgradeCloudConfigRequest::set_allocated_to_template(std::string* to_template) {
  if (to_template != nullptr) {
    
  } else {
    
  }
  to_template_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), to_template,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (to_template_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    to_template_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.UpgradeCloudConfigRequest.to_template)
}

// string from_template = 3;
inline void UpgradeCloudConfigRequest::clear_from_template() {
  from_template_.ClearToEmpty();
}
inline const std::string& UpgradeCloudConfigRequest::from_template() const {
  // @@protoc_insertion_point(field_get:carbon.config.proto.UpgradeCloudConfigRequest.from_template)
  return _internal_from_template();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UpgradeCloudConfigRequest::set_from_template(ArgT0&& arg0, ArgT... args) {
 
 from_template_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.config.proto.UpgradeCloudConfigRequest.from_template)
}
inline std::string* UpgradeCloudConfigRequest::mutable_from_template() {
  std::string* _s = _internal_mutable_from_template();
  // @@protoc_insertion_point(field_mutable:carbon.config.proto.UpgradeCloudConfigRequest.from_template)
  return _s;
}
inline const std::string& UpgradeCloudConfigRequest::_internal_from_template() const {
  return from_template_.Get();
}
inline void UpgradeCloudConfigRequest::_internal_set_from_template(const std::string& value) {
  
  from_template_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UpgradeCloudConfigRequest::_internal_mutable_from_template() {
  
  return from_template_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UpgradeCloudConfigRequest::release_from_template() {
  // @@protoc_insertion_point(field_release:carbon.config.proto.UpgradeCloudConfigRequest.from_template)
  return from_template_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UpgradeCloudConfigRequest::set_allocated_from_template(std::string* from_template) {
  if (from_template != nullptr) {
    
  } else {
    
  }
  from_template_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), from_template,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (from_template_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    from_template_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.UpgradeCloudConfigRequest.from_template)
}

// -------------------------------------------------------------------

// UpgradeCloudConfigResponse

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace proto
}  // namespace config
}  // namespace carbon

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::carbon::config::proto::ConfigType> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::config::proto::ConfigType>() {
  return ::carbon::config::proto::ConfigType_descriptor();
}
template <> struct is_proto_enum< ::carbon::config::proto::ConfigComplexity> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::carbon::config::proto::ConfigComplexity>() {
  return ::carbon::config::proto::ConfigComplexity_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_config_2fapi_2fproto_2fconfig_5fservice_2eproto

// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: config/api/proto/config_service.proto
#ifndef GRPC_config_2fapi_2fproto_2fconfig_5fservice_2eproto__INCLUDED
#define GRPC_config_2fapi_2fproto_2fconfig_5fservice_2eproto__INCLUDED

#include "config/api/proto/config_service.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_generic_service.h>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/client_context.h>
#include <grpcpp/impl/codegen/completion_queue.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/proto_utils.h>
#include <grpcpp/impl/codegen/rpc_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/status.h>
#include <grpcpp/impl/codegen/stub_options.h>
#include <grpcpp/impl/codegen/sync_stream.h>

namespace carbon {
namespace config {
namespace proto {

class ConfigService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.config.proto.ConfigService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    virtual ::grpc::Status Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::carbon::config::proto::PongResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::PongResponse>> AsyncPing(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::PongResponse>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::PongResponse>> PrepareAsyncPing(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::PongResponse>>(PrepareAsyncPingRaw(context, request, cq));
    }
    virtual ::grpc::Status SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::carbon::config::proto::SetValueResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetValueResponse>> AsyncSetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetValueResponse>>(AsyncSetValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetValueResponse>> PrepareAsyncSetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetValueResponse>>(PrepareAsyncSetValueRaw(context, request, cq));
    }
    virtual ::grpc::Status GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::carbon::config::proto::GetTreeResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetTreeResponse>> AsyncGetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetTreeResponse>>(AsyncGetTreeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetTreeResponse>> PrepareAsyncGetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetTreeResponse>>(PrepareAsyncGetTreeRaw(context, request, cq));
    }
    virtual ::grpc::Status SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::carbon::config::proto::SetTreeResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetTreeResponse>> AsyncSetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetTreeResponse>>(AsyncSetTreeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetTreeResponse>> PrepareAsyncSetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetTreeResponse>>(PrepareAsyncSetTreeRaw(context, request, cq));
    }
    virtual ::grpc::Status GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::carbon::config::proto::GetLeavesResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetLeavesResponse>> AsyncGetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetLeavesResponse>>(AsyncGetLeavesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetLeavesResponse>> PrepareAsyncGetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetLeavesResponse>>(PrepareAsyncGetLeavesRaw(context, request, cq));
    }
    virtual ::grpc::Status AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::carbon::config::proto::AddToListResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::AddToListResponse>> AsyncAddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::AddToListResponse>>(AsyncAddToListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::AddToListResponse>> PrepareAsyncAddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::AddToListResponse>>(PrepareAsyncAddToListRaw(context, request, cq));
    }
    virtual ::grpc::Status RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::carbon::config::proto::RemoveFromListResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::RemoveFromListResponse>> AsyncRemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::RemoveFromListResponse>>(AsyncRemoveFromListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::RemoveFromListResponse>> PrepareAsyncRemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::RemoveFromListResponse>>(PrepareAsyncRemoveFromListRaw(context, request, cq));
    }
    virtual ::grpc::Status UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::carbon::config::proto::UpgradeCloudConfigResponse* response) = 0;
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::UpgradeCloudConfigResponse>> AsyncUpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::UpgradeCloudConfigResponse>>(AsyncUpgradeCloudConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::UpgradeCloudConfigResponse>> PrepareAsyncUpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::UpgradeCloudConfigResponse>>(PrepareAsyncUpgradeCloudConfigRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
      virtual void UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response, std::function<void(::grpc::Status)>) = 0;
      virtual void UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::PongResponse>* AsyncPingRaw(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::PongResponse>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetValueResponse>* AsyncSetValueRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetValueResponse>* PrepareAsyncSetValueRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetTreeResponse>* AsyncGetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetTreeResponse>* PrepareAsyncGetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetTreeResponse>* AsyncSetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::SetTreeResponse>* PrepareAsyncSetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetLeavesResponse>* AsyncGetLeavesRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::GetLeavesResponse>* PrepareAsyncGetLeavesRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::AddToListResponse>* AsyncAddToListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::AddToListResponse>* PrepareAsyncAddToListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::RemoveFromListResponse>* AsyncRemoveFromListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::RemoveFromListResponse>* PrepareAsyncRemoveFromListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::UpgradeCloudConfigResponse>* AsyncUpgradeCloudConfigRaw(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
    virtual ::grpc::ClientAsyncResponseReaderInterface< ::carbon::config::proto::UpgradeCloudConfigResponse>* PrepareAsyncUpgradeCloudConfigRaw(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    ::grpc::Status Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::carbon::config::proto::PongResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::PongResponse>> AsyncPing(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::PongResponse>>(AsyncPingRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::PongResponse>> PrepareAsyncPing(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::PongResponse>>(PrepareAsyncPingRaw(context, request, cq));
    }
    ::grpc::Status SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::carbon::config::proto::SetValueResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetValueResponse>> AsyncSetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetValueResponse>>(AsyncSetValueRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetValueResponse>> PrepareAsyncSetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetValueResponse>>(PrepareAsyncSetValueRaw(context, request, cq));
    }
    ::grpc::Status GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::carbon::config::proto::GetTreeResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetTreeResponse>> AsyncGetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetTreeResponse>>(AsyncGetTreeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetTreeResponse>> PrepareAsyncGetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetTreeResponse>>(PrepareAsyncGetTreeRaw(context, request, cq));
    }
    ::grpc::Status SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::carbon::config::proto::SetTreeResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetTreeResponse>> AsyncSetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetTreeResponse>>(AsyncSetTreeRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetTreeResponse>> PrepareAsyncSetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetTreeResponse>>(PrepareAsyncSetTreeRaw(context, request, cq));
    }
    ::grpc::Status GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::carbon::config::proto::GetLeavesResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetLeavesResponse>> AsyncGetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetLeavesResponse>>(AsyncGetLeavesRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetLeavesResponse>> PrepareAsyncGetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetLeavesResponse>>(PrepareAsyncGetLeavesRaw(context, request, cq));
    }
    ::grpc::Status AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::carbon::config::proto::AddToListResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::AddToListResponse>> AsyncAddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::AddToListResponse>>(AsyncAddToListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::AddToListResponse>> PrepareAsyncAddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::AddToListResponse>>(PrepareAsyncAddToListRaw(context, request, cq));
    }
    ::grpc::Status RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::carbon::config::proto::RemoveFromListResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::RemoveFromListResponse>> AsyncRemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::RemoveFromListResponse>>(AsyncRemoveFromListRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::RemoveFromListResponse>> PrepareAsyncRemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::RemoveFromListResponse>>(PrepareAsyncRemoveFromListRaw(context, request, cq));
    }
    ::grpc::Status UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::carbon::config::proto::UpgradeCloudConfigResponse* response) override;
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::UpgradeCloudConfigResponse>> AsyncUpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::UpgradeCloudConfigResponse>>(AsyncUpgradeCloudConfigRaw(context, request, cq));
    }
    std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::UpgradeCloudConfigResponse>> PrepareAsyncUpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::UpgradeCloudConfigResponse>>(PrepareAsyncUpgradeCloudConfigRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response, std::function<void(::grpc::Status)>) override;
      void Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response, std::function<void(::grpc::Status)>) override;
      void SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response, std::function<void(::grpc::Status)>) override;
      void GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response, std::function<void(::grpc::Status)>) override;
      void SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response, std::function<void(::grpc::Status)>) override;
      void GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response, std::function<void(::grpc::Status)>) override;
      void AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response, std::function<void(::grpc::Status)>) override;
      void RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
      void UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response, std::function<void(::grpc::Status)>) override;
      void UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::PongResponse>* AsyncPingRaw(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::PongResponse>* PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetValueResponse>* AsyncSetValueRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetValueResponse>* PrepareAsyncSetValueRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetTreeResponse>* AsyncGetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetTreeResponse>* PrepareAsyncGetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetTreeResponse>* AsyncSetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetTreeResponse>* PrepareAsyncSetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetLeavesResponse>* AsyncGetLeavesRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetLeavesResponse>* PrepareAsyncGetLeavesRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::AddToListResponse>* AsyncAddToListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::AddToListResponse>* PrepareAsyncAddToListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::RemoveFromListResponse>* AsyncRemoveFromListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::RemoveFromListResponse>* PrepareAsyncRemoveFromListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::UpgradeCloudConfigResponse>* AsyncUpgradeCloudConfigRaw(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    ::grpc::ClientAsyncResponseReader< ::carbon::config::proto::UpgradeCloudConfigResponse>* PrepareAsyncUpgradeCloudConfigRaw(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Ping_;
    const ::grpc::internal::RpcMethod rpcmethod_SetValue_;
    const ::grpc::internal::RpcMethod rpcmethod_GetTree_;
    const ::grpc::internal::RpcMethod rpcmethod_SetTree_;
    const ::grpc::internal::RpcMethod rpcmethod_GetLeaves_;
    const ::grpc::internal::RpcMethod rpcmethod_AddToList_;
    const ::grpc::internal::RpcMethod rpcmethod_RemoveFromList_;
    const ::grpc::internal::RpcMethod rpcmethod_UpgradeCloudConfig_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Ping(::grpc::ServerContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response);
    virtual ::grpc::Status SetValue(::grpc::ServerContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response);
    virtual ::grpc::Status GetTree(::grpc::ServerContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response);
    virtual ::grpc::Status SetTree(::grpc::ServerContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response);
    virtual ::grpc::Status GetLeaves(::grpc::ServerContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response);
    virtual ::grpc::Status AddToList(::grpc::ServerContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response);
    virtual ::grpc::Status RemoveFromList(::grpc::ServerContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response);
    virtual ::grpc::Status UpgradeCloudConfig(::grpc::ServerContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response);
  };
  template <class BaseClass>
  class WithAsyncMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Ping() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::PingRequest* /*request*/, ::carbon::config::proto::PongResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::carbon::config::proto::PingRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::config::proto::PongResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetValue() {
      ::grpc::Service::MarkMethodAsync(1);
    }
    ~WithAsyncMethod_SetValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetValue(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetValueRequest* /*request*/, ::carbon::config::proto::SetValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetValue(::grpc::ServerContext* context, ::carbon::config::proto::SetValueRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::config::proto::SetValueResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetTree() {
      ::grpc::Service::MarkMethodAsync(2);
    }
    ~WithAsyncMethod_GetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetTreeRequest* /*request*/, ::carbon::config::proto::GetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTree(::grpc::ServerContext* context, ::carbon::config::proto::GetTreeRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::config::proto::GetTreeResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_SetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_SetTree() {
      ::grpc::Service::MarkMethodAsync(3);
    }
    ~WithAsyncMethod_SetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetTreeRequest* /*request*/, ::carbon::config::proto::SetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetTree(::grpc::ServerContext* context, ::carbon::config::proto::SetTreeRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::config::proto::SetTreeResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_GetLeaves : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_GetLeaves() {
      ::grpc::Service::MarkMethodAsync(4);
    }
    ~WithAsyncMethod_GetLeaves() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLeaves(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetLeavesRequest* /*request*/, ::carbon::config::proto::GetLeavesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetLeaves(::grpc::ServerContext* context, ::carbon::config::proto::GetLeavesRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::config::proto::GetLeavesResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_AddToList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_AddToList() {
      ::grpc::Service::MarkMethodAsync(5);
    }
    ~WithAsyncMethod_AddToList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddToList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::AddToListRequest* /*request*/, ::carbon::config::proto::AddToListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAddToList(::grpc::ServerContext* context, ::carbon::config::proto::AddToListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::config::proto::AddToListResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_RemoveFromList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_RemoveFromList() {
      ::grpc::Service::MarkMethodAsync(6);
    }
    ~WithAsyncMethod_RemoveFromList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveFromList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::RemoveFromListRequest* /*request*/, ::carbon::config::proto::RemoveFromListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRemoveFromList(::grpc::ServerContext* context, ::carbon::config::proto::RemoveFromListRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::config::proto::RemoveFromListResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithAsyncMethod_UpgradeCloudConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_UpgradeCloudConfig() {
      ::grpc::Service::MarkMethodAsync(7);
    }
    ~WithAsyncMethod_UpgradeCloudConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpgradeCloudConfig(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::UpgradeCloudConfigRequest* /*request*/, ::carbon::config::proto::UpgradeCloudConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpgradeCloudConfig(::grpc::ServerContext* context, ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::grpc::ServerAsyncResponseWriter< ::carbon::config::proto::UpgradeCloudConfigResponse>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Ping<WithAsyncMethod_SetValue<WithAsyncMethod_GetTree<WithAsyncMethod_SetTree<WithAsyncMethod_GetLeaves<WithAsyncMethod_AddToList<WithAsyncMethod_RemoveFromList<WithAsyncMethod_UpgradeCloudConfig<Service > > > > > > > > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::PingRequest, ::carbon::config::proto::PongResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response) { return this->Ping(context, request, response); }));}
    void SetMessageAllocatorFor_Ping(
        ::grpc::MessageAllocator< ::carbon::config::proto::PingRequest, ::carbon::config::proto::PongResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(0);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::PingRequest, ::carbon::config::proto::PongResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::PingRequest* /*request*/, ::carbon::config::proto::PongResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::PingRequest* /*request*/, ::carbon::config::proto::PongResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetValue() {
      ::grpc::Service::MarkMethodCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::SetValueRequest, ::carbon::config::proto::SetValueResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response) { return this->SetValue(context, request, response); }));}
    void SetMessageAllocatorFor_SetValue(
        ::grpc::MessageAllocator< ::carbon::config::proto::SetValueRequest, ::carbon::config::proto::SetValueResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(1);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::SetValueRequest, ::carbon::config::proto::SetValueResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetValue(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetValueRequest* /*request*/, ::carbon::config::proto::SetValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetValue(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::SetValueRequest* /*request*/, ::carbon::config::proto::SetValueResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetTree() {
      ::grpc::Service::MarkMethodCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::GetTreeRequest, ::carbon::config::proto::GetTreeResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response) { return this->GetTree(context, request, response); }));}
    void SetMessageAllocatorFor_GetTree(
        ::grpc::MessageAllocator< ::carbon::config::proto::GetTreeRequest, ::carbon::config::proto::GetTreeResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(2);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::GetTreeRequest, ::carbon::config::proto::GetTreeResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetTreeRequest* /*request*/, ::carbon::config::proto::GetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTree(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::GetTreeRequest* /*request*/, ::carbon::config::proto::GetTreeResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_SetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_SetTree() {
      ::grpc::Service::MarkMethodCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::SetTreeRequest, ::carbon::config::proto::SetTreeResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response) { return this->SetTree(context, request, response); }));}
    void SetMessageAllocatorFor_SetTree(
        ::grpc::MessageAllocator< ::carbon::config::proto::SetTreeRequest, ::carbon::config::proto::SetTreeResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(3);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::SetTreeRequest, ::carbon::config::proto::SetTreeResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_SetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetTreeRequest* /*request*/, ::carbon::config::proto::SetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetTree(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::SetTreeRequest* /*request*/, ::carbon::config::proto::SetTreeResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_GetLeaves : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_GetLeaves() {
      ::grpc::Service::MarkMethodCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::GetLeavesRequest, ::carbon::config::proto::GetLeavesResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response) { return this->GetLeaves(context, request, response); }));}
    void SetMessageAllocatorFor_GetLeaves(
        ::grpc::MessageAllocator< ::carbon::config::proto::GetLeavesRequest, ::carbon::config::proto::GetLeavesResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(4);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::GetLeavesRequest, ::carbon::config::proto::GetLeavesResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_GetLeaves() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLeaves(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetLeavesRequest* /*request*/, ::carbon::config::proto::GetLeavesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetLeaves(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::GetLeavesRequest* /*request*/, ::carbon::config::proto::GetLeavesResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_AddToList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_AddToList() {
      ::grpc::Service::MarkMethodCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::AddToListRequest, ::carbon::config::proto::AddToListResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response) { return this->AddToList(context, request, response); }));}
    void SetMessageAllocatorFor_AddToList(
        ::grpc::MessageAllocator< ::carbon::config::proto::AddToListRequest, ::carbon::config::proto::AddToListResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(5);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::AddToListRequest, ::carbon::config::proto::AddToListResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_AddToList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddToList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::AddToListRequest* /*request*/, ::carbon::config::proto::AddToListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AddToList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::AddToListRequest* /*request*/, ::carbon::config::proto::AddToListResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_RemoveFromList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_RemoveFromList() {
      ::grpc::Service::MarkMethodCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::RemoveFromListRequest, ::carbon::config::proto::RemoveFromListResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response) { return this->RemoveFromList(context, request, response); }));}
    void SetMessageAllocatorFor_RemoveFromList(
        ::grpc::MessageAllocator< ::carbon::config::proto::RemoveFromListRequest, ::carbon::config::proto::RemoveFromListResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(6);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::RemoveFromListRequest, ::carbon::config::proto::RemoveFromListResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_RemoveFromList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveFromList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::RemoveFromListRequest* /*request*/, ::carbon::config::proto::RemoveFromListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RemoveFromList(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::RemoveFromListRequest* /*request*/, ::carbon::config::proto::RemoveFromListResponse* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithCallbackMethod_UpgradeCloudConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_UpgradeCloudConfig() {
      ::grpc::Service::MarkMethodCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::UpgradeCloudConfigRequest, ::carbon::config::proto::UpgradeCloudConfigResponse>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response) { return this->UpgradeCloudConfig(context, request, response); }));}
    void SetMessageAllocatorFor_UpgradeCloudConfig(
        ::grpc::MessageAllocator< ::carbon::config::proto::UpgradeCloudConfigRequest, ::carbon::config::proto::UpgradeCloudConfigResponse>* allocator) {
      ::grpc::internal::MethodHandler* const handler = ::grpc::Service::GetHandler(7);
      static_cast<::grpc::internal::CallbackUnaryHandler< ::carbon::config::proto::UpgradeCloudConfigRequest, ::carbon::config::proto::UpgradeCloudConfigResponse>*>(handler)
              ->SetMessageAllocator(allocator);
    }
    ~WithCallbackMethod_UpgradeCloudConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpgradeCloudConfig(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::UpgradeCloudConfigRequest* /*request*/, ::carbon::config::proto::UpgradeCloudConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpgradeCloudConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::UpgradeCloudConfigRequest* /*request*/, ::carbon::config::proto::UpgradeCloudConfigResponse* /*response*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Ping<WithCallbackMethod_SetValue<WithCallbackMethod_GetTree<WithCallbackMethod_SetTree<WithCallbackMethod_GetLeaves<WithCallbackMethod_AddToList<WithCallbackMethod_RemoveFromList<WithCallbackMethod_UpgradeCloudConfig<Service > > > > > > > > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Ping() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::PingRequest* /*request*/, ::carbon::config::proto::PongResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetValue() {
      ::grpc::Service::MarkMethodGeneric(1);
    }
    ~WithGenericMethod_SetValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetValue(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetValueRequest* /*request*/, ::carbon::config::proto::SetValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetTree() {
      ::grpc::Service::MarkMethodGeneric(2);
    }
    ~WithGenericMethod_GetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetTreeRequest* /*request*/, ::carbon::config::proto::GetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_SetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_SetTree() {
      ::grpc::Service::MarkMethodGeneric(3);
    }
    ~WithGenericMethod_SetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetTreeRequest* /*request*/, ::carbon::config::proto::SetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_GetLeaves : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_GetLeaves() {
      ::grpc::Service::MarkMethodGeneric(4);
    }
    ~WithGenericMethod_GetLeaves() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLeaves(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetLeavesRequest* /*request*/, ::carbon::config::proto::GetLeavesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_AddToList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_AddToList() {
      ::grpc::Service::MarkMethodGeneric(5);
    }
    ~WithGenericMethod_AddToList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddToList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::AddToListRequest* /*request*/, ::carbon::config::proto::AddToListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_RemoveFromList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_RemoveFromList() {
      ::grpc::Service::MarkMethodGeneric(6);
    }
    ~WithGenericMethod_RemoveFromList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveFromList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::RemoveFromListRequest* /*request*/, ::carbon::config::proto::RemoveFromListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithGenericMethod_UpgradeCloudConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_UpgradeCloudConfig() {
      ::grpc::Service::MarkMethodGeneric(7);
    }
    ~WithGenericMethod_UpgradeCloudConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpgradeCloudConfig(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::UpgradeCloudConfigRequest* /*request*/, ::carbon::config::proto::UpgradeCloudConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Ping() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::PingRequest* /*request*/, ::carbon::config::proto::PongResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestPing(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(0, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetValue() {
      ::grpc::Service::MarkMethodRaw(1);
    }
    ~WithRawMethod_SetValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetValue(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetValueRequest* /*request*/, ::carbon::config::proto::SetValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetValue(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(1, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetTree() {
      ::grpc::Service::MarkMethodRaw(2);
    }
    ~WithRawMethod_GetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetTreeRequest* /*request*/, ::carbon::config::proto::GetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetTree(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(2, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_SetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_SetTree() {
      ::grpc::Service::MarkMethodRaw(3);
    }
    ~WithRawMethod_SetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetTreeRequest* /*request*/, ::carbon::config::proto::SetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSetTree(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(3, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_GetLeaves : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_GetLeaves() {
      ::grpc::Service::MarkMethodRaw(4);
    }
    ~WithRawMethod_GetLeaves() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLeaves(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetLeavesRequest* /*request*/, ::carbon::config::proto::GetLeavesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestGetLeaves(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(4, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_AddToList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_AddToList() {
      ::grpc::Service::MarkMethodRaw(5);
    }
    ~WithRawMethod_AddToList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddToList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::AddToListRequest* /*request*/, ::carbon::config::proto::AddToListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestAddToList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(5, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_RemoveFromList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_RemoveFromList() {
      ::grpc::Service::MarkMethodRaw(6);
    }
    ~WithRawMethod_RemoveFromList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveFromList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::RemoveFromListRequest* /*request*/, ::carbon::config::proto::RemoveFromListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestRemoveFromList(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(6, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawMethod_UpgradeCloudConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_UpgradeCloudConfig() {
      ::grpc::Service::MarkMethodRaw(7);
    }
    ~WithRawMethod_UpgradeCloudConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpgradeCloudConfig(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::UpgradeCloudConfigRequest* /*request*/, ::carbon::config::proto::UpgradeCloudConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestUpgradeCloudConfig(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncResponseWriter< ::grpc::ByteBuffer>* response, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncUnary(7, context, request, response, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Ping() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->Ping(context, request, response); }));
    }
    ~WithRawCallbackMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::PingRequest* /*request*/, ::carbon::config::proto::PongResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* Ping(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetValue() {
      ::grpc::Service::MarkMethodRawCallback(1,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetValue(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetValue(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetValueRequest* /*request*/, ::carbon::config::proto::SetValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetValue(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetTree() {
      ::grpc::Service::MarkMethodRawCallback(2,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetTree(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetTreeRequest* /*request*/, ::carbon::config::proto::GetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetTree(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_SetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_SetTree() {
      ::grpc::Service::MarkMethodRawCallback(3,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->SetTree(context, request, response); }));
    }
    ~WithRawCallbackMethod_SetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status SetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetTreeRequest* /*request*/, ::carbon::config::proto::SetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* SetTree(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_GetLeaves : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_GetLeaves() {
      ::grpc::Service::MarkMethodRawCallback(4,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->GetLeaves(context, request, response); }));
    }
    ~WithRawCallbackMethod_GetLeaves() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status GetLeaves(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetLeavesRequest* /*request*/, ::carbon::config::proto::GetLeavesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* GetLeaves(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_AddToList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_AddToList() {
      ::grpc::Service::MarkMethodRawCallback(5,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->AddToList(context, request, response); }));
    }
    ~WithRawCallbackMethod_AddToList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status AddToList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::AddToListRequest* /*request*/, ::carbon::config::proto::AddToListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* AddToList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_RemoveFromList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_RemoveFromList() {
      ::grpc::Service::MarkMethodRawCallback(6,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->RemoveFromList(context, request, response); }));
    }
    ~WithRawCallbackMethod_RemoveFromList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status RemoveFromList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::RemoveFromListRequest* /*request*/, ::carbon::config::proto::RemoveFromListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* RemoveFromList(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_UpgradeCloudConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_UpgradeCloudConfig() {
      ::grpc::Service::MarkMethodRawCallback(7,
          new ::grpc::internal::CallbackUnaryHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::grpc::ByteBuffer* request, ::grpc::ByteBuffer* response) { return this->UpgradeCloudConfig(context, request, response); }));
    }
    ~WithRawCallbackMethod_UpgradeCloudConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status UpgradeCloudConfig(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::UpgradeCloudConfigRequest* /*request*/, ::carbon::config::proto::UpgradeCloudConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerUnaryReactor* UpgradeCloudConfig(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/, ::grpc::ByteBuffer* /*response*/)  { return nullptr; }
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_Ping : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_Ping() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::config::proto::PingRequest, ::carbon::config::proto::PongResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::config::proto::PingRequest, ::carbon::config::proto::PongResponse>* streamer) {
                       return this->StreamedPing(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_Ping() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Ping(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::PingRequest* /*request*/, ::carbon::config::proto::PongResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedPing(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::config::proto::PingRequest,::carbon::config::proto::PongResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetValue : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetValue() {
      ::grpc::Service::MarkMethodStreamed(1,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::config::proto::SetValueRequest, ::carbon::config::proto::SetValueResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::config::proto::SetValueRequest, ::carbon::config::proto::SetValueResponse>* streamer) {
                       return this->StreamedSetValue(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetValue() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetValue(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetValueRequest* /*request*/, ::carbon::config::proto::SetValueResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetValue(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::config::proto::SetValueRequest,::carbon::config::proto::SetValueResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetTree() {
      ::grpc::Service::MarkMethodStreamed(2,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::config::proto::GetTreeRequest, ::carbon::config::proto::GetTreeResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::config::proto::GetTreeRequest, ::carbon::config::proto::GetTreeResponse>* streamer) {
                       return this->StreamedGetTree(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetTreeRequest* /*request*/, ::carbon::config::proto::GetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetTree(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::config::proto::GetTreeRequest,::carbon::config::proto::GetTreeResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_SetTree : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_SetTree() {
      ::grpc::Service::MarkMethodStreamed(3,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::config::proto::SetTreeRequest, ::carbon::config::proto::SetTreeResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::config::proto::SetTreeRequest, ::carbon::config::proto::SetTreeResponse>* streamer) {
                       return this->StreamedSetTree(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_SetTree() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status SetTree(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SetTreeRequest* /*request*/, ::carbon::config::proto::SetTreeResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedSetTree(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::config::proto::SetTreeRequest,::carbon::config::proto::SetTreeResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_GetLeaves : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_GetLeaves() {
      ::grpc::Service::MarkMethodStreamed(4,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::config::proto::GetLeavesRequest, ::carbon::config::proto::GetLeavesResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::config::proto::GetLeavesRequest, ::carbon::config::proto::GetLeavesResponse>* streamer) {
                       return this->StreamedGetLeaves(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_GetLeaves() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status GetLeaves(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::GetLeavesRequest* /*request*/, ::carbon::config::proto::GetLeavesResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedGetLeaves(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::config::proto::GetLeavesRequest,::carbon::config::proto::GetLeavesResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_AddToList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_AddToList() {
      ::grpc::Service::MarkMethodStreamed(5,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::config::proto::AddToListRequest, ::carbon::config::proto::AddToListResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::config::proto::AddToListRequest, ::carbon::config::proto::AddToListResponse>* streamer) {
                       return this->StreamedAddToList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_AddToList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status AddToList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::AddToListRequest* /*request*/, ::carbon::config::proto::AddToListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedAddToList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::config::proto::AddToListRequest,::carbon::config::proto::AddToListResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_RemoveFromList : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_RemoveFromList() {
      ::grpc::Service::MarkMethodStreamed(6,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::config::proto::RemoveFromListRequest, ::carbon::config::proto::RemoveFromListResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::config::proto::RemoveFromListRequest, ::carbon::config::proto::RemoveFromListResponse>* streamer) {
                       return this->StreamedRemoveFromList(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_RemoveFromList() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status RemoveFromList(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::RemoveFromListRequest* /*request*/, ::carbon::config::proto::RemoveFromListResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedRemoveFromList(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::config::proto::RemoveFromListRequest,::carbon::config::proto::RemoveFromListResponse>* server_unary_streamer) = 0;
  };
  template <class BaseClass>
  class WithStreamedUnaryMethod_UpgradeCloudConfig : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithStreamedUnaryMethod_UpgradeCloudConfig() {
      ::grpc::Service::MarkMethodStreamed(7,
        new ::grpc::internal::StreamedUnaryHandler<
          ::carbon::config::proto::UpgradeCloudConfigRequest, ::carbon::config::proto::UpgradeCloudConfigResponse>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerUnaryStreamer<
                     ::carbon::config::proto::UpgradeCloudConfigRequest, ::carbon::config::proto::UpgradeCloudConfigResponse>* streamer) {
                       return this->StreamedUpgradeCloudConfig(context,
                         streamer);
                  }));
    }
    ~WithStreamedUnaryMethod_UpgradeCloudConfig() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status UpgradeCloudConfig(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::UpgradeCloudConfigRequest* /*request*/, ::carbon::config::proto::UpgradeCloudConfigResponse* /*response*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with streamed unary
    virtual ::grpc::Status StreamedUpgradeCloudConfig(::grpc::ServerContext* context, ::grpc::ServerUnaryStreamer< ::carbon::config::proto::UpgradeCloudConfigRequest,::carbon::config::proto::UpgradeCloudConfigResponse>* server_unary_streamer) = 0;
  };
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_SetValue<WithStreamedUnaryMethod_GetTree<WithStreamedUnaryMethod_SetTree<WithStreamedUnaryMethod_GetLeaves<WithStreamedUnaryMethod_AddToList<WithStreamedUnaryMethod_RemoveFromList<WithStreamedUnaryMethod_UpgradeCloudConfig<Service > > > > > > > > StreamedUnaryService;
  typedef Service SplitStreamedService;
  typedef WithStreamedUnaryMethod_Ping<WithStreamedUnaryMethod_SetValue<WithStreamedUnaryMethod_GetTree<WithStreamedUnaryMethod_SetTree<WithStreamedUnaryMethod_GetLeaves<WithStreamedUnaryMethod_AddToList<WithStreamedUnaryMethod_RemoveFromList<WithStreamedUnaryMethod_UpgradeCloudConfig<Service > > > > > > > > StreamedService;
};

class ConfigNotificationService final {
 public:
  static constexpr char const* service_full_name() {
    return "carbon.config.proto.ConfigNotificationService";
  }
  class StubInterface {
   public:
    virtual ~StubInterface() {}
    std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>> Subscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request) {
      return std::unique_ptr< ::grpc::ClientReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>>(SubscribeRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>> AsyncSubscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>>(AsyncSubscribeRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>> PrepareAsyncSubscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>>(PrepareAsyncSubscribeRaw(context, request, cq));
    }
    class async_interface {
     public:
      virtual ~async_interface() {}
      virtual void Subscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest* request, ::grpc::ClientReadReactor< ::carbon::config::proto::SubscriptionNotifyMessage>* reactor) = 0;
    };
    typedef class async_interface experimental_async_interface;
    virtual class async_interface* async() { return nullptr; }
    class async_interface* experimental_async() { return async(); }
   private:
    virtual ::grpc::ClientReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>* SubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>* AsyncSubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq, void* tag) = 0;
    virtual ::grpc::ClientAsyncReaderInterface< ::carbon::config::proto::SubscriptionNotifyMessage>* PrepareAsyncSubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq) = 0;
  };
  class Stub final : public StubInterface {
   public:
    Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());
    std::unique_ptr< ::grpc::ClientReader< ::carbon::config::proto::SubscriptionNotifyMessage>> Subscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request) {
      return std::unique_ptr< ::grpc::ClientReader< ::carbon::config::proto::SubscriptionNotifyMessage>>(SubscribeRaw(context, request));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::config::proto::SubscriptionNotifyMessage>> AsyncSubscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::config::proto::SubscriptionNotifyMessage>>(AsyncSubscribeRaw(context, request, cq, tag));
    }
    std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::config::proto::SubscriptionNotifyMessage>> PrepareAsyncSubscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq) {
      return std::unique_ptr< ::grpc::ClientAsyncReader< ::carbon::config::proto::SubscriptionNotifyMessage>>(PrepareAsyncSubscribeRaw(context, request, cq));
    }
    class async final :
      public StubInterface::async_interface {
     public:
      void Subscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest* request, ::grpc::ClientReadReactor< ::carbon::config::proto::SubscriptionNotifyMessage>* reactor) override;
     private:
      friend class Stub;
      explicit async(Stub* stub): stub_(stub) { }
      Stub* stub() { return stub_; }
      Stub* stub_;
    };
    class async* async() override { return &async_stub_; }

   private:
    std::shared_ptr< ::grpc::ChannelInterface> channel_;
    class async async_stub_{this};
    ::grpc::ClientReader< ::carbon::config::proto::SubscriptionNotifyMessage>* SubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request) override;
    ::grpc::ClientAsyncReader< ::carbon::config::proto::SubscriptionNotifyMessage>* AsyncSubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq, void* tag) override;
    ::grpc::ClientAsyncReader< ::carbon::config::proto::SubscriptionNotifyMessage>* PrepareAsyncSubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq) override;
    const ::grpc::internal::RpcMethod rpcmethod_Subscribe_;
  };
  static std::unique_ptr<Stub> NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options = ::grpc::StubOptions());

  class Service : public ::grpc::Service {
   public:
    Service();
    virtual ~Service();
    virtual ::grpc::Status Subscribe(::grpc::ServerContext* context, const ::carbon::config::proto::SubscriptionRequest* request, ::grpc::ServerWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* writer);
  };
  template <class BaseClass>
  class WithAsyncMethod_Subscribe : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithAsyncMethod_Subscribe() {
      ::grpc::Service::MarkMethodAsync(0);
    }
    ~WithAsyncMethod_Subscribe() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Subscribe(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SubscriptionRequest* /*request*/, ::grpc::ServerWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSubscribe(::grpc::ServerContext* context, ::carbon::config::proto::SubscriptionRequest* request, ::grpc::ServerAsyncWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(0, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  typedef WithAsyncMethod_Subscribe<Service > AsyncService;
  template <class BaseClass>
  class WithCallbackMethod_Subscribe : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithCallbackMethod_Subscribe() {
      ::grpc::Service::MarkMethodCallback(0,
          new ::grpc::internal::CallbackServerStreamingHandler< ::carbon::config::proto::SubscriptionRequest, ::carbon::config::proto::SubscriptionNotifyMessage>(
            [this](
                   ::grpc::CallbackServerContext* context, const ::carbon::config::proto::SubscriptionRequest* request) { return this->Subscribe(context, request); }));
    }
    ~WithCallbackMethod_Subscribe() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Subscribe(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SubscriptionRequest* /*request*/, ::grpc::ServerWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::carbon::config::proto::SubscriptionNotifyMessage>* Subscribe(
      ::grpc::CallbackServerContext* /*context*/, const ::carbon::config::proto::SubscriptionRequest* /*request*/)  { return nullptr; }
  };
  typedef WithCallbackMethod_Subscribe<Service > CallbackService;
  typedef CallbackService ExperimentalCallbackService;
  template <class BaseClass>
  class WithGenericMethod_Subscribe : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithGenericMethod_Subscribe() {
      ::grpc::Service::MarkMethodGeneric(0);
    }
    ~WithGenericMethod_Subscribe() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Subscribe(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SubscriptionRequest* /*request*/, ::grpc::ServerWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
  };
  template <class BaseClass>
  class WithRawMethod_Subscribe : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawMethod_Subscribe() {
      ::grpc::Service::MarkMethodRaw(0);
    }
    ~WithRawMethod_Subscribe() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Subscribe(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SubscriptionRequest* /*request*/, ::grpc::ServerWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    void RequestSubscribe(::grpc::ServerContext* context, ::grpc::ByteBuffer* request, ::grpc::ServerAsyncWriter< ::grpc::ByteBuffer>* writer, ::grpc::CompletionQueue* new_call_cq, ::grpc::ServerCompletionQueue* notification_cq, void *tag) {
      ::grpc::Service::RequestAsyncServerStreaming(0, context, request, writer, new_call_cq, notification_cq, tag);
    }
  };
  template <class BaseClass>
  class WithRawCallbackMethod_Subscribe : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithRawCallbackMethod_Subscribe() {
      ::grpc::Service::MarkMethodRawCallback(0,
          new ::grpc::internal::CallbackServerStreamingHandler< ::grpc::ByteBuffer, ::grpc::ByteBuffer>(
            [this](
                   ::grpc::CallbackServerContext* context, const::grpc::ByteBuffer* request) { return this->Subscribe(context, request); }));
    }
    ~WithRawCallbackMethod_Subscribe() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable synchronous version of this method
    ::grpc::Status Subscribe(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SubscriptionRequest* /*request*/, ::grpc::ServerWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    virtual ::grpc::ServerWriteReactor< ::grpc::ByteBuffer>* Subscribe(
      ::grpc::CallbackServerContext* /*context*/, const ::grpc::ByteBuffer* /*request*/)  { return nullptr; }
  };
  typedef Service StreamedUnaryService;
  template <class BaseClass>
  class WithSplitStreamingMethod_Subscribe : public BaseClass {
   private:
    void BaseClassMustBeDerivedFromService(const Service* /*service*/) {}
   public:
    WithSplitStreamingMethod_Subscribe() {
      ::grpc::Service::MarkMethodStreamed(0,
        new ::grpc::internal::SplitServerStreamingHandler<
          ::carbon::config::proto::SubscriptionRequest, ::carbon::config::proto::SubscriptionNotifyMessage>(
            [this](::grpc::ServerContext* context,
                   ::grpc::ServerSplitStreamer<
                     ::carbon::config::proto::SubscriptionRequest, ::carbon::config::proto::SubscriptionNotifyMessage>* streamer) {
                       return this->StreamedSubscribe(context,
                         streamer);
                  }));
    }
    ~WithSplitStreamingMethod_Subscribe() override {
      BaseClassMustBeDerivedFromService(this);
    }
    // disable regular version of this method
    ::grpc::Status Subscribe(::grpc::ServerContext* /*context*/, const ::carbon::config::proto::SubscriptionRequest* /*request*/, ::grpc::ServerWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* /*writer*/) override {
      abort();
      return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
    }
    // replace default version of method with split streamed
    virtual ::grpc::Status StreamedSubscribe(::grpc::ServerContext* context, ::grpc::ServerSplitStreamer< ::carbon::config::proto::SubscriptionRequest,::carbon::config::proto::SubscriptionNotifyMessage>* server_split_streamer) = 0;
  };
  typedef WithSplitStreamingMethod_Subscribe<Service > SplitStreamedService;
  typedef WithSplitStreamingMethod_Subscribe<Service > StreamedService;
};

}  // namespace proto
}  // namespace config
}  // namespace carbon


#endif  // GRPC_config_2fapi_2fproto_2fconfig_5fservice_2eproto__INCLUDED

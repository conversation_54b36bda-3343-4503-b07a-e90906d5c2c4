# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from config.api.proto import config_service_pb2 as config_dot_api_dot_proto_dot_config__service__pb2


class ConfigServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Ping = channel.unary_unary(
                '/carbon.config.proto.ConfigService/Ping',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.PingRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.PongResponse.FromString,
                )
        self.SetValue = channel.unary_unary(
                '/carbon.config.proto.ConfigService/SetValue',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.SetValueRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.SetValueResponse.FromString,
                )
        self.GetTree = channel.unary_unary(
                '/carbon.config.proto.ConfigService/GetTree',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.GetTreeRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.GetTreeResponse.FromString,
                )
        self.SetTree = channel.unary_unary(
                '/carbon.config.proto.ConfigService/SetTree',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.SetTreeRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.SetTreeResponse.FromString,
                )
        self.GetLeaves = channel.unary_unary(
                '/carbon.config.proto.ConfigService/GetLeaves',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.GetLeavesRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.GetLeavesResponse.FromString,
                )
        self.AddToList = channel.unary_unary(
                '/carbon.config.proto.ConfigService/AddToList',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.AddToListRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.AddToListResponse.FromString,
                )
        self.RemoveFromList = channel.unary_unary(
                '/carbon.config.proto.ConfigService/RemoveFromList',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.RemoveFromListRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.RemoveFromListResponse.FromString,
                )
        self.UpgradeCloudConfig = channel.unary_unary(
                '/carbon.config.proto.ConfigService/UpgradeCloudConfig',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.UpgradeCloudConfigRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.UpgradeCloudConfigResponse.FromString,
                )


class ConfigServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Ping(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetValue(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetTree(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def SetTree(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetLeaves(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def AddToList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def RemoveFromList(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UpgradeCloudConfig(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ConfigServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Ping': grpc.unary_unary_rpc_method_handler(
                    servicer.Ping,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.PingRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.PongResponse.SerializeToString,
            ),
            'SetValue': grpc.unary_unary_rpc_method_handler(
                    servicer.SetValue,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.SetValueRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.SetValueResponse.SerializeToString,
            ),
            'GetTree': grpc.unary_unary_rpc_method_handler(
                    servicer.GetTree,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.GetTreeRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.GetTreeResponse.SerializeToString,
            ),
            'SetTree': grpc.unary_unary_rpc_method_handler(
                    servicer.SetTree,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.SetTreeRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.SetTreeResponse.SerializeToString,
            ),
            'GetLeaves': grpc.unary_unary_rpc_method_handler(
                    servicer.GetLeaves,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.GetLeavesRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.GetLeavesResponse.SerializeToString,
            ),
            'AddToList': grpc.unary_unary_rpc_method_handler(
                    servicer.AddToList,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.AddToListRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.AddToListResponse.SerializeToString,
            ),
            'RemoveFromList': grpc.unary_unary_rpc_method_handler(
                    servicer.RemoveFromList,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.RemoveFromListRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.RemoveFromListResponse.SerializeToString,
            ),
            'UpgradeCloudConfig': grpc.unary_unary_rpc_method_handler(
                    servicer.UpgradeCloudConfig,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.UpgradeCloudConfigRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.UpgradeCloudConfigResponse.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.config.proto.ConfigService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ConfigService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Ping(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.config.proto.ConfigService/Ping',
            config_dot_api_dot_proto_dot_config__service__pb2.PingRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.PongResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetValue(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.config.proto.ConfigService/SetValue',
            config_dot_api_dot_proto_dot_config__service__pb2.SetValueRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.SetValueResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetTree(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.config.proto.ConfigService/GetTree',
            config_dot_api_dot_proto_dot_config__service__pb2.GetTreeRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.GetTreeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def SetTree(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.config.proto.ConfigService/SetTree',
            config_dot_api_dot_proto_dot_config__service__pb2.SetTreeRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.SetTreeResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetLeaves(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.config.proto.ConfigService/GetLeaves',
            config_dot_api_dot_proto_dot_config__service__pb2.GetLeavesRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.GetLeavesResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def AddToList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.config.proto.ConfigService/AddToList',
            config_dot_api_dot_proto_dot_config__service__pb2.AddToListRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.AddToListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def RemoveFromList(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.config.proto.ConfigService/RemoveFromList',
            config_dot_api_dot_proto_dot_config__service__pb2.RemoveFromListRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.RemoveFromListResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UpgradeCloudConfig(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.config.proto.ConfigService/UpgradeCloudConfig',
            config_dot_api_dot_proto_dot_config__service__pb2.UpgradeCloudConfigRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.UpgradeCloudConfigResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)


class ConfigNotificationServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.Subscribe = channel.unary_stream(
                '/carbon.config.proto.ConfigNotificationService/Subscribe',
                request_serializer=config_dot_api_dot_proto_dot_config__service__pb2.SubscriptionRequest.SerializeToString,
                response_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.SubscriptionNotifyMessage.FromString,
                )


class ConfigNotificationServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def Subscribe(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_ConfigNotificationServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'Subscribe': grpc.unary_stream_rpc_method_handler(
                    servicer.Subscribe,
                    request_deserializer=config_dot_api_dot_proto_dot_config__service__pb2.SubscriptionRequest.FromString,
                    response_serializer=config_dot_api_dot_proto_dot_config__service__pb2.SubscriptionNotifyMessage.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.config.proto.ConfigNotificationService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class ConfigNotificationService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def Subscribe(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_stream(request, target, '/carbon.config.proto.ConfigNotificationService/Subscribe',
            config_dot_api_dot_proto_dot_config__service__pb2.SubscriptionRequest.SerializeToString,
            config_dot_api_dot_proto_dot_config__service__pb2.SubscriptionNotifyMessage.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

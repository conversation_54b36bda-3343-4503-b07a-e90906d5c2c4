// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: config/api/proto/config_service.proto

#include "config/api/proto/config_service.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace carbon {
namespace config {
namespace proto {
constexpr PingRequest::PingRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0){}
struct PingRequestDefaultTypeInternal {
  constexpr PingRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PingRequestDefaultTypeInternal() {}
  union {
    PingRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PingRequestDefaultTypeInternal _PingRequest_default_instance_;
constexpr PongResponse::PongResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : x_(0){}
struct PongResponseDefaultTypeInternal {
  constexpr PongResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~PongResponseDefaultTypeInternal() {}
  union {
    PongResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT PongResponseDefaultTypeInternal _PongResponse_default_instance_;
constexpr ConfigValue::ConfigValue(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_ms_(uint64_t{0u})
  , _oneof_case_{}{}
struct ConfigValueDefaultTypeInternal {
  constexpr ConfigValueDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigValueDefaultTypeInternal() {}
  union {
    ConfigValue _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigValueDefaultTypeInternal _ConfigValue_default_instance_;
constexpr IntConfigDef::IntConfigDef(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : min_(int64_t{0})
  , max_(int64_t{0})
  , step_(int64_t{0}){}
struct IntConfigDefDefaultTypeInternal {
  constexpr IntConfigDefDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~IntConfigDefDefaultTypeInternal() {}
  union {
    IntConfigDef _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT IntConfigDefDefaultTypeInternal _IntConfigDef_default_instance_;
constexpr UIntConfigDef::UIntConfigDef(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : min_(uint64_t{0u})
  , max_(uint64_t{0u})
  , step_(uint64_t{0u}){}
struct UIntConfigDefDefaultTypeInternal {
  constexpr UIntConfigDefDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UIntConfigDefDefaultTypeInternal() {}
  union {
    UIntConfigDef _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UIntConfigDefDefaultTypeInternal _UIntConfigDef_default_instance_;
constexpr FloatConfigDef::FloatConfigDef(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : min_(0)
  , max_(0)
  , step_(0){}
struct FloatConfigDefDefaultTypeInternal {
  constexpr FloatConfigDefDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~FloatConfigDefDefaultTypeInternal() {}
  union {
    FloatConfigDef _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT FloatConfigDefDefaultTypeInternal _FloatConfigDef_default_instance_;
constexpr StringConfigDef::StringConfigDef(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : choices_()
  , size_limit_(0u){}
struct StringConfigDefDefaultTypeInternal {
  constexpr StringConfigDefDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~StringConfigDefDefaultTypeInternal() {}
  union {
    StringConfigDef _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT StringConfigDefDefaultTypeInternal _StringConfigDef_default_instance_;
constexpr ConfigDef::ConfigDef(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : hint_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , units_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , type_(0)

  , complexity_(0)

  , default_recommended_(false)
  , _oneof_case_{}{}
struct ConfigDefDefaultTypeInternal {
  constexpr ConfigDefDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigDefDefaultTypeInternal() {}
  union {
    ConfigDef _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigDefDefaultTypeInternal _ConfigDef_default_instance_;
constexpr ConfigNode::ConfigNode(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : children_()
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , value_(nullptr)
  , def_(nullptr){}
struct ConfigNodeDefaultTypeInternal {
  constexpr ConfigNodeDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigNodeDefaultTypeInternal() {}
  union {
    ConfigNode _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigNodeDefaultTypeInternal _ConfigNode_default_instance_;
constexpr ConfigLeaf::ConfigLeaf(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , value_(nullptr){}
struct ConfigLeafDefaultTypeInternal {
  constexpr ConfigLeafDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ConfigLeafDefaultTypeInternal() {}
  union {
    ConfigLeaf _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ConfigLeafDefaultTypeInternal _ConfigLeaf_default_instance_;
constexpr SetValueRequest::SetValueRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , value_(nullptr){}
struct SetValueRequestDefaultTypeInternal {
  constexpr SetValueRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetValueRequestDefaultTypeInternal() {}
  union {
    SetValueRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetValueRequestDefaultTypeInternal _SetValueRequest_default_instance_;
constexpr SetValueResponse::SetValueResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetValueResponseDefaultTypeInternal {
  constexpr SetValueResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetValueResponseDefaultTypeInternal() {}
  union {
    SetValueResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetValueResponseDefaultTypeInternal _SetValueResponse_default_instance_;
constexpr GetTreeRequest::GetTreeRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetTreeRequestDefaultTypeInternal {
  constexpr GetTreeRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetTreeRequestDefaultTypeInternal() {}
  union {
    GetTreeRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetTreeRequestDefaultTypeInternal _GetTreeRequest_default_instance_;
constexpr GetTreeResponse::GetTreeResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : node_(nullptr){}
struct GetTreeResponseDefaultTypeInternal {
  constexpr GetTreeResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetTreeResponseDefaultTypeInternal() {}
  union {
    GetTreeResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetTreeResponseDefaultTypeInternal _GetTreeResponse_default_instance_;
constexpr SetTreeRequest::SetTreeRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , node_(nullptr){}
struct SetTreeRequestDefaultTypeInternal {
  constexpr SetTreeRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetTreeRequestDefaultTypeInternal() {}
  union {
    SetTreeRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetTreeRequestDefaultTypeInternal _SetTreeRequest_default_instance_;
constexpr SetTreeResponse::SetTreeResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct SetTreeResponseDefaultTypeInternal {
  constexpr SetTreeResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SetTreeResponseDefaultTypeInternal() {}
  union {
    SetTreeResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SetTreeResponseDefaultTypeInternal _SetTreeResponse_default_instance_;
constexpr GetLeavesRequest::GetLeavesRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct GetLeavesRequestDefaultTypeInternal {
  constexpr GetLeavesRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetLeavesRequestDefaultTypeInternal() {}
  union {
    GetLeavesRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetLeavesRequestDefaultTypeInternal _GetLeavesRequest_default_instance_;
constexpr GetLeavesResponse::GetLeavesResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : leaves_(){}
struct GetLeavesResponseDefaultTypeInternal {
  constexpr GetLeavesResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~GetLeavesResponseDefaultTypeInternal() {}
  union {
    GetLeavesResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT GetLeavesResponseDefaultTypeInternal _GetLeavesResponse_default_instance_;
constexpr AddToListRequest::AddToListRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct AddToListRequestDefaultTypeInternal {
  constexpr AddToListRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AddToListRequestDefaultTypeInternal() {}
  union {
    AddToListRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AddToListRequestDefaultTypeInternal _AddToListRequest_default_instance_;
constexpr AddToListResponse::AddToListResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct AddToListResponseDefaultTypeInternal {
  constexpr AddToListResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~AddToListResponseDefaultTypeInternal() {}
  union {
    AddToListResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT AddToListResponseDefaultTypeInternal _AddToListResponse_default_instance_;
constexpr RemoveFromListRequest::RemoveFromListRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , name_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct RemoveFromListRequestDefaultTypeInternal {
  constexpr RemoveFromListRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RemoveFromListRequestDefaultTypeInternal() {}
  union {
    RemoveFromListRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RemoveFromListRequestDefaultTypeInternal _RemoveFromListRequest_default_instance_;
constexpr RemoveFromListResponse::RemoveFromListResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct RemoveFromListResponseDefaultTypeInternal {
  constexpr RemoveFromListResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RemoveFromListResponseDefaultTypeInternal() {}
  union {
    RemoveFromListResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RemoveFromListResponseDefaultTypeInternal _RemoveFromListResponse_default_instance_;
constexpr SubscriptionRequest::SubscriptionRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : keys_(){}
struct SubscriptionRequestDefaultTypeInternal {
  constexpr SubscriptionRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SubscriptionRequestDefaultTypeInternal() {}
  union {
    SubscriptionRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SubscriptionRequestDefaultTypeInternal _SubscriptionRequest_default_instance_;
constexpr SubscriptionNotifyMessage::SubscriptionNotifyMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : subscription_key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , notify_key_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct SubscriptionNotifyMessageDefaultTypeInternal {
  constexpr SubscriptionNotifyMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SubscriptionNotifyMessageDefaultTypeInternal() {}
  union {
    SubscriptionNotifyMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SubscriptionNotifyMessageDefaultTypeInternal _SubscriptionNotifyMessage_default_instance_;
constexpr UpgradeCloudConfigRequest::UpgradeCloudConfigRequest(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : robot_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , to_template_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , from_template_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string){}
struct UpgradeCloudConfigRequestDefaultTypeInternal {
  constexpr UpgradeCloudConfigRequestDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpgradeCloudConfigRequestDefaultTypeInternal() {}
  union {
    UpgradeCloudConfigRequest _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpgradeCloudConfigRequestDefaultTypeInternal _UpgradeCloudConfigRequest_default_instance_;
constexpr UpgradeCloudConfigResponse::UpgradeCloudConfigResponse(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized){}
struct UpgradeCloudConfigResponseDefaultTypeInternal {
  constexpr UpgradeCloudConfigResponseDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpgradeCloudConfigResponseDefaultTypeInternal() {}
  union {
    UpgradeCloudConfigResponse _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpgradeCloudConfigResponseDefaultTypeInternal _UpgradeCloudConfigResponse_default_instance_;
}  // namespace proto
}  // namespace config
}  // namespace carbon
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[26];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_config_2fapi_2fproto_2fconfig_5fservice_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_config_2fapi_2fproto_2fconfig_5fservice_2eproto = nullptr;

const uint32_t TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::PingRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::PingRequest, x_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::PongResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::PongResponse, x_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigValue, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigValue, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigValue, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigValue, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::IntConfigDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::IntConfigDef, min_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::IntConfigDef, max_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::IntConfigDef, step_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UIntConfigDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UIntConfigDef, min_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UIntConfigDef, max_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UIntConfigDef, step_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::FloatConfigDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::FloatConfigDef, min_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::FloatConfigDef, max_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::FloatConfigDef, step_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::StringConfigDef, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::StringConfigDef, size_limit_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::StringConfigDef, choices_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigDef, _internal_metadata_),
  ~0u,  // no _extensions_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigDef, _oneof_case_[0]),
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigDef, type_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigDef, complexity_),
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  ::PROTOBUF_NAMESPACE_ID::internal::kInvalidFieldOffsetTag,
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigDef, hint_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigDef, default_recommended_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigDef, units_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigDef, extra_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigNode, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigNode, name_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigNode, value_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigNode, def_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigNode, children_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigLeaf, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigLeaf, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::ConfigLeaf, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SetValueRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SetValueRequest, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SetValueRequest, value_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SetValueResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::GetTreeRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::GetTreeRequest, key_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::GetTreeResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::GetTreeResponse, node_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SetTreeRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SetTreeRequest, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SetTreeRequest, node_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SetTreeResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::GetLeavesRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::GetLeavesRequest, key_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::GetLeavesResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::GetLeavesResponse, leaves_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::AddToListRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::AddToListRequest, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::AddToListRequest, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::AddToListResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::RemoveFromListRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::RemoveFromListRequest, key_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::RemoveFromListRequest, name_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::RemoveFromListResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SubscriptionRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SubscriptionRequest, keys_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SubscriptionNotifyMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SubscriptionNotifyMessage, subscription_key_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::SubscriptionNotifyMessage, notify_key_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UpgradeCloudConfigRequest, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UpgradeCloudConfigRequest, robot_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UpgradeCloudConfigRequest, to_template_),
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UpgradeCloudConfigRequest, from_template_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::carbon::config::proto::UpgradeCloudConfigResponse, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::carbon::config::proto::PingRequest)},
  { 7, -1, -1, sizeof(::carbon::config::proto::PongResponse)},
  { 14, -1, -1, sizeof(::carbon::config::proto::ConfigValue)},
  { 27, -1, -1, sizeof(::carbon::config::proto::IntConfigDef)},
  { 36, -1, -1, sizeof(::carbon::config::proto::UIntConfigDef)},
  { 45, -1, -1, sizeof(::carbon::config::proto::FloatConfigDef)},
  { 54, -1, -1, sizeof(::carbon::config::proto::StringConfigDef)},
  { 62, -1, -1, sizeof(::carbon::config::proto::ConfigDef)},
  { 78, -1, -1, sizeof(::carbon::config::proto::ConfigNode)},
  { 88, -1, -1, sizeof(::carbon::config::proto::ConfigLeaf)},
  { 96, -1, -1, sizeof(::carbon::config::proto::SetValueRequest)},
  { 104, -1, -1, sizeof(::carbon::config::proto::SetValueResponse)},
  { 110, -1, -1, sizeof(::carbon::config::proto::GetTreeRequest)},
  { 117, -1, -1, sizeof(::carbon::config::proto::GetTreeResponse)},
  { 124, -1, -1, sizeof(::carbon::config::proto::SetTreeRequest)},
  { 132, -1, -1, sizeof(::carbon::config::proto::SetTreeResponse)},
  { 138, -1, -1, sizeof(::carbon::config::proto::GetLeavesRequest)},
  { 145, -1, -1, sizeof(::carbon::config::proto::GetLeavesResponse)},
  { 152, -1, -1, sizeof(::carbon::config::proto::AddToListRequest)},
  { 160, -1, -1, sizeof(::carbon::config::proto::AddToListResponse)},
  { 166, -1, -1, sizeof(::carbon::config::proto::RemoveFromListRequest)},
  { 174, -1, -1, sizeof(::carbon::config::proto::RemoveFromListResponse)},
  { 180, -1, -1, sizeof(::carbon::config::proto::SubscriptionRequest)},
  { 187, -1, -1, sizeof(::carbon::config::proto::SubscriptionNotifyMessage)},
  { 195, -1, -1, sizeof(::carbon::config::proto::UpgradeCloudConfigRequest)},
  { 204, -1, -1, sizeof(::carbon::config::proto::UpgradeCloudConfigResponse)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_PingRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_PongResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_ConfigValue_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_IntConfigDef_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_UIntConfigDef_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_FloatConfigDef_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_StringConfigDef_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_ConfigDef_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_ConfigNode_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_ConfigLeaf_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_SetValueRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_SetValueResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_GetTreeRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_GetTreeResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_SetTreeRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_SetTreeResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_GetLeavesRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_GetLeavesResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_AddToListRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_AddToListResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_RemoveFromListRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_RemoveFromListResponse_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_SubscriptionRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_SubscriptionNotifyMessage_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_UpgradeCloudConfigRequest_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::carbon::config::proto::_UpgradeCloudConfigResponse_default_instance_),
};

const char descriptor_table_protodef_config_2fapi_2fproto_2fconfig_5fservice_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n%config/api/proto/config_service.proto\022"
  "\023carbon.config.proto\"\030\n\013PingRequest\022\t\n\001x"
  "\030\001 \001(\005\"\031\n\014PongResponse\022\t\n\001x\030\001 \001(\005\"\226\001\n\013Co"
  "nfigValue\022\023\n\tint64_val\030\001 \001(\003H\000\022\024\n\nuint64"
  "_val\030\002 \001(\004H\000\022\022\n\010bool_val\030\003 \001(\010H\000\022\023\n\tfloa"
  "t_val\030\004 \001(\001H\000\022\024\n\nstring_val\030\005 \001(\tH\000\022\024\n\014t"
  "imestamp_ms\030\006 \001(\004B\007\n\005value\"6\n\014IntConfigD"
  "ef\022\013\n\003min\030\001 \001(\003\022\013\n\003max\030\002 \001(\003\022\014\n\004step\030\003 \001"
  "(\003\"7\n\rUIntConfigDef\022\013\n\003min\030\001 \001(\004\022\013\n\003max\030"
  "\002 \001(\004\022\014\n\004step\030\003 \001(\004\"8\n\016FloatConfigDef\022\013\n"
  "\003min\030\001 \001(\001\022\013\n\003max\030\002 \001(\001\022\014\n\004step\030\003 \001(\001\"6\n"
  "\017StringConfigDef\022\022\n\nsize_limit\030\001 \001(\r\022\017\n\007"
  "choices\030\003 \003(\t\"\234\003\n\tConfigDef\022-\n\004type\030\001 \001("
  "\0162\037.carbon.config.proto.ConfigType\0229\n\nco"
  "mplexity\030\002 \001(\0162%.carbon.config.proto.Con"
  "figComplexity\0224\n\007int_def\030\003 \001(\0132!.carbon."
  "config.proto.IntConfigDefH\000\0226\n\010uint_def\030"
  "\004 \001(\0132\".carbon.config.proto.UIntConfigDe"
  "fH\000\0228\n\tfloat_def\030\005 \001(\0132#.carbon.config.p"
  "roto.FloatConfigDefH\000\022:\n\nstring_def\030\006 \001("
  "\0132$.carbon.config.proto.StringConfigDefH"
  "\000\022\014\n\004hint\030\007 \001(\t\022\033\n\023default_recommended\030\010"
  " \001(\010\022\r\n\005units\030\t \001(\tB\007\n\005extra\"\253\001\n\nConfigN"
  "ode\022\014\n\004name\030\001 \001(\t\022/\n\005value\030\002 \001(\0132 .carbo"
  "n.config.proto.ConfigValue\022+\n\003def\030\003 \001(\0132"
  "\036.carbon.config.proto.ConfigDef\0221\n\010child"
  "ren\030\004 \003(\0132\037.carbon.config.proto.ConfigNo"
  "de\"J\n\nConfigLeaf\022\013\n\003key\030\001 \001(\t\022/\n\005value\030\002"
  " \001(\0132 .carbon.config.proto.ConfigValue\"O"
  "\n\017SetValueRequest\022\013\n\003key\030\001 \001(\t\022/\n\005value\030"
  "\002 \001(\0132 .carbon.config.proto.ConfigValue\""
  "\022\n\020SetValueResponse\"\035\n\016GetTreeRequest\022\013\n"
  "\003key\030\001 \001(\t\"@\n\017GetTreeResponse\022-\n\004node\030\001 "
  "\001(\0132\037.carbon.config.proto.ConfigNode\"L\n\016"
  "SetTreeRequest\022\013\n\003key\030\001 \001(\t\022-\n\004node\030\002 \001("
  "\0132\037.carbon.config.proto.ConfigNode\"\021\n\017Se"
  "tTreeResponse\"\037\n\020GetLeavesRequest\022\013\n\003key"
  "\030\001 \001(\t\"D\n\021GetLeavesResponse\022/\n\006leaves\030\001 "
  "\003(\0132\037.carbon.config.proto.ConfigLeaf\"-\n\020"
  "AddToListRequest\022\013\n\003key\030\001 \001(\t\022\014\n\004name\030\002 "
  "\001(\t\"\023\n\021AddToListResponse\"2\n\025RemoveFromLi"
  "stRequest\022\013\n\003key\030\001 \001(\t\022\014\n\004name\030\002 \001(\t\"\030\n\026"
  "RemoveFromListResponse\"#\n\023SubscriptionRe"
  "quest\022\014\n\004keys\030\001 \003(\t\"I\n\031SubscriptionNotif"
  "yMessage\022\030\n\020subscription_key\030\001 \001(\t\022\022\n\nno"
  "tify_key\030\002 \001(\t\"V\n\031UpgradeCloudConfigRequ"
  "est\022\r\n\005robot\030\001 \001(\t\022\023\n\013to_template\030\002 \001(\t\022"
  "\025\n\rfrom_template\030\003 \001(\t\"\034\n\032UpgradeCloudCo"
  "nfigResponse*T\n\nConfigType\022\010\n\004NODE\020\000\022\010\n\004"
  "LIST\020\001\022\n\n\006STRING\020\002\022\007\n\003INT\020\003\022\010\n\004UINT\020\004\022\t\n"
  "\005FLOAT\020\005\022\010\n\004BOOL\020\006*E\n\020ConfigComplexity\022\010"
  "\n\004USER\020\000\022\014\n\010ADVANCED\020\001\022\n\n\006EXPERT\020\002\022\r\n\tDE"
  "VELOPER\020\0032\373\005\n\rConfigService\022K\n\004Ping\022 .ca"
  "rbon.config.proto.PingRequest\032!.carbon.c"
  "onfig.proto.PongResponse\022W\n\010SetValue\022$.c"
  "arbon.config.proto.SetValueRequest\032%.car"
  "bon.config.proto.SetValueResponse\022T\n\007Get"
  "Tree\022#.carbon.config.proto.GetTreeReques"
  "t\032$.carbon.config.proto.GetTreeResponse\022"
  "T\n\007SetTree\022#.carbon.config.proto.SetTree"
  "Request\032$.carbon.config.proto.SetTreeRes"
  "ponse\022Z\n\tGetLeaves\022%.carbon.config.proto"
  ".GetLeavesRequest\032&.carbon.config.proto."
  "GetLeavesResponse\022Z\n\tAddToList\022%.carbon."
  "config.proto.AddToListRequest\032&.carbon.c"
  "onfig.proto.AddToListResponse\022i\n\016RemoveF"
  "romList\022*.carbon.config.proto.RemoveFrom"
  "ListRequest\032+.carbon.config.proto.Remove"
  "FromListResponse\022u\n\022UpgradeCloudConfig\022."
  ".carbon.config.proto.UpgradeCloudConfigR"
  "equest\032/.carbon.config.proto.UpgradeClou"
  "dConfigResponse2\204\001\n\031ConfigNotificationSe"
  "rvice\022g\n\tSubscribe\022(.carbon.config.proto"
  ".SubscriptionRequest\032..carbon.config.pro"
  "to.SubscriptionNotifyMessage0\001B\026Z\024proto/"
  "config_serviceb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto = {
  false, false, 3022, descriptor_table_protodef_config_2fapi_2fproto_2fconfig_5fservice_2eproto, "config/api/proto/config_service.proto", 
  &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once, nullptr, 0, 26,
  schemas, file_default_instances, TableStruct_config_2fapi_2fproto_2fconfig_5fservice_2eproto::offsets,
  file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto, file_level_enum_descriptors_config_2fapi_2fproto_2fconfig_5fservice_2eproto, file_level_service_descriptors_config_2fapi_2fproto_2fconfig_5fservice_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter() {
  return &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_config_2fapi_2fproto_2fconfig_5fservice_2eproto(&descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto);
namespace carbon {
namespace config {
namespace proto {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConfigType_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto);
  return file_level_enum_descriptors_config_2fapi_2fproto_2fconfig_5fservice_2eproto[0];
}
bool ConfigType_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
    case 6:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* ConfigComplexity_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto);
  return file_level_enum_descriptors_config_2fapi_2fproto_2fconfig_5fservice_2eproto[1];
}
bool ConfigComplexity_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}


// ===================================================================

class PingRequest::_Internal {
 public:
};

PingRequest::PingRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.PingRequest)
}
PingRequest::PingRequest(const PingRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  x_ = from.x_;
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.PingRequest)
}

inline void PingRequest::SharedCtor() {
x_ = 0;
}

PingRequest::~PingRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.PingRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PingRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PingRequest::ArenaDtor(void* object) {
  PingRequest* _this = reinterpret_cast< PingRequest* >(object);
  (void)_this;
}
void PingRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PingRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PingRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.PingRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  x_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PingRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PingRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.PingRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_x(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.PingRequest)
  return target;
}

size_t PingRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.PingRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_x());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PingRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PingRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PingRequest::GetClassData() const { return &_class_data_; }

void PingRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PingRequest *>(to)->MergeFrom(
      static_cast<const PingRequest &>(from));
}


void PingRequest::MergeFrom(const PingRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.PingRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _internal_set_x(from._internal_x());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PingRequest::CopyFrom(const PingRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.PingRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PingRequest::IsInitialized() const {
  return true;
}

void PingRequest::InternalSwap(PingRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(x_, other->x_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PingRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[0]);
}

// ===================================================================

class PongResponse::_Internal {
 public:
};

PongResponse::PongResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.PongResponse)
}
PongResponse::PongResponse(const PongResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  x_ = from.x_;
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.PongResponse)
}

inline void PongResponse::SharedCtor() {
x_ = 0;
}

PongResponse::~PongResponse() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.PongResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void PongResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void PongResponse::ArenaDtor(void* object) {
  PongResponse* _this = reinterpret_cast< PongResponse* >(object);
  (void)_this;
}
void PongResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void PongResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void PongResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.PongResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  x_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* PongResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* PongResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.PongResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 x = 1;
  if (this->_internal_x() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_x(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.PongResponse)
  return target;
}

size_t PongResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.PongResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 x = 1;
  if (this->_internal_x() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_x());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData PongResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    PongResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*PongResponse::GetClassData() const { return &_class_data_; }

void PongResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<PongResponse *>(to)->MergeFrom(
      static_cast<const PongResponse &>(from));
}


void PongResponse::MergeFrom(const PongResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.PongResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_x() != 0) {
    _internal_set_x(from._internal_x());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void PongResponse::CopyFrom(const PongResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.PongResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool PongResponse::IsInitialized() const {
  return true;
}

void PongResponse::InternalSwap(PongResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(x_, other->x_);
}

::PROTOBUF_NAMESPACE_ID::Metadata PongResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[1]);
}

// ===================================================================

class ConfigValue::_Internal {
 public:
};

ConfigValue::ConfigValue(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.ConfigValue)
}
ConfigValue::ConfigValue(const ConfigValue& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  timestamp_ms_ = from.timestamp_ms_;
  clear_has_value();
  switch (from.value_case()) {
    case kInt64Val: {
      _internal_set_int64_val(from._internal_int64_val());
      break;
    }
    case kUint64Val: {
      _internal_set_uint64_val(from._internal_uint64_val());
      break;
    }
    case kBoolVal: {
      _internal_set_bool_val(from._internal_bool_val());
      break;
    }
    case kFloatVal: {
      _internal_set_float_val(from._internal_float_val());
      break;
    }
    case kStringVal: {
      _internal_set_string_val(from._internal_string_val());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.ConfigValue)
}

inline void ConfigValue::SharedCtor() {
timestamp_ms_ = uint64_t{0u};
clear_has_value();
}

ConfigValue::~ConfigValue() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.ConfigValue)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConfigValue::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (has_value()) {
    clear_value();
  }
}

void ConfigValue::ArenaDtor(void* object) {
  ConfigValue* _this = reinterpret_cast< ConfigValue* >(object);
  (void)_this;
}
void ConfigValue::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConfigValue::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConfigValue::clear_value() {
// @@protoc_insertion_point(one_of_clear_start:carbon.config.proto.ConfigValue)
  switch (value_case()) {
    case kInt64Val: {
      // No need to clear
      break;
    }
    case kUint64Val: {
      // No need to clear
      break;
    }
    case kBoolVal: {
      // No need to clear
      break;
    }
    case kFloatVal: {
      // No need to clear
      break;
    }
    case kStringVal: {
      value_.string_val_.Destroy(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = VALUE_NOT_SET;
}


void ConfigValue::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.ConfigValue)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  timestamp_ms_ = uint64_t{0u};
  clear_value();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigValue::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 int64_val = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          _internal_set_int64_val(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 uint64_val = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          _internal_set_uint64_val(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool bool_val = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          _internal_set_bool_val(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // double float_val = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 33)) {
          _internal_set_float_val(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // string string_val = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_string_val();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.ConfigValue.string_val"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 timestamp_ms = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigValue::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.ConfigValue)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 int64_val = 1;
  if (_internal_has_int64_val()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_int64_val(), target);
  }

  // uint64 uint64_val = 2;
  if (_internal_has_uint64_val()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_uint64_val(), target);
  }

  // bool bool_val = 3;
  if (_internal_has_bool_val()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(3, this->_internal_bool_val(), target);
  }

  // double float_val = 4;
  if (_internal_has_float_val()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(4, this->_internal_float_val(), target);
  }

  // string string_val = 5;
  if (_internal_has_string_val()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_string_val().data(), static_cast<int>(this->_internal_string_val().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.ConfigValue.string_val");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_string_val(), target);
  }

  // uint64 timestamp_ms = 6;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(6, this->_internal_timestamp_ms(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.ConfigValue)
  return target;
}

size_t ConfigValue::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.ConfigValue)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 timestamp_ms = 6;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_timestamp_ms());
  }

  switch (value_case()) {
    // int64 int64_val = 1;
    case kInt64Val: {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_int64_val());
      break;
    }
    // uint64 uint64_val = 2;
    case kUint64Val: {
      total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_uint64_val());
      break;
    }
    // bool bool_val = 3;
    case kBoolVal: {
      total_size += 1 + 1;
      break;
    }
    // double float_val = 4;
    case kFloatVal: {
      total_size += 1 + 8;
      break;
    }
    // string string_val = 5;
    case kStringVal: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
          this->_internal_string_val());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigValue::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConfigValue::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigValue::GetClassData() const { return &_class_data_; }

void ConfigValue::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ConfigValue *>(to)->MergeFrom(
      static_cast<const ConfigValue &>(from));
}


void ConfigValue::MergeFrom(const ConfigValue& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.ConfigValue)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  switch (from.value_case()) {
    case kInt64Val: {
      _internal_set_int64_val(from._internal_int64_val());
      break;
    }
    case kUint64Val: {
      _internal_set_uint64_val(from._internal_uint64_val());
      break;
    }
    case kBoolVal: {
      _internal_set_bool_val(from._internal_bool_val());
      break;
    }
    case kFloatVal: {
      _internal_set_float_val(from._internal_float_val());
      break;
    }
    case kStringVal: {
      _internal_set_string_val(from._internal_string_val());
      break;
    }
    case VALUE_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigValue::CopyFrom(const ConfigValue& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.ConfigValue)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigValue::IsInitialized() const {
  return true;
}

void ConfigValue::InternalSwap(ConfigValue* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(timestamp_ms_, other->timestamp_ms_);
  swap(value_, other->value_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigValue::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[2]);
}

// ===================================================================

class IntConfigDef::_Internal {
 public:
};

IntConfigDef::IntConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.IntConfigDef)
}
IntConfigDef::IntConfigDef(const IntConfigDef& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&min_, &from.min_,
    static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&min_)) + sizeof(step_));
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.IntConfigDef)
}

inline void IntConfigDef::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&min_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&min_)) + sizeof(step_));
}

IntConfigDef::~IntConfigDef() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.IntConfigDef)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void IntConfigDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void IntConfigDef::ArenaDtor(void* object) {
  IntConfigDef* _this = reinterpret_cast< IntConfigDef* >(object);
  (void)_this;
}
void IntConfigDef::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void IntConfigDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void IntConfigDef::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.IntConfigDef)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&min_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&step_) -
      reinterpret_cast<char*>(&min_)) + sizeof(step_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* IntConfigDef::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 min = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          min_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 max = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          max_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 step = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          step_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* IntConfigDef::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.IntConfigDef)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 min = 1;
  if (this->_internal_min() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_min(), target);
  }

  // int64 max = 2;
  if (this->_internal_max() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_max(), target);
  }

  // int64 step = 3;
  if (this->_internal_step() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(3, this->_internal_step(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.IntConfigDef)
  return target;
}

size_t IntConfigDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.IntConfigDef)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int64 min = 1;
  if (this->_internal_min() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_min());
  }

  // int64 max = 2;
  if (this->_internal_max() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_max());
  }

  // int64 step = 3;
  if (this->_internal_step() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_step());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData IntConfigDef::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    IntConfigDef::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*IntConfigDef::GetClassData() const { return &_class_data_; }

void IntConfigDef::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<IntConfigDef *>(to)->MergeFrom(
      static_cast<const IntConfigDef &>(from));
}


void IntConfigDef::MergeFrom(const IntConfigDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.IntConfigDef)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_min() != 0) {
    _internal_set_min(from._internal_min());
  }
  if (from._internal_max() != 0) {
    _internal_set_max(from._internal_max());
  }
  if (from._internal_step() != 0) {
    _internal_set_step(from._internal_step());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void IntConfigDef::CopyFrom(const IntConfigDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.IntConfigDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool IntConfigDef::IsInitialized() const {
  return true;
}

void IntConfigDef::InternalSwap(IntConfigDef* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(IntConfigDef, step_)
      + sizeof(IntConfigDef::step_)
      - PROTOBUF_FIELD_OFFSET(IntConfigDef, min_)>(
          reinterpret_cast<char*>(&min_),
          reinterpret_cast<char*>(&other->min_));
}

::PROTOBUF_NAMESPACE_ID::Metadata IntConfigDef::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[3]);
}

// ===================================================================

class UIntConfigDef::_Internal {
 public:
};

UIntConfigDef::UIntConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.UIntConfigDef)
}
UIntConfigDef::UIntConfigDef(const UIntConfigDef& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&min_, &from.min_,
    static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&min_)) + sizeof(step_));
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.UIntConfigDef)
}

inline void UIntConfigDef::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&min_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&min_)) + sizeof(step_));
}

UIntConfigDef::~UIntConfigDef() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.UIntConfigDef)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UIntConfigDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void UIntConfigDef::ArenaDtor(void* object) {
  UIntConfigDef* _this = reinterpret_cast< UIntConfigDef* >(object);
  (void)_this;
}
void UIntConfigDef::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UIntConfigDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UIntConfigDef::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.UIntConfigDef)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&min_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&step_) -
      reinterpret_cast<char*>(&min_)) + sizeof(step_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UIntConfigDef::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 min = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          min_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 max = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          max_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // uint64 step = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          step_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UIntConfigDef::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.UIntConfigDef)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 min = 1;
  if (this->_internal_min() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_min(), target);
  }

  // uint64 max = 2;
  if (this->_internal_max() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(2, this->_internal_max(), target);
  }

  // uint64 step = 3;
  if (this->_internal_step() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(3, this->_internal_step(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.UIntConfigDef)
  return target;
}

size_t UIntConfigDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.UIntConfigDef)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 min = 1;
  if (this->_internal_min() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_min());
  }

  // uint64 max = 2;
  if (this->_internal_max() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_max());
  }

  // uint64 step = 3;
  if (this->_internal_step() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_step());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UIntConfigDef::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UIntConfigDef::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UIntConfigDef::GetClassData() const { return &_class_data_; }

void UIntConfigDef::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UIntConfigDef *>(to)->MergeFrom(
      static_cast<const UIntConfigDef &>(from));
}


void UIntConfigDef::MergeFrom(const UIntConfigDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.UIntConfigDef)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_min() != 0) {
    _internal_set_min(from._internal_min());
  }
  if (from._internal_max() != 0) {
    _internal_set_max(from._internal_max());
  }
  if (from._internal_step() != 0) {
    _internal_set_step(from._internal_step());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UIntConfigDef::CopyFrom(const UIntConfigDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.UIntConfigDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UIntConfigDef::IsInitialized() const {
  return true;
}

void UIntConfigDef::InternalSwap(UIntConfigDef* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(UIntConfigDef, step_)
      + sizeof(UIntConfigDef::step_)
      - PROTOBUF_FIELD_OFFSET(UIntConfigDef, min_)>(
          reinterpret_cast<char*>(&min_),
          reinterpret_cast<char*>(&other->min_));
}

::PROTOBUF_NAMESPACE_ID::Metadata UIntConfigDef::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[4]);
}

// ===================================================================

class FloatConfigDef::_Internal {
 public:
};

FloatConfigDef::FloatConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.FloatConfigDef)
}
FloatConfigDef::FloatConfigDef(const FloatConfigDef& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&min_, &from.min_,
    static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&min_)) + sizeof(step_));
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.FloatConfigDef)
}

inline void FloatConfigDef::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&min_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&step_) -
    reinterpret_cast<char*>(&min_)) + sizeof(step_));
}

FloatConfigDef::~FloatConfigDef() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.FloatConfigDef)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void FloatConfigDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void FloatConfigDef::ArenaDtor(void* object) {
  FloatConfigDef* _this = reinterpret_cast< FloatConfigDef* >(object);
  (void)_this;
}
void FloatConfigDef::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void FloatConfigDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void FloatConfigDef::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.FloatConfigDef)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&min_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&step_) -
      reinterpret_cast<char*>(&min_)) + sizeof(step_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* FloatConfigDef::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // double min = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 9)) {
          min_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double max = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 17)) {
          max_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // double step = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 25)) {
          step_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr);
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* FloatConfigDef::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.FloatConfigDef)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // double min = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_min = this->_internal_min();
  uint64_t raw_min;
  memcpy(&raw_min, &tmp_min, sizeof(tmp_min));
  if (raw_min != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(1, this->_internal_min(), target);
  }

  // double max = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_max = this->_internal_max();
  uint64_t raw_max;
  memcpy(&raw_max, &tmp_max, sizeof(tmp_max));
  if (raw_max != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(2, this->_internal_max(), target);
  }

  // double step = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_step = this->_internal_step();
  uint64_t raw_step;
  memcpy(&raw_step, &tmp_step, sizeof(tmp_step));
  if (raw_step != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteDoubleToArray(3, this->_internal_step(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.FloatConfigDef)
  return target;
}

size_t FloatConfigDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.FloatConfigDef)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // double min = 1;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_min = this->_internal_min();
  uint64_t raw_min;
  memcpy(&raw_min, &tmp_min, sizeof(tmp_min));
  if (raw_min != 0) {
    total_size += 1 + 8;
  }

  // double max = 2;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_max = this->_internal_max();
  uint64_t raw_max;
  memcpy(&raw_max, &tmp_max, sizeof(tmp_max));
  if (raw_max != 0) {
    total_size += 1 + 8;
  }

  // double step = 3;
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_step = this->_internal_step();
  uint64_t raw_step;
  memcpy(&raw_step, &tmp_step, sizeof(tmp_step));
  if (raw_step != 0) {
    total_size += 1 + 8;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData FloatConfigDef::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    FloatConfigDef::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*FloatConfigDef::GetClassData() const { return &_class_data_; }

void FloatConfigDef::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<FloatConfigDef *>(to)->MergeFrom(
      static_cast<const FloatConfigDef &>(from));
}


void FloatConfigDef::MergeFrom(const FloatConfigDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.FloatConfigDef)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_min = from._internal_min();
  uint64_t raw_min;
  memcpy(&raw_min, &tmp_min, sizeof(tmp_min));
  if (raw_min != 0) {
    _internal_set_min(from._internal_min());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_max = from._internal_max();
  uint64_t raw_max;
  memcpy(&raw_max, &tmp_max, sizeof(tmp_max));
  if (raw_max != 0) {
    _internal_set_max(from._internal_max());
  }
  static_assert(sizeof(uint64_t) == sizeof(double), "Code assumes uint64_t and double are the same size.");
  double tmp_step = from._internal_step();
  uint64_t raw_step;
  memcpy(&raw_step, &tmp_step, sizeof(tmp_step));
  if (raw_step != 0) {
    _internal_set_step(from._internal_step());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void FloatConfigDef::CopyFrom(const FloatConfigDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.FloatConfigDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool FloatConfigDef::IsInitialized() const {
  return true;
}

void FloatConfigDef::InternalSwap(FloatConfigDef* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(FloatConfigDef, step_)
      + sizeof(FloatConfigDef::step_)
      - PROTOBUF_FIELD_OFFSET(FloatConfigDef, min_)>(
          reinterpret_cast<char*>(&min_),
          reinterpret_cast<char*>(&other->min_));
}

::PROTOBUF_NAMESPACE_ID::Metadata FloatConfigDef::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[5]);
}

// ===================================================================

class StringConfigDef::_Internal {
 public:
};

StringConfigDef::StringConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  choices_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.StringConfigDef)
}
StringConfigDef::StringConfigDef(const StringConfigDef& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      choices_(from.choices_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  size_limit_ = from.size_limit_;
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.StringConfigDef)
}

inline void StringConfigDef::SharedCtor() {
size_limit_ = 0u;
}

StringConfigDef::~StringConfigDef() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.StringConfigDef)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void StringConfigDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void StringConfigDef::ArenaDtor(void* object) {
  StringConfigDef* _this = reinterpret_cast< StringConfigDef* >(object);
  (void)_this;
}
void StringConfigDef::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void StringConfigDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void StringConfigDef::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.StringConfigDef)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  choices_.Clear();
  size_limit_ = 0u;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* StringConfigDef::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint32 size_limit = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          size_limit_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string choices = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_choices();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.StringConfigDef.choices"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* StringConfigDef::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.StringConfigDef)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint32 size_limit = 1;
  if (this->_internal_size_limit() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(1, this->_internal_size_limit(), target);
  }

  // repeated string choices = 3;
  for (int i = 0, n = this->_internal_choices_size(); i < n; i++) {
    const auto& s = this->_internal_choices(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.StringConfigDef.choices");
    target = stream->WriteString(3, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.StringConfigDef)
  return target;
}

size_t StringConfigDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.StringConfigDef)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string choices = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(choices_.size());
  for (int i = 0, n = choices_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      choices_.Get(i));
  }

  // uint32 size_limit = 1;
  if (this->_internal_size_limit() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_size_limit());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData StringConfigDef::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    StringConfigDef::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*StringConfigDef::GetClassData() const { return &_class_data_; }

void StringConfigDef::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<StringConfigDef *>(to)->MergeFrom(
      static_cast<const StringConfigDef &>(from));
}


void StringConfigDef::MergeFrom(const StringConfigDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.StringConfigDef)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  choices_.MergeFrom(from.choices_);
  if (from._internal_size_limit() != 0) {
    _internal_set_size_limit(from._internal_size_limit());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void StringConfigDef::CopyFrom(const StringConfigDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.StringConfigDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool StringConfigDef::IsInitialized() const {
  return true;
}

void StringConfigDef::InternalSwap(StringConfigDef* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  choices_.InternalSwap(&other->choices_);
  swap(size_limit_, other->size_limit_);
}

::PROTOBUF_NAMESPACE_ID::Metadata StringConfigDef::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[6]);
}

// ===================================================================

class ConfigDef::_Internal {
 public:
  static const ::carbon::config::proto::IntConfigDef& int_def(const ConfigDef* msg);
  static const ::carbon::config::proto::UIntConfigDef& uint_def(const ConfigDef* msg);
  static const ::carbon::config::proto::FloatConfigDef& float_def(const ConfigDef* msg);
  static const ::carbon::config::proto::StringConfigDef& string_def(const ConfigDef* msg);
};

const ::carbon::config::proto::IntConfigDef&
ConfigDef::_Internal::int_def(const ConfigDef* msg) {
  return *msg->extra_.int_def_;
}
const ::carbon::config::proto::UIntConfigDef&
ConfigDef::_Internal::uint_def(const ConfigDef* msg) {
  return *msg->extra_.uint_def_;
}
const ::carbon::config::proto::FloatConfigDef&
ConfigDef::_Internal::float_def(const ConfigDef* msg) {
  return *msg->extra_.float_def_;
}
const ::carbon::config::proto::StringConfigDef&
ConfigDef::_Internal::string_def(const ConfigDef* msg) {
  return *msg->extra_.string_def_;
}
void ConfigDef::set_allocated_int_def(::carbon::config::proto::IntConfigDef* int_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_extra();
  if (int_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::IntConfigDef>::GetOwningArena(int_def);
    if (message_arena != submessage_arena) {
      int_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, int_def, submessage_arena);
    }
    set_has_int_def();
    extra_.int_def_ = int_def;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigDef.int_def)
}
void ConfigDef::set_allocated_uint_def(::carbon::config::proto::UIntConfigDef* uint_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_extra();
  if (uint_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::UIntConfigDef>::GetOwningArena(uint_def);
    if (message_arena != submessage_arena) {
      uint_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, uint_def, submessage_arena);
    }
    set_has_uint_def();
    extra_.uint_def_ = uint_def;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigDef.uint_def)
}
void ConfigDef::set_allocated_float_def(::carbon::config::proto::FloatConfigDef* float_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_extra();
  if (float_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::FloatConfigDef>::GetOwningArena(float_def);
    if (message_arena != submessage_arena) {
      float_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, float_def, submessage_arena);
    }
    set_has_float_def();
    extra_.float_def_ = float_def;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigDef.float_def)
}
void ConfigDef::set_allocated_string_def(::carbon::config::proto::StringConfigDef* string_def) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  clear_extra();
  if (string_def) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
      ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::carbon::config::proto::StringConfigDef>::GetOwningArena(string_def);
    if (message_arena != submessage_arena) {
      string_def = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, string_def, submessage_arena);
    }
    set_has_string_def();
    extra_.string_def_ = string_def;
  }
  // @@protoc_insertion_point(field_set_allocated:carbon.config.proto.ConfigDef.string_def)
}
ConfigDef::ConfigDef(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.ConfigDef)
}
ConfigDef::ConfigDef(const ConfigDef& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  hint_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    hint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_hint().empty()) {
    hint_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_hint(), 
      GetArenaForAllocation());
  }
  units_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    units_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_units().empty()) {
    units_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_units(), 
      GetArenaForAllocation());
  }
  ::memcpy(&type_, &from.type_,
    static_cast<size_t>(reinterpret_cast<char*>(&default_recommended_) -
    reinterpret_cast<char*>(&type_)) + sizeof(default_recommended_));
  clear_has_extra();
  switch (from.extra_case()) {
    case kIntDef: {
      _internal_mutable_int_def()->::carbon::config::proto::IntConfigDef::MergeFrom(from._internal_int_def());
      break;
    }
    case kUintDef: {
      _internal_mutable_uint_def()->::carbon::config::proto::UIntConfigDef::MergeFrom(from._internal_uint_def());
      break;
    }
    case kFloatDef: {
      _internal_mutable_float_def()->::carbon::config::proto::FloatConfigDef::MergeFrom(from._internal_float_def());
      break;
    }
    case kStringDef: {
      _internal_mutable_string_def()->::carbon::config::proto::StringConfigDef::MergeFrom(from._internal_string_def());
      break;
    }
    case EXTRA_NOT_SET: {
      break;
    }
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.ConfigDef)
}

inline void ConfigDef::SharedCtor() {
hint_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  hint_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
units_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  units_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&type_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&default_recommended_) -
    reinterpret_cast<char*>(&type_)) + sizeof(default_recommended_));
clear_has_extra();
}

ConfigDef::~ConfigDef() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.ConfigDef)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConfigDef::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  hint_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  units_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (has_extra()) {
    clear_extra();
  }
}

void ConfigDef::ArenaDtor(void* object) {
  ConfigDef* _this = reinterpret_cast< ConfigDef* >(object);
  (void)_this;
}
void ConfigDef::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConfigDef::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConfigDef::clear_extra() {
// @@protoc_insertion_point(one_of_clear_start:carbon.config.proto.ConfigDef)
  switch (extra_case()) {
    case kIntDef: {
      if (GetArenaForAllocation() == nullptr) {
        delete extra_.int_def_;
      }
      break;
    }
    case kUintDef: {
      if (GetArenaForAllocation() == nullptr) {
        delete extra_.uint_def_;
      }
      break;
    }
    case kFloatDef: {
      if (GetArenaForAllocation() == nullptr) {
        delete extra_.float_def_;
      }
      break;
    }
    case kStringDef: {
      if (GetArenaForAllocation() == nullptr) {
        delete extra_.string_def_;
      }
      break;
    }
    case EXTRA_NOT_SET: {
      break;
    }
  }
  _oneof_case_[0] = EXTRA_NOT_SET;
}


void ConfigDef::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.ConfigDef)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  hint_.ClearToEmpty();
  units_.ClearToEmpty();
  ::memset(&type_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&default_recommended_) -
      reinterpret_cast<char*>(&type_)) + sizeof(default_recommended_));
  clear_extra();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigDef::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.config.proto.ConfigType type = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_type(static_cast<::carbon::config::proto::ConfigType>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.ConfigComplexity complexity = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_complexity(static_cast<::carbon::config::proto::ConfigComplexity>(val));
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.IntConfigDef int_def = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_int_def(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.UIntConfigDef uint_def = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ctx->ParseMessage(_internal_mutable_uint_def(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.FloatConfigDef float_def = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ctx->ParseMessage(_internal_mutable_float_def(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.StringConfigDef string_def = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ctx->ParseMessage(_internal_mutable_string_def(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string hint = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          auto str = _internal_mutable_hint();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.ConfigDef.hint"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool default_recommended = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          default_recommended_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string units = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 74)) {
          auto str = _internal_mutable_units();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.ConfigDef.units"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigDef::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.ConfigDef)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.config.proto.ConfigType type = 1;
  if (this->_internal_type() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      1, this->_internal_type(), target);
  }

  // .carbon.config.proto.ConfigComplexity complexity = 2;
  if (this->_internal_complexity() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      2, this->_internal_complexity(), target);
  }

  // .carbon.config.proto.IntConfigDef int_def = 3;
  if (_internal_has_int_def()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::int_def(this), target, stream);
  }

  // .carbon.config.proto.UIntConfigDef uint_def = 4;
  if (_internal_has_uint_def()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        4, _Internal::uint_def(this), target, stream);
  }

  // .carbon.config.proto.FloatConfigDef float_def = 5;
  if (_internal_has_float_def()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        5, _Internal::float_def(this), target, stream);
  }

  // .carbon.config.proto.StringConfigDef string_def = 6;
  if (_internal_has_string_def()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        6, _Internal::string_def(this), target, stream);
  }

  // string hint = 7;
  if (!this->_internal_hint().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_hint().data(), static_cast<int>(this->_internal_hint().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.ConfigDef.hint");
    target = stream->WriteStringMaybeAliased(
        7, this->_internal_hint(), target);
  }

  // bool default_recommended = 8;
  if (this->_internal_default_recommended() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_default_recommended(), target);
  }

  // string units = 9;
  if (!this->_internal_units().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_units().data(), static_cast<int>(this->_internal_units().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.ConfigDef.units");
    target = stream->WriteStringMaybeAliased(
        9, this->_internal_units(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.ConfigDef)
  return target;
}

size_t ConfigDef::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.ConfigDef)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string hint = 7;
  if (!this->_internal_hint().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_hint());
  }

  // string units = 9;
  if (!this->_internal_units().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_units());
  }

  // .carbon.config.proto.ConfigType type = 1;
  if (this->_internal_type() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_type());
  }

  // .carbon.config.proto.ConfigComplexity complexity = 2;
  if (this->_internal_complexity() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_complexity());
  }

  // bool default_recommended = 8;
  if (this->_internal_default_recommended() != 0) {
    total_size += 1 + 1;
  }

  switch (extra_case()) {
    // .carbon.config.proto.IntConfigDef int_def = 3;
    case kIntDef: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *extra_.int_def_);
      break;
    }
    // .carbon.config.proto.UIntConfigDef uint_def = 4;
    case kUintDef: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *extra_.uint_def_);
      break;
    }
    // .carbon.config.proto.FloatConfigDef float_def = 5;
    case kFloatDef: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *extra_.float_def_);
      break;
    }
    // .carbon.config.proto.StringConfigDef string_def = 6;
    case kStringDef: {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
          *extra_.string_def_);
      break;
    }
    case EXTRA_NOT_SET: {
      break;
    }
  }
  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigDef::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConfigDef::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigDef::GetClassData() const { return &_class_data_; }

void ConfigDef::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ConfigDef *>(to)->MergeFrom(
      static_cast<const ConfigDef &>(from));
}


void ConfigDef::MergeFrom(const ConfigDef& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.ConfigDef)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_hint().empty()) {
    _internal_set_hint(from._internal_hint());
  }
  if (!from._internal_units().empty()) {
    _internal_set_units(from._internal_units());
  }
  if (from._internal_type() != 0) {
    _internal_set_type(from._internal_type());
  }
  if (from._internal_complexity() != 0) {
    _internal_set_complexity(from._internal_complexity());
  }
  if (from._internal_default_recommended() != 0) {
    _internal_set_default_recommended(from._internal_default_recommended());
  }
  switch (from.extra_case()) {
    case kIntDef: {
      _internal_mutable_int_def()->::carbon::config::proto::IntConfigDef::MergeFrom(from._internal_int_def());
      break;
    }
    case kUintDef: {
      _internal_mutable_uint_def()->::carbon::config::proto::UIntConfigDef::MergeFrom(from._internal_uint_def());
      break;
    }
    case kFloatDef: {
      _internal_mutable_float_def()->::carbon::config::proto::FloatConfigDef::MergeFrom(from._internal_float_def());
      break;
    }
    case kStringDef: {
      _internal_mutable_string_def()->::carbon::config::proto::StringConfigDef::MergeFrom(from._internal_string_def());
      break;
    }
    case EXTRA_NOT_SET: {
      break;
    }
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigDef::CopyFrom(const ConfigDef& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.ConfigDef)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigDef::IsInitialized() const {
  return true;
}

void ConfigDef::InternalSwap(ConfigDef* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &hint_, lhs_arena,
      &other->hint_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &units_, lhs_arena,
      &other->units_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ConfigDef, default_recommended_)
      + sizeof(ConfigDef::default_recommended_)
      - PROTOBUF_FIELD_OFFSET(ConfigDef, type_)>(
          reinterpret_cast<char*>(&type_),
          reinterpret_cast<char*>(&other->type_));
  swap(extra_, other->extra_);
  swap(_oneof_case_[0], other->_oneof_case_[0]);
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigDef::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[7]);
}

// ===================================================================

class ConfigNode::_Internal {
 public:
  static const ::carbon::config::proto::ConfigValue& value(const ConfigNode* msg);
  static const ::carbon::config::proto::ConfigDef& def(const ConfigNode* msg);
};

const ::carbon::config::proto::ConfigValue&
ConfigNode::_Internal::value(const ConfigNode* msg) {
  return *msg->value_;
}
const ::carbon::config::proto::ConfigDef&
ConfigNode::_Internal::def(const ConfigNode* msg) {
  return *msg->def_;
}
ConfigNode::ConfigNode(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  children_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.ConfigNode)
}
ConfigNode::ConfigNode(const ConfigNode& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      children_(from.children_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_value()) {
    value_ = new ::carbon::config::proto::ConfigValue(*from.value_);
  } else {
    value_ = nullptr;
  }
  if (from._internal_has_def()) {
    def_ = new ::carbon::config::proto::ConfigDef(*from.def_);
  } else {
    def_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.ConfigNode)
}

inline void ConfigNode::SharedCtor() {
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&value_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&def_) -
    reinterpret_cast<char*>(&value_)) + sizeof(def_));
}

ConfigNode::~ConfigNode() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.ConfigNode)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConfigNode::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete value_;
  if (this != internal_default_instance()) delete def_;
}

void ConfigNode::ArenaDtor(void* object) {
  ConfigNode* _this = reinterpret_cast< ConfigNode* >(object);
  (void)_this;
}
void ConfigNode::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConfigNode::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConfigNode::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.ConfigNode)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  children_.Clear();
  name_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  if (GetArenaForAllocation() == nullptr && def_ != nullptr) {
    delete def_;
  }
  def_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigNode::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string name = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.ConfigNode.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.ConfigValue value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.ConfigDef def = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ctx->ParseMessage(_internal_mutable_def(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .carbon.config.proto.ConfigNode children = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_children(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigNode::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.ConfigNode)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string name = 1;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.ConfigNode.name");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_name(), target);
  }

  // .carbon.config.proto.ConfigValue value = 2;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::value(this), target, stream);
  }

  // .carbon.config.proto.ConfigDef def = 3;
  if (this->_internal_has_def()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        3, _Internal::def(this), target, stream);
  }

  // repeated .carbon.config.proto.ConfigNode children = 4;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_children_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(4, this->_internal_children(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.ConfigNode)
  return target;
}

size_t ConfigNode::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.ConfigNode)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.config.proto.ConfigNode children = 4;
  total_size += 1UL * this->_internal_children_size();
  for (const auto& msg : this->children_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // string name = 1;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  // .carbon.config.proto.ConfigValue value = 2;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  // .carbon.config.proto.ConfigDef def = 3;
  if (this->_internal_has_def()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *def_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigNode::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConfigNode::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigNode::GetClassData() const { return &_class_data_; }

void ConfigNode::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ConfigNode *>(to)->MergeFrom(
      static_cast<const ConfigNode &>(from));
}


void ConfigNode::MergeFrom(const ConfigNode& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.ConfigNode)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  children_.MergeFrom(from.children_);
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  if (from._internal_has_value()) {
    _internal_mutable_value()->::carbon::config::proto::ConfigValue::MergeFrom(from._internal_value());
  }
  if (from._internal_has_def()) {
    _internal_mutable_def()->::carbon::config::proto::ConfigDef::MergeFrom(from._internal_def());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigNode::CopyFrom(const ConfigNode& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.ConfigNode)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigNode::IsInitialized() const {
  return true;
}

void ConfigNode::InternalSwap(ConfigNode* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  children_.InternalSwap(&other->children_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ConfigNode, def_)
      + sizeof(ConfigNode::def_)
      - PROTOBUF_FIELD_OFFSET(ConfigNode, value_)>(
          reinterpret_cast<char*>(&value_),
          reinterpret_cast<char*>(&other->value_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigNode::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[8]);
}

// ===================================================================

class ConfigLeaf::_Internal {
 public:
  static const ::carbon::config::proto::ConfigValue& value(const ConfigLeaf* msg);
};

const ::carbon::config::proto::ConfigValue&
ConfigLeaf::_Internal::value(const ConfigLeaf* msg) {
  return *msg->value_;
}
ConfigLeaf::ConfigLeaf(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.ConfigLeaf)
}
ConfigLeaf::ConfigLeaf(const ConfigLeaf& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_value()) {
    value_ = new ::carbon::config::proto::ConfigValue(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.ConfigLeaf)
}

inline void ConfigLeaf::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
value_ = nullptr;
}

ConfigLeaf::~ConfigLeaf() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.ConfigLeaf)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ConfigLeaf::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete value_;
}

void ConfigLeaf::ArenaDtor(void* object) {
  ConfigLeaf* _this = reinterpret_cast< ConfigLeaf* >(object);
  (void)_this;
}
void ConfigLeaf::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ConfigLeaf::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ConfigLeaf::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.ConfigLeaf)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ConfigLeaf::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.ConfigLeaf.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.ConfigValue value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ConfigLeaf::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.ConfigLeaf)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.ConfigLeaf.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  // .carbon.config.proto.ConfigValue value = 2;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::value(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.ConfigLeaf)
  return target;
}

size_t ConfigLeaf::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.ConfigLeaf)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  // .carbon.config.proto.ConfigValue value = 2;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ConfigLeaf::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ConfigLeaf::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ConfigLeaf::GetClassData() const { return &_class_data_; }

void ConfigLeaf::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ConfigLeaf *>(to)->MergeFrom(
      static_cast<const ConfigLeaf &>(from));
}


void ConfigLeaf::MergeFrom(const ConfigLeaf& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.ConfigLeaf)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  if (from._internal_has_value()) {
    _internal_mutable_value()->::carbon::config::proto::ConfigValue::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ConfigLeaf::CopyFrom(const ConfigLeaf& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.ConfigLeaf)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ConfigLeaf::IsInitialized() const {
  return true;
}

void ConfigLeaf::InternalSwap(ConfigLeaf* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata ConfigLeaf::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[9]);
}

// ===================================================================

class SetValueRequest::_Internal {
 public:
  static const ::carbon::config::proto::ConfigValue& value(const SetValueRequest* msg);
};

const ::carbon::config::proto::ConfigValue&
SetValueRequest::_Internal::value(const SetValueRequest* msg) {
  return *msg->value_;
}
SetValueRequest::SetValueRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.SetValueRequest)
}
SetValueRequest::SetValueRequest(const SetValueRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_value()) {
    value_ = new ::carbon::config::proto::ConfigValue(*from.value_);
  } else {
    value_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.SetValueRequest)
}

inline void SetValueRequest::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
value_ = nullptr;
}

SetValueRequest::~SetValueRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.SetValueRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetValueRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete value_;
}

void SetValueRequest::ArenaDtor(void* object) {
  SetValueRequest* _this = reinterpret_cast< SetValueRequest* >(object);
  (void)_this;
}
void SetValueRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetValueRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetValueRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.SetValueRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && value_ != nullptr) {
    delete value_;
  }
  value_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetValueRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.SetValueRequest.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.ConfigValue value = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_value(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetValueRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.SetValueRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.SetValueRequest.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  // .carbon.config.proto.ConfigValue value = 2;
  if (this->_internal_has_value()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::value(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.SetValueRequest)
  return target;
}

size_t SetValueRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.SetValueRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  // .carbon.config.proto.ConfigValue value = 2;
  if (this->_internal_has_value()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *value_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetValueRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetValueRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetValueRequest::GetClassData() const { return &_class_data_; }

void SetValueRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetValueRequest *>(to)->MergeFrom(
      static_cast<const SetValueRequest &>(from));
}


void SetValueRequest::MergeFrom(const SetValueRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.SetValueRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  if (from._internal_has_value()) {
    _internal_mutable_value()->::carbon::config::proto::ConfigValue::MergeFrom(from._internal_value());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetValueRequest::CopyFrom(const SetValueRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.SetValueRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetValueRequest::IsInitialized() const {
  return true;
}

void SetValueRequest::InternalSwap(SetValueRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
  swap(value_, other->value_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetValueRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[10]);
}

// ===================================================================

class SetValueResponse::_Internal {
 public:
};

SetValueResponse::SetValueResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.SetValueResponse)
}
SetValueResponse::SetValueResponse(const SetValueResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.SetValueResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetValueResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetValueResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetValueResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[11]);
}

// ===================================================================

class GetTreeRequest::_Internal {
 public:
};

GetTreeRequest::GetTreeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.GetTreeRequest)
}
GetTreeRequest::GetTreeRequest(const GetTreeRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.GetTreeRequest)
}

inline void GetTreeRequest::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetTreeRequest::~GetTreeRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.GetTreeRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetTreeRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetTreeRequest::ArenaDtor(void* object) {
  GetTreeRequest* _this = reinterpret_cast< GetTreeRequest* >(object);
  (void)_this;
}
void GetTreeRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetTreeRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetTreeRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.GetTreeRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetTreeRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.GetTreeRequest.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetTreeRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.GetTreeRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.GetTreeRequest.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.GetTreeRequest)
  return target;
}

size_t GetTreeRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.GetTreeRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetTreeRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetTreeRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetTreeRequest::GetClassData() const { return &_class_data_; }

void GetTreeRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetTreeRequest *>(to)->MergeFrom(
      static_cast<const GetTreeRequest &>(from));
}


void GetTreeRequest::MergeFrom(const GetTreeRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.GetTreeRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetTreeRequest::CopyFrom(const GetTreeRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.GetTreeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetTreeRequest::IsInitialized() const {
  return true;
}

void GetTreeRequest::InternalSwap(GetTreeRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetTreeRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[12]);
}

// ===================================================================

class GetTreeResponse::_Internal {
 public:
  static const ::carbon::config::proto::ConfigNode& node(const GetTreeResponse* msg);
};

const ::carbon::config::proto::ConfigNode&
GetTreeResponse::_Internal::node(const GetTreeResponse* msg) {
  return *msg->node_;
}
GetTreeResponse::GetTreeResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.GetTreeResponse)
}
GetTreeResponse::GetTreeResponse(const GetTreeResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_node()) {
    node_ = new ::carbon::config::proto::ConfigNode(*from.node_);
  } else {
    node_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.GetTreeResponse)
}

inline void GetTreeResponse::SharedCtor() {
node_ = nullptr;
}

GetTreeResponse::~GetTreeResponse() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.GetTreeResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetTreeResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete node_;
}

void GetTreeResponse::ArenaDtor(void* object) {
  GetTreeResponse* _this = reinterpret_cast< GetTreeResponse* >(object);
  (void)_this;
}
void GetTreeResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetTreeResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetTreeResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.GetTreeResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && node_ != nullptr) {
    delete node_;
  }
  node_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetTreeResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // .carbon.config.proto.ConfigNode node = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ctx->ParseMessage(_internal_mutable_node(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetTreeResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.GetTreeResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // .carbon.config.proto.ConfigNode node = 1;
  if (this->_internal_has_node()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        1, _Internal::node(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.GetTreeResponse)
  return target;
}

size_t GetTreeResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.GetTreeResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .carbon.config.proto.ConfigNode node = 1;
  if (this->_internal_has_node()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *node_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetTreeResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetTreeResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetTreeResponse::GetClassData() const { return &_class_data_; }

void GetTreeResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetTreeResponse *>(to)->MergeFrom(
      static_cast<const GetTreeResponse &>(from));
}


void GetTreeResponse::MergeFrom(const GetTreeResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.GetTreeResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_node()) {
    _internal_mutable_node()->::carbon::config::proto::ConfigNode::MergeFrom(from._internal_node());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetTreeResponse::CopyFrom(const GetTreeResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.GetTreeResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetTreeResponse::IsInitialized() const {
  return true;
}

void GetTreeResponse::InternalSwap(GetTreeResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  swap(node_, other->node_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetTreeResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[13]);
}

// ===================================================================

class SetTreeRequest::_Internal {
 public:
  static const ::carbon::config::proto::ConfigNode& node(const SetTreeRequest* msg);
};

const ::carbon::config::proto::ConfigNode&
SetTreeRequest::_Internal::node(const SetTreeRequest* msg) {
  return *msg->node_;
}
SetTreeRequest::SetTreeRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.SetTreeRequest)
}
SetTreeRequest::SetTreeRequest(const SetTreeRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_node()) {
    node_ = new ::carbon::config::proto::ConfigNode(*from.node_);
  } else {
    node_ = nullptr;
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.SetTreeRequest)
}

inline void SetTreeRequest::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
node_ = nullptr;
}

SetTreeRequest::~SetTreeRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.SetTreeRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SetTreeRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete node_;
}

void SetTreeRequest::ArenaDtor(void* object) {
  SetTreeRequest* _this = reinterpret_cast< SetTreeRequest* >(object);
  (void)_this;
}
void SetTreeRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SetTreeRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SetTreeRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.SetTreeRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && node_ != nullptr) {
    delete node_;
  }
  node_ = nullptr;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SetTreeRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.SetTreeRequest.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .carbon.config.proto.ConfigNode node = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_node(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SetTreeRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.SetTreeRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.SetTreeRequest.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  // .carbon.config.proto.ConfigNode node = 2;
  if (this->_internal_has_node()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::node(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.SetTreeRequest)
  return target;
}

size_t SetTreeRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.SetTreeRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  // .carbon.config.proto.ConfigNode node = 2;
  if (this->_internal_has_node()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *node_);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetTreeRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SetTreeRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetTreeRequest::GetClassData() const { return &_class_data_; }

void SetTreeRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SetTreeRequest *>(to)->MergeFrom(
      static_cast<const SetTreeRequest &>(from));
}


void SetTreeRequest::MergeFrom(const SetTreeRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.SetTreeRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  if (from._internal_has_node()) {
    _internal_mutable_node()->::carbon::config::proto::ConfigNode::MergeFrom(from._internal_node());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SetTreeRequest::CopyFrom(const SetTreeRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.SetTreeRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SetTreeRequest::IsInitialized() const {
  return true;
}

void SetTreeRequest::InternalSwap(SetTreeRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
  swap(node_, other->node_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SetTreeRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[14]);
}

// ===================================================================

class SetTreeResponse::_Internal {
 public:
};

SetTreeResponse::SetTreeResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.SetTreeResponse)
}
SetTreeResponse::SetTreeResponse(const SetTreeResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.SetTreeResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SetTreeResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SetTreeResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata SetTreeResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[15]);
}

// ===================================================================

class GetLeavesRequest::_Internal {
 public:
};

GetLeavesRequest::GetLeavesRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.GetLeavesRequest)
}
GetLeavesRequest::GetLeavesRequest(const GetLeavesRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.GetLeavesRequest)
}

inline void GetLeavesRequest::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

GetLeavesRequest::~GetLeavesRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.GetLeavesRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetLeavesRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void GetLeavesRequest::ArenaDtor(void* object) {
  GetLeavesRequest* _this = reinterpret_cast< GetLeavesRequest* >(object);
  (void)_this;
}
void GetLeavesRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetLeavesRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetLeavesRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.GetLeavesRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetLeavesRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.GetLeavesRequest.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetLeavesRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.GetLeavesRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.GetLeavesRequest.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.GetLeavesRequest)
  return target;
}

size_t GetLeavesRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.GetLeavesRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetLeavesRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetLeavesRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetLeavesRequest::GetClassData() const { return &_class_data_; }

void GetLeavesRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetLeavesRequest *>(to)->MergeFrom(
      static_cast<const GetLeavesRequest &>(from));
}


void GetLeavesRequest::MergeFrom(const GetLeavesRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.GetLeavesRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetLeavesRequest::CopyFrom(const GetLeavesRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.GetLeavesRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetLeavesRequest::IsInitialized() const {
  return true;
}

void GetLeavesRequest::InternalSwap(GetLeavesRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata GetLeavesRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[16]);
}

// ===================================================================

class GetLeavesResponse::_Internal {
 public:
};

GetLeavesResponse::GetLeavesResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  leaves_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.GetLeavesResponse)
}
GetLeavesResponse::GetLeavesResponse(const GetLeavesResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      leaves_(from.leaves_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.GetLeavesResponse)
}

inline void GetLeavesResponse::SharedCtor() {
}

GetLeavesResponse::~GetLeavesResponse() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.GetLeavesResponse)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void GetLeavesResponse::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void GetLeavesResponse::ArenaDtor(void* object) {
  GetLeavesResponse* _this = reinterpret_cast< GetLeavesResponse* >(object);
  (void)_this;
}
void GetLeavesResponse::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void GetLeavesResponse::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void GetLeavesResponse::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.GetLeavesResponse)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  leaves_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* GetLeavesResponse::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated .carbon.config.proto.ConfigLeaf leaves = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_leaves(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* GetLeavesResponse::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.GetLeavesResponse)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated .carbon.config.proto.ConfigLeaf leaves = 1;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_leaves_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(1, this->_internal_leaves(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.GetLeavesResponse)
  return target;
}

size_t GetLeavesResponse::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.GetLeavesResponse)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .carbon.config.proto.ConfigLeaf leaves = 1;
  total_size += 1UL * this->_internal_leaves_size();
  for (const auto& msg : this->leaves_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData GetLeavesResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    GetLeavesResponse::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetLeavesResponse::GetClassData() const { return &_class_data_; }

void GetLeavesResponse::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<GetLeavesResponse *>(to)->MergeFrom(
      static_cast<const GetLeavesResponse &>(from));
}


void GetLeavesResponse::MergeFrom(const GetLeavesResponse& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.GetLeavesResponse)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  leaves_.MergeFrom(from.leaves_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void GetLeavesResponse::CopyFrom(const GetLeavesResponse& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.GetLeavesResponse)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool GetLeavesResponse::IsInitialized() const {
  return true;
}

void GetLeavesResponse::InternalSwap(GetLeavesResponse* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  leaves_.InternalSwap(&other->leaves_);
}

::PROTOBUF_NAMESPACE_ID::Metadata GetLeavesResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[17]);
}

// ===================================================================

class AddToListRequest::_Internal {
 public:
};

AddToListRequest::AddToListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.AddToListRequest)
}
AddToListRequest::AddToListRequest(const AddToListRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.AddToListRequest)
}

inline void AddToListRequest::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

AddToListRequest::~AddToListRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.AddToListRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void AddToListRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void AddToListRequest::ArenaDtor(void* object) {
  AddToListRequest* _this = reinterpret_cast< AddToListRequest* >(object);
  (void)_this;
}
void AddToListRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void AddToListRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void AddToListRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.AddToListRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* AddToListRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.AddToListRequest.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.AddToListRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* AddToListRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.AddToListRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.AddToListRequest.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.AddToListRequest.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.AddToListRequest)
  return target;
}

size_t AddToListRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.AddToListRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AddToListRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    AddToListRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AddToListRequest::GetClassData() const { return &_class_data_; }

void AddToListRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<AddToListRequest *>(to)->MergeFrom(
      static_cast<const AddToListRequest &>(from));
}


void AddToListRequest::MergeFrom(const AddToListRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.AddToListRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void AddToListRequest::CopyFrom(const AddToListRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.AddToListRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool AddToListRequest::IsInitialized() const {
  return true;
}

void AddToListRequest::InternalSwap(AddToListRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata AddToListRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[18]);
}

// ===================================================================

class AddToListResponse::_Internal {
 public:
};

AddToListResponse::AddToListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.AddToListResponse)
}
AddToListResponse::AddToListResponse(const AddToListResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.AddToListResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData AddToListResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*AddToListResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata AddToListResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[19]);
}

// ===================================================================

class RemoveFromListRequest::_Internal {
 public:
};

RemoveFromListRequest::RemoveFromListRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.RemoveFromListRequest)
}
RemoveFromListRequest::RemoveFromListRequest(const RemoveFromListRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_key().empty()) {
    key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_key(), 
      GetArenaForAllocation());
  }
  name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_name().empty()) {
    name_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_name(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.RemoveFromListRequest)
}

inline void RemoveFromListRequest::SharedCtor() {
key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
name_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  name_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

RemoveFromListRequest::~RemoveFromListRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.RemoveFromListRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RemoveFromListRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  name_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void RemoveFromListRequest::ArenaDtor(void* object) {
  RemoveFromListRequest* _this = reinterpret_cast< RemoveFromListRequest* >(object);
  (void)_this;
}
void RemoveFromListRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RemoveFromListRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RemoveFromListRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.RemoveFromListRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  key_.ClearToEmpty();
  name_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RemoveFromListRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.RemoveFromListRequest.key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string name = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_name();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.RemoveFromListRequest.name"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RemoveFromListRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.RemoveFromListRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_key().data(), static_cast<int>(this->_internal_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.RemoveFromListRequest.key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_key(), target);
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_name().data(), static_cast<int>(this->_internal_name().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.RemoveFromListRequest.name");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_name(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.RemoveFromListRequest)
  return target;
}

size_t RemoveFromListRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.RemoveFromListRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string key = 1;
  if (!this->_internal_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_key());
  }

  // string name = 2;
  if (!this->_internal_name().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_name());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RemoveFromListRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RemoveFromListRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RemoveFromListRequest::GetClassData() const { return &_class_data_; }

void RemoveFromListRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RemoveFromListRequest *>(to)->MergeFrom(
      static_cast<const RemoveFromListRequest &>(from));
}


void RemoveFromListRequest::MergeFrom(const RemoveFromListRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.RemoveFromListRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_key().empty()) {
    _internal_set_key(from._internal_key());
  }
  if (!from._internal_name().empty()) {
    _internal_set_name(from._internal_name());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RemoveFromListRequest::CopyFrom(const RemoveFromListRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.RemoveFromListRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RemoveFromListRequest::IsInitialized() const {
  return true;
}

void RemoveFromListRequest::InternalSwap(RemoveFromListRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &key_, lhs_arena,
      &other->key_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &name_, lhs_arena,
      &other->name_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata RemoveFromListRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[20]);
}

// ===================================================================

class RemoveFromListResponse::_Internal {
 public:
};

RemoveFromListResponse::RemoveFromListResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.RemoveFromListResponse)
}
RemoveFromListResponse::RemoveFromListResponse(const RemoveFromListResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.RemoveFromListResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RemoveFromListResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RemoveFromListResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata RemoveFromListResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[21]);
}

// ===================================================================

class SubscriptionRequest::_Internal {
 public:
};

SubscriptionRequest::SubscriptionRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  keys_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.SubscriptionRequest)
}
SubscriptionRequest::SubscriptionRequest(const SubscriptionRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      keys_(from.keys_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.SubscriptionRequest)
}

inline void SubscriptionRequest::SharedCtor() {
}

SubscriptionRequest::~SubscriptionRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.SubscriptionRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SubscriptionRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SubscriptionRequest::ArenaDtor(void* object) {
  SubscriptionRequest* _this = reinterpret_cast< SubscriptionRequest* >(object);
  (void)_this;
}
void SubscriptionRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SubscriptionRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SubscriptionRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.SubscriptionRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  keys_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SubscriptionRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated string keys = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_keys();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.SubscriptionRequest.keys"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<10>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SubscriptionRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.SubscriptionRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated string keys = 1;
  for (int i = 0, n = this->_internal_keys_size(); i < n; i++) {
    const auto& s = this->_internal_keys(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.SubscriptionRequest.keys");
    target = stream->WriteString(1, s, target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.SubscriptionRequest)
  return target;
}

size_t SubscriptionRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.SubscriptionRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string keys = 1;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(keys_.size());
  for (int i = 0, n = keys_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      keys_.Get(i));
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SubscriptionRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SubscriptionRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SubscriptionRequest::GetClassData() const { return &_class_data_; }

void SubscriptionRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SubscriptionRequest *>(to)->MergeFrom(
      static_cast<const SubscriptionRequest &>(from));
}


void SubscriptionRequest::MergeFrom(const SubscriptionRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.SubscriptionRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  keys_.MergeFrom(from.keys_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SubscriptionRequest::CopyFrom(const SubscriptionRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.SubscriptionRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SubscriptionRequest::IsInitialized() const {
  return true;
}

void SubscriptionRequest::InternalSwap(SubscriptionRequest* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  keys_.InternalSwap(&other->keys_);
}

::PROTOBUF_NAMESPACE_ID::Metadata SubscriptionRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[22]);
}

// ===================================================================

class SubscriptionNotifyMessage::_Internal {
 public:
};

SubscriptionNotifyMessage::SubscriptionNotifyMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.SubscriptionNotifyMessage)
}
SubscriptionNotifyMessage::SubscriptionNotifyMessage(const SubscriptionNotifyMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  subscription_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    subscription_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_subscription_key().empty()) {
    subscription_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_subscription_key(), 
      GetArenaForAllocation());
  }
  notify_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    notify_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_notify_key().empty()) {
    notify_key_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_notify_key(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.SubscriptionNotifyMessage)
}

inline void SubscriptionNotifyMessage::SharedCtor() {
subscription_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  subscription_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
notify_key_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  notify_key_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

SubscriptionNotifyMessage::~SubscriptionNotifyMessage() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.SubscriptionNotifyMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SubscriptionNotifyMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  subscription_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  notify_key_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void SubscriptionNotifyMessage::ArenaDtor(void* object) {
  SubscriptionNotifyMessage* _this = reinterpret_cast< SubscriptionNotifyMessage* >(object);
  (void)_this;
}
void SubscriptionNotifyMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SubscriptionNotifyMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SubscriptionNotifyMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.SubscriptionNotifyMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  subscription_key_.ClearToEmpty();
  notify_key_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SubscriptionNotifyMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string subscription_key = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_subscription_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.SubscriptionNotifyMessage.subscription_key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string notify_key = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_notify_key();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.SubscriptionNotifyMessage.notify_key"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SubscriptionNotifyMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.SubscriptionNotifyMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string subscription_key = 1;
  if (!this->_internal_subscription_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_subscription_key().data(), static_cast<int>(this->_internal_subscription_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.SubscriptionNotifyMessage.subscription_key");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_subscription_key(), target);
  }

  // string notify_key = 2;
  if (!this->_internal_notify_key().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_notify_key().data(), static_cast<int>(this->_internal_notify_key().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.SubscriptionNotifyMessage.notify_key");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_notify_key(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.SubscriptionNotifyMessage)
  return target;
}

size_t SubscriptionNotifyMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.SubscriptionNotifyMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string subscription_key = 1;
  if (!this->_internal_subscription_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_subscription_key());
  }

  // string notify_key = 2;
  if (!this->_internal_notify_key().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_notify_key());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SubscriptionNotifyMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SubscriptionNotifyMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SubscriptionNotifyMessage::GetClassData() const { return &_class_data_; }

void SubscriptionNotifyMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SubscriptionNotifyMessage *>(to)->MergeFrom(
      static_cast<const SubscriptionNotifyMessage &>(from));
}


void SubscriptionNotifyMessage::MergeFrom(const SubscriptionNotifyMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.SubscriptionNotifyMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_subscription_key().empty()) {
    _internal_set_subscription_key(from._internal_subscription_key());
  }
  if (!from._internal_notify_key().empty()) {
    _internal_set_notify_key(from._internal_notify_key());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SubscriptionNotifyMessage::CopyFrom(const SubscriptionNotifyMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.SubscriptionNotifyMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SubscriptionNotifyMessage::IsInitialized() const {
  return true;
}

void SubscriptionNotifyMessage::InternalSwap(SubscriptionNotifyMessage* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &subscription_key_, lhs_arena,
      &other->subscription_key_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &notify_key_, lhs_arena,
      &other->notify_key_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata SubscriptionNotifyMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[23]);
}

// ===================================================================

class UpgradeCloudConfigRequest::_Internal {
 public:
};

UpgradeCloudConfigRequest::UpgradeCloudConfigRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.UpgradeCloudConfigRequest)
}
UpgradeCloudConfigRequest::UpgradeCloudConfigRequest(const UpgradeCloudConfigRequest& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_robot().empty()) {
    robot_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_robot(), 
      GetArenaForAllocation());
  }
  to_template_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    to_template_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_to_template().empty()) {
    to_template_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_to_template(), 
      GetArenaForAllocation());
  }
  from_template_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    from_template_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_from_template().empty()) {
    from_template_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_from_template(), 
      GetArenaForAllocation());
  }
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.UpgradeCloudConfigRequest)
}

inline void UpgradeCloudConfigRequest::SharedCtor() {
robot_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  robot_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
to_template_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  to_template_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
from_template_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  from_template_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
}

UpgradeCloudConfigRequest::~UpgradeCloudConfigRequest() {
  // @@protoc_insertion_point(destructor:carbon.config.proto.UpgradeCloudConfigRequest)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UpgradeCloudConfigRequest::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  robot_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  to_template_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  from_template_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void UpgradeCloudConfigRequest::ArenaDtor(void* object) {
  UpgradeCloudConfigRequest* _this = reinterpret_cast< UpgradeCloudConfigRequest* >(object);
  (void)_this;
}
void UpgradeCloudConfigRequest::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UpgradeCloudConfigRequest::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UpgradeCloudConfigRequest::Clear() {
// @@protoc_insertion_point(message_clear_start:carbon.config.proto.UpgradeCloudConfigRequest)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  robot_.ClearToEmpty();
  to_template_.ClearToEmpty();
  from_template_.ClearToEmpty();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UpgradeCloudConfigRequest::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string robot = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_robot();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.UpgradeCloudConfigRequest.robot"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string to_template = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          auto str = _internal_mutable_to_template();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.UpgradeCloudConfigRequest.to_template"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string from_template = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          auto str = _internal_mutable_from_template();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "carbon.config.proto.UpgradeCloudConfigRequest.from_template"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UpgradeCloudConfigRequest::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:carbon.config.proto.UpgradeCloudConfigRequest)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_robot().data(), static_cast<int>(this->_internal_robot().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.UpgradeCloudConfigRequest.robot");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_robot(), target);
  }

  // string to_template = 2;
  if (!this->_internal_to_template().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_to_template().data(), static_cast<int>(this->_internal_to_template().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.UpgradeCloudConfigRequest.to_template");
    target = stream->WriteStringMaybeAliased(
        2, this->_internal_to_template(), target);
  }

  // string from_template = 3;
  if (!this->_internal_from_template().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_from_template().data(), static_cast<int>(this->_internal_from_template().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "carbon.config.proto.UpgradeCloudConfigRequest.from_template");
    target = stream->WriteStringMaybeAliased(
        3, this->_internal_from_template(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:carbon.config.proto.UpgradeCloudConfigRequest)
  return target;
}

size_t UpgradeCloudConfigRequest::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:carbon.config.proto.UpgradeCloudConfigRequest)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string robot = 1;
  if (!this->_internal_robot().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_robot());
  }

  // string to_template = 2;
  if (!this->_internal_to_template().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_to_template());
  }

  // string from_template = 3;
  if (!this->_internal_from_template().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_from_template());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpgradeCloudConfigRequest::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UpgradeCloudConfigRequest::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpgradeCloudConfigRequest::GetClassData() const { return &_class_data_; }

void UpgradeCloudConfigRequest::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UpgradeCloudConfigRequest *>(to)->MergeFrom(
      static_cast<const UpgradeCloudConfigRequest &>(from));
}


void UpgradeCloudConfigRequest::MergeFrom(const UpgradeCloudConfigRequest& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:carbon.config.proto.UpgradeCloudConfigRequest)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_robot().empty()) {
    _internal_set_robot(from._internal_robot());
  }
  if (!from._internal_to_template().empty()) {
    _internal_set_to_template(from._internal_to_template());
  }
  if (!from._internal_from_template().empty()) {
    _internal_set_from_template(from._internal_from_template());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UpgradeCloudConfigRequest::CopyFrom(const UpgradeCloudConfigRequest& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:carbon.config.proto.UpgradeCloudConfigRequest)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpgradeCloudConfigRequest::IsInitialized() const {
  return true;
}

void UpgradeCloudConfigRequest::InternalSwap(UpgradeCloudConfigRequest* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &robot_, lhs_arena,
      &other->robot_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &to_template_, lhs_arena,
      &other->to_template_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &from_template_, lhs_arena,
      &other->from_template_, rhs_arena
  );
}

::PROTOBUF_NAMESPACE_ID::Metadata UpgradeCloudConfigRequest::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[24]);
}

// ===================================================================

class UpgradeCloudConfigResponse::_Internal {
 public:
};

UpgradeCloudConfigResponse::UpgradeCloudConfigResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase(arena, is_message_owned) {
  // @@protoc_insertion_point(arena_constructor:carbon.config.proto.UpgradeCloudConfigResponse)
}
UpgradeCloudConfigResponse::UpgradeCloudConfigResponse(const UpgradeCloudConfigResponse& from)
  : ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:carbon.config.proto.UpgradeCloudConfigResponse)
}





const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpgradeCloudConfigResponse::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::CopyImpl,
    ::PROTOBUF_NAMESPACE_ID::internal::ZeroFieldsBase::MergeImpl,
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpgradeCloudConfigResponse::GetClassData() const { return &_class_data_; }







::PROTOBUF_NAMESPACE_ID::Metadata UpgradeCloudConfigResponse::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_getter, &descriptor_table_config_2fapi_2fproto_2fconfig_5fservice_2eproto_once,
      file_level_metadata_config_2fapi_2fproto_2fconfig_5fservice_2eproto[25]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace proto
}  // namespace config
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::carbon::config::proto::PingRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::PingRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::PingRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::PongResponse* Arena::CreateMaybeMessage< ::carbon::config::proto::PongResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::PongResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::ConfigValue* Arena::CreateMaybeMessage< ::carbon::config::proto::ConfigValue >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::ConfigValue >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::IntConfigDef* Arena::CreateMaybeMessage< ::carbon::config::proto::IntConfigDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::IntConfigDef >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::UIntConfigDef* Arena::CreateMaybeMessage< ::carbon::config::proto::UIntConfigDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::UIntConfigDef >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::FloatConfigDef* Arena::CreateMaybeMessage< ::carbon::config::proto::FloatConfigDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::FloatConfigDef >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::StringConfigDef* Arena::CreateMaybeMessage< ::carbon::config::proto::StringConfigDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::StringConfigDef >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::ConfigDef* Arena::CreateMaybeMessage< ::carbon::config::proto::ConfigDef >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::ConfigDef >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::ConfigNode* Arena::CreateMaybeMessage< ::carbon::config::proto::ConfigNode >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::ConfigNode >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::ConfigLeaf* Arena::CreateMaybeMessage< ::carbon::config::proto::ConfigLeaf >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::ConfigLeaf >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::SetValueRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::SetValueRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::SetValueRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::SetValueResponse* Arena::CreateMaybeMessage< ::carbon::config::proto::SetValueResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::SetValueResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::GetTreeRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::GetTreeRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::GetTreeRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::GetTreeResponse* Arena::CreateMaybeMessage< ::carbon::config::proto::GetTreeResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::GetTreeResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::SetTreeRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::SetTreeRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::SetTreeRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::SetTreeResponse* Arena::CreateMaybeMessage< ::carbon::config::proto::SetTreeResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::SetTreeResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::GetLeavesRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::GetLeavesRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::GetLeavesRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::GetLeavesResponse* Arena::CreateMaybeMessage< ::carbon::config::proto::GetLeavesResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::GetLeavesResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::AddToListRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::AddToListRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::AddToListRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::AddToListResponse* Arena::CreateMaybeMessage< ::carbon::config::proto::AddToListResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::AddToListResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::RemoveFromListRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::RemoveFromListRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::RemoveFromListRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::RemoveFromListResponse* Arena::CreateMaybeMessage< ::carbon::config::proto::RemoveFromListResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::RemoveFromListResponse >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::SubscriptionRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::SubscriptionRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::SubscriptionRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::SubscriptionNotifyMessage* Arena::CreateMaybeMessage< ::carbon::config::proto::SubscriptionNotifyMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::SubscriptionNotifyMessage >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::UpgradeCloudConfigRequest* Arena::CreateMaybeMessage< ::carbon::config::proto::UpgradeCloudConfigRequest >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::UpgradeCloudConfigRequest >(arena);
}
template<> PROTOBUF_NOINLINE ::carbon::config::proto::UpgradeCloudConfigResponse* Arena::CreateMaybeMessage< ::carbon::config::proto::UpgradeCloudConfigResponse >(Arena* arena) {
  return Arena::CreateMessageInternal< ::carbon::config::proto::UpgradeCloudConfigResponse >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

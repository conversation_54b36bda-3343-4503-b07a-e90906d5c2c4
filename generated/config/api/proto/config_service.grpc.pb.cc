// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: config/api/proto/config_service.proto

#include "config/api/proto/config_service.pb.h"
#include "config/api/proto/config_service.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace config {
namespace proto {

static const char* ConfigService_method_names[] = {
  "/carbon.config.proto.ConfigService/Ping",
  "/carbon.config.proto.ConfigService/SetValue",
  "/carbon.config.proto.ConfigService/GetTree",
  "/carbon.config.proto.ConfigService/SetTree",
  "/carbon.config.proto.ConfigService/GetLeaves",
  "/carbon.config.proto.ConfigService/AddToList",
  "/carbon.config.proto.ConfigService/RemoveFromList",
  "/carbon.config.proto.ConfigService/UpgradeCloudConfig",
};

std::unique_ptr< ConfigService::Stub> ConfigService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ConfigService::Stub> stub(new ConfigService::Stub(channel, options));
  return stub;
}

ConfigService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Ping_(ConfigService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetValue_(ConfigService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetTree_(ConfigService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_SetTree_(ConfigService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetLeaves_(ConfigService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_AddToList_(ConfigService_method_names[5], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_RemoveFromList_(ConfigService_method_names[6], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UpgradeCloudConfig_(ConfigService_method_names[7], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status ConfigService::Stub::Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::carbon::config::proto::PongResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::config::proto::PingRequest, ::carbon::config::proto::PongResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_Ping_, context, request, response);
}

void ConfigService::Stub::async::Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::config::proto::PingRequest, ::carbon::config::proto::PongResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, std::move(f));
}

void ConfigService::Stub::async::Ping(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_Ping_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::PongResponse>* ConfigService::Stub::PrepareAsyncPingRaw(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::config::proto::PongResponse, ::carbon::config::proto::PingRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_Ping_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::PongResponse>* ConfigService::Stub::AsyncPingRaw(::grpc::ClientContext* context, const ::carbon::config::proto::PingRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPingRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ConfigService::Stub::SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::carbon::config::proto::SetValueResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::config::proto::SetValueRequest, ::carbon::config::proto::SetValueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetValue_, context, request, response);
}

void ConfigService::Stub::async::SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::config::proto::SetValueRequest, ::carbon::config::proto::SetValueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetValue_, context, request, response, std::move(f));
}

void ConfigService::Stub::async::SetValue(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetValue_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetValueResponse>* ConfigService::Stub::PrepareAsyncSetValueRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::config::proto::SetValueResponse, ::carbon::config::proto::SetValueRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetValue_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetValueResponse>* ConfigService::Stub::AsyncSetValueRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetValueRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetValueRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ConfigService::Stub::GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::carbon::config::proto::GetTreeResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::config::proto::GetTreeRequest, ::carbon::config::proto::GetTreeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetTree_, context, request, response);
}

void ConfigService::Stub::async::GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::config::proto::GetTreeRequest, ::carbon::config::proto::GetTreeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTree_, context, request, response, std::move(f));
}

void ConfigService::Stub::async::GetTree(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetTree_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetTreeResponse>* ConfigService::Stub::PrepareAsyncGetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::config::proto::GetTreeResponse, ::carbon::config::proto::GetTreeRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetTree_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetTreeResponse>* ConfigService::Stub::AsyncGetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetTreeRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetTreeRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ConfigService::Stub::SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::carbon::config::proto::SetTreeResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::config::proto::SetTreeRequest, ::carbon::config::proto::SetTreeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_SetTree_, context, request, response);
}

void ConfigService::Stub::async::SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::config::proto::SetTreeRequest, ::carbon::config::proto::SetTreeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTree_, context, request, response, std::move(f));
}

void ConfigService::Stub::async::SetTree(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_SetTree_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetTreeResponse>* ConfigService::Stub::PrepareAsyncSetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::config::proto::SetTreeResponse, ::carbon::config::proto::SetTreeRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_SetTree_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::SetTreeResponse>* ConfigService::Stub::AsyncSetTreeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SetTreeRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncSetTreeRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ConfigService::Stub::GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::carbon::config::proto::GetLeavesResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::config::proto::GetLeavesRequest, ::carbon::config::proto::GetLeavesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetLeaves_, context, request, response);
}

void ConfigService::Stub::async::GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::config::proto::GetLeavesRequest, ::carbon::config::proto::GetLeavesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLeaves_, context, request, response, std::move(f));
}

void ConfigService::Stub::async::GetLeaves(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetLeaves_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetLeavesResponse>* ConfigService::Stub::PrepareAsyncGetLeavesRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::config::proto::GetLeavesResponse, ::carbon::config::proto::GetLeavesRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetLeaves_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::GetLeavesResponse>* ConfigService::Stub::AsyncGetLeavesRaw(::grpc::ClientContext* context, const ::carbon::config::proto::GetLeavesRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetLeavesRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ConfigService::Stub::AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::carbon::config::proto::AddToListResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::config::proto::AddToListRequest, ::carbon::config::proto::AddToListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_AddToList_, context, request, response);
}

void ConfigService::Stub::async::AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::config::proto::AddToListRequest, ::carbon::config::proto::AddToListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AddToList_, context, request, response, std::move(f));
}

void ConfigService::Stub::async::AddToList(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_AddToList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::AddToListResponse>* ConfigService::Stub::PrepareAsyncAddToListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::config::proto::AddToListResponse, ::carbon::config::proto::AddToListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_AddToList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::AddToListResponse>* ConfigService::Stub::AsyncAddToListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::AddToListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncAddToListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ConfigService::Stub::RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::carbon::config::proto::RemoveFromListResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::config::proto::RemoveFromListRequest, ::carbon::config::proto::RemoveFromListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_RemoveFromList_, context, request, response);
}

void ConfigService::Stub::async::RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::config::proto::RemoveFromListRequest, ::carbon::config::proto::RemoveFromListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RemoveFromList_, context, request, response, std::move(f));
}

void ConfigService::Stub::async::RemoveFromList(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_RemoveFromList_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::RemoveFromListResponse>* ConfigService::Stub::PrepareAsyncRemoveFromListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::config::proto::RemoveFromListResponse, ::carbon::config::proto::RemoveFromListRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_RemoveFromList_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::RemoveFromListResponse>* ConfigService::Stub::AsyncRemoveFromListRaw(::grpc::ClientContext* context, const ::carbon::config::proto::RemoveFromListRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncRemoveFromListRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status ConfigService::Stub::UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::carbon::config::proto::UpgradeCloudConfigResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::config::proto::UpgradeCloudConfigRequest, ::carbon::config::proto::UpgradeCloudConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UpgradeCloudConfig_, context, request, response);
}

void ConfigService::Stub::async::UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::config::proto::UpgradeCloudConfigRequest, ::carbon::config::proto::UpgradeCloudConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpgradeCloudConfig_, context, request, response, std::move(f));
}

void ConfigService::Stub::async::UpgradeCloudConfig(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UpgradeCloudConfig_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::UpgradeCloudConfigResponse>* ConfigService::Stub::PrepareAsyncUpgradeCloudConfigRaw(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::config::proto::UpgradeCloudConfigResponse, ::carbon::config::proto::UpgradeCloudConfigRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UpgradeCloudConfig_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::config::proto::UpgradeCloudConfigResponse>* ConfigService::Stub::AsyncUpgradeCloudConfigRaw(::grpc::ClientContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUpgradeCloudConfigRaw(context, request, cq);
  result->StartCall();
  return result;
}

ConfigService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ConfigService::Service, ::carbon::config::proto::PingRequest, ::carbon::config::proto::PongResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::PingRequest* req,
             ::carbon::config::proto::PongResponse* resp) {
               return service->Ping(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ConfigService::Service, ::carbon::config::proto::SetValueRequest, ::carbon::config::proto::SetValueResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::SetValueRequest* req,
             ::carbon::config::proto::SetValueResponse* resp) {
               return service->SetValue(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ConfigService::Service, ::carbon::config::proto::GetTreeRequest, ::carbon::config::proto::GetTreeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::GetTreeRequest* req,
             ::carbon::config::proto::GetTreeResponse* resp) {
               return service->GetTree(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ConfigService::Service, ::carbon::config::proto::SetTreeRequest, ::carbon::config::proto::SetTreeResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::SetTreeRequest* req,
             ::carbon::config::proto::SetTreeResponse* resp) {
               return service->SetTree(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ConfigService::Service, ::carbon::config::proto::GetLeavesRequest, ::carbon::config::proto::GetLeavesResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::GetLeavesRequest* req,
             ::carbon::config::proto::GetLeavesResponse* resp) {
               return service->GetLeaves(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigService_method_names[5],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ConfigService::Service, ::carbon::config::proto::AddToListRequest, ::carbon::config::proto::AddToListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::AddToListRequest* req,
             ::carbon::config::proto::AddToListResponse* resp) {
               return service->AddToList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigService_method_names[6],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ConfigService::Service, ::carbon::config::proto::RemoveFromListRequest, ::carbon::config::proto::RemoveFromListResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::RemoveFromListRequest* req,
             ::carbon::config::proto::RemoveFromListResponse* resp) {
               return service->RemoveFromList(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigService_method_names[7],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< ConfigService::Service, ::carbon::config::proto::UpgradeCloudConfigRequest, ::carbon::config::proto::UpgradeCloudConfigResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](ConfigService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::UpgradeCloudConfigRequest* req,
             ::carbon::config::proto::UpgradeCloudConfigResponse* resp) {
               return service->UpgradeCloudConfig(ctx, req, resp);
             }, this)));
}

ConfigService::Service::~Service() {
}

::grpc::Status ConfigService::Service::Ping(::grpc::ServerContext* context, const ::carbon::config::proto::PingRequest* request, ::carbon::config::proto::PongResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ConfigService::Service::SetValue(::grpc::ServerContext* context, const ::carbon::config::proto::SetValueRequest* request, ::carbon::config::proto::SetValueResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ConfigService::Service::GetTree(::grpc::ServerContext* context, const ::carbon::config::proto::GetTreeRequest* request, ::carbon::config::proto::GetTreeResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ConfigService::Service::SetTree(::grpc::ServerContext* context, const ::carbon::config::proto::SetTreeRequest* request, ::carbon::config::proto::SetTreeResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ConfigService::Service::GetLeaves(::grpc::ServerContext* context, const ::carbon::config::proto::GetLeavesRequest* request, ::carbon::config::proto::GetLeavesResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ConfigService::Service::AddToList(::grpc::ServerContext* context, const ::carbon::config::proto::AddToListRequest* request, ::carbon::config::proto::AddToListResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ConfigService::Service::RemoveFromList(::grpc::ServerContext* context, const ::carbon::config::proto::RemoveFromListRequest* request, ::carbon::config::proto::RemoveFromListResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status ConfigService::Service::UpgradeCloudConfig(::grpc::ServerContext* context, const ::carbon::config::proto::UpgradeCloudConfigRequest* request, ::carbon::config::proto::UpgradeCloudConfigResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


static const char* ConfigNotificationService_method_names[] = {
  "/carbon.config.proto.ConfigNotificationService/Subscribe",
};

std::unique_ptr< ConfigNotificationService::Stub> ConfigNotificationService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< ConfigNotificationService::Stub> stub(new ConfigNotificationService::Stub(channel, options));
  return stub;
}

ConfigNotificationService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_Subscribe_(ConfigNotificationService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::SERVER_STREAMING, channel)
  {}

::grpc::ClientReader< ::carbon::config::proto::SubscriptionNotifyMessage>* ConfigNotificationService::Stub::SubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request) {
  return ::grpc::internal::ClientReaderFactory< ::carbon::config::proto::SubscriptionNotifyMessage>::Create(channel_.get(), rpcmethod_Subscribe_, context, request);
}

void ConfigNotificationService::Stub::async::Subscribe(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest* request, ::grpc::ClientReadReactor< ::carbon::config::proto::SubscriptionNotifyMessage>* reactor) {
  ::grpc::internal::ClientCallbackReaderFactory< ::carbon::config::proto::SubscriptionNotifyMessage>::Create(stub_->channel_.get(), stub_->rpcmethod_Subscribe_, context, request, reactor);
}

::grpc::ClientAsyncReader< ::carbon::config::proto::SubscriptionNotifyMessage>* ConfigNotificationService::Stub::AsyncSubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq, void* tag) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::config::proto::SubscriptionNotifyMessage>::Create(channel_.get(), cq, rpcmethod_Subscribe_, context, request, true, tag);
}

::grpc::ClientAsyncReader< ::carbon::config::proto::SubscriptionNotifyMessage>* ConfigNotificationService::Stub::PrepareAsyncSubscribeRaw(::grpc::ClientContext* context, const ::carbon::config::proto::SubscriptionRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncReaderFactory< ::carbon::config::proto::SubscriptionNotifyMessage>::Create(channel_.get(), cq, rpcmethod_Subscribe_, context, request, false, nullptr);
}

ConfigNotificationService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      ConfigNotificationService_method_names[0],
      ::grpc::internal::RpcMethod::SERVER_STREAMING,
      new ::grpc::internal::ServerStreamingHandler< ConfigNotificationService::Service, ::carbon::config::proto::SubscriptionRequest, ::carbon::config::proto::SubscriptionNotifyMessage>(
          [](ConfigNotificationService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::config::proto::SubscriptionRequest* req,
             ::grpc::ServerWriter<::carbon::config::proto::SubscriptionNotifyMessage>* writer) {
               return service->Subscribe(ctx, req, writer);
             }, this)));
}

ConfigNotificationService::Service::~Service() {
}

::grpc::Status ConfigNotificationService::Service::Subscribe(::grpc::ServerContext* context, const ::carbon::config::proto::SubscriptionRequest* request, ::grpc::ServerWriter< ::carbon::config::proto::SubscriptionNotifyMessage>* writer) {
  (void) context;
  (void) request;
  (void) writer;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace config
}  // namespace proto


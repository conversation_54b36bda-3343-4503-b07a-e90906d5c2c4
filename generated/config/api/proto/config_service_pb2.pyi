"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    NewType as typing___NewType,
    Optional as typing___Optional,
    Text as typing___Text,
    cast as typing___cast,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

ConfigTypeValue = typing___NewType('ConfigTypeValue', builtin___int)
type___ConfigTypeValue = ConfigTypeValue
ConfigType: _ConfigType
class _ConfigType(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ConfigTypeValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    NODE = typing___cast(ConfigTypeValue, 0)
    LIST = typing___cast(ConfigTypeValue, 1)
    STRING = typing___cast(ConfigTypeValue, 2)
    INT = typing___cast(ConfigTypeValue, 3)
    UINT = typing___cast(ConfigTypeValue, 4)
    FLOAT = typing___cast(ConfigTypeValue, 5)
    BOOL = typing___cast(ConfigTypeValue, 6)
NODE = typing___cast(ConfigTypeValue, 0)
LIST = typing___cast(ConfigTypeValue, 1)
STRING = typing___cast(ConfigTypeValue, 2)
INT = typing___cast(ConfigTypeValue, 3)
UINT = typing___cast(ConfigTypeValue, 4)
FLOAT = typing___cast(ConfigTypeValue, 5)
BOOL = typing___cast(ConfigTypeValue, 6)

ConfigComplexityValue = typing___NewType('ConfigComplexityValue', builtin___int)
type___ConfigComplexityValue = ConfigComplexityValue
ConfigComplexity: _ConfigComplexity
class _ConfigComplexity(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[ConfigComplexityValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    USER = typing___cast(ConfigComplexityValue, 0)
    ADVANCED = typing___cast(ConfigComplexityValue, 1)
    EXPERT = typing___cast(ConfigComplexityValue, 2)
    DEVELOPER = typing___cast(ConfigComplexityValue, 3)
USER = typing___cast(ConfigComplexityValue, 0)
ADVANCED = typing___cast(ConfigComplexityValue, 1)
EXPERT = typing___cast(ConfigComplexityValue, 2)
DEVELOPER = typing___cast(ConfigComplexityValue, 3)

class PingRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PingRequest = PingRequest

class PongResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    x: builtin___int = ...

    def __init__(self,
        *,
        x : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"x",b"x"]) -> None: ...
type___PongResponse = PongResponse

class ConfigValue(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    int64_val: builtin___int = ...
    uint64_val: builtin___int = ...
    bool_val: builtin___bool = ...
    float_val: builtin___float = ...
    string_val: typing___Text = ...
    timestamp_ms: builtin___int = ...

    def __init__(self,
        *,
        int64_val : typing___Optional[builtin___int] = None,
        uint64_val : typing___Optional[builtin___int] = None,
        bool_val : typing___Optional[builtin___bool] = None,
        float_val : typing___Optional[builtin___float] = None,
        string_val : typing___Optional[typing___Text] = None,
        timestamp_ms : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"bool_val",b"bool_val",u"float_val",b"float_val",u"int64_val",b"int64_val",u"string_val",b"string_val",u"uint64_val",b"uint64_val",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"bool_val",b"bool_val",u"float_val",b"float_val",u"int64_val",b"int64_val",u"string_val",b"string_val",u"timestamp_ms",b"timestamp_ms",u"uint64_val",b"uint64_val",u"value",b"value"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"value",b"value"]) -> typing_extensions___Literal["int64_val","uint64_val","bool_val","float_val","string_val"]: ...
type___ConfigValue = ConfigValue

class IntConfigDef(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    min: builtin___int = ...
    max: builtin___int = ...
    step: builtin___int = ...

    def __init__(self,
        *,
        min : typing___Optional[builtin___int] = None,
        max : typing___Optional[builtin___int] = None,
        step : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"max",b"max",u"min",b"min",u"step",b"step"]) -> None: ...
type___IntConfigDef = IntConfigDef

class UIntConfigDef(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    min: builtin___int = ...
    max: builtin___int = ...
    step: builtin___int = ...

    def __init__(self,
        *,
        min : typing___Optional[builtin___int] = None,
        max : typing___Optional[builtin___int] = None,
        step : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"max",b"max",u"min",b"min",u"step",b"step"]) -> None: ...
type___UIntConfigDef = UIntConfigDef

class FloatConfigDef(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    min: builtin___float = ...
    max: builtin___float = ...
    step: builtin___float = ...

    def __init__(self,
        *,
        min : typing___Optional[builtin___float] = None,
        max : typing___Optional[builtin___float] = None,
        step : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"max",b"max",u"min",b"min",u"step",b"step"]) -> None: ...
type___FloatConfigDef = FloatConfigDef

class StringConfigDef(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    size_limit: builtin___int = ...
    choices: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        size_limit : typing___Optional[builtin___int] = None,
        choices : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"choices",b"choices",u"size_limit",b"size_limit"]) -> None: ...
type___StringConfigDef = StringConfigDef

class ConfigDef(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    type: type___ConfigTypeValue = ...
    complexity: type___ConfigComplexityValue = ...
    hint: typing___Text = ...
    default_recommended: builtin___bool = ...
    units: typing___Text = ...

    @property
    def int_def(self) -> type___IntConfigDef: ...

    @property
    def uint_def(self) -> type___UIntConfigDef: ...

    @property
    def float_def(self) -> type___FloatConfigDef: ...

    @property
    def string_def(self) -> type___StringConfigDef: ...

    def __init__(self,
        *,
        type : typing___Optional[type___ConfigTypeValue] = None,
        complexity : typing___Optional[type___ConfigComplexityValue] = None,
        int_def : typing___Optional[type___IntConfigDef] = None,
        uint_def : typing___Optional[type___UIntConfigDef] = None,
        float_def : typing___Optional[type___FloatConfigDef] = None,
        string_def : typing___Optional[type___StringConfigDef] = None,
        hint : typing___Optional[typing___Text] = None,
        default_recommended : typing___Optional[builtin___bool] = None,
        units : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"extra",b"extra",u"float_def",b"float_def",u"int_def",b"int_def",u"string_def",b"string_def",u"uint_def",b"uint_def"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"complexity",b"complexity",u"default_recommended",b"default_recommended",u"extra",b"extra",u"float_def",b"float_def",u"hint",b"hint",u"int_def",b"int_def",u"string_def",b"string_def",u"type",b"type",u"uint_def",b"uint_def",u"units",b"units"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"extra",b"extra"]) -> typing_extensions___Literal["int_def","uint_def","float_def","string_def"]: ...
type___ConfigDef = ConfigDef

class ConfigNode(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    name: typing___Text = ...

    @property
    def value(self) -> type___ConfigValue: ...

    @property
    def children(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ConfigNode]: ...

    def __init__(self,
        *,
        name : typing___Optional[typing___Text] = None,
        value : typing___Optional[type___ConfigValue] = None,
        children : typing___Optional[typing___Iterable[type___ConfigNode]] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"def",b"def",u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"children",b"children",u"def",b"def",u"name",b"name",u"value",b"value"]) -> None: ...
type___ConfigNode = ConfigNode

class ConfigLeaf(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    key: typing___Text = ...

    @property
    def value(self) -> type___ConfigValue: ...

    def __init__(self,
        *,
        key : typing___Optional[typing___Text] = None,
        value : typing___Optional[type___ConfigValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
type___ConfigLeaf = ConfigLeaf

class SetValueRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    key: typing___Text = ...

    @property
    def value(self) -> type___ConfigValue: ...

    def __init__(self,
        *,
        key : typing___Optional[typing___Text] = None,
        value : typing___Optional[type___ConfigValue] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
type___SetValueRequest = SetValueRequest

class SetValueResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetValueResponse = SetValueResponse

class GetTreeRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    key: typing___Text = ...

    def __init__(self,
        *,
        key : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key"]) -> None: ...
type___GetTreeRequest = GetTreeRequest

class GetTreeResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def node(self) -> type___ConfigNode: ...

    def __init__(self,
        *,
        node : typing___Optional[type___ConfigNode] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"node",b"node"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"node",b"node"]) -> None: ...
type___GetTreeResponse = GetTreeResponse

class SetTreeRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    key: typing___Text = ...

    @property
    def node(self) -> type___ConfigNode: ...

    def __init__(self,
        *,
        key : typing___Optional[typing___Text] = None,
        node : typing___Optional[type___ConfigNode] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"node",b"node"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"node",b"node"]) -> None: ...
type___SetTreeRequest = SetTreeRequest

class SetTreeResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___SetTreeResponse = SetTreeResponse

class GetLeavesRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    key: typing___Text = ...

    def __init__(self,
        *,
        key : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key"]) -> None: ...
type___GetLeavesRequest = GetLeavesRequest

class GetLeavesResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def leaves(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___ConfigLeaf]: ...

    def __init__(self,
        *,
        leaves : typing___Optional[typing___Iterable[type___ConfigLeaf]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"leaves",b"leaves"]) -> None: ...
type___GetLeavesResponse = GetLeavesResponse

class AddToListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    key: typing___Text = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        key : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"name",b"name"]) -> None: ...
type___AddToListRequest = AddToListRequest

class AddToListResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___AddToListResponse = AddToListResponse

class RemoveFromListRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    key: typing___Text = ...
    name: typing___Text = ...

    def __init__(self,
        *,
        key : typing___Optional[typing___Text] = None,
        name : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"name",b"name"]) -> None: ...
type___RemoveFromListRequest = RemoveFromListRequest

class RemoveFromListResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___RemoveFromListResponse = RemoveFromListResponse

class SubscriptionRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    keys: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...

    def __init__(self,
        *,
        keys : typing___Optional[typing___Iterable[typing___Text]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"keys",b"keys"]) -> None: ...
type___SubscriptionRequest = SubscriptionRequest

class SubscriptionNotifyMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    subscription_key: typing___Text = ...
    notify_key: typing___Text = ...

    def __init__(self,
        *,
        subscription_key : typing___Optional[typing___Text] = None,
        notify_key : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"notify_key",b"notify_key",u"subscription_key",b"subscription_key"]) -> None: ...
type___SubscriptionNotifyMessage = SubscriptionNotifyMessage

class UpgradeCloudConfigRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot: typing___Text = ...
    to_template: typing___Text = ...
    from_template: typing___Text = ...

    def __init__(self,
        *,
        robot : typing___Optional[typing___Text] = None,
        to_template : typing___Optional[typing___Text] = None,
        from_template : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"from_template",b"from_template",u"robot",b"robot",u"to_template",b"to_template"]) -> None: ...
type___UpgradeCloudConfigRequest = UpgradeCloudConfigRequest

class UpgradeCloudConfigResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    def __init__(self,
        ) -> None: ...
type___UpgradeCloudConfigResponse = UpgradeCloudConfigResponse

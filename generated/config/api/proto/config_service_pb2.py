# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: config/api/proto/config_service.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='config/api/proto/config_service.proto',
  package='carbon.config.proto',
  syntax='proto3',
  serialized_options=b'Z\024proto/config_service',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n%config/api/proto/config_service.proto\x12\x13\x63\x61rbon.config.proto\"\x18\n\x0bPingRequest\x12\t\n\x01x\x18\x01 \x01(\x05\"\x19\n\x0cPongResponse\x12\t\n\x01x\x18\x01 \x01(\x05\"\x96\x01\n\x0b\x43onfigValue\x12\x13\n\tint64_val\x18\x01 \x01(\x03H\x00\x12\x14\n\nuint64_val\x18\x02 \x01(\x04H\x00\x12\x12\n\x08\x62ool_val\x18\x03 \x01(\x08H\x00\x12\x13\n\tfloat_val\x18\x04 \x01(\x01H\x00\x12\x14\n\nstring_val\x18\x05 \x01(\tH\x00\x12\x14\n\x0ctimestamp_ms\x18\x06 \x01(\x04\x42\x07\n\x05value\"6\n\x0cIntConfigDef\x12\x0b\n\x03min\x18\x01 \x01(\x03\x12\x0b\n\x03max\x18\x02 \x01(\x03\x12\x0c\n\x04step\x18\x03 \x01(\x03\"7\n\rUIntConfigDef\x12\x0b\n\x03min\x18\x01 \x01(\x04\x12\x0b\n\x03max\x18\x02 \x01(\x04\x12\x0c\n\x04step\x18\x03 \x01(\x04\"8\n\x0e\x46loatConfigDef\x12\x0b\n\x03min\x18\x01 \x01(\x01\x12\x0b\n\x03max\x18\x02 \x01(\x01\x12\x0c\n\x04step\x18\x03 \x01(\x01\"6\n\x0fStringConfigDef\x12\x12\n\nsize_limit\x18\x01 \x01(\r\x12\x0f\n\x07\x63hoices\x18\x03 \x03(\t\"\x9c\x03\n\tConfigDef\x12-\n\x04type\x18\x01 \x01(\x0e\x32\x1f.carbon.config.proto.ConfigType\x12\x39\n\ncomplexity\x18\x02 \x01(\x0e\x32%.carbon.config.proto.ConfigComplexity\x12\x34\n\x07int_def\x18\x03 \x01(\x0b\x32!.carbon.config.proto.IntConfigDefH\x00\x12\x36\n\x08uint_def\x18\x04 \x01(\x0b\x32\".carbon.config.proto.UIntConfigDefH\x00\x12\x38\n\tfloat_def\x18\x05 \x01(\x0b\x32#.carbon.config.proto.FloatConfigDefH\x00\x12:\n\nstring_def\x18\x06 \x01(\x0b\x32$.carbon.config.proto.StringConfigDefH\x00\x12\x0c\n\x04hint\x18\x07 \x01(\t\x12\x1b\n\x13\x64\x65\x66\x61ult_recommended\x18\x08 \x01(\x08\x12\r\n\x05units\x18\t \x01(\tB\x07\n\x05\x65xtra\"\xab\x01\n\nConfigNode\x12\x0c\n\x04name\x18\x01 \x01(\t\x12/\n\x05value\x18\x02 \x01(\x0b\x32 .carbon.config.proto.ConfigValue\x12+\n\x03\x64\x65\x66\x18\x03 \x01(\x0b\x32\x1e.carbon.config.proto.ConfigDef\x12\x31\n\x08\x63hildren\x18\x04 \x03(\x0b\x32\x1f.carbon.config.proto.ConfigNode\"J\n\nConfigLeaf\x12\x0b\n\x03key\x18\x01 \x01(\t\x12/\n\x05value\x18\x02 \x01(\x0b\x32 .carbon.config.proto.ConfigValue\"O\n\x0fSetValueRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12/\n\x05value\x18\x02 \x01(\x0b\x32 .carbon.config.proto.ConfigValue\"\x12\n\x10SetValueResponse\"\x1d\n\x0eGetTreeRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\"@\n\x0fGetTreeResponse\x12-\n\x04node\x18\x01 \x01(\x0b\x32\x1f.carbon.config.proto.ConfigNode\"L\n\x0eSetTreeRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12-\n\x04node\x18\x02 \x01(\x0b\x32\x1f.carbon.config.proto.ConfigNode\"\x11\n\x0fSetTreeResponse\"\x1f\n\x10GetLeavesRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\"D\n\x11GetLeavesResponse\x12/\n\x06leaves\x18\x01 \x03(\x0b\x32\x1f.carbon.config.proto.ConfigLeaf\"-\n\x10\x41\x64\x64ToListRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\x13\n\x11\x41\x64\x64ToListResponse\"2\n\x15RemoveFromListRequest\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\x0c\n\x04name\x18\x02 \x01(\t\"\x18\n\x16RemoveFromListResponse\"#\n\x13SubscriptionRequest\x12\x0c\n\x04keys\x18\x01 \x03(\t\"I\n\x19SubscriptionNotifyMessage\x12\x18\n\x10subscription_key\x18\x01 \x01(\t\x12\x12\n\nnotify_key\x18\x02 \x01(\t\"V\n\x19UpgradeCloudConfigRequest\x12\r\n\x05robot\x18\x01 \x01(\t\x12\x13\n\x0bto_template\x18\x02 \x01(\t\x12\x15\n\rfrom_template\x18\x03 \x01(\t\"\x1c\n\x1aUpgradeCloudConfigResponse*T\n\nConfigType\x12\x08\n\x04NODE\x10\x00\x12\x08\n\x04LIST\x10\x01\x12\n\n\x06STRING\x10\x02\x12\x07\n\x03INT\x10\x03\x12\x08\n\x04UINT\x10\x04\x12\t\n\x05\x46LOAT\x10\x05\x12\x08\n\x04\x42OOL\x10\x06*E\n\x10\x43onfigComplexity\x12\x08\n\x04USER\x10\x00\x12\x0c\n\x08\x41\x44VANCED\x10\x01\x12\n\n\x06\x45XPERT\x10\x02\x12\r\n\tDEVELOPER\x10\x03\x32\xfb\x05\n\rConfigService\x12K\n\x04Ping\x12 .carbon.config.proto.PingRequest\x1a!.carbon.config.proto.PongResponse\x12W\n\x08SetValue\x12$.carbon.config.proto.SetValueRequest\x1a%.carbon.config.proto.SetValueResponse\x12T\n\x07GetTree\x12#.carbon.config.proto.GetTreeRequest\x1a$.carbon.config.proto.GetTreeResponse\x12T\n\x07SetTree\x12#.carbon.config.proto.SetTreeRequest\x1a$.carbon.config.proto.SetTreeResponse\x12Z\n\tGetLeaves\x12%.carbon.config.proto.GetLeavesRequest\x1a&.carbon.config.proto.GetLeavesResponse\x12Z\n\tAddToList\x12%.carbon.config.proto.AddToListRequest\x1a&.carbon.config.proto.AddToListResponse\x12i\n\x0eRemoveFromList\x12*.carbon.config.proto.RemoveFromListRequest\x1a+.carbon.config.proto.RemoveFromListResponse\x12u\n\x12UpgradeCloudConfig\x12..carbon.config.proto.UpgradeCloudConfigRequest\x1a/.carbon.config.proto.UpgradeCloudConfigResponse2\x84\x01\n\x19\x43onfigNotificationService\x12g\n\tSubscribe\x12(.carbon.config.proto.SubscriptionRequest\x1a..carbon.config.proto.SubscriptionNotifyMessage0\x01\x42\x16Z\x14proto/config_serviceb\x06proto3'
)

_CONFIGTYPE = _descriptor.EnumDescriptor(
  name='ConfigType',
  full_name='carbon.config.proto.ConfigType',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='NODE', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='LIST', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='STRING', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='INT', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='UINT', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='FLOAT', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='BOOL', index=6, number=6,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=1934,
  serialized_end=2018,
)
_sym_db.RegisterEnumDescriptor(_CONFIGTYPE)

ConfigType = enum_type_wrapper.EnumTypeWrapper(_CONFIGTYPE)
_CONFIGCOMPLEXITY = _descriptor.EnumDescriptor(
  name='ConfigComplexity',
  full_name='carbon.config.proto.ConfigComplexity',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='USER', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='ADVANCED', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='EXPERT', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='DEVELOPER', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=2020,
  serialized_end=2089,
)
_sym_db.RegisterEnumDescriptor(_CONFIGCOMPLEXITY)

ConfigComplexity = enum_type_wrapper.EnumTypeWrapper(_CONFIGCOMPLEXITY)
NODE = 0
LIST = 1
STRING = 2
INT = 3
UINT = 4
FLOAT = 5
BOOL = 6
USER = 0
ADVANCED = 1
EXPERT = 2
DEVELOPER = 3



_PINGREQUEST = _descriptor.Descriptor(
  name='PingRequest',
  full_name='carbon.config.proto.PingRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='carbon.config.proto.PingRequest.x', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=62,
  serialized_end=86,
)


_PONGRESPONSE = _descriptor.Descriptor(
  name='PongResponse',
  full_name='carbon.config.proto.PongResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='x', full_name='carbon.config.proto.PongResponse.x', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=88,
  serialized_end=113,
)


_CONFIGVALUE = _descriptor.Descriptor(
  name='ConfigValue',
  full_name='carbon.config.proto.ConfigValue',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='int64_val', full_name='carbon.config.proto.ConfigValue.int64_val', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uint64_val', full_name='carbon.config.proto.ConfigValue.uint64_val', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='bool_val', full_name='carbon.config.proto.ConfigValue.bool_val', index=2,
      number=3, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='float_val', full_name='carbon.config.proto.ConfigValue.float_val', index=3,
      number=4, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='string_val', full_name='carbon.config.proto.ConfigValue.string_val', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='timestamp_ms', full_name='carbon.config.proto.ConfigValue.timestamp_ms', index=5,
      number=6, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='value', full_name='carbon.config.proto.ConfigValue.value',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=116,
  serialized_end=266,
)


_INTCONFIGDEF = _descriptor.Descriptor(
  name='IntConfigDef',
  full_name='carbon.config.proto.IntConfigDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min', full_name='carbon.config.proto.IntConfigDef.min', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max', full_name='carbon.config.proto.IntConfigDef.max', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='step', full_name='carbon.config.proto.IntConfigDef.step', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=268,
  serialized_end=322,
)


_UINTCONFIGDEF = _descriptor.Descriptor(
  name='UIntConfigDef',
  full_name='carbon.config.proto.UIntConfigDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min', full_name='carbon.config.proto.UIntConfigDef.min', index=0,
      number=1, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max', full_name='carbon.config.proto.UIntConfigDef.max', index=1,
      number=2, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='step', full_name='carbon.config.proto.UIntConfigDef.step', index=2,
      number=3, type=4, cpp_type=4, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=324,
  serialized_end=379,
)


_FLOATCONFIGDEF = _descriptor.Descriptor(
  name='FloatConfigDef',
  full_name='carbon.config.proto.FloatConfigDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min', full_name='carbon.config.proto.FloatConfigDef.min', index=0,
      number=1, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max', full_name='carbon.config.proto.FloatConfigDef.max', index=1,
      number=2, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='step', full_name='carbon.config.proto.FloatConfigDef.step', index=2,
      number=3, type=1, cpp_type=5, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=381,
  serialized_end=437,
)


_STRINGCONFIGDEF = _descriptor.Descriptor(
  name='StringConfigDef',
  full_name='carbon.config.proto.StringConfigDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='size_limit', full_name='carbon.config.proto.StringConfigDef.size_limit', index=0,
      number=1, type=13, cpp_type=3, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='choices', full_name='carbon.config.proto.StringConfigDef.choices', index=1,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=439,
  serialized_end=493,
)


_CONFIGDEF = _descriptor.Descriptor(
  name='ConfigDef',
  full_name='carbon.config.proto.ConfigDef',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='type', full_name='carbon.config.proto.ConfigDef.type', index=0,
      number=1, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='complexity', full_name='carbon.config.proto.ConfigDef.complexity', index=1,
      number=2, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='int_def', full_name='carbon.config.proto.ConfigDef.int_def', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='uint_def', full_name='carbon.config.proto.ConfigDef.uint_def', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='float_def', full_name='carbon.config.proto.ConfigDef.float_def', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='string_def', full_name='carbon.config.proto.ConfigDef.string_def', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='hint', full_name='carbon.config.proto.ConfigDef.hint', index=6,
      number=7, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='default_recommended', full_name='carbon.config.proto.ConfigDef.default_recommended', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='units', full_name='carbon.config.proto.ConfigDef.units', index=8,
      number=9, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='extra', full_name='carbon.config.proto.ConfigDef.extra',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=496,
  serialized_end=908,
)


_CONFIGNODE = _descriptor.Descriptor(
  name='ConfigNode',
  full_name='carbon.config.proto.ConfigNode',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.config.proto.ConfigNode.name', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.config.proto.ConfigNode.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='def', full_name='carbon.config.proto.ConfigNode.def', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='children', full_name='carbon.config.proto.ConfigNode.children', index=3,
      number=4, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=911,
  serialized_end=1082,
)


_CONFIGLEAF = _descriptor.Descriptor(
  name='ConfigLeaf',
  full_name='carbon.config.proto.ConfigLeaf',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.config.proto.ConfigLeaf.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.config.proto.ConfigLeaf.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1084,
  serialized_end=1158,
)


_SETVALUEREQUEST = _descriptor.Descriptor(
  name='SetValueRequest',
  full_name='carbon.config.proto.SetValueRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.config.proto.SetValueRequest.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.config.proto.SetValueRequest.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1160,
  serialized_end=1239,
)


_SETVALUERESPONSE = _descriptor.Descriptor(
  name='SetValueResponse',
  full_name='carbon.config.proto.SetValueResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1241,
  serialized_end=1259,
)


_GETTREEREQUEST = _descriptor.Descriptor(
  name='GetTreeRequest',
  full_name='carbon.config.proto.GetTreeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.config.proto.GetTreeRequest.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1261,
  serialized_end=1290,
)


_GETTREERESPONSE = _descriptor.Descriptor(
  name='GetTreeResponse',
  full_name='carbon.config.proto.GetTreeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='node', full_name='carbon.config.proto.GetTreeResponse.node', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1292,
  serialized_end=1356,
)


_SETTREEREQUEST = _descriptor.Descriptor(
  name='SetTreeRequest',
  full_name='carbon.config.proto.SetTreeRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.config.proto.SetTreeRequest.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='node', full_name='carbon.config.proto.SetTreeRequest.node', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1358,
  serialized_end=1434,
)


_SETTREERESPONSE = _descriptor.Descriptor(
  name='SetTreeResponse',
  full_name='carbon.config.proto.SetTreeResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1436,
  serialized_end=1453,
)


_GETLEAVESREQUEST = _descriptor.Descriptor(
  name='GetLeavesRequest',
  full_name='carbon.config.proto.GetLeavesRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.config.proto.GetLeavesRequest.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1455,
  serialized_end=1486,
)


_GETLEAVESRESPONSE = _descriptor.Descriptor(
  name='GetLeavesResponse',
  full_name='carbon.config.proto.GetLeavesResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='leaves', full_name='carbon.config.proto.GetLeavesResponse.leaves', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1488,
  serialized_end=1556,
)


_ADDTOLISTREQUEST = _descriptor.Descriptor(
  name='AddToListRequest',
  full_name='carbon.config.proto.AddToListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.config.proto.AddToListRequest.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.config.proto.AddToListRequest.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1558,
  serialized_end=1603,
)


_ADDTOLISTRESPONSE = _descriptor.Descriptor(
  name='AddToListResponse',
  full_name='carbon.config.proto.AddToListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1605,
  serialized_end=1624,
)


_REMOVEFROMLISTREQUEST = _descriptor.Descriptor(
  name='RemoveFromListRequest',
  full_name='carbon.config.proto.RemoveFromListRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.config.proto.RemoveFromListRequest.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='name', full_name='carbon.config.proto.RemoveFromListRequest.name', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1626,
  serialized_end=1676,
)


_REMOVEFROMLISTRESPONSE = _descriptor.Descriptor(
  name='RemoveFromListResponse',
  full_name='carbon.config.proto.RemoveFromListResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1678,
  serialized_end=1702,
)


_SUBSCRIPTIONREQUEST = _descriptor.Descriptor(
  name='SubscriptionRequest',
  full_name='carbon.config.proto.SubscriptionRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='keys', full_name='carbon.config.proto.SubscriptionRequest.keys', index=0,
      number=1, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1704,
  serialized_end=1739,
)


_SUBSCRIPTIONNOTIFYMESSAGE = _descriptor.Descriptor(
  name='SubscriptionNotifyMessage',
  full_name='carbon.config.proto.SubscriptionNotifyMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='subscription_key', full_name='carbon.config.proto.SubscriptionNotifyMessage.subscription_key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='notify_key', full_name='carbon.config.proto.SubscriptionNotifyMessage.notify_key', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1741,
  serialized_end=1814,
)


_UPGRADECLOUDCONFIGREQUEST = _descriptor.Descriptor(
  name='UpgradeCloudConfigRequest',
  full_name='carbon.config.proto.UpgradeCloudConfigRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot', full_name='carbon.config.proto.UpgradeCloudConfigRequest.robot', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='to_template', full_name='carbon.config.proto.UpgradeCloudConfigRequest.to_template', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='from_template', full_name='carbon.config.proto.UpgradeCloudConfigRequest.from_template', index=2,
      number=3, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1816,
  serialized_end=1902,
)


_UPGRADECLOUDCONFIGRESPONSE = _descriptor.Descriptor(
  name='UpgradeCloudConfigResponse',
  full_name='carbon.config.proto.UpgradeCloudConfigResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1904,
  serialized_end=1932,
)

_CONFIGVALUE.oneofs_by_name['value'].fields.append(
  _CONFIGVALUE.fields_by_name['int64_val'])
_CONFIGVALUE.fields_by_name['int64_val'].containing_oneof = _CONFIGVALUE.oneofs_by_name['value']
_CONFIGVALUE.oneofs_by_name['value'].fields.append(
  _CONFIGVALUE.fields_by_name['uint64_val'])
_CONFIGVALUE.fields_by_name['uint64_val'].containing_oneof = _CONFIGVALUE.oneofs_by_name['value']
_CONFIGVALUE.oneofs_by_name['value'].fields.append(
  _CONFIGVALUE.fields_by_name['bool_val'])
_CONFIGVALUE.fields_by_name['bool_val'].containing_oneof = _CONFIGVALUE.oneofs_by_name['value']
_CONFIGVALUE.oneofs_by_name['value'].fields.append(
  _CONFIGVALUE.fields_by_name['float_val'])
_CONFIGVALUE.fields_by_name['float_val'].containing_oneof = _CONFIGVALUE.oneofs_by_name['value']
_CONFIGVALUE.oneofs_by_name['value'].fields.append(
  _CONFIGVALUE.fields_by_name['string_val'])
_CONFIGVALUE.fields_by_name['string_val'].containing_oneof = _CONFIGVALUE.oneofs_by_name['value']
_CONFIGDEF.fields_by_name['type'].enum_type = _CONFIGTYPE
_CONFIGDEF.fields_by_name['complexity'].enum_type = _CONFIGCOMPLEXITY
_CONFIGDEF.fields_by_name['int_def'].message_type = _INTCONFIGDEF
_CONFIGDEF.fields_by_name['uint_def'].message_type = _UINTCONFIGDEF
_CONFIGDEF.fields_by_name['float_def'].message_type = _FLOATCONFIGDEF
_CONFIGDEF.fields_by_name['string_def'].message_type = _STRINGCONFIGDEF
_CONFIGDEF.oneofs_by_name['extra'].fields.append(
  _CONFIGDEF.fields_by_name['int_def'])
_CONFIGDEF.fields_by_name['int_def'].containing_oneof = _CONFIGDEF.oneofs_by_name['extra']
_CONFIGDEF.oneofs_by_name['extra'].fields.append(
  _CONFIGDEF.fields_by_name['uint_def'])
_CONFIGDEF.fields_by_name['uint_def'].containing_oneof = _CONFIGDEF.oneofs_by_name['extra']
_CONFIGDEF.oneofs_by_name['extra'].fields.append(
  _CONFIGDEF.fields_by_name['float_def'])
_CONFIGDEF.fields_by_name['float_def'].containing_oneof = _CONFIGDEF.oneofs_by_name['extra']
_CONFIGDEF.oneofs_by_name['extra'].fields.append(
  _CONFIGDEF.fields_by_name['string_def'])
_CONFIGDEF.fields_by_name['string_def'].containing_oneof = _CONFIGDEF.oneofs_by_name['extra']
_CONFIGNODE.fields_by_name['value'].message_type = _CONFIGVALUE
_CONFIGNODE.fields_by_name['def'].message_type = _CONFIGDEF
_CONFIGNODE.fields_by_name['children'].message_type = _CONFIGNODE
_CONFIGLEAF.fields_by_name['value'].message_type = _CONFIGVALUE
_SETVALUEREQUEST.fields_by_name['value'].message_type = _CONFIGVALUE
_GETTREERESPONSE.fields_by_name['node'].message_type = _CONFIGNODE
_SETTREEREQUEST.fields_by_name['node'].message_type = _CONFIGNODE
_GETLEAVESRESPONSE.fields_by_name['leaves'].message_type = _CONFIGLEAF
DESCRIPTOR.message_types_by_name['PingRequest'] = _PINGREQUEST
DESCRIPTOR.message_types_by_name['PongResponse'] = _PONGRESPONSE
DESCRIPTOR.message_types_by_name['ConfigValue'] = _CONFIGVALUE
DESCRIPTOR.message_types_by_name['IntConfigDef'] = _INTCONFIGDEF
DESCRIPTOR.message_types_by_name['UIntConfigDef'] = _UINTCONFIGDEF
DESCRIPTOR.message_types_by_name['FloatConfigDef'] = _FLOATCONFIGDEF
DESCRIPTOR.message_types_by_name['StringConfigDef'] = _STRINGCONFIGDEF
DESCRIPTOR.message_types_by_name['ConfigDef'] = _CONFIGDEF
DESCRIPTOR.message_types_by_name['ConfigNode'] = _CONFIGNODE
DESCRIPTOR.message_types_by_name['ConfigLeaf'] = _CONFIGLEAF
DESCRIPTOR.message_types_by_name['SetValueRequest'] = _SETVALUEREQUEST
DESCRIPTOR.message_types_by_name['SetValueResponse'] = _SETVALUERESPONSE
DESCRIPTOR.message_types_by_name['GetTreeRequest'] = _GETTREEREQUEST
DESCRIPTOR.message_types_by_name['GetTreeResponse'] = _GETTREERESPONSE
DESCRIPTOR.message_types_by_name['SetTreeRequest'] = _SETTREEREQUEST
DESCRIPTOR.message_types_by_name['SetTreeResponse'] = _SETTREERESPONSE
DESCRIPTOR.message_types_by_name['GetLeavesRequest'] = _GETLEAVESREQUEST
DESCRIPTOR.message_types_by_name['GetLeavesResponse'] = _GETLEAVESRESPONSE
DESCRIPTOR.message_types_by_name['AddToListRequest'] = _ADDTOLISTREQUEST
DESCRIPTOR.message_types_by_name['AddToListResponse'] = _ADDTOLISTRESPONSE
DESCRIPTOR.message_types_by_name['RemoveFromListRequest'] = _REMOVEFROMLISTREQUEST
DESCRIPTOR.message_types_by_name['RemoveFromListResponse'] = _REMOVEFROMLISTRESPONSE
DESCRIPTOR.message_types_by_name['SubscriptionRequest'] = _SUBSCRIPTIONREQUEST
DESCRIPTOR.message_types_by_name['SubscriptionNotifyMessage'] = _SUBSCRIPTIONNOTIFYMESSAGE
DESCRIPTOR.message_types_by_name['UpgradeCloudConfigRequest'] = _UPGRADECLOUDCONFIGREQUEST
DESCRIPTOR.message_types_by_name['UpgradeCloudConfigResponse'] = _UPGRADECLOUDCONFIGRESPONSE
DESCRIPTOR.enum_types_by_name['ConfigType'] = _CONFIGTYPE
DESCRIPTOR.enum_types_by_name['ConfigComplexity'] = _CONFIGCOMPLEXITY
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

PingRequest = _reflection.GeneratedProtocolMessageType('PingRequest', (_message.Message,), {
  'DESCRIPTOR' : _PINGREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.PingRequest)
  })
_sym_db.RegisterMessage(PingRequest)

PongResponse = _reflection.GeneratedProtocolMessageType('PongResponse', (_message.Message,), {
  'DESCRIPTOR' : _PONGRESPONSE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.PongResponse)
  })
_sym_db.RegisterMessage(PongResponse)

ConfigValue = _reflection.GeneratedProtocolMessageType('ConfigValue', (_message.Message,), {
  'DESCRIPTOR' : _CONFIGVALUE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.ConfigValue)
  })
_sym_db.RegisterMessage(ConfigValue)

IntConfigDef = _reflection.GeneratedProtocolMessageType('IntConfigDef', (_message.Message,), {
  'DESCRIPTOR' : _INTCONFIGDEF,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.IntConfigDef)
  })
_sym_db.RegisterMessage(IntConfigDef)

UIntConfigDef = _reflection.GeneratedProtocolMessageType('UIntConfigDef', (_message.Message,), {
  'DESCRIPTOR' : _UINTCONFIGDEF,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.UIntConfigDef)
  })
_sym_db.RegisterMessage(UIntConfigDef)

FloatConfigDef = _reflection.GeneratedProtocolMessageType('FloatConfigDef', (_message.Message,), {
  'DESCRIPTOR' : _FLOATCONFIGDEF,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.FloatConfigDef)
  })
_sym_db.RegisterMessage(FloatConfigDef)

StringConfigDef = _reflection.GeneratedProtocolMessageType('StringConfigDef', (_message.Message,), {
  'DESCRIPTOR' : _STRINGCONFIGDEF,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.StringConfigDef)
  })
_sym_db.RegisterMessage(StringConfigDef)

ConfigDef = _reflection.GeneratedProtocolMessageType('ConfigDef', (_message.Message,), {
  'DESCRIPTOR' : _CONFIGDEF,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.ConfigDef)
  })
_sym_db.RegisterMessage(ConfigDef)

ConfigNode = _reflection.GeneratedProtocolMessageType('ConfigNode', (_message.Message,), {
  'DESCRIPTOR' : _CONFIGNODE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.ConfigNode)
  })
_sym_db.RegisterMessage(ConfigNode)

ConfigLeaf = _reflection.GeneratedProtocolMessageType('ConfigLeaf', (_message.Message,), {
  'DESCRIPTOR' : _CONFIGLEAF,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.ConfigLeaf)
  })
_sym_db.RegisterMessage(ConfigLeaf)

SetValueRequest = _reflection.GeneratedProtocolMessageType('SetValueRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETVALUEREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.SetValueRequest)
  })
_sym_db.RegisterMessage(SetValueRequest)

SetValueResponse = _reflection.GeneratedProtocolMessageType('SetValueResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETVALUERESPONSE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.SetValueResponse)
  })
_sym_db.RegisterMessage(SetValueResponse)

GetTreeRequest = _reflection.GeneratedProtocolMessageType('GetTreeRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETTREEREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.GetTreeRequest)
  })
_sym_db.RegisterMessage(GetTreeRequest)

GetTreeResponse = _reflection.GeneratedProtocolMessageType('GetTreeResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETTREERESPONSE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.GetTreeResponse)
  })
_sym_db.RegisterMessage(GetTreeResponse)

SetTreeRequest = _reflection.GeneratedProtocolMessageType('SetTreeRequest', (_message.Message,), {
  'DESCRIPTOR' : _SETTREEREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.SetTreeRequest)
  })
_sym_db.RegisterMessage(SetTreeRequest)

SetTreeResponse = _reflection.GeneratedProtocolMessageType('SetTreeResponse', (_message.Message,), {
  'DESCRIPTOR' : _SETTREERESPONSE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.SetTreeResponse)
  })
_sym_db.RegisterMessage(SetTreeResponse)

GetLeavesRequest = _reflection.GeneratedProtocolMessageType('GetLeavesRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETLEAVESREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.GetLeavesRequest)
  })
_sym_db.RegisterMessage(GetLeavesRequest)

GetLeavesResponse = _reflection.GeneratedProtocolMessageType('GetLeavesResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETLEAVESRESPONSE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.GetLeavesResponse)
  })
_sym_db.RegisterMessage(GetLeavesResponse)

AddToListRequest = _reflection.GeneratedProtocolMessageType('AddToListRequest', (_message.Message,), {
  'DESCRIPTOR' : _ADDTOLISTREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.AddToListRequest)
  })
_sym_db.RegisterMessage(AddToListRequest)

AddToListResponse = _reflection.GeneratedProtocolMessageType('AddToListResponse', (_message.Message,), {
  'DESCRIPTOR' : _ADDTOLISTRESPONSE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.AddToListResponse)
  })
_sym_db.RegisterMessage(AddToListResponse)

RemoveFromListRequest = _reflection.GeneratedProtocolMessageType('RemoveFromListRequest', (_message.Message,), {
  'DESCRIPTOR' : _REMOVEFROMLISTREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.RemoveFromListRequest)
  })
_sym_db.RegisterMessage(RemoveFromListRequest)

RemoveFromListResponse = _reflection.GeneratedProtocolMessageType('RemoveFromListResponse', (_message.Message,), {
  'DESCRIPTOR' : _REMOVEFROMLISTRESPONSE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.RemoveFromListResponse)
  })
_sym_db.RegisterMessage(RemoveFromListResponse)

SubscriptionRequest = _reflection.GeneratedProtocolMessageType('SubscriptionRequest', (_message.Message,), {
  'DESCRIPTOR' : _SUBSCRIPTIONREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.SubscriptionRequest)
  })
_sym_db.RegisterMessage(SubscriptionRequest)

SubscriptionNotifyMessage = _reflection.GeneratedProtocolMessageType('SubscriptionNotifyMessage', (_message.Message,), {
  'DESCRIPTOR' : _SUBSCRIPTIONNOTIFYMESSAGE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.SubscriptionNotifyMessage)
  })
_sym_db.RegisterMessage(SubscriptionNotifyMessage)

UpgradeCloudConfigRequest = _reflection.GeneratedProtocolMessageType('UpgradeCloudConfigRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPGRADECLOUDCONFIGREQUEST,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.UpgradeCloudConfigRequest)
  })
_sym_db.RegisterMessage(UpgradeCloudConfigRequest)

UpgradeCloudConfigResponse = _reflection.GeneratedProtocolMessageType('UpgradeCloudConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPGRADECLOUDCONFIGRESPONSE,
  '__module__' : 'config.api.proto.config_service_pb2'
  # @@protoc_insertion_point(class_scope:carbon.config.proto.UpgradeCloudConfigResponse)
  })
_sym_db.RegisterMessage(UpgradeCloudConfigResponse)


DESCRIPTOR._options = None

_CONFIGSERVICE = _descriptor.ServiceDescriptor(
  name='ConfigService',
  full_name='carbon.config.proto.ConfigService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=2092,
  serialized_end=2855,
  methods=[
  _descriptor.MethodDescriptor(
    name='Ping',
    full_name='carbon.config.proto.ConfigService.Ping',
    index=0,
    containing_service=None,
    input_type=_PINGREQUEST,
    output_type=_PONGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetValue',
    full_name='carbon.config.proto.ConfigService.SetValue',
    index=1,
    containing_service=None,
    input_type=_SETVALUEREQUEST,
    output_type=_SETVALUERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetTree',
    full_name='carbon.config.proto.ConfigService.GetTree',
    index=2,
    containing_service=None,
    input_type=_GETTREEREQUEST,
    output_type=_GETTREERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='SetTree',
    full_name='carbon.config.proto.ConfigService.SetTree',
    index=3,
    containing_service=None,
    input_type=_SETTREEREQUEST,
    output_type=_SETTREERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetLeaves',
    full_name='carbon.config.proto.ConfigService.GetLeaves',
    index=4,
    containing_service=None,
    input_type=_GETLEAVESREQUEST,
    output_type=_GETLEAVESRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='AddToList',
    full_name='carbon.config.proto.ConfigService.AddToList',
    index=5,
    containing_service=None,
    input_type=_ADDTOLISTREQUEST,
    output_type=_ADDTOLISTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='RemoveFromList',
    full_name='carbon.config.proto.ConfigService.RemoveFromList',
    index=6,
    containing_service=None,
    input_type=_REMOVEFROMLISTREQUEST,
    output_type=_REMOVEFROMLISTRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UpgradeCloudConfig',
    full_name='carbon.config.proto.ConfigService.UpgradeCloudConfig',
    index=7,
    containing_service=None,
    input_type=_UPGRADECLOUDCONFIGREQUEST,
    output_type=_UPGRADECLOUDCONFIGRESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CONFIGSERVICE)

DESCRIPTOR.services_by_name['ConfigService'] = _CONFIGSERVICE


_CONFIGNOTIFICATIONSERVICE = _descriptor.ServiceDescriptor(
  name='ConfigNotificationService',
  full_name='carbon.config.proto.ConfigNotificationService',
  file=DESCRIPTOR,
  index=1,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=2858,
  serialized_end=2990,
  methods=[
  _descriptor.MethodDescriptor(
    name='Subscribe',
    full_name='carbon.config.proto.ConfigNotificationService.Subscribe',
    index=0,
    containing_service=None,
    input_type=_SUBSCRIPTIONREQUEST,
    output_type=_SUBSCRIPTIONNOTIFYMESSAGE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_CONFIGNOTIFICATIONSERVICE)

DESCRIPTOR.services_by_name['ConfigNotificationService'] = _CONFIGNOTIFICATIONSERVICE

# @@protoc_insertion_point(module_scope)

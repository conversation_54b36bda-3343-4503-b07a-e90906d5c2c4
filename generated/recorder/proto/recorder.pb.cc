// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: recorder/proto/recorder.proto

#include "recorder/proto/recorder.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace recorder {
constexpr DetectionClass::DetectionClass(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : class__(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , score_(0){}
struct DetectionClassDefaultTypeInternal {
  constexpr DetectionClassDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DetectionClassDefaultTypeInternal() {}
  union {
    DetectionClass _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DetectionClassDefaultTypeInternal _DetectionClass_default_instance_;
constexpr DeepweedDetection::DeepweedDetection(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : detection_classes_()
  , mask_intersections_()
  , x_(0)
  , y_(0)
  , size_(0)
  , score_(0)
  , hit_class_(0)

  , trajectory_id_(0u)
  , weed_score_(0)
  , crop_score_(0){}
struct DeepweedDetectionDefaultTypeInternal {
  constexpr DeepweedDetectionDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeepweedDetectionDefaultTypeInternal() {}
  union {
    DeepweedDetection _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeepweedDetectionDefaultTypeInternal _DeepweedDetection_default_instance_;
constexpr DeepweedPredictionFrame::DeepweedPredictionFrame(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : detections_()
  , timestamp_ms_(int64_t{0}){}
struct DeepweedPredictionFrameDefaultTypeInternal {
  constexpr DeepweedPredictionFrameDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeepweedPredictionFrameDefaultTypeInternal() {}
  union {
    DeepweedPredictionFrame _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeepweedPredictionFrameDefaultTypeInternal _DeepweedPredictionFrame_default_instance_;
constexpr DeepweedPredictionRecord::DeepweedPredictionRecord(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : frame_(nullptr)
  , record_timestamp_ms_(uint64_t{0u}){}
struct DeepweedPredictionRecordDefaultTypeInternal {
  constexpr DeepweedPredictionRecordDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~DeepweedPredictionRecordDefaultTypeInternal() {}
  union {
    DeepweedPredictionRecord _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT DeepweedPredictionRecordDefaultTypeInternal _DeepweedPredictionRecord_default_instance_;
constexpr LaneHeightSnapshot::LaneHeightSnapshot(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : weed_height_()
  , crop_height_(){}
struct LaneHeightSnapshotDefaultTypeInternal {
  constexpr LaneHeightSnapshotDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaneHeightSnapshotDefaultTypeInternal() {}
  union {
    LaneHeightSnapshot _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaneHeightSnapshotDefaultTypeInternal _LaneHeightSnapshot_default_instance_;
constexpr LaneHeightRecord::LaneHeightRecord(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : snapshot_(nullptr)
  , record_timestamp_ms_(uint64_t{0u}){}
struct LaneHeightRecordDefaultTypeInternal {
  constexpr LaneHeightRecordDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~LaneHeightRecordDefaultTypeInternal() {}
  union {
    LaneHeightRecord _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT LaneHeightRecordDefaultTypeInternal _LaneHeightRecord_default_instance_;
constexpr RotaryTicksSnapshot::RotaryTicksSnapshot(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : timestamp_us_(uint64_t{0u})
  , fl_(0)
  , fr_(0)
  , bl_(0)
  , br_(0)
  , fl_enabled_(false)
  , fr_enabled_(false)
  , bl_enabled_(false)
  , br_enabled_(false){}
struct RotaryTicksSnapshotDefaultTypeInternal {
  constexpr RotaryTicksSnapshotDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RotaryTicksSnapshotDefaultTypeInternal() {}
  union {
    RotaryTicksSnapshot _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RotaryTicksSnapshotDefaultTypeInternal _RotaryTicksSnapshot_default_instance_;
constexpr RotaryTicksRecord::RotaryTicksRecord(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : snapshot_(nullptr)
  , record_timestamp_ms_(uint64_t{0u}){}
struct RotaryTicksRecordDefaultTypeInternal {
  constexpr RotaryTicksRecordDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~RotaryTicksRecordDefaultTypeInternal() {}
  union {
    RotaryTicksRecord _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT RotaryTicksRecordDefaultTypeInternal _RotaryTicksRecord_default_instance_;
}  // namespace recorder
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_recorder_2fproto_2frecorder_2eproto[8];
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_recorder_2fproto_2frecorder_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_recorder_2fproto_2frecorder_2eproto = nullptr;

const uint32_t TableStruct_recorder_2fproto_2frecorder_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::DetectionClass, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::DetectionClass, class__),
  PROTOBUF_FIELD_OFFSET(::recorder::DetectionClass, score_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, x_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, y_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, size_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, score_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, hit_class_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, detection_classes_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, mask_intersections_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, trajectory_id_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, weed_score_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedDetection, crop_score_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionFrame, detections_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionRecord, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionRecord, record_timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::recorder::DeepweedPredictionRecord, frame_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightSnapshot, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightSnapshot, weed_height_),
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightSnapshot, crop_height_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightRecord, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightRecord, record_timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::recorder::LaneHeightRecord, snapshot_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, timestamp_us_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, fl_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, fr_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, bl_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, br_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, fl_enabled_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, fr_enabled_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, bl_enabled_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksSnapshot, br_enabled_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksRecord, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksRecord, record_timestamp_ms_),
  PROTOBUF_FIELD_OFFSET(::recorder::RotaryTicksRecord, snapshot_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::recorder::DetectionClass)},
  { 8, -1, -1, sizeof(::recorder::DeepweedDetection)},
  { 24, -1, -1, sizeof(::recorder::DeepweedPredictionFrame)},
  { 32, -1, -1, sizeof(::recorder::DeepweedPredictionRecord)},
  { 40, -1, -1, sizeof(::recorder::LaneHeightSnapshot)},
  { 48, -1, -1, sizeof(::recorder::LaneHeightRecord)},
  { 56, -1, -1, sizeof(::recorder::RotaryTicksSnapshot)},
  { 71, -1, -1, sizeof(::recorder::RotaryTicksRecord)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_DetectionClass_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_DeepweedDetection_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_DeepweedPredictionFrame_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_DeepweedPredictionRecord_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_LaneHeightSnapshot_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_LaneHeightRecord_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_RotaryTicksSnapshot_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::recorder::_RotaryTicksRecord_default_instance_),
};

const char descriptor_table_protodef_recorder_2fproto_2frecorder_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n\035recorder/proto/recorder.proto\022\010recorde"
  "r\".\n\016DetectionClass\022\r\n\005class\030\001 \001(\t\022\r\n\005sc"
  "ore\030\002 \001(\002\"\257\002\n\021DeepweedDetection\022\t\n\001x\030\001 \001"
  "(\002\022\t\n\001y\030\002 \001(\002\022\014\n\004size\030\003 \001(\002\022\r\n\005score\030\004 \001"
  "(\002\0227\n\thit_class\030\005 \001(\0162$.recorder.Deepwee"
  "dDetection.HitClass\0223\n\021detection_classes"
  "\030\006 \003(\0132\030.recorder.DetectionClass\022\032\n\022mask"
  "_intersections\030\007 \003(\014\022\025\n\rtrajectory_id\030\010 "
  "\001(\r\022\022\n\nweed_score\030\t \001(\002\022\022\n\ncrop_score\030\n "
  "\001(\002\"\036\n\010HitClass\022\010\n\004WEED\020\000\022\010\n\004CROP\020\001\"`\n\027D"
  "eepweedPredictionFrame\022\024\n\014timestamp_ms\030\001"
  " \001(\003\022/\n\ndetections\030\002 \003(\0132\033.recorder.Deep"
  "weedDetection\"i\n\030DeepweedPredictionRecor"
  "d\022\033\n\023record_timestamp_ms\030\001 \001(\004\0220\n\005frame\030"
  "\002 \001(\0132!.recorder.DeepweedPredictionFrame"
  "\">\n\022LaneHeightSnapshot\022\023\n\013weed_height\030\001 "
  "\003(\001\022\023\n\013crop_height\030\002 \003(\001\"_\n\020LaneHeightRe"
  "cord\022\033\n\023record_timestamp_ms\030\001 \001(\004\022.\n\010sna"
  "pshot\030\002 \001(\0132\034.recorder.LaneHeightSnapsho"
  "t\"\253\001\n\023RotaryTicksSnapshot\022\024\n\014timestamp_u"
  "s\030\001 \001(\004\022\n\n\002fl\030\002 \001(\005\022\n\n\002fr\030\003 \001(\005\022\n\n\002bl\030\004 "
  "\001(\005\022\n\n\002br\030\005 \001(\005\022\022\n\nfl_enabled\030\006 \001(\010\022\022\n\nf"
  "r_enabled\030\007 \001(\010\022\022\n\nbl_enabled\030\010 \001(\010\022\022\n\nb"
  "r_enabled\030\t \001(\010\"a\n\021RotaryTicksRecord\022\033\n\023"
  "record_timestamp_ms\030\001 \001(\004\022/\n\010snapshot\030\002 "
  "\001(\0132\035.recorder.RotaryTicksSnapshotB\020Z\016pr"
  "oto/recorderb\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_recorder_2fproto_2frecorder_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_recorder_2fproto_2frecorder_2eproto = {
  false, false, 1060, descriptor_table_protodef_recorder_2fproto_2frecorder_2eproto, "recorder/proto/recorder.proto", 
  &descriptor_table_recorder_2fproto_2frecorder_2eproto_once, nullptr, 0, 8,
  schemas, file_default_instances, TableStruct_recorder_2fproto_2frecorder_2eproto::offsets,
  file_level_metadata_recorder_2fproto_2frecorder_2eproto, file_level_enum_descriptors_recorder_2fproto_2frecorder_2eproto, file_level_service_descriptors_recorder_2fproto_2frecorder_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_recorder_2fproto_2frecorder_2eproto_getter() {
  return &descriptor_table_recorder_2fproto_2frecorder_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_recorder_2fproto_2frecorder_2eproto(&descriptor_table_recorder_2fproto_2frecorder_2eproto);
namespace recorder {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DeepweedDetection_HitClass_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_recorder_2fproto_2frecorder_2eproto);
  return file_level_enum_descriptors_recorder_2fproto_2frecorder_2eproto[0];
}
bool DeepweedDetection_HitClass_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
      return true;
    default:
      return false;
  }
}

#if (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))
constexpr DeepweedDetection_HitClass DeepweedDetection::WEED;
constexpr DeepweedDetection_HitClass DeepweedDetection::CROP;
constexpr DeepweedDetection_HitClass DeepweedDetection::HitClass_MIN;
constexpr DeepweedDetection_HitClass DeepweedDetection::HitClass_MAX;
constexpr int DeepweedDetection::HitClass_ARRAYSIZE;
#endif  // (__cplusplus < 201703) && (!defined(_MSC_VER) || (_MSC_VER >= 1900 && _MSC_VER < 1912))

// ===================================================================

class DetectionClass::_Internal {
 public:
};

DetectionClass::DetectionClass(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.DetectionClass)
}
DetectionClass::DetectionClass(const DetectionClass& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  class__.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    class__.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_class_().empty()) {
    class__.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_class_(), 
      GetArenaForAllocation());
  }
  score_ = from.score_;
  // @@protoc_insertion_point(copy_constructor:recorder.DetectionClass)
}

inline void DetectionClass::SharedCtor() {
class__.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  class__.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
score_ = 0;
}

DetectionClass::~DetectionClass() {
  // @@protoc_insertion_point(destructor:recorder.DetectionClass)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DetectionClass::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  class__.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
}

void DetectionClass::ArenaDtor(void* object) {
  DetectionClass* _this = reinterpret_cast< DetectionClass* >(object);
  (void)_this;
}
void DetectionClass::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DetectionClass::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DetectionClass::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.DetectionClass)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  class__.ClearToEmpty();
  score_ = 0;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DetectionClass::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string class = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_class_();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "recorder.DetectionClass.class"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float score = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DetectionClass::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.DetectionClass)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string class = 1;
  if (!this->_internal_class_().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_class_().data(), static_cast<int>(this->_internal_class_().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "recorder.DetectionClass.class");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_class_(), target);
  }

  // float score = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_score(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.DetectionClass)
  return target;
}

size_t DetectionClass::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.DetectionClass)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // string class = 1;
  if (!this->_internal_class_().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_class_());
  }

  // float score = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DetectionClass::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DetectionClass::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DetectionClass::GetClassData() const { return &_class_data_; }

void DetectionClass::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DetectionClass *>(to)->MergeFrom(
      static_cast<const DetectionClass &>(from));
}


void DetectionClass::MergeFrom(const DetectionClass& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.DetectionClass)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (!from._internal_class_().empty()) {
    _internal_set_class_(from._internal_class_());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = from._internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    _internal_set_score(from._internal_score());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DetectionClass::CopyFrom(const DetectionClass& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.DetectionClass)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DetectionClass::IsInitialized() const {
  return true;
}

void DetectionClass::InternalSwap(DetectionClass* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &class__, lhs_arena,
      &other->class__, rhs_arena
  );
  swap(score_, other->score_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DetectionClass::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[0]);
}

// ===================================================================

class DeepweedDetection::_Internal {
 public:
};

DeepweedDetection::DeepweedDetection(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  detection_classes_(arena),
  mask_intersections_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.DeepweedDetection)
}
DeepweedDetection::DeepweedDetection(const DeepweedDetection& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      detection_classes_(from.detection_classes_),
      mask_intersections_(from.mask_intersections_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&x_, &from.x_,
    static_cast<size_t>(reinterpret_cast<char*>(&crop_score_) -
    reinterpret_cast<char*>(&x_)) + sizeof(crop_score_));
  // @@protoc_insertion_point(copy_constructor:recorder.DeepweedDetection)
}

inline void DeepweedDetection::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&x_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&crop_score_) -
    reinterpret_cast<char*>(&x_)) + sizeof(crop_score_));
}

DeepweedDetection::~DeepweedDetection() {
  // @@protoc_insertion_point(destructor:recorder.DeepweedDetection)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeepweedDetection::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DeepweedDetection::ArenaDtor(void* object) {
  DeepweedDetection* _this = reinterpret_cast< DeepweedDetection* >(object);
  (void)_this;
}
void DeepweedDetection::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeepweedDetection::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeepweedDetection::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.DeepweedDetection)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  detection_classes_.Clear();
  mask_intersections_.Clear();
  ::memset(&x_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&crop_score_) -
      reinterpret_cast<char*>(&x_)) + sizeof(crop_score_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeepweedDetection::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float x = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          x_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float y = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          y_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float size = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          size_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float score = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .recorder.DeepweedDetection.HitClass hit_class = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_hit_class(static_cast<::recorder::DeepweedDetection_HitClass>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated .recorder.DetectionClass detection_classes = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_detection_classes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated bytes mask_intersections = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_mask_intersections();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // uint32 trajectory_id = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          trajectory_id_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float weed_score = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          weed_score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float crop_score = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 85)) {
          crop_score_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeepweedDetection::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.DeepweedDetection)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_x(), target);
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_y(), target);
  }

  // float size = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_size = this->_internal_size();
  uint32_t raw_size;
  memcpy(&raw_size, &tmp_size, sizeof(tmp_size));
  if (raw_size != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_size(), target);
  }

  // float score = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_score(), target);
  }

  // .recorder.DeepweedDetection.HitClass hit_class = 5;
  if (this->_internal_hit_class() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->_internal_hit_class(), target);
  }

  // repeated .recorder.DetectionClass detection_classes = 6;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_detection_classes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(6, this->_internal_detection_classes(i), target, stream);
  }

  // repeated bytes mask_intersections = 7;
  for (int i = 0, n = this->_internal_mask_intersections_size(); i < n; i++) {
    const auto& s = this->_internal_mask_intersections(i);
    target = stream->WriteBytes(7, s, target);
  }

  // uint32 trajectory_id = 8;
  if (this->_internal_trajectory_id() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt32ToArray(8, this->_internal_trajectory_id(), target);
  }

  // float weed_score = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = this->_internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_weed_score(), target);
  }

  // float crop_score = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = this->_internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(10, this->_internal_crop_score(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.DeepweedDetection)
  return target;
}

size_t DeepweedDetection::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.DeepweedDetection)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .recorder.DetectionClass detection_classes = 6;
  total_size += 1UL * this->_internal_detection_classes_size();
  for (const auto& msg : this->detection_classes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated bytes mask_intersections = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(mask_intersections_.size());
  for (int i = 0, n = mask_intersections_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
      mask_intersections_.Get(i));
  }

  // float x = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = this->_internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    total_size += 1 + 4;
  }

  // float y = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = this->_internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    total_size += 1 + 4;
  }

  // float size = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_size = this->_internal_size();
  uint32_t raw_size;
  memcpy(&raw_size, &tmp_size, sizeof(tmp_size));
  if (raw_size != 0) {
    total_size += 1 + 4;
  }

  // float score = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = this->_internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    total_size += 1 + 4;
  }

  // .recorder.DeepweedDetection.HitClass hit_class = 5;
  if (this->_internal_hit_class() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_hit_class());
  }

  // uint32 trajectory_id = 8;
  if (this->_internal_trajectory_id() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt32SizePlusOne(this->_internal_trajectory_id());
  }

  // float weed_score = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = this->_internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    total_size += 1 + 4;
  }

  // float crop_score = 10;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = this->_internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeepweedDetection::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeepweedDetection::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeepweedDetection::GetClassData() const { return &_class_data_; }

void DeepweedDetection::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeepweedDetection *>(to)->MergeFrom(
      static_cast<const DeepweedDetection &>(from));
}


void DeepweedDetection::MergeFrom(const DeepweedDetection& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.DeepweedDetection)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  detection_classes_.MergeFrom(from.detection_classes_);
  mask_intersections_.MergeFrom(from.mask_intersections_);
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_x = from._internal_x();
  uint32_t raw_x;
  memcpy(&raw_x, &tmp_x, sizeof(tmp_x));
  if (raw_x != 0) {
    _internal_set_x(from._internal_x());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_y = from._internal_y();
  uint32_t raw_y;
  memcpy(&raw_y, &tmp_y, sizeof(tmp_y));
  if (raw_y != 0) {
    _internal_set_y(from._internal_y());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_size = from._internal_size();
  uint32_t raw_size;
  memcpy(&raw_size, &tmp_size, sizeof(tmp_size));
  if (raw_size != 0) {
    _internal_set_size(from._internal_size());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_score = from._internal_score();
  uint32_t raw_score;
  memcpy(&raw_score, &tmp_score, sizeof(tmp_score));
  if (raw_score != 0) {
    _internal_set_score(from._internal_score());
  }
  if (from._internal_hit_class() != 0) {
    _internal_set_hit_class(from._internal_hit_class());
  }
  if (from._internal_trajectory_id() != 0) {
    _internal_set_trajectory_id(from._internal_trajectory_id());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_weed_score = from._internal_weed_score();
  uint32_t raw_weed_score;
  memcpy(&raw_weed_score, &tmp_weed_score, sizeof(tmp_weed_score));
  if (raw_weed_score != 0) {
    _internal_set_weed_score(from._internal_weed_score());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_crop_score = from._internal_crop_score();
  uint32_t raw_crop_score;
  memcpy(&raw_crop_score, &tmp_crop_score, sizeof(tmp_crop_score));
  if (raw_crop_score != 0) {
    _internal_set_crop_score(from._internal_crop_score());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeepweedDetection::CopyFrom(const DeepweedDetection& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.DeepweedDetection)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeepweedDetection::IsInitialized() const {
  return true;
}

void DeepweedDetection::InternalSwap(DeepweedDetection* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  detection_classes_.InternalSwap(&other->detection_classes_);
  mask_intersections_.InternalSwap(&other->mask_intersections_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DeepweedDetection, crop_score_)
      + sizeof(DeepweedDetection::crop_score_)
      - PROTOBUF_FIELD_OFFSET(DeepweedDetection, x_)>(
          reinterpret_cast<char*>(&x_),
          reinterpret_cast<char*>(&other->x_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DeepweedDetection::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[1]);
}

// ===================================================================

class DeepweedPredictionFrame::_Internal {
 public:
};

DeepweedPredictionFrame::DeepweedPredictionFrame(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  detections_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.DeepweedPredictionFrame)
}
DeepweedPredictionFrame::DeepweedPredictionFrame(const DeepweedPredictionFrame& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      detections_(from.detections_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  timestamp_ms_ = from.timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:recorder.DeepweedPredictionFrame)
}

inline void DeepweedPredictionFrame::SharedCtor() {
timestamp_ms_ = int64_t{0};
}

DeepweedPredictionFrame::~DeepweedPredictionFrame() {
  // @@protoc_insertion_point(destructor:recorder.DeepweedPredictionFrame)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeepweedPredictionFrame::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void DeepweedPredictionFrame::ArenaDtor(void* object) {
  DeepweedPredictionFrame* _this = reinterpret_cast< DeepweedPredictionFrame* >(object);
  (void)_this;
}
void DeepweedPredictionFrame::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeepweedPredictionFrame::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeepweedPredictionFrame::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.DeepweedPredictionFrame)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  detections_.Clear();
  timestamp_ms_ = int64_t{0};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeepweedPredictionFrame::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .recorder.DeepweedDetection detections = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_detections(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<18>(ptr));
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeepweedPredictionFrame::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.DeepweedPredictionFrame)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_timestamp_ms(), target);
  }

  // repeated .recorder.DeepweedDetection detections = 2;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_detections_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(2, this->_internal_detections(i), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.DeepweedPredictionFrame)
  return target;
}

size_t DeepweedPredictionFrame::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.DeepweedPredictionFrame)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated .recorder.DeepweedDetection detections = 2;
  total_size += 1UL * this->_internal_detections_size();
  for (const auto& msg : this->detections_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // int64 timestamp_ms = 1;
  if (this->_internal_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeepweedPredictionFrame::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeepweedPredictionFrame::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeepweedPredictionFrame::GetClassData() const { return &_class_data_; }

void DeepweedPredictionFrame::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeepweedPredictionFrame *>(to)->MergeFrom(
      static_cast<const DeepweedPredictionFrame &>(from));
}


void DeepweedPredictionFrame::MergeFrom(const DeepweedPredictionFrame& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.DeepweedPredictionFrame)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  detections_.MergeFrom(from.detections_);
  if (from._internal_timestamp_ms() != 0) {
    _internal_set_timestamp_ms(from._internal_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeepweedPredictionFrame::CopyFrom(const DeepweedPredictionFrame& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.DeepweedPredictionFrame)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeepweedPredictionFrame::IsInitialized() const {
  return true;
}

void DeepweedPredictionFrame::InternalSwap(DeepweedPredictionFrame* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  detections_.InternalSwap(&other->detections_);
  swap(timestamp_ms_, other->timestamp_ms_);
}

::PROTOBUF_NAMESPACE_ID::Metadata DeepweedPredictionFrame::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[2]);
}

// ===================================================================

class DeepweedPredictionRecord::_Internal {
 public:
  static const ::recorder::DeepweedPredictionFrame& frame(const DeepweedPredictionRecord* msg);
};

const ::recorder::DeepweedPredictionFrame&
DeepweedPredictionRecord::_Internal::frame(const DeepweedPredictionRecord* msg) {
  return *msg->frame_;
}
DeepweedPredictionRecord::DeepweedPredictionRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.DeepweedPredictionRecord)
}
DeepweedPredictionRecord::DeepweedPredictionRecord(const DeepweedPredictionRecord& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_frame()) {
    frame_ = new ::recorder::DeepweedPredictionFrame(*from.frame_);
  } else {
    frame_ = nullptr;
  }
  record_timestamp_ms_ = from.record_timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:recorder.DeepweedPredictionRecord)
}

inline void DeepweedPredictionRecord::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&frame_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&record_timestamp_ms_) -
    reinterpret_cast<char*>(&frame_)) + sizeof(record_timestamp_ms_));
}

DeepweedPredictionRecord::~DeepweedPredictionRecord() {
  // @@protoc_insertion_point(destructor:recorder.DeepweedPredictionRecord)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void DeepweedPredictionRecord::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete frame_;
}

void DeepweedPredictionRecord::ArenaDtor(void* object) {
  DeepweedPredictionRecord* _this = reinterpret_cast< DeepweedPredictionRecord* >(object);
  (void)_this;
}
void DeepweedPredictionRecord::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void DeepweedPredictionRecord::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void DeepweedPredictionRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.DeepweedPredictionRecord)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && frame_ != nullptr) {
    delete frame_;
  }
  frame_ = nullptr;
  record_timestamp_ms_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* DeepweedPredictionRecord::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 record_timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          record_timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .recorder.DeepweedPredictionFrame frame = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_frame(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* DeepweedPredictionRecord::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.DeepweedPredictionRecord)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_record_timestamp_ms(), target);
  }

  // .recorder.DeepweedPredictionFrame frame = 2;
  if (this->_internal_has_frame()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::frame(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.DeepweedPredictionRecord)
  return target;
}

size_t DeepweedPredictionRecord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.DeepweedPredictionRecord)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .recorder.DeepweedPredictionFrame frame = 2;
  if (this->_internal_has_frame()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *frame_);
  }

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_record_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData DeepweedPredictionRecord::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    DeepweedPredictionRecord::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*DeepweedPredictionRecord::GetClassData() const { return &_class_data_; }

void DeepweedPredictionRecord::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<DeepweedPredictionRecord *>(to)->MergeFrom(
      static_cast<const DeepweedPredictionRecord &>(from));
}


void DeepweedPredictionRecord::MergeFrom(const DeepweedPredictionRecord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.DeepweedPredictionRecord)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_frame()) {
    _internal_mutable_frame()->::recorder::DeepweedPredictionFrame::MergeFrom(from._internal_frame());
  }
  if (from._internal_record_timestamp_ms() != 0) {
    _internal_set_record_timestamp_ms(from._internal_record_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void DeepweedPredictionRecord::CopyFrom(const DeepweedPredictionRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.DeepweedPredictionRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool DeepweedPredictionRecord::IsInitialized() const {
  return true;
}

void DeepweedPredictionRecord::InternalSwap(DeepweedPredictionRecord* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(DeepweedPredictionRecord, record_timestamp_ms_)
      + sizeof(DeepweedPredictionRecord::record_timestamp_ms_)
      - PROTOBUF_FIELD_OFFSET(DeepweedPredictionRecord, frame_)>(
          reinterpret_cast<char*>(&frame_),
          reinterpret_cast<char*>(&other->frame_));
}

::PROTOBUF_NAMESPACE_ID::Metadata DeepweedPredictionRecord::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[3]);
}

// ===================================================================

class LaneHeightSnapshot::_Internal {
 public:
};

LaneHeightSnapshot::LaneHeightSnapshot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  weed_height_(arena),
  crop_height_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.LaneHeightSnapshot)
}
LaneHeightSnapshot::LaneHeightSnapshot(const LaneHeightSnapshot& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      weed_height_(from.weed_height_),
      crop_height_(from.crop_height_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  // @@protoc_insertion_point(copy_constructor:recorder.LaneHeightSnapshot)
}

inline void LaneHeightSnapshot::SharedCtor() {
}

LaneHeightSnapshot::~LaneHeightSnapshot() {
  // @@protoc_insertion_point(destructor:recorder.LaneHeightSnapshot)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaneHeightSnapshot::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void LaneHeightSnapshot::ArenaDtor(void* object) {
  LaneHeightSnapshot* _this = reinterpret_cast< LaneHeightSnapshot* >(object);
  (void)_this;
}
void LaneHeightSnapshot::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneHeightSnapshot::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaneHeightSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.LaneHeightSnapshot)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  weed_height_.Clear();
  crop_height_.Clear();
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneHeightSnapshot::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // repeated double weed_height = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedDoubleParser(_internal_mutable_weed_height(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 9) {
          _internal_add_weed_height(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      // repeated double crop_height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedDoubleParser(_internal_mutable_crop_height(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 17) {
          _internal_add_crop_height(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<double>(ptr));
          ptr += sizeof(double);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaneHeightSnapshot::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.LaneHeightSnapshot)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // repeated double weed_height = 1;
  if (this->_internal_weed_height_size() > 0) {
    target = stream->WriteFixedPacked(1, _internal_weed_height(), target);
  }

  // repeated double crop_height = 2;
  if (this->_internal_crop_height_size() > 0) {
    target = stream->WriteFixedPacked(2, _internal_crop_height(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.LaneHeightSnapshot)
  return target;
}

size_t LaneHeightSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.LaneHeightSnapshot)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated double weed_height = 1;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_weed_height_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated double crop_height = 2;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_crop_height_size());
    size_t data_size = 8UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaneHeightSnapshot::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaneHeightSnapshot::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaneHeightSnapshot::GetClassData() const { return &_class_data_; }

void LaneHeightSnapshot::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaneHeightSnapshot *>(to)->MergeFrom(
      static_cast<const LaneHeightSnapshot &>(from));
}


void LaneHeightSnapshot::MergeFrom(const LaneHeightSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.LaneHeightSnapshot)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  weed_height_.MergeFrom(from.weed_height_);
  crop_height_.MergeFrom(from.crop_height_);
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaneHeightSnapshot::CopyFrom(const LaneHeightSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.LaneHeightSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneHeightSnapshot::IsInitialized() const {
  return true;
}

void LaneHeightSnapshot::InternalSwap(LaneHeightSnapshot* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  weed_height_.InternalSwap(&other->weed_height_);
  crop_height_.InternalSwap(&other->crop_height_);
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneHeightSnapshot::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[4]);
}

// ===================================================================

class LaneHeightRecord::_Internal {
 public:
  static const ::recorder::LaneHeightSnapshot& snapshot(const LaneHeightRecord* msg);
};

const ::recorder::LaneHeightSnapshot&
LaneHeightRecord::_Internal::snapshot(const LaneHeightRecord* msg) {
  return *msg->snapshot_;
}
LaneHeightRecord::LaneHeightRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.LaneHeightRecord)
}
LaneHeightRecord::LaneHeightRecord(const LaneHeightRecord& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_snapshot()) {
    snapshot_ = new ::recorder::LaneHeightSnapshot(*from.snapshot_);
  } else {
    snapshot_ = nullptr;
  }
  record_timestamp_ms_ = from.record_timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:recorder.LaneHeightRecord)
}

inline void LaneHeightRecord::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&snapshot_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&record_timestamp_ms_) -
    reinterpret_cast<char*>(&snapshot_)) + sizeof(record_timestamp_ms_));
}

LaneHeightRecord::~LaneHeightRecord() {
  // @@protoc_insertion_point(destructor:recorder.LaneHeightRecord)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void LaneHeightRecord::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete snapshot_;
}

void LaneHeightRecord::ArenaDtor(void* object) {
  LaneHeightRecord* _this = reinterpret_cast< LaneHeightRecord* >(object);
  (void)_this;
}
void LaneHeightRecord::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void LaneHeightRecord::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void LaneHeightRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.LaneHeightRecord)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && snapshot_ != nullptr) {
    delete snapshot_;
  }
  snapshot_ = nullptr;
  record_timestamp_ms_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* LaneHeightRecord::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 record_timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          record_timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .recorder.LaneHeightSnapshot snapshot = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_snapshot(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* LaneHeightRecord::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.LaneHeightRecord)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_record_timestamp_ms(), target);
  }

  // .recorder.LaneHeightSnapshot snapshot = 2;
  if (this->_internal_has_snapshot()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::snapshot(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.LaneHeightRecord)
  return target;
}

size_t LaneHeightRecord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.LaneHeightRecord)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .recorder.LaneHeightSnapshot snapshot = 2;
  if (this->_internal_has_snapshot()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *snapshot_);
  }

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_record_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData LaneHeightRecord::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    LaneHeightRecord::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*LaneHeightRecord::GetClassData() const { return &_class_data_; }

void LaneHeightRecord::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<LaneHeightRecord *>(to)->MergeFrom(
      static_cast<const LaneHeightRecord &>(from));
}


void LaneHeightRecord::MergeFrom(const LaneHeightRecord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.LaneHeightRecord)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_snapshot()) {
    _internal_mutable_snapshot()->::recorder::LaneHeightSnapshot::MergeFrom(from._internal_snapshot());
  }
  if (from._internal_record_timestamp_ms() != 0) {
    _internal_set_record_timestamp_ms(from._internal_record_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void LaneHeightRecord::CopyFrom(const LaneHeightRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.LaneHeightRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool LaneHeightRecord::IsInitialized() const {
  return true;
}

void LaneHeightRecord::InternalSwap(LaneHeightRecord* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(LaneHeightRecord, record_timestamp_ms_)
      + sizeof(LaneHeightRecord::record_timestamp_ms_)
      - PROTOBUF_FIELD_OFFSET(LaneHeightRecord, snapshot_)>(
          reinterpret_cast<char*>(&snapshot_),
          reinterpret_cast<char*>(&other->snapshot_));
}

::PROTOBUF_NAMESPACE_ID::Metadata LaneHeightRecord::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[5]);
}

// ===================================================================

class RotaryTicksSnapshot::_Internal {
 public:
};

RotaryTicksSnapshot::RotaryTicksSnapshot(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.RotaryTicksSnapshot)
}
RotaryTicksSnapshot::RotaryTicksSnapshot(const RotaryTicksSnapshot& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&timestamp_us_, &from.timestamp_us_,
    static_cast<size_t>(reinterpret_cast<char*>(&br_enabled_) -
    reinterpret_cast<char*>(&timestamp_us_)) + sizeof(br_enabled_));
  // @@protoc_insertion_point(copy_constructor:recorder.RotaryTicksSnapshot)
}

inline void RotaryTicksSnapshot::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&timestamp_us_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&br_enabled_) -
    reinterpret_cast<char*>(&timestamp_us_)) + sizeof(br_enabled_));
}

RotaryTicksSnapshot::~RotaryTicksSnapshot() {
  // @@protoc_insertion_point(destructor:recorder.RotaryTicksSnapshot)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RotaryTicksSnapshot::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void RotaryTicksSnapshot::ArenaDtor(void* object) {
  RotaryTicksSnapshot* _this = reinterpret_cast< RotaryTicksSnapshot* >(object);
  (void)_this;
}
void RotaryTicksSnapshot::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RotaryTicksSnapshot::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RotaryTicksSnapshot::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.RotaryTicksSnapshot)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&timestamp_us_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&br_enabled_) -
      reinterpret_cast<char*>(&timestamp_us_)) + sizeof(br_enabled_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RotaryTicksSnapshot::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 timestamp_us = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          timestamp_us_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 fl = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          fl_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 fr = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          fr_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 bl = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          bl_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 br = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          br_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool fl_enabled = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          fl_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool fr_enabled = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 56)) {
          fr_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool bl_enabled = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          bl_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool br_enabled = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 72)) {
          br_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RotaryTicksSnapshot::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.RotaryTicksSnapshot)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 timestamp_us = 1;
  if (this->_internal_timestamp_us() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_timestamp_us(), target);
  }

  // int32 fl = 2;
  if (this->_internal_fl() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_fl(), target);
  }

  // int32 fr = 3;
  if (this->_internal_fr() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(3, this->_internal_fr(), target);
  }

  // int32 bl = 4;
  if (this->_internal_bl() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(4, this->_internal_bl(), target);
  }

  // int32 br = 5;
  if (this->_internal_br() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(5, this->_internal_br(), target);
  }

  // bool fl_enabled = 6;
  if (this->_internal_fl_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(6, this->_internal_fl_enabled(), target);
  }

  // bool fr_enabled = 7;
  if (this->_internal_fr_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(7, this->_internal_fr_enabled(), target);
  }

  // bool bl_enabled = 8;
  if (this->_internal_bl_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_bl_enabled(), target);
  }

  // bool br_enabled = 9;
  if (this->_internal_br_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(9, this->_internal_br_enabled(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.RotaryTicksSnapshot)
  return target;
}

size_t RotaryTicksSnapshot::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.RotaryTicksSnapshot)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // uint64 timestamp_us = 1;
  if (this->_internal_timestamp_us() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_timestamp_us());
  }

  // int32 fl = 2;
  if (this->_internal_fl() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_fl());
  }

  // int32 fr = 3;
  if (this->_internal_fr() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_fr());
  }

  // int32 bl = 4;
  if (this->_internal_bl() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_bl());
  }

  // int32 br = 5;
  if (this->_internal_br() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_br());
  }

  // bool fl_enabled = 6;
  if (this->_internal_fl_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool fr_enabled = 7;
  if (this->_internal_fr_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool bl_enabled = 8;
  if (this->_internal_bl_enabled() != 0) {
    total_size += 1 + 1;
  }

  // bool br_enabled = 9;
  if (this->_internal_br_enabled() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RotaryTicksSnapshot::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RotaryTicksSnapshot::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RotaryTicksSnapshot::GetClassData() const { return &_class_data_; }

void RotaryTicksSnapshot::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RotaryTicksSnapshot *>(to)->MergeFrom(
      static_cast<const RotaryTicksSnapshot &>(from));
}


void RotaryTicksSnapshot::MergeFrom(const RotaryTicksSnapshot& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.RotaryTicksSnapshot)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_timestamp_us() != 0) {
    _internal_set_timestamp_us(from._internal_timestamp_us());
  }
  if (from._internal_fl() != 0) {
    _internal_set_fl(from._internal_fl());
  }
  if (from._internal_fr() != 0) {
    _internal_set_fr(from._internal_fr());
  }
  if (from._internal_bl() != 0) {
    _internal_set_bl(from._internal_bl());
  }
  if (from._internal_br() != 0) {
    _internal_set_br(from._internal_br());
  }
  if (from._internal_fl_enabled() != 0) {
    _internal_set_fl_enabled(from._internal_fl_enabled());
  }
  if (from._internal_fr_enabled() != 0) {
    _internal_set_fr_enabled(from._internal_fr_enabled());
  }
  if (from._internal_bl_enabled() != 0) {
    _internal_set_bl_enabled(from._internal_bl_enabled());
  }
  if (from._internal_br_enabled() != 0) {
    _internal_set_br_enabled(from._internal_br_enabled());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RotaryTicksSnapshot::CopyFrom(const RotaryTicksSnapshot& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.RotaryTicksSnapshot)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RotaryTicksSnapshot::IsInitialized() const {
  return true;
}

void RotaryTicksSnapshot::InternalSwap(RotaryTicksSnapshot* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RotaryTicksSnapshot, br_enabled_)
      + sizeof(RotaryTicksSnapshot::br_enabled_)
      - PROTOBUF_FIELD_OFFSET(RotaryTicksSnapshot, timestamp_us_)>(
          reinterpret_cast<char*>(&timestamp_us_),
          reinterpret_cast<char*>(&other->timestamp_us_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RotaryTicksSnapshot::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[6]);
}

// ===================================================================

class RotaryTicksRecord::_Internal {
 public:
  static const ::recorder::RotaryTicksSnapshot& snapshot(const RotaryTicksRecord* msg);
};

const ::recorder::RotaryTicksSnapshot&
RotaryTicksRecord::_Internal::snapshot(const RotaryTicksRecord* msg) {
  return *msg->snapshot_;
}
RotaryTicksRecord::RotaryTicksRecord(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:recorder.RotaryTicksRecord)
}
RotaryTicksRecord::RotaryTicksRecord(const RotaryTicksRecord& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  if (from._internal_has_snapshot()) {
    snapshot_ = new ::recorder::RotaryTicksSnapshot(*from.snapshot_);
  } else {
    snapshot_ = nullptr;
  }
  record_timestamp_ms_ = from.record_timestamp_ms_;
  // @@protoc_insertion_point(copy_constructor:recorder.RotaryTicksRecord)
}

inline void RotaryTicksRecord::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&snapshot_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&record_timestamp_ms_) -
    reinterpret_cast<char*>(&snapshot_)) + sizeof(record_timestamp_ms_));
}

RotaryTicksRecord::~RotaryTicksRecord() {
  // @@protoc_insertion_point(destructor:recorder.RotaryTicksRecord)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void RotaryTicksRecord::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  if (this != internal_default_instance()) delete snapshot_;
}

void RotaryTicksRecord::ArenaDtor(void* object) {
  RotaryTicksRecord* _this = reinterpret_cast< RotaryTicksRecord* >(object);
  (void)_this;
}
void RotaryTicksRecord::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void RotaryTicksRecord::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void RotaryTicksRecord::Clear() {
// @@protoc_insertion_point(message_clear_start:recorder.RotaryTicksRecord)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  if (GetArenaForAllocation() == nullptr && snapshot_ != nullptr) {
    delete snapshot_;
  }
  snapshot_ = nullptr;
  record_timestamp_ms_ = uint64_t{0u};
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* RotaryTicksRecord::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // uint64 record_timestamp_ms = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          record_timestamp_ms_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .recorder.RotaryTicksSnapshot snapshot = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_snapshot(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* RotaryTicksRecord::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:recorder.RotaryTicksRecord)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteUInt64ToArray(1, this->_internal_record_timestamp_ms(), target);
  }

  // .recorder.RotaryTicksSnapshot snapshot = 2;
  if (this->_internal_has_snapshot()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::snapshot(this), target, stream);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:recorder.RotaryTicksRecord)
  return target;
}

size_t RotaryTicksRecord::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:recorder.RotaryTicksRecord)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // .recorder.RotaryTicksSnapshot snapshot = 2;
  if (this->_internal_has_snapshot()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *snapshot_);
  }

  // uint64 record_timestamp_ms = 1;
  if (this->_internal_record_timestamp_ms() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::UInt64SizePlusOne(this->_internal_record_timestamp_ms());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData RotaryTicksRecord::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    RotaryTicksRecord::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*RotaryTicksRecord::GetClassData() const { return &_class_data_; }

void RotaryTicksRecord::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<RotaryTicksRecord *>(to)->MergeFrom(
      static_cast<const RotaryTicksRecord &>(from));
}


void RotaryTicksRecord::MergeFrom(const RotaryTicksRecord& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:recorder.RotaryTicksRecord)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_has_snapshot()) {
    _internal_mutable_snapshot()->::recorder::RotaryTicksSnapshot::MergeFrom(from._internal_snapshot());
  }
  if (from._internal_record_timestamp_ms() != 0) {
    _internal_set_record_timestamp_ms(from._internal_record_timestamp_ms());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void RotaryTicksRecord::CopyFrom(const RotaryTicksRecord& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:recorder.RotaryTicksRecord)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool RotaryTicksRecord::IsInitialized() const {
  return true;
}

void RotaryTicksRecord::InternalSwap(RotaryTicksRecord* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(RotaryTicksRecord, record_timestamp_ms_)
      + sizeof(RotaryTicksRecord::record_timestamp_ms_)
      - PROTOBUF_FIELD_OFFSET(RotaryTicksRecord, snapshot_)>(
          reinterpret_cast<char*>(&snapshot_),
          reinterpret_cast<char*>(&other->snapshot_));
}

::PROTOBUF_NAMESPACE_ID::Metadata RotaryTicksRecord::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_recorder_2fproto_2frecorder_2eproto_getter, &descriptor_table_recorder_2fproto_2frecorder_2eproto_once,
      file_level_metadata_recorder_2fproto_2frecorder_2eproto[7]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace recorder
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::recorder::DetectionClass* Arena::CreateMaybeMessage< ::recorder::DetectionClass >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::DetectionClass >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::DeepweedDetection* Arena::CreateMaybeMessage< ::recorder::DeepweedDetection >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::DeepweedDetection >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::DeepweedPredictionFrame* Arena::CreateMaybeMessage< ::recorder::DeepweedPredictionFrame >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::DeepweedPredictionFrame >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::DeepweedPredictionRecord* Arena::CreateMaybeMessage< ::recorder::DeepweedPredictionRecord >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::DeepweedPredictionRecord >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::LaneHeightSnapshot* Arena::CreateMaybeMessage< ::recorder::LaneHeightSnapshot >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::LaneHeightSnapshot >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::LaneHeightRecord* Arena::CreateMaybeMessage< ::recorder::LaneHeightRecord >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::LaneHeightRecord >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::RotaryTicksSnapshot* Arena::CreateMaybeMessage< ::recorder::RotaryTicksSnapshot >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::RotaryTicksSnapshot >(arena);
}
template<> PROTOBUF_NOINLINE ::recorder::RotaryTicksRecord* Arena::CreateMaybeMessage< ::recorder::RotaryTicksRecord >(Arena* arena) {
  return Arena::CreateMessageInternal< ::recorder::RotaryTicksRecord >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/model_io/proto/trt_model.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "deeplearning/model_io/proto/metadata.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto;
class TRTModelProto;
struct TRTModelProtoDefaultTypeInternal;
extern TRTModelProtoDefaultTypeInternal _TRTModelProto_default_instance_;
PROTOBUF_NAMESPACE_OPEN
template<> ::TRTModelProto* Arena::CreateMaybeMessage<::TRTModelProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE

// ===================================================================

class TRTModelProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:TRTModelProto) */ {
 public:
  inline TRTModelProto() : TRTModelProto(nullptr) {}
  ~TRTModelProto() override;
  explicit constexpr TRTModelProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  TRTModelProto(const TRTModelProto& from);
  TRTModelProto(TRTModelProto&& from) noexcept
    : TRTModelProto() {
    *this = ::std::move(from);
  }

  inline TRTModelProto& operator=(const TRTModelProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline TRTModelProto& operator=(TRTModelProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const TRTModelProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const TRTModelProto* internal_default_instance() {
    return reinterpret_cast<const TRTModelProto*>(
               &_TRTModelProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(TRTModelProto& a, TRTModelProto& b) {
    a.Swap(&b);
  }
  inline void Swap(TRTModelProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(TRTModelProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  TRTModelProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<TRTModelProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const TRTModelProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const TRTModelProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(TRTModelProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "TRTModelProto";
  }
  protected:
  explicit TRTModelProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputNamesFieldNumber = 3,
    kOutputNamesFieldNumber = 4,
    kEngineBytesFieldNumber = 1,
    kTensorrtVersionFieldNumber = 6,
    kMetadataFieldNumber = 2,
    kImplicitBatchDimensionFieldNumber = 5,
  };
  // repeated string input_names = 3;
  int input_names_size() const;
  private:
  int _internal_input_names_size() const;
  public:
  void clear_input_names();
  const std::string& input_names(int index) const;
  std::string* mutable_input_names(int index);
  void set_input_names(int index, const std::string& value);
  void set_input_names(int index, std::string&& value);
  void set_input_names(int index, const char* value);
  void set_input_names(int index, const char* value, size_t size);
  std::string* add_input_names();
  void add_input_names(const std::string& value);
  void add_input_names(std::string&& value);
  void add_input_names(const char* value);
  void add_input_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& input_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_input_names();
  private:
  const std::string& _internal_input_names(int index) const;
  std::string* _internal_add_input_names();
  public:

  // repeated string output_names = 4;
  int output_names_size() const;
  private:
  int _internal_output_names_size() const;
  public:
  void clear_output_names();
  const std::string& output_names(int index) const;
  std::string* mutable_output_names(int index);
  void set_output_names(int index, const std::string& value);
  void set_output_names(int index, std::string&& value);
  void set_output_names(int index, const char* value);
  void set_output_names(int index, const char* value, size_t size);
  std::string* add_output_names();
  void add_output_names(const std::string& value);
  void add_output_names(std::string&& value);
  void add_output_names(const char* value);
  void add_output_names(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& output_names() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_output_names();
  private:
  const std::string& _internal_output_names(int index) const;
  std::string* _internal_add_output_names();
  public:

  // bytes engine_bytes = 1;
  void clear_engine_bytes();
  const std::string& engine_bytes() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_engine_bytes(ArgT0&& arg0, ArgT... args);
  std::string* mutable_engine_bytes();
  PROTOBUF_NODISCARD std::string* release_engine_bytes();
  void set_allocated_engine_bytes(std::string* engine_bytes);
  private:
  const std::string& _internal_engine_bytes() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_engine_bytes(const std::string& value);
  std::string* _internal_mutable_engine_bytes();
  public:

  // string tensorrt_version = 6;
  void clear_tensorrt_version();
  const std::string& tensorrt_version() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_tensorrt_version(ArgT0&& arg0, ArgT... args);
  std::string* mutable_tensorrt_version();
  PROTOBUF_NODISCARD std::string* release_tensorrt_version();
  void set_allocated_tensorrt_version(std::string* tensorrt_version);
  private:
  const std::string& _internal_tensorrt_version() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_tensorrt_version(const std::string& value);
  std::string* _internal_mutable_tensorrt_version();
  public:

  // .ModelMetadataProto metadata = 2;
  bool has_metadata() const;
  private:
  bool _internal_has_metadata() const;
  public:
  void clear_metadata();
  const ::ModelMetadataProto& metadata() const;
  PROTOBUF_NODISCARD ::ModelMetadataProto* release_metadata();
  ::ModelMetadataProto* mutable_metadata();
  void set_allocated_metadata(::ModelMetadataProto* metadata);
  private:
  const ::ModelMetadataProto& _internal_metadata() const;
  ::ModelMetadataProto* _internal_mutable_metadata();
  public:
  void unsafe_arena_set_allocated_metadata(
      ::ModelMetadataProto* metadata);
  ::ModelMetadataProto* unsafe_arena_release_metadata();

  // bool implicit_batch_dimension = 5;
  void clear_implicit_batch_dimension();
  bool implicit_batch_dimension() const;
  void set_implicit_batch_dimension(bool value);
  private:
  bool _internal_implicit_batch_dimension() const;
  void _internal_set_implicit_batch_dimension(bool value);
  public:

  // @@protoc_insertion_point(class_scope:TRTModelProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> input_names_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> output_names_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr engine_bytes_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr tensorrt_version_;
  ::ModelMetadataProto* metadata_;
  bool implicit_batch_dimension_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// TRTModelProto

// bytes engine_bytes = 1;
inline void TRTModelProto::clear_engine_bytes() {
  engine_bytes_.ClearToEmpty();
}
inline const std::string& TRTModelProto::engine_bytes() const {
  // @@protoc_insertion_point(field_get:TRTModelProto.engine_bytes)
  return _internal_engine_bytes();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TRTModelProto::set_engine_bytes(ArgT0&& arg0, ArgT... args) {
 
 engine_bytes_.SetBytes(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:TRTModelProto.engine_bytes)
}
inline std::string* TRTModelProto::mutable_engine_bytes() {
  std::string* _s = _internal_mutable_engine_bytes();
  // @@protoc_insertion_point(field_mutable:TRTModelProto.engine_bytes)
  return _s;
}
inline const std::string& TRTModelProto::_internal_engine_bytes() const {
  return engine_bytes_.Get();
}
inline void TRTModelProto::_internal_set_engine_bytes(const std::string& value) {
  
  engine_bytes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TRTModelProto::_internal_mutable_engine_bytes() {
  
  return engine_bytes_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TRTModelProto::release_engine_bytes() {
  // @@protoc_insertion_point(field_release:TRTModelProto.engine_bytes)
  return engine_bytes_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TRTModelProto::set_allocated_engine_bytes(std::string* engine_bytes) {
  if (engine_bytes != nullptr) {
    
  } else {
    
  }
  engine_bytes_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), engine_bytes,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (engine_bytes_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    engine_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:TRTModelProto.engine_bytes)
}

// .ModelMetadataProto metadata = 2;
inline bool TRTModelProto::_internal_has_metadata() const {
  return this != internal_default_instance() && metadata_ != nullptr;
}
inline bool TRTModelProto::has_metadata() const {
  return _internal_has_metadata();
}
inline const ::ModelMetadataProto& TRTModelProto::_internal_metadata() const {
  const ::ModelMetadataProto* p = metadata_;
  return p != nullptr ? *p : reinterpret_cast<const ::ModelMetadataProto&>(
      ::_ModelMetadataProto_default_instance_);
}
inline const ::ModelMetadataProto& TRTModelProto::metadata() const {
  // @@protoc_insertion_point(field_get:TRTModelProto.metadata)
  return _internal_metadata();
}
inline void TRTModelProto::unsafe_arena_set_allocated_metadata(
    ::ModelMetadataProto* metadata) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  metadata_ = metadata;
  if (metadata) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:TRTModelProto.metadata)
}
inline ::ModelMetadataProto* TRTModelProto::release_metadata() {
  
  ::ModelMetadataProto* temp = metadata_;
  metadata_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::ModelMetadataProto* TRTModelProto::unsafe_arena_release_metadata() {
  // @@protoc_insertion_point(field_release:TRTModelProto.metadata)
  
  ::ModelMetadataProto* temp = metadata_;
  metadata_ = nullptr;
  return temp;
}
inline ::ModelMetadataProto* TRTModelProto::_internal_mutable_metadata() {
  
  if (metadata_ == nullptr) {
    auto* p = CreateMaybeMessage<::ModelMetadataProto>(GetArenaForAllocation());
    metadata_ = p;
  }
  return metadata_;
}
inline ::ModelMetadataProto* TRTModelProto::mutable_metadata() {
  ::ModelMetadataProto* _msg = _internal_mutable_metadata();
  // @@protoc_insertion_point(field_mutable:TRTModelProto.metadata)
  return _msg;
}
inline void TRTModelProto::set_allocated_metadata(::ModelMetadataProto* metadata) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete reinterpret_cast< ::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata_);
  }
  if (metadata) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<
            ::PROTOBUF_NAMESPACE_ID::MessageLite>::GetOwningArena(
                reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(metadata));
    if (message_arena != submessage_arena) {
      metadata = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, metadata, submessage_arena);
    }
    
  } else {
    
  }
  metadata_ = metadata;
  // @@protoc_insertion_point(field_set_allocated:TRTModelProto.metadata)
}

// repeated string input_names = 3;
inline int TRTModelProto::_internal_input_names_size() const {
  return input_names_.size();
}
inline int TRTModelProto::input_names_size() const {
  return _internal_input_names_size();
}
inline void TRTModelProto::clear_input_names() {
  input_names_.Clear();
}
inline std::string* TRTModelProto::add_input_names() {
  std::string* _s = _internal_add_input_names();
  // @@protoc_insertion_point(field_add_mutable:TRTModelProto.input_names)
  return _s;
}
inline const std::string& TRTModelProto::_internal_input_names(int index) const {
  return input_names_.Get(index);
}
inline const std::string& TRTModelProto::input_names(int index) const {
  // @@protoc_insertion_point(field_get:TRTModelProto.input_names)
  return _internal_input_names(index);
}
inline std::string* TRTModelProto::mutable_input_names(int index) {
  // @@protoc_insertion_point(field_mutable:TRTModelProto.input_names)
  return input_names_.Mutable(index);
}
inline void TRTModelProto::set_input_names(int index, const std::string& value) {
  input_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:TRTModelProto.input_names)
}
inline void TRTModelProto::set_input_names(int index, std::string&& value) {
  input_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:TRTModelProto.input_names)
}
inline void TRTModelProto::set_input_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:TRTModelProto.input_names)
}
inline void TRTModelProto::set_input_names(int index, const char* value, size_t size) {
  input_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:TRTModelProto.input_names)
}
inline std::string* TRTModelProto::_internal_add_input_names() {
  return input_names_.Add();
}
inline void TRTModelProto::add_input_names(const std::string& value) {
  input_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:TRTModelProto.input_names)
}
inline void TRTModelProto::add_input_names(std::string&& value) {
  input_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:TRTModelProto.input_names)
}
inline void TRTModelProto::add_input_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  input_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:TRTModelProto.input_names)
}
inline void TRTModelProto::add_input_names(const char* value, size_t size) {
  input_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:TRTModelProto.input_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TRTModelProto::input_names() const {
  // @@protoc_insertion_point(field_list:TRTModelProto.input_names)
  return input_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TRTModelProto::mutable_input_names() {
  // @@protoc_insertion_point(field_mutable_list:TRTModelProto.input_names)
  return &input_names_;
}

// repeated string output_names = 4;
inline int TRTModelProto::_internal_output_names_size() const {
  return output_names_.size();
}
inline int TRTModelProto::output_names_size() const {
  return _internal_output_names_size();
}
inline void TRTModelProto::clear_output_names() {
  output_names_.Clear();
}
inline std::string* TRTModelProto::add_output_names() {
  std::string* _s = _internal_add_output_names();
  // @@protoc_insertion_point(field_add_mutable:TRTModelProto.output_names)
  return _s;
}
inline const std::string& TRTModelProto::_internal_output_names(int index) const {
  return output_names_.Get(index);
}
inline const std::string& TRTModelProto::output_names(int index) const {
  // @@protoc_insertion_point(field_get:TRTModelProto.output_names)
  return _internal_output_names(index);
}
inline std::string* TRTModelProto::mutable_output_names(int index) {
  // @@protoc_insertion_point(field_mutable:TRTModelProto.output_names)
  return output_names_.Mutable(index);
}
inline void TRTModelProto::set_output_names(int index, const std::string& value) {
  output_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:TRTModelProto.output_names)
}
inline void TRTModelProto::set_output_names(int index, std::string&& value) {
  output_names_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:TRTModelProto.output_names)
}
inline void TRTModelProto::set_output_names(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_names_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:TRTModelProto.output_names)
}
inline void TRTModelProto::set_output_names(int index, const char* value, size_t size) {
  output_names_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:TRTModelProto.output_names)
}
inline std::string* TRTModelProto::_internal_add_output_names() {
  return output_names_.Add();
}
inline void TRTModelProto::add_output_names(const std::string& value) {
  output_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:TRTModelProto.output_names)
}
inline void TRTModelProto::add_output_names(std::string&& value) {
  output_names_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:TRTModelProto.output_names)
}
inline void TRTModelProto::add_output_names(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  output_names_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:TRTModelProto.output_names)
}
inline void TRTModelProto::add_output_names(const char* value, size_t size) {
  output_names_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:TRTModelProto.output_names)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
TRTModelProto::output_names() const {
  // @@protoc_insertion_point(field_list:TRTModelProto.output_names)
  return output_names_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
TRTModelProto::mutable_output_names() {
  // @@protoc_insertion_point(field_mutable_list:TRTModelProto.output_names)
  return &output_names_;
}

// bool implicit_batch_dimension = 5;
inline void TRTModelProto::clear_implicit_batch_dimension() {
  implicit_batch_dimension_ = false;
}
inline bool TRTModelProto::_internal_implicit_batch_dimension() const {
  return implicit_batch_dimension_;
}
inline bool TRTModelProto::implicit_batch_dimension() const {
  // @@protoc_insertion_point(field_get:TRTModelProto.implicit_batch_dimension)
  return _internal_implicit_batch_dimension();
}
inline void TRTModelProto::_internal_set_implicit_batch_dimension(bool value) {
  
  implicit_batch_dimension_ = value;
}
inline void TRTModelProto::set_implicit_batch_dimension(bool value) {
  _internal_set_implicit_batch_dimension(value);
  // @@protoc_insertion_point(field_set:TRTModelProto.implicit_batch_dimension)
}

// string tensorrt_version = 6;
inline void TRTModelProto::clear_tensorrt_version() {
  tensorrt_version_.ClearToEmpty();
}
inline const std::string& TRTModelProto::tensorrt_version() const {
  // @@protoc_insertion_point(field_get:TRTModelProto.tensorrt_version)
  return _internal_tensorrt_version();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void TRTModelProto::set_tensorrt_version(ArgT0&& arg0, ArgT... args) {
 
 tensorrt_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:TRTModelProto.tensorrt_version)
}
inline std::string* TRTModelProto::mutable_tensorrt_version() {
  std::string* _s = _internal_mutable_tensorrt_version();
  // @@protoc_insertion_point(field_mutable:TRTModelProto.tensorrt_version)
  return _s;
}
inline const std::string& TRTModelProto::_internal_tensorrt_version() const {
  return tensorrt_version_.Get();
}
inline void TRTModelProto::_internal_set_tensorrt_version(const std::string& value) {
  
  tensorrt_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* TRTModelProto::_internal_mutable_tensorrt_version() {
  
  return tensorrt_version_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* TRTModelProto::release_tensorrt_version() {
  // @@protoc_insertion_point(field_release:TRTModelProto.tensorrt_version)
  return tensorrt_version_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void TRTModelProto::set_allocated_tensorrt_version(std::string* tensorrt_version) {
  if (tensorrt_version != nullptr) {
    
  } else {
    
  }
  tensorrt_version_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), tensorrt_version,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (tensorrt_version_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    tensorrt_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:TRTModelProto.tensorrt_version)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)


// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto

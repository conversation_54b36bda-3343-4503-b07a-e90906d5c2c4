# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: deeplearning/model_io/proto/metadata.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='deeplearning/model_io/proto/metadata.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n*deeplearning/model_io/proto/metadata.proto\"*\n\tSizeProto\x12\r\n\x05width\x18\x01 \x01(\x05\x12\x0e\n\x06height\x18\x02 \x01(\x05\"\x8e\x01\n\x1bScalerShifterParameterProto\x12\x10\n\x08min_real\x18\x01 \x01(\x02\x12\x10\n\x08max_real\x18\x02 \x01(\x02\x12\x11\n\tmin_scale\x18\x03 \x01(\x02\x12\x11\n\tmax_scale\x18\x04 \x01(\x02\x12\x12\n\nshift_real\x18\x05 \x01(\x02\x12\x11\n\tshift_ind\x18\x06 \x01(\x05\"\xf3\x05\n\x12ModelMetadataProto\x12\x13\n\x0binput_dtype\x18\x01 \x01(\t\x12\x1e\n\ninput_size\x18\x02 \x01(\x0b\x32\n.SizeProto\x12\r\n\x05means\x18\x03 \x03(\x02\x12\x0c\n\x04stds\x18\x04 \x03(\x02\x12\x16\n\x0e\x65xperiment_url\x18\x05 \x01(\t\x12\x19\n\x11supported_classes\x18\x06 \x03(\t\x12\x11\n\tuse_cases\x18\x07 \x03(\t\x12\x16\n\x0esupports_depth\x18\x08 \x01(\x08\x12\x0b\n\x03ppi\x18\t \x01(\x02\x12\x18\n\x04tile\x18\n \x01(\x0b\x32\n.SizeProto\x12\x15\n\rsupports_half\x18\x0b \x01(\x08\x12\x16\n\x0emax_batch_size\x18\x0c \x01(\x05\x12#\n\x0f\x61ux_input_sizes\x18\r \x03(\x0b\x32\n.SizeProto\x12\x0f\n\x07version\x18\x0e \x01(\x05\x12\x12\n\nmodel_type\x18\x0f \x01(\t\x12\x17\n\x0fnot_interleaved\x18\x10 \x01(\x08\x12 \n\x18\x64iscard_points_border_px\x18\x11 \x01(\x02\x12\x1a\n\x12weed_point_classes\x18\x12 \x03(\t\x12\x1a\n\x12\x63rop_point_classes\x18\x13 \x03(\t\x12\x14\n\x0csegm_classes\x18\x14 \x03(\t\x12\x14\n\x0cline_classes\x18\x15 \x03(\t\x12\x13\n\x0bmodel_class\x18\x16 \x01(\t\x12\x15\n\rplant_enabled\x18\x17 \x01(\x08\x12\x1a\n\x12trained_embeddings\x18\x18 \x01(\x08\x12\x1b\n\x13\x63ontains_pumap_head\x18\x19 \x01(\x08\x12\x1d\n\x15\x62\x61\x63kbone_architecture\x18\x1a \x01(\t\x12?\n\x19scaler_shifter_parameters\x18\x1b \x01(\x0b\x32\x1c.ScalerShifterParameterProto\x12\x10\n\x08\x63rop_ids\x18\x1c \x03(\t\x12\x17\n\x0f\x63rop_embeddings\x18\x1d \x01(\x08\x62\x06proto3'
)




_SIZEPROTO = _descriptor.Descriptor(
  name='SizeProto',
  full_name='SizeProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='width', full_name='SizeProto.width', index=0,
      number=1, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='height', full_name='SizeProto.height', index=1,
      number=2, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=46,
  serialized_end=88,
)


_SCALERSHIFTERPARAMETERPROTO = _descriptor.Descriptor(
  name='ScalerShifterParameterProto',
  full_name='ScalerShifterParameterProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='min_real', full_name='ScalerShifterParameterProto.min_real', index=0,
      number=1, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_real', full_name='ScalerShifterParameterProto.max_real', index=1,
      number=2, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='min_scale', full_name='ScalerShifterParameterProto.min_scale', index=2,
      number=3, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_scale', full_name='ScalerShifterParameterProto.max_scale', index=3,
      number=4, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='shift_real', full_name='ScalerShifterParameterProto.shift_real', index=4,
      number=5, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='shift_ind', full_name='ScalerShifterParameterProto.shift_ind', index=5,
      number=6, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=91,
  serialized_end=233,
)


_MODELMETADATAPROTO = _descriptor.Descriptor(
  name='ModelMetadataProto',
  full_name='ModelMetadataProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='input_dtype', full_name='ModelMetadataProto.input_dtype', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='input_size', full_name='ModelMetadataProto.input_size', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='means', full_name='ModelMetadataProto.means', index=2,
      number=3, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stds', full_name='ModelMetadataProto.stds', index=3,
      number=4, type=2, cpp_type=6, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='experiment_url', full_name='ModelMetadataProto.experiment_url', index=4,
      number=5, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='supported_classes', full_name='ModelMetadataProto.supported_classes', index=5,
      number=6, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='use_cases', full_name='ModelMetadataProto.use_cases', index=6,
      number=7, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='supports_depth', full_name='ModelMetadataProto.supports_depth', index=7,
      number=8, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='ppi', full_name='ModelMetadataProto.ppi', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tile', full_name='ModelMetadataProto.tile', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='supports_half', full_name='ModelMetadataProto.supports_half', index=10,
      number=11, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='max_batch_size', full_name='ModelMetadataProto.max_batch_size', index=11,
      number=12, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='aux_input_sizes', full_name='ModelMetadataProto.aux_input_sizes', index=12,
      number=13, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='version', full_name='ModelMetadataProto.version', index=13,
      number=14, type=5, cpp_type=1, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_type', full_name='ModelMetadataProto.model_type', index=14,
      number=15, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='not_interleaved', full_name='ModelMetadataProto.not_interleaved', index=15,
      number=16, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='discard_points_border_px', full_name='ModelMetadataProto.discard_points_border_px', index=16,
      number=17, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='weed_point_classes', full_name='ModelMetadataProto.weed_point_classes', index=17,
      number=18, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_point_classes', full_name='ModelMetadataProto.crop_point_classes', index=18,
      number=19, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='segm_classes', full_name='ModelMetadataProto.segm_classes', index=19,
      number=20, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='line_classes', full_name='ModelMetadataProto.line_classes', index=20,
      number=21, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='model_class', full_name='ModelMetadataProto.model_class', index=21,
      number=22, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='plant_enabled', full_name='ModelMetadataProto.plant_enabled', index=22,
      number=23, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='trained_embeddings', full_name='ModelMetadataProto.trained_embeddings', index=23,
      number=24, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='contains_pumap_head', full_name='ModelMetadataProto.contains_pumap_head', index=24,
      number=25, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='backbone_architecture', full_name='ModelMetadataProto.backbone_architecture', index=25,
      number=26, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='scaler_shifter_parameters', full_name='ModelMetadataProto.scaler_shifter_parameters', index=26,
      number=27, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_ids', full_name='ModelMetadataProto.crop_ids', index=27,
      number=28, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='crop_embeddings', full_name='ModelMetadataProto.crop_embeddings', index=28,
      number=29, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=236,
  serialized_end=991,
)

_MODELMETADATAPROTO.fields_by_name['input_size'].message_type = _SIZEPROTO
_MODELMETADATAPROTO.fields_by_name['tile'].message_type = _SIZEPROTO
_MODELMETADATAPROTO.fields_by_name['aux_input_sizes'].message_type = _SIZEPROTO
_MODELMETADATAPROTO.fields_by_name['scaler_shifter_parameters'].message_type = _SCALERSHIFTERPARAMETERPROTO
DESCRIPTOR.message_types_by_name['SizeProto'] = _SIZEPROTO
DESCRIPTOR.message_types_by_name['ScalerShifterParameterProto'] = _SCALERSHIFTERPARAMETERPROTO
DESCRIPTOR.message_types_by_name['ModelMetadataProto'] = _MODELMETADATAPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

SizeProto = _reflection.GeneratedProtocolMessageType('SizeProto', (_message.Message,), {
  'DESCRIPTOR' : _SIZEPROTO,
  '__module__' : 'deeplearning.model_io.proto.metadata_pb2'
  # @@protoc_insertion_point(class_scope:SizeProto)
  })
_sym_db.RegisterMessage(SizeProto)

ScalerShifterParameterProto = _reflection.GeneratedProtocolMessageType('ScalerShifterParameterProto', (_message.Message,), {
  'DESCRIPTOR' : _SCALERSHIFTERPARAMETERPROTO,
  '__module__' : 'deeplearning.model_io.proto.metadata_pb2'
  # @@protoc_insertion_point(class_scope:ScalerShifterParameterProto)
  })
_sym_db.RegisterMessage(ScalerShifterParameterProto)

ModelMetadataProto = _reflection.GeneratedProtocolMessageType('ModelMetadataProto', (_message.Message,), {
  'DESCRIPTOR' : _MODELMETADATAPROTO,
  '__module__' : 'deeplearning.model_io.proto.metadata_pb2'
  # @@protoc_insertion_point(class_scope:ModelMetadataProto)
  })
_sym_db.RegisterMessage(ModelMetadataProto)


# @@protoc_insertion_point(module_scope)

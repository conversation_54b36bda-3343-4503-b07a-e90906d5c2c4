// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/model_io/proto/trt_model.proto

#include "deeplearning/model_io/proto/trt_model.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
constexpr TRTModelProto::TRTModelProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : input_names_()
  , output_names_()
  , engine_bytes_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , tensorrt_version_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , metadata_(nullptr)
  , implicit_batch_dimension_(false){}
struct TRTModelProtoDefaultTypeInternal {
  constexpr TRTModelProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~TRTModelProtoDefaultTypeInternal() {}
  union {
    TRTModelProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT TRTModelProtoDefaultTypeInternal _TRTModelProto_default_instance_;
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto = nullptr;

const uint32_t TableStruct_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::TRTModelProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::TRTModelProto, engine_bytes_),
  PROTOBUF_FIELD_OFFSET(::TRTModelProto, metadata_),
  PROTOBUF_FIELD_OFFSET(::TRTModelProto, input_names_),
  PROTOBUF_FIELD_OFFSET(::TRTModelProto, output_names_),
  PROTOBUF_FIELD_OFFSET(::TRTModelProto, implicit_batch_dimension_),
  PROTOBUF_FIELD_OFFSET(::TRTModelProto, tensorrt_version_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::TRTModelProto)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::_TRTModelProto_default_instance_),
};

const char descriptor_table_protodef_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n+deeplearning/model_io/proto/trt_model."
  "proto\032*deeplearning/model_io/proto/metad"
  "ata.proto\"\263\001\n\rTRTModelProto\022\024\n\014engine_by"
  "tes\030\001 \001(\014\022%\n\010metadata\030\002 \001(\0132\023.ModelMetad"
  "ataProto\022\023\n\013input_names\030\003 \003(\t\022\024\n\014output_"
  "names\030\004 \003(\t\022 \n\030implicit_batch_dimension\030"
  "\005 \001(\010\022\030\n\020tensorrt_version\030\006 \001(\tb\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto_deps[1] = {
  &::descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto = {
  false, false, 279, descriptor_table_protodef_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto, "deeplearning/model_io/proto/trt_model.proto", 
  &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto_once, descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto::offsets,
  file_level_metadata_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto, file_level_enum_descriptors_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto, file_level_service_descriptors_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto_getter() {
  return &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto(&descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto);

// ===================================================================

class TRTModelProto::_Internal {
 public:
  static const ::ModelMetadataProto& metadata(const TRTModelProto* msg);
};

const ::ModelMetadataProto&
TRTModelProto::_Internal::metadata(const TRTModelProto* msg) {
  return *msg->metadata_;
}
void TRTModelProto::clear_metadata() {
  if (GetArenaForAllocation() == nullptr && metadata_ != nullptr) {
    delete metadata_;
  }
  metadata_ = nullptr;
}
TRTModelProto::TRTModelProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  input_names_(arena),
  output_names_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:TRTModelProto)
}
TRTModelProto::TRTModelProto(const TRTModelProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      input_names_(from.input_names_),
      output_names_(from.output_names_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  engine_bytes_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    engine_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_engine_bytes().empty()) {
    engine_bytes_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_engine_bytes(), 
      GetArenaForAllocation());
  }
  tensorrt_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    tensorrt_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_tensorrt_version().empty()) {
    tensorrt_version_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_tensorrt_version(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_metadata()) {
    metadata_ = new ::ModelMetadataProto(*from.metadata_);
  } else {
    metadata_ = nullptr;
  }
  implicit_batch_dimension_ = from.implicit_batch_dimension_;
  // @@protoc_insertion_point(copy_constructor:TRTModelProto)
}

inline void TRTModelProto::SharedCtor() {
engine_bytes_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  engine_bytes_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
tensorrt_version_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  tensorrt_version_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&metadata_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&implicit_batch_dimension_) -
    reinterpret_cast<char*>(&metadata_)) + sizeof(implicit_batch_dimension_));
}

TRTModelProto::~TRTModelProto() {
  // @@protoc_insertion_point(destructor:TRTModelProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void TRTModelProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  engine_bytes_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  tensorrt_version_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete metadata_;
}

void TRTModelProto::ArenaDtor(void* object) {
  TRTModelProto* _this = reinterpret_cast< TRTModelProto* >(object);
  (void)_this;
}
void TRTModelProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void TRTModelProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void TRTModelProto::Clear() {
// @@protoc_insertion_point(message_clear_start:TRTModelProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_names_.Clear();
  output_names_.Clear();
  engine_bytes_.ClearToEmpty();
  tensorrt_version_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && metadata_ != nullptr) {
    delete metadata_;
  }
  metadata_ = nullptr;
  implicit_batch_dimension_ = false;
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* TRTModelProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // bytes engine_bytes = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_engine_bytes();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .ModelMetadataProto metadata = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_metadata(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string input_names = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_input_names();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "TRTModelProto.input_names"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<26>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string output_names = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_output_names();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "TRTModelProto.output_names"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<34>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool implicit_batch_dimension = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          implicit_batch_dimension_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string tensorrt_version = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          auto str = _internal_mutable_tensorrt_version();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "TRTModelProto.tensorrt_version"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* TRTModelProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:TRTModelProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // bytes engine_bytes = 1;
  if (!this->_internal_engine_bytes().empty()) {
    target = stream->WriteBytesMaybeAliased(
        1, this->_internal_engine_bytes(), target);
  }

  // .ModelMetadataProto metadata = 2;
  if (this->_internal_has_metadata()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::metadata(this), target, stream);
  }

  // repeated string input_names = 3;
  for (int i = 0, n = this->_internal_input_names_size(); i < n; i++) {
    const auto& s = this->_internal_input_names(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "TRTModelProto.input_names");
    target = stream->WriteString(3, s, target);
  }

  // repeated string output_names = 4;
  for (int i = 0, n = this->_internal_output_names_size(); i < n; i++) {
    const auto& s = this->_internal_output_names(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "TRTModelProto.output_names");
    target = stream->WriteString(4, s, target);
  }

  // bool implicit_batch_dimension = 5;
  if (this->_internal_implicit_batch_dimension() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(5, this->_internal_implicit_batch_dimension(), target);
  }

  // string tensorrt_version = 6;
  if (!this->_internal_tensorrt_version().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_tensorrt_version().data(), static_cast<int>(this->_internal_tensorrt_version().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "TRTModelProto.tensorrt_version");
    target = stream->WriteStringMaybeAliased(
        6, this->_internal_tensorrt_version(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:TRTModelProto)
  return target;
}

size_t TRTModelProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:TRTModelProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated string input_names = 3;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(input_names_.size());
  for (int i = 0, n = input_names_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      input_names_.Get(i));
  }

  // repeated string output_names = 4;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(output_names_.size());
  for (int i = 0, n = output_names_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      output_names_.Get(i));
  }

  // bytes engine_bytes = 1;
  if (!this->_internal_engine_bytes().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::BytesSize(
        this->_internal_engine_bytes());
  }

  // string tensorrt_version = 6;
  if (!this->_internal_tensorrt_version().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_tensorrt_version());
  }

  // .ModelMetadataProto metadata = 2;
  if (this->_internal_has_metadata()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *metadata_);
  }

  // bool implicit_batch_dimension = 5;
  if (this->_internal_implicit_batch_dimension() != 0) {
    total_size += 1 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData TRTModelProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    TRTModelProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*TRTModelProto::GetClassData() const { return &_class_data_; }

void TRTModelProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<TRTModelProto *>(to)->MergeFrom(
      static_cast<const TRTModelProto &>(from));
}


void TRTModelProto::MergeFrom(const TRTModelProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:TRTModelProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  input_names_.MergeFrom(from.input_names_);
  output_names_.MergeFrom(from.output_names_);
  if (!from._internal_engine_bytes().empty()) {
    _internal_set_engine_bytes(from._internal_engine_bytes());
  }
  if (!from._internal_tensorrt_version().empty()) {
    _internal_set_tensorrt_version(from._internal_tensorrt_version());
  }
  if (from._internal_has_metadata()) {
    _internal_mutable_metadata()->::ModelMetadataProto::MergeFrom(from._internal_metadata());
  }
  if (from._internal_implicit_batch_dimension() != 0) {
    _internal_set_implicit_batch_dimension(from._internal_implicit_batch_dimension());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void TRTModelProto::CopyFrom(const TRTModelProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:TRTModelProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool TRTModelProto::IsInitialized() const {
  return true;
}

void TRTModelProto::InternalSwap(TRTModelProto* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  input_names_.InternalSwap(&other->input_names_);
  output_names_.InternalSwap(&other->output_names_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &engine_bytes_, lhs_arena,
      &other->engine_bytes_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &tensorrt_version_, lhs_arena,
      &other->tensorrt_version_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(TRTModelProto, implicit_batch_dimension_)
      + sizeof(TRTModelProto::implicit_batch_dimension_)
      - PROTOBUF_FIELD_OFFSET(TRTModelProto, metadata_)>(
          reinterpret_cast<char*>(&metadata_),
          reinterpret_cast<char*>(&other->metadata_));
}

::PROTOBUF_NAMESPACE_ID::Metadata TRTModelProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto_getter, &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto_once,
      file_level_metadata_deeplearning_2fmodel_5fio_2fproto_2ftrt_5fmodel_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::TRTModelProto* Arena::CreateMaybeMessage< ::TRTModelProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::TRTModelProto >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

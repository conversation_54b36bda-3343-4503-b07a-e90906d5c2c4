"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.deeplearning.model_io.proto.metadata_pb2 import (
    ModelMetadataProto as deeplearning___model_io___proto___metadata_pb2___ModelMetadataProto,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class TRTModelProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    engine_bytes: builtin___bytes = ...
    input_names: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    output_names: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    implicit_batch_dimension: builtin___bool = ...
    tensorrt_version: typing___Text = ...

    @property
    def metadata(self) -> deeplearning___model_io___proto___metadata_pb2___ModelMetadataProto: ...

    def __init__(self,
        *,
        engine_bytes : typing___Optional[builtin___bytes] = None,
        metadata : typing___Optional[deeplearning___model_io___proto___metadata_pb2___ModelMetadataProto] = None,
        input_names : typing___Optional[typing___Iterable[typing___Text]] = None,
        output_names : typing___Optional[typing___Iterable[typing___Text]] = None,
        implicit_batch_dimension : typing___Optional[builtin___bool] = None,
        tensorrt_version : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"metadata",b"metadata"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"engine_bytes",b"engine_bytes",u"implicit_batch_dimension",b"implicit_batch_dimension",u"input_names",b"input_names",u"metadata",b"metadata",u"output_names",b"output_names",u"tensorrt_version",b"tensorrt_version"]) -> None: ...
type___TRTModelProto = TRTModelProto

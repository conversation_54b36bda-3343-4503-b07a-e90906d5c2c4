// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/model_io/proto/metadata.proto

#include "deeplearning/model_io/proto/metadata.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
constexpr SizeProto::SizeProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : width_(0)
  , height_(0){}
struct SizeProtoDefaultTypeInternal {
  constexpr SizeProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~SizeProtoDefaultTypeInternal() {}
  union {
    SizeProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT SizeProtoDefaultTypeInternal _SizeProto_default_instance_;
constexpr ScalerShifterParameterProto::ScalerShifterParameterProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : min_real_(0)
  , max_real_(0)
  , min_scale_(0)
  , max_scale_(0)
  , shift_real_(0)
  , shift_ind_(0){}
struct ScalerShifterParameterProtoDefaultTypeInternal {
  constexpr ScalerShifterParameterProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ScalerShifterParameterProtoDefaultTypeInternal() {}
  union {
    ScalerShifterParameterProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ScalerShifterParameterProtoDefaultTypeInternal _ScalerShifterParameterProto_default_instance_;
constexpr ModelMetadataProto::ModelMetadataProto(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : means_()
  , stds_()
  , supported_classes_()
  , use_cases_()
  , aux_input_sizes_()
  , weed_point_classes_()
  , crop_point_classes_()
  , segm_classes_()
  , line_classes_()
  , crop_ids_()
  , input_dtype_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , experiment_url_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_type_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , model_class_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , backbone_architecture_(&::PROTOBUF_NAMESPACE_ID::internal::fixed_address_empty_string)
  , input_size_(nullptr)
  , tile_(nullptr)
  , scaler_shifter_parameters_(nullptr)
  , ppi_(0)
  , max_batch_size_(0)
  , version_(0)
  , supports_depth_(false)
  , supports_half_(false)
  , not_interleaved_(false)
  , plant_enabled_(false)
  , discard_points_border_px_(0)
  , trained_embeddings_(false)
  , contains_pumap_head_(false)
  , crop_embeddings_(false){}
struct ModelMetadataProtoDefaultTypeInternal {
  constexpr ModelMetadataProtoDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~ModelMetadataProtoDefaultTypeInternal() {}
  union {
    ModelMetadataProto _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT ModelMetadataProtoDefaultTypeInternal _ModelMetadataProto_default_instance_;
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto[3];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto = nullptr;

const uint32_t TableStruct_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::SizeProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::SizeProto, width_),
  PROTOBUF_FIELD_OFFSET(::SizeProto, height_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ScalerShifterParameterProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ScalerShifterParameterProto, min_real_),
  PROTOBUF_FIELD_OFFSET(::ScalerShifterParameterProto, max_real_),
  PROTOBUF_FIELD_OFFSET(::ScalerShifterParameterProto, min_scale_),
  PROTOBUF_FIELD_OFFSET(::ScalerShifterParameterProto, max_scale_),
  PROTOBUF_FIELD_OFFSET(::ScalerShifterParameterProto, shift_real_),
  PROTOBUF_FIELD_OFFSET(::ScalerShifterParameterProto, shift_ind_),
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, input_dtype_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, input_size_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, means_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, stds_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, experiment_url_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, supported_classes_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, use_cases_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, supports_depth_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, ppi_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, tile_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, supports_half_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, max_batch_size_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, aux_input_sizes_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, version_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, model_type_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, not_interleaved_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, discard_points_border_px_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, weed_point_classes_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, crop_point_classes_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, segm_classes_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, line_classes_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, model_class_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, plant_enabled_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, trained_embeddings_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, contains_pumap_head_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, backbone_architecture_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, scaler_shifter_parameters_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, crop_ids_),
  PROTOBUF_FIELD_OFFSET(::ModelMetadataProto, crop_embeddings_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::SizeProto)},
  { 8, -1, -1, sizeof(::ScalerShifterParameterProto)},
  { 20, -1, -1, sizeof(::ModelMetadataProto)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::_SizeProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::_ScalerShifterParameterProto_default_instance_),
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::_ModelMetadataProto_default_instance_),
};

const char descriptor_table_protodef_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n*deeplearning/model_io/proto/metadata.p"
  "roto\"*\n\tSizeProto\022\r\n\005width\030\001 \001(\005\022\016\n\006heig"
  "ht\030\002 \001(\005\"\216\001\n\033ScalerShifterParameterProto"
  "\022\020\n\010min_real\030\001 \001(\002\022\020\n\010max_real\030\002 \001(\002\022\021\n\t"
  "min_scale\030\003 \001(\002\022\021\n\tmax_scale\030\004 \001(\002\022\022\n\nsh"
  "ift_real\030\005 \001(\002\022\021\n\tshift_ind\030\006 \001(\005\"\363\005\n\022Mo"
  "delMetadataProto\022\023\n\013input_dtype\030\001 \001(\t\022\036\n"
  "\ninput_size\030\002 \001(\0132\n.SizeProto\022\r\n\005means\030\003"
  " \003(\002\022\014\n\004stds\030\004 \003(\002\022\026\n\016experiment_url\030\005 \001"
  "(\t\022\031\n\021supported_classes\030\006 \003(\t\022\021\n\tuse_cas"
  "es\030\007 \003(\t\022\026\n\016supports_depth\030\010 \001(\010\022\013\n\003ppi\030"
  "\t \001(\002\022\030\n\004tile\030\n \001(\0132\n.SizeProto\022\025\n\rsuppo"
  "rts_half\030\013 \001(\010\022\026\n\016max_batch_size\030\014 \001(\005\022#"
  "\n\017aux_input_sizes\030\r \003(\0132\n.SizeProto\022\017\n\007v"
  "ersion\030\016 \001(\005\022\022\n\nmodel_type\030\017 \001(\t\022\027\n\017not_"
  "interleaved\030\020 \001(\010\022 \n\030discard_points_bord"
  "er_px\030\021 \001(\002\022\032\n\022weed_point_classes\030\022 \003(\t\022"
  "\032\n\022crop_point_classes\030\023 \003(\t\022\024\n\014segm_clas"
  "ses\030\024 \003(\t\022\024\n\014line_classes\030\025 \003(\t\022\023\n\013model"
  "_class\030\026 \001(\t\022\025\n\rplant_enabled\030\027 \001(\010\022\032\n\022t"
  "rained_embeddings\030\030 \001(\010\022\033\n\023contains_puma"
  "p_head\030\031 \001(\010\022\035\n\025backbone_architecture\030\032 "
  "\001(\t\022\?\n\031scaler_shifter_parameters\030\033 \001(\0132\034"
  ".ScalerShifterParameterProto\022\020\n\010crop_ids"
  "\030\034 \003(\t\022\027\n\017crop_embeddings\030\035 \001(\010b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto = {
  false, false, 999, descriptor_table_protodef_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto, "deeplearning/model_io/proto/metadata.proto", 
  &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_once, nullptr, 0, 3,
  schemas, file_default_instances, TableStruct_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto::offsets,
  file_level_metadata_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto, file_level_enum_descriptors_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto, file_level_service_descriptors_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_getter() {
  return &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto(&descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto);

// ===================================================================

class SizeProto::_Internal {
 public:
};

SizeProto::SizeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:SizeProto)
}
SizeProto::SizeProto(const SizeProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&width_, &from.width_,
    static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&width_)) + sizeof(height_));
  // @@protoc_insertion_point(copy_constructor:SizeProto)
}

inline void SizeProto::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&width_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&height_) -
    reinterpret_cast<char*>(&width_)) + sizeof(height_));
}

SizeProto::~SizeProto() {
  // @@protoc_insertion_point(destructor:SizeProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void SizeProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void SizeProto::ArenaDtor(void* object) {
  SizeProto* _this = reinterpret_cast< SizeProto* >(object);
  (void)_this;
}
void SizeProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void SizeProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void SizeProto::Clear() {
// @@protoc_insertion_point(message_clear_start:SizeProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&width_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&height_) -
      reinterpret_cast<char*>(&width_)) + sizeof(height_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* SizeProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int32 width = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* SizeProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:SizeProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int32 width = 1;
  if (this->_internal_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(1, this->_internal_width(), target);
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(2, this->_internal_height(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:SizeProto)
  return target;
}

size_t SizeProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:SizeProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // int32 width = 1;
  if (this->_internal_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_width());
  }

  // int32 height = 2;
  if (this->_internal_height() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_height());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData SizeProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    SizeProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*SizeProto::GetClassData() const { return &_class_data_; }

void SizeProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<SizeProto *>(to)->MergeFrom(
      static_cast<const SizeProto &>(from));
}


void SizeProto::MergeFrom(const SizeProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:SizeProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  if (from._internal_width() != 0) {
    _internal_set_width(from._internal_width());
  }
  if (from._internal_height() != 0) {
    _internal_set_height(from._internal_height());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void SizeProto::CopyFrom(const SizeProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:SizeProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool SizeProto::IsInitialized() const {
  return true;
}

void SizeProto::InternalSwap(SizeProto* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(SizeProto, height_)
      + sizeof(SizeProto::height_)
      - PROTOBUF_FIELD_OFFSET(SizeProto, width_)>(
          reinterpret_cast<char*>(&width_),
          reinterpret_cast<char*>(&other->width_));
}

::PROTOBUF_NAMESPACE_ID::Metadata SizeProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_getter, &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_once,
      file_level_metadata_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto[0]);
}

// ===================================================================

class ScalerShifterParameterProto::_Internal {
 public:
};

ScalerShifterParameterProto::ScalerShifterParameterProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ScalerShifterParameterProto)
}
ScalerShifterParameterProto::ScalerShifterParameterProto(const ScalerShifterParameterProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message() {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&min_real_, &from.min_real_,
    static_cast<size_t>(reinterpret_cast<char*>(&shift_ind_) -
    reinterpret_cast<char*>(&min_real_)) + sizeof(shift_ind_));
  // @@protoc_insertion_point(copy_constructor:ScalerShifterParameterProto)
}

inline void ScalerShifterParameterProto::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&min_real_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&shift_ind_) -
    reinterpret_cast<char*>(&min_real_)) + sizeof(shift_ind_));
}

ScalerShifterParameterProto::~ScalerShifterParameterProto() {
  // @@protoc_insertion_point(destructor:ScalerShifterParameterProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ScalerShifterParameterProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void ScalerShifterParameterProto::ArenaDtor(void* object) {
  ScalerShifterParameterProto* _this = reinterpret_cast< ScalerShifterParameterProto* >(object);
  (void)_this;
}
void ScalerShifterParameterProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ScalerShifterParameterProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ScalerShifterParameterProto::Clear() {
// @@protoc_insertion_point(message_clear_start:ScalerShifterParameterProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  ::memset(&min_real_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&shift_ind_) -
      reinterpret_cast<char*>(&min_real_)) + sizeof(shift_ind_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ScalerShifterParameterProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // float min_real = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 13)) {
          min_real_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float max_real = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 21)) {
          max_real_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float min_scale = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          min_scale_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float max_scale = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 37)) {
          max_scale_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float shift_real = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 45)) {
          shift_real_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // int32 shift_ind = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 48)) {
          shift_ind_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ScalerShifterParameterProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ScalerShifterParameterProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // float min_real = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_real = this->_internal_min_real();
  uint32_t raw_min_real;
  memcpy(&raw_min_real, &tmp_min_real, sizeof(tmp_min_real));
  if (raw_min_real != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(1, this->_internal_min_real(), target);
  }

  // float max_real = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_real = this->_internal_max_real();
  uint32_t raw_max_real;
  memcpy(&raw_max_real, &tmp_max_real, sizeof(tmp_max_real));
  if (raw_max_real != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(2, this->_internal_max_real(), target);
  }

  // float min_scale = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_scale = this->_internal_min_scale();
  uint32_t raw_min_scale;
  memcpy(&raw_min_scale, &tmp_min_scale, sizeof(tmp_min_scale));
  if (raw_min_scale != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_min_scale(), target);
  }

  // float max_scale = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_scale = this->_internal_max_scale();
  uint32_t raw_max_scale;
  memcpy(&raw_max_scale, &tmp_max_scale, sizeof(tmp_max_scale));
  if (raw_max_scale != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(4, this->_internal_max_scale(), target);
  }

  // float shift_real = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_shift_real = this->_internal_shift_real();
  uint32_t raw_shift_real;
  memcpy(&raw_shift_real, &tmp_shift_real, sizeof(tmp_shift_real));
  if (raw_shift_real != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(5, this->_internal_shift_real(), target);
  }

  // int32 shift_ind = 6;
  if (this->_internal_shift_ind() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(6, this->_internal_shift_ind(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ScalerShifterParameterProto)
  return target;
}

size_t ScalerShifterParameterProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ScalerShifterParameterProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // float min_real = 1;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_real = this->_internal_min_real();
  uint32_t raw_min_real;
  memcpy(&raw_min_real, &tmp_min_real, sizeof(tmp_min_real));
  if (raw_min_real != 0) {
    total_size += 1 + 4;
  }

  // float max_real = 2;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_real = this->_internal_max_real();
  uint32_t raw_max_real;
  memcpy(&raw_max_real, &tmp_max_real, sizeof(tmp_max_real));
  if (raw_max_real != 0) {
    total_size += 1 + 4;
  }

  // float min_scale = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_scale = this->_internal_min_scale();
  uint32_t raw_min_scale;
  memcpy(&raw_min_scale, &tmp_min_scale, sizeof(tmp_min_scale));
  if (raw_min_scale != 0) {
    total_size += 1 + 4;
  }

  // float max_scale = 4;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_scale = this->_internal_max_scale();
  uint32_t raw_max_scale;
  memcpy(&raw_max_scale, &tmp_max_scale, sizeof(tmp_max_scale));
  if (raw_max_scale != 0) {
    total_size += 1 + 4;
  }

  // float shift_real = 5;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_shift_real = this->_internal_shift_real();
  uint32_t raw_shift_real;
  memcpy(&raw_shift_real, &tmp_shift_real, sizeof(tmp_shift_real));
  if (raw_shift_real != 0) {
    total_size += 1 + 4;
  }

  // int32 shift_ind = 6;
  if (this->_internal_shift_ind() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_shift_ind());
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ScalerShifterParameterProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ScalerShifterParameterProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ScalerShifterParameterProto::GetClassData() const { return &_class_data_; }

void ScalerShifterParameterProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ScalerShifterParameterProto *>(to)->MergeFrom(
      static_cast<const ScalerShifterParameterProto &>(from));
}


void ScalerShifterParameterProto::MergeFrom(const ScalerShifterParameterProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ScalerShifterParameterProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_real = from._internal_min_real();
  uint32_t raw_min_real;
  memcpy(&raw_min_real, &tmp_min_real, sizeof(tmp_min_real));
  if (raw_min_real != 0) {
    _internal_set_min_real(from._internal_min_real());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_real = from._internal_max_real();
  uint32_t raw_max_real;
  memcpy(&raw_max_real, &tmp_max_real, sizeof(tmp_max_real));
  if (raw_max_real != 0) {
    _internal_set_max_real(from._internal_max_real());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_scale = from._internal_min_scale();
  uint32_t raw_min_scale;
  memcpy(&raw_min_scale, &tmp_min_scale, sizeof(tmp_min_scale));
  if (raw_min_scale != 0) {
    _internal_set_min_scale(from._internal_min_scale());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_max_scale = from._internal_max_scale();
  uint32_t raw_max_scale;
  memcpy(&raw_max_scale, &tmp_max_scale, sizeof(tmp_max_scale));
  if (raw_max_scale != 0) {
    _internal_set_max_scale(from._internal_max_scale());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_shift_real = from._internal_shift_real();
  uint32_t raw_shift_real;
  memcpy(&raw_shift_real, &tmp_shift_real, sizeof(tmp_shift_real));
  if (raw_shift_real != 0) {
    _internal_set_shift_real(from._internal_shift_real());
  }
  if (from._internal_shift_ind() != 0) {
    _internal_set_shift_ind(from._internal_shift_ind());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ScalerShifterParameterProto::CopyFrom(const ScalerShifterParameterProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ScalerShifterParameterProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ScalerShifterParameterProto::IsInitialized() const {
  return true;
}

void ScalerShifterParameterProto::InternalSwap(ScalerShifterParameterProto* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ScalerShifterParameterProto, shift_ind_)
      + sizeof(ScalerShifterParameterProto::shift_ind_)
      - PROTOBUF_FIELD_OFFSET(ScalerShifterParameterProto, min_real_)>(
          reinterpret_cast<char*>(&min_real_),
          reinterpret_cast<char*>(&other->min_real_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ScalerShifterParameterProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_getter, &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_once,
      file_level_metadata_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto[1]);
}

// ===================================================================

class ModelMetadataProto::_Internal {
 public:
  static const ::SizeProto& input_size(const ModelMetadataProto* msg);
  static const ::SizeProto& tile(const ModelMetadataProto* msg);
  static const ::ScalerShifterParameterProto& scaler_shifter_parameters(const ModelMetadataProto* msg);
};

const ::SizeProto&
ModelMetadataProto::_Internal::input_size(const ModelMetadataProto* msg) {
  return *msg->input_size_;
}
const ::SizeProto&
ModelMetadataProto::_Internal::tile(const ModelMetadataProto* msg) {
  return *msg->tile_;
}
const ::ScalerShifterParameterProto&
ModelMetadataProto::_Internal::scaler_shifter_parameters(const ModelMetadataProto* msg) {
  return *msg->scaler_shifter_parameters_;
}
ModelMetadataProto::ModelMetadataProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  means_(arena),
  stds_(arena),
  supported_classes_(arena),
  use_cases_(arena),
  aux_input_sizes_(arena),
  weed_point_classes_(arena),
  crop_point_classes_(arena),
  segm_classes_(arena),
  line_classes_(arena),
  crop_ids_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:ModelMetadataProto)
}
ModelMetadataProto::ModelMetadataProto(const ModelMetadataProto& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      means_(from.means_),
      stds_(from.stds_),
      supported_classes_(from.supported_classes_),
      use_cases_(from.use_cases_),
      aux_input_sizes_(from.aux_input_sizes_),
      weed_point_classes_(from.weed_point_classes_),
      crop_point_classes_(from.crop_point_classes_),
      segm_classes_(from.segm_classes_),
      line_classes_(from.line_classes_),
      crop_ids_(from.crop_ids_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  input_dtype_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    input_dtype_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_input_dtype().empty()) {
    input_dtype_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_input_dtype(), 
      GetArenaForAllocation());
  }
  experiment_url_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    experiment_url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_experiment_url().empty()) {
    experiment_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_experiment_url(), 
      GetArenaForAllocation());
  }
  model_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_type().empty()) {
    model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_type(), 
      GetArenaForAllocation());
  }
  model_class_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    model_class_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_model_class().empty()) {
    model_class_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_model_class(), 
      GetArenaForAllocation());
  }
  backbone_architecture_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  #ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
    backbone_architecture_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  #endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (!from._internal_backbone_architecture().empty()) {
    backbone_architecture_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, from._internal_backbone_architecture(), 
      GetArenaForAllocation());
  }
  if (from._internal_has_input_size()) {
    input_size_ = new ::SizeProto(*from.input_size_);
  } else {
    input_size_ = nullptr;
  }
  if (from._internal_has_tile()) {
    tile_ = new ::SizeProto(*from.tile_);
  } else {
    tile_ = nullptr;
  }
  if (from._internal_has_scaler_shifter_parameters()) {
    scaler_shifter_parameters_ = new ::ScalerShifterParameterProto(*from.scaler_shifter_parameters_);
  } else {
    scaler_shifter_parameters_ = nullptr;
  }
  ::memcpy(&ppi_, &from.ppi_,
    static_cast<size_t>(reinterpret_cast<char*>(&crop_embeddings_) -
    reinterpret_cast<char*>(&ppi_)) + sizeof(crop_embeddings_));
  // @@protoc_insertion_point(copy_constructor:ModelMetadataProto)
}

inline void ModelMetadataProto::SharedCtor() {
input_dtype_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  input_dtype_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
experiment_url_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  experiment_url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_type_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
model_class_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  model_class_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
backbone_architecture_.UnsafeSetDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  backbone_architecture_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&input_size_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&crop_embeddings_) -
    reinterpret_cast<char*>(&input_size_)) + sizeof(crop_embeddings_));
}

ModelMetadataProto::~ModelMetadataProto() {
  // @@protoc_insertion_point(destructor:ModelMetadataProto)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void ModelMetadataProto::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
  input_dtype_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  experiment_url_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_type_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  model_class_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  backbone_architecture_.DestroyNoArena(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited());
  if (this != internal_default_instance()) delete input_size_;
  if (this != internal_default_instance()) delete tile_;
  if (this != internal_default_instance()) delete scaler_shifter_parameters_;
}

void ModelMetadataProto::ArenaDtor(void* object) {
  ModelMetadataProto* _this = reinterpret_cast< ModelMetadataProto* >(object);
  (void)_this;
}
void ModelMetadataProto::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void ModelMetadataProto::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void ModelMetadataProto::Clear() {
// @@protoc_insertion_point(message_clear_start:ModelMetadataProto)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  means_.Clear();
  stds_.Clear();
  supported_classes_.Clear();
  use_cases_.Clear();
  aux_input_sizes_.Clear();
  weed_point_classes_.Clear();
  crop_point_classes_.Clear();
  segm_classes_.Clear();
  line_classes_.Clear();
  crop_ids_.Clear();
  input_dtype_.ClearToEmpty();
  experiment_url_.ClearToEmpty();
  model_type_.ClearToEmpty();
  model_class_.ClearToEmpty();
  backbone_architecture_.ClearToEmpty();
  if (GetArenaForAllocation() == nullptr && input_size_ != nullptr) {
    delete input_size_;
  }
  input_size_ = nullptr;
  if (GetArenaForAllocation() == nullptr && tile_ != nullptr) {
    delete tile_;
  }
  tile_ = nullptr;
  if (GetArenaForAllocation() == nullptr && scaler_shifter_parameters_ != nullptr) {
    delete scaler_shifter_parameters_;
  }
  scaler_shifter_parameters_ = nullptr;
  ::memset(&ppi_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&crop_embeddings_) -
      reinterpret_cast<char*>(&ppi_)) + sizeof(crop_embeddings_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* ModelMetadataProto::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // string input_dtype = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 10)) {
          auto str = _internal_mutable_input_dtype();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.input_dtype"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .SizeProto input_size = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 18)) {
          ptr = ctx->ParseMessage(_internal_mutable_input_size(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated float means = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 26)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_means(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 29) {
          _internal_add_means(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated float stds = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 34)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedFloatParser(_internal_mutable_stds(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 37) {
          _internal_add_stds(::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr));
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // string experiment_url = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          auto str = _internal_mutable_experiment_url();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.experiment_url"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string supported_classes = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_supported_classes();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.supported_classes"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<50>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string use_cases = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr -= 1;
          do {
            ptr += 1;
            auto str = _internal_add_use_cases();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.use_cases"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<58>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool supports_depth = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 64)) {
          supports_depth_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float ppi = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          ppi_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .SizeProto tile = 10;
      case 10:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 82)) {
          ptr = ctx->ParseMessage(_internal_mutable_tile(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool supports_half = 11;
      case 11:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 88)) {
          supports_half_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int32 max_batch_size = 12;
      case 12:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 96)) {
          max_batch_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated .SizeProto aux_input_sizes = 13;
      case 13:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 106)) {
          ptr -= 1;
          do {
            ptr += 1;
            ptr = ctx->ParseMessage(_internal_add_aux_input_sizes(), ptr);
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<106>(ptr));
        } else
          goto handle_unusual;
        continue;
      // int32 version = 14;
      case 14:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 112)) {
          version_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint32(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string model_type = 15;
      case 15:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 122)) {
          auto str = _internal_mutable_model_type();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.model_type"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool not_interleaved = 16;
      case 16:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 128)) {
          not_interleaved_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float discard_points_border_px = 17;
      case 17:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 141)) {
          discard_points_border_px_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // repeated string weed_point_classes = 18;
      case 18:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 146)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_weed_point_classes();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.weed_point_classes"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<146>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string crop_point_classes = 19;
      case 19:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 154)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_crop_point_classes();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.crop_point_classes"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<154>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string segm_classes = 20;
      case 20:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 162)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_segm_classes();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.segm_classes"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<162>(ptr));
        } else
          goto handle_unusual;
        continue;
      // repeated string line_classes = 21;
      case 21:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 170)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_line_classes();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.line_classes"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<170>(ptr));
        } else
          goto handle_unusual;
        continue;
      // string model_class = 22;
      case 22:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 178)) {
          auto str = _internal_mutable_model_class();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.model_class"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool plant_enabled = 23;
      case 23:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 184)) {
          plant_enabled_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool trained_embeddings = 24;
      case 24:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 192)) {
          trained_embeddings_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // bool contains_pumap_head = 25;
      case 25:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 200)) {
          contains_pumap_head_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // string backbone_architecture = 26;
      case 26:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 210)) {
          auto str = _internal_mutable_backbone_architecture();
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
          CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.backbone_architecture"));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .ScalerShifterParameterProto scaler_shifter_parameters = 27;
      case 27:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 218)) {
          ptr = ctx->ParseMessage(_internal_mutable_scaler_shifter_parameters(), ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated string crop_ids = 28;
      case 28:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 226)) {
          ptr -= 2;
          do {
            ptr += 2;
            auto str = _internal_add_crop_ids();
            ptr = ::PROTOBUF_NAMESPACE_ID::internal::InlineGreedyStringParser(str, ptr, ctx);
            CHK_(::PROTOBUF_NAMESPACE_ID::internal::VerifyUTF8(str, "ModelMetadataProto.crop_ids"));
            CHK_(ptr);
            if (!ctx->DataAvailable(ptr)) break;
          } while (::PROTOBUF_NAMESPACE_ID::internal::ExpectTag<226>(ptr));
        } else
          goto handle_unusual;
        continue;
      // bool crop_embeddings = 29;
      case 29:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 232)) {
          crop_embeddings_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* ModelMetadataProto::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:ModelMetadataProto)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // string input_dtype = 1;
  if (!this->_internal_input_dtype().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_input_dtype().data(), static_cast<int>(this->_internal_input_dtype().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.input_dtype");
    target = stream->WriteStringMaybeAliased(
        1, this->_internal_input_dtype(), target);
  }

  // .SizeProto input_size = 2;
  if (this->_internal_has_input_size()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        2, _Internal::input_size(this), target, stream);
  }

  // repeated float means = 3;
  if (this->_internal_means_size() > 0) {
    target = stream->WriteFixedPacked(3, _internal_means(), target);
  }

  // repeated float stds = 4;
  if (this->_internal_stds_size() > 0) {
    target = stream->WriteFixedPacked(4, _internal_stds(), target);
  }

  // string experiment_url = 5;
  if (!this->_internal_experiment_url().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_experiment_url().data(), static_cast<int>(this->_internal_experiment_url().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.experiment_url");
    target = stream->WriteStringMaybeAliased(
        5, this->_internal_experiment_url(), target);
  }

  // repeated string supported_classes = 6;
  for (int i = 0, n = this->_internal_supported_classes_size(); i < n; i++) {
    const auto& s = this->_internal_supported_classes(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.supported_classes");
    target = stream->WriteString(6, s, target);
  }

  // repeated string use_cases = 7;
  for (int i = 0, n = this->_internal_use_cases_size(); i < n; i++) {
    const auto& s = this->_internal_use_cases(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.use_cases");
    target = stream->WriteString(7, s, target);
  }

  // bool supports_depth = 8;
  if (this->_internal_supports_depth() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(8, this->_internal_supports_depth(), target);
  }

  // float ppi = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ppi = this->_internal_ppi();
  uint32_t raw_ppi;
  memcpy(&raw_ppi, &tmp_ppi, sizeof(tmp_ppi));
  if (raw_ppi != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_ppi(), target);
  }

  // .SizeProto tile = 10;
  if (this->_internal_has_tile()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        10, _Internal::tile(this), target, stream);
  }

  // bool supports_half = 11;
  if (this->_internal_supports_half() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(11, this->_internal_supports_half(), target);
  }

  // int32 max_batch_size = 12;
  if (this->_internal_max_batch_size() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(12, this->_internal_max_batch_size(), target);
  }

  // repeated .SizeProto aux_input_sizes = 13;
  for (unsigned int i = 0,
      n = static_cast<unsigned int>(this->_internal_aux_input_sizes_size()); i < n; i++) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(13, this->_internal_aux_input_sizes(i), target, stream);
  }

  // int32 version = 14;
  if (this->_internal_version() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt32ToArray(14, this->_internal_version(), target);
  }

  // string model_type = 15;
  if (!this->_internal_model_type().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_type().data(), static_cast<int>(this->_internal_model_type().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.model_type");
    target = stream->WriteStringMaybeAliased(
        15, this->_internal_model_type(), target);
  }

  // bool not_interleaved = 16;
  if (this->_internal_not_interleaved() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(16, this->_internal_not_interleaved(), target);
  }

  // float discard_points_border_px = 17;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_discard_points_border_px = this->_internal_discard_points_border_px();
  uint32_t raw_discard_points_border_px;
  memcpy(&raw_discard_points_border_px, &tmp_discard_points_border_px, sizeof(tmp_discard_points_border_px));
  if (raw_discard_points_border_px != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(17, this->_internal_discard_points_border_px(), target);
  }

  // repeated string weed_point_classes = 18;
  for (int i = 0, n = this->_internal_weed_point_classes_size(); i < n; i++) {
    const auto& s = this->_internal_weed_point_classes(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.weed_point_classes");
    target = stream->WriteString(18, s, target);
  }

  // repeated string crop_point_classes = 19;
  for (int i = 0, n = this->_internal_crop_point_classes_size(); i < n; i++) {
    const auto& s = this->_internal_crop_point_classes(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.crop_point_classes");
    target = stream->WriteString(19, s, target);
  }

  // repeated string segm_classes = 20;
  for (int i = 0, n = this->_internal_segm_classes_size(); i < n; i++) {
    const auto& s = this->_internal_segm_classes(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.segm_classes");
    target = stream->WriteString(20, s, target);
  }

  // repeated string line_classes = 21;
  for (int i = 0, n = this->_internal_line_classes_size(); i < n; i++) {
    const auto& s = this->_internal_line_classes(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.line_classes");
    target = stream->WriteString(21, s, target);
  }

  // string model_class = 22;
  if (!this->_internal_model_class().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_model_class().data(), static_cast<int>(this->_internal_model_class().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.model_class");
    target = stream->WriteStringMaybeAliased(
        22, this->_internal_model_class(), target);
  }

  // bool plant_enabled = 23;
  if (this->_internal_plant_enabled() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(23, this->_internal_plant_enabled(), target);
  }

  // bool trained_embeddings = 24;
  if (this->_internal_trained_embeddings() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(24, this->_internal_trained_embeddings(), target);
  }

  // bool contains_pumap_head = 25;
  if (this->_internal_contains_pumap_head() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(25, this->_internal_contains_pumap_head(), target);
  }

  // string backbone_architecture = 26;
  if (!this->_internal_backbone_architecture().empty()) {
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      this->_internal_backbone_architecture().data(), static_cast<int>(this->_internal_backbone_architecture().length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.backbone_architecture");
    target = stream->WriteStringMaybeAliased(
        26, this->_internal_backbone_architecture(), target);
  }

  // .ScalerShifterParameterProto scaler_shifter_parameters = 27;
  if (this->_internal_has_scaler_shifter_parameters()) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      InternalWriteMessage(
        27, _Internal::scaler_shifter_parameters(this), target, stream);
  }

  // repeated string crop_ids = 28;
  for (int i = 0, n = this->_internal_crop_ids_size(); i < n; i++) {
    const auto& s = this->_internal_crop_ids(i);
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(
      s.data(), static_cast<int>(s.length()),
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::SERIALIZE,
      "ModelMetadataProto.crop_ids");
    target = stream->WriteString(28, s, target);
  }

  // bool crop_embeddings = 29;
  if (this->_internal_crop_embeddings() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteBoolToArray(29, this->_internal_crop_embeddings(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:ModelMetadataProto)
  return target;
}

size_t ModelMetadataProto::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:ModelMetadataProto)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated float means = 3;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_means_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated float stds = 4;
  {
    unsigned int count = static_cast<unsigned int>(this->_internal_stds_size());
    size_t data_size = 4UL * count;
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    total_size += data_size;
  }

  // repeated string supported_classes = 6;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(supported_classes_.size());
  for (int i = 0, n = supported_classes_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      supported_classes_.Get(i));
  }

  // repeated string use_cases = 7;
  total_size += 1 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(use_cases_.size());
  for (int i = 0, n = use_cases_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      use_cases_.Get(i));
  }

  // repeated .SizeProto aux_input_sizes = 13;
  total_size += 1UL * this->_internal_aux_input_sizes_size();
  for (const auto& msg : this->aux_input_sizes_) {
    total_size +=
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(msg);
  }

  // repeated string weed_point_classes = 18;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(weed_point_classes_.size());
  for (int i = 0, n = weed_point_classes_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      weed_point_classes_.Get(i));
  }

  // repeated string crop_point_classes = 19;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(crop_point_classes_.size());
  for (int i = 0, n = crop_point_classes_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      crop_point_classes_.Get(i));
  }

  // repeated string segm_classes = 20;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(segm_classes_.size());
  for (int i = 0, n = segm_classes_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      segm_classes_.Get(i));
  }

  // repeated string line_classes = 21;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(line_classes_.size());
  for (int i = 0, n = line_classes_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      line_classes_.Get(i));
  }

  // repeated string crop_ids = 28;
  total_size += 2 *
      ::PROTOBUF_NAMESPACE_ID::internal::FromIntSize(crop_ids_.size());
  for (int i = 0, n = crop_ids_.size(); i < n; i++) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
      crop_ids_.Get(i));
  }

  // string input_dtype = 1;
  if (!this->_internal_input_dtype().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_input_dtype());
  }

  // string experiment_url = 5;
  if (!this->_internal_experiment_url().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_experiment_url());
  }

  // string model_type = 15;
  if (!this->_internal_model_type().empty()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_type());
  }

  // string model_class = 22;
  if (!this->_internal_model_class().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_model_class());
  }

  // string backbone_architecture = 26;
  if (!this->_internal_backbone_architecture().empty()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::StringSize(
        this->_internal_backbone_architecture());
  }

  // .SizeProto input_size = 2;
  if (this->_internal_has_input_size()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *input_size_);
  }

  // .SizeProto tile = 10;
  if (this->_internal_has_tile()) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *tile_);
  }

  // .ScalerShifterParameterProto scaler_shifter_parameters = 27;
  if (this->_internal_has_scaler_shifter_parameters()) {
    total_size += 2 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::MessageSize(
        *scaler_shifter_parameters_);
  }

  // float ppi = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ppi = this->_internal_ppi();
  uint32_t raw_ppi;
  memcpy(&raw_ppi, &tmp_ppi, sizeof(tmp_ppi));
  if (raw_ppi != 0) {
    total_size += 1 + 4;
  }

  // int32 max_batch_size = 12;
  if (this->_internal_max_batch_size() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_max_batch_size());
  }

  // int32 version = 14;
  if (this->_internal_version() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32SizePlusOne(this->_internal_version());
  }

  // bool supports_depth = 8;
  if (this->_internal_supports_depth() != 0) {
    total_size += 1 + 1;
  }

  // bool supports_half = 11;
  if (this->_internal_supports_half() != 0) {
    total_size += 1 + 1;
  }

  // bool not_interleaved = 16;
  if (this->_internal_not_interleaved() != 0) {
    total_size += 2 + 1;
  }

  // bool plant_enabled = 23;
  if (this->_internal_plant_enabled() != 0) {
    total_size += 2 + 1;
  }

  // float discard_points_border_px = 17;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_discard_points_border_px = this->_internal_discard_points_border_px();
  uint32_t raw_discard_points_border_px;
  memcpy(&raw_discard_points_border_px, &tmp_discard_points_border_px, sizeof(tmp_discard_points_border_px));
  if (raw_discard_points_border_px != 0) {
    total_size += 2 + 4;
  }

  // bool trained_embeddings = 24;
  if (this->_internal_trained_embeddings() != 0) {
    total_size += 2 + 1;
  }

  // bool contains_pumap_head = 25;
  if (this->_internal_contains_pumap_head() != 0) {
    total_size += 2 + 1;
  }

  // bool crop_embeddings = 29;
  if (this->_internal_crop_embeddings() != 0) {
    total_size += 2 + 1;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData ModelMetadataProto::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    ModelMetadataProto::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*ModelMetadataProto::GetClassData() const { return &_class_data_; }

void ModelMetadataProto::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<ModelMetadataProto *>(to)->MergeFrom(
      static_cast<const ModelMetadataProto &>(from));
}


void ModelMetadataProto::MergeFrom(const ModelMetadataProto& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:ModelMetadataProto)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  means_.MergeFrom(from.means_);
  stds_.MergeFrom(from.stds_);
  supported_classes_.MergeFrom(from.supported_classes_);
  use_cases_.MergeFrom(from.use_cases_);
  aux_input_sizes_.MergeFrom(from.aux_input_sizes_);
  weed_point_classes_.MergeFrom(from.weed_point_classes_);
  crop_point_classes_.MergeFrom(from.crop_point_classes_);
  segm_classes_.MergeFrom(from.segm_classes_);
  line_classes_.MergeFrom(from.line_classes_);
  crop_ids_.MergeFrom(from.crop_ids_);
  if (!from._internal_input_dtype().empty()) {
    _internal_set_input_dtype(from._internal_input_dtype());
  }
  if (!from._internal_experiment_url().empty()) {
    _internal_set_experiment_url(from._internal_experiment_url());
  }
  if (!from._internal_model_type().empty()) {
    _internal_set_model_type(from._internal_model_type());
  }
  if (!from._internal_model_class().empty()) {
    _internal_set_model_class(from._internal_model_class());
  }
  if (!from._internal_backbone_architecture().empty()) {
    _internal_set_backbone_architecture(from._internal_backbone_architecture());
  }
  if (from._internal_has_input_size()) {
    _internal_mutable_input_size()->::SizeProto::MergeFrom(from._internal_input_size());
  }
  if (from._internal_has_tile()) {
    _internal_mutable_tile()->::SizeProto::MergeFrom(from._internal_tile());
  }
  if (from._internal_has_scaler_shifter_parameters()) {
    _internal_mutable_scaler_shifter_parameters()->::ScalerShifterParameterProto::MergeFrom(from._internal_scaler_shifter_parameters());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_ppi = from._internal_ppi();
  uint32_t raw_ppi;
  memcpy(&raw_ppi, &tmp_ppi, sizeof(tmp_ppi));
  if (raw_ppi != 0) {
    _internal_set_ppi(from._internal_ppi());
  }
  if (from._internal_max_batch_size() != 0) {
    _internal_set_max_batch_size(from._internal_max_batch_size());
  }
  if (from._internal_version() != 0) {
    _internal_set_version(from._internal_version());
  }
  if (from._internal_supports_depth() != 0) {
    _internal_set_supports_depth(from._internal_supports_depth());
  }
  if (from._internal_supports_half() != 0) {
    _internal_set_supports_half(from._internal_supports_half());
  }
  if (from._internal_not_interleaved() != 0) {
    _internal_set_not_interleaved(from._internal_not_interleaved());
  }
  if (from._internal_plant_enabled() != 0) {
    _internal_set_plant_enabled(from._internal_plant_enabled());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_discard_points_border_px = from._internal_discard_points_border_px();
  uint32_t raw_discard_points_border_px;
  memcpy(&raw_discard_points_border_px, &tmp_discard_points_border_px, sizeof(tmp_discard_points_border_px));
  if (raw_discard_points_border_px != 0) {
    _internal_set_discard_points_border_px(from._internal_discard_points_border_px());
  }
  if (from._internal_trained_embeddings() != 0) {
    _internal_set_trained_embeddings(from._internal_trained_embeddings());
  }
  if (from._internal_contains_pumap_head() != 0) {
    _internal_set_contains_pumap_head(from._internal_contains_pumap_head());
  }
  if (from._internal_crop_embeddings() != 0) {
    _internal_set_crop_embeddings(from._internal_crop_embeddings());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void ModelMetadataProto::CopyFrom(const ModelMetadataProto& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:ModelMetadataProto)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool ModelMetadataProto::IsInitialized() const {
  return true;
}

void ModelMetadataProto::InternalSwap(ModelMetadataProto* other) {
  using std::swap;
  auto* lhs_arena = GetArenaForAllocation();
  auto* rhs_arena = other->GetArenaForAllocation();
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  means_.InternalSwap(&other->means_);
  stds_.InternalSwap(&other->stds_);
  supported_classes_.InternalSwap(&other->supported_classes_);
  use_cases_.InternalSwap(&other->use_cases_);
  aux_input_sizes_.InternalSwap(&other->aux_input_sizes_);
  weed_point_classes_.InternalSwap(&other->weed_point_classes_);
  crop_point_classes_.InternalSwap(&other->crop_point_classes_);
  segm_classes_.InternalSwap(&other->segm_classes_);
  line_classes_.InternalSwap(&other->line_classes_);
  crop_ids_.InternalSwap(&other->crop_ids_);
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &input_dtype_, lhs_arena,
      &other->input_dtype_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &experiment_url_, lhs_arena,
      &other->experiment_url_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_type_, lhs_arena,
      &other->model_type_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &model_class_, lhs_arena,
      &other->model_class_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::InternalSwap(
      &::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(),
      &backbone_architecture_, lhs_arena,
      &other->backbone_architecture_, rhs_arena
  );
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(ModelMetadataProto, crop_embeddings_)
      + sizeof(ModelMetadataProto::crop_embeddings_)
      - PROTOBUF_FIELD_OFFSET(ModelMetadataProto, input_size_)>(
          reinterpret_cast<char*>(&input_size_),
          reinterpret_cast<char*>(&other->input_size_));
}

::PROTOBUF_NAMESPACE_ID::Metadata ModelMetadataProto::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_getter, &descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto_once,
      file_level_metadata_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto[2]);
}

// @@protoc_insertion_point(namespace_scope)
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::SizeProto* Arena::CreateMaybeMessage< ::SizeProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::SizeProto >(arena);
}
template<> PROTOBUF_NOINLINE ::ScalerShifterParameterProto* Arena::CreateMaybeMessage< ::ScalerShifterParameterProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ScalerShifterParameterProto >(arena);
}
template<> PROTOBUF_NOINLINE ::ModelMetadataProto* Arena::CreateMaybeMessage< ::ModelMetadataProto >(Arena* arena) {
  return Arena::CreateMessageInternal< ::ModelMetadataProto >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedCompositeFieldContainer as google___protobuf___internal___containers___RepeatedCompositeFieldContainer,
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class SizeProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    width: builtin___int = ...
    height: builtin___int = ...

    def __init__(self,
        *,
        width : typing___Optional[builtin___int] = None,
        height : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"height",b"height",u"width",b"width"]) -> None: ...
type___SizeProto = SizeProto

class ScalerShifterParameterProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    min_real: builtin___float = ...
    max_real: builtin___float = ...
    min_scale: builtin___float = ...
    max_scale: builtin___float = ...
    shift_real: builtin___float = ...
    shift_ind: builtin___int = ...

    def __init__(self,
        *,
        min_real : typing___Optional[builtin___float] = None,
        max_real : typing___Optional[builtin___float] = None,
        min_scale : typing___Optional[builtin___float] = None,
        max_scale : typing___Optional[builtin___float] = None,
        shift_real : typing___Optional[builtin___float] = None,
        shift_ind : typing___Optional[builtin___int] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"max_real",b"max_real",u"max_scale",b"max_scale",u"min_real",b"min_real",u"min_scale",b"min_scale",u"shift_ind",b"shift_ind",u"shift_real",b"shift_real"]) -> None: ...
type___ScalerShifterParameterProto = ScalerShifterParameterProto

class ModelMetadataProto(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    input_dtype: typing___Text = ...
    means: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    stds: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___float] = ...
    experiment_url: typing___Text = ...
    supported_classes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    use_cases: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    supports_depth: builtin___bool = ...
    ppi: builtin___float = ...
    supports_half: builtin___bool = ...
    max_batch_size: builtin___int = ...
    version: builtin___int = ...
    model_type: typing___Text = ...
    not_interleaved: builtin___bool = ...
    discard_points_border_px: builtin___float = ...
    weed_point_classes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    crop_point_classes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    segm_classes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    line_classes: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    model_class: typing___Text = ...
    plant_enabled: builtin___bool = ...
    trained_embeddings: builtin___bool = ...
    contains_pumap_head: builtin___bool = ...
    backbone_architecture: typing___Text = ...
    crop_ids: google___protobuf___internal___containers___RepeatedScalarFieldContainer[typing___Text] = ...
    crop_embeddings: builtin___bool = ...

    @property
    def input_size(self) -> type___SizeProto: ...

    @property
    def tile(self) -> type___SizeProto: ...

    @property
    def aux_input_sizes(self) -> google___protobuf___internal___containers___RepeatedCompositeFieldContainer[type___SizeProto]: ...

    @property
    def scaler_shifter_parameters(self) -> type___ScalerShifterParameterProto: ...

    def __init__(self,
        *,
        input_dtype : typing___Optional[typing___Text] = None,
        input_size : typing___Optional[type___SizeProto] = None,
        means : typing___Optional[typing___Iterable[builtin___float]] = None,
        stds : typing___Optional[typing___Iterable[builtin___float]] = None,
        experiment_url : typing___Optional[typing___Text] = None,
        supported_classes : typing___Optional[typing___Iterable[typing___Text]] = None,
        use_cases : typing___Optional[typing___Iterable[typing___Text]] = None,
        supports_depth : typing___Optional[builtin___bool] = None,
        ppi : typing___Optional[builtin___float] = None,
        tile : typing___Optional[type___SizeProto] = None,
        supports_half : typing___Optional[builtin___bool] = None,
        max_batch_size : typing___Optional[builtin___int] = None,
        aux_input_sizes : typing___Optional[typing___Iterable[type___SizeProto]] = None,
        version : typing___Optional[builtin___int] = None,
        model_type : typing___Optional[typing___Text] = None,
        not_interleaved : typing___Optional[builtin___bool] = None,
        discard_points_border_px : typing___Optional[builtin___float] = None,
        weed_point_classes : typing___Optional[typing___Iterable[typing___Text]] = None,
        crop_point_classes : typing___Optional[typing___Iterable[typing___Text]] = None,
        segm_classes : typing___Optional[typing___Iterable[typing___Text]] = None,
        line_classes : typing___Optional[typing___Iterable[typing___Text]] = None,
        model_class : typing___Optional[typing___Text] = None,
        plant_enabled : typing___Optional[builtin___bool] = None,
        trained_embeddings : typing___Optional[builtin___bool] = None,
        contains_pumap_head : typing___Optional[builtin___bool] = None,
        backbone_architecture : typing___Optional[typing___Text] = None,
        scaler_shifter_parameters : typing___Optional[type___ScalerShifterParameterProto] = None,
        crop_ids : typing___Optional[typing___Iterable[typing___Text]] = None,
        crop_embeddings : typing___Optional[builtin___bool] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"input_size",b"input_size",u"scaler_shifter_parameters",b"scaler_shifter_parameters",u"tile",b"tile"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"aux_input_sizes",b"aux_input_sizes",u"backbone_architecture",b"backbone_architecture",u"contains_pumap_head",b"contains_pumap_head",u"crop_embeddings",b"crop_embeddings",u"crop_ids",b"crop_ids",u"crop_point_classes",b"crop_point_classes",u"discard_points_border_px",b"discard_points_border_px",u"experiment_url",b"experiment_url",u"input_dtype",b"input_dtype",u"input_size",b"input_size",u"line_classes",b"line_classes",u"max_batch_size",b"max_batch_size",u"means",b"means",u"model_class",b"model_class",u"model_type",b"model_type",u"not_interleaved",b"not_interleaved",u"plant_enabled",b"plant_enabled",u"ppi",b"ppi",u"scaler_shifter_parameters",b"scaler_shifter_parameters",u"segm_classes",b"segm_classes",u"stds",b"stds",u"supported_classes",b"supported_classes",u"supports_depth",b"supports_depth",u"supports_half",b"supports_half",u"tile",b"tile",u"trained_embeddings",b"trained_embeddings",u"use_cases",b"use_cases",u"version",b"version",u"weed_point_classes",b"weed_point_classes"]) -> None: ...
type___ModelMetadataProto = ModelMetadataProto

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: deeplearning/model_io/proto/trt_model.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.deeplearning.model_io.proto import metadata_pb2 as deeplearning_dot_model__io_dot_proto_dot_metadata__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='deeplearning/model_io/proto/trt_model.proto',
  package='',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n+deeplearning/model_io/proto/trt_model.proto\x1a*deeplearning/model_io/proto/metadata.proto\"\xb3\x01\n\rTRTModelProto\x12\x14\n\x0c\x65ngine_bytes\x18\x01 \x01(\x0c\x12%\n\x08metadata\x18\x02 \x01(\x0b\x32\x13.ModelMetadataProto\x12\x13\n\x0binput_names\x18\x03 \x03(\t\x12\x14\n\x0coutput_names\x18\x04 \x03(\t\x12 \n\x18implicit_batch_dimension\x18\x05 \x01(\x08\x12\x18\n\x10tensorrt_version\x18\x06 \x01(\tb\x06proto3'
  ,
  dependencies=[deeplearning_dot_model__io_dot_proto_dot_metadata__pb2.DESCRIPTOR,])




_TRTMODELPROTO = _descriptor.Descriptor(
  name='TRTModelProto',
  full_name='TRTModelProto',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='engine_bytes', full_name='TRTModelProto.engine_bytes', index=0,
      number=1, type=12, cpp_type=9, label=1,
      has_default_value=False, default_value=b"",
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='metadata', full_name='TRTModelProto.metadata', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='input_names', full_name='TRTModelProto.input_names', index=2,
      number=3, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='output_names', full_name='TRTModelProto.output_names', index=3,
      number=4, type=9, cpp_type=9, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='implicit_batch_dimension', full_name='TRTModelProto.implicit_batch_dimension', index=4,
      number=5, type=8, cpp_type=7, label=1,
      has_default_value=False, default_value=False,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='tensorrt_version', full_name='TRTModelProto.tensorrt_version', index=5,
      number=6, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=92,
  serialized_end=271,
)

_TRTMODELPROTO.fields_by_name['metadata'].message_type = deeplearning_dot_model__io_dot_proto_dot_metadata__pb2._MODELMETADATAPROTO
DESCRIPTOR.message_types_by_name['TRTModelProto'] = _TRTMODELPROTO
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

TRTModelProto = _reflection.GeneratedProtocolMessageType('TRTModelProto', (_message.Message,), {
  'DESCRIPTOR' : _TRTMODELPROTO,
  '__module__' : 'deeplearning.model_io.proto.trt_model_pb2'
  # @@protoc_insertion_point(class_scope:TRTModelProto)
  })
_sym_db.RegisterMessage(TRTModelProto)


# @@protoc_insertion_point(module_scope)

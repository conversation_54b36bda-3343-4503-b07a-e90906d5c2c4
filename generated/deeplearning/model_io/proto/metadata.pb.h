// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/model_io/proto/metadata.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[3]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto;
class ModelMetadataProto;
struct ModelMetadataProtoDefaultTypeInternal;
extern ModelMetadataProtoDefaultTypeInternal _ModelMetadataProto_default_instance_;
class ScalerShifterParameterProto;
struct ScalerShifterParameterProtoDefaultTypeInternal;
extern ScalerShifterParameterProtoDefaultTypeInternal _ScalerShifterParameterProto_default_instance_;
class SizeProto;
struct SizeProtoDefaultTypeInternal;
extern SizeProtoDefaultTypeInternal _SizeProto_default_instance_;
PROTOBUF_NAMESPACE_OPEN
template<> ::ModelMetadataProto* Arena::CreateMaybeMessage<::ModelMetadataProto>(Arena*);
template<> ::ScalerShifterParameterProto* Arena::CreateMaybeMessage<::ScalerShifterParameterProto>(Arena*);
template<> ::SizeProto* Arena::CreateMaybeMessage<::SizeProto>(Arena*);
PROTOBUF_NAMESPACE_CLOSE

// ===================================================================

class SizeProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:SizeProto) */ {
 public:
  inline SizeProto() : SizeProto(nullptr) {}
  ~SizeProto() override;
  explicit constexpr SizeProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  SizeProto(const SizeProto& from);
  SizeProto(SizeProto&& from) noexcept
    : SizeProto() {
    *this = ::std::move(from);
  }

  inline SizeProto& operator=(const SizeProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline SizeProto& operator=(SizeProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const SizeProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const SizeProto* internal_default_instance() {
    return reinterpret_cast<const SizeProto*>(
               &_SizeProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(SizeProto& a, SizeProto& b) {
    a.Swap(&b);
  }
  inline void Swap(SizeProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(SizeProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  SizeProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<SizeProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const SizeProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const SizeProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(SizeProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "SizeProto";
  }
  protected:
  explicit SizeProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kWidthFieldNumber = 1,
    kHeightFieldNumber = 2,
  };
  // int32 width = 1;
  void clear_width();
  int32_t width() const;
  void set_width(int32_t value);
  private:
  int32_t _internal_width() const;
  void _internal_set_width(int32_t value);
  public:

  // int32 height = 2;
  void clear_height();
  int32_t height() const;
  void set_height(int32_t value);
  private:
  int32_t _internal_height() const;
  void _internal_set_height(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:SizeProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  int32_t width_;
  int32_t height_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto;
};
// -------------------------------------------------------------------

class ScalerShifterParameterProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ScalerShifterParameterProto) */ {
 public:
  inline ScalerShifterParameterProto() : ScalerShifterParameterProto(nullptr) {}
  ~ScalerShifterParameterProto() override;
  explicit constexpr ScalerShifterParameterProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ScalerShifterParameterProto(const ScalerShifterParameterProto& from);
  ScalerShifterParameterProto(ScalerShifterParameterProto&& from) noexcept
    : ScalerShifterParameterProto() {
    *this = ::std::move(from);
  }

  inline ScalerShifterParameterProto& operator=(const ScalerShifterParameterProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ScalerShifterParameterProto& operator=(ScalerShifterParameterProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ScalerShifterParameterProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ScalerShifterParameterProto* internal_default_instance() {
    return reinterpret_cast<const ScalerShifterParameterProto*>(
               &_ScalerShifterParameterProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    1;

  friend void swap(ScalerShifterParameterProto& a, ScalerShifterParameterProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ScalerShifterParameterProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ScalerShifterParameterProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ScalerShifterParameterProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ScalerShifterParameterProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ScalerShifterParameterProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ScalerShifterParameterProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ScalerShifterParameterProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ScalerShifterParameterProto";
  }
  protected:
  explicit ScalerShifterParameterProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMinRealFieldNumber = 1,
    kMaxRealFieldNumber = 2,
    kMinScaleFieldNumber = 3,
    kMaxScaleFieldNumber = 4,
    kShiftRealFieldNumber = 5,
    kShiftIndFieldNumber = 6,
  };
  // float min_real = 1;
  void clear_min_real();
  float min_real() const;
  void set_min_real(float value);
  private:
  float _internal_min_real() const;
  void _internal_set_min_real(float value);
  public:

  // float max_real = 2;
  void clear_max_real();
  float max_real() const;
  void set_max_real(float value);
  private:
  float _internal_max_real() const;
  void _internal_set_max_real(float value);
  public:

  // float min_scale = 3;
  void clear_min_scale();
  float min_scale() const;
  void set_min_scale(float value);
  private:
  float _internal_min_scale() const;
  void _internal_set_min_scale(float value);
  public:

  // float max_scale = 4;
  void clear_max_scale();
  float max_scale() const;
  void set_max_scale(float value);
  private:
  float _internal_max_scale() const;
  void _internal_set_max_scale(float value);
  public:

  // float shift_real = 5;
  void clear_shift_real();
  float shift_real() const;
  void set_shift_real(float value);
  private:
  float _internal_shift_real() const;
  void _internal_set_shift_real(float value);
  public:

  // int32 shift_ind = 6;
  void clear_shift_ind();
  int32_t shift_ind() const;
  void set_shift_ind(int32_t value);
  private:
  int32_t _internal_shift_ind() const;
  void _internal_set_shift_ind(int32_t value);
  public:

  // @@protoc_insertion_point(class_scope:ScalerShifterParameterProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  float min_real_;
  float max_real_;
  float min_scale_;
  float max_scale_;
  float shift_real_;
  int32_t shift_ind_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto;
};
// -------------------------------------------------------------------

class ModelMetadataProto final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:ModelMetadataProto) */ {
 public:
  inline ModelMetadataProto() : ModelMetadataProto(nullptr) {}
  ~ModelMetadataProto() override;
  explicit constexpr ModelMetadataProto(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  ModelMetadataProto(const ModelMetadataProto& from);
  ModelMetadataProto(ModelMetadataProto&& from) noexcept
    : ModelMetadataProto() {
    *this = ::std::move(from);
  }

  inline ModelMetadataProto& operator=(const ModelMetadataProto& from) {
    CopyFrom(from);
    return *this;
  }
  inline ModelMetadataProto& operator=(ModelMetadataProto&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const ModelMetadataProto& default_instance() {
    return *internal_default_instance();
  }
  static inline const ModelMetadataProto* internal_default_instance() {
    return reinterpret_cast<const ModelMetadataProto*>(
               &_ModelMetadataProto_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(ModelMetadataProto& a, ModelMetadataProto& b) {
    a.Swap(&b);
  }
  inline void Swap(ModelMetadataProto* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(ModelMetadataProto* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  ModelMetadataProto* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<ModelMetadataProto>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const ModelMetadataProto& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const ModelMetadataProto& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(ModelMetadataProto* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "ModelMetadataProto";
  }
  protected:
  explicit ModelMetadataProto(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kMeansFieldNumber = 3,
    kStdsFieldNumber = 4,
    kSupportedClassesFieldNumber = 6,
    kUseCasesFieldNumber = 7,
    kAuxInputSizesFieldNumber = 13,
    kWeedPointClassesFieldNumber = 18,
    kCropPointClassesFieldNumber = 19,
    kSegmClassesFieldNumber = 20,
    kLineClassesFieldNumber = 21,
    kCropIdsFieldNumber = 28,
    kInputDtypeFieldNumber = 1,
    kExperimentUrlFieldNumber = 5,
    kModelTypeFieldNumber = 15,
    kModelClassFieldNumber = 22,
    kBackboneArchitectureFieldNumber = 26,
    kInputSizeFieldNumber = 2,
    kTileFieldNumber = 10,
    kScalerShifterParametersFieldNumber = 27,
    kPpiFieldNumber = 9,
    kMaxBatchSizeFieldNumber = 12,
    kVersionFieldNumber = 14,
    kSupportsDepthFieldNumber = 8,
    kSupportsHalfFieldNumber = 11,
    kNotInterleavedFieldNumber = 16,
    kPlantEnabledFieldNumber = 23,
    kDiscardPointsBorderPxFieldNumber = 17,
    kTrainedEmbeddingsFieldNumber = 24,
    kContainsPumapHeadFieldNumber = 25,
    kCropEmbeddingsFieldNumber = 29,
  };
  // repeated float means = 3;
  int means_size() const;
  private:
  int _internal_means_size() const;
  public:
  void clear_means();
  private:
  float _internal_means(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_means() const;
  void _internal_add_means(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_means();
  public:
  float means(int index) const;
  void set_means(int index, float value);
  void add_means(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      means() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_means();

  // repeated float stds = 4;
  int stds_size() const;
  private:
  int _internal_stds_size() const;
  public:
  void clear_stds();
  private:
  float _internal_stds(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      _internal_stds() const;
  void _internal_add_stds(float value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      _internal_mutable_stds();
  public:
  float stds(int index) const;
  void set_stds(int index, float value);
  void add_stds(float value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
      stds() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
      mutable_stds();

  // repeated string supported_classes = 6;
  int supported_classes_size() const;
  private:
  int _internal_supported_classes_size() const;
  public:
  void clear_supported_classes();
  const std::string& supported_classes(int index) const;
  std::string* mutable_supported_classes(int index);
  void set_supported_classes(int index, const std::string& value);
  void set_supported_classes(int index, std::string&& value);
  void set_supported_classes(int index, const char* value);
  void set_supported_classes(int index, const char* value, size_t size);
  std::string* add_supported_classes();
  void add_supported_classes(const std::string& value);
  void add_supported_classes(std::string&& value);
  void add_supported_classes(const char* value);
  void add_supported_classes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& supported_classes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_supported_classes();
  private:
  const std::string& _internal_supported_classes(int index) const;
  std::string* _internal_add_supported_classes();
  public:

  // repeated string use_cases = 7;
  int use_cases_size() const;
  private:
  int _internal_use_cases_size() const;
  public:
  void clear_use_cases();
  const std::string& use_cases(int index) const;
  std::string* mutable_use_cases(int index);
  void set_use_cases(int index, const std::string& value);
  void set_use_cases(int index, std::string&& value);
  void set_use_cases(int index, const char* value);
  void set_use_cases(int index, const char* value, size_t size);
  std::string* add_use_cases();
  void add_use_cases(const std::string& value);
  void add_use_cases(std::string&& value);
  void add_use_cases(const char* value);
  void add_use_cases(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& use_cases() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_use_cases();
  private:
  const std::string& _internal_use_cases(int index) const;
  std::string* _internal_add_use_cases();
  public:

  // repeated .SizeProto aux_input_sizes = 13;
  int aux_input_sizes_size() const;
  private:
  int _internal_aux_input_sizes_size() const;
  public:
  void clear_aux_input_sizes();
  ::SizeProto* mutable_aux_input_sizes(int index);
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SizeProto >*
      mutable_aux_input_sizes();
  private:
  const ::SizeProto& _internal_aux_input_sizes(int index) const;
  ::SizeProto* _internal_add_aux_input_sizes();
  public:
  const ::SizeProto& aux_input_sizes(int index) const;
  ::SizeProto* add_aux_input_sizes();
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SizeProto >&
      aux_input_sizes() const;

  // repeated string weed_point_classes = 18;
  int weed_point_classes_size() const;
  private:
  int _internal_weed_point_classes_size() const;
  public:
  void clear_weed_point_classes();
  const std::string& weed_point_classes(int index) const;
  std::string* mutable_weed_point_classes(int index);
  void set_weed_point_classes(int index, const std::string& value);
  void set_weed_point_classes(int index, std::string&& value);
  void set_weed_point_classes(int index, const char* value);
  void set_weed_point_classes(int index, const char* value, size_t size);
  std::string* add_weed_point_classes();
  void add_weed_point_classes(const std::string& value);
  void add_weed_point_classes(std::string&& value);
  void add_weed_point_classes(const char* value);
  void add_weed_point_classes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& weed_point_classes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_weed_point_classes();
  private:
  const std::string& _internal_weed_point_classes(int index) const;
  std::string* _internal_add_weed_point_classes();
  public:

  // repeated string crop_point_classes = 19;
  int crop_point_classes_size() const;
  private:
  int _internal_crop_point_classes_size() const;
  public:
  void clear_crop_point_classes();
  const std::string& crop_point_classes(int index) const;
  std::string* mutable_crop_point_classes(int index);
  void set_crop_point_classes(int index, const std::string& value);
  void set_crop_point_classes(int index, std::string&& value);
  void set_crop_point_classes(int index, const char* value);
  void set_crop_point_classes(int index, const char* value, size_t size);
  std::string* add_crop_point_classes();
  void add_crop_point_classes(const std::string& value);
  void add_crop_point_classes(std::string&& value);
  void add_crop_point_classes(const char* value);
  void add_crop_point_classes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& crop_point_classes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_crop_point_classes();
  private:
  const std::string& _internal_crop_point_classes(int index) const;
  std::string* _internal_add_crop_point_classes();
  public:

  // repeated string segm_classes = 20;
  int segm_classes_size() const;
  private:
  int _internal_segm_classes_size() const;
  public:
  void clear_segm_classes();
  const std::string& segm_classes(int index) const;
  std::string* mutable_segm_classes(int index);
  void set_segm_classes(int index, const std::string& value);
  void set_segm_classes(int index, std::string&& value);
  void set_segm_classes(int index, const char* value);
  void set_segm_classes(int index, const char* value, size_t size);
  std::string* add_segm_classes();
  void add_segm_classes(const std::string& value);
  void add_segm_classes(std::string&& value);
  void add_segm_classes(const char* value);
  void add_segm_classes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& segm_classes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_segm_classes();
  private:
  const std::string& _internal_segm_classes(int index) const;
  std::string* _internal_add_segm_classes();
  public:

  // repeated string line_classes = 21;
  int line_classes_size() const;
  private:
  int _internal_line_classes_size() const;
  public:
  void clear_line_classes();
  const std::string& line_classes(int index) const;
  std::string* mutable_line_classes(int index);
  void set_line_classes(int index, const std::string& value);
  void set_line_classes(int index, std::string&& value);
  void set_line_classes(int index, const char* value);
  void set_line_classes(int index, const char* value, size_t size);
  std::string* add_line_classes();
  void add_line_classes(const std::string& value);
  void add_line_classes(std::string&& value);
  void add_line_classes(const char* value);
  void add_line_classes(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& line_classes() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_line_classes();
  private:
  const std::string& _internal_line_classes(int index) const;
  std::string* _internal_add_line_classes();
  public:

  // repeated string crop_ids = 28;
  int crop_ids_size() const;
  private:
  int _internal_crop_ids_size() const;
  public:
  void clear_crop_ids();
  const std::string& crop_ids(int index) const;
  std::string* mutable_crop_ids(int index);
  void set_crop_ids(int index, const std::string& value);
  void set_crop_ids(int index, std::string&& value);
  void set_crop_ids(int index, const char* value);
  void set_crop_ids(int index, const char* value, size_t size);
  std::string* add_crop_ids();
  void add_crop_ids(const std::string& value);
  void add_crop_ids(std::string&& value);
  void add_crop_ids(const char* value);
  void add_crop_ids(const char* value, size_t size);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>& crop_ids() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>* mutable_crop_ids();
  private:
  const std::string& _internal_crop_ids(int index) const;
  std::string* _internal_add_crop_ids();
  public:

  // string input_dtype = 1;
  void clear_input_dtype();
  const std::string& input_dtype() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_input_dtype(ArgT0&& arg0, ArgT... args);
  std::string* mutable_input_dtype();
  PROTOBUF_NODISCARD std::string* release_input_dtype();
  void set_allocated_input_dtype(std::string* input_dtype);
  private:
  const std::string& _internal_input_dtype() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_input_dtype(const std::string& value);
  std::string* _internal_mutable_input_dtype();
  public:

  // string experiment_url = 5;
  void clear_experiment_url();
  const std::string& experiment_url() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_experiment_url(ArgT0&& arg0, ArgT... args);
  std::string* mutable_experiment_url();
  PROTOBUF_NODISCARD std::string* release_experiment_url();
  void set_allocated_experiment_url(std::string* experiment_url);
  private:
  const std::string& _internal_experiment_url() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_experiment_url(const std::string& value);
  std::string* _internal_mutable_experiment_url();
  public:

  // string model_type = 15;
  void clear_model_type();
  const std::string& model_type() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_type(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_type();
  PROTOBUF_NODISCARD std::string* release_model_type();
  void set_allocated_model_type(std::string* model_type);
  private:
  const std::string& _internal_model_type() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_type(const std::string& value);
  std::string* _internal_mutable_model_type();
  public:

  // string model_class = 22;
  void clear_model_class();
  const std::string& model_class() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_model_class(ArgT0&& arg0, ArgT... args);
  std::string* mutable_model_class();
  PROTOBUF_NODISCARD std::string* release_model_class();
  void set_allocated_model_class(std::string* model_class);
  private:
  const std::string& _internal_model_class() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_model_class(const std::string& value);
  std::string* _internal_mutable_model_class();
  public:

  // string backbone_architecture = 26;
  void clear_backbone_architecture();
  const std::string& backbone_architecture() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_backbone_architecture(ArgT0&& arg0, ArgT... args);
  std::string* mutable_backbone_architecture();
  PROTOBUF_NODISCARD std::string* release_backbone_architecture();
  void set_allocated_backbone_architecture(std::string* backbone_architecture);
  private:
  const std::string& _internal_backbone_architecture() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_backbone_architecture(const std::string& value);
  std::string* _internal_mutable_backbone_architecture();
  public:

  // .SizeProto input_size = 2;
  bool has_input_size() const;
  private:
  bool _internal_has_input_size() const;
  public:
  void clear_input_size();
  const ::SizeProto& input_size() const;
  PROTOBUF_NODISCARD ::SizeProto* release_input_size();
  ::SizeProto* mutable_input_size();
  void set_allocated_input_size(::SizeProto* input_size);
  private:
  const ::SizeProto& _internal_input_size() const;
  ::SizeProto* _internal_mutable_input_size();
  public:
  void unsafe_arena_set_allocated_input_size(
      ::SizeProto* input_size);
  ::SizeProto* unsafe_arena_release_input_size();

  // .SizeProto tile = 10;
  bool has_tile() const;
  private:
  bool _internal_has_tile() const;
  public:
  void clear_tile();
  const ::SizeProto& tile() const;
  PROTOBUF_NODISCARD ::SizeProto* release_tile();
  ::SizeProto* mutable_tile();
  void set_allocated_tile(::SizeProto* tile);
  private:
  const ::SizeProto& _internal_tile() const;
  ::SizeProto* _internal_mutable_tile();
  public:
  void unsafe_arena_set_allocated_tile(
      ::SizeProto* tile);
  ::SizeProto* unsafe_arena_release_tile();

  // .ScalerShifterParameterProto scaler_shifter_parameters = 27;
  bool has_scaler_shifter_parameters() const;
  private:
  bool _internal_has_scaler_shifter_parameters() const;
  public:
  void clear_scaler_shifter_parameters();
  const ::ScalerShifterParameterProto& scaler_shifter_parameters() const;
  PROTOBUF_NODISCARD ::ScalerShifterParameterProto* release_scaler_shifter_parameters();
  ::ScalerShifterParameterProto* mutable_scaler_shifter_parameters();
  void set_allocated_scaler_shifter_parameters(::ScalerShifterParameterProto* scaler_shifter_parameters);
  private:
  const ::ScalerShifterParameterProto& _internal_scaler_shifter_parameters() const;
  ::ScalerShifterParameterProto* _internal_mutable_scaler_shifter_parameters();
  public:
  void unsafe_arena_set_allocated_scaler_shifter_parameters(
      ::ScalerShifterParameterProto* scaler_shifter_parameters);
  ::ScalerShifterParameterProto* unsafe_arena_release_scaler_shifter_parameters();

  // float ppi = 9;
  void clear_ppi();
  float ppi() const;
  void set_ppi(float value);
  private:
  float _internal_ppi() const;
  void _internal_set_ppi(float value);
  public:

  // int32 max_batch_size = 12;
  void clear_max_batch_size();
  int32_t max_batch_size() const;
  void set_max_batch_size(int32_t value);
  private:
  int32_t _internal_max_batch_size() const;
  void _internal_set_max_batch_size(int32_t value);
  public:

  // int32 version = 14;
  void clear_version();
  int32_t version() const;
  void set_version(int32_t value);
  private:
  int32_t _internal_version() const;
  void _internal_set_version(int32_t value);
  public:

  // bool supports_depth = 8;
  void clear_supports_depth();
  bool supports_depth() const;
  void set_supports_depth(bool value);
  private:
  bool _internal_supports_depth() const;
  void _internal_set_supports_depth(bool value);
  public:

  // bool supports_half = 11;
  void clear_supports_half();
  bool supports_half() const;
  void set_supports_half(bool value);
  private:
  bool _internal_supports_half() const;
  void _internal_set_supports_half(bool value);
  public:

  // bool not_interleaved = 16;
  void clear_not_interleaved();
  bool not_interleaved() const;
  void set_not_interleaved(bool value);
  private:
  bool _internal_not_interleaved() const;
  void _internal_set_not_interleaved(bool value);
  public:

  // bool plant_enabled = 23;
  void clear_plant_enabled();
  bool plant_enabled() const;
  void set_plant_enabled(bool value);
  private:
  bool _internal_plant_enabled() const;
  void _internal_set_plant_enabled(bool value);
  public:

  // float discard_points_border_px = 17;
  void clear_discard_points_border_px();
  float discard_points_border_px() const;
  void set_discard_points_border_px(float value);
  private:
  float _internal_discard_points_border_px() const;
  void _internal_set_discard_points_border_px(float value);
  public:

  // bool trained_embeddings = 24;
  void clear_trained_embeddings();
  bool trained_embeddings() const;
  void set_trained_embeddings(bool value);
  private:
  bool _internal_trained_embeddings() const;
  void _internal_set_trained_embeddings(bool value);
  public:

  // bool contains_pumap_head = 25;
  void clear_contains_pumap_head();
  bool contains_pumap_head() const;
  void set_contains_pumap_head(bool value);
  private:
  bool _internal_contains_pumap_head() const;
  void _internal_set_contains_pumap_head(bool value);
  public:

  // bool crop_embeddings = 29;
  void clear_crop_embeddings();
  bool crop_embeddings() const;
  void set_crop_embeddings(bool value);
  private:
  bool _internal_crop_embeddings() const;
  void _internal_set_crop_embeddings(bool value);
  public:

  // @@protoc_insertion_point(class_scope:ModelMetadataProto)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > means_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< float > stds_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> supported_classes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> use_cases_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SizeProto > aux_input_sizes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> weed_point_classes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> crop_point_classes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> segm_classes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> line_classes_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string> crop_ids_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr input_dtype_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr experiment_url_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_type_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr model_class_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr backbone_architecture_;
  ::SizeProto* input_size_;
  ::SizeProto* tile_;
  ::ScalerShifterParameterProto* scaler_shifter_parameters_;
  float ppi_;
  int32_t max_batch_size_;
  int32_t version_;
  bool supports_depth_;
  bool supports_half_;
  bool not_interleaved_;
  bool plant_enabled_;
  float discard_points_border_px_;
  bool trained_embeddings_;
  bool contains_pumap_head_;
  bool crop_embeddings_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// SizeProto

// int32 width = 1;
inline void SizeProto::clear_width() {
  width_ = 0;
}
inline int32_t SizeProto::_internal_width() const {
  return width_;
}
inline int32_t SizeProto::width() const {
  // @@protoc_insertion_point(field_get:SizeProto.width)
  return _internal_width();
}
inline void SizeProto::_internal_set_width(int32_t value) {
  
  width_ = value;
}
inline void SizeProto::set_width(int32_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:SizeProto.width)
}

// int32 height = 2;
inline void SizeProto::clear_height() {
  height_ = 0;
}
inline int32_t SizeProto::_internal_height() const {
  return height_;
}
inline int32_t SizeProto::height() const {
  // @@protoc_insertion_point(field_get:SizeProto.height)
  return _internal_height();
}
inline void SizeProto::_internal_set_height(int32_t value) {
  
  height_ = value;
}
inline void SizeProto::set_height(int32_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:SizeProto.height)
}

// -------------------------------------------------------------------

// ScalerShifterParameterProto

// float min_real = 1;
inline void ScalerShifterParameterProto::clear_min_real() {
  min_real_ = 0;
}
inline float ScalerShifterParameterProto::_internal_min_real() const {
  return min_real_;
}
inline float ScalerShifterParameterProto::min_real() const {
  // @@protoc_insertion_point(field_get:ScalerShifterParameterProto.min_real)
  return _internal_min_real();
}
inline void ScalerShifterParameterProto::_internal_set_min_real(float value) {
  
  min_real_ = value;
}
inline void ScalerShifterParameterProto::set_min_real(float value) {
  _internal_set_min_real(value);
  // @@protoc_insertion_point(field_set:ScalerShifterParameterProto.min_real)
}

// float max_real = 2;
inline void ScalerShifterParameterProto::clear_max_real() {
  max_real_ = 0;
}
inline float ScalerShifterParameterProto::_internal_max_real() const {
  return max_real_;
}
inline float ScalerShifterParameterProto::max_real() const {
  // @@protoc_insertion_point(field_get:ScalerShifterParameterProto.max_real)
  return _internal_max_real();
}
inline void ScalerShifterParameterProto::_internal_set_max_real(float value) {
  
  max_real_ = value;
}
inline void ScalerShifterParameterProto::set_max_real(float value) {
  _internal_set_max_real(value);
  // @@protoc_insertion_point(field_set:ScalerShifterParameterProto.max_real)
}

// float min_scale = 3;
inline void ScalerShifterParameterProto::clear_min_scale() {
  min_scale_ = 0;
}
inline float ScalerShifterParameterProto::_internal_min_scale() const {
  return min_scale_;
}
inline float ScalerShifterParameterProto::min_scale() const {
  // @@protoc_insertion_point(field_get:ScalerShifterParameterProto.min_scale)
  return _internal_min_scale();
}
inline void ScalerShifterParameterProto::_internal_set_min_scale(float value) {
  
  min_scale_ = value;
}
inline void ScalerShifterParameterProto::set_min_scale(float value) {
  _internal_set_min_scale(value);
  // @@protoc_insertion_point(field_set:ScalerShifterParameterProto.min_scale)
}

// float max_scale = 4;
inline void ScalerShifterParameterProto::clear_max_scale() {
  max_scale_ = 0;
}
inline float ScalerShifterParameterProto::_internal_max_scale() const {
  return max_scale_;
}
inline float ScalerShifterParameterProto::max_scale() const {
  // @@protoc_insertion_point(field_get:ScalerShifterParameterProto.max_scale)
  return _internal_max_scale();
}
inline void ScalerShifterParameterProto::_internal_set_max_scale(float value) {
  
  max_scale_ = value;
}
inline void ScalerShifterParameterProto::set_max_scale(float value) {
  _internal_set_max_scale(value);
  // @@protoc_insertion_point(field_set:ScalerShifterParameterProto.max_scale)
}

// float shift_real = 5;
inline void ScalerShifterParameterProto::clear_shift_real() {
  shift_real_ = 0;
}
inline float ScalerShifterParameterProto::_internal_shift_real() const {
  return shift_real_;
}
inline float ScalerShifterParameterProto::shift_real() const {
  // @@protoc_insertion_point(field_get:ScalerShifterParameterProto.shift_real)
  return _internal_shift_real();
}
inline void ScalerShifterParameterProto::_internal_set_shift_real(float value) {
  
  shift_real_ = value;
}
inline void ScalerShifterParameterProto::set_shift_real(float value) {
  _internal_set_shift_real(value);
  // @@protoc_insertion_point(field_set:ScalerShifterParameterProto.shift_real)
}

// int32 shift_ind = 6;
inline void ScalerShifterParameterProto::clear_shift_ind() {
  shift_ind_ = 0;
}
inline int32_t ScalerShifterParameterProto::_internal_shift_ind() const {
  return shift_ind_;
}
inline int32_t ScalerShifterParameterProto::shift_ind() const {
  // @@protoc_insertion_point(field_get:ScalerShifterParameterProto.shift_ind)
  return _internal_shift_ind();
}
inline void ScalerShifterParameterProto::_internal_set_shift_ind(int32_t value) {
  
  shift_ind_ = value;
}
inline void ScalerShifterParameterProto::set_shift_ind(int32_t value) {
  _internal_set_shift_ind(value);
  // @@protoc_insertion_point(field_set:ScalerShifterParameterProto.shift_ind)
}

// -------------------------------------------------------------------

// ModelMetadataProto

// string input_dtype = 1;
inline void ModelMetadataProto::clear_input_dtype() {
  input_dtype_.ClearToEmpty();
}
inline const std::string& ModelMetadataProto::input_dtype() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.input_dtype)
  return _internal_input_dtype();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelMetadataProto::set_input_dtype(ArgT0&& arg0, ArgT... args) {
 
 input_dtype_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ModelMetadataProto.input_dtype)
}
inline std::string* ModelMetadataProto::mutable_input_dtype() {
  std::string* _s = _internal_mutable_input_dtype();
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.input_dtype)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_input_dtype() const {
  return input_dtype_.Get();
}
inline void ModelMetadataProto::_internal_set_input_dtype(const std::string& value) {
  
  input_dtype_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::_internal_mutable_input_dtype() {
  
  return input_dtype_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::release_input_dtype() {
  // @@protoc_insertion_point(field_release:ModelMetadataProto.input_dtype)
  return input_dtype_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelMetadataProto::set_allocated_input_dtype(std::string* input_dtype) {
  if (input_dtype != nullptr) {
    
  } else {
    
  }
  input_dtype_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), input_dtype,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (input_dtype_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    input_dtype_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:ModelMetadataProto.input_dtype)
}

// .SizeProto input_size = 2;
inline bool ModelMetadataProto::_internal_has_input_size() const {
  return this != internal_default_instance() && input_size_ != nullptr;
}
inline bool ModelMetadataProto::has_input_size() const {
  return _internal_has_input_size();
}
inline void ModelMetadataProto::clear_input_size() {
  if (GetArenaForAllocation() == nullptr && input_size_ != nullptr) {
    delete input_size_;
  }
  input_size_ = nullptr;
}
inline const ::SizeProto& ModelMetadataProto::_internal_input_size() const {
  const ::SizeProto* p = input_size_;
  return p != nullptr ? *p : reinterpret_cast<const ::SizeProto&>(
      ::_SizeProto_default_instance_);
}
inline const ::SizeProto& ModelMetadataProto::input_size() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.input_size)
  return _internal_input_size();
}
inline void ModelMetadataProto::unsafe_arena_set_allocated_input_size(
    ::SizeProto* input_size) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(input_size_);
  }
  input_size_ = input_size;
  if (input_size) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:ModelMetadataProto.input_size)
}
inline ::SizeProto* ModelMetadataProto::release_input_size() {
  
  ::SizeProto* temp = input_size_;
  input_size_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::SizeProto* ModelMetadataProto::unsafe_arena_release_input_size() {
  // @@protoc_insertion_point(field_release:ModelMetadataProto.input_size)
  
  ::SizeProto* temp = input_size_;
  input_size_ = nullptr;
  return temp;
}
inline ::SizeProto* ModelMetadataProto::_internal_mutable_input_size() {
  
  if (input_size_ == nullptr) {
    auto* p = CreateMaybeMessage<::SizeProto>(GetArenaForAllocation());
    input_size_ = p;
  }
  return input_size_;
}
inline ::SizeProto* ModelMetadataProto::mutable_input_size() {
  ::SizeProto* _msg = _internal_mutable_input_size();
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.input_size)
  return _msg;
}
inline void ModelMetadataProto::set_allocated_input_size(::SizeProto* input_size) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete input_size_;
  }
  if (input_size) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::SizeProto>::GetOwningArena(input_size);
    if (message_arena != submessage_arena) {
      input_size = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, input_size, submessage_arena);
    }
    
  } else {
    
  }
  input_size_ = input_size;
  // @@protoc_insertion_point(field_set_allocated:ModelMetadataProto.input_size)
}

// repeated float means = 3;
inline int ModelMetadataProto::_internal_means_size() const {
  return means_.size();
}
inline int ModelMetadataProto::means_size() const {
  return _internal_means_size();
}
inline void ModelMetadataProto::clear_means() {
  means_.Clear();
}
inline float ModelMetadataProto::_internal_means(int index) const {
  return means_.Get(index);
}
inline float ModelMetadataProto::means(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.means)
  return _internal_means(index);
}
inline void ModelMetadataProto::set_means(int index, float value) {
  means_.Set(index, value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.means)
}
inline void ModelMetadataProto::_internal_add_means(float value) {
  means_.Add(value);
}
inline void ModelMetadataProto::add_means(float value) {
  _internal_add_means(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.means)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
ModelMetadataProto::_internal_means() const {
  return means_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
ModelMetadataProto::means() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.means)
  return _internal_means();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
ModelMetadataProto::_internal_mutable_means() {
  return &means_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
ModelMetadataProto::mutable_means() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.means)
  return _internal_mutable_means();
}

// repeated float stds = 4;
inline int ModelMetadataProto::_internal_stds_size() const {
  return stds_.size();
}
inline int ModelMetadataProto::stds_size() const {
  return _internal_stds_size();
}
inline void ModelMetadataProto::clear_stds() {
  stds_.Clear();
}
inline float ModelMetadataProto::_internal_stds(int index) const {
  return stds_.Get(index);
}
inline float ModelMetadataProto::stds(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.stds)
  return _internal_stds(index);
}
inline void ModelMetadataProto::set_stds(int index, float value) {
  stds_.Set(index, value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.stds)
}
inline void ModelMetadataProto::_internal_add_stds(float value) {
  stds_.Add(value);
}
inline void ModelMetadataProto::add_stds(float value) {
  _internal_add_stds(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.stds)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
ModelMetadataProto::_internal_stds() const {
  return stds_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >&
ModelMetadataProto::stds() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.stds)
  return _internal_stds();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
ModelMetadataProto::_internal_mutable_stds() {
  return &stds_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< float >*
ModelMetadataProto::mutable_stds() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.stds)
  return _internal_mutable_stds();
}

// string experiment_url = 5;
inline void ModelMetadataProto::clear_experiment_url() {
  experiment_url_.ClearToEmpty();
}
inline const std::string& ModelMetadataProto::experiment_url() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.experiment_url)
  return _internal_experiment_url();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelMetadataProto::set_experiment_url(ArgT0&& arg0, ArgT... args) {
 
 experiment_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ModelMetadataProto.experiment_url)
}
inline std::string* ModelMetadataProto::mutable_experiment_url() {
  std::string* _s = _internal_mutable_experiment_url();
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.experiment_url)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_experiment_url() const {
  return experiment_url_.Get();
}
inline void ModelMetadataProto::_internal_set_experiment_url(const std::string& value) {
  
  experiment_url_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::_internal_mutable_experiment_url() {
  
  return experiment_url_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::release_experiment_url() {
  // @@protoc_insertion_point(field_release:ModelMetadataProto.experiment_url)
  return experiment_url_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelMetadataProto::set_allocated_experiment_url(std::string* experiment_url) {
  if (experiment_url != nullptr) {
    
  } else {
    
  }
  experiment_url_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), experiment_url,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (experiment_url_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    experiment_url_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:ModelMetadataProto.experiment_url)
}

// repeated string supported_classes = 6;
inline int ModelMetadataProto::_internal_supported_classes_size() const {
  return supported_classes_.size();
}
inline int ModelMetadataProto::supported_classes_size() const {
  return _internal_supported_classes_size();
}
inline void ModelMetadataProto::clear_supported_classes() {
  supported_classes_.Clear();
}
inline std::string* ModelMetadataProto::add_supported_classes() {
  std::string* _s = _internal_add_supported_classes();
  // @@protoc_insertion_point(field_add_mutable:ModelMetadataProto.supported_classes)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_supported_classes(int index) const {
  return supported_classes_.Get(index);
}
inline const std::string& ModelMetadataProto::supported_classes(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.supported_classes)
  return _internal_supported_classes(index);
}
inline std::string* ModelMetadataProto::mutable_supported_classes(int index) {
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.supported_classes)
  return supported_classes_.Mutable(index);
}
inline void ModelMetadataProto::set_supported_classes(int index, const std::string& value) {
  supported_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.supported_classes)
}
inline void ModelMetadataProto::set_supported_classes(int index, std::string&& value) {
  supported_classes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:ModelMetadataProto.supported_classes)
}
inline void ModelMetadataProto::set_supported_classes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  supported_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ModelMetadataProto.supported_classes)
}
inline void ModelMetadataProto::set_supported_classes(int index, const char* value, size_t size) {
  supported_classes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ModelMetadataProto.supported_classes)
}
inline std::string* ModelMetadataProto::_internal_add_supported_classes() {
  return supported_classes_.Add();
}
inline void ModelMetadataProto::add_supported_classes(const std::string& value) {
  supported_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.supported_classes)
}
inline void ModelMetadataProto::add_supported_classes(std::string&& value) {
  supported_classes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:ModelMetadataProto.supported_classes)
}
inline void ModelMetadataProto::add_supported_classes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  supported_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ModelMetadataProto.supported_classes)
}
inline void ModelMetadataProto::add_supported_classes(const char* value, size_t size) {
  supported_classes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ModelMetadataProto.supported_classes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelMetadataProto::supported_classes() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.supported_classes)
  return supported_classes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelMetadataProto::mutable_supported_classes() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.supported_classes)
  return &supported_classes_;
}

// repeated string use_cases = 7;
inline int ModelMetadataProto::_internal_use_cases_size() const {
  return use_cases_.size();
}
inline int ModelMetadataProto::use_cases_size() const {
  return _internal_use_cases_size();
}
inline void ModelMetadataProto::clear_use_cases() {
  use_cases_.Clear();
}
inline std::string* ModelMetadataProto::add_use_cases() {
  std::string* _s = _internal_add_use_cases();
  // @@protoc_insertion_point(field_add_mutable:ModelMetadataProto.use_cases)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_use_cases(int index) const {
  return use_cases_.Get(index);
}
inline const std::string& ModelMetadataProto::use_cases(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.use_cases)
  return _internal_use_cases(index);
}
inline std::string* ModelMetadataProto::mutable_use_cases(int index) {
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.use_cases)
  return use_cases_.Mutable(index);
}
inline void ModelMetadataProto::set_use_cases(int index, const std::string& value) {
  use_cases_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.use_cases)
}
inline void ModelMetadataProto::set_use_cases(int index, std::string&& value) {
  use_cases_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:ModelMetadataProto.use_cases)
}
inline void ModelMetadataProto::set_use_cases(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  use_cases_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ModelMetadataProto.use_cases)
}
inline void ModelMetadataProto::set_use_cases(int index, const char* value, size_t size) {
  use_cases_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ModelMetadataProto.use_cases)
}
inline std::string* ModelMetadataProto::_internal_add_use_cases() {
  return use_cases_.Add();
}
inline void ModelMetadataProto::add_use_cases(const std::string& value) {
  use_cases_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.use_cases)
}
inline void ModelMetadataProto::add_use_cases(std::string&& value) {
  use_cases_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:ModelMetadataProto.use_cases)
}
inline void ModelMetadataProto::add_use_cases(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  use_cases_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ModelMetadataProto.use_cases)
}
inline void ModelMetadataProto::add_use_cases(const char* value, size_t size) {
  use_cases_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ModelMetadataProto.use_cases)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelMetadataProto::use_cases() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.use_cases)
  return use_cases_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelMetadataProto::mutable_use_cases() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.use_cases)
  return &use_cases_;
}

// bool supports_depth = 8;
inline void ModelMetadataProto::clear_supports_depth() {
  supports_depth_ = false;
}
inline bool ModelMetadataProto::_internal_supports_depth() const {
  return supports_depth_;
}
inline bool ModelMetadataProto::supports_depth() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.supports_depth)
  return _internal_supports_depth();
}
inline void ModelMetadataProto::_internal_set_supports_depth(bool value) {
  
  supports_depth_ = value;
}
inline void ModelMetadataProto::set_supports_depth(bool value) {
  _internal_set_supports_depth(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.supports_depth)
}

// float ppi = 9;
inline void ModelMetadataProto::clear_ppi() {
  ppi_ = 0;
}
inline float ModelMetadataProto::_internal_ppi() const {
  return ppi_;
}
inline float ModelMetadataProto::ppi() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.ppi)
  return _internal_ppi();
}
inline void ModelMetadataProto::_internal_set_ppi(float value) {
  
  ppi_ = value;
}
inline void ModelMetadataProto::set_ppi(float value) {
  _internal_set_ppi(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.ppi)
}

// .SizeProto tile = 10;
inline bool ModelMetadataProto::_internal_has_tile() const {
  return this != internal_default_instance() && tile_ != nullptr;
}
inline bool ModelMetadataProto::has_tile() const {
  return _internal_has_tile();
}
inline void ModelMetadataProto::clear_tile() {
  if (GetArenaForAllocation() == nullptr && tile_ != nullptr) {
    delete tile_;
  }
  tile_ = nullptr;
}
inline const ::SizeProto& ModelMetadataProto::_internal_tile() const {
  const ::SizeProto* p = tile_;
  return p != nullptr ? *p : reinterpret_cast<const ::SizeProto&>(
      ::_SizeProto_default_instance_);
}
inline const ::SizeProto& ModelMetadataProto::tile() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.tile)
  return _internal_tile();
}
inline void ModelMetadataProto::unsafe_arena_set_allocated_tile(
    ::SizeProto* tile) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(tile_);
  }
  tile_ = tile;
  if (tile) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:ModelMetadataProto.tile)
}
inline ::SizeProto* ModelMetadataProto::release_tile() {
  
  ::SizeProto* temp = tile_;
  tile_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::SizeProto* ModelMetadataProto::unsafe_arena_release_tile() {
  // @@protoc_insertion_point(field_release:ModelMetadataProto.tile)
  
  ::SizeProto* temp = tile_;
  tile_ = nullptr;
  return temp;
}
inline ::SizeProto* ModelMetadataProto::_internal_mutable_tile() {
  
  if (tile_ == nullptr) {
    auto* p = CreateMaybeMessage<::SizeProto>(GetArenaForAllocation());
    tile_ = p;
  }
  return tile_;
}
inline ::SizeProto* ModelMetadataProto::mutable_tile() {
  ::SizeProto* _msg = _internal_mutable_tile();
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.tile)
  return _msg;
}
inline void ModelMetadataProto::set_allocated_tile(::SizeProto* tile) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete tile_;
  }
  if (tile) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::SizeProto>::GetOwningArena(tile);
    if (message_arena != submessage_arena) {
      tile = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, tile, submessage_arena);
    }
    
  } else {
    
  }
  tile_ = tile;
  // @@protoc_insertion_point(field_set_allocated:ModelMetadataProto.tile)
}

// bool supports_half = 11;
inline void ModelMetadataProto::clear_supports_half() {
  supports_half_ = false;
}
inline bool ModelMetadataProto::_internal_supports_half() const {
  return supports_half_;
}
inline bool ModelMetadataProto::supports_half() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.supports_half)
  return _internal_supports_half();
}
inline void ModelMetadataProto::_internal_set_supports_half(bool value) {
  
  supports_half_ = value;
}
inline void ModelMetadataProto::set_supports_half(bool value) {
  _internal_set_supports_half(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.supports_half)
}

// int32 max_batch_size = 12;
inline void ModelMetadataProto::clear_max_batch_size() {
  max_batch_size_ = 0;
}
inline int32_t ModelMetadataProto::_internal_max_batch_size() const {
  return max_batch_size_;
}
inline int32_t ModelMetadataProto::max_batch_size() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.max_batch_size)
  return _internal_max_batch_size();
}
inline void ModelMetadataProto::_internal_set_max_batch_size(int32_t value) {
  
  max_batch_size_ = value;
}
inline void ModelMetadataProto::set_max_batch_size(int32_t value) {
  _internal_set_max_batch_size(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.max_batch_size)
}

// repeated .SizeProto aux_input_sizes = 13;
inline int ModelMetadataProto::_internal_aux_input_sizes_size() const {
  return aux_input_sizes_.size();
}
inline int ModelMetadataProto::aux_input_sizes_size() const {
  return _internal_aux_input_sizes_size();
}
inline void ModelMetadataProto::clear_aux_input_sizes() {
  aux_input_sizes_.Clear();
}
inline ::SizeProto* ModelMetadataProto::mutable_aux_input_sizes(int index) {
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.aux_input_sizes)
  return aux_input_sizes_.Mutable(index);
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SizeProto >*
ModelMetadataProto::mutable_aux_input_sizes() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.aux_input_sizes)
  return &aux_input_sizes_;
}
inline const ::SizeProto& ModelMetadataProto::_internal_aux_input_sizes(int index) const {
  return aux_input_sizes_.Get(index);
}
inline const ::SizeProto& ModelMetadataProto::aux_input_sizes(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.aux_input_sizes)
  return _internal_aux_input_sizes(index);
}
inline ::SizeProto* ModelMetadataProto::_internal_add_aux_input_sizes() {
  return aux_input_sizes_.Add();
}
inline ::SizeProto* ModelMetadataProto::add_aux_input_sizes() {
  ::SizeProto* _add = _internal_add_aux_input_sizes();
  // @@protoc_insertion_point(field_add:ModelMetadataProto.aux_input_sizes)
  return _add;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField< ::SizeProto >&
ModelMetadataProto::aux_input_sizes() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.aux_input_sizes)
  return aux_input_sizes_;
}

// int32 version = 14;
inline void ModelMetadataProto::clear_version() {
  version_ = 0;
}
inline int32_t ModelMetadataProto::_internal_version() const {
  return version_;
}
inline int32_t ModelMetadataProto::version() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.version)
  return _internal_version();
}
inline void ModelMetadataProto::_internal_set_version(int32_t value) {
  
  version_ = value;
}
inline void ModelMetadataProto::set_version(int32_t value) {
  _internal_set_version(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.version)
}

// string model_type = 15;
inline void ModelMetadataProto::clear_model_type() {
  model_type_.ClearToEmpty();
}
inline const std::string& ModelMetadataProto::model_type() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.model_type)
  return _internal_model_type();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelMetadataProto::set_model_type(ArgT0&& arg0, ArgT... args) {
 
 model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ModelMetadataProto.model_type)
}
inline std::string* ModelMetadataProto::mutable_model_type() {
  std::string* _s = _internal_mutable_model_type();
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.model_type)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_model_type() const {
  return model_type_.Get();
}
inline void ModelMetadataProto::_internal_set_model_type(const std::string& value) {
  
  model_type_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::_internal_mutable_model_type() {
  
  return model_type_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::release_model_type() {
  // @@protoc_insertion_point(field_release:ModelMetadataProto.model_type)
  return model_type_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelMetadataProto::set_allocated_model_type(std::string* model_type) {
  if (model_type != nullptr) {
    
  } else {
    
  }
  model_type_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_type,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_type_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_type_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:ModelMetadataProto.model_type)
}

// bool not_interleaved = 16;
inline void ModelMetadataProto::clear_not_interleaved() {
  not_interleaved_ = false;
}
inline bool ModelMetadataProto::_internal_not_interleaved() const {
  return not_interleaved_;
}
inline bool ModelMetadataProto::not_interleaved() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.not_interleaved)
  return _internal_not_interleaved();
}
inline void ModelMetadataProto::_internal_set_not_interleaved(bool value) {
  
  not_interleaved_ = value;
}
inline void ModelMetadataProto::set_not_interleaved(bool value) {
  _internal_set_not_interleaved(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.not_interleaved)
}

// float discard_points_border_px = 17;
inline void ModelMetadataProto::clear_discard_points_border_px() {
  discard_points_border_px_ = 0;
}
inline float ModelMetadataProto::_internal_discard_points_border_px() const {
  return discard_points_border_px_;
}
inline float ModelMetadataProto::discard_points_border_px() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.discard_points_border_px)
  return _internal_discard_points_border_px();
}
inline void ModelMetadataProto::_internal_set_discard_points_border_px(float value) {
  
  discard_points_border_px_ = value;
}
inline void ModelMetadataProto::set_discard_points_border_px(float value) {
  _internal_set_discard_points_border_px(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.discard_points_border_px)
}

// repeated string weed_point_classes = 18;
inline int ModelMetadataProto::_internal_weed_point_classes_size() const {
  return weed_point_classes_.size();
}
inline int ModelMetadataProto::weed_point_classes_size() const {
  return _internal_weed_point_classes_size();
}
inline void ModelMetadataProto::clear_weed_point_classes() {
  weed_point_classes_.Clear();
}
inline std::string* ModelMetadataProto::add_weed_point_classes() {
  std::string* _s = _internal_add_weed_point_classes();
  // @@protoc_insertion_point(field_add_mutable:ModelMetadataProto.weed_point_classes)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_weed_point_classes(int index) const {
  return weed_point_classes_.Get(index);
}
inline const std::string& ModelMetadataProto::weed_point_classes(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.weed_point_classes)
  return _internal_weed_point_classes(index);
}
inline std::string* ModelMetadataProto::mutable_weed_point_classes(int index) {
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.weed_point_classes)
  return weed_point_classes_.Mutable(index);
}
inline void ModelMetadataProto::set_weed_point_classes(int index, const std::string& value) {
  weed_point_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.weed_point_classes)
}
inline void ModelMetadataProto::set_weed_point_classes(int index, std::string&& value) {
  weed_point_classes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:ModelMetadataProto.weed_point_classes)
}
inline void ModelMetadataProto::set_weed_point_classes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  weed_point_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ModelMetadataProto.weed_point_classes)
}
inline void ModelMetadataProto::set_weed_point_classes(int index, const char* value, size_t size) {
  weed_point_classes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ModelMetadataProto.weed_point_classes)
}
inline std::string* ModelMetadataProto::_internal_add_weed_point_classes() {
  return weed_point_classes_.Add();
}
inline void ModelMetadataProto::add_weed_point_classes(const std::string& value) {
  weed_point_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.weed_point_classes)
}
inline void ModelMetadataProto::add_weed_point_classes(std::string&& value) {
  weed_point_classes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:ModelMetadataProto.weed_point_classes)
}
inline void ModelMetadataProto::add_weed_point_classes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  weed_point_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ModelMetadataProto.weed_point_classes)
}
inline void ModelMetadataProto::add_weed_point_classes(const char* value, size_t size) {
  weed_point_classes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ModelMetadataProto.weed_point_classes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelMetadataProto::weed_point_classes() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.weed_point_classes)
  return weed_point_classes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelMetadataProto::mutable_weed_point_classes() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.weed_point_classes)
  return &weed_point_classes_;
}

// repeated string crop_point_classes = 19;
inline int ModelMetadataProto::_internal_crop_point_classes_size() const {
  return crop_point_classes_.size();
}
inline int ModelMetadataProto::crop_point_classes_size() const {
  return _internal_crop_point_classes_size();
}
inline void ModelMetadataProto::clear_crop_point_classes() {
  crop_point_classes_.Clear();
}
inline std::string* ModelMetadataProto::add_crop_point_classes() {
  std::string* _s = _internal_add_crop_point_classes();
  // @@protoc_insertion_point(field_add_mutable:ModelMetadataProto.crop_point_classes)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_crop_point_classes(int index) const {
  return crop_point_classes_.Get(index);
}
inline const std::string& ModelMetadataProto::crop_point_classes(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.crop_point_classes)
  return _internal_crop_point_classes(index);
}
inline std::string* ModelMetadataProto::mutable_crop_point_classes(int index) {
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.crop_point_classes)
  return crop_point_classes_.Mutable(index);
}
inline void ModelMetadataProto::set_crop_point_classes(int index, const std::string& value) {
  crop_point_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.crop_point_classes)
}
inline void ModelMetadataProto::set_crop_point_classes(int index, std::string&& value) {
  crop_point_classes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:ModelMetadataProto.crop_point_classes)
}
inline void ModelMetadataProto::set_crop_point_classes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  crop_point_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ModelMetadataProto.crop_point_classes)
}
inline void ModelMetadataProto::set_crop_point_classes(int index, const char* value, size_t size) {
  crop_point_classes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ModelMetadataProto.crop_point_classes)
}
inline std::string* ModelMetadataProto::_internal_add_crop_point_classes() {
  return crop_point_classes_.Add();
}
inline void ModelMetadataProto::add_crop_point_classes(const std::string& value) {
  crop_point_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.crop_point_classes)
}
inline void ModelMetadataProto::add_crop_point_classes(std::string&& value) {
  crop_point_classes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:ModelMetadataProto.crop_point_classes)
}
inline void ModelMetadataProto::add_crop_point_classes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  crop_point_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ModelMetadataProto.crop_point_classes)
}
inline void ModelMetadataProto::add_crop_point_classes(const char* value, size_t size) {
  crop_point_classes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ModelMetadataProto.crop_point_classes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelMetadataProto::crop_point_classes() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.crop_point_classes)
  return crop_point_classes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelMetadataProto::mutable_crop_point_classes() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.crop_point_classes)
  return &crop_point_classes_;
}

// repeated string segm_classes = 20;
inline int ModelMetadataProto::_internal_segm_classes_size() const {
  return segm_classes_.size();
}
inline int ModelMetadataProto::segm_classes_size() const {
  return _internal_segm_classes_size();
}
inline void ModelMetadataProto::clear_segm_classes() {
  segm_classes_.Clear();
}
inline std::string* ModelMetadataProto::add_segm_classes() {
  std::string* _s = _internal_add_segm_classes();
  // @@protoc_insertion_point(field_add_mutable:ModelMetadataProto.segm_classes)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_segm_classes(int index) const {
  return segm_classes_.Get(index);
}
inline const std::string& ModelMetadataProto::segm_classes(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.segm_classes)
  return _internal_segm_classes(index);
}
inline std::string* ModelMetadataProto::mutable_segm_classes(int index) {
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.segm_classes)
  return segm_classes_.Mutable(index);
}
inline void ModelMetadataProto::set_segm_classes(int index, const std::string& value) {
  segm_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.segm_classes)
}
inline void ModelMetadataProto::set_segm_classes(int index, std::string&& value) {
  segm_classes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:ModelMetadataProto.segm_classes)
}
inline void ModelMetadataProto::set_segm_classes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  segm_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ModelMetadataProto.segm_classes)
}
inline void ModelMetadataProto::set_segm_classes(int index, const char* value, size_t size) {
  segm_classes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ModelMetadataProto.segm_classes)
}
inline std::string* ModelMetadataProto::_internal_add_segm_classes() {
  return segm_classes_.Add();
}
inline void ModelMetadataProto::add_segm_classes(const std::string& value) {
  segm_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.segm_classes)
}
inline void ModelMetadataProto::add_segm_classes(std::string&& value) {
  segm_classes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:ModelMetadataProto.segm_classes)
}
inline void ModelMetadataProto::add_segm_classes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  segm_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ModelMetadataProto.segm_classes)
}
inline void ModelMetadataProto::add_segm_classes(const char* value, size_t size) {
  segm_classes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ModelMetadataProto.segm_classes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelMetadataProto::segm_classes() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.segm_classes)
  return segm_classes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelMetadataProto::mutable_segm_classes() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.segm_classes)
  return &segm_classes_;
}

// repeated string line_classes = 21;
inline int ModelMetadataProto::_internal_line_classes_size() const {
  return line_classes_.size();
}
inline int ModelMetadataProto::line_classes_size() const {
  return _internal_line_classes_size();
}
inline void ModelMetadataProto::clear_line_classes() {
  line_classes_.Clear();
}
inline std::string* ModelMetadataProto::add_line_classes() {
  std::string* _s = _internal_add_line_classes();
  // @@protoc_insertion_point(field_add_mutable:ModelMetadataProto.line_classes)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_line_classes(int index) const {
  return line_classes_.Get(index);
}
inline const std::string& ModelMetadataProto::line_classes(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.line_classes)
  return _internal_line_classes(index);
}
inline std::string* ModelMetadataProto::mutable_line_classes(int index) {
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.line_classes)
  return line_classes_.Mutable(index);
}
inline void ModelMetadataProto::set_line_classes(int index, const std::string& value) {
  line_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.line_classes)
}
inline void ModelMetadataProto::set_line_classes(int index, std::string&& value) {
  line_classes_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:ModelMetadataProto.line_classes)
}
inline void ModelMetadataProto::set_line_classes(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  line_classes_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ModelMetadataProto.line_classes)
}
inline void ModelMetadataProto::set_line_classes(int index, const char* value, size_t size) {
  line_classes_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ModelMetadataProto.line_classes)
}
inline std::string* ModelMetadataProto::_internal_add_line_classes() {
  return line_classes_.Add();
}
inline void ModelMetadataProto::add_line_classes(const std::string& value) {
  line_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.line_classes)
}
inline void ModelMetadataProto::add_line_classes(std::string&& value) {
  line_classes_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:ModelMetadataProto.line_classes)
}
inline void ModelMetadataProto::add_line_classes(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  line_classes_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ModelMetadataProto.line_classes)
}
inline void ModelMetadataProto::add_line_classes(const char* value, size_t size) {
  line_classes_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ModelMetadataProto.line_classes)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelMetadataProto::line_classes() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.line_classes)
  return line_classes_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelMetadataProto::mutable_line_classes() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.line_classes)
  return &line_classes_;
}

// string model_class = 22;
inline void ModelMetadataProto::clear_model_class() {
  model_class_.ClearToEmpty();
}
inline const std::string& ModelMetadataProto::model_class() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.model_class)
  return _internal_model_class();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelMetadataProto::set_model_class(ArgT0&& arg0, ArgT... args) {
 
 model_class_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ModelMetadataProto.model_class)
}
inline std::string* ModelMetadataProto::mutable_model_class() {
  std::string* _s = _internal_mutable_model_class();
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.model_class)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_model_class() const {
  return model_class_.Get();
}
inline void ModelMetadataProto::_internal_set_model_class(const std::string& value) {
  
  model_class_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::_internal_mutable_model_class() {
  
  return model_class_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::release_model_class() {
  // @@protoc_insertion_point(field_release:ModelMetadataProto.model_class)
  return model_class_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelMetadataProto::set_allocated_model_class(std::string* model_class) {
  if (model_class != nullptr) {
    
  } else {
    
  }
  model_class_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), model_class,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (model_class_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    model_class_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:ModelMetadataProto.model_class)
}

// bool plant_enabled = 23;
inline void ModelMetadataProto::clear_plant_enabled() {
  plant_enabled_ = false;
}
inline bool ModelMetadataProto::_internal_plant_enabled() const {
  return plant_enabled_;
}
inline bool ModelMetadataProto::plant_enabled() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.plant_enabled)
  return _internal_plant_enabled();
}
inline void ModelMetadataProto::_internal_set_plant_enabled(bool value) {
  
  plant_enabled_ = value;
}
inline void ModelMetadataProto::set_plant_enabled(bool value) {
  _internal_set_plant_enabled(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.plant_enabled)
}

// bool trained_embeddings = 24;
inline void ModelMetadataProto::clear_trained_embeddings() {
  trained_embeddings_ = false;
}
inline bool ModelMetadataProto::_internal_trained_embeddings() const {
  return trained_embeddings_;
}
inline bool ModelMetadataProto::trained_embeddings() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.trained_embeddings)
  return _internal_trained_embeddings();
}
inline void ModelMetadataProto::_internal_set_trained_embeddings(bool value) {
  
  trained_embeddings_ = value;
}
inline void ModelMetadataProto::set_trained_embeddings(bool value) {
  _internal_set_trained_embeddings(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.trained_embeddings)
}

// bool contains_pumap_head = 25;
inline void ModelMetadataProto::clear_contains_pumap_head() {
  contains_pumap_head_ = false;
}
inline bool ModelMetadataProto::_internal_contains_pumap_head() const {
  return contains_pumap_head_;
}
inline bool ModelMetadataProto::contains_pumap_head() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.contains_pumap_head)
  return _internal_contains_pumap_head();
}
inline void ModelMetadataProto::_internal_set_contains_pumap_head(bool value) {
  
  contains_pumap_head_ = value;
}
inline void ModelMetadataProto::set_contains_pumap_head(bool value) {
  _internal_set_contains_pumap_head(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.contains_pumap_head)
}

// string backbone_architecture = 26;
inline void ModelMetadataProto::clear_backbone_architecture() {
  backbone_architecture_.ClearToEmpty();
}
inline const std::string& ModelMetadataProto::backbone_architecture() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.backbone_architecture)
  return _internal_backbone_architecture();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void ModelMetadataProto::set_backbone_architecture(ArgT0&& arg0, ArgT... args) {
 
 backbone_architecture_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:ModelMetadataProto.backbone_architecture)
}
inline std::string* ModelMetadataProto::mutable_backbone_architecture() {
  std::string* _s = _internal_mutable_backbone_architecture();
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.backbone_architecture)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_backbone_architecture() const {
  return backbone_architecture_.Get();
}
inline void ModelMetadataProto::_internal_set_backbone_architecture(const std::string& value) {
  
  backbone_architecture_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::_internal_mutable_backbone_architecture() {
  
  return backbone_architecture_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* ModelMetadataProto::release_backbone_architecture() {
  // @@protoc_insertion_point(field_release:ModelMetadataProto.backbone_architecture)
  return backbone_architecture_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void ModelMetadataProto::set_allocated_backbone_architecture(std::string* backbone_architecture) {
  if (backbone_architecture != nullptr) {
    
  } else {
    
  }
  backbone_architecture_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), backbone_architecture,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (backbone_architecture_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    backbone_architecture_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:ModelMetadataProto.backbone_architecture)
}

// .ScalerShifterParameterProto scaler_shifter_parameters = 27;
inline bool ModelMetadataProto::_internal_has_scaler_shifter_parameters() const {
  return this != internal_default_instance() && scaler_shifter_parameters_ != nullptr;
}
inline bool ModelMetadataProto::has_scaler_shifter_parameters() const {
  return _internal_has_scaler_shifter_parameters();
}
inline void ModelMetadataProto::clear_scaler_shifter_parameters() {
  if (GetArenaForAllocation() == nullptr && scaler_shifter_parameters_ != nullptr) {
    delete scaler_shifter_parameters_;
  }
  scaler_shifter_parameters_ = nullptr;
}
inline const ::ScalerShifterParameterProto& ModelMetadataProto::_internal_scaler_shifter_parameters() const {
  const ::ScalerShifterParameterProto* p = scaler_shifter_parameters_;
  return p != nullptr ? *p : reinterpret_cast<const ::ScalerShifterParameterProto&>(
      ::_ScalerShifterParameterProto_default_instance_);
}
inline const ::ScalerShifterParameterProto& ModelMetadataProto::scaler_shifter_parameters() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.scaler_shifter_parameters)
  return _internal_scaler_shifter_parameters();
}
inline void ModelMetadataProto::unsafe_arena_set_allocated_scaler_shifter_parameters(
    ::ScalerShifterParameterProto* scaler_shifter_parameters) {
  if (GetArenaForAllocation() == nullptr) {
    delete reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(scaler_shifter_parameters_);
  }
  scaler_shifter_parameters_ = scaler_shifter_parameters;
  if (scaler_shifter_parameters) {
    
  } else {
    
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:ModelMetadataProto.scaler_shifter_parameters)
}
inline ::ScalerShifterParameterProto* ModelMetadataProto::release_scaler_shifter_parameters() {
  
  ::ScalerShifterParameterProto* temp = scaler_shifter_parameters_;
  scaler_shifter_parameters_ = nullptr;
#ifdef PROTOBUF_FORCE_COPY_IN_RELEASE
  auto* old =  reinterpret_cast<::PROTOBUF_NAMESPACE_ID::MessageLite*>(temp);
  temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  if (GetArenaForAllocation() == nullptr) { delete old; }
#else  // PROTOBUF_FORCE_COPY_IN_RELEASE
  if (GetArenaForAllocation() != nullptr) {
    temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
  }
#endif  // !PROTOBUF_FORCE_COPY_IN_RELEASE
  return temp;
}
inline ::ScalerShifterParameterProto* ModelMetadataProto::unsafe_arena_release_scaler_shifter_parameters() {
  // @@protoc_insertion_point(field_release:ModelMetadataProto.scaler_shifter_parameters)
  
  ::ScalerShifterParameterProto* temp = scaler_shifter_parameters_;
  scaler_shifter_parameters_ = nullptr;
  return temp;
}
inline ::ScalerShifterParameterProto* ModelMetadataProto::_internal_mutable_scaler_shifter_parameters() {
  
  if (scaler_shifter_parameters_ == nullptr) {
    auto* p = CreateMaybeMessage<::ScalerShifterParameterProto>(GetArenaForAllocation());
    scaler_shifter_parameters_ = p;
  }
  return scaler_shifter_parameters_;
}
inline ::ScalerShifterParameterProto* ModelMetadataProto::mutable_scaler_shifter_parameters() {
  ::ScalerShifterParameterProto* _msg = _internal_mutable_scaler_shifter_parameters();
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.scaler_shifter_parameters)
  return _msg;
}
inline void ModelMetadataProto::set_allocated_scaler_shifter_parameters(::ScalerShifterParameterProto* scaler_shifter_parameters) {
  ::PROTOBUF_NAMESPACE_ID::Arena* message_arena = GetArenaForAllocation();
  if (message_arena == nullptr) {
    delete scaler_shifter_parameters_;
  }
  if (scaler_shifter_parameters) {
    ::PROTOBUF_NAMESPACE_ID::Arena* submessage_arena =
        ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper<::ScalerShifterParameterProto>::GetOwningArena(scaler_shifter_parameters);
    if (message_arena != submessage_arena) {
      scaler_shifter_parameters = ::PROTOBUF_NAMESPACE_ID::internal::GetOwnedMessage(
          message_arena, scaler_shifter_parameters, submessage_arena);
    }
    
  } else {
    
  }
  scaler_shifter_parameters_ = scaler_shifter_parameters;
  // @@protoc_insertion_point(field_set_allocated:ModelMetadataProto.scaler_shifter_parameters)
}

// repeated string crop_ids = 28;
inline int ModelMetadataProto::_internal_crop_ids_size() const {
  return crop_ids_.size();
}
inline int ModelMetadataProto::crop_ids_size() const {
  return _internal_crop_ids_size();
}
inline void ModelMetadataProto::clear_crop_ids() {
  crop_ids_.Clear();
}
inline std::string* ModelMetadataProto::add_crop_ids() {
  std::string* _s = _internal_add_crop_ids();
  // @@protoc_insertion_point(field_add_mutable:ModelMetadataProto.crop_ids)
  return _s;
}
inline const std::string& ModelMetadataProto::_internal_crop_ids(int index) const {
  return crop_ids_.Get(index);
}
inline const std::string& ModelMetadataProto::crop_ids(int index) const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.crop_ids)
  return _internal_crop_ids(index);
}
inline std::string* ModelMetadataProto::mutable_crop_ids(int index) {
  // @@protoc_insertion_point(field_mutable:ModelMetadataProto.crop_ids)
  return crop_ids_.Mutable(index);
}
inline void ModelMetadataProto::set_crop_ids(int index, const std::string& value) {
  crop_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.crop_ids)
}
inline void ModelMetadataProto::set_crop_ids(int index, std::string&& value) {
  crop_ids_.Mutable(index)->assign(std::move(value));
  // @@protoc_insertion_point(field_set:ModelMetadataProto.crop_ids)
}
inline void ModelMetadataProto::set_crop_ids(int index, const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  crop_ids_.Mutable(index)->assign(value);
  // @@protoc_insertion_point(field_set_char:ModelMetadataProto.crop_ids)
}
inline void ModelMetadataProto::set_crop_ids(int index, const char* value, size_t size) {
  crop_ids_.Mutable(index)->assign(
    reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_set_pointer:ModelMetadataProto.crop_ids)
}
inline std::string* ModelMetadataProto::_internal_add_crop_ids() {
  return crop_ids_.Add();
}
inline void ModelMetadataProto::add_crop_ids(const std::string& value) {
  crop_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add:ModelMetadataProto.crop_ids)
}
inline void ModelMetadataProto::add_crop_ids(std::string&& value) {
  crop_ids_.Add(std::move(value));
  // @@protoc_insertion_point(field_add:ModelMetadataProto.crop_ids)
}
inline void ModelMetadataProto::add_crop_ids(const char* value) {
  GOOGLE_DCHECK(value != nullptr);
  crop_ids_.Add()->assign(value);
  // @@protoc_insertion_point(field_add_char:ModelMetadataProto.crop_ids)
}
inline void ModelMetadataProto::add_crop_ids(const char* value, size_t size) {
  crop_ids_.Add()->assign(reinterpret_cast<const char*>(value), size);
  // @@protoc_insertion_point(field_add_pointer:ModelMetadataProto.crop_ids)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>&
ModelMetadataProto::crop_ids() const {
  // @@protoc_insertion_point(field_list:ModelMetadataProto.crop_ids)
  return crop_ids_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedPtrField<std::string>*
ModelMetadataProto::mutable_crop_ids() {
  // @@protoc_insertion_point(field_mutable_list:ModelMetadataProto.crop_ids)
  return &crop_ids_;
}

// bool crop_embeddings = 29;
inline void ModelMetadataProto::clear_crop_embeddings() {
  crop_embeddings_ = false;
}
inline bool ModelMetadataProto::_internal_crop_embeddings() const {
  return crop_embeddings_;
}
inline bool ModelMetadataProto::crop_embeddings() const {
  // @@protoc_insertion_point(field_get:ModelMetadataProto.crop_embeddings)
  return _internal_crop_embeddings();
}
inline void ModelMetadataProto::_internal_set_crop_embeddings(bool value) {
  
  crop_embeddings_ = value;
}
inline void ModelMetadataProto::set_crop_embeddings(bool value) {
  _internal_set_crop_embeddings(value);
  // @@protoc_insertion_point(field_set:ModelMetadataProto.crop_embeddings)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)


// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_deeplearning_2fmodel_5fio_2fproto_2fmetadata_2eproto

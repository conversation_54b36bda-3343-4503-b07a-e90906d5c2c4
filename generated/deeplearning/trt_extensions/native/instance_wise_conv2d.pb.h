// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/trt_extensions/native/instance_wise_conv2d.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2finstance_5fwise_5fconv2d_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2finstance_5fwise_5fconv2d_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "deeplearning/trt_extensions/native/common.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_deeplearning_2ftrt_5fextensions_2fnative_2finstance_5fwise_5fconv2d_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2finstance_5fwise_5fconv2d_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2finstance_5fwise_5fconv2d_2eproto;
namespace trt_extensions {
class InstanceWiseConv2DMessage;
struct InstanceWiseConv2DMessageDefaultTypeInternal;
extern InstanceWiseConv2DMessageDefaultTypeInternal _InstanceWiseConv2DMessage_default_instance_;
}  // namespace trt_extensions
PROTOBUF_NAMESPACE_OPEN
template<> ::trt_extensions::InstanceWiseConv2DMessage* Arena::CreateMaybeMessage<::trt_extensions::InstanceWiseConv2DMessage>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace trt_extensions {

// ===================================================================

class InstanceWiseConv2DMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:trt_extensions.InstanceWiseConv2DMessage) */ {
 public:
  inline InstanceWiseConv2DMessage() : InstanceWiseConv2DMessage(nullptr) {}
  ~InstanceWiseConv2DMessage() override;
  explicit constexpr InstanceWiseConv2DMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  InstanceWiseConv2DMessage(const InstanceWiseConv2DMessage& from);
  InstanceWiseConv2DMessage(InstanceWiseConv2DMessage&& from) noexcept
    : InstanceWiseConv2DMessage() {
    *this = ::std::move(from);
  }

  inline InstanceWiseConv2DMessage& operator=(const InstanceWiseConv2DMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline InstanceWiseConv2DMessage& operator=(InstanceWiseConv2DMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const InstanceWiseConv2DMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const InstanceWiseConv2DMessage* internal_default_instance() {
    return reinterpret_cast<const InstanceWiseConv2DMessage*>(
               &_InstanceWiseConv2DMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(InstanceWiseConv2DMessage& a, InstanceWiseConv2DMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(InstanceWiseConv2DMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(InstanceWiseConv2DMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  InstanceWiseConv2DMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<InstanceWiseConv2DMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const InstanceWiseConv2DMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const InstanceWiseConv2DMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(InstanceWiseConv2DMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "trt_extensions.InstanceWiseConv2DMessage";
  }
  protected:
  explicit InstanceWiseConv2DMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputSizeFieldNumber = 7,
    kOutputSizeFieldNumber = 8,
    kPaddingWidthFieldNumber = 1,
    kPaddingHeightFieldNumber = 2,
    kStrideWidthFieldNumber = 3,
    kStrideHeightFieldNumber = 4,
    kDtypeFieldNumber = 5,
    kFormatFieldNumber = 6,
    kInputScaleFieldNumber = 9,
    kOutputScaleFieldNumber = 10,
  };
  // repeated int64 input_size = 7;
  int input_size_size() const;
  private:
  int _internal_input_size_size() const;
  public:
  void clear_input_size();
  private:
  int64_t _internal_input_size(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_input_size() const;
  void _internal_add_input_size(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_input_size();
  public:
  int64_t input_size(int index) const;
  void set_input_size(int index, int64_t value);
  void add_input_size(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      input_size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_input_size();

  // repeated int64 output_size = 8;
  int output_size_size() const;
  private:
  int _internal_output_size_size() const;
  public:
  void clear_output_size();
  private:
  int64_t _internal_output_size(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_output_size() const;
  void _internal_add_output_size(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_output_size();
  public:
  int64_t output_size(int index) const;
  void set_output_size(int index, int64_t value);
  void add_output_size(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      output_size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_output_size();

  // int64 padding_width = 1;
  void clear_padding_width();
  int64_t padding_width() const;
  void set_padding_width(int64_t value);
  private:
  int64_t _internal_padding_width() const;
  void _internal_set_padding_width(int64_t value);
  public:

  // int64 padding_height = 2;
  void clear_padding_height();
  int64_t padding_height() const;
  void set_padding_height(int64_t value);
  private:
  int64_t _internal_padding_height() const;
  void _internal_set_padding_height(int64_t value);
  public:

  // int64 stride_width = 3;
  void clear_stride_width();
  int64_t stride_width() const;
  void set_stride_width(int64_t value);
  private:
  int64_t _internal_stride_width() const;
  void _internal_set_stride_width(int64_t value);
  public:

  // int64 stride_height = 4;
  void clear_stride_height();
  int64_t stride_height() const;
  void set_stride_height(int64_t value);
  private:
  int64_t _internal_stride_height() const;
  void _internal_set_stride_height(int64_t value);
  public:

  // .trt_extensions.DataTypeMessage dtype = 5;
  void clear_dtype();
  ::trt_extensions::DataTypeMessage dtype() const;
  void set_dtype(::trt_extensions::DataTypeMessage value);
  private:
  ::trt_extensions::DataTypeMessage _internal_dtype() const;
  void _internal_set_dtype(::trt_extensions::DataTypeMessage value);
  public:

  // .trt_extensions.DataFormatMessage format = 6;
  void clear_format();
  ::trt_extensions::DataFormatMessage format() const;
  void set_format(::trt_extensions::DataFormatMessage value);
  private:
  ::trt_extensions::DataFormatMessage _internal_format() const;
  void _internal_set_format(::trt_extensions::DataFormatMessage value);
  public:

  // float input_scale = 9;
  void clear_input_scale();
  float input_scale() const;
  void set_input_scale(float value);
  private:
  float _internal_input_scale() const;
  void _internal_set_input_scale(float value);
  public:

  // float output_scale = 10;
  void clear_output_scale();
  float output_scale() const;
  void set_output_scale(float value);
  private:
  float _internal_output_scale() const;
  void _internal_set_output_scale(float value);
  public:

  // @@protoc_insertion_point(class_scope:trt_extensions.InstanceWiseConv2DMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > input_size_;
  mutable std::atomic<int> _input_size_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > output_size_;
  mutable std::atomic<int> _output_size_cached_byte_size_;
  int64_t padding_width_;
  int64_t padding_height_;
  int64_t stride_width_;
  int64_t stride_height_;
  int dtype_;
  int format_;
  float input_scale_;
  float output_scale_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2finstance_5fwise_5fconv2d_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// InstanceWiseConv2DMessage

// int64 padding_width = 1;
inline void InstanceWiseConv2DMessage::clear_padding_width() {
  padding_width_ = int64_t{0};
}
inline int64_t InstanceWiseConv2DMessage::_internal_padding_width() const {
  return padding_width_;
}
inline int64_t InstanceWiseConv2DMessage::padding_width() const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.padding_width)
  return _internal_padding_width();
}
inline void InstanceWiseConv2DMessage::_internal_set_padding_width(int64_t value) {
  
  padding_width_ = value;
}
inline void InstanceWiseConv2DMessage::set_padding_width(int64_t value) {
  _internal_set_padding_width(value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.padding_width)
}

// int64 padding_height = 2;
inline void InstanceWiseConv2DMessage::clear_padding_height() {
  padding_height_ = int64_t{0};
}
inline int64_t InstanceWiseConv2DMessage::_internal_padding_height() const {
  return padding_height_;
}
inline int64_t InstanceWiseConv2DMessage::padding_height() const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.padding_height)
  return _internal_padding_height();
}
inline void InstanceWiseConv2DMessage::_internal_set_padding_height(int64_t value) {
  
  padding_height_ = value;
}
inline void InstanceWiseConv2DMessage::set_padding_height(int64_t value) {
  _internal_set_padding_height(value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.padding_height)
}

// int64 stride_width = 3;
inline void InstanceWiseConv2DMessage::clear_stride_width() {
  stride_width_ = int64_t{0};
}
inline int64_t InstanceWiseConv2DMessage::_internal_stride_width() const {
  return stride_width_;
}
inline int64_t InstanceWiseConv2DMessage::stride_width() const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.stride_width)
  return _internal_stride_width();
}
inline void InstanceWiseConv2DMessage::_internal_set_stride_width(int64_t value) {
  
  stride_width_ = value;
}
inline void InstanceWiseConv2DMessage::set_stride_width(int64_t value) {
  _internal_set_stride_width(value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.stride_width)
}

// int64 stride_height = 4;
inline void InstanceWiseConv2DMessage::clear_stride_height() {
  stride_height_ = int64_t{0};
}
inline int64_t InstanceWiseConv2DMessage::_internal_stride_height() const {
  return stride_height_;
}
inline int64_t InstanceWiseConv2DMessage::stride_height() const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.stride_height)
  return _internal_stride_height();
}
inline void InstanceWiseConv2DMessage::_internal_set_stride_height(int64_t value) {
  
  stride_height_ = value;
}
inline void InstanceWiseConv2DMessage::set_stride_height(int64_t value) {
  _internal_set_stride_height(value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.stride_height)
}

// .trt_extensions.DataTypeMessage dtype = 5;
inline void InstanceWiseConv2DMessage::clear_dtype() {
  dtype_ = 0;
}
inline ::trt_extensions::DataTypeMessage InstanceWiseConv2DMessage::_internal_dtype() const {
  return static_cast< ::trt_extensions::DataTypeMessage >(dtype_);
}
inline ::trt_extensions::DataTypeMessage InstanceWiseConv2DMessage::dtype() const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.dtype)
  return _internal_dtype();
}
inline void InstanceWiseConv2DMessage::_internal_set_dtype(::trt_extensions::DataTypeMessage value) {
  
  dtype_ = value;
}
inline void InstanceWiseConv2DMessage::set_dtype(::trt_extensions::DataTypeMessage value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.dtype)
}

// .trt_extensions.DataFormatMessage format = 6;
inline void InstanceWiseConv2DMessage::clear_format() {
  format_ = 0;
}
inline ::trt_extensions::DataFormatMessage InstanceWiseConv2DMessage::_internal_format() const {
  return static_cast< ::trt_extensions::DataFormatMessage >(format_);
}
inline ::trt_extensions::DataFormatMessage InstanceWiseConv2DMessage::format() const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.format)
  return _internal_format();
}
inline void InstanceWiseConv2DMessage::_internal_set_format(::trt_extensions::DataFormatMessage value) {
  
  format_ = value;
}
inline void InstanceWiseConv2DMessage::set_format(::trt_extensions::DataFormatMessage value) {
  _internal_set_format(value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.format)
}

// repeated int64 input_size = 7;
inline int InstanceWiseConv2DMessage::_internal_input_size_size() const {
  return input_size_.size();
}
inline int InstanceWiseConv2DMessage::input_size_size() const {
  return _internal_input_size_size();
}
inline void InstanceWiseConv2DMessage::clear_input_size() {
  input_size_.Clear();
}
inline int64_t InstanceWiseConv2DMessage::_internal_input_size(int index) const {
  return input_size_.Get(index);
}
inline int64_t InstanceWiseConv2DMessage::input_size(int index) const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.input_size)
  return _internal_input_size(index);
}
inline void InstanceWiseConv2DMessage::set_input_size(int index, int64_t value) {
  input_size_.Set(index, value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.input_size)
}
inline void InstanceWiseConv2DMessage::_internal_add_input_size(int64_t value) {
  input_size_.Add(value);
}
inline void InstanceWiseConv2DMessage::add_input_size(int64_t value) {
  _internal_add_input_size(value);
  // @@protoc_insertion_point(field_add:trt_extensions.InstanceWiseConv2DMessage.input_size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
InstanceWiseConv2DMessage::_internal_input_size() const {
  return input_size_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
InstanceWiseConv2DMessage::input_size() const {
  // @@protoc_insertion_point(field_list:trt_extensions.InstanceWiseConv2DMessage.input_size)
  return _internal_input_size();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
InstanceWiseConv2DMessage::_internal_mutable_input_size() {
  return &input_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
InstanceWiseConv2DMessage::mutable_input_size() {
  // @@protoc_insertion_point(field_mutable_list:trt_extensions.InstanceWiseConv2DMessage.input_size)
  return _internal_mutable_input_size();
}

// repeated int64 output_size = 8;
inline int InstanceWiseConv2DMessage::_internal_output_size_size() const {
  return output_size_.size();
}
inline int InstanceWiseConv2DMessage::output_size_size() const {
  return _internal_output_size_size();
}
inline void InstanceWiseConv2DMessage::clear_output_size() {
  output_size_.Clear();
}
inline int64_t InstanceWiseConv2DMessage::_internal_output_size(int index) const {
  return output_size_.Get(index);
}
inline int64_t InstanceWiseConv2DMessage::output_size(int index) const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.output_size)
  return _internal_output_size(index);
}
inline void InstanceWiseConv2DMessage::set_output_size(int index, int64_t value) {
  output_size_.Set(index, value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.output_size)
}
inline void InstanceWiseConv2DMessage::_internal_add_output_size(int64_t value) {
  output_size_.Add(value);
}
inline void InstanceWiseConv2DMessage::add_output_size(int64_t value) {
  _internal_add_output_size(value);
  // @@protoc_insertion_point(field_add:trt_extensions.InstanceWiseConv2DMessage.output_size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
InstanceWiseConv2DMessage::_internal_output_size() const {
  return output_size_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
InstanceWiseConv2DMessage::output_size() const {
  // @@protoc_insertion_point(field_list:trt_extensions.InstanceWiseConv2DMessage.output_size)
  return _internal_output_size();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
InstanceWiseConv2DMessage::_internal_mutable_output_size() {
  return &output_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
InstanceWiseConv2DMessage::mutable_output_size() {
  // @@protoc_insertion_point(field_mutable_list:trt_extensions.InstanceWiseConv2DMessage.output_size)
  return _internal_mutable_output_size();
}

// float input_scale = 9;
inline void InstanceWiseConv2DMessage::clear_input_scale() {
  input_scale_ = 0;
}
inline float InstanceWiseConv2DMessage::_internal_input_scale() const {
  return input_scale_;
}
inline float InstanceWiseConv2DMessage::input_scale() const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.input_scale)
  return _internal_input_scale();
}
inline void InstanceWiseConv2DMessage::_internal_set_input_scale(float value) {
  
  input_scale_ = value;
}
inline void InstanceWiseConv2DMessage::set_input_scale(float value) {
  _internal_set_input_scale(value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.input_scale)
}

// float output_scale = 10;
inline void InstanceWiseConv2DMessage::clear_output_scale() {
  output_scale_ = 0;
}
inline float InstanceWiseConv2DMessage::_internal_output_scale() const {
  return output_scale_;
}
inline float InstanceWiseConv2DMessage::output_scale() const {
  // @@protoc_insertion_point(field_get:trt_extensions.InstanceWiseConv2DMessage.output_scale)
  return _internal_output_scale();
}
inline void InstanceWiseConv2DMessage::_internal_set_output_scale(float value) {
  
  output_scale_ = value;
}
inline void InstanceWiseConv2DMessage::set_output_scale(float value) {
  _internal_set_output_scale(value);
  // @@protoc_insertion_point(field_set:trt_extensions.InstanceWiseConv2DMessage.output_scale)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace trt_extensions

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2finstance_5fwise_5fconv2d_2eproto

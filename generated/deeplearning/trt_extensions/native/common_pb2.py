# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: deeplearning/trt_extensions/native/common.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor.FileDescriptor(
  name='deeplearning/trt_extensions/native/common.proto',
  package='trt_extensions',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n/deeplearning/trt_extensions/native/common.proto\x12\x0etrt_extensions*?\n\x0f\x44\x61taTypeMessage\x12\n\n\x06kFloat\x10\x00\x12\t\n\x05kHalf\x10\x01\x12\t\n\x05kInt8\x10\x02\x12\n\n\x06kInt32\x10\x03*Y\n\x11\x44\x61taFormatMessage\x12\x0b\n\x07kLINEAR\x10\x00\x12\t\n\x05kCHW2\x10\x01\x12\t\n\x05kHWC8\x10\x02\x12\t\n\x05kCHW4\x10\x03\x12\n\n\x06kCHW16\x10\x04\x12\n\n\x06kCHW32\x10\x05\x62\x06proto3'
)

_DATATYPEMESSAGE = _descriptor.EnumDescriptor(
  name='DataTypeMessage',
  full_name='trt_extensions.DataTypeMessage',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='kFloat', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kHalf', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kInt8', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kInt32', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=67,
  serialized_end=130,
)
_sym_db.RegisterEnumDescriptor(_DATATYPEMESSAGE)

DataTypeMessage = enum_type_wrapper.EnumTypeWrapper(_DATATYPEMESSAGE)
_DATAFORMATMESSAGE = _descriptor.EnumDescriptor(
  name='DataFormatMessage',
  full_name='trt_extensions.DataFormatMessage',
  filename=None,
  file=DESCRIPTOR,
  create_key=_descriptor._internal_create_key,
  values=[
    _descriptor.EnumValueDescriptor(
      name='kLINEAR', index=0, number=0,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kCHW2', index=1, number=1,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kHWC8', index=2, number=2,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kCHW4', index=3, number=3,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kCHW16', index=4, number=4,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
    _descriptor.EnumValueDescriptor(
      name='kCHW32', index=5, number=5,
      serialized_options=None,
      type=None,
      create_key=_descriptor._internal_create_key),
  ],
  containing_type=None,
  serialized_options=None,
  serialized_start=132,
  serialized_end=221,
)
_sym_db.RegisterEnumDescriptor(_DATAFORMATMESSAGE)

DataFormatMessage = enum_type_wrapper.EnumTypeWrapper(_DATAFORMATMESSAGE)
kFloat = 0
kHalf = 1
kInt8 = 2
kInt32 = 3
kLINEAR = 0
kCHW2 = 1
kHWC8 = 2
kCHW4 = 3
kCHW16 = 4
kCHW32 = 5


DESCRIPTOR.enum_types_by_name['DataTypeMessage'] = _DATATYPEMESSAGE
DESCRIPTOR.enum_types_by_name['DataFormatMessage'] = _DATAFORMATMESSAGE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)


# @@protoc_insertion_point(module_scope)

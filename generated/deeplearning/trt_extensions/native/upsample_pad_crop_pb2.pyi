"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.deeplearning.trt_extensions.native.common_pb2 import (
    DataFormatMessageValue as deeplearning___trt_extensions___native___common_pb2___DataFormatMessageValue,
    DataTypeMessageValue as deeplearning___trt_extensions___native___common_pb2___DataTypeMessageValue,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    RepeatedScalarFieldContainer as google___protobuf___internal___containers___RepeatedScalarFieldContainer,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    Iterable as typing___Iterable,
    Optional as typing___Optional,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class UpsamplePadCropMessage(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    padding: builtin___int = ...
    crop_size: builtin___int = ...
    dtype: deeplearning___trt_extensions___native___common_pb2___DataTypeMessageValue = ...
    format: deeplearning___trt_extensions___native___common_pb2___DataFormatMessageValue = ...
    input_size: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    output_size: google___protobuf___internal___containers___RepeatedScalarFieldContainer[builtin___int] = ...
    input_scale: builtin___float = ...
    output_scale: builtin___float = ...

    def __init__(self,
        *,
        padding : typing___Optional[builtin___int] = None,
        crop_size : typing___Optional[builtin___int] = None,
        dtype : typing___Optional[deeplearning___trt_extensions___native___common_pb2___DataTypeMessageValue] = None,
        format : typing___Optional[deeplearning___trt_extensions___native___common_pb2___DataFormatMessageValue] = None,
        input_size : typing___Optional[typing___Iterable[builtin___int]] = None,
        output_size : typing___Optional[typing___Iterable[builtin___int]] = None,
        input_scale : typing___Optional[builtin___float] = None,
        output_scale : typing___Optional[builtin___float] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"crop_size",b"crop_size",u"dtype",b"dtype",u"format",b"format",u"input_scale",b"input_scale",u"input_size",b"input_size",u"output_scale",b"output_scale",u"output_size",b"output_size",u"padding",b"padding"]) -> None: ...
type___UpsamplePadCropMessage = UpsamplePadCropMessage

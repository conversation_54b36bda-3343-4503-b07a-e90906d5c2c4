// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/trt_extensions/native/common.proto

#include "deeplearning/trt_extensions/native/common.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace trt_extensions {
}  // namespace trt_extensions
static const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* file_level_enum_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto[2];
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto = nullptr;
const uint32_t TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto::offsets[1] = {};
static constexpr ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema* schemas = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::Message* const* file_default_instances = nullptr;

const char descriptor_table_protodef_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n/deeplearning/trt_extensions/native/com"
  "mon.proto\022\016trt_extensions*\?\n\017DataTypeMes"
  "sage\022\n\n\006kFloat\020\000\022\t\n\005kHalf\020\001\022\t\n\005kInt8\020\002\022\n"
  "\n\006kInt32\020\003*Y\n\021DataFormatMessage\022\013\n\007kLINE"
  "AR\020\000\022\t\n\005kCHW2\020\001\022\t\n\005kHWC8\020\002\022\t\n\005kCHW4\020\003\022\n\n"
  "\006kCHW16\020\004\022\n\n\006kCHW32\020\005b\006proto3"
  ;
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto = {
  false, false, 229, descriptor_table_protodef_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto, "deeplearning/trt_extensions/native/common.proto", 
  &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto_once, nullptr, 0, 0,
  schemas, file_default_instances, TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto::offsets,
  nullptr, file_level_enum_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto, file_level_service_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto_getter() {
  return &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto(&descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto);
namespace trt_extensions {
const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataTypeMessage_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto);
  return file_level_enum_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto[0];
}
bool DataTypeMessage_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
      return true;
    default:
      return false;
  }
}

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataFormatMessage_descriptor() {
  ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(&descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto);
  return file_level_enum_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto[1];
}
bool DataFormatMessage_IsValid(int value) {
  switch (value) {
    case 0:
    case 1:
    case 2:
    case 3:
    case 4:
    case 5:
      return true;
    default:
      return false;
  }
}


// @@protoc_insertion_point(namespace_scope)
}  // namespace trt_extensions
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

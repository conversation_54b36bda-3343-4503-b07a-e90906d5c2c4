// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/trt_extensions/native/upsample.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "deeplearning/trt_extensions/native/common.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_2eproto;
namespace trt_extensions {
class UpsampleMessage;
struct UpsampleMessageDefaultTypeInternal;
extern UpsampleMessageDefaultTypeInternal _UpsampleMessage_default_instance_;
}  // namespace trt_extensions
PROTOBUF_NAMESPACE_OPEN
template<> ::trt_extensions::UpsampleMessage* Arena::CreateMaybeMessage<::trt_extensions::UpsampleMessage>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace trt_extensions {

// ===================================================================

class UpsampleMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:trt_extensions.UpsampleMessage) */ {
 public:
  inline UpsampleMessage() : UpsampleMessage(nullptr) {}
  ~UpsampleMessage() override;
  explicit constexpr UpsampleMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpsampleMessage(const UpsampleMessage& from);
  UpsampleMessage(UpsampleMessage&& from) noexcept
    : UpsampleMessage() {
    *this = ::std::move(from);
  }

  inline UpsampleMessage& operator=(const UpsampleMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpsampleMessage& operator=(UpsampleMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpsampleMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpsampleMessage* internal_default_instance() {
    return reinterpret_cast<const UpsampleMessage*>(
               &_UpsampleMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(UpsampleMessage& a, UpsampleMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(UpsampleMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpsampleMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpsampleMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpsampleMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UpsampleMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UpsampleMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpsampleMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "trt_extensions.UpsampleMessage";
  }
  protected:
  explicit UpsampleMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputSizeFieldNumber = 5,
    kOutputSizeFieldNumber = 6,
    kWidthFieldNumber = 1,
    kHeightFieldNumber = 2,
    kDtypeFieldNumber = 3,
    kFormatFieldNumber = 4,
    kInputScaleFieldNumber = 7,
    kOutputScaleFieldNumber = 8,
  };
  // repeated int64 input_size = 5;
  int input_size_size() const;
  private:
  int _internal_input_size_size() const;
  public:
  void clear_input_size();
  private:
  int64_t _internal_input_size(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_input_size() const;
  void _internal_add_input_size(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_input_size();
  public:
  int64_t input_size(int index) const;
  void set_input_size(int index, int64_t value);
  void add_input_size(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      input_size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_input_size();

  // repeated int64 output_size = 6;
  int output_size_size() const;
  private:
  int _internal_output_size_size() const;
  public:
  void clear_output_size();
  private:
  int64_t _internal_output_size(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_output_size() const;
  void _internal_add_output_size(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_output_size();
  public:
  int64_t output_size(int index) const;
  void set_output_size(int index, int64_t value);
  void add_output_size(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      output_size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_output_size();

  // int64 width = 1;
  void clear_width();
  int64_t width() const;
  void set_width(int64_t value);
  private:
  int64_t _internal_width() const;
  void _internal_set_width(int64_t value);
  public:

  // int64 height = 2;
  void clear_height();
  int64_t height() const;
  void set_height(int64_t value);
  private:
  int64_t _internal_height() const;
  void _internal_set_height(int64_t value);
  public:

  // .trt_extensions.DataTypeMessage dtype = 3;
  void clear_dtype();
  ::trt_extensions::DataTypeMessage dtype() const;
  void set_dtype(::trt_extensions::DataTypeMessage value);
  private:
  ::trt_extensions::DataTypeMessage _internal_dtype() const;
  void _internal_set_dtype(::trt_extensions::DataTypeMessage value);
  public:

  // .trt_extensions.DataFormatMessage format = 4;
  void clear_format();
  ::trt_extensions::DataFormatMessage format() const;
  void set_format(::trt_extensions::DataFormatMessage value);
  private:
  ::trt_extensions::DataFormatMessage _internal_format() const;
  void _internal_set_format(::trt_extensions::DataFormatMessage value);
  public:

  // float input_scale = 7;
  void clear_input_scale();
  float input_scale() const;
  void set_input_scale(float value);
  private:
  float _internal_input_scale() const;
  void _internal_set_input_scale(float value);
  public:

  // float output_scale = 8;
  void clear_output_scale();
  float output_scale() const;
  void set_output_scale(float value);
  private:
  float _internal_output_scale() const;
  void _internal_set_output_scale(float value);
  public:

  // @@protoc_insertion_point(class_scope:trt_extensions.UpsampleMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > input_size_;
  mutable std::atomic<int> _input_size_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > output_size_;
  mutable std::atomic<int> _output_size_cached_byte_size_;
  int64_t width_;
  int64_t height_;
  int dtype_;
  int format_;
  float input_scale_;
  float output_scale_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// UpsampleMessage

// int64 width = 1;
inline void UpsampleMessage::clear_width() {
  width_ = int64_t{0};
}
inline int64_t UpsampleMessage::_internal_width() const {
  return width_;
}
inline int64_t UpsampleMessage::width() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleMessage.width)
  return _internal_width();
}
inline void UpsampleMessage::_internal_set_width(int64_t value) {
  
  width_ = value;
}
inline void UpsampleMessage::set_width(int64_t value) {
  _internal_set_width(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleMessage.width)
}

// int64 height = 2;
inline void UpsampleMessage::clear_height() {
  height_ = int64_t{0};
}
inline int64_t UpsampleMessage::_internal_height() const {
  return height_;
}
inline int64_t UpsampleMessage::height() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleMessage.height)
  return _internal_height();
}
inline void UpsampleMessage::_internal_set_height(int64_t value) {
  
  height_ = value;
}
inline void UpsampleMessage::set_height(int64_t value) {
  _internal_set_height(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleMessage.height)
}

// .trt_extensions.DataTypeMessage dtype = 3;
inline void UpsampleMessage::clear_dtype() {
  dtype_ = 0;
}
inline ::trt_extensions::DataTypeMessage UpsampleMessage::_internal_dtype() const {
  return static_cast< ::trt_extensions::DataTypeMessage >(dtype_);
}
inline ::trt_extensions::DataTypeMessage UpsampleMessage::dtype() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleMessage.dtype)
  return _internal_dtype();
}
inline void UpsampleMessage::_internal_set_dtype(::trt_extensions::DataTypeMessage value) {
  
  dtype_ = value;
}
inline void UpsampleMessage::set_dtype(::trt_extensions::DataTypeMessage value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleMessage.dtype)
}

// .trt_extensions.DataFormatMessage format = 4;
inline void UpsampleMessage::clear_format() {
  format_ = 0;
}
inline ::trt_extensions::DataFormatMessage UpsampleMessage::_internal_format() const {
  return static_cast< ::trt_extensions::DataFormatMessage >(format_);
}
inline ::trt_extensions::DataFormatMessage UpsampleMessage::format() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleMessage.format)
  return _internal_format();
}
inline void UpsampleMessage::_internal_set_format(::trt_extensions::DataFormatMessage value) {
  
  format_ = value;
}
inline void UpsampleMessage::set_format(::trt_extensions::DataFormatMessage value) {
  _internal_set_format(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleMessage.format)
}

// repeated int64 input_size = 5;
inline int UpsampleMessage::_internal_input_size_size() const {
  return input_size_.size();
}
inline int UpsampleMessage::input_size_size() const {
  return _internal_input_size_size();
}
inline void UpsampleMessage::clear_input_size() {
  input_size_.Clear();
}
inline int64_t UpsampleMessage::_internal_input_size(int index) const {
  return input_size_.Get(index);
}
inline int64_t UpsampleMessage::input_size(int index) const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleMessage.input_size)
  return _internal_input_size(index);
}
inline void UpsampleMessage::set_input_size(int index, int64_t value) {
  input_size_.Set(index, value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleMessage.input_size)
}
inline void UpsampleMessage::_internal_add_input_size(int64_t value) {
  input_size_.Add(value);
}
inline void UpsampleMessage::add_input_size(int64_t value) {
  _internal_add_input_size(value);
  // @@protoc_insertion_point(field_add:trt_extensions.UpsampleMessage.input_size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
UpsampleMessage::_internal_input_size() const {
  return input_size_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
UpsampleMessage::input_size() const {
  // @@protoc_insertion_point(field_list:trt_extensions.UpsampleMessage.input_size)
  return _internal_input_size();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
UpsampleMessage::_internal_mutable_input_size() {
  return &input_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
UpsampleMessage::mutable_input_size() {
  // @@protoc_insertion_point(field_mutable_list:trt_extensions.UpsampleMessage.input_size)
  return _internal_mutable_input_size();
}

// repeated int64 output_size = 6;
inline int UpsampleMessage::_internal_output_size_size() const {
  return output_size_.size();
}
inline int UpsampleMessage::output_size_size() const {
  return _internal_output_size_size();
}
inline void UpsampleMessage::clear_output_size() {
  output_size_.Clear();
}
inline int64_t UpsampleMessage::_internal_output_size(int index) const {
  return output_size_.Get(index);
}
inline int64_t UpsampleMessage::output_size(int index) const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleMessage.output_size)
  return _internal_output_size(index);
}
inline void UpsampleMessage::set_output_size(int index, int64_t value) {
  output_size_.Set(index, value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleMessage.output_size)
}
inline void UpsampleMessage::_internal_add_output_size(int64_t value) {
  output_size_.Add(value);
}
inline void UpsampleMessage::add_output_size(int64_t value) {
  _internal_add_output_size(value);
  // @@protoc_insertion_point(field_add:trt_extensions.UpsampleMessage.output_size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
UpsampleMessage::_internal_output_size() const {
  return output_size_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
UpsampleMessage::output_size() const {
  // @@protoc_insertion_point(field_list:trt_extensions.UpsampleMessage.output_size)
  return _internal_output_size();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
UpsampleMessage::_internal_mutable_output_size() {
  return &output_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
UpsampleMessage::mutable_output_size() {
  // @@protoc_insertion_point(field_mutable_list:trt_extensions.UpsampleMessage.output_size)
  return _internal_mutable_output_size();
}

// float input_scale = 7;
inline void UpsampleMessage::clear_input_scale() {
  input_scale_ = 0;
}
inline float UpsampleMessage::_internal_input_scale() const {
  return input_scale_;
}
inline float UpsampleMessage::input_scale() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleMessage.input_scale)
  return _internal_input_scale();
}
inline void UpsampleMessage::_internal_set_input_scale(float value) {
  
  input_scale_ = value;
}
inline void UpsampleMessage::set_input_scale(float value) {
  _internal_set_input_scale(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleMessage.input_scale)
}

// float output_scale = 8;
inline void UpsampleMessage::clear_output_scale() {
  output_scale_ = 0;
}
inline float UpsampleMessage::_internal_output_scale() const {
  return output_scale_;
}
inline float UpsampleMessage::output_scale() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleMessage.output_scale)
  return _internal_output_scale();
}
inline void UpsampleMessage::_internal_set_output_scale(float value) {
  
  output_scale_ = value;
}
inline void UpsampleMessage::set_output_scale(float value) {
  _internal_set_output_scale(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleMessage.output_scale)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace trt_extensions

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_2eproto

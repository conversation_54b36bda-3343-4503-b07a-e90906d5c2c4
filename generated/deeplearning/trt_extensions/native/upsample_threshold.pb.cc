// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/trt_extensions/native/upsample_threshold.proto

#include "deeplearning/trt_extensions/native/upsample_threshold.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace trt_extensions {
constexpr UpsampleThresholdMessage::UpsampleThresholdMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : input_size_()
  , _input_size_cached_byte_size_(0)
  , output_size_()
  , _output_size_cached_byte_size_(0)
  , crop_width_(int64_t{0})
  , crop_height_(int64_t{0})
  , min_threshold_(0)
  , dtype_(0)

  , format_(0)

  , input_scale_(0)
  , output_scale_(0){}
struct UpsampleThresholdMessageDefaultTypeInternal {
  constexpr UpsampleThresholdMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpsampleThresholdMessageDefaultTypeInternal() {}
  union {
    UpsampleThresholdMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpsampleThresholdMessageDefaultTypeInternal _UpsampleThresholdMessage_default_instance_;
}  // namespace trt_extensions
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto = nullptr;

const uint32_t TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, crop_width_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, crop_height_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, min_threshold_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, dtype_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, format_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, input_size_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, output_size_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, input_scale_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsampleThresholdMessage, output_scale_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::trt_extensions::UpsampleThresholdMessage)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::trt_extensions::_UpsampleThresholdMessage_default_instance_),
};

const char descriptor_table_protodef_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n;deeplearning/trt_extensions/native/ups"
  "ample_threshold.proto\022\016trt_extensions\032/d"
  "eeplearning/trt_extensions/native/common"
  ".proto\"\221\002\n\030UpsampleThresholdMessage\022\022\n\nc"
  "rop_width\030\001 \001(\003\022\023\n\013crop_height\030\002 \001(\003\022\025\n\r"
  "min_threshold\030\003 \001(\002\022.\n\005dtype\030\004 \001(\0162\037.trt"
  "_extensions.DataTypeMessage\0221\n\006format\030\005 "
  "\001(\0162!.trt_extensions.DataFormatMessage\022\022"
  "\n\ninput_size\030\006 \003(\003\022\023\n\013output_size\030\007 \003(\003\022"
  "\023\n\013input_scale\030\010 \001(\002\022\024\n\014output_scale\030\t \001"
  "(\002b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto_deps[1] = {
  &::descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto = {
  false, false, 410, descriptor_table_protodef_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto, "deeplearning/trt_extensions/native/upsample_threshold.proto", 
  &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto_once, descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto::offsets,
  file_level_metadata_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto, file_level_enum_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto, file_level_service_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto_getter() {
  return &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto(&descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto);
namespace trt_extensions {

// ===================================================================

class UpsampleThresholdMessage::_Internal {
 public:
};

UpsampleThresholdMessage::UpsampleThresholdMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  input_size_(arena),
  output_size_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:trt_extensions.UpsampleThresholdMessage)
}
UpsampleThresholdMessage::UpsampleThresholdMessage(const UpsampleThresholdMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      input_size_(from.input_size_),
      output_size_(from.output_size_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&crop_width_, &from.crop_width_,
    static_cast<size_t>(reinterpret_cast<char*>(&output_scale_) -
    reinterpret_cast<char*>(&crop_width_)) + sizeof(output_scale_));
  // @@protoc_insertion_point(copy_constructor:trt_extensions.UpsampleThresholdMessage)
}

inline void UpsampleThresholdMessage::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&crop_width_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&output_scale_) -
    reinterpret_cast<char*>(&crop_width_)) + sizeof(output_scale_));
}

UpsampleThresholdMessage::~UpsampleThresholdMessage() {
  // @@protoc_insertion_point(destructor:trt_extensions.UpsampleThresholdMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UpsampleThresholdMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void UpsampleThresholdMessage::ArenaDtor(void* object) {
  UpsampleThresholdMessage* _this = reinterpret_cast< UpsampleThresholdMessage* >(object);
  (void)_this;
}
void UpsampleThresholdMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UpsampleThresholdMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UpsampleThresholdMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:trt_extensions.UpsampleThresholdMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_size_.Clear();
  output_size_.Clear();
  ::memset(&crop_width_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_scale_) -
      reinterpret_cast<char*>(&crop_width_)) + sizeof(output_scale_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UpsampleThresholdMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 crop_width = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          crop_width_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 crop_height = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          crop_height_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float min_threshold = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 29)) {
          min_threshold_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // .trt_extensions.DataTypeMessage dtype = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_dtype(static_cast<::trt_extensions::DataTypeMessage>(val));
        } else
          goto handle_unusual;
        continue;
      // .trt_extensions.DataFormatMessage format = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 40)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_format(static_cast<::trt_extensions::DataFormatMessage>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated int64 input_size = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_input_size(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 48) {
          _internal_add_input_size(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 output_size = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 58)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_output_size(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 56) {
          _internal_add_output_size(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float input_scale = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          input_scale_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float output_scale = 9;
      case 9:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 77)) {
          output_scale_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UpsampleThresholdMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:trt_extensions.UpsampleThresholdMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 crop_width = 1;
  if (this->_internal_crop_width() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_crop_width(), target);
  }

  // int64 crop_height = 2;
  if (this->_internal_crop_height() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_crop_height(), target);
  }

  // float min_threshold = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_threshold = this->_internal_min_threshold();
  uint32_t raw_min_threshold;
  memcpy(&raw_min_threshold, &tmp_min_threshold, sizeof(tmp_min_threshold));
  if (raw_min_threshold != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(3, this->_internal_min_threshold(), target);
  }

  // .trt_extensions.DataTypeMessage dtype = 4;
  if (this->_internal_dtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_dtype(), target);
  }

  // .trt_extensions.DataFormatMessage format = 5;
  if (this->_internal_format() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      5, this->_internal_format(), target);
  }

  // repeated int64 input_size = 6;
  {
    int byte_size = _input_size_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          6, _internal_input_size(), byte_size, target);
    }
  }

  // repeated int64 output_size = 7;
  {
    int byte_size = _output_size_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          7, _internal_output_size(), byte_size, target);
    }
  }

  // float input_scale = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_input_scale = this->_internal_input_scale();
  uint32_t raw_input_scale;
  memcpy(&raw_input_scale, &tmp_input_scale, sizeof(tmp_input_scale));
  if (raw_input_scale != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_input_scale(), target);
  }

  // float output_scale = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_output_scale = this->_internal_output_scale();
  uint32_t raw_output_scale;
  memcpy(&raw_output_scale, &tmp_output_scale, sizeof(tmp_output_scale));
  if (raw_output_scale != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(9, this->_internal_output_scale(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:trt_extensions.UpsampleThresholdMessage)
  return target;
}

size_t UpsampleThresholdMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:trt_extensions.UpsampleThresholdMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int64 input_size = 6;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->input_size_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _input_size_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int64 output_size = 7;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->output_size_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _output_size_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // int64 crop_width = 1;
  if (this->_internal_crop_width() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_crop_width());
  }

  // int64 crop_height = 2;
  if (this->_internal_crop_height() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_crop_height());
  }

  // float min_threshold = 3;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_threshold = this->_internal_min_threshold();
  uint32_t raw_min_threshold;
  memcpy(&raw_min_threshold, &tmp_min_threshold, sizeof(tmp_min_threshold));
  if (raw_min_threshold != 0) {
    total_size += 1 + 4;
  }

  // .trt_extensions.DataTypeMessage dtype = 4;
  if (this->_internal_dtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_dtype());
  }

  // .trt_extensions.DataFormatMessage format = 5;
  if (this->_internal_format() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_format());
  }

  // float input_scale = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_input_scale = this->_internal_input_scale();
  uint32_t raw_input_scale;
  memcpy(&raw_input_scale, &tmp_input_scale, sizeof(tmp_input_scale));
  if (raw_input_scale != 0) {
    total_size += 1 + 4;
  }

  // float output_scale = 9;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_output_scale = this->_internal_output_scale();
  uint32_t raw_output_scale;
  memcpy(&raw_output_scale, &tmp_output_scale, sizeof(tmp_output_scale));
  if (raw_output_scale != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpsampleThresholdMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UpsampleThresholdMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpsampleThresholdMessage::GetClassData() const { return &_class_data_; }

void UpsampleThresholdMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UpsampleThresholdMessage *>(to)->MergeFrom(
      static_cast<const UpsampleThresholdMessage &>(from));
}


void UpsampleThresholdMessage::MergeFrom(const UpsampleThresholdMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:trt_extensions.UpsampleThresholdMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  input_size_.MergeFrom(from.input_size_);
  output_size_.MergeFrom(from.output_size_);
  if (from._internal_crop_width() != 0) {
    _internal_set_crop_width(from._internal_crop_width());
  }
  if (from._internal_crop_height() != 0) {
    _internal_set_crop_height(from._internal_crop_height());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_min_threshold = from._internal_min_threshold();
  uint32_t raw_min_threshold;
  memcpy(&raw_min_threshold, &tmp_min_threshold, sizeof(tmp_min_threshold));
  if (raw_min_threshold != 0) {
    _internal_set_min_threshold(from._internal_min_threshold());
  }
  if (from._internal_dtype() != 0) {
    _internal_set_dtype(from._internal_dtype());
  }
  if (from._internal_format() != 0) {
    _internal_set_format(from._internal_format());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_input_scale = from._internal_input_scale();
  uint32_t raw_input_scale;
  memcpy(&raw_input_scale, &tmp_input_scale, sizeof(tmp_input_scale));
  if (raw_input_scale != 0) {
    _internal_set_input_scale(from._internal_input_scale());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_output_scale = from._internal_output_scale();
  uint32_t raw_output_scale;
  memcpy(&raw_output_scale, &tmp_output_scale, sizeof(tmp_output_scale));
  if (raw_output_scale != 0) {
    _internal_set_output_scale(from._internal_output_scale());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UpsampleThresholdMessage::CopyFrom(const UpsampleThresholdMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:trt_extensions.UpsampleThresholdMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpsampleThresholdMessage::IsInitialized() const {
  return true;
}

void UpsampleThresholdMessage::InternalSwap(UpsampleThresholdMessage* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  input_size_.InternalSwap(&other->input_size_);
  output_size_.InternalSwap(&other->output_size_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(UpsampleThresholdMessage, output_scale_)
      + sizeof(UpsampleThresholdMessage::output_scale_)
      - PROTOBUF_FIELD_OFFSET(UpsampleThresholdMessage, crop_width_)>(
          reinterpret_cast<char*>(&crop_width_),
          reinterpret_cast<char*>(&other->crop_width_));
}

::PROTOBUF_NAMESPACE_ID::Metadata UpsampleThresholdMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto_getter, &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto_once,
      file_level_metadata_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace trt_extensions
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::trt_extensions::UpsampleThresholdMessage* Arena::CreateMaybeMessage< ::trt_extensions::UpsampleThresholdMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::trt_extensions::UpsampleThresholdMessage >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: deeplearning/trt_extensions/native/instance_wise_conv2d.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.deeplearning.trt_extensions.native import common_pb2 as deeplearning_dot_trt__extensions_dot_native_dot_common__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='deeplearning/trt_extensions/native/instance_wise_conv2d.proto',
  package='trt_extensions',
  syntax='proto3',
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n=deeplearning/trt_extensions/native/instance_wise_conv2d.proto\x12\x0etrt_extensions\x1a/deeplearning/trt_extensions/native/common.proto\"\xae\x02\n\x19InstanceWiseConv2DMessage\x12\x15\n\rpadding_width\x18\x01 \x01(\x03\x12\x16\n\x0epadding_height\x18\x02 \x01(\x03\x12\x14\n\x0cstride_width\x18\x03 \x01(\x03\x12\x15\n\rstride_height\x18\x04 \x01(\x03\x12.\n\x05\x64type\x18\x05 \x01(\x0e\x32\x1f.trt_extensions.DataTypeMessage\x12\x31\n\x06\x66ormat\x18\x06 \x01(\x0e\x32!.trt_extensions.DataFormatMessage\x12\x12\n\ninput_size\x18\x07 \x03(\x03\x12\x13\n\x0boutput_size\x18\x08 \x03(\x03\x12\x13\n\x0binput_scale\x18\t \x01(\x02\x12\x14\n\x0coutput_scale\x18\n \x01(\x02\x62\x06proto3'
  ,
  dependencies=[deeplearning_dot_trt__extensions_dot_native_dot_common__pb2.DESCRIPTOR,])




_INSTANCEWISECONV2DMESSAGE = _descriptor.Descriptor(
  name='InstanceWiseConv2DMessage',
  full_name='trt_extensions.InstanceWiseConv2DMessage',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='padding_width', full_name='trt_extensions.InstanceWiseConv2DMessage.padding_width', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='padding_height', full_name='trt_extensions.InstanceWiseConv2DMessage.padding_height', index=1,
      number=2, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stride_width', full_name='trt_extensions.InstanceWiseConv2DMessage.stride_width', index=2,
      number=3, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='stride_height', full_name='trt_extensions.InstanceWiseConv2DMessage.stride_height', index=3,
      number=4, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='dtype', full_name='trt_extensions.InstanceWiseConv2DMessage.dtype', index=4,
      number=5, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='format', full_name='trt_extensions.InstanceWiseConv2DMessage.format', index=5,
      number=6, type=14, cpp_type=8, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='input_size', full_name='trt_extensions.InstanceWiseConv2DMessage.input_size', index=6,
      number=7, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='output_size', full_name='trt_extensions.InstanceWiseConv2DMessage.output_size', index=7,
      number=8, type=3, cpp_type=2, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='input_scale', full_name='trt_extensions.InstanceWiseConv2DMessage.input_scale', index=8,
      number=9, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='output_scale', full_name='trt_extensions.InstanceWiseConv2DMessage.output_scale', index=9,
      number=10, type=2, cpp_type=6, label=1,
      has_default_value=False, default_value=float(0),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=131,
  serialized_end=433,
)

_INSTANCEWISECONV2DMESSAGE.fields_by_name['dtype'].enum_type = deeplearning_dot_trt__extensions_dot_native_dot_common__pb2._DATATYPEMESSAGE
_INSTANCEWISECONV2DMESSAGE.fields_by_name['format'].enum_type = deeplearning_dot_trt__extensions_dot_native_dot_common__pb2._DATAFORMATMESSAGE
DESCRIPTOR.message_types_by_name['InstanceWiseConv2DMessage'] = _INSTANCEWISECONV2DMESSAGE
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

InstanceWiseConv2DMessage = _reflection.GeneratedProtocolMessageType('InstanceWiseConv2DMessage', (_message.Message,), {
  'DESCRIPTOR' : _INSTANCEWISECONV2DMESSAGE,
  '__module__' : 'deeplearning.trt_extensions.native.instance_wise_conv2d_pb2'
  # @@protoc_insertion_point(class_scope:trt_extensions.InstanceWiseConv2DMessage)
  })
_sym_db.RegisterMessage(InstanceWiseConv2DMessage)


# @@protoc_insertion_point(module_scope)

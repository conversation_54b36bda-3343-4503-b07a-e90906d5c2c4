// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/trt_extensions/native/upsample_pad_crop.proto

#include "deeplearning/trt_extensions/native/upsample_pad_crop.pb.h"

#include <algorithm>

#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/extension_set.h>
#include <google/protobuf/wire_format_lite.h>
#include <google/protobuf/descriptor.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/reflection_ops.h>
#include <google/protobuf/wire_format.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>

PROTOBUF_PRAGMA_INIT_SEG
namespace trt_extensions {
constexpr UpsamplePadCropMessage::UpsamplePadCropMessage(
  ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized)
  : input_size_()
  , _input_size_cached_byte_size_(0)
  , output_size_()
  , _output_size_cached_byte_size_(0)
  , padding_(int64_t{0})
  , crop_size_(int64_t{0})
  , dtype_(0)

  , format_(0)

  , input_scale_(0)
  , output_scale_(0){}
struct UpsamplePadCropMessageDefaultTypeInternal {
  constexpr UpsamplePadCropMessageDefaultTypeInternal()
    : _instance(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized{}) {}
  ~UpsamplePadCropMessageDefaultTypeInternal() {}
  union {
    UpsamplePadCropMessage _instance;
  };
};
PROTOBUF_ATTRIBUTE_NO_DESTROY PROTOBUF_CONSTINIT UpsamplePadCropMessageDefaultTypeInternal _UpsamplePadCropMessage_default_instance_;
}  // namespace trt_extensions
static ::PROTOBUF_NAMESPACE_ID::Metadata file_level_metadata_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto[1];
static constexpr ::PROTOBUF_NAMESPACE_ID::EnumDescriptor const** file_level_enum_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto = nullptr;
static constexpr ::PROTOBUF_NAMESPACE_ID::ServiceDescriptor const** file_level_service_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto = nullptr;

const uint32_t TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto::offsets[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  ~0u,  // no _has_bits_
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, _internal_metadata_),
  ~0u,  // no _extensions_
  ~0u,  // no _oneof_case_
  ~0u,  // no _weak_field_map_
  ~0u,  // no _inlined_string_donated_
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, padding_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, crop_size_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, dtype_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, format_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, input_size_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, output_size_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, input_scale_),
  PROTOBUF_FIELD_OFFSET(::trt_extensions::UpsamplePadCropMessage, output_scale_),
};
static const ::PROTOBUF_NAMESPACE_ID::internal::MigrationSchema schemas[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) = {
  { 0, -1, -1, sizeof(::trt_extensions::UpsamplePadCropMessage)},
};

static ::PROTOBUF_NAMESPACE_ID::Message const * const file_default_instances[] = {
  reinterpret_cast<const ::PROTOBUF_NAMESPACE_ID::Message*>(&::trt_extensions::_UpsamplePadCropMessage_default_instance_),
};

const char descriptor_table_protodef_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto[] PROTOBUF_SECTION_VARIABLE(protodesc_cold) =
  "\n:deeplearning/trt_extensions/native/ups"
  "ample_pad_crop.proto\022\016trt_extensions\032/de"
  "eplearning/trt_extensions/native/common."
  "proto\"\363\001\n\026UpsamplePadCropMessage\022\017\n\007padd"
  "ing\030\001 \001(\003\022\021\n\tcrop_size\030\002 \001(\003\022.\n\005dtype\030\003 "
  "\001(\0162\037.trt_extensions.DataTypeMessage\0221\n\006"
  "format\030\004 \001(\0162!.trt_extensions.DataFormat"
  "Message\022\022\n\ninput_size\030\005 \003(\003\022\023\n\013output_si"
  "ze\030\006 \003(\003\022\023\n\013input_scale\030\007 \001(\002\022\024\n\014output_"
  "scale\030\010 \001(\002b\006proto3"
  ;
static const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable*const descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto_deps[1] = {
  &::descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto,
};
static ::PROTOBUF_NAMESPACE_ID::internal::once_flag descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto_once;
const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto = {
  false, false, 379, descriptor_table_protodef_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto, "deeplearning/trt_extensions/native/upsample_pad_crop.proto", 
  &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto_once, descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto_deps, 1, 1,
  schemas, file_default_instances, TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto::offsets,
  file_level_metadata_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto, file_level_enum_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto, file_level_service_descriptors_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto,
};
PROTOBUF_ATTRIBUTE_WEAK const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable* descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto_getter() {
  return &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto;
}

// Force running AddDescriptors() at dynamic initialization time.
PROTOBUF_ATTRIBUTE_INIT_PRIORITY static ::PROTOBUF_NAMESPACE_ID::internal::AddDescriptorsRunner dynamic_init_dummy_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto(&descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto);
namespace trt_extensions {

// ===================================================================

class UpsamplePadCropMessage::_Internal {
 public:
};

UpsamplePadCropMessage::UpsamplePadCropMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                         bool is_message_owned)
  : ::PROTOBUF_NAMESPACE_ID::Message(arena, is_message_owned),
  input_size_(arena),
  output_size_(arena) {
  SharedCtor();
  if (!is_message_owned) {
    RegisterArenaDtor(arena);
  }
  // @@protoc_insertion_point(arena_constructor:trt_extensions.UpsamplePadCropMessage)
}
UpsamplePadCropMessage::UpsamplePadCropMessage(const UpsamplePadCropMessage& from)
  : ::PROTOBUF_NAMESPACE_ID::Message(),
      input_size_(from.input_size_),
      output_size_(from.output_size_) {
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
  ::memcpy(&padding_, &from.padding_,
    static_cast<size_t>(reinterpret_cast<char*>(&output_scale_) -
    reinterpret_cast<char*>(&padding_)) + sizeof(output_scale_));
  // @@protoc_insertion_point(copy_constructor:trt_extensions.UpsamplePadCropMessage)
}

inline void UpsamplePadCropMessage::SharedCtor() {
::memset(reinterpret_cast<char*>(this) + static_cast<size_t>(
    reinterpret_cast<char*>(&padding_) - reinterpret_cast<char*>(this)),
    0, static_cast<size_t>(reinterpret_cast<char*>(&output_scale_) -
    reinterpret_cast<char*>(&padding_)) + sizeof(output_scale_));
}

UpsamplePadCropMessage::~UpsamplePadCropMessage() {
  // @@protoc_insertion_point(destructor:trt_extensions.UpsamplePadCropMessage)
  if (GetArenaForAllocation() != nullptr) return;
  SharedDtor();
  _internal_metadata_.Delete<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

inline void UpsamplePadCropMessage::SharedDtor() {
  GOOGLE_DCHECK(GetArenaForAllocation() == nullptr);
}

void UpsamplePadCropMessage::ArenaDtor(void* object) {
  UpsamplePadCropMessage* _this = reinterpret_cast< UpsamplePadCropMessage* >(object);
  (void)_this;
}
void UpsamplePadCropMessage::RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena*) {
}
void UpsamplePadCropMessage::SetCachedSize(int size) const {
  _cached_size_.Set(size);
}

void UpsamplePadCropMessage::Clear() {
// @@protoc_insertion_point(message_clear_start:trt_extensions.UpsamplePadCropMessage)
  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  input_size_.Clear();
  output_size_.Clear();
  ::memset(&padding_, 0, static_cast<size_t>(
      reinterpret_cast<char*>(&output_scale_) -
      reinterpret_cast<char*>(&padding_)) + sizeof(output_scale_));
  _internal_metadata_.Clear<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>();
}

const char* UpsamplePadCropMessage::_InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) {
#define CHK_(x) if (PROTOBUF_PREDICT_FALSE(!(x))) goto failure
  while (!ctx->Done(&ptr)) {
    uint32_t tag;
    ptr = ::PROTOBUF_NAMESPACE_ID::internal::ReadTag(ptr, &tag);
    switch (tag >> 3) {
      // int64 padding = 1;
      case 1:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 8)) {
          padding_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // int64 crop_size = 2;
      case 2:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 16)) {
          crop_size_ = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // .trt_extensions.DataTypeMessage dtype = 3;
      case 3:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 24)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_dtype(static_cast<::trt_extensions::DataTypeMessage>(val));
        } else
          goto handle_unusual;
        continue;
      // .trt_extensions.DataFormatMessage format = 4;
      case 4:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 32)) {
          uint64_t val = ::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr);
          CHK_(ptr);
          _internal_set_format(static_cast<::trt_extensions::DataFormatMessage>(val));
        } else
          goto handle_unusual;
        continue;
      // repeated int64 input_size = 5;
      case 5:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 42)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_input_size(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 40) {
          _internal_add_input_size(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // repeated int64 output_size = 6;
      case 6:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 50)) {
          ptr = ::PROTOBUF_NAMESPACE_ID::internal::PackedInt64Parser(_internal_mutable_output_size(), ptr, ctx);
          CHK_(ptr);
        } else if (static_cast<uint8_t>(tag) == 48) {
          _internal_add_output_size(::PROTOBUF_NAMESPACE_ID::internal::ReadVarint64(&ptr));
          CHK_(ptr);
        } else
          goto handle_unusual;
        continue;
      // float input_scale = 7;
      case 7:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 61)) {
          input_scale_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      // float output_scale = 8;
      case 8:
        if (PROTOBUF_PREDICT_TRUE(static_cast<uint8_t>(tag) == 69)) {
          output_scale_ = ::PROTOBUF_NAMESPACE_ID::internal::UnalignedLoad<float>(ptr);
          ptr += sizeof(float);
        } else
          goto handle_unusual;
        continue;
      default:
        goto handle_unusual;
    }  // switch
  handle_unusual:
    if ((tag == 0) || ((tag & 7) == 4)) {
      CHK_(ptr);
      ctx->SetLastTag(tag);
      goto message_done;
    }
    ptr = UnknownFieldParse(
        tag,
        _internal_metadata_.mutable_unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(),
        ptr, ctx);
    CHK_(ptr != nullptr);
  }  // while
message_done:
  return ptr;
failure:
  ptr = nullptr;
  goto message_done;
#undef CHK_
}

uint8_t* UpsamplePadCropMessage::_InternalSerialize(
    uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const {
  // @@protoc_insertion_point(serialize_to_array_start:trt_extensions.UpsamplePadCropMessage)
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  // int64 padding = 1;
  if (this->_internal_padding() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(1, this->_internal_padding(), target);
  }

  // int64 crop_size = 2;
  if (this->_internal_crop_size() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteInt64ToArray(2, this->_internal_crop_size(), target);
  }

  // .trt_extensions.DataTypeMessage dtype = 3;
  if (this->_internal_dtype() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      3, this->_internal_dtype(), target);
  }

  // .trt_extensions.DataFormatMessage format = 4;
  if (this->_internal_format() != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteEnumToArray(
      4, this->_internal_format(), target);
  }

  // repeated int64 input_size = 5;
  {
    int byte_size = _input_size_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          5, _internal_input_size(), byte_size, target);
    }
  }

  // repeated int64 output_size = 6;
  {
    int byte_size = _output_size_cached_byte_size_.load(std::memory_order_relaxed);
    if (byte_size > 0) {
      target = stream->WriteInt64Packed(
          6, _internal_output_size(), byte_size, target);
    }
  }

  // float input_scale = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_input_scale = this->_internal_input_scale();
  uint32_t raw_input_scale;
  memcpy(&raw_input_scale, &tmp_input_scale, sizeof(tmp_input_scale));
  if (raw_input_scale != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(7, this->_internal_input_scale(), target);
  }

  // float output_scale = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_output_scale = this->_internal_output_scale();
  uint32_t raw_output_scale;
  memcpy(&raw_output_scale, &tmp_output_scale, sizeof(tmp_output_scale));
  if (raw_output_scale != 0) {
    target = stream->EnsureSpace(target);
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::WriteFloatToArray(8, this->_internal_output_scale(), target);
  }

  if (PROTOBUF_PREDICT_FALSE(_internal_metadata_.have_unknown_fields())) {
    target = ::PROTOBUF_NAMESPACE_ID::internal::WireFormat::InternalSerializeUnknownFieldsToArray(
        _internal_metadata_.unknown_fields<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(::PROTOBUF_NAMESPACE_ID::UnknownFieldSet::default_instance), target, stream);
  }
  // @@protoc_insertion_point(serialize_to_array_end:trt_extensions.UpsamplePadCropMessage)
  return target;
}

size_t UpsamplePadCropMessage::ByteSizeLong() const {
// @@protoc_insertion_point(message_byte_size_start:trt_extensions.UpsamplePadCropMessage)
  size_t total_size = 0;

  uint32_t cached_has_bits = 0;
  // Prevent compiler warnings about cached_has_bits being unused
  (void) cached_has_bits;

  // repeated int64 input_size = 5;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->input_size_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _input_size_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // repeated int64 output_size = 6;
  {
    size_t data_size = ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::
      Int64Size(this->output_size_);
    if (data_size > 0) {
      total_size += 1 +
        ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int32Size(
            static_cast<int32_t>(data_size));
    }
    int cached_size = ::PROTOBUF_NAMESPACE_ID::internal::ToCachedSize(data_size);
    _output_size_cached_byte_size_.store(cached_size,
                                    std::memory_order_relaxed);
    total_size += data_size;
  }

  // int64 padding = 1;
  if (this->_internal_padding() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_padding());
  }

  // int64 crop_size = 2;
  if (this->_internal_crop_size() != 0) {
    total_size += ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::Int64SizePlusOne(this->_internal_crop_size());
  }

  // .trt_extensions.DataTypeMessage dtype = 3;
  if (this->_internal_dtype() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_dtype());
  }

  // .trt_extensions.DataFormatMessage format = 4;
  if (this->_internal_format() != 0) {
    total_size += 1 +
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::EnumSize(this->_internal_format());
  }

  // float input_scale = 7;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_input_scale = this->_internal_input_scale();
  uint32_t raw_input_scale;
  memcpy(&raw_input_scale, &tmp_input_scale, sizeof(tmp_input_scale));
  if (raw_input_scale != 0) {
    total_size += 1 + 4;
  }

  // float output_scale = 8;
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_output_scale = this->_internal_output_scale();
  uint32_t raw_output_scale;
  memcpy(&raw_output_scale, &tmp_output_scale, sizeof(tmp_output_scale));
  if (raw_output_scale != 0) {
    total_size += 1 + 4;
  }

  return MaybeComputeUnknownFieldsSize(total_size, &_cached_size_);
}

const ::PROTOBUF_NAMESPACE_ID::Message::ClassData UpsamplePadCropMessage::_class_data_ = {
    ::PROTOBUF_NAMESPACE_ID::Message::CopyWithSizeCheck,
    UpsamplePadCropMessage::MergeImpl
};
const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*UpsamplePadCropMessage::GetClassData() const { return &_class_data_; }

void UpsamplePadCropMessage::MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to,
                      const ::PROTOBUF_NAMESPACE_ID::Message& from) {
  static_cast<UpsamplePadCropMessage *>(to)->MergeFrom(
      static_cast<const UpsamplePadCropMessage &>(from));
}


void UpsamplePadCropMessage::MergeFrom(const UpsamplePadCropMessage& from) {
// @@protoc_insertion_point(class_specific_merge_from_start:trt_extensions.UpsamplePadCropMessage)
  GOOGLE_DCHECK_NE(&from, this);
  uint32_t cached_has_bits = 0;
  (void) cached_has_bits;

  input_size_.MergeFrom(from.input_size_);
  output_size_.MergeFrom(from.output_size_);
  if (from._internal_padding() != 0) {
    _internal_set_padding(from._internal_padding());
  }
  if (from._internal_crop_size() != 0) {
    _internal_set_crop_size(from._internal_crop_size());
  }
  if (from._internal_dtype() != 0) {
    _internal_set_dtype(from._internal_dtype());
  }
  if (from._internal_format() != 0) {
    _internal_set_format(from._internal_format());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_input_scale = from._internal_input_scale();
  uint32_t raw_input_scale;
  memcpy(&raw_input_scale, &tmp_input_scale, sizeof(tmp_input_scale));
  if (raw_input_scale != 0) {
    _internal_set_input_scale(from._internal_input_scale());
  }
  static_assert(sizeof(uint32_t) == sizeof(float), "Code assumes uint32_t and float are the same size.");
  float tmp_output_scale = from._internal_output_scale();
  uint32_t raw_output_scale;
  memcpy(&raw_output_scale, &tmp_output_scale, sizeof(tmp_output_scale));
  if (raw_output_scale != 0) {
    _internal_set_output_scale(from._internal_output_scale());
  }
  _internal_metadata_.MergeFrom<::PROTOBUF_NAMESPACE_ID::UnknownFieldSet>(from._internal_metadata_);
}

void UpsamplePadCropMessage::CopyFrom(const UpsamplePadCropMessage& from) {
// @@protoc_insertion_point(class_specific_copy_from_start:trt_extensions.UpsamplePadCropMessage)
  if (&from == this) return;
  Clear();
  MergeFrom(from);
}

bool UpsamplePadCropMessage::IsInitialized() const {
  return true;
}

void UpsamplePadCropMessage::InternalSwap(UpsamplePadCropMessage* other) {
  using std::swap;
  _internal_metadata_.InternalSwap(&other->_internal_metadata_);
  input_size_.InternalSwap(&other->input_size_);
  output_size_.InternalSwap(&other->output_size_);
  ::PROTOBUF_NAMESPACE_ID::internal::memswap<
      PROTOBUF_FIELD_OFFSET(UpsamplePadCropMessage, output_scale_)
      + sizeof(UpsamplePadCropMessage::output_scale_)
      - PROTOBUF_FIELD_OFFSET(UpsamplePadCropMessage, padding_)>(
          reinterpret_cast<char*>(&padding_),
          reinterpret_cast<char*>(&other->padding_));
}

::PROTOBUF_NAMESPACE_ID::Metadata UpsamplePadCropMessage::GetMetadata() const {
  return ::PROTOBUF_NAMESPACE_ID::internal::AssignDescriptors(
      &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto_getter, &descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto_once,
      file_level_metadata_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fpad_5fcrop_2eproto[0]);
}

// @@protoc_insertion_point(namespace_scope)
}  // namespace trt_extensions
PROTOBUF_NAMESPACE_OPEN
template<> PROTOBUF_NOINLINE ::trt_extensions::UpsamplePadCropMessage* Arena::CreateMaybeMessage< ::trt_extensions::UpsamplePadCropMessage >(Arena* arena) {
  return Arena::CreateMessageInternal< ::trt_extensions::UpsamplePadCropMessage >(arena);
}
PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)
#include <google/protobuf/port_undef.inc>

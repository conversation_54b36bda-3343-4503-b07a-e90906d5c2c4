// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/trt_extensions/native/upsample_threshold.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/unknown_field_set.h>
#include "deeplearning/trt_extensions/native/common.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto;
namespace trt_extensions {
class UpsampleThresholdMessage;
struct UpsampleThresholdMessageDefaultTypeInternal;
extern UpsampleThresholdMessageDefaultTypeInternal _UpsampleThresholdMessage_default_instance_;
}  // namespace trt_extensions
PROTOBUF_NAMESPACE_OPEN
template<> ::trt_extensions::UpsampleThresholdMessage* Arena::CreateMaybeMessage<::trt_extensions::UpsampleThresholdMessage>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace trt_extensions {

// ===================================================================

class UpsampleThresholdMessage final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:trt_extensions.UpsampleThresholdMessage) */ {
 public:
  inline UpsampleThresholdMessage() : UpsampleThresholdMessage(nullptr) {}
  ~UpsampleThresholdMessage() override;
  explicit constexpr UpsampleThresholdMessage(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UpsampleThresholdMessage(const UpsampleThresholdMessage& from);
  UpsampleThresholdMessage(UpsampleThresholdMessage&& from) noexcept
    : UpsampleThresholdMessage() {
    *this = ::std::move(from);
  }

  inline UpsampleThresholdMessage& operator=(const UpsampleThresholdMessage& from) {
    CopyFrom(from);
    return *this;
  }
  inline UpsampleThresholdMessage& operator=(UpsampleThresholdMessage&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UpsampleThresholdMessage& default_instance() {
    return *internal_default_instance();
  }
  static inline const UpsampleThresholdMessage* internal_default_instance() {
    return reinterpret_cast<const UpsampleThresholdMessage*>(
               &_UpsampleThresholdMessage_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(UpsampleThresholdMessage& a, UpsampleThresholdMessage& b) {
    a.Swap(&b);
  }
  inline void Swap(UpsampleThresholdMessage* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UpsampleThresholdMessage* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UpsampleThresholdMessage* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UpsampleThresholdMessage>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UpsampleThresholdMessage& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UpsampleThresholdMessage& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UpsampleThresholdMessage* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "trt_extensions.UpsampleThresholdMessage";
  }
  protected:
  explicit UpsampleThresholdMessage(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kInputSizeFieldNumber = 6,
    kOutputSizeFieldNumber = 7,
    kCropWidthFieldNumber = 1,
    kCropHeightFieldNumber = 2,
    kMinThresholdFieldNumber = 3,
    kDtypeFieldNumber = 4,
    kFormatFieldNumber = 5,
    kInputScaleFieldNumber = 8,
    kOutputScaleFieldNumber = 9,
  };
  // repeated int64 input_size = 6;
  int input_size_size() const;
  private:
  int _internal_input_size_size() const;
  public:
  void clear_input_size();
  private:
  int64_t _internal_input_size(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_input_size() const;
  void _internal_add_input_size(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_input_size();
  public:
  int64_t input_size(int index) const;
  void set_input_size(int index, int64_t value);
  void add_input_size(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      input_size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_input_size();

  // repeated int64 output_size = 7;
  int output_size_size() const;
  private:
  int _internal_output_size_size() const;
  public:
  void clear_output_size();
  private:
  int64_t _internal_output_size(int index) const;
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      _internal_output_size() const;
  void _internal_add_output_size(int64_t value);
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      _internal_mutable_output_size();
  public:
  int64_t output_size(int index) const;
  void set_output_size(int index, int64_t value);
  void add_output_size(int64_t value);
  const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
      output_size() const;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
      mutable_output_size();

  // int64 crop_width = 1;
  void clear_crop_width();
  int64_t crop_width() const;
  void set_crop_width(int64_t value);
  private:
  int64_t _internal_crop_width() const;
  void _internal_set_crop_width(int64_t value);
  public:

  // int64 crop_height = 2;
  void clear_crop_height();
  int64_t crop_height() const;
  void set_crop_height(int64_t value);
  private:
  int64_t _internal_crop_height() const;
  void _internal_set_crop_height(int64_t value);
  public:

  // float min_threshold = 3;
  void clear_min_threshold();
  float min_threshold() const;
  void set_min_threshold(float value);
  private:
  float _internal_min_threshold() const;
  void _internal_set_min_threshold(float value);
  public:

  // .trt_extensions.DataTypeMessage dtype = 4;
  void clear_dtype();
  ::trt_extensions::DataTypeMessage dtype() const;
  void set_dtype(::trt_extensions::DataTypeMessage value);
  private:
  ::trt_extensions::DataTypeMessage _internal_dtype() const;
  void _internal_set_dtype(::trt_extensions::DataTypeMessage value);
  public:

  // .trt_extensions.DataFormatMessage format = 5;
  void clear_format();
  ::trt_extensions::DataFormatMessage format() const;
  void set_format(::trt_extensions::DataFormatMessage value);
  private:
  ::trt_extensions::DataFormatMessage _internal_format() const;
  void _internal_set_format(::trt_extensions::DataFormatMessage value);
  public:

  // float input_scale = 8;
  void clear_input_scale();
  float input_scale() const;
  void set_input_scale(float value);
  private:
  float _internal_input_scale() const;
  void _internal_set_input_scale(float value);
  public:

  // float output_scale = 9;
  void clear_output_scale();
  float output_scale() const;
  void set_output_scale(float value);
  private:
  float _internal_output_scale() const;
  void _internal_set_output_scale(float value);
  public:

  // @@protoc_insertion_point(class_scope:trt_extensions.UpsampleThresholdMessage)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > input_size_;
  mutable std::atomic<int> _input_size_cached_byte_size_;
  ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t > output_size_;
  mutable std::atomic<int> _output_size_cached_byte_size_;
  int64_t crop_width_;
  int64_t crop_height_;
  float min_threshold_;
  int dtype_;
  int format_;
  float input_scale_;
  float output_scale_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// UpsampleThresholdMessage

// int64 crop_width = 1;
inline void UpsampleThresholdMessage::clear_crop_width() {
  crop_width_ = int64_t{0};
}
inline int64_t UpsampleThresholdMessage::_internal_crop_width() const {
  return crop_width_;
}
inline int64_t UpsampleThresholdMessage::crop_width() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.crop_width)
  return _internal_crop_width();
}
inline void UpsampleThresholdMessage::_internal_set_crop_width(int64_t value) {
  
  crop_width_ = value;
}
inline void UpsampleThresholdMessage::set_crop_width(int64_t value) {
  _internal_set_crop_width(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.crop_width)
}

// int64 crop_height = 2;
inline void UpsampleThresholdMessage::clear_crop_height() {
  crop_height_ = int64_t{0};
}
inline int64_t UpsampleThresholdMessage::_internal_crop_height() const {
  return crop_height_;
}
inline int64_t UpsampleThresholdMessage::crop_height() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.crop_height)
  return _internal_crop_height();
}
inline void UpsampleThresholdMessage::_internal_set_crop_height(int64_t value) {
  
  crop_height_ = value;
}
inline void UpsampleThresholdMessage::set_crop_height(int64_t value) {
  _internal_set_crop_height(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.crop_height)
}

// float min_threshold = 3;
inline void UpsampleThresholdMessage::clear_min_threshold() {
  min_threshold_ = 0;
}
inline float UpsampleThresholdMessage::_internal_min_threshold() const {
  return min_threshold_;
}
inline float UpsampleThresholdMessage::min_threshold() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.min_threshold)
  return _internal_min_threshold();
}
inline void UpsampleThresholdMessage::_internal_set_min_threshold(float value) {
  
  min_threshold_ = value;
}
inline void UpsampleThresholdMessage::set_min_threshold(float value) {
  _internal_set_min_threshold(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.min_threshold)
}

// .trt_extensions.DataTypeMessage dtype = 4;
inline void UpsampleThresholdMessage::clear_dtype() {
  dtype_ = 0;
}
inline ::trt_extensions::DataTypeMessage UpsampleThresholdMessage::_internal_dtype() const {
  return static_cast< ::trt_extensions::DataTypeMessage >(dtype_);
}
inline ::trt_extensions::DataTypeMessage UpsampleThresholdMessage::dtype() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.dtype)
  return _internal_dtype();
}
inline void UpsampleThresholdMessage::_internal_set_dtype(::trt_extensions::DataTypeMessage value) {
  
  dtype_ = value;
}
inline void UpsampleThresholdMessage::set_dtype(::trt_extensions::DataTypeMessage value) {
  _internal_set_dtype(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.dtype)
}

// .trt_extensions.DataFormatMessage format = 5;
inline void UpsampleThresholdMessage::clear_format() {
  format_ = 0;
}
inline ::trt_extensions::DataFormatMessage UpsampleThresholdMessage::_internal_format() const {
  return static_cast< ::trt_extensions::DataFormatMessage >(format_);
}
inline ::trt_extensions::DataFormatMessage UpsampleThresholdMessage::format() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.format)
  return _internal_format();
}
inline void UpsampleThresholdMessage::_internal_set_format(::trt_extensions::DataFormatMessage value) {
  
  format_ = value;
}
inline void UpsampleThresholdMessage::set_format(::trt_extensions::DataFormatMessage value) {
  _internal_set_format(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.format)
}

// repeated int64 input_size = 6;
inline int UpsampleThresholdMessage::_internal_input_size_size() const {
  return input_size_.size();
}
inline int UpsampleThresholdMessage::input_size_size() const {
  return _internal_input_size_size();
}
inline void UpsampleThresholdMessage::clear_input_size() {
  input_size_.Clear();
}
inline int64_t UpsampleThresholdMessage::_internal_input_size(int index) const {
  return input_size_.Get(index);
}
inline int64_t UpsampleThresholdMessage::input_size(int index) const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.input_size)
  return _internal_input_size(index);
}
inline void UpsampleThresholdMessage::set_input_size(int index, int64_t value) {
  input_size_.Set(index, value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.input_size)
}
inline void UpsampleThresholdMessage::_internal_add_input_size(int64_t value) {
  input_size_.Add(value);
}
inline void UpsampleThresholdMessage::add_input_size(int64_t value) {
  _internal_add_input_size(value);
  // @@protoc_insertion_point(field_add:trt_extensions.UpsampleThresholdMessage.input_size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
UpsampleThresholdMessage::_internal_input_size() const {
  return input_size_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
UpsampleThresholdMessage::input_size() const {
  // @@protoc_insertion_point(field_list:trt_extensions.UpsampleThresholdMessage.input_size)
  return _internal_input_size();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
UpsampleThresholdMessage::_internal_mutable_input_size() {
  return &input_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
UpsampleThresholdMessage::mutable_input_size() {
  // @@protoc_insertion_point(field_mutable_list:trt_extensions.UpsampleThresholdMessage.input_size)
  return _internal_mutable_input_size();
}

// repeated int64 output_size = 7;
inline int UpsampleThresholdMessage::_internal_output_size_size() const {
  return output_size_.size();
}
inline int UpsampleThresholdMessage::output_size_size() const {
  return _internal_output_size_size();
}
inline void UpsampleThresholdMessage::clear_output_size() {
  output_size_.Clear();
}
inline int64_t UpsampleThresholdMessage::_internal_output_size(int index) const {
  return output_size_.Get(index);
}
inline int64_t UpsampleThresholdMessage::output_size(int index) const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.output_size)
  return _internal_output_size(index);
}
inline void UpsampleThresholdMessage::set_output_size(int index, int64_t value) {
  output_size_.Set(index, value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.output_size)
}
inline void UpsampleThresholdMessage::_internal_add_output_size(int64_t value) {
  output_size_.Add(value);
}
inline void UpsampleThresholdMessage::add_output_size(int64_t value) {
  _internal_add_output_size(value);
  // @@protoc_insertion_point(field_add:trt_extensions.UpsampleThresholdMessage.output_size)
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
UpsampleThresholdMessage::_internal_output_size() const {
  return output_size_;
}
inline const ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >&
UpsampleThresholdMessage::output_size() const {
  // @@protoc_insertion_point(field_list:trt_extensions.UpsampleThresholdMessage.output_size)
  return _internal_output_size();
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
UpsampleThresholdMessage::_internal_mutable_output_size() {
  return &output_size_;
}
inline ::PROTOBUF_NAMESPACE_ID::RepeatedField< int64_t >*
UpsampleThresholdMessage::mutable_output_size() {
  // @@protoc_insertion_point(field_mutable_list:trt_extensions.UpsampleThresholdMessage.output_size)
  return _internal_mutable_output_size();
}

// float input_scale = 8;
inline void UpsampleThresholdMessage::clear_input_scale() {
  input_scale_ = 0;
}
inline float UpsampleThresholdMessage::_internal_input_scale() const {
  return input_scale_;
}
inline float UpsampleThresholdMessage::input_scale() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.input_scale)
  return _internal_input_scale();
}
inline void UpsampleThresholdMessage::_internal_set_input_scale(float value) {
  
  input_scale_ = value;
}
inline void UpsampleThresholdMessage::set_input_scale(float value) {
  _internal_set_input_scale(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.input_scale)
}

// float output_scale = 9;
inline void UpsampleThresholdMessage::clear_output_scale() {
  output_scale_ = 0;
}
inline float UpsampleThresholdMessage::_internal_output_scale() const {
  return output_scale_;
}
inline float UpsampleThresholdMessage::output_scale() const {
  // @@protoc_insertion_point(field_get:trt_extensions.UpsampleThresholdMessage.output_scale)
  return _internal_output_scale();
}
inline void UpsampleThresholdMessage::_internal_set_output_scale(float value) {
  
  output_scale_ = value;
}
inline void UpsampleThresholdMessage::set_output_scale(float value) {
  _internal_set_output_scale(value);
  // @@protoc_insertion_point(field_set:trt_extensions.UpsampleThresholdMessage.output_scale)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace trt_extensions

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fupsample_5fthreshold_2eproto

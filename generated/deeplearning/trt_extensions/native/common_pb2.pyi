"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from google.protobuf.descriptor import (
    EnumDescriptor as google___protobuf___descriptor___EnumDescriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.enum_type_wrapper import (
    _EnumTypeWrapper as google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from typing import (
    NewType as typing___NewType,
    cast as typing___cast,
)


builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

DataTypeMessageValue = typing___NewType('DataTypeMessageValue', builtin___int)
type___DataTypeMessageValue = DataTypeMessageValue
DataTypeMessage: _DataTypeMessage
class _DataTypeMessage(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[DataTypeMessageValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    kFloat = typing___cast(DataTypeMessageValue, 0)
    kHalf = typing___cast(DataTypeMessageValue, 1)
    kInt8 = typing___cast(DataTypeMessageValue, 2)
    kInt32 = typing___cast(DataTypeMessageValue, 3)
kFloat = typing___cast(DataTypeMessageValue, 0)
kHalf = typing___cast(DataTypeMessageValue, 1)
kInt8 = typing___cast(DataTypeMessageValue, 2)
kInt32 = typing___cast(DataTypeMessageValue, 3)

DataFormatMessageValue = typing___NewType('DataFormatMessageValue', builtin___int)
type___DataFormatMessageValue = DataFormatMessageValue
DataFormatMessage: _DataFormatMessage
class _DataFormatMessage(google___protobuf___internal___enum_type_wrapper____EnumTypeWrapper[DataFormatMessageValue]):
    DESCRIPTOR: google___protobuf___descriptor___EnumDescriptor = ...
    kLINEAR = typing___cast(DataFormatMessageValue, 0)
    kCHW2 = typing___cast(DataFormatMessageValue, 1)
    kHWC8 = typing___cast(DataFormatMessageValue, 2)
    kCHW4 = typing___cast(DataFormatMessageValue, 3)
    kCHW16 = typing___cast(DataFormatMessageValue, 4)
    kCHW32 = typing___cast(DataFormatMessageValue, 5)
kLINEAR = typing___cast(DataFormatMessageValue, 0)
kCHW2 = typing___cast(DataFormatMessageValue, 1)
kHWC8 = typing___cast(DataFormatMessageValue, 2)
kCHW4 = typing___cast(DataFormatMessageValue, 3)
kCHW16 = typing___cast(DataFormatMessageValue, 4)
kCHW32 = typing___cast(DataFormatMessageValue, 5)

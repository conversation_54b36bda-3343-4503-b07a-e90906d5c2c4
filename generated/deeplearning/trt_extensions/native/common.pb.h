// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: deeplearning/trt_extensions/native/common.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/generated_enum_reflection.h>
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[1]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto;
PROTOBUF_NAMESPACE_OPEN
PROTOBUF_NAMESPACE_CLOSE
namespace trt_extensions {

enum DataTypeMessage : int {
  kFloat = 0,
  kHalf = 1,
  kInt8 = 2,
  kInt32 = 3,
  DataTypeMessage_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DataTypeMessage_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DataTypeMessage_IsValid(int value);
constexpr DataTypeMessage DataTypeMessage_MIN = kFloat;
constexpr DataTypeMessage DataTypeMessage_MAX = kInt32;
constexpr int DataTypeMessage_ARRAYSIZE = DataTypeMessage_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataTypeMessage_descriptor();
template<typename T>
inline const std::string& DataTypeMessage_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataTypeMessage>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataTypeMessage_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataTypeMessage_descriptor(), enum_t_value);
}
inline bool DataTypeMessage_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DataTypeMessage* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataTypeMessage>(
    DataTypeMessage_descriptor(), name, value);
}
enum DataFormatMessage : int {
  kLINEAR = 0,
  kCHW2 = 1,
  kHWC8 = 2,
  kCHW4 = 3,
  kCHW16 = 4,
  kCHW32 = 5,
  DataFormatMessage_INT_MIN_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::min(),
  DataFormatMessage_INT_MAX_SENTINEL_DO_NOT_USE_ = std::numeric_limits<int32_t>::max()
};
bool DataFormatMessage_IsValid(int value);
constexpr DataFormatMessage DataFormatMessage_MIN = kLINEAR;
constexpr DataFormatMessage DataFormatMessage_MAX = kCHW32;
constexpr int DataFormatMessage_ARRAYSIZE = DataFormatMessage_MAX + 1;

const ::PROTOBUF_NAMESPACE_ID::EnumDescriptor* DataFormatMessage_descriptor();
template<typename T>
inline const std::string& DataFormatMessage_Name(T enum_t_value) {
  static_assert(::std::is_same<T, DataFormatMessage>::value ||
    ::std::is_integral<T>::value,
    "Incorrect type passed to function DataFormatMessage_Name.");
  return ::PROTOBUF_NAMESPACE_ID::internal::NameOfEnum(
    DataFormatMessage_descriptor(), enum_t_value);
}
inline bool DataFormatMessage_Parse(
    ::PROTOBUF_NAMESPACE_ID::ConstStringParam name, DataFormatMessage* value) {
  return ::PROTOBUF_NAMESPACE_ID::internal::ParseNamedEnum<DataFormatMessage>(
    DataFormatMessage_descriptor(), name, value);
}
// ===================================================================


// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__

// @@protoc_insertion_point(namespace_scope)

}  // namespace trt_extensions

PROTOBUF_NAMESPACE_OPEN

template <> struct is_proto_enum< ::trt_extensions::DataTypeMessage> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::trt_extensions::DataTypeMessage>() {
  return ::trt_extensions::DataTypeMessage_descriptor();
}
template <> struct is_proto_enum< ::trt_extensions::DataFormatMessage> : ::std::true_type {};
template <>
inline const EnumDescriptor* GetEnumDescriptor< ::trt_extensions::DataFormatMessage>() {
  return ::trt_extensions::DataFormatMessage_descriptor();
}

PROTOBUF_NAMESPACE_CLOSE

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_deeplearning_2ftrt_5fextensions_2fnative_2fcommon_2eproto

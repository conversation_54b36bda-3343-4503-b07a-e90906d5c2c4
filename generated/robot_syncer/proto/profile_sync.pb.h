// Generated by the protocol buffer compiler.  DO NOT EDIT!
// source: robot_syncer/proto/profile_sync.proto

#ifndef GOOGLE_PROTOBUF_INCLUDED_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto
#define GOOGLE_PROTOBUF_INCLUDED_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto

#include <limits>
#include <string>

#include <google/protobuf/port_def.inc>
#if PROTOBUF_VERSION < 3019000
#error This file was generated by a newer version of protoc which is
#error incompatible with your Protocol Buffer headers. Please update
#error your headers.
#endif
#if 3019001 < PROTOBUF_MIN_PROTOC_VERSION
#error This file was generated by an older version of protoc which is
#error incompatible with your Protocol Buffer headers. Please
#error regenerate this file with a newer version of protoc.
#endif

#include <google/protobuf/port_undef.inc>
#include <google/protobuf/io/coded_stream.h>
#include <google/protobuf/arena.h>
#include <google/protobuf/arenastring.h>
#include <google/protobuf/generated_message_table_driven.h>
#include <google/protobuf/generated_message_util.h>
#include <google/protobuf/metadata_lite.h>
#include <google/protobuf/generated_message_reflection.h>
#include <google/protobuf/message.h>
#include <google/protobuf/repeated_field.h>  // IWYU pragma: export
#include <google/protobuf/extension_set.h>  // IWYU pragma: export
#include <google/protobuf/map.h>  // IWYU pragma: export
#include <google/protobuf/map_entry.h>
#include <google/protobuf/map_field_inl.h>
#include <google/protobuf/unknown_field_set.h>
#include "portal/proto/util.pb.h"
#include "frontend/proto/profile_sync.pb.h"
#include "proto/almanac/almanac.pb.h"
#include "proto/thinning/thinning.pb.h"
#include "frontend/proto/banding.pb.h"
#include "proto/target_velocity_estimator/target_velocity_estimator.pb.h"
#include "category/proto/category.pb.h"
// @@protoc_insertion_point(includes)
#include <google/protobuf/port_def.inc>
#define PROTOBUF_INTERNAL_EXPORT_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto
PROTOBUF_NAMESPACE_OPEN
namespace internal {
class AnyMetadata;
}  // namespace internal
PROTOBUF_NAMESPACE_CLOSE

// Internal implementation detail -- do not use these members.
struct TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto {
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTableField entries[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::AuxiliaryParseTableField aux[]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::ParseTable schema[8]
    PROTOBUF_SECTION_VARIABLE(protodesc_cold);
  static const ::PROTOBUF_NAMESPACE_ID::internal::FieldMetadata field_metadata[];
  static const ::PROTOBUF_NAMESPACE_ID::internal::SerializationTable serialization_table[];
  static const uint32_t offsets[];
};
extern const ::PROTOBUF_NAMESPACE_ID::internal::DescriptorTable descriptor_table_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
namespace carbon {
namespace robot_syncer {
namespace profile_sync {
class DeleteProfileRequest;
struct DeleteProfileRequestDefaultTypeInternal;
extern DeleteProfileRequestDefaultTypeInternal _DeleteProfileRequest_default_instance_;
class GetProfileRequest;
struct GetProfileRequestDefaultTypeInternal;
extern GetProfileRequestDefaultTypeInternal _GetProfileRequest_default_instance_;
class GetProfileResponse;
struct GetProfileResponseDefaultTypeInternal;
extern GetProfileResponseDefaultTypeInternal _GetProfileResponse_default_instance_;
class GetProfileSyncDataRequest;
struct GetProfileSyncDataRequestDefaultTypeInternal;
extern GetProfileSyncDataRequestDefaultTypeInternal _GetProfileSyncDataRequest_default_instance_;
class GetProfileSyncDataResponse;
struct GetProfileSyncDataResponseDefaultTypeInternal;
extern GetProfileSyncDataResponseDefaultTypeInternal _GetProfileSyncDataResponse_default_instance_;
class GetProfileSyncDataResponse_ProfilesEntry_DoNotUse;
struct GetProfileSyncDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal;
extern GetProfileSyncDataResponse_ProfilesEntry_DoNotUseDefaultTypeInternal _GetProfileSyncDataResponse_ProfilesEntry_DoNotUse_default_instance_;
class PurgeProfileRequest;
struct PurgeProfileRequestDefaultTypeInternal;
extern PurgeProfileRequestDefaultTypeInternal _PurgeProfileRequest_default_instance_;
class UploadProfileRequest;
struct UploadProfileRequestDefaultTypeInternal;
extern UploadProfileRequestDefaultTypeInternal _UploadProfileRequest_default_instance_;
}  // namespace profile_sync
}  // namespace robot_syncer
}  // namespace carbon
PROTOBUF_NAMESPACE_OPEN
template<> ::carbon::robot_syncer::profile_sync::DeleteProfileRequest* Arena::CreateMaybeMessage<::carbon::robot_syncer::profile_sync::DeleteProfileRequest>(Arena*);
template<> ::carbon::robot_syncer::profile_sync::GetProfileRequest* Arena::CreateMaybeMessage<::carbon::robot_syncer::profile_sync::GetProfileRequest>(Arena*);
template<> ::carbon::robot_syncer::profile_sync::GetProfileResponse* Arena::CreateMaybeMessage<::carbon::robot_syncer::profile_sync::GetProfileResponse>(Arena*);
template<> ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest* Arena::CreateMaybeMessage<::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest>(Arena*);
template<> ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse* Arena::CreateMaybeMessage<::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse>(Arena*);
template<> ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse* Arena::CreateMaybeMessage<::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse_ProfilesEntry_DoNotUse>(Arena*);
template<> ::carbon::robot_syncer::profile_sync::PurgeProfileRequest* Arena::CreateMaybeMessage<::carbon::robot_syncer::profile_sync::PurgeProfileRequest>(Arena*);
template<> ::carbon::robot_syncer::profile_sync::UploadProfileRequest* Arena::CreateMaybeMessage<::carbon::robot_syncer::profile_sync::UploadProfileRequest>(Arena*);
PROTOBUF_NAMESPACE_CLOSE
namespace carbon {
namespace robot_syncer {
namespace profile_sync {

// ===================================================================

class GetProfileSyncDataRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest) */ {
 public:
  inline GetProfileSyncDataRequest() : GetProfileSyncDataRequest(nullptr) {}
  ~GetProfileSyncDataRequest() override;
  explicit constexpr GetProfileSyncDataRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetProfileSyncDataRequest(const GetProfileSyncDataRequest& from);
  GetProfileSyncDataRequest(GetProfileSyncDataRequest&& from) noexcept
    : GetProfileSyncDataRequest() {
    *this = ::std::move(from);
  }

  inline GetProfileSyncDataRequest& operator=(const GetProfileSyncDataRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetProfileSyncDataRequest& operator=(GetProfileSyncDataRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetProfileSyncDataRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetProfileSyncDataRequest* internal_default_instance() {
    return reinterpret_cast<const GetProfileSyncDataRequest*>(
               &_GetProfileSyncDataRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    0;

  friend void swap(GetProfileSyncDataRequest& a, GetProfileSyncDataRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetProfileSyncDataRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetProfileSyncDataRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetProfileSyncDataRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetProfileSyncDataRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetProfileSyncDataRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetProfileSyncDataRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetProfileSyncDataRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest";
  }
  protected:
  explicit GetProfileSyncDataRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotSerialFieldNumber = 1,
  };
  // string robot_serial = 1;
  void clear_robot_serial();
  const std::string& robot_serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_serial();
  PROTOBUF_NODISCARD std::string* release_robot_serial();
  void set_allocated_robot_serial(std::string* robot_serial);
  private:
  const std::string& _internal_robot_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_serial(const std::string& value);
  std::string* _internal_mutable_robot_serial();
  public:

  // @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_serial_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
};
// -------------------------------------------------------------------

class GetProfileSyncDataResponse_ProfilesEntry_DoNotUse : public ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetProfileSyncDataResponse_ProfilesEntry_DoNotUse, 
    std::string, ::carbon::frontend::profile_sync::ProfileSyncData,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> {
public:
  typedef ::PROTOBUF_NAMESPACE_ID::internal::MapEntry<GetProfileSyncDataResponse_ProfilesEntry_DoNotUse, 
    std::string, ::carbon::frontend::profile_sync::ProfileSyncData,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
    ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> SuperType;
  GetProfileSyncDataResponse_ProfilesEntry_DoNotUse();
  explicit constexpr GetProfileSyncDataResponse_ProfilesEntry_DoNotUse(
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);
  explicit GetProfileSyncDataResponse_ProfilesEntry_DoNotUse(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  void MergeFrom(const GetProfileSyncDataResponse_ProfilesEntry_DoNotUse& other);
  static const GetProfileSyncDataResponse_ProfilesEntry_DoNotUse* internal_default_instance() { return reinterpret_cast<const GetProfileSyncDataResponse_ProfilesEntry_DoNotUse*>(&_GetProfileSyncDataResponse_ProfilesEntry_DoNotUse_default_instance_); }
  static bool ValidateKey(std::string* s) {
    return ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::VerifyUtf8String(s->data(), static_cast<int>(s->size()), ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::PARSE, "carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry.key");
 }
  static bool ValidateValue(void*) { return true; }
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;
};

// -------------------------------------------------------------------

class GetProfileSyncDataResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse) */ {
 public:
  inline GetProfileSyncDataResponse() : GetProfileSyncDataResponse(nullptr) {}
  ~GetProfileSyncDataResponse() override;
  explicit constexpr GetProfileSyncDataResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetProfileSyncDataResponse(const GetProfileSyncDataResponse& from);
  GetProfileSyncDataResponse(GetProfileSyncDataResponse&& from) noexcept
    : GetProfileSyncDataResponse() {
    *this = ::std::move(from);
  }

  inline GetProfileSyncDataResponse& operator=(const GetProfileSyncDataResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetProfileSyncDataResponse& operator=(GetProfileSyncDataResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetProfileSyncDataResponse& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetProfileSyncDataResponse* internal_default_instance() {
    return reinterpret_cast<const GetProfileSyncDataResponse*>(
               &_GetProfileSyncDataResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    2;

  friend void swap(GetProfileSyncDataResponse& a, GetProfileSyncDataResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetProfileSyncDataResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetProfileSyncDataResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetProfileSyncDataResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetProfileSyncDataResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetProfileSyncDataResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetProfileSyncDataResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetProfileSyncDataResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse";
  }
  protected:
  explicit GetProfileSyncDataResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------


  // accessors -------------------------------------------------------

  enum : int {
    kProfilesFieldNumber = 1,
  };
  // map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
  int profiles_size() const;
  private:
  int _internal_profiles_size() const;
  public:
  void clear_profiles();
  private:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >&
      _internal_profiles() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >*
      _internal_mutable_profiles();
  public:
  const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >&
      profiles() const;
  ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >*
      mutable_profiles();

  // @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::MapField<
      GetProfileSyncDataResponse_ProfilesEntry_DoNotUse,
      std::string, ::carbon::frontend::profile_sync::ProfileSyncData,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_STRING,
      ::PROTOBUF_NAMESPACE_ID::internal::WireFormatLite::TYPE_MESSAGE> profiles_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
};
// -------------------------------------------------------------------

class UploadProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.robot_syncer.profile_sync.UploadProfileRequest) */ {
 public:
  inline UploadProfileRequest() : UploadProfileRequest(nullptr) {}
  ~UploadProfileRequest() override;
  explicit constexpr UploadProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  UploadProfileRequest(const UploadProfileRequest& from);
  UploadProfileRequest(UploadProfileRequest&& from) noexcept
    : UploadProfileRequest() {
    *this = ::std::move(from);
  }

  inline UploadProfileRequest& operator=(const UploadProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline UploadProfileRequest& operator=(UploadProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const UploadProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  enum ProfileCase {
    kAlmanac = 3,
    kDiscriminator = 4,
    kModelinator = 5,
    kBanding = 6,
    kThinning = 7,
    kTargetVelocityEstimator = 8,
    kCategoryCollection = 9,
    kCategory = 10,
    PROFILE_NOT_SET = 0,
  };

  static inline const UploadProfileRequest* internal_default_instance() {
    return reinterpret_cast<const UploadProfileRequest*>(
               &_UploadProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    3;

  friend void swap(UploadProfileRequest& a, UploadProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(UploadProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(UploadProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  UploadProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<UploadProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const UploadProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const UploadProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(UploadProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.robot_syncer.profile_sync.UploadProfileRequest";
  }
  protected:
  explicit UploadProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kRobotSerialFieldNumber = 2,
    kLastUpdateTimeMsFieldNumber = 1,
    kAlmanacFieldNumber = 3,
    kDiscriminatorFieldNumber = 4,
    kModelinatorFieldNumber = 5,
    kBandingFieldNumber = 6,
    kThinningFieldNumber = 7,
    kTargetVelocityEstimatorFieldNumber = 8,
    kCategoryCollectionFieldNumber = 9,
    kCategoryFieldNumber = 10,
  };
  // string robot_serial = 2;
  void clear_robot_serial();
  const std::string& robot_serial() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_robot_serial(ArgT0&& arg0, ArgT... args);
  std::string* mutable_robot_serial();
  PROTOBUF_NODISCARD std::string* release_robot_serial();
  void set_allocated_robot_serial(std::string* robot_serial);
  private:
  const std::string& _internal_robot_serial() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_robot_serial(const std::string& value);
  std::string* _internal_mutable_robot_serial();
  public:

  // int64 last_update_time_ms = 1;
  void clear_last_update_time_ms();
  int64_t last_update_time_ms() const;
  void set_last_update_time_ms(int64_t value);
  private:
  int64_t _internal_last_update_time_ms() const;
  void _internal_set_last_update_time_ms(int64_t value);
  public:

  // .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
  bool has_almanac() const;
  private:
  bool _internal_has_almanac() const;
  public:
  void clear_almanac();
  const ::carbon::aimbot::almanac::AlmanacConfig& almanac() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::AlmanacConfig* release_almanac();
  ::carbon::aimbot::almanac::AlmanacConfig* mutable_almanac();
  void set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac);
  private:
  const ::carbon::aimbot::almanac::AlmanacConfig& _internal_almanac() const;
  ::carbon::aimbot::almanac::AlmanacConfig* _internal_mutable_almanac();
  public:
  void unsafe_arena_set_allocated_almanac(
      ::carbon::aimbot::almanac::AlmanacConfig* almanac);
  ::carbon::aimbot::almanac::AlmanacConfig* unsafe_arena_release_almanac();

  // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
  bool has_discriminator() const;
  private:
  bool _internal_has_discriminator() const;
  public:
  void clear_discriminator();
  const ::carbon::aimbot::almanac::DiscriminatorConfig& discriminator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::DiscriminatorConfig* release_discriminator();
  ::carbon::aimbot::almanac::DiscriminatorConfig* mutable_discriminator();
  void set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator);
  private:
  const ::carbon::aimbot::almanac::DiscriminatorConfig& _internal_discriminator() const;
  ::carbon::aimbot::almanac::DiscriminatorConfig* _internal_mutable_discriminator();
  public:
  void unsafe_arena_set_allocated_discriminator(
      ::carbon::aimbot::almanac::DiscriminatorConfig* discriminator);
  ::carbon::aimbot::almanac::DiscriminatorConfig* unsafe_arena_release_discriminator();

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
  bool has_modelinator() const;
  private:
  bool _internal_has_modelinator() const;
  public:
  void clear_modelinator();
  const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_modelinator();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_modelinator();
  void set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_modelinator() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_modelinator();
  public:
  void unsafe_arena_set_allocated_modelinator(
      ::carbon::aimbot::almanac::ModelinatorConfig* modelinator);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_modelinator();

  // .carbon.frontend.banding.BandingDef banding = 6;
  bool has_banding() const;
  private:
  bool _internal_has_banding() const;
  public:
  void clear_banding();
  const ::carbon::frontend::banding::BandingDef& banding() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::BandingDef* release_banding();
  ::carbon::frontend::banding::BandingDef* mutable_banding();
  void set_allocated_banding(::carbon::frontend::banding::BandingDef* banding);
  private:
  const ::carbon::frontend::banding::BandingDef& _internal_banding() const;
  ::carbon::frontend::banding::BandingDef* _internal_mutable_banding();
  public:
  void unsafe_arena_set_allocated_banding(
      ::carbon::frontend::banding::BandingDef* banding);
  ::carbon::frontend::banding::BandingDef* unsafe_arena_release_banding();

  // .carbon.thinning.ConfigDefinition thinning = 7;
  bool has_thinning() const;
  private:
  bool _internal_has_thinning() const;
  public:
  void clear_thinning();
  const ::carbon::thinning::ConfigDefinition& thinning() const;
  PROTOBUF_NODISCARD ::carbon::thinning::ConfigDefinition* release_thinning();
  ::carbon::thinning::ConfigDefinition* mutable_thinning();
  void set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning);
  private:
  const ::carbon::thinning::ConfigDefinition& _internal_thinning() const;
  ::carbon::thinning::ConfigDefinition* _internal_mutable_thinning();
  public:
  void unsafe_arena_set_allocated_thinning(
      ::carbon::thinning::ConfigDefinition* thinning);
  ::carbon::thinning::ConfigDefinition* unsafe_arena_release_thinning();

  // .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 8;
  bool has_target_velocity_estimator() const;
  private:
  bool _internal_has_target_velocity_estimator() const;
  public:
  void clear_target_velocity_estimator();
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& target_velocity_estimator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVEProfile* release_target_velocity_estimator();
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* mutable_target_velocity_estimator();
  void set_allocated_target_velocity_estimator(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& _internal_target_velocity_estimator() const;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _internal_mutable_target_velocity_estimator();
  public:
  void unsafe_arena_set_allocated_target_velocity_estimator(
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator);
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* unsafe_arena_release_target_velocity_estimator();

  // .carbon.category.CategoryCollection categoryCollection = 9;
  bool has_categorycollection() const;
  private:
  bool _internal_has_categorycollection() const;
  public:
  void clear_categorycollection();
  const ::carbon::category::CategoryCollection& categorycollection() const;
  PROTOBUF_NODISCARD ::carbon::category::CategoryCollection* release_categorycollection();
  ::carbon::category::CategoryCollection* mutable_categorycollection();
  void set_allocated_categorycollection(::carbon::category::CategoryCollection* categorycollection);
  private:
  const ::carbon::category::CategoryCollection& _internal_categorycollection() const;
  ::carbon::category::CategoryCollection* _internal_mutable_categorycollection();
  public:
  void unsafe_arena_set_allocated_categorycollection(
      ::carbon::category::CategoryCollection* categorycollection);
  ::carbon::category::CategoryCollection* unsafe_arena_release_categorycollection();

  // .carbon.category.Category category = 10;
  bool has_category() const;
  private:
  bool _internal_has_category() const;
  public:
  void clear_category();
  const ::carbon::category::Category& category() const;
  PROTOBUF_NODISCARD ::carbon::category::Category* release_category();
  ::carbon::category::Category* mutable_category();
  void set_allocated_category(::carbon::category::Category* category);
  private:
  const ::carbon::category::Category& _internal_category() const;
  ::carbon::category::Category* _internal_mutable_category();
  public:
  void unsafe_arena_set_allocated_category(
      ::carbon::category::Category* category);
  ::carbon::category::Category* unsafe_arena_release_category();

  void clear_profile();
  ProfileCase profile_case() const;
  // @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.UploadProfileRequest)
 private:
  class _Internal;
  void set_has_almanac();
  void set_has_discriminator();
  void set_has_modelinator();
  void set_has_banding();
  void set_has_thinning();
  void set_has_target_velocity_estimator();
  void set_has_categorycollection();
  void set_has_category();

  inline bool has_profile() const;
  inline void clear_has_profile();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr robot_serial_;
  int64_t last_update_time_ms_;
  union ProfileUnion {
    constexpr ProfileUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::aimbot::almanac::AlmanacConfig* almanac_;
    ::carbon::aimbot::almanac::DiscriminatorConfig* discriminator_;
    ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_;
    ::carbon::frontend::banding::BandingDef* banding_;
    ::carbon::thinning::ConfigDefinition* thinning_;
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator_;
    ::carbon::category::CategoryCollection* categorycollection_;
    ::carbon::category::Category* category_;
  } profile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
};
// -------------------------------------------------------------------

class GetProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.robot_syncer.profile_sync.GetProfileRequest) */ {
 public:
  inline GetProfileRequest() : GetProfileRequest(nullptr) {}
  ~GetProfileRequest() override;
  explicit constexpr GetProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetProfileRequest(const GetProfileRequest& from);
  GetProfileRequest(GetProfileRequest&& from) noexcept
    : GetProfileRequest() {
    *this = ::std::move(from);
  }

  inline GetProfileRequest& operator=(const GetProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetProfileRequest& operator=(GetProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const GetProfileRequest* internal_default_instance() {
    return reinterpret_cast<const GetProfileRequest*>(
               &_GetProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    4;

  friend void swap(GetProfileRequest& a, GetProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(GetProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.robot_syncer.profile_sync.GetProfileRequest";
  }
  protected:
  explicit GetProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
};
// -------------------------------------------------------------------

class GetProfileResponse final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.robot_syncer.profile_sync.GetProfileResponse) */ {
 public:
  inline GetProfileResponse() : GetProfileResponse(nullptr) {}
  ~GetProfileResponse() override;
  explicit constexpr GetProfileResponse(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  GetProfileResponse(const GetProfileResponse& from);
  GetProfileResponse(GetProfileResponse&& from) noexcept
    : GetProfileResponse() {
    *this = ::std::move(from);
  }

  inline GetProfileResponse& operator=(const GetProfileResponse& from) {
    CopyFrom(from);
    return *this;
  }
  inline GetProfileResponse& operator=(GetProfileResponse&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const GetProfileResponse& default_instance() {
    return *internal_default_instance();
  }
  enum ProfileCase {
    kAlmanac = 1,
    kDiscriminator = 2,
    kModelinator = 3,
    kBanding = 4,
    kThinning = 5,
    kTargetVelocityEstimator = 6,
    kCategoryCollection = 7,
    kCategory = 8,
    PROFILE_NOT_SET = 0,
  };

  static inline const GetProfileResponse* internal_default_instance() {
    return reinterpret_cast<const GetProfileResponse*>(
               &_GetProfileResponse_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    5;

  friend void swap(GetProfileResponse& a, GetProfileResponse& b) {
    a.Swap(&b);
  }
  inline void Swap(GetProfileResponse* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(GetProfileResponse* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  GetProfileResponse* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<GetProfileResponse>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const GetProfileResponse& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const GetProfileResponse& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(GetProfileResponse* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.robot_syncer.profile_sync.GetProfileResponse";
  }
  protected:
  explicit GetProfileResponse(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kAlmanacFieldNumber = 1,
    kDiscriminatorFieldNumber = 2,
    kModelinatorFieldNumber = 3,
    kBandingFieldNumber = 4,
    kThinningFieldNumber = 5,
    kTargetVelocityEstimatorFieldNumber = 6,
    kCategoryCollectionFieldNumber = 7,
    kCategoryFieldNumber = 8,
  };
  // .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
  bool has_almanac() const;
  private:
  bool _internal_has_almanac() const;
  public:
  void clear_almanac();
  const ::carbon::aimbot::almanac::AlmanacConfig& almanac() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::AlmanacConfig* release_almanac();
  ::carbon::aimbot::almanac::AlmanacConfig* mutable_almanac();
  void set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac);
  private:
  const ::carbon::aimbot::almanac::AlmanacConfig& _internal_almanac() const;
  ::carbon::aimbot::almanac::AlmanacConfig* _internal_mutable_almanac();
  public:
  void unsafe_arena_set_allocated_almanac(
      ::carbon::aimbot::almanac::AlmanacConfig* almanac);
  ::carbon::aimbot::almanac::AlmanacConfig* unsafe_arena_release_almanac();

  // .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
  bool has_discriminator() const;
  private:
  bool _internal_has_discriminator() const;
  public:
  void clear_discriminator();
  const ::carbon::aimbot::almanac::DiscriminatorConfig& discriminator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::DiscriminatorConfig* release_discriminator();
  ::carbon::aimbot::almanac::DiscriminatorConfig* mutable_discriminator();
  void set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator);
  private:
  const ::carbon::aimbot::almanac::DiscriminatorConfig& _internal_discriminator() const;
  ::carbon::aimbot::almanac::DiscriminatorConfig* _internal_mutable_discriminator();
  public:
  void unsafe_arena_set_allocated_discriminator(
      ::carbon::aimbot::almanac::DiscriminatorConfig* discriminator);
  ::carbon::aimbot::almanac::DiscriminatorConfig* unsafe_arena_release_discriminator();

  // .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
  bool has_modelinator() const;
  private:
  bool _internal_has_modelinator() const;
  public:
  void clear_modelinator();
  const ::carbon::aimbot::almanac::ModelinatorConfig& modelinator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::almanac::ModelinatorConfig* release_modelinator();
  ::carbon::aimbot::almanac::ModelinatorConfig* mutable_modelinator();
  void set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator);
  private:
  const ::carbon::aimbot::almanac::ModelinatorConfig& _internal_modelinator() const;
  ::carbon::aimbot::almanac::ModelinatorConfig* _internal_mutable_modelinator();
  public:
  void unsafe_arena_set_allocated_modelinator(
      ::carbon::aimbot::almanac::ModelinatorConfig* modelinator);
  ::carbon::aimbot::almanac::ModelinatorConfig* unsafe_arena_release_modelinator();

  // .carbon.frontend.banding.BandingDef banding = 4;
  bool has_banding() const;
  private:
  bool _internal_has_banding() const;
  public:
  void clear_banding();
  const ::carbon::frontend::banding::BandingDef& banding() const;
  PROTOBUF_NODISCARD ::carbon::frontend::banding::BandingDef* release_banding();
  ::carbon::frontend::banding::BandingDef* mutable_banding();
  void set_allocated_banding(::carbon::frontend::banding::BandingDef* banding);
  private:
  const ::carbon::frontend::banding::BandingDef& _internal_banding() const;
  ::carbon::frontend::banding::BandingDef* _internal_mutable_banding();
  public:
  void unsafe_arena_set_allocated_banding(
      ::carbon::frontend::banding::BandingDef* banding);
  ::carbon::frontend::banding::BandingDef* unsafe_arena_release_banding();

  // .carbon.thinning.ConfigDefinition thinning = 5;
  bool has_thinning() const;
  private:
  bool _internal_has_thinning() const;
  public:
  void clear_thinning();
  const ::carbon::thinning::ConfigDefinition& thinning() const;
  PROTOBUF_NODISCARD ::carbon::thinning::ConfigDefinition* release_thinning();
  ::carbon::thinning::ConfigDefinition* mutable_thinning();
  void set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning);
  private:
  const ::carbon::thinning::ConfigDefinition& _internal_thinning() const;
  ::carbon::thinning::ConfigDefinition* _internal_mutable_thinning();
  public:
  void unsafe_arena_set_allocated_thinning(
      ::carbon::thinning::ConfigDefinition* thinning);
  ::carbon::thinning::ConfigDefinition* unsafe_arena_release_thinning();

  // .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 6;
  bool has_target_velocity_estimator() const;
  private:
  bool _internal_has_target_velocity_estimator() const;
  public:
  void clear_target_velocity_estimator();
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& target_velocity_estimator() const;
  PROTOBUF_NODISCARD ::carbon::aimbot::target_velocity_estimator::TVEProfile* release_target_velocity_estimator();
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* mutable_target_velocity_estimator();
  void set_allocated_target_velocity_estimator(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator);
  private:
  const ::carbon::aimbot::target_velocity_estimator::TVEProfile& _internal_target_velocity_estimator() const;
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _internal_mutable_target_velocity_estimator();
  public:
  void unsafe_arena_set_allocated_target_velocity_estimator(
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator);
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* unsafe_arena_release_target_velocity_estimator();

  // .carbon.category.CategoryCollection categoryCollection = 7;
  bool has_categorycollection() const;
  private:
  bool _internal_has_categorycollection() const;
  public:
  void clear_categorycollection();
  const ::carbon::category::CategoryCollection& categorycollection() const;
  PROTOBUF_NODISCARD ::carbon::category::CategoryCollection* release_categorycollection();
  ::carbon::category::CategoryCollection* mutable_categorycollection();
  void set_allocated_categorycollection(::carbon::category::CategoryCollection* categorycollection);
  private:
  const ::carbon::category::CategoryCollection& _internal_categorycollection() const;
  ::carbon::category::CategoryCollection* _internal_mutable_categorycollection();
  public:
  void unsafe_arena_set_allocated_categorycollection(
      ::carbon::category::CategoryCollection* categorycollection);
  ::carbon::category::CategoryCollection* unsafe_arena_release_categorycollection();

  // .carbon.category.Category category = 8;
  bool has_category() const;
  private:
  bool _internal_has_category() const;
  public:
  void clear_category();
  const ::carbon::category::Category& category() const;
  PROTOBUF_NODISCARD ::carbon::category::Category* release_category();
  ::carbon::category::Category* mutable_category();
  void set_allocated_category(::carbon::category::Category* category);
  private:
  const ::carbon::category::Category& _internal_category() const;
  ::carbon::category::Category* _internal_mutable_category();
  public:
  void unsafe_arena_set_allocated_category(
      ::carbon::category::Category* category);
  ::carbon::category::Category* unsafe_arena_release_category();

  void clear_profile();
  ProfileCase profile_case() const;
  // @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileResponse)
 private:
  class _Internal;
  void set_has_almanac();
  void set_has_discriminator();
  void set_has_modelinator();
  void set_has_banding();
  void set_has_thinning();
  void set_has_target_velocity_estimator();
  void set_has_categorycollection();
  void set_has_category();

  inline bool has_profile() const;
  inline void clear_has_profile();

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  union ProfileUnion {
    constexpr ProfileUnion() : _constinit_{} {}
      ::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized _constinit_;
    ::carbon::aimbot::almanac::AlmanacConfig* almanac_;
    ::carbon::aimbot::almanac::DiscriminatorConfig* discriminator_;
    ::carbon::aimbot::almanac::ModelinatorConfig* modelinator_;
    ::carbon::frontend::banding::BandingDef* banding_;
    ::carbon::thinning::ConfigDefinition* thinning_;
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator_;
    ::carbon::category::CategoryCollection* categorycollection_;
    ::carbon::category::Category* category_;
  } profile_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  uint32_t _oneof_case_[1];

  friend struct ::TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
};
// -------------------------------------------------------------------

class DeleteProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.robot_syncer.profile_sync.DeleteProfileRequest) */ {
 public:
  inline DeleteProfileRequest() : DeleteProfileRequest(nullptr) {}
  ~DeleteProfileRequest() override;
  explicit constexpr DeleteProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  DeleteProfileRequest(const DeleteProfileRequest& from);
  DeleteProfileRequest(DeleteProfileRequest&& from) noexcept
    : DeleteProfileRequest() {
    *this = ::std::move(from);
  }

  inline DeleteProfileRequest& operator=(const DeleteProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline DeleteProfileRequest& operator=(DeleteProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const DeleteProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const DeleteProfileRequest* internal_default_instance() {
    return reinterpret_cast<const DeleteProfileRequest*>(
               &_DeleteProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    6;

  friend void swap(DeleteProfileRequest& a, DeleteProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(DeleteProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(DeleteProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  DeleteProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<DeleteProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const DeleteProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const DeleteProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(DeleteProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.robot_syncer.profile_sync.DeleteProfileRequest";
  }
  protected:
  explicit DeleteProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
};
// -------------------------------------------------------------------

class PurgeProfileRequest final :
    public ::PROTOBUF_NAMESPACE_ID::Message /* @@protoc_insertion_point(class_definition:carbon.robot_syncer.profile_sync.PurgeProfileRequest) */ {
 public:
  inline PurgeProfileRequest() : PurgeProfileRequest(nullptr) {}
  ~PurgeProfileRequest() override;
  explicit constexpr PurgeProfileRequest(::PROTOBUF_NAMESPACE_ID::internal::ConstantInitialized);

  PurgeProfileRequest(const PurgeProfileRequest& from);
  PurgeProfileRequest(PurgeProfileRequest&& from) noexcept
    : PurgeProfileRequest() {
    *this = ::std::move(from);
  }

  inline PurgeProfileRequest& operator=(const PurgeProfileRequest& from) {
    CopyFrom(from);
    return *this;
  }
  inline PurgeProfileRequest& operator=(PurgeProfileRequest&& from) noexcept {
    if (this == &from) return *this;
    if (GetOwningArena() == from.GetOwningArena()
  #ifdef PROTOBUF_FORCE_COPY_IN_MOVE
        && GetOwningArena() != nullptr
  #endif  // !PROTOBUF_FORCE_COPY_IN_MOVE
    ) {
      InternalSwap(&from);
    } else {
      CopyFrom(from);
    }
    return *this;
  }

  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* descriptor() {
    return GetDescriptor();
  }
  static const ::PROTOBUF_NAMESPACE_ID::Descriptor* GetDescriptor() {
    return default_instance().GetMetadata().descriptor;
  }
  static const ::PROTOBUF_NAMESPACE_ID::Reflection* GetReflection() {
    return default_instance().GetMetadata().reflection;
  }
  static const PurgeProfileRequest& default_instance() {
    return *internal_default_instance();
  }
  static inline const PurgeProfileRequest* internal_default_instance() {
    return reinterpret_cast<const PurgeProfileRequest*>(
               &_PurgeProfileRequest_default_instance_);
  }
  static constexpr int kIndexInFileMessages =
    7;

  friend void swap(PurgeProfileRequest& a, PurgeProfileRequest& b) {
    a.Swap(&b);
  }
  inline void Swap(PurgeProfileRequest* other) {
    if (other == this) return;
  #ifdef PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() != nullptr &&
        GetOwningArena() == other->GetOwningArena()) {
   #else  // PROTOBUF_FORCE_COPY_IN_SWAP
    if (GetOwningArena() == other->GetOwningArena()) {
  #endif  // !PROTOBUF_FORCE_COPY_IN_SWAP
      InternalSwap(other);
    } else {
      ::PROTOBUF_NAMESPACE_ID::internal::GenericSwap(this, other);
    }
  }
  void UnsafeArenaSwap(PurgeProfileRequest* other) {
    if (other == this) return;
    GOOGLE_DCHECK(GetOwningArena() == other->GetOwningArena());
    InternalSwap(other);
  }

  // implements Message ----------------------------------------------

  PurgeProfileRequest* New(::PROTOBUF_NAMESPACE_ID::Arena* arena = nullptr) const final {
    return CreateMaybeMessage<PurgeProfileRequest>(arena);
  }
  using ::PROTOBUF_NAMESPACE_ID::Message::CopyFrom;
  void CopyFrom(const PurgeProfileRequest& from);
  using ::PROTOBUF_NAMESPACE_ID::Message::MergeFrom;
  void MergeFrom(const PurgeProfileRequest& from);
  private:
  static void MergeImpl(::PROTOBUF_NAMESPACE_ID::Message* to, const ::PROTOBUF_NAMESPACE_ID::Message& from);
  public:
  PROTOBUF_ATTRIBUTE_REINITIALIZES void Clear() final;
  bool IsInitialized() const final;

  size_t ByteSizeLong() const final;
  const char* _InternalParse(const char* ptr, ::PROTOBUF_NAMESPACE_ID::internal::ParseContext* ctx) final;
  uint8_t* _InternalSerialize(
      uint8_t* target, ::PROTOBUF_NAMESPACE_ID::io::EpsCopyOutputStream* stream) const final;
  int GetCachedSize() const final { return _cached_size_.Get(); }

  private:
  void SharedCtor();
  void SharedDtor();
  void SetCachedSize(int size) const final;
  void InternalSwap(PurgeProfileRequest* other);

  private:
  friend class ::PROTOBUF_NAMESPACE_ID::internal::AnyMetadata;
  static ::PROTOBUF_NAMESPACE_ID::StringPiece FullMessageName() {
    return "carbon.robot_syncer.profile_sync.PurgeProfileRequest";
  }
  protected:
  explicit PurgeProfileRequest(::PROTOBUF_NAMESPACE_ID::Arena* arena,
                       bool is_message_owned = false);
  private:
  static void ArenaDtor(void* object);
  inline void RegisterArenaDtor(::PROTOBUF_NAMESPACE_ID::Arena* arena);
  public:

  static const ClassData _class_data_;
  const ::PROTOBUF_NAMESPACE_ID::Message::ClassData*GetClassData() const final;

  ::PROTOBUF_NAMESPACE_ID::Metadata GetMetadata() const final;

  // nested types ----------------------------------------------------

  // accessors -------------------------------------------------------

  enum : int {
    kUuidFieldNumber = 1,
  };
  // string uuid = 1;
  void clear_uuid();
  const std::string& uuid() const;
  template <typename ArgT0 = const std::string&, typename... ArgT>
  void set_uuid(ArgT0&& arg0, ArgT... args);
  std::string* mutable_uuid();
  PROTOBUF_NODISCARD std::string* release_uuid();
  void set_allocated_uuid(std::string* uuid);
  private:
  const std::string& _internal_uuid() const;
  inline PROTOBUF_ALWAYS_INLINE void _internal_set_uuid(const std::string& value);
  std::string* _internal_mutable_uuid();
  public:

  // @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
 private:
  class _Internal;

  template <typename T> friend class ::PROTOBUF_NAMESPACE_ID::Arena::InternalHelper;
  typedef void InternalArenaConstructable_;
  typedef void DestructorSkippable_;
  ::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr uuid_;
  mutable ::PROTOBUF_NAMESPACE_ID::internal::CachedSize _cached_size_;
  friend struct ::TableStruct_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto;
};
// ===================================================================


// ===================================================================

#ifdef __GNUC__
  #pragma GCC diagnostic push
  #pragma GCC diagnostic ignored "-Wstrict-aliasing"
#endif  // __GNUC__
// GetProfileSyncDataRequest

// string robot_serial = 1;
inline void GetProfileSyncDataRequest::clear_robot_serial() {
  robot_serial_.ClearToEmpty();
}
inline const std::string& GetProfileSyncDataRequest::robot_serial() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest.robot_serial)
  return _internal_robot_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetProfileSyncDataRequest::set_robot_serial(ArgT0&& arg0, ArgT... args) {
 
 robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest.robot_serial)
}
inline std::string* GetProfileSyncDataRequest::mutable_robot_serial() {
  std::string* _s = _internal_mutable_robot_serial();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest.robot_serial)
  return _s;
}
inline const std::string& GetProfileSyncDataRequest::_internal_robot_serial() const {
  return robot_serial_.Get();
}
inline void GetProfileSyncDataRequest::_internal_set_robot_serial(const std::string& value) {
  
  robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetProfileSyncDataRequest::_internal_mutable_robot_serial() {
  
  return robot_serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetProfileSyncDataRequest::release_robot_serial() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest.robot_serial)
  return robot_serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetProfileSyncDataRequest::set_allocated_robot_serial(std::string* robot_serial) {
  if (robot_serial != nullptr) {
    
  } else {
    
  }
  robot_serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest.robot_serial)
}

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// GetProfileSyncDataResponse

// map<string, .carbon.frontend.profile_sync.ProfileSyncData> profiles = 1;
inline int GetProfileSyncDataResponse::_internal_profiles_size() const {
  return profiles_.size();
}
inline int GetProfileSyncDataResponse::profiles_size() const {
  return _internal_profiles_size();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >&
GetProfileSyncDataResponse::_internal_profiles() const {
  return profiles_.GetMap();
}
inline const ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >&
GetProfileSyncDataResponse::profiles() const {
  // @@protoc_insertion_point(field_map:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.profiles)
  return _internal_profiles();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >*
GetProfileSyncDataResponse::_internal_mutable_profiles() {
  return profiles_.MutableMap();
}
inline ::PROTOBUF_NAMESPACE_ID::Map< std::string, ::carbon::frontend::profile_sync::ProfileSyncData >*
GetProfileSyncDataResponse::mutable_profiles() {
  // @@protoc_insertion_point(field_mutable_map:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.profiles)
  return _internal_mutable_profiles();
}

// -------------------------------------------------------------------

// UploadProfileRequest

// int64 last_update_time_ms = 1;
inline void UploadProfileRequest::clear_last_update_time_ms() {
  last_update_time_ms_ = int64_t{0};
}
inline int64_t UploadProfileRequest::_internal_last_update_time_ms() const {
  return last_update_time_ms_;
}
inline int64_t UploadProfileRequest::last_update_time_ms() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.last_update_time_ms)
  return _internal_last_update_time_ms();
}
inline void UploadProfileRequest::_internal_set_last_update_time_ms(int64_t value) {
  
  last_update_time_ms_ = value;
}
inline void UploadProfileRequest::set_last_update_time_ms(int64_t value) {
  _internal_set_last_update_time_ms(value);
  // @@protoc_insertion_point(field_set:carbon.robot_syncer.profile_sync.UploadProfileRequest.last_update_time_ms)
}

// string robot_serial = 2;
inline void UploadProfileRequest::clear_robot_serial() {
  robot_serial_.ClearToEmpty();
}
inline const std::string& UploadProfileRequest::robot_serial() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.robot_serial)
  return _internal_robot_serial();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void UploadProfileRequest::set_robot_serial(ArgT0&& arg0, ArgT... args) {
 
 robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.robot_syncer.profile_sync.UploadProfileRequest.robot_serial)
}
inline std::string* UploadProfileRequest::mutable_robot_serial() {
  std::string* _s = _internal_mutable_robot_serial();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.robot_serial)
  return _s;
}
inline const std::string& UploadProfileRequest::_internal_robot_serial() const {
  return robot_serial_.Get();
}
inline void UploadProfileRequest::_internal_set_robot_serial(const std::string& value) {
  
  robot_serial_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* UploadProfileRequest::_internal_mutable_robot_serial() {
  
  return robot_serial_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* UploadProfileRequest::release_robot_serial() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.robot_serial)
  return robot_serial_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void UploadProfileRequest::set_allocated_robot_serial(std::string* robot_serial) {
  if (robot_serial != nullptr) {
    
  } else {
    
  }
  robot_serial_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), robot_serial,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (robot_serial_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    robot_serial_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.robot_serial)
}

// .carbon.aimbot.almanac.AlmanacConfig almanac = 3;
inline bool UploadProfileRequest::_internal_has_almanac() const {
  return profile_case() == kAlmanac;
}
inline bool UploadProfileRequest::has_almanac() const {
  return _internal_has_almanac();
}
inline void UploadProfileRequest::set_has_almanac() {
  _oneof_case_[0] = kAlmanac;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* UploadProfileRequest::release_almanac() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.almanac)
  if (_internal_has_almanac()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::AlmanacConfig* temp = profile_.almanac_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.almanac_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& UploadProfileRequest::_internal_almanac() const {
  return _internal_has_almanac()
      ? *profile_.almanac_
      : reinterpret_cast< ::carbon::aimbot::almanac::AlmanacConfig&>(::carbon::aimbot::almanac::_AlmanacConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& UploadProfileRequest::almanac() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.almanac)
  return _internal_almanac();
}
inline ::carbon::aimbot::almanac::AlmanacConfig* UploadProfileRequest::unsafe_arena_release_almanac() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.almanac)
  if (_internal_has_almanac()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::AlmanacConfig* temp = profile_.almanac_;
    profile_.almanac_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  clear_profile();
  if (almanac) {
    set_has_almanac();
    profile_.almanac_ = almanac;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.almanac)
}
inline ::carbon::aimbot::almanac::AlmanacConfig* UploadProfileRequest::_internal_mutable_almanac() {
  if (!_internal_has_almanac()) {
    clear_profile();
    set_has_almanac();
    profile_.almanac_ = CreateMaybeMessage< ::carbon::aimbot::almanac::AlmanacConfig >(GetArenaForAllocation());
  }
  return profile_.almanac_;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* UploadProfileRequest::mutable_almanac() {
  ::carbon::aimbot::almanac::AlmanacConfig* _msg = _internal_mutable_almanac();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.almanac)
  return _msg;
}

// .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 4;
inline bool UploadProfileRequest::_internal_has_discriminator() const {
  return profile_case() == kDiscriminator;
}
inline bool UploadProfileRequest::has_discriminator() const {
  return _internal_has_discriminator();
}
inline void UploadProfileRequest::set_has_discriminator() {
  _oneof_case_[0] = kDiscriminator;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* UploadProfileRequest::release_discriminator() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.discriminator)
  if (_internal_has_discriminator()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::DiscriminatorConfig* temp = profile_.discriminator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.discriminator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& UploadProfileRequest::_internal_discriminator() const {
  return _internal_has_discriminator()
      ? *profile_.discriminator_
      : reinterpret_cast< ::carbon::aimbot::almanac::DiscriminatorConfig&>(::carbon::aimbot::almanac::_DiscriminatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& UploadProfileRequest::discriminator() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.discriminator)
  return _internal_discriminator();
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* UploadProfileRequest::unsafe_arena_release_discriminator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.discriminator)
  if (_internal_has_discriminator()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::DiscriminatorConfig* temp = profile_.discriminator_;
    profile_.discriminator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator) {
  clear_profile();
  if (discriminator) {
    set_has_discriminator();
    profile_.discriminator_ = discriminator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.discriminator)
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* UploadProfileRequest::_internal_mutable_discriminator() {
  if (!_internal_has_discriminator()) {
    clear_profile();
    set_has_discriminator();
    profile_.discriminator_ = CreateMaybeMessage< ::carbon::aimbot::almanac::DiscriminatorConfig >(GetArenaForAllocation());
  }
  return profile_.discriminator_;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* UploadProfileRequest::mutable_discriminator() {
  ::carbon::aimbot::almanac::DiscriminatorConfig* _msg = _internal_mutable_discriminator();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.discriminator)
  return _msg;
}

// .carbon.aimbot.almanac.ModelinatorConfig modelinator = 5;
inline bool UploadProfileRequest::_internal_has_modelinator() const {
  return profile_case() == kModelinator;
}
inline bool UploadProfileRequest::has_modelinator() const {
  return _internal_has_modelinator();
}
inline void UploadProfileRequest::set_has_modelinator() {
  _oneof_case_[0] = kModelinator;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* UploadProfileRequest::release_modelinator() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.modelinator)
  if (_internal_has_modelinator()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::ModelinatorConfig* temp = profile_.modelinator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.modelinator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& UploadProfileRequest::_internal_modelinator() const {
  return _internal_has_modelinator()
      ? *profile_.modelinator_
      : reinterpret_cast< ::carbon::aimbot::almanac::ModelinatorConfig&>(::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& UploadProfileRequest::modelinator() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.modelinator)
  return _internal_modelinator();
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* UploadProfileRequest::unsafe_arena_release_modelinator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.modelinator)
  if (_internal_has_modelinator()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::ModelinatorConfig* temp = profile_.modelinator_;
    profile_.modelinator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator) {
  clear_profile();
  if (modelinator) {
    set_has_modelinator();
    profile_.modelinator_ = modelinator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.modelinator)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* UploadProfileRequest::_internal_mutable_modelinator() {
  if (!_internal_has_modelinator()) {
    clear_profile();
    set_has_modelinator();
    profile_.modelinator_ = CreateMaybeMessage< ::carbon::aimbot::almanac::ModelinatorConfig >(GetArenaForAllocation());
  }
  return profile_.modelinator_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* UploadProfileRequest::mutable_modelinator() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_modelinator();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.modelinator)
  return _msg;
}

// .carbon.frontend.banding.BandingDef banding = 6;
inline bool UploadProfileRequest::_internal_has_banding() const {
  return profile_case() == kBanding;
}
inline bool UploadProfileRequest::has_banding() const {
  return _internal_has_banding();
}
inline void UploadProfileRequest::set_has_banding() {
  _oneof_case_[0] = kBanding;
}
inline ::carbon::frontend::banding::BandingDef* UploadProfileRequest::release_banding() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.banding)
  if (_internal_has_banding()) {
    clear_has_profile();
      ::carbon::frontend::banding::BandingDef* temp = profile_.banding_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.banding_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::banding::BandingDef& UploadProfileRequest::_internal_banding() const {
  return _internal_has_banding()
      ? *profile_.banding_
      : reinterpret_cast< ::carbon::frontend::banding::BandingDef&>(::carbon::frontend::banding::_BandingDef_default_instance_);
}
inline const ::carbon::frontend::banding::BandingDef& UploadProfileRequest::banding() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.banding)
  return _internal_banding();
}
inline ::carbon::frontend::banding::BandingDef* UploadProfileRequest::unsafe_arena_release_banding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.banding)
  if (_internal_has_banding()) {
    clear_has_profile();
    ::carbon::frontend::banding::BandingDef* temp = profile_.banding_;
    profile_.banding_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_banding(::carbon::frontend::banding::BandingDef* banding) {
  clear_profile();
  if (banding) {
    set_has_banding();
    profile_.banding_ = banding;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.banding)
}
inline ::carbon::frontend::banding::BandingDef* UploadProfileRequest::_internal_mutable_banding() {
  if (!_internal_has_banding()) {
    clear_profile();
    set_has_banding();
    profile_.banding_ = CreateMaybeMessage< ::carbon::frontend::banding::BandingDef >(GetArenaForAllocation());
  }
  return profile_.banding_;
}
inline ::carbon::frontend::banding::BandingDef* UploadProfileRequest::mutable_banding() {
  ::carbon::frontend::banding::BandingDef* _msg = _internal_mutable_banding();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.banding)
  return _msg;
}

// .carbon.thinning.ConfigDefinition thinning = 7;
inline bool UploadProfileRequest::_internal_has_thinning() const {
  return profile_case() == kThinning;
}
inline bool UploadProfileRequest::has_thinning() const {
  return _internal_has_thinning();
}
inline void UploadProfileRequest::set_has_thinning() {
  _oneof_case_[0] = kThinning;
}
inline ::carbon::thinning::ConfigDefinition* UploadProfileRequest::release_thinning() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.thinning)
  if (_internal_has_thinning()) {
    clear_has_profile();
      ::carbon::thinning::ConfigDefinition* temp = profile_.thinning_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.thinning_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::thinning::ConfigDefinition& UploadProfileRequest::_internal_thinning() const {
  return _internal_has_thinning()
      ? *profile_.thinning_
      : reinterpret_cast< ::carbon::thinning::ConfigDefinition&>(::carbon::thinning::_ConfigDefinition_default_instance_);
}
inline const ::carbon::thinning::ConfigDefinition& UploadProfileRequest::thinning() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.thinning)
  return _internal_thinning();
}
inline ::carbon::thinning::ConfigDefinition* UploadProfileRequest::unsafe_arena_release_thinning() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.thinning)
  if (_internal_has_thinning()) {
    clear_has_profile();
    ::carbon::thinning::ConfigDefinition* temp = profile_.thinning_;
    profile_.thinning_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning) {
  clear_profile();
  if (thinning) {
    set_has_thinning();
    profile_.thinning_ = thinning;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.thinning)
}
inline ::carbon::thinning::ConfigDefinition* UploadProfileRequest::_internal_mutable_thinning() {
  if (!_internal_has_thinning()) {
    clear_profile();
    set_has_thinning();
    profile_.thinning_ = CreateMaybeMessage< ::carbon::thinning::ConfigDefinition >(GetArenaForAllocation());
  }
  return profile_.thinning_;
}
inline ::carbon::thinning::ConfigDefinition* UploadProfileRequest::mutable_thinning() {
  ::carbon::thinning::ConfigDefinition* _msg = _internal_mutable_thinning();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.thinning)
  return _msg;
}

// .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 8;
inline bool UploadProfileRequest::_internal_has_target_velocity_estimator() const {
  return profile_case() == kTargetVelocityEstimator;
}
inline bool UploadProfileRequest::has_target_velocity_estimator() const {
  return _internal_has_target_velocity_estimator();
}
inline void UploadProfileRequest::set_has_target_velocity_estimator() {
  _oneof_case_[0] = kTargetVelocityEstimator;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* UploadProfileRequest::release_target_velocity_estimator() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.target_velocity_estimator)
  if (_internal_has_target_velocity_estimator()) {
    clear_has_profile();
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_.target_velocity_estimator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.target_velocity_estimator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& UploadProfileRequest::_internal_target_velocity_estimator() const {
  return _internal_has_target_velocity_estimator()
      ? *profile_.target_velocity_estimator_
      : reinterpret_cast< ::carbon::aimbot::target_velocity_estimator::TVEProfile&>(::carbon::aimbot::target_velocity_estimator::_TVEProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& UploadProfileRequest::target_velocity_estimator() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.target_velocity_estimator)
  return _internal_target_velocity_estimator();
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* UploadProfileRequest::unsafe_arena_release_target_velocity_estimator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.target_velocity_estimator)
  if (_internal_has_target_velocity_estimator()) {
    clear_has_profile();
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_.target_velocity_estimator_;
    profile_.target_velocity_estimator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_target_velocity_estimator(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator) {
  clear_profile();
  if (target_velocity_estimator) {
    set_has_target_velocity_estimator();
    profile_.target_velocity_estimator_ = target_velocity_estimator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.target_velocity_estimator)
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* UploadProfileRequest::_internal_mutable_target_velocity_estimator() {
  if (!_internal_has_target_velocity_estimator()) {
    clear_profile();
    set_has_target_velocity_estimator();
    profile_.target_velocity_estimator_ = CreateMaybeMessage< ::carbon::aimbot::target_velocity_estimator::TVEProfile >(GetArenaForAllocation());
  }
  return profile_.target_velocity_estimator_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* UploadProfileRequest::mutable_target_velocity_estimator() {
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _msg = _internal_mutable_target_velocity_estimator();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.target_velocity_estimator)
  return _msg;
}

// .carbon.category.CategoryCollection categoryCollection = 9;
inline bool UploadProfileRequest::_internal_has_categorycollection() const {
  return profile_case() == kCategoryCollection;
}
inline bool UploadProfileRequest::has_categorycollection() const {
  return _internal_has_categorycollection();
}
inline void UploadProfileRequest::set_has_categorycollection() {
  _oneof_case_[0] = kCategoryCollection;
}
inline ::carbon::category::CategoryCollection* UploadProfileRequest::release_categorycollection() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.categoryCollection)
  if (_internal_has_categorycollection()) {
    clear_has_profile();
      ::carbon::category::CategoryCollection* temp = profile_.categorycollection_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.categorycollection_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::category::CategoryCollection& UploadProfileRequest::_internal_categorycollection() const {
  return _internal_has_categorycollection()
      ? *profile_.categorycollection_
      : reinterpret_cast< ::carbon::category::CategoryCollection&>(::carbon::category::_CategoryCollection_default_instance_);
}
inline const ::carbon::category::CategoryCollection& UploadProfileRequest::categorycollection() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.categoryCollection)
  return _internal_categorycollection();
}
inline ::carbon::category::CategoryCollection* UploadProfileRequest::unsafe_arena_release_categorycollection() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.categoryCollection)
  if (_internal_has_categorycollection()) {
    clear_has_profile();
    ::carbon::category::CategoryCollection* temp = profile_.categorycollection_;
    profile_.categorycollection_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_categorycollection(::carbon::category::CategoryCollection* categorycollection) {
  clear_profile();
  if (categorycollection) {
    set_has_categorycollection();
    profile_.categorycollection_ = categorycollection;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.categoryCollection)
}
inline ::carbon::category::CategoryCollection* UploadProfileRequest::_internal_mutable_categorycollection() {
  if (!_internal_has_categorycollection()) {
    clear_profile();
    set_has_categorycollection();
    profile_.categorycollection_ = CreateMaybeMessage< ::carbon::category::CategoryCollection >(GetArenaForAllocation());
  }
  return profile_.categorycollection_;
}
inline ::carbon::category::CategoryCollection* UploadProfileRequest::mutable_categorycollection() {
  ::carbon::category::CategoryCollection* _msg = _internal_mutable_categorycollection();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.categoryCollection)
  return _msg;
}

// .carbon.category.Category category = 10;
inline bool UploadProfileRequest::_internal_has_category() const {
  return profile_case() == kCategory;
}
inline bool UploadProfileRequest::has_category() const {
  return _internal_has_category();
}
inline void UploadProfileRequest::set_has_category() {
  _oneof_case_[0] = kCategory;
}
inline ::carbon::category::Category* UploadProfileRequest::release_category() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.category)
  if (_internal_has_category()) {
    clear_has_profile();
      ::carbon::category::Category* temp = profile_.category_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.category_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::category::Category& UploadProfileRequest::_internal_category() const {
  return _internal_has_category()
      ? *profile_.category_
      : reinterpret_cast< ::carbon::category::Category&>(::carbon::category::_Category_default_instance_);
}
inline const ::carbon::category::Category& UploadProfileRequest::category() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.UploadProfileRequest.category)
  return _internal_category();
}
inline ::carbon::category::Category* UploadProfileRequest::unsafe_arena_release_category() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.UploadProfileRequest.category)
  if (_internal_has_category()) {
    clear_has_profile();
    ::carbon::category::Category* temp = profile_.category_;
    profile_.category_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void UploadProfileRequest::unsafe_arena_set_allocated_category(::carbon::category::Category* category) {
  clear_profile();
  if (category) {
    set_has_category();
    profile_.category_ = category;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.UploadProfileRequest.category)
}
inline ::carbon::category::Category* UploadProfileRequest::_internal_mutable_category() {
  if (!_internal_has_category()) {
    clear_profile();
    set_has_category();
    profile_.category_ = CreateMaybeMessage< ::carbon::category::Category >(GetArenaForAllocation());
  }
  return profile_.category_;
}
inline ::carbon::category::Category* UploadProfileRequest::mutable_category() {
  ::carbon::category::Category* _msg = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.UploadProfileRequest.category)
  return _msg;
}

inline bool UploadProfileRequest::has_profile() const {
  return profile_case() != PROFILE_NOT_SET;
}
inline void UploadProfileRequest::clear_has_profile() {
  _oneof_case_[0] = PROFILE_NOT_SET;
}
inline UploadProfileRequest::ProfileCase UploadProfileRequest::profile_case() const {
  return UploadProfileRequest::ProfileCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// GetProfileRequest

// string uuid = 1;
inline void GetProfileRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& GetProfileRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void GetProfileRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.robot_syncer.profile_sync.GetProfileRequest.uuid)
}
inline std::string* GetProfileRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileRequest.uuid)
  return _s;
}
inline const std::string& GetProfileRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void GetProfileRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* GetProfileRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* GetProfileRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void GetProfileRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.GetProfileRequest.uuid)
}

// -------------------------------------------------------------------

// GetProfileResponse

// .carbon.aimbot.almanac.AlmanacConfig almanac = 1;
inline bool GetProfileResponse::_internal_has_almanac() const {
  return profile_case() == kAlmanac;
}
inline bool GetProfileResponse::has_almanac() const {
  return _internal_has_almanac();
}
inline void GetProfileResponse::set_has_almanac() {
  _oneof_case_[0] = kAlmanac;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* GetProfileResponse::release_almanac() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileResponse.almanac)
  if (_internal_has_almanac()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::AlmanacConfig* temp = profile_.almanac_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.almanac_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& GetProfileResponse::_internal_almanac() const {
  return _internal_has_almanac()
      ? *profile_.almanac_
      : reinterpret_cast< ::carbon::aimbot::almanac::AlmanacConfig&>(::carbon::aimbot::almanac::_AlmanacConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::AlmanacConfig& GetProfileResponse::almanac() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileResponse.almanac)
  return _internal_almanac();
}
inline ::carbon::aimbot::almanac::AlmanacConfig* GetProfileResponse::unsafe_arena_release_almanac() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.GetProfileResponse.almanac)
  if (_internal_has_almanac()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::AlmanacConfig* temp = profile_.almanac_;
    profile_.almanac_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_almanac(::carbon::aimbot::almanac::AlmanacConfig* almanac) {
  clear_profile();
  if (almanac) {
    set_has_almanac();
    profile_.almanac_ = almanac;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.almanac)
}
inline ::carbon::aimbot::almanac::AlmanacConfig* GetProfileResponse::_internal_mutable_almanac() {
  if (!_internal_has_almanac()) {
    clear_profile();
    set_has_almanac();
    profile_.almanac_ = CreateMaybeMessage< ::carbon::aimbot::almanac::AlmanacConfig >(GetArenaForAllocation());
  }
  return profile_.almanac_;
}
inline ::carbon::aimbot::almanac::AlmanacConfig* GetProfileResponse::mutable_almanac() {
  ::carbon::aimbot::almanac::AlmanacConfig* _msg = _internal_mutable_almanac();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileResponse.almanac)
  return _msg;
}

// .carbon.aimbot.almanac.DiscriminatorConfig discriminator = 2;
inline bool GetProfileResponse::_internal_has_discriminator() const {
  return profile_case() == kDiscriminator;
}
inline bool GetProfileResponse::has_discriminator() const {
  return _internal_has_discriminator();
}
inline void GetProfileResponse::set_has_discriminator() {
  _oneof_case_[0] = kDiscriminator;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* GetProfileResponse::release_discriminator() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileResponse.discriminator)
  if (_internal_has_discriminator()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::DiscriminatorConfig* temp = profile_.discriminator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.discriminator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& GetProfileResponse::_internal_discriminator() const {
  return _internal_has_discriminator()
      ? *profile_.discriminator_
      : reinterpret_cast< ::carbon::aimbot::almanac::DiscriminatorConfig&>(::carbon::aimbot::almanac::_DiscriminatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::DiscriminatorConfig& GetProfileResponse::discriminator() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileResponse.discriminator)
  return _internal_discriminator();
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* GetProfileResponse::unsafe_arena_release_discriminator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.GetProfileResponse.discriminator)
  if (_internal_has_discriminator()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::DiscriminatorConfig* temp = profile_.discriminator_;
    profile_.discriminator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_discriminator(::carbon::aimbot::almanac::DiscriminatorConfig* discriminator) {
  clear_profile();
  if (discriminator) {
    set_has_discriminator();
    profile_.discriminator_ = discriminator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.discriminator)
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* GetProfileResponse::_internal_mutable_discriminator() {
  if (!_internal_has_discriminator()) {
    clear_profile();
    set_has_discriminator();
    profile_.discriminator_ = CreateMaybeMessage< ::carbon::aimbot::almanac::DiscriminatorConfig >(GetArenaForAllocation());
  }
  return profile_.discriminator_;
}
inline ::carbon::aimbot::almanac::DiscriminatorConfig* GetProfileResponse::mutable_discriminator() {
  ::carbon::aimbot::almanac::DiscriminatorConfig* _msg = _internal_mutable_discriminator();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileResponse.discriminator)
  return _msg;
}

// .carbon.aimbot.almanac.ModelinatorConfig modelinator = 3;
inline bool GetProfileResponse::_internal_has_modelinator() const {
  return profile_case() == kModelinator;
}
inline bool GetProfileResponse::has_modelinator() const {
  return _internal_has_modelinator();
}
inline void GetProfileResponse::set_has_modelinator() {
  _oneof_case_[0] = kModelinator;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetProfileResponse::release_modelinator() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileResponse.modelinator)
  if (_internal_has_modelinator()) {
    clear_has_profile();
      ::carbon::aimbot::almanac::ModelinatorConfig* temp = profile_.modelinator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.modelinator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& GetProfileResponse::_internal_modelinator() const {
  return _internal_has_modelinator()
      ? *profile_.modelinator_
      : reinterpret_cast< ::carbon::aimbot::almanac::ModelinatorConfig&>(::carbon::aimbot::almanac::_ModelinatorConfig_default_instance_);
}
inline const ::carbon::aimbot::almanac::ModelinatorConfig& GetProfileResponse::modelinator() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileResponse.modelinator)
  return _internal_modelinator();
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetProfileResponse::unsafe_arena_release_modelinator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.GetProfileResponse.modelinator)
  if (_internal_has_modelinator()) {
    clear_has_profile();
    ::carbon::aimbot::almanac::ModelinatorConfig* temp = profile_.modelinator_;
    profile_.modelinator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_modelinator(::carbon::aimbot::almanac::ModelinatorConfig* modelinator) {
  clear_profile();
  if (modelinator) {
    set_has_modelinator();
    profile_.modelinator_ = modelinator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.modelinator)
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetProfileResponse::_internal_mutable_modelinator() {
  if (!_internal_has_modelinator()) {
    clear_profile();
    set_has_modelinator();
    profile_.modelinator_ = CreateMaybeMessage< ::carbon::aimbot::almanac::ModelinatorConfig >(GetArenaForAllocation());
  }
  return profile_.modelinator_;
}
inline ::carbon::aimbot::almanac::ModelinatorConfig* GetProfileResponse::mutable_modelinator() {
  ::carbon::aimbot::almanac::ModelinatorConfig* _msg = _internal_mutable_modelinator();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileResponse.modelinator)
  return _msg;
}

// .carbon.frontend.banding.BandingDef banding = 4;
inline bool GetProfileResponse::_internal_has_banding() const {
  return profile_case() == kBanding;
}
inline bool GetProfileResponse::has_banding() const {
  return _internal_has_banding();
}
inline void GetProfileResponse::set_has_banding() {
  _oneof_case_[0] = kBanding;
}
inline ::carbon::frontend::banding::BandingDef* GetProfileResponse::release_banding() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileResponse.banding)
  if (_internal_has_banding()) {
    clear_has_profile();
      ::carbon::frontend::banding::BandingDef* temp = profile_.banding_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.banding_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::frontend::banding::BandingDef& GetProfileResponse::_internal_banding() const {
  return _internal_has_banding()
      ? *profile_.banding_
      : reinterpret_cast< ::carbon::frontend::banding::BandingDef&>(::carbon::frontend::banding::_BandingDef_default_instance_);
}
inline const ::carbon::frontend::banding::BandingDef& GetProfileResponse::banding() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileResponse.banding)
  return _internal_banding();
}
inline ::carbon::frontend::banding::BandingDef* GetProfileResponse::unsafe_arena_release_banding() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.GetProfileResponse.banding)
  if (_internal_has_banding()) {
    clear_has_profile();
    ::carbon::frontend::banding::BandingDef* temp = profile_.banding_;
    profile_.banding_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_banding(::carbon::frontend::banding::BandingDef* banding) {
  clear_profile();
  if (banding) {
    set_has_banding();
    profile_.banding_ = banding;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.banding)
}
inline ::carbon::frontend::banding::BandingDef* GetProfileResponse::_internal_mutable_banding() {
  if (!_internal_has_banding()) {
    clear_profile();
    set_has_banding();
    profile_.banding_ = CreateMaybeMessage< ::carbon::frontend::banding::BandingDef >(GetArenaForAllocation());
  }
  return profile_.banding_;
}
inline ::carbon::frontend::banding::BandingDef* GetProfileResponse::mutable_banding() {
  ::carbon::frontend::banding::BandingDef* _msg = _internal_mutable_banding();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileResponse.banding)
  return _msg;
}

// .carbon.thinning.ConfigDefinition thinning = 5;
inline bool GetProfileResponse::_internal_has_thinning() const {
  return profile_case() == kThinning;
}
inline bool GetProfileResponse::has_thinning() const {
  return _internal_has_thinning();
}
inline void GetProfileResponse::set_has_thinning() {
  _oneof_case_[0] = kThinning;
}
inline ::carbon::thinning::ConfigDefinition* GetProfileResponse::release_thinning() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileResponse.thinning)
  if (_internal_has_thinning()) {
    clear_has_profile();
      ::carbon::thinning::ConfigDefinition* temp = profile_.thinning_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.thinning_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::thinning::ConfigDefinition& GetProfileResponse::_internal_thinning() const {
  return _internal_has_thinning()
      ? *profile_.thinning_
      : reinterpret_cast< ::carbon::thinning::ConfigDefinition&>(::carbon::thinning::_ConfigDefinition_default_instance_);
}
inline const ::carbon::thinning::ConfigDefinition& GetProfileResponse::thinning() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileResponse.thinning)
  return _internal_thinning();
}
inline ::carbon::thinning::ConfigDefinition* GetProfileResponse::unsafe_arena_release_thinning() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.GetProfileResponse.thinning)
  if (_internal_has_thinning()) {
    clear_has_profile();
    ::carbon::thinning::ConfigDefinition* temp = profile_.thinning_;
    profile_.thinning_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_thinning(::carbon::thinning::ConfigDefinition* thinning) {
  clear_profile();
  if (thinning) {
    set_has_thinning();
    profile_.thinning_ = thinning;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.thinning)
}
inline ::carbon::thinning::ConfigDefinition* GetProfileResponse::_internal_mutable_thinning() {
  if (!_internal_has_thinning()) {
    clear_profile();
    set_has_thinning();
    profile_.thinning_ = CreateMaybeMessage< ::carbon::thinning::ConfigDefinition >(GetArenaForAllocation());
  }
  return profile_.thinning_;
}
inline ::carbon::thinning::ConfigDefinition* GetProfileResponse::mutable_thinning() {
  ::carbon::thinning::ConfigDefinition* _msg = _internal_mutable_thinning();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileResponse.thinning)
  return _msg;
}

// .carbon.aimbot.target_velocity_estimator.TVEProfile target_velocity_estimator = 6;
inline bool GetProfileResponse::_internal_has_target_velocity_estimator() const {
  return profile_case() == kTargetVelocityEstimator;
}
inline bool GetProfileResponse::has_target_velocity_estimator() const {
  return _internal_has_target_velocity_estimator();
}
inline void GetProfileResponse::set_has_target_velocity_estimator() {
  _oneof_case_[0] = kTargetVelocityEstimator;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetProfileResponse::release_target_velocity_estimator() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileResponse.target_velocity_estimator)
  if (_internal_has_target_velocity_estimator()) {
    clear_has_profile();
      ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_.target_velocity_estimator_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.target_velocity_estimator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& GetProfileResponse::_internal_target_velocity_estimator() const {
  return _internal_has_target_velocity_estimator()
      ? *profile_.target_velocity_estimator_
      : reinterpret_cast< ::carbon::aimbot::target_velocity_estimator::TVEProfile&>(::carbon::aimbot::target_velocity_estimator::_TVEProfile_default_instance_);
}
inline const ::carbon::aimbot::target_velocity_estimator::TVEProfile& GetProfileResponse::target_velocity_estimator() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileResponse.target_velocity_estimator)
  return _internal_target_velocity_estimator();
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetProfileResponse::unsafe_arena_release_target_velocity_estimator() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.GetProfileResponse.target_velocity_estimator)
  if (_internal_has_target_velocity_estimator()) {
    clear_has_profile();
    ::carbon::aimbot::target_velocity_estimator::TVEProfile* temp = profile_.target_velocity_estimator_;
    profile_.target_velocity_estimator_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_target_velocity_estimator(::carbon::aimbot::target_velocity_estimator::TVEProfile* target_velocity_estimator) {
  clear_profile();
  if (target_velocity_estimator) {
    set_has_target_velocity_estimator();
    profile_.target_velocity_estimator_ = target_velocity_estimator;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.target_velocity_estimator)
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetProfileResponse::_internal_mutable_target_velocity_estimator() {
  if (!_internal_has_target_velocity_estimator()) {
    clear_profile();
    set_has_target_velocity_estimator();
    profile_.target_velocity_estimator_ = CreateMaybeMessage< ::carbon::aimbot::target_velocity_estimator::TVEProfile >(GetArenaForAllocation());
  }
  return profile_.target_velocity_estimator_;
}
inline ::carbon::aimbot::target_velocity_estimator::TVEProfile* GetProfileResponse::mutable_target_velocity_estimator() {
  ::carbon::aimbot::target_velocity_estimator::TVEProfile* _msg = _internal_mutable_target_velocity_estimator();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileResponse.target_velocity_estimator)
  return _msg;
}

// .carbon.category.CategoryCollection categoryCollection = 7;
inline bool GetProfileResponse::_internal_has_categorycollection() const {
  return profile_case() == kCategoryCollection;
}
inline bool GetProfileResponse::has_categorycollection() const {
  return _internal_has_categorycollection();
}
inline void GetProfileResponse::set_has_categorycollection() {
  _oneof_case_[0] = kCategoryCollection;
}
inline ::carbon::category::CategoryCollection* GetProfileResponse::release_categorycollection() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileResponse.categoryCollection)
  if (_internal_has_categorycollection()) {
    clear_has_profile();
      ::carbon::category::CategoryCollection* temp = profile_.categorycollection_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.categorycollection_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::category::CategoryCollection& GetProfileResponse::_internal_categorycollection() const {
  return _internal_has_categorycollection()
      ? *profile_.categorycollection_
      : reinterpret_cast< ::carbon::category::CategoryCollection&>(::carbon::category::_CategoryCollection_default_instance_);
}
inline const ::carbon::category::CategoryCollection& GetProfileResponse::categorycollection() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileResponse.categoryCollection)
  return _internal_categorycollection();
}
inline ::carbon::category::CategoryCollection* GetProfileResponse::unsafe_arena_release_categorycollection() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.GetProfileResponse.categoryCollection)
  if (_internal_has_categorycollection()) {
    clear_has_profile();
    ::carbon::category::CategoryCollection* temp = profile_.categorycollection_;
    profile_.categorycollection_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_categorycollection(::carbon::category::CategoryCollection* categorycollection) {
  clear_profile();
  if (categorycollection) {
    set_has_categorycollection();
    profile_.categorycollection_ = categorycollection;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.categoryCollection)
}
inline ::carbon::category::CategoryCollection* GetProfileResponse::_internal_mutable_categorycollection() {
  if (!_internal_has_categorycollection()) {
    clear_profile();
    set_has_categorycollection();
    profile_.categorycollection_ = CreateMaybeMessage< ::carbon::category::CategoryCollection >(GetArenaForAllocation());
  }
  return profile_.categorycollection_;
}
inline ::carbon::category::CategoryCollection* GetProfileResponse::mutable_categorycollection() {
  ::carbon::category::CategoryCollection* _msg = _internal_mutable_categorycollection();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileResponse.categoryCollection)
  return _msg;
}

// .carbon.category.Category category = 8;
inline bool GetProfileResponse::_internal_has_category() const {
  return profile_case() == kCategory;
}
inline bool GetProfileResponse::has_category() const {
  return _internal_has_category();
}
inline void GetProfileResponse::set_has_category() {
  _oneof_case_[0] = kCategory;
}
inline ::carbon::category::Category* GetProfileResponse::release_category() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.GetProfileResponse.category)
  if (_internal_has_category()) {
    clear_has_profile();
      ::carbon::category::Category* temp = profile_.category_;
    if (GetArenaForAllocation() != nullptr) {
      temp = ::PROTOBUF_NAMESPACE_ID::internal::DuplicateIfNonNull(temp);
    }
    profile_.category_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline const ::carbon::category::Category& GetProfileResponse::_internal_category() const {
  return _internal_has_category()
      ? *profile_.category_
      : reinterpret_cast< ::carbon::category::Category&>(::carbon::category::_Category_default_instance_);
}
inline const ::carbon::category::Category& GetProfileResponse::category() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.GetProfileResponse.category)
  return _internal_category();
}
inline ::carbon::category::Category* GetProfileResponse::unsafe_arena_release_category() {
  // @@protoc_insertion_point(field_unsafe_arena_release:carbon.robot_syncer.profile_sync.GetProfileResponse.category)
  if (_internal_has_category()) {
    clear_has_profile();
    ::carbon::category::Category* temp = profile_.category_;
    profile_.category_ = nullptr;
    return temp;
  } else {
    return nullptr;
  }
}
inline void GetProfileResponse::unsafe_arena_set_allocated_category(::carbon::category::Category* category) {
  clear_profile();
  if (category) {
    set_has_category();
    profile_.category_ = category;
  }
  // @@protoc_insertion_point(field_unsafe_arena_set_allocated:carbon.robot_syncer.profile_sync.GetProfileResponse.category)
}
inline ::carbon::category::Category* GetProfileResponse::_internal_mutable_category() {
  if (!_internal_has_category()) {
    clear_profile();
    set_has_category();
    profile_.category_ = CreateMaybeMessage< ::carbon::category::Category >(GetArenaForAllocation());
  }
  return profile_.category_;
}
inline ::carbon::category::Category* GetProfileResponse::mutable_category() {
  ::carbon::category::Category* _msg = _internal_mutable_category();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.GetProfileResponse.category)
  return _msg;
}

inline bool GetProfileResponse::has_profile() const {
  return profile_case() != PROFILE_NOT_SET;
}
inline void GetProfileResponse::clear_has_profile() {
  _oneof_case_[0] = PROFILE_NOT_SET;
}
inline GetProfileResponse::ProfileCase GetProfileResponse::profile_case() const {
  return GetProfileResponse::ProfileCase(_oneof_case_[0]);
}
// -------------------------------------------------------------------

// DeleteProfileRequest

// string uuid = 1;
inline void DeleteProfileRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& DeleteProfileRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.DeleteProfileRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void DeleteProfileRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.robot_syncer.profile_sync.DeleteProfileRequest.uuid)
}
inline std::string* DeleteProfileRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.DeleteProfileRequest.uuid)
  return _s;
}
inline const std::string& DeleteProfileRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void DeleteProfileRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* DeleteProfileRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* DeleteProfileRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.DeleteProfileRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void DeleteProfileRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.DeleteProfileRequest.uuid)
}

// -------------------------------------------------------------------

// PurgeProfileRequest

// string uuid = 1;
inline void PurgeProfileRequest::clear_uuid() {
  uuid_.ClearToEmpty();
}
inline const std::string& PurgeProfileRequest::uuid() const {
  // @@protoc_insertion_point(field_get:carbon.robot_syncer.profile_sync.PurgeProfileRequest.uuid)
  return _internal_uuid();
}
template <typename ArgT0, typename... ArgT>
inline PROTOBUF_ALWAYS_INLINE
void PurgeProfileRequest::set_uuid(ArgT0&& arg0, ArgT... args) {
 
 uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, static_cast<ArgT0 &&>(arg0), args..., GetArenaForAllocation());
  // @@protoc_insertion_point(field_set:carbon.robot_syncer.profile_sync.PurgeProfileRequest.uuid)
}
inline std::string* PurgeProfileRequest::mutable_uuid() {
  std::string* _s = _internal_mutable_uuid();
  // @@protoc_insertion_point(field_mutable:carbon.robot_syncer.profile_sync.PurgeProfileRequest.uuid)
  return _s;
}
inline const std::string& PurgeProfileRequest::_internal_uuid() const {
  return uuid_.Get();
}
inline void PurgeProfileRequest::_internal_set_uuid(const std::string& value) {
  
  uuid_.Set(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, value, GetArenaForAllocation());
}
inline std::string* PurgeProfileRequest::_internal_mutable_uuid() {
  
  return uuid_.Mutable(::PROTOBUF_NAMESPACE_ID::internal::ArenaStringPtr::EmptyDefault{}, GetArenaForAllocation());
}
inline std::string* PurgeProfileRequest::release_uuid() {
  // @@protoc_insertion_point(field_release:carbon.robot_syncer.profile_sync.PurgeProfileRequest.uuid)
  return uuid_.Release(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), GetArenaForAllocation());
}
inline void PurgeProfileRequest::set_allocated_uuid(std::string* uuid) {
  if (uuid != nullptr) {
    
  } else {
    
  }
  uuid_.SetAllocated(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), uuid,
      GetArenaForAllocation());
#ifdef PROTOBUF_FORCE_COPY_DEFAULT_STRING
  if (uuid_.IsDefault(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited())) {
    uuid_.Set(&::PROTOBUF_NAMESPACE_ID::internal::GetEmptyStringAlreadyInited(), "", GetArenaForAllocation());
  }
#endif // PROTOBUF_FORCE_COPY_DEFAULT_STRING
  // @@protoc_insertion_point(field_set_allocated:carbon.robot_syncer.profile_sync.PurgeProfileRequest.uuid)
}

#ifdef __GNUC__
  #pragma GCC diagnostic pop
#endif  // __GNUC__
// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------

// -------------------------------------------------------------------


// @@protoc_insertion_point(namespace_scope)

}  // namespace profile_sync
}  // namespace robot_syncer
}  // namespace carbon

// @@protoc_insertion_point(global_scope)

#include <google/protobuf/port_undef.inc>
#endif  // GOOGLE_PROTOBUF_INCLUDED_GOOGLE_PROTOBUF_INCLUDED_robot_5fsyncer_2fproto_2fprofile_5fsync_2eproto

# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: robot_syncer/proto/profile_sync.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2
from generated.frontend.proto import profile_sync_pb2 as frontend_dot_proto_dot_profile__sync__pb2
from generated.proto.almanac import almanac_pb2 as proto_dot_almanac_dot_almanac__pb2
from generated.proto.thinning import thinning_pb2 as proto_dot_thinning_dot_thinning__pb2
from generated.frontend.proto import banding_pb2 as frontend_dot_proto_dot_banding__pb2
from generated.proto.target_velocity_estimator import target_velocity_estimator_pb2 as proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2
from generated.category.proto import category_pb2 as category_dot_proto_dot_category__pb2


DESCRIPTOR = _descriptor.FileDescriptor(
  name='robot_syncer/proto/profile_sync.proto',
  package='carbon.robot_syncer.profile_sync',
  syntax='proto3',
  serialized_options=b'Z\022proto/robot_syncer',
  create_key=_descriptor._internal_create_key,
  serialized_pb=b'\n%robot_syncer/proto/profile_sync.proto\x12 carbon.robot_syncer.profile_sync\x1a\x17portal/proto/util.proto\x1a!frontend/proto/profile_sync.proto\x1a\x1bproto/almanac/almanac.proto\x1a\x1dproto/thinning/thinning.proto\x1a\x1c\x66rontend/proto/banding.proto\x1a?proto/target_velocity_estimator/target_velocity_estimator.proto\x1a\x1d\x63\x61tegory/proto/category.proto\"1\n\x19GetProfileSyncDataRequest\x12\x14\n\x0crobot_serial\x18\x01 \x01(\t\"\xda\x01\n\x1aGetProfileSyncDataResponse\x12\\\n\x08profiles\x18\x01 \x03(\x0b\x32J.carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry\x1a^\n\rProfilesEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12<\n\x05value\x18\x02 \x01(\x0b\x32-.carbon.frontend.profile_sync.ProfileSyncData:\x02\x38\x01\"\xce\x04\n\x14UploadProfileRequest\x12\x1b\n\x13last_update_time_ms\x18\x01 \x01(\x03\x12\x14\n\x0crobot_serial\x18\x02 \x01(\t\x12\x37\n\x07\x61lmanac\x18\x03 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfigH\x00\x12\x43\n\rdiscriminator\x18\x04 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfigH\x00\x12?\n\x0bmodelinator\x18\x05 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfigH\x00\x12\x36\n\x07\x62\x61nding\x18\x06 \x01(\x0b\x32#.carbon.frontend.banding.BandingDefH\x00\x12\x35\n\x08thinning\x18\x07 \x01(\x0b\x32!.carbon.thinning.ConfigDefinitionH\x00\x12X\n\x19target_velocity_estimator\x18\x08 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfileH\x00\x12\x41\n\x12\x63\x61tegoryCollection\x18\t \x01(\x0b\x32#.carbon.category.CategoryCollectionH\x00\x12-\n\x08\x63\x61tegory\x18\n \x01(\x0b\x32\x19.carbon.category.CategoryH\x00\x42\t\n\x07profile\"!\n\x11GetProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"\x99\x04\n\x12GetProfileResponse\x12\x37\n\x07\x61lmanac\x18\x01 \x01(\x0b\x32$.carbon.aimbot.almanac.AlmanacConfigH\x00\x12\x43\n\rdiscriminator\x18\x02 \x01(\x0b\x32*.carbon.aimbot.almanac.DiscriminatorConfigH\x00\x12?\n\x0bmodelinator\x18\x03 \x01(\x0b\x32(.carbon.aimbot.almanac.ModelinatorConfigH\x00\x12\x36\n\x07\x62\x61nding\x18\x04 \x01(\x0b\x32#.carbon.frontend.banding.BandingDefH\x00\x12\x35\n\x08thinning\x18\x05 \x01(\x0b\x32!.carbon.thinning.ConfigDefinitionH\x00\x12X\n\x19target_velocity_estimator\x18\x06 \x01(\x0b\x32\x33.carbon.aimbot.target_velocity_estimator.TVEProfileH\x00\x12\x41\n\x12\x63\x61tegoryCollection\x18\x07 \x01(\x0b\x32#.carbon.category.CategoryCollectionH\x00\x12-\n\x08\x63\x61tegory\x18\x08 \x01(\x0b\x32\x19.carbon.category.CategoryH\x00\x42\t\n\x07profile\"$\n\x14\x44\x65leteProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t\"#\n\x13PurgeProfileRequest\x12\x0c\n\x04uuid\x18\x01 \x01(\t2\xcd\x04\n\x16RoSyProfileSyncService\x12\x8f\x01\n\x12GetProfileSyncData\x12;.carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest\x1a<.carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse\x12\x62\n\rUploadProfile\x12\x36.carbon.robot_syncer.profile_sync.UploadProfileRequest\x1a\x19.carbon.portal.util.Empty\x12w\n\nGetProfile\x12\x33.carbon.robot_syncer.profile_sync.GetProfileRequest\x1a\x34.carbon.robot_syncer.profile_sync.GetProfileResponse\x12\x62\n\rDeleteProfile\x12\x36.carbon.robot_syncer.profile_sync.DeleteProfileRequest\x1a\x19.carbon.portal.util.Empty\x12`\n\x0cPurgeProfile\x12\x35.carbon.robot_syncer.profile_sync.PurgeProfileRequest\x1a\x19.carbon.portal.util.EmptyB\x14Z\x12proto/robot_syncerb\x06proto3'
  ,
  dependencies=[portal_dot_proto_dot_util__pb2.DESCRIPTOR,frontend_dot_proto_dot_profile__sync__pb2.DESCRIPTOR,proto_dot_almanac_dot_almanac__pb2.DESCRIPTOR,proto_dot_thinning_dot_thinning__pb2.DESCRIPTOR,frontend_dot_proto_dot_banding__pb2.DESCRIPTOR,proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2.DESCRIPTOR,category_dot_proto_dot_category__pb2.DESCRIPTOR,])




_GETPROFILESYNCDATAREQUEST = _descriptor.Descriptor(
  name='GetProfileSyncDataRequest',
  full_name='carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='robot_serial', full_name='carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest.robot_serial', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=321,
  serialized_end=370,
)


_GETPROFILESYNCDATARESPONSE_PROFILESENTRY = _descriptor.Descriptor(
  name='ProfilesEntry',
  full_name='carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='key', full_name='carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry.key', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='value', full_name='carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry.value', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=b'8\001',
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=497,
  serialized_end=591,
)

_GETPROFILESYNCDATARESPONSE = _descriptor.Descriptor(
  name='GetProfileSyncDataResponse',
  full_name='carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='profiles', full_name='carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.profiles', index=0,
      number=1, type=11, cpp_type=10, label=3,
      has_default_value=False, default_value=[],
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[_GETPROFILESYNCDATARESPONSE_PROFILESENTRY, ],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=373,
  serialized_end=591,
)


_UPLOADPROFILEREQUEST = _descriptor.Descriptor(
  name='UploadProfileRequest',
  full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='last_update_time_ms', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.last_update_time_ms', index=0,
      number=1, type=3, cpp_type=2, label=1,
      has_default_value=False, default_value=0,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='robot_serial', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.robot_serial', index=1,
      number=2, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='almanac', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.almanac', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='discriminator', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.discriminator', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='modelinator', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.modelinator', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.banding', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.thinning', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_velocity_estimator', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.target_velocity_estimator', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='categoryCollection', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.categoryCollection', index=8,
      number=9, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='category', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.category', index=9,
      number=10, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='profile', full_name='carbon.robot_syncer.profile_sync.UploadProfileRequest.profile',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=594,
  serialized_end=1184,
)


_GETPROFILEREQUEST = _descriptor.Descriptor(
  name='GetProfileRequest',
  full_name='carbon.robot_syncer.profile_sync.GetProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.robot_syncer.profile_sync.GetProfileRequest.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1186,
  serialized_end=1219,
)


_GETPROFILERESPONSE = _descriptor.Descriptor(
  name='GetProfileResponse',
  full_name='carbon.robot_syncer.profile_sync.GetProfileResponse',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='almanac', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.almanac', index=0,
      number=1, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='discriminator', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.discriminator', index=1,
      number=2, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='modelinator', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.modelinator', index=2,
      number=3, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='banding', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.banding', index=3,
      number=4, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='thinning', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.thinning', index=4,
      number=5, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='target_velocity_estimator', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.target_velocity_estimator', index=5,
      number=6, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='categoryCollection', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.categoryCollection', index=6,
      number=7, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
    _descriptor.FieldDescriptor(
      name='category', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.category', index=7,
      number=8, type=11, cpp_type=10, label=1,
      has_default_value=False, default_value=None,
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
    _descriptor.OneofDescriptor(
      name='profile', full_name='carbon.robot_syncer.profile_sync.GetProfileResponse.profile',
      index=0, containing_type=None,
      create_key=_descriptor._internal_create_key,
    fields=[]),
  ],
  serialized_start=1222,
  serialized_end=1759,
)


_DELETEPROFILEREQUEST = _descriptor.Descriptor(
  name='DeleteProfileRequest',
  full_name='carbon.robot_syncer.profile_sync.DeleteProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.robot_syncer.profile_sync.DeleteProfileRequest.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1761,
  serialized_end=1797,
)


_PURGEPROFILEREQUEST = _descriptor.Descriptor(
  name='PurgeProfileRequest',
  full_name='carbon.robot_syncer.profile_sync.PurgeProfileRequest',
  filename=None,
  file=DESCRIPTOR,
  containing_type=None,
  create_key=_descriptor._internal_create_key,
  fields=[
    _descriptor.FieldDescriptor(
      name='uuid', full_name='carbon.robot_syncer.profile_sync.PurgeProfileRequest.uuid', index=0,
      number=1, type=9, cpp_type=9, label=1,
      has_default_value=False, default_value=b"".decode('utf-8'),
      message_type=None, enum_type=None, containing_type=None,
      is_extension=False, extension_scope=None,
      serialized_options=None, file=DESCRIPTOR,  create_key=_descriptor._internal_create_key),
  ],
  extensions=[
  ],
  nested_types=[],
  enum_types=[
  ],
  serialized_options=None,
  is_extendable=False,
  syntax='proto3',
  extension_ranges=[],
  oneofs=[
  ],
  serialized_start=1799,
  serialized_end=1834,
)

_GETPROFILESYNCDATARESPONSE_PROFILESENTRY.fields_by_name['value'].message_type = frontend_dot_proto_dot_profile__sync__pb2._PROFILESYNCDATA
_GETPROFILESYNCDATARESPONSE_PROFILESENTRY.containing_type = _GETPROFILESYNCDATARESPONSE
_GETPROFILESYNCDATARESPONSE.fields_by_name['profiles'].message_type = _GETPROFILESYNCDATARESPONSE_PROFILESENTRY
_UPLOADPROFILEREQUEST.fields_by_name['almanac'].message_type = proto_dot_almanac_dot_almanac__pb2._ALMANACCONFIG
_UPLOADPROFILEREQUEST.fields_by_name['discriminator'].message_type = proto_dot_almanac_dot_almanac__pb2._DISCRIMINATORCONFIG
_UPLOADPROFILEREQUEST.fields_by_name['modelinator'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_UPLOADPROFILEREQUEST.fields_by_name['banding'].message_type = frontend_dot_proto_dot_banding__pb2._BANDINGDEF
_UPLOADPROFILEREQUEST.fields_by_name['thinning'].message_type = proto_dot_thinning_dot_thinning__pb2._CONFIGDEFINITION
_UPLOADPROFILEREQUEST.fields_by_name['target_velocity_estimator'].message_type = proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2._TVEPROFILE
_UPLOADPROFILEREQUEST.fields_by_name['categoryCollection'].message_type = category_dot_proto_dot_category__pb2._CATEGORYCOLLECTION
_UPLOADPROFILEREQUEST.fields_by_name['category'].message_type = category_dot_proto_dot_category__pb2._CATEGORY
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['almanac'])
_UPLOADPROFILEREQUEST.fields_by_name['almanac'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['discriminator'])
_UPLOADPROFILEREQUEST.fields_by_name['discriminator'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['modelinator'])
_UPLOADPROFILEREQUEST.fields_by_name['modelinator'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['banding'])
_UPLOADPROFILEREQUEST.fields_by_name['banding'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['thinning'])
_UPLOADPROFILEREQUEST.fields_by_name['thinning'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['target_velocity_estimator'])
_UPLOADPROFILEREQUEST.fields_by_name['target_velocity_estimator'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['categoryCollection'])
_UPLOADPROFILEREQUEST.fields_by_name['categoryCollection'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_UPLOADPROFILEREQUEST.oneofs_by_name['profile'].fields.append(
  _UPLOADPROFILEREQUEST.fields_by_name['category'])
_UPLOADPROFILEREQUEST.fields_by_name['category'].containing_oneof = _UPLOADPROFILEREQUEST.oneofs_by_name['profile']
_GETPROFILERESPONSE.fields_by_name['almanac'].message_type = proto_dot_almanac_dot_almanac__pb2._ALMANACCONFIG
_GETPROFILERESPONSE.fields_by_name['discriminator'].message_type = proto_dot_almanac_dot_almanac__pb2._DISCRIMINATORCONFIG
_GETPROFILERESPONSE.fields_by_name['modelinator'].message_type = proto_dot_almanac_dot_almanac__pb2._MODELINATORCONFIG
_GETPROFILERESPONSE.fields_by_name['banding'].message_type = frontend_dot_proto_dot_banding__pb2._BANDINGDEF
_GETPROFILERESPONSE.fields_by_name['thinning'].message_type = proto_dot_thinning_dot_thinning__pb2._CONFIGDEFINITION
_GETPROFILERESPONSE.fields_by_name['target_velocity_estimator'].message_type = proto_dot_target__velocity__estimator_dot_target__velocity__estimator__pb2._TVEPROFILE
_GETPROFILERESPONSE.fields_by_name['categoryCollection'].message_type = category_dot_proto_dot_category__pb2._CATEGORYCOLLECTION
_GETPROFILERESPONSE.fields_by_name['category'].message_type = category_dot_proto_dot_category__pb2._CATEGORY
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['almanac'])
_GETPROFILERESPONSE.fields_by_name['almanac'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['discriminator'])
_GETPROFILERESPONSE.fields_by_name['discriminator'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['modelinator'])
_GETPROFILERESPONSE.fields_by_name['modelinator'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['banding'])
_GETPROFILERESPONSE.fields_by_name['banding'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['thinning'])
_GETPROFILERESPONSE.fields_by_name['thinning'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['target_velocity_estimator'])
_GETPROFILERESPONSE.fields_by_name['target_velocity_estimator'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['categoryCollection'])
_GETPROFILERESPONSE.fields_by_name['categoryCollection'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
_GETPROFILERESPONSE.oneofs_by_name['profile'].fields.append(
  _GETPROFILERESPONSE.fields_by_name['category'])
_GETPROFILERESPONSE.fields_by_name['category'].containing_oneof = _GETPROFILERESPONSE.oneofs_by_name['profile']
DESCRIPTOR.message_types_by_name['GetProfileSyncDataRequest'] = _GETPROFILESYNCDATAREQUEST
DESCRIPTOR.message_types_by_name['GetProfileSyncDataResponse'] = _GETPROFILESYNCDATARESPONSE
DESCRIPTOR.message_types_by_name['UploadProfileRequest'] = _UPLOADPROFILEREQUEST
DESCRIPTOR.message_types_by_name['GetProfileRequest'] = _GETPROFILEREQUEST
DESCRIPTOR.message_types_by_name['GetProfileResponse'] = _GETPROFILERESPONSE
DESCRIPTOR.message_types_by_name['DeleteProfileRequest'] = _DELETEPROFILEREQUEST
DESCRIPTOR.message_types_by_name['PurgeProfileRequest'] = _PURGEPROFILEREQUEST
_sym_db.RegisterFileDescriptor(DESCRIPTOR)

GetProfileSyncDataRequest = _reflection.GeneratedProtocolMessageType('GetProfileSyncDataRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPROFILESYNCDATAREQUEST,
  '__module__' : 'robot_syncer.proto.profile_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileSyncDataRequest)
  })
_sym_db.RegisterMessage(GetProfileSyncDataRequest)

GetProfileSyncDataResponse = _reflection.GeneratedProtocolMessageType('GetProfileSyncDataResponse', (_message.Message,), {

  'ProfilesEntry' : _reflection.GeneratedProtocolMessageType('ProfilesEntry', (_message.Message,), {
    'DESCRIPTOR' : _GETPROFILESYNCDATARESPONSE_PROFILESENTRY,
    '__module__' : 'robot_syncer.proto.profile_sync_pb2'
    # @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse.ProfilesEntry)
    })
  ,
  'DESCRIPTOR' : _GETPROFILESYNCDATARESPONSE,
  '__module__' : 'robot_syncer.proto.profile_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileSyncDataResponse)
  })
_sym_db.RegisterMessage(GetProfileSyncDataResponse)
_sym_db.RegisterMessage(GetProfileSyncDataResponse.ProfilesEntry)

UploadProfileRequest = _reflection.GeneratedProtocolMessageType('UploadProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADPROFILEREQUEST,
  '__module__' : 'robot_syncer.proto.profile_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.UploadProfileRequest)
  })
_sym_db.RegisterMessage(UploadProfileRequest)

GetProfileRequest = _reflection.GeneratedProtocolMessageType('GetProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _GETPROFILEREQUEST,
  '__module__' : 'robot_syncer.proto.profile_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileRequest)
  })
_sym_db.RegisterMessage(GetProfileRequest)

GetProfileResponse = _reflection.GeneratedProtocolMessageType('GetProfileResponse', (_message.Message,), {
  'DESCRIPTOR' : _GETPROFILERESPONSE,
  '__module__' : 'robot_syncer.proto.profile_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.GetProfileResponse)
  })
_sym_db.RegisterMessage(GetProfileResponse)

DeleteProfileRequest = _reflection.GeneratedProtocolMessageType('DeleteProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _DELETEPROFILEREQUEST,
  '__module__' : 'robot_syncer.proto.profile_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.DeleteProfileRequest)
  })
_sym_db.RegisterMessage(DeleteProfileRequest)

PurgeProfileRequest = _reflection.GeneratedProtocolMessageType('PurgeProfileRequest', (_message.Message,), {
  'DESCRIPTOR' : _PURGEPROFILEREQUEST,
  '__module__' : 'robot_syncer.proto.profile_sync_pb2'
  # @@protoc_insertion_point(class_scope:carbon.robot_syncer.profile_sync.PurgeProfileRequest)
  })
_sym_db.RegisterMessage(PurgeProfileRequest)


DESCRIPTOR._options = None
_GETPROFILESYNCDATARESPONSE_PROFILESENTRY._options = None

_ROSYPROFILESYNCSERVICE = _descriptor.ServiceDescriptor(
  name='RoSyProfileSyncService',
  full_name='carbon.robot_syncer.profile_sync.RoSyProfileSyncService',
  file=DESCRIPTOR,
  index=0,
  serialized_options=None,
  create_key=_descriptor._internal_create_key,
  serialized_start=1837,
  serialized_end=2426,
  methods=[
  _descriptor.MethodDescriptor(
    name='GetProfileSyncData',
    full_name='carbon.robot_syncer.profile_sync.RoSyProfileSyncService.GetProfileSyncData',
    index=0,
    containing_service=None,
    input_type=_GETPROFILESYNCDATAREQUEST,
    output_type=_GETPROFILESYNCDATARESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='UploadProfile',
    full_name='carbon.robot_syncer.profile_sync.RoSyProfileSyncService.UploadProfile',
    index=1,
    containing_service=None,
    input_type=_UPLOADPROFILEREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='GetProfile',
    full_name='carbon.robot_syncer.profile_sync.RoSyProfileSyncService.GetProfile',
    index=2,
    containing_service=None,
    input_type=_GETPROFILEREQUEST,
    output_type=_GETPROFILERESPONSE,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='DeleteProfile',
    full_name='carbon.robot_syncer.profile_sync.RoSyProfileSyncService.DeleteProfile',
    index=3,
    containing_service=None,
    input_type=_DELETEPROFILEREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
  _descriptor.MethodDescriptor(
    name='PurgeProfile',
    full_name='carbon.robot_syncer.profile_sync.RoSyProfileSyncService.PurgeProfile',
    index=4,
    containing_service=None,
    input_type=_PURGEPROFILEREQUEST,
    output_type=portal_dot_proto_dot_util__pb2._EMPTY,
    serialized_options=None,
    create_key=_descriptor._internal_create_key,
  ),
])
_sym_db.RegisterServiceDescriptor(_ROSYPROFILESYNCSERVICE)

DESCRIPTOR.services_by_name['RoSyProfileSyncService'] = _ROSYPROFILESYNCSERVICE

# @@protoc_insertion_point(module_scope)

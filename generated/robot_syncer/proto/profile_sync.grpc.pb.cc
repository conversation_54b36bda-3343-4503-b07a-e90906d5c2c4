// Generated by the gRPC C++ plugin.
// If you make any local change, they will be lost.
// source: robot_syncer/proto/profile_sync.proto

#include "robot_syncer/proto/profile_sync.pb.h"
#include "robot_syncer/proto/profile_sync.grpc.pb.h"

#include <functional>
#include <grpcpp/impl/codegen/async_stream.h>
#include <grpcpp/impl/codegen/async_unary_call.h>
#include <grpcpp/impl/codegen/channel_interface.h>
#include <grpcpp/impl/codegen/client_unary_call.h>
#include <grpcpp/impl/codegen/client_callback.h>
#include <grpcpp/impl/codegen/message_allocator.h>
#include <grpcpp/impl/codegen/method_handler.h>
#include <grpcpp/impl/codegen/rpc_service_method.h>
#include <grpcpp/impl/codegen/server_callback.h>
#include <grpcpp/impl/codegen/server_callback_handlers.h>
#include <grpcpp/impl/codegen/server_context.h>
#include <grpcpp/impl/codegen/service_type.h>
#include <grpcpp/impl/codegen/sync_stream.h>
namespace carbon {
namespace robot_syncer {
namespace profile_sync {

static const char* RoSyProfileSyncService_method_names[] = {
  "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfileSyncData",
  "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/UploadProfile",
  "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfile",
  "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/DeleteProfile",
  "/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/PurgeProfile",
};

std::unique_ptr< RoSyProfileSyncService::Stub> RoSyProfileSyncService::NewStub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options) {
  (void)options;
  std::unique_ptr< RoSyProfileSyncService::Stub> stub(new RoSyProfileSyncService::Stub(channel, options));
  return stub;
}

RoSyProfileSyncService::Stub::Stub(const std::shared_ptr< ::grpc::ChannelInterface>& channel, const ::grpc::StubOptions& options)
  : channel_(channel), rpcmethod_GetProfileSyncData_(RoSyProfileSyncService_method_names[0], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_UploadProfile_(RoSyProfileSyncService_method_names[1], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_GetProfile_(RoSyProfileSyncService_method_names[2], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_DeleteProfile_(RoSyProfileSyncService_method_names[3], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  , rpcmethod_PurgeProfile_(RoSyProfileSyncService_method_names[4], options.suffix_for_stats(),::grpc::internal::RpcMethod::NORMAL_RPC, channel)
  {}

::grpc::Status RoSyProfileSyncService::Stub::GetProfileSyncData(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest& request, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetProfileSyncData_, context, request, response);
}

void RoSyProfileSyncService::Stub::async::GetProfileSyncData(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest* request, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetProfileSyncData_, context, request, response, std::move(f));
}

void RoSyProfileSyncService::Stub::async::GetProfileSyncData(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest* request, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetProfileSyncData_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse>* RoSyProfileSyncService::Stub::PrepareAsyncGetProfileSyncDataRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetProfileSyncData_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse>* RoSyProfileSyncService::Stub::AsyncGetProfileSyncDataRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetProfileSyncDataRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RoSyProfileSyncService::Stub::UploadProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::UploadProfileRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::robot_syncer::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_UploadProfile_, context, request, response);
}

void RoSyProfileSyncService::Stub::async::UploadProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::robot_syncer::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadProfile_, context, request, response, std::move(f));
}

void RoSyProfileSyncService::Stub::async::UploadProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_UploadProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* RoSyProfileSyncService::Stub::PrepareAsyncUploadProfileRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::robot_syncer::profile_sync::UploadProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_UploadProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* RoSyProfileSyncService::Stub::AsyncUploadProfileRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::UploadProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncUploadProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RoSyProfileSyncService::Stub::GetProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileRequest& request, ::carbon::robot_syncer::profile_sync::GetProfileResponse* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::robot_syncer::profile_sync::GetProfileRequest, ::carbon::robot_syncer::profile_sync::GetProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_GetProfile_, context, request, response);
}

void RoSyProfileSyncService::Stub::async::GetProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileRequest* request, ::carbon::robot_syncer::profile_sync::GetProfileResponse* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::robot_syncer::profile_sync::GetProfileRequest, ::carbon::robot_syncer::profile_sync::GetProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetProfile_, context, request, response, std::move(f));
}

void RoSyProfileSyncService::Stub::async::GetProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileRequest* request, ::carbon::robot_syncer::profile_sync::GetProfileResponse* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_GetProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::robot_syncer::profile_sync::GetProfileResponse>* RoSyProfileSyncService::Stub::PrepareAsyncGetProfileRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::robot_syncer::profile_sync::GetProfileResponse, ::carbon::robot_syncer::profile_sync::GetProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_GetProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::robot_syncer::profile_sync::GetProfileResponse>* RoSyProfileSyncService::Stub::AsyncGetProfileRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncGetProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RoSyProfileSyncService::Stub::DeleteProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::DeleteProfileRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::robot_syncer::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_DeleteProfile_, context, request, response);
}

void RoSyProfileSyncService::Stub::async::DeleteProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::robot_syncer::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteProfile_, context, request, response, std::move(f));
}

void RoSyProfileSyncService::Stub::async::DeleteProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_DeleteProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* RoSyProfileSyncService::Stub::PrepareAsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::robot_syncer::profile_sync::DeleteProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_DeleteProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* RoSyProfileSyncService::Stub::AsyncDeleteProfileRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::DeleteProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncDeleteProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

::grpc::Status RoSyProfileSyncService::Stub::PurgeProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::PurgeProfileRequest& request, ::carbon::portal::util::Empty* response) {
  return ::grpc::internal::BlockingUnaryCall< ::carbon::robot_syncer::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), rpcmethod_PurgeProfile_, context, request, response);
}

void RoSyProfileSyncService::Stub::async::PurgeProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response, std::function<void(::grpc::Status)> f) {
  ::grpc::internal::CallbackUnaryCall< ::carbon::robot_syncer::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PurgeProfile_, context, request, response, std::move(f));
}

void RoSyProfileSyncService::Stub::async::PurgeProfile(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response, ::grpc::ClientUnaryReactor* reactor) {
  ::grpc::internal::ClientCallbackUnaryFactory::Create< ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(stub_->channel_.get(), stub_->rpcmethod_PurgeProfile_, context, request, response, reactor);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* RoSyProfileSyncService::Stub::PrepareAsyncPurgeProfileRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) {
  return ::grpc::internal::ClientAsyncResponseReaderHelper::Create< ::carbon::portal::util::Empty, ::carbon::robot_syncer::profile_sync::PurgeProfileRequest, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(channel_.get(), cq, rpcmethod_PurgeProfile_, context, request);
}

::grpc::ClientAsyncResponseReader< ::carbon::portal::util::Empty>* RoSyProfileSyncService::Stub::AsyncPurgeProfileRaw(::grpc::ClientContext* context, const ::carbon::robot_syncer::profile_sync::PurgeProfileRequest& request, ::grpc::CompletionQueue* cq) {
  auto* result =
    this->PrepareAsyncPurgeProfileRaw(context, request, cq);
  result->StartCall();
  return result;
}

RoSyProfileSyncService::Service::Service() {
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RoSyProfileSyncService_method_names[0],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RoSyProfileSyncService::Service, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RoSyProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest* req,
             ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse* resp) {
               return service->GetProfileSyncData(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RoSyProfileSyncService_method_names[1],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RoSyProfileSyncService::Service, ::carbon::robot_syncer::profile_sync::UploadProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RoSyProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::robot_syncer::profile_sync::UploadProfileRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->UploadProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RoSyProfileSyncService_method_names[2],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RoSyProfileSyncService::Service, ::carbon::robot_syncer::profile_sync::GetProfileRequest, ::carbon::robot_syncer::profile_sync::GetProfileResponse, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RoSyProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::robot_syncer::profile_sync::GetProfileRequest* req,
             ::carbon::robot_syncer::profile_sync::GetProfileResponse* resp) {
               return service->GetProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RoSyProfileSyncService_method_names[3],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RoSyProfileSyncService::Service, ::carbon::robot_syncer::profile_sync::DeleteProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RoSyProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::robot_syncer::profile_sync::DeleteProfileRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->DeleteProfile(ctx, req, resp);
             }, this)));
  AddMethod(new ::grpc::internal::RpcServiceMethod(
      RoSyProfileSyncService_method_names[4],
      ::grpc::internal::RpcMethod::NORMAL_RPC,
      new ::grpc::internal::RpcMethodHandler< RoSyProfileSyncService::Service, ::carbon::robot_syncer::profile_sync::PurgeProfileRequest, ::carbon::portal::util::Empty, ::grpc::protobuf::MessageLite, ::grpc::protobuf::MessageLite>(
          [](RoSyProfileSyncService::Service* service,
             ::grpc::ServerContext* ctx,
             const ::carbon::robot_syncer::profile_sync::PurgeProfileRequest* req,
             ::carbon::portal::util::Empty* resp) {
               return service->PurgeProfile(ctx, req, resp);
             }, this)));
}

RoSyProfileSyncService::Service::~Service() {
}

::grpc::Status RoSyProfileSyncService::Service::GetProfileSyncData(::grpc::ServerContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileSyncDataRequest* request, ::carbon::robot_syncer::profile_sync::GetProfileSyncDataResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RoSyProfileSyncService::Service::UploadProfile(::grpc::ServerContext* context, const ::carbon::robot_syncer::profile_sync::UploadProfileRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RoSyProfileSyncService::Service::GetProfile(::grpc::ServerContext* context, const ::carbon::robot_syncer::profile_sync::GetProfileRequest* request, ::carbon::robot_syncer::profile_sync::GetProfileResponse* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RoSyProfileSyncService::Service::DeleteProfile(::grpc::ServerContext* context, const ::carbon::robot_syncer::profile_sync::DeleteProfileRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}

::grpc::Status RoSyProfileSyncService::Service::PurgeProfile(::grpc::ServerContext* context, const ::carbon::robot_syncer::profile_sync::PurgeProfileRequest* request, ::carbon::portal::util::Empty* response) {
  (void) context;
  (void) request;
  (void) response;
  return ::grpc::Status(::grpc::StatusCode::UNIMPLEMENTED, "");
}


}  // namespace carbon
}  // namespace robot_syncer
}  // namespace profile_sync


# Generated by the gRPC Python protocol compiler plugin. DO NOT EDIT!
"""Client and server classes corresponding to protobuf-defined services."""
import grpc

from generated.portal.proto import util_pb2 as portal_dot_proto_dot_util__pb2
from robot_syncer.proto import profile_sync_pb2 as robot__syncer_dot_proto_dot_profile__sync__pb2


class RoSyProfileSyncServiceStub(object):
    """Missing associated documentation comment in .proto file."""

    def __init__(self, channel):
        """Constructor.

        Args:
            channel: A grpc.Channel.
        """
        self.GetProfileSyncData = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfileSyncData',
                request_serializer=robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileSyncDataRequest.SerializeToString,
                response_deserializer=robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileSyncDataResponse.FromString,
                )
        self.UploadProfile = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/UploadProfile',
                request_serializer=robot__syncer_dot_proto_dot_profile__sync__pb2.UploadProfileRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.GetProfile = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfile',
                request_serializer=robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileRequest.SerializeToString,
                response_deserializer=robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileResponse.FromString,
                )
        self.DeleteProfile = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/DeleteProfile',
                request_serializer=robot__syncer_dot_proto_dot_profile__sync__pb2.DeleteProfileRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )
        self.PurgeProfile = channel.unary_unary(
                '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/PurgeProfile',
                request_serializer=robot__syncer_dot_proto_dot_profile__sync__pb2.PurgeProfileRequest.SerializeToString,
                response_deserializer=portal_dot_proto_dot_util__pb2.Empty.FromString,
                )


class RoSyProfileSyncServiceServicer(object):
    """Missing associated documentation comment in .proto file."""

    def GetProfileSyncData(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def UploadProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def GetProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def DeleteProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')

    def PurgeProfile(self, request, context):
        """Missing associated documentation comment in .proto file."""
        context.set_code(grpc.StatusCode.UNIMPLEMENTED)
        context.set_details('Method not implemented!')
        raise NotImplementedError('Method not implemented!')


def add_RoSyProfileSyncServiceServicer_to_server(servicer, server):
    rpc_method_handlers = {
            'GetProfileSyncData': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfileSyncData,
                    request_deserializer=robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileSyncDataRequest.FromString,
                    response_serializer=robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileSyncDataResponse.SerializeToString,
            ),
            'UploadProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.UploadProfile,
                    request_deserializer=robot__syncer_dot_proto_dot_profile__sync__pb2.UploadProfileRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'GetProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.GetProfile,
                    request_deserializer=robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileRequest.FromString,
                    response_serializer=robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileResponse.SerializeToString,
            ),
            'DeleteProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.DeleteProfile,
                    request_deserializer=robot__syncer_dot_proto_dot_profile__sync__pb2.DeleteProfileRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
            'PurgeProfile': grpc.unary_unary_rpc_method_handler(
                    servicer.PurgeProfile,
                    request_deserializer=robot__syncer_dot_proto_dot_profile__sync__pb2.PurgeProfileRequest.FromString,
                    response_serializer=portal_dot_proto_dot_util__pb2.Empty.SerializeToString,
            ),
    }
    generic_handler = grpc.method_handlers_generic_handler(
            'carbon.robot_syncer.profile_sync.RoSyProfileSyncService', rpc_method_handlers)
    server.add_generic_rpc_handlers((generic_handler,))


 # This class is part of an EXPERIMENTAL API.
class RoSyProfileSyncService(object):
    """Missing associated documentation comment in .proto file."""

    @staticmethod
    def GetProfileSyncData(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfileSyncData',
            robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileSyncDataRequest.SerializeToString,
            robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileSyncDataResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def UploadProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/UploadProfile',
            robot__syncer_dot_proto_dot_profile__sync__pb2.UploadProfileRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def GetProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/GetProfile',
            robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileRequest.SerializeToString,
            robot__syncer_dot_proto_dot_profile__sync__pb2.GetProfileResponse.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def DeleteProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/DeleteProfile',
            robot__syncer_dot_proto_dot_profile__sync__pb2.DeleteProfileRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

    @staticmethod
    def PurgeProfile(request,
            target,
            options=(),
            channel_credentials=None,
            call_credentials=None,
            insecure=False,
            compression=None,
            wait_for_ready=None,
            timeout=None,
            metadata=None):
        return grpc.experimental.unary_unary(request, target, '/carbon.robot_syncer.profile_sync.RoSyProfileSyncService/PurgeProfile',
            robot__syncer_dot_proto_dot_profile__sync__pb2.PurgeProfileRequest.SerializeToString,
            portal_dot_proto_dot_util__pb2.Empty.FromString,
            options, channel_credentials,
            insecure, call_credentials, compression, wait_for_ready, timeout, metadata)

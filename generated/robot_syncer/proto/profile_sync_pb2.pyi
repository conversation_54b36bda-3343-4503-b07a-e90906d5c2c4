"""
@generated by mypy-protobuf.  Do not edit manually!
isort:skip_file
"""
from generated.category.proto.category_pb2 import (
    Category as category___proto___category_pb2___Category,
    CategoryCollection as category___proto___category_pb2___CategoryCollection,
)

from generated.frontend.proto.banding_pb2 import (
    BandingDef as frontend___proto___banding_pb2___BandingDef,
)

from generated.frontend.proto.profile_sync_pb2 import (
    ProfileSyncData as frontend___proto___profile_sync_pb2___ProfileSyncData,
)

from google.protobuf.descriptor import (
    Descriptor as google___protobuf___descriptor___Descriptor,
    FileDescriptor as google___protobuf___descriptor___FileDescriptor,
)

from google.protobuf.internal.containers import (
    MessageMap as google___protobuf___internal___containers___MessageMap,
)

from google.protobuf.message import (
    Message as google___protobuf___message___Message,
)

from generated.proto.almanac.almanac_pb2 import (
    AlmanacConfig as proto___almanac___almanac_pb2___AlmanacConfig,
    DiscriminatorConfig as proto___almanac___almanac_pb2___DiscriminatorConfig,
    ModelinatorConfig as proto___almanac___almanac_pb2___ModelinatorConfig,
)

from generated.proto.target_velocity_estimator.target_velocity_estimator_pb2 import (
    TVEProfile as proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile,
)

from generated.proto.thinning.thinning_pb2 import (
    ConfigDefinition as proto___thinning___thinning_pb2___ConfigDefinition,
)

from typing import (
    Mapping as typing___Mapping,
    Optional as typing___Optional,
    Text as typing___Text,
)

from typing_extensions import (
    Literal as typing_extensions___Literal,
)


builtin___bool = bool
builtin___bytes = bytes
builtin___float = float
builtin___int = int


DESCRIPTOR: google___protobuf___descriptor___FileDescriptor = ...

class GetProfileSyncDataRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    robot_serial: typing___Text = ...

    def __init__(self,
        *,
        robot_serial : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"robot_serial",b"robot_serial"]) -> None: ...
type___GetProfileSyncDataRequest = GetProfileSyncDataRequest

class GetProfileSyncDataResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    class ProfilesEntry(google___protobuf___message___Message):
        DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
        key: typing___Text = ...

        @property
        def value(self) -> frontend___proto___profile_sync_pb2___ProfileSyncData: ...

        def __init__(self,
            *,
            key : typing___Optional[typing___Text] = None,
            value : typing___Optional[frontend___proto___profile_sync_pb2___ProfileSyncData] = None,
            ) -> None: ...
        def HasField(self, field_name: typing_extensions___Literal[u"value",b"value"]) -> builtin___bool: ...
        def ClearField(self, field_name: typing_extensions___Literal[u"key",b"key",u"value",b"value"]) -> None: ...
    type___ProfilesEntry = ProfilesEntry


    @property
    def profiles(self) -> google___protobuf___internal___containers___MessageMap[typing___Text, frontend___proto___profile_sync_pb2___ProfileSyncData]: ...

    def __init__(self,
        *,
        profiles : typing___Optional[typing___Mapping[typing___Text, frontend___proto___profile_sync_pb2___ProfileSyncData]] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"profiles",b"profiles"]) -> None: ...
type___GetProfileSyncDataResponse = GetProfileSyncDataResponse

class UploadProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    last_update_time_ms: builtin___int = ...
    robot_serial: typing___Text = ...

    @property
    def almanac(self) -> proto___almanac___almanac_pb2___AlmanacConfig: ...

    @property
    def discriminator(self) -> proto___almanac___almanac_pb2___DiscriminatorConfig: ...

    @property
    def modelinator(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    @property
    def banding(self) -> frontend___proto___banding_pb2___BandingDef: ...

    @property
    def thinning(self) -> proto___thinning___thinning_pb2___ConfigDefinition: ...

    @property
    def target_velocity_estimator(self) -> proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile: ...

    @property
    def categoryCollection(self) -> category___proto___category_pb2___CategoryCollection: ...

    @property
    def category(self) -> category___proto___category_pb2___Category: ...

    def __init__(self,
        *,
        last_update_time_ms : typing___Optional[builtin___int] = None,
        robot_serial : typing___Optional[typing___Text] = None,
        almanac : typing___Optional[proto___almanac___almanac_pb2___AlmanacConfig] = None,
        discriminator : typing___Optional[proto___almanac___almanac_pb2___DiscriminatorConfig] = None,
        modelinator : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        banding : typing___Optional[frontend___proto___banding_pb2___BandingDef] = None,
        thinning : typing___Optional[proto___thinning___thinning_pb2___ConfigDefinition] = None,
        target_velocity_estimator : typing___Optional[proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile] = None,
        categoryCollection : typing___Optional[category___proto___category_pb2___CategoryCollection] = None,
        category : typing___Optional[category___proto___category_pb2___Category] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"almanac",b"almanac",u"banding",b"banding",u"category",b"category",u"categoryCollection",b"categoryCollection",u"discriminator",b"discriminator",u"modelinator",b"modelinator",u"profile",b"profile",u"target_velocity_estimator",b"target_velocity_estimator",u"thinning",b"thinning"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"almanac",b"almanac",u"banding",b"banding",u"category",b"category",u"categoryCollection",b"categoryCollection",u"discriminator",b"discriminator",u"last_update_time_ms",b"last_update_time_ms",u"modelinator",b"modelinator",u"profile",b"profile",u"robot_serial",b"robot_serial",u"target_velocity_estimator",b"target_velocity_estimator",u"thinning",b"thinning"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"profile",b"profile"]) -> typing_extensions___Literal["almanac","discriminator","modelinator","banding","thinning","target_velocity_estimator","categoryCollection","category"]: ...
type___UploadProfileRequest = UploadProfileRequest

class GetProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uuid: typing___Text = ...

    def __init__(self,
        *,
        uuid : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"uuid",b"uuid"]) -> None: ...
type___GetProfileRequest = GetProfileRequest

class GetProfileResponse(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...

    @property
    def almanac(self) -> proto___almanac___almanac_pb2___AlmanacConfig: ...

    @property
    def discriminator(self) -> proto___almanac___almanac_pb2___DiscriminatorConfig: ...

    @property
    def modelinator(self) -> proto___almanac___almanac_pb2___ModelinatorConfig: ...

    @property
    def banding(self) -> frontend___proto___banding_pb2___BandingDef: ...

    @property
    def thinning(self) -> proto___thinning___thinning_pb2___ConfigDefinition: ...

    @property
    def target_velocity_estimator(self) -> proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile: ...

    @property
    def categoryCollection(self) -> category___proto___category_pb2___CategoryCollection: ...

    @property
    def category(self) -> category___proto___category_pb2___Category: ...

    def __init__(self,
        *,
        almanac : typing___Optional[proto___almanac___almanac_pb2___AlmanacConfig] = None,
        discriminator : typing___Optional[proto___almanac___almanac_pb2___DiscriminatorConfig] = None,
        modelinator : typing___Optional[proto___almanac___almanac_pb2___ModelinatorConfig] = None,
        banding : typing___Optional[frontend___proto___banding_pb2___BandingDef] = None,
        thinning : typing___Optional[proto___thinning___thinning_pb2___ConfigDefinition] = None,
        target_velocity_estimator : typing___Optional[proto___target_velocity_estimator___target_velocity_estimator_pb2___TVEProfile] = None,
        categoryCollection : typing___Optional[category___proto___category_pb2___CategoryCollection] = None,
        category : typing___Optional[category___proto___category_pb2___Category] = None,
        ) -> None: ...
    def HasField(self, field_name: typing_extensions___Literal[u"almanac",b"almanac",u"banding",b"banding",u"category",b"category",u"categoryCollection",b"categoryCollection",u"discriminator",b"discriminator",u"modelinator",b"modelinator",u"profile",b"profile",u"target_velocity_estimator",b"target_velocity_estimator",u"thinning",b"thinning"]) -> builtin___bool: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"almanac",b"almanac",u"banding",b"banding",u"category",b"category",u"categoryCollection",b"categoryCollection",u"discriminator",b"discriminator",u"modelinator",b"modelinator",u"profile",b"profile",u"target_velocity_estimator",b"target_velocity_estimator",u"thinning",b"thinning"]) -> None: ...
    def WhichOneof(self, oneof_group: typing_extensions___Literal[u"profile",b"profile"]) -> typing_extensions___Literal["almanac","discriminator","modelinator","banding","thinning","target_velocity_estimator","categoryCollection","category"]: ...
type___GetProfileResponse = GetProfileResponse

class DeleteProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uuid: typing___Text = ...

    def __init__(self,
        *,
        uuid : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"uuid",b"uuid"]) -> None: ...
type___DeleteProfileRequest = DeleteProfileRequest

class PurgeProfileRequest(google___protobuf___message___Message):
    DESCRIPTOR: google___protobuf___descriptor___Descriptor = ...
    uuid: typing___Text = ...

    def __init__(self,
        *,
        uuid : typing___Optional[typing___Text] = None,
        ) -> None: ...
    def ClearField(self, field_name: typing_extensions___Literal[u"uuid",b"uuid"]) -> None: ...
type___PurgeProfileRequest = PurgeProfileRequest
